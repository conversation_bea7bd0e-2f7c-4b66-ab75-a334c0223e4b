import React, { useState, useEffect, useRef } from 'react'
import {
  alphaNumericButtons,
  numberPadButtons,
  specialCharecters
} from './buttonConstant'
import './../../styles/AlphaNumeric.scss'
import './../../styles/tvstyles.scss'
import { hoveringMouse } from '../../utils/constant'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { removeReturnFocusById, setReturnFocusById } from '../../store/slices/SearchSlice'
import { useDispatch } from 'react-redux'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const AlphaNumeric = props => {
  const boardBtnRef = useRef([])
  const numericBtnRef = useRef([])
  const dispatch = useDispatch()

  const [keyVal, setValue] = useState('')
  const [isSpecial, setSpecial] = useState(false)
  const [caps, setCaps] = useState(false)
  const [numericSubmitDisable, setNumericSubDisable] = useState(true)
  const [selectedIdx, setSelectedIdx] = useState(0)

  useEffect(() => {
    setValue(props?.searchValue ?? '')
    props?.currentButtonFocus == 'abc_active' && props?.setCurrentButtonFocus('abc_active')
   props?.currentButtonFocus == 'borrar' && props?.setCurrentButtonFocus('borrar')
   props?.currentButtonFocus == 'vaciar' && props?.setCurrentButtonFocus('vaciar')

  }, [props])

  useEffect(() => {
    setNumericSubDisable(props?.submitDisable)
  }, [props?.submitDisable])

  useEffect(() => {
    props?.autoFocus &&
      setTimeout(() => {
        numericBtnRef?.current[0]?.focus()
      }, 1400)
  }, [props?.autoFocus])

  useEffect(() => {
    if (props?.fromMic) {
      setTimeout(() => {
        boardBtnRef?.current[0]?.focus()
      }, 200)
    }
  }, [props?.fromMic])

  useEffect(() => {
    props?.name === 'profile'
      ? document.getElementById('ChooseProfileText_Container_id')?.focus()
      : document.getElementById('Key_0') &&
        document.getElementById('Key_0')?.focus()
  }, [isSpecial])

  useEffect(() => {
    props?.fromTransactions && setSpecial(true)
  }, [props?.fromTransactions])

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF1Green', 'ColorF3Blue'])
      const codes = {
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code,
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code
      }
      handleSamsungKey(codes, keycode, event)
    } else {
      handleLgkey(keycode, event)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [])

  const handleSamsungKey = (key, keycode) => {
    if (key.greencode === keycode) {
      document.getElementById('clearBtn').click()
    } else if (key.bluecode === keycode) {
      onKeyChange('clr')
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 404 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 119)) {
      document.getElementById('clearBtn').click()
    } else if (keycode == 406 ||  ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 121)) {
      onKeyChange('clr')
    }
  }

  const onKeyChange = e => {
    let val = keyVal
    dispatch(removeReturnFocusById())
    dispatch(setReturnFocusById(''))
    switch (e) {
      case 'cl':
        val = val.slice(0, -1)
        break
      case 'space':
        val = val.concat(' ')
        break
      case 'clr':
        val = ''
        break
      default:
        val = val.concat(e)
        break
    }
    if (props?.length) {
      if (val?.length <= props?.length) {
        setValue(val)
        props?.onChange(val)
      }
    } else {
      setValue(val)
      props?.onChange(val)
    }
    document.getElementById(props?.id) &&
      document.getElementById(props?.id).scrollBy(30, 0)
  }

  const keyPressed = (e, i) => {
    e?.keyCode == 38 && i <= 9 && props?.onkeyDown(e?.keyCode)
  }

  const onNumericKeyChange = item => {
    props?.onChange(item)
  }

  let splCharrow = specialCharecters && Math.ceil(specialCharecters?.length / 8)

  return props?.type == 'alphaNumeric' ? (
    <>
      <div className="top-buttons-key">
        <button
          className="keyboard-special-buttons focusable"
          id="abcBtn"
          ref={props?.abcRef}
          onFocus={() => props?.setCurrentButtonFocus('abc_active')}
          onKeyDown={props?.focusDown}
          onBlur={() => props?.setCurrentButtonFocus('')}
          tabIndex={1}
          data-testid="unVoice"
          onClick={() => {
            setSpecial(false)
          }}
          autoFocus={() => props?.setCurrentButtonFocus('abc_active')}
        >
          {!isSpecial ? (
            <LazyLoadImage
              className="top-buttom-image"
              alt="Search.."
              src={'images/Search_Icons/sc_teclado_abc_active.png'}
            />
          ) : (
            <LazyLoadImage
              className="top-buttom-image"
              alt="Search.."
              src={'images/Search_Icons/sc_teclado_abc_inactive.png'}
            />
          )}
        </button>
        <button
          className="keyboard-special-buttons focusable"
          id="numBtn"
          ref={props?.numRef}
          onFocus={() => props?.setCurrentButtonFocus('123_active')}
          onKeyDown={props?.focusDown}
          onBlur={() => props?.setCurrentButtonFocus('')}
          tabIndex={1}
          onClick={() => {
            setSpecial(true)
          }}
          data-testid="unVoice"
        >
          {isSpecial ? (
            <LazyLoadImage
              className="top-buttom-image"
              alt="Search.."
              src={'images/Search_Icons/sc_teclado_123_active.png'}
            />
          ) : (
            <LazyLoadImage
              className="top-buttom-image"
              alt="Search.."
              src={'images/Search_Icons/sc_teclado_123_inactive.png'}
            />
          )}
        </button>

        <button
          className="keyboard-clear-buttons focusable"
          id="clearBtn"
          ref={props?.borrarRef}
          onFocus={() => props?.setCurrentButtonFocus('borrar')}
          onKeyDown={props?.focusDown}
          onBlur={() => props?.setCurrentButtonFocus('')}
          tabIndex={1}
          data-testid="unVoice"
          onClick={() => onKeyChange('cl')}
          data-sn-up={props?.id == 'searchleft' ? '#serachId' : ''}
        >
          <LazyLoadImage
            className="top-buttom-image"
            alt="Search.."
            src={'images/Search_Icons/sc_teclado_borrar.png'}
          />
        </button>
        <button
          className="keyboard-clear-buttons focusable"
          id="clBtn"
          ref={props?.vaciarRef}
          onFocus={() => props?.setCurrentButtonFocus('vaciar')}
          onKeyDown={props?.focusDown}
          onBlur={() => props?.setCurrentButtonFocus('')}
          tabIndex={1}
          data-testid="unVoice"
          onClick={() => onKeyChange('clr')}
          data-sn-up={props?.id == 'searchleft' ? '#serachId' : ''}
        >
          <LazyLoadImage
            className="top-buttom-image"
            alt="Search.."
            src={'images/Search_Icons/sc_teclado_vaciar.png'}
          />
        </button>
      </div>
      {isSpecial ? (
        <div className={'keyboardCover'}>
          {specialCharecters?.map((e, i) =>
            e == 'space' ? (
              <button
                id="spcId"
                type="button"
                data-sn-down={props?.dsndown}
                onClick={() => onKeyChange('space')}
                style={{ width: '210px' }}
                className="search-keyboard-button focusable has-outline-key"
                onMouseOver={() => {
                  hoveringMouse('spcId')
                }}
              >
                <img src="images/spacebar.png" alt="spacebar" />
              </button>
            ) : (
              <button
                style={{
                  width: e == '&?!' ? '40px' : e == 'abc' ? '90px' : '70px'
                }}
                data-sn-down={
                  props?.name === 'profile' &&
                  Math.ceil((i + 1) / 8) !== splCharrow
                    ? undefined
                    : document.getElementById('option0') && '#option0'
                }
                data-sn-right={
                  props?.id == 'profileup' && (e == 'abc' || (i + 1) % 8 == 0)
                    ? ''
                    : undefined
                }
                name={props?.name}
                data-testid={'profile'}
                type="button"
                id={'Key_' + i}
                onClick={() =>
                  e == 'abc'
                    ? setSpecial(false)
                    : onKeyChange(caps ? e.toUpperCase() : e.toLowerCase())
                }
                className="search-numerical-keyboard-button focusable has-outline-key"
                onMouseOver={() => {
                  hoveringMouse('Key_' + i)
                }}
              >
                {caps ? e.toUpperCase() : e.toLowerCase()}
              </button>
            )
          )}
        </div>
      ) : (
        <>
          <div className={`keyboardCover`}>
            {alphaNumericButtons?.map((e, i) => (
              <>
                {e == 'shift' ||
                e == 'backSpace' ||
                e == 'clearAll' ||
                e == 'space' ||
                e == '.com' ||
                e == '.ar' ||
                e == '@gmail.com' ||
                e == '@hotmail.com' ? null : (
                  <button
                    ref={ref => (boardBtnRef.current[i] = ref)}
                    onKeyDown={eve => keyPressed(eve, i)}
                    style={{
                      width: e == '&?!' ? '40px' : e == 'abc' ? '90px' : '40px'
                    }}
                    type="button"
                    data-testid="profile"
                    id={'Key_' + i}
                    data-sn-up={i >= 30 && i <= 37 ? `#Key_${i - 9}` : i >= 21 && i <= 27 ? `#Key_${i - 10}` : null}
                    data-sn-down={i >= 21 && i <= 27 ? `#Key_${i + 9}` : i >= 11 && i <= 18 ? `#Key_${i + 10}` : null}
                    onClick={() =>
                      e == '&?!'
                        ? setSpecial(true)
                        : onKeyChange(caps ? e.toUpperCase() : e.toLowerCase())
                    }
                    className={`search-keyboard-button focusable has-outline-key ${
                      e == '&?!'
                        ? 'key-width-spl'
                        : e == 'abc'
                        ? 'key-width-abc'
                        : 'key-width'
                    }`}
                    onMouseOver={() => {
                      hoveringMouse('Key_' + i)
                    }}
                  >
                    {e == 'shift' ||
                    e == 'backSpace' ||
                    e == 'clearAll' ||
                    e == 'space'
                      ? null
                      : caps
                      ? e.toUpperCase()
                      : e.toLowerCase()}
                  </button>
                )}
                {e == 'shift' ? (
                  <button
                    id="shftId"
                    type="button"
                    onClick={() => setCaps(!caps)}
                    className="search-keyboard-button focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('shftId')
                    }}
                  >
                    <img
                      src="images/shift.png"
                      style={{ width: '47px', height: '48px' }}
                      alt="shift"
                    />
                  </button>
                ) : e == 'backSpace' ? (
                  <button
                    id="bckId"
                    type="button"
                    onClick={() => onKeyChange('cl')}
                    className={`search-keyboard-button has-outline-key focusable`}
                    onMouseOver={() => {
                      hoveringMouse('bckId')
                    }}
                  >
                    <img
                      style={{ width: '37px', height: '34px' }}
                      src="images/backspace.png"
                      alt="backspace"
                    />
                  </button>
                ) : e == 'clearAll' ? (
                  <button
                    id="clrId"
                    type="button"
                    onClick={() => onKeyChange('clr')}
                    style={{ width: '140px', fontSize: '22px' }}
                    className="search-keyboard-button focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('clrId')
                    }}
                    data-testid="clear"
                  >
                    {'Clear All'}
                  </button>
                ) : e == 'space' ? (
                  <button
                    id="spcId"
                    type="button"
                    data-sn-down={props?.dsndown}
                    onClick={() => onKeyChange('space')}
                    style={{ width: '170px' }}
                    className="search-keyboard-button focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('spcId')
                    }}
                  >
                    <img src="images/spacebar.png" alt="spacebar" />
                  </button>
                ) : e == '.com' &&
                  props?.name != 'search' &&
                  props?.name != 'register' &&
                  props?.name != 'coupon' ? (
                  <button
                    id="comId"
                    type="button"
                    onClick={() => onKeyChange('.com')}
                    style={{
                      width: '63px',
                      height: '30px',
                      paddingLeft: '31.5px',
                      paddingRight: '31.5px'
                    }}
                    className="search-keyboard-button mailid-row focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('comId')
                    }}
                  >
                    .com
                  </button>
                ) : e == '.ar' &&
                  props?.name != 'search' &&
                  props?.name != 'register' &&
                  props?.name != 'coupon' ? (
                  <button
                    // id=".comId"
                    id="arId"
                    type="button"
                    onClick={() => onKeyChange('.ar')}
                    style={{
                      width: '36px',
                      height: '30px',
                      paddingLeft: '35px',
                      paddingRight: '35px'
                    }}
                    className="search-keyboard-button mailid-row focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('arId')
                    }}
                  >
                    .ar
                  </button>
                ) : e == '@gmail.com' &&
                  props?.name != 'search' &&
                  props?.name != 'register' &&
                  props?.name != 'coupon' ? (
                  <button
                    id="gmlId"
                    type="button"
                    onClick={() => onKeyChange('@gmail.com')}
                    style={{
                      width: '185px',
                      height: '29px',
                      paddingLeft: '39.5px',
                      paddingRight: '39.5px'
                    }}
                    className="search-keyboard-button mailid-row focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('gmlId')
                    }}
                  >
                    @gmail.com
                  </button>
                ) : e == '@hotmail.com' &&
                  props?.name != 'search' &&
                  props?.name != 'register' &&
                  props?.name != 'coupon' ? (
                  <button
                    id="yhoId"
                    type="button"
                    onClick={() => onKeyChange('@hotmail.com')}
                    style={{
                      width: '207px',
                      height: '32.67px',
                      paddingLeft: '16.95px',
                      paddingRight: '16.95px'
                    }}
                    className="search-keyboard-button mailid-row focusable has-outline-key"
                    onMouseOver={() => {
                      hoveringMouse('yhoId')
                    }}
                  >
                    @hotmail.com
                  </button>
                ) : null}
              </>
            ))}
          </div>
        </>
      )}
    </>
  ) : (
    <div className="Keyboard-cont">
      <div style={{ marginLeft: 13 }}>
        {numberPadButtons?.map((item, index) => (
          <button
            key={index}
            className={
              selectedIdx === index
                ? 'Keyboard-btn-focused focusable'
                : 'Keyboard-btn-unfocused focusable'
            }
            ref={ref => (numericBtnRef.current[index] = ref)}
            onFocus={() => {
              setSelectedIdx(index)
              props?.onFocus?.()
            }}
            onBlur={props?.onBlur}
            onClick={e => onNumericKeyChange(item)}
          >
            <p
              style={{ color: selectedIdx === index ? '#1C2229' : '#DADCE0' }}
              className="Keyboard-txt"
            >
              {item}
            </p>
          </button>
        ))}
      </div>
    </div>
  )
}

export default AlphaNumeric
