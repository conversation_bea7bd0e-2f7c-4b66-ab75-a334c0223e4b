import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from 'react-router-dom'
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import RecordingDelete from "./RecordingDelete";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
 <MemoryRouter history={history}
        initialEntries={[{ state: {recordingData:{
            "id": "1152293",
            "title": "Knuckles",
            "title_episode": "El Guerrero",
            "title_uri": "Knuckles",
            "title_original": "Knuckles",
            "description": "Knuckles lucha por adaptarse a su nueva vida en Green Hills.",
            "description_large": "Knuckles lucha por adaptarse a su nueva vida en Green Hills.",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/SS/69744WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/SS/69744WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/SS/69744WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/HDPPICKNU101X-01-01-01/EXPORTACION_WEB/STILLS/HDPPICKNU101X-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn0.clarovideo.net/PARAMOUNT/SERIES/KNUCKLES-01-01-00/EXPORTACION_WEB/CLEAN/KNUCKLES-01-01-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/CLEAN/69744WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/CLEAN/69744WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/SS/69744WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/SS/69744WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/CLEAN/69744WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/69744/EXPORTACION_WEB/CLEAN/69744WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/PELICULAS/HDPPICKNU101X-01-01-01/EXPORTACION_WEB/SPRITES/HDPPICKNU101X-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn0.clarovideo.net/PARAMOUNT/PELICULAS/HDPPICKNU101X-01-01-01/EXPORTACION_WEB/SPRITES/HDPPICKNU101X-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/PELICULAS/HDPPICKNU101X-01-01-01/EXPORTACION_WEB/SPRITES/HDPPICKNU101X-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:33:42",
            "date": "20240514211134",
            "year": "2024",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "0",
            "live_type": null,
            "live_ref": null,
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "G",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": true,
            "channel_number": null
        }} }]}
        >{children}</MemoryRouter>    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

describe('RecordDelete page test case', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('should render onclick confirm button', () => {
        jest.useFakeTimers()
        const { container } = renderWithState(<RecordingDelete  />)
        const getById = queryByAttribute.bind(null, 'id');
            const scroll = getById(container, 'confirmbt');
            fireEvent.keyUp(scroll,{keyCode: '405'})
            fireEvent(
                scroll,
                new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                })
            )
            jest.advanceTimersByTime(3000)
    })
    test('should render onclick cancel button', () => {
        const { container } = renderWithState(<RecordingDelete  />)
        const getById = queryByAttribute.bind(null, 'id');
            const scroll = getById(container, 'cancelbtn');
            fireEvent(
                scroll,
                new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                })
            )
    })
    test('should render onclick back button', () => {
        const { container } = renderWithState(<RecordingDelete  />)
        const getById = queryByAttribute.bind(null, 'id');
            const scroll = getById(container, 'backbutton');
            fireEvent(
                scroll,
                new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                })
            )
    })
})