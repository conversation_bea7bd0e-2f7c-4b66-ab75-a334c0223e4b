import { call, put, takeEvery } from "redux-saga/effects";
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from "../store/sagaStore";
import { getEpgChannelError, getEpgChannelSuccess } from "../store/slices/EpgChannelSlice";

function* workGetEpgChannel(payload) {
    const getidmenu = sessionStorage.getItem("nodeid");
    const getversion = sessionStorage.getItem("epgversion");
    
  try {
    yield call(
        request,
        URL.EPG_CHANNEL_URL + `&date_from=20231005000000&date_to=20231006000000&node_id=${140867}&epg_version=${263839}&metaData=full&soaVersion=0.4.1`,
        {
          method: 'GET',
        },
        {
          onSuccess(response) {
            store.dispatch(getEpgChannelSuccess(response))
          },
          onError(error) {
            store.dispatch(getEpgChannelError(error))
          },
        },
      )
  } catch (error){
    store.dispatch(getEpgChannelError(error));
  }
}

export default function* epgChannelSaga() {
  yield takeEvery("epgChannel/getEpgChannel", workGetEpgChannel);
}
