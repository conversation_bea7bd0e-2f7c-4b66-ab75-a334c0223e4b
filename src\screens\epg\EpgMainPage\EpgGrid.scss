@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

$position-absolute: absolute;
$position-relative: relative;
$position-center: center;
$position-left: left;
$position-sticky: sticky;
$display-flex: flex;
$display-block: block;
$display-none: none;
$color-white: #ffffff;

.program-container,
.grid-wrapper {
  display: $display-flex;
  -ms-overflow-style: $display-none;
  scrollbar-width: $display-none;
}

.program-container::-webkit-scrollbar {
  display: $display-none;
}

/* css for channel */
.channel {
  height: 85px;
  width: 200.28px;
  display: $display-flex;
  align-items: $position-center;
  opacity: 1;
  color: white;
  padding: 10px;
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border-radius: 12px;
}

.channel-container {
  //Commenting and keeping for future referrence
  // padding-left: 9px;
  // gap: 10px;
  align-items: $position-center;
  display: $display-flex;
}

.channel-image {
  display: $display-flex;
  justify-content: $position-center;
  align-items: $position-center;
  background: #1a1a1a;
}

// Commenting and keeping for future referrence
// .channel-details .channel-number {
//   font-size: 12px;
//   text-align: $position-left;
//   font: normal normal normal Roboto;
//   letter-spacing: 0px;
//   color: $color-white;
//   opacity: 1;
//   padding-right: 15px;
// }

.channel-details .channel-name {
  font-size: 12px;
  text-align: $position-left;
  font: normal normal normal 10px Roboto;
  letter-spacing: 0px;
  color: $color-white;
  opacity: 1;
}

.channel-list {
  gap: 2px;
  display: grid;
}

/* css for program */

.program {
  box-sizing: border-box;
  height: 112px;
  width: 637px;
  background-color: #1a1a1a;
  display: $display-flex;
  // align-items: $position-center;
  gap: 10px;
  color: $color-white;
  justify-content: $position-center;
  flex-direction: column;
}

.program:focus {
  background: #981c15 0% 0% no-repeat padding-box;
  opacity: 1;
  border-radius: 12px 0 0 0;
  border: $display-none;
  color: $color-white;
  outline: $display-none;
  // font-weight: bold !important;
}

.poster-image {
  background-color: #ffffff;
}

.program-image {
  width: 44px;
  height: 24px;
  font-size: 5px;
  color: black;
}

.program-list {
  padding-bottom: 4px;
  display: $display-flex;
  flex-direction: row;
  gap: 5px;
}

// .program-time {
//   font: normal normal normal 10px Roboto;
//   letter-spacing: 0px;
//   color: $color-white;
//   opacity: 1;
// text-overflow: ellipsis;
// overflow: hidden;
// white-space: nowrap;
// }

.program-grid {
  padding: 2px;
  text-align: $position-left;
}

.container-list {
  display: $display-flex;
  flex-direction: row;
}

/* css for EPG container */
.epg-container {
  position: $position-relative;
  top: 0px;
  display: grid;
  gap: 1px;
  background: #1a1a1a 0% 0% no-repeat padding-box;
  width: 1920px;
}

/* css for channel */
.channel {
  height: 85px;
  width: 200.28px;
  display: $display-flex;
  align-items: $position-center;
  opacity: 1;
  color: white;
  padding: 10px;
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border-radius: 3px;
}

.channel-container {
  //Commenting and keeping for future referrence
  // padding-left: 9px;
  // gap: 20px;
  display: $display-flex;
  position: absolute;
  left: 100px;
}

.channel-image {
  display: $display-flex;
  justify-content: $position-center;
  align-items: $position-center;
  background: #1a1a1a;
}

.channel-details {
  position: absolute;
  left: 26px;
  top: 26px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 46px;
  height: 50px;
  .channel-number {
    text-align: $position-left;
    font: normal normal normal 28px/31px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    // padding-right: 15px;
    .channel-name {
      text-align: $position-left;
      font: normal normal normal 26px/31px Roboto;
      letter-spacing: 0px;
      color: $color-white;
      opacity: 1;
    }
  }
  .epg-fav-channel {
    height: 15.75px;
    width: 18px;
    padding-top: 6.75px;
    // position: absolute;
    // top: 75px;
    // left: 30px;
    .epg-fav-icon {
      width: '20px';
    }
  }
}

.channel-list {
  position: $position-sticky;
  display: $display-block;
  top: 0;
  left: 0;
  z-index: 2;
}

/* css for program */
.program {
  height: 112px;
  display: $display-flex;
  // align-items: $position-center;
  box-sizing: border-box;
  margin: 0px;
  color: $color-white;
  // border-radius: 3px;
  background: #1a1a1a 0% 0% no-repeat padding-box;
  overflow: hidden;
  border: 1px solid #585858;
}

.program-image {
  min-width: 161px;
  max-width: 161px;
  height: 90px;
  // background: white;
}

// .program-image img {
//   width: 111px;
//   height: 70px;
// }

// .program-title {
//   text-align: $position-left;
//   letter-spacing: 0px;
//   color: $color-white;
//   opacity: 1;
//   font-family: Roboto;
//   font-size: 36px;
//   font-weight: bold;
//   letter-spacing: 0;
//   line-height: 39px;
// }

.program-list {
  display: $display-flex;
  flex-direction: row;
  gap: 10px;
}

.program-grid {
  text-align: $position-left;
}



.container-list {
  display: $display-flex;
  flex-direction: row;
}

.marker {
  width: 0px;
  height: 560px;
  border: 2px solid $color-white;
  opacity: 1;
  z-index: 1;
  position: $position-absolute;
}

.time-line-container {
  display: $display-flex;
  bottom: 610px;
  height: 64px;
  align-items: flex-end;
  overflow-x: scroll;
  width: 1315px;
  left: 606px;
  position: $position-sticky;
  background: #000000 0% 0% no-repeat padding-box;
  opacity: 0;
  padding-left: 5px;
}

.time-line {
  width: 424px;
  display: grid;
  grid-template-columns: auto auto auto auto auto auto;
  grid-auto-rows: max-content;
  grid-gap: 0px;
  height: 64px;
  align-items: end;
}

.hour {
  color: white;
  top: -30px;
  position: $position-relative;
  font: normal normal normal 24px/28px Roboto;
  height: 32px;
  width: 110px;
  color: $color-white;
  font-family: Roboto;
  font-size: 28px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 32px;
}

.hour-line {
  width: 424px;
  display: grid;
  grid-template-columns: auto auto auto auto auto auto;
  grid-auto-rows: max-content;
  grid-gap: 0px;
  align-items: start;
}

.caliber-1 {
  height: 21px;
  width: 70.65px;
  position: $position-relative;
  bottom: -50px;
}

.day {
  width: 605px;
  height: 64px;
  background: white;
  position: $position-absolute;
  left: 0px;
  background: #000000 0% 0% no-repeat padding-box;
  border-radius: 20px 0px 0px 0px;
  opacity: 0.8;
  z-index: 1;
}

.day-font {
  width: 163px;
  height: 30px;
  left: 96px;
  font-family: Roboto;
  position: $position-relative;
  top: 20px;
  font-size: 26px;
  text-align: $position-center;
  letter-spacing: 0px;
  color: #dddddd;
  opacity: 1;
}

.timescale {
  left: 605px;
  position: $position-absolute;
  display: $display-flex;
}

.program-details-wrapper {
  display: $display-flex;
  position: $position-relative;
  // justify-content: space-around;
  align-items: $position-center;
  padding-right: 5px;//Addding this so as to avoid touching of record icon with wrapper border incase of more margin-left applied events
  // width: 90%;
  .program-details {
    position: $position-relative;
    overflow: hidden;

    .program-title {
      letter-spacing: 0px;
      color: $color-white;
      opacity: 1;
      font-family: Roboto;
      font-size: 36px;
      // font-weight: bold;
      letter-spacing: 0;
      line-height: 39px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      .program-title-span {
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .epg-grid-program-record-img {
        // left: 20px; not needed for current implementation ,hence commented but kept for future referrence
        // bottom: 10px;
        position: relative;
        // top: 5px;
        margin-left: 17.75px;
        // display: flex;
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: nowrap;
      }

      .epg-grid-program-record-img2 {
        position: relative;
        // top: 5px;
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: nowrap;
        margin-right : 20px;
      }
    }

    .program-time {
      font: normal normal normal 24px/28px Roboto;
      letter-spacing: 0px;
      color: $color-white;
      opacity: 1;
      text-align: $position-left;
      text-transform: capitalize;
      max-width: fit-content;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}

// .program-details-wrapper.program-visible.program-right {
//   align-self: flex-start;
//   margin-left: 16px;
//   justify-content: start;
// }

// .program-details-wrapper.program-visible.program-left {
//   align-self: flex-end;
//   margin-right: 16px;
//   justify-content: end;
// }

// .program-details-wrapper.program-visible.program-start {
//   align-self: flex-start;
//   margin: 0px;
//   justify-content: start;
// }

.epg-wrapper {
  display: $display-flex;
  flex-direction: column;
  justify-content: $position-center;

  .event-alert-main-epg {
    display: flex;
    position: relative;
    bottom: 624px;
    align-items: center;
    z-index: 5;
    flex-direction: row;
    justify-content: center;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.71) 42.99%,
      rgba(0, 0, 0, 0.96) 100%
    );
    height: 624px;
    //padding-top: 154px;
    //  background: linear-gradient(0deg, rgba(0, 0, 0, 0.95) 65%, rgba(161, 161, 161, 0) 100%);
    //height: 250px;

    .event-tooltip {
      display: flex;
      padding: 0px 30px;
      border-radius: 18px;
      height: 80px;
      color: white;
      background: #2e303d;
      font-size: 40px;
      align-items: center;
      margin-right: 15px;
      position: relative;
      //position: fixed;
      // bottom: 124px;

      .alert-text {
        font-size: 36px;
        color: #ffffff;
        font-family: Roboto;
      }
    }
  }
}

.description-wrapper {
  overflow: hidden;
  justify-content: right;
  width: 1920px;
  height: 456px;
  background: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.75) 0%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

.epg-grid-wrapper {
  display: $display-block;
  width: 1920px;
  height: 624px;
  background: #000000 0% 0% no-repeat padding-box;
  overflow: auto;
  position: $position-relative;
}

.epg-channel-program-grid {
  table-layout: fixed;
  border-spacing: 0;
  white-space: nowrap;
}

.epg-timeline-wrapper {
  // display: $display-block;
  position: $position-sticky;
  top: 0;
  z-index: 2;
  height: 64px;
  overflow: hidden;
}

.epg-timeline {
  left: 0;
  background: #000000 0% 0% no-repeat padding-box;
  margin: 0px;
  display: flex;
  flex-direction: row;
}

.epg-day {
  height: 47px;
  padding-top: 17px;
  left: 0;
  width: 226px;
  position: fixed;
  z-index: 3;
  margin: 0;
  display: $display-flex;
  justify-content: $position-center;
  background: #000000 0% 0% no-repeat padding-box;
  opacity: 1;
}

.epg-hours-scale {
  display: $display-flex;
  margin-left: 150px;
}

//Fixed focus fix need for future reference

/* .epg-hours-scale2 {
  display: $display-flex;
  margin-left: 228px;
}
 */

.epg-timescale {
  padding-right: 566.35px;
  height: 64px;
  display: flex;
  flex-direction: row;
  background: #000000 0% 0% no-repeat padding-box;
  position: $position-sticky;
}

.epg-timescale-48 {
  height: 64px;
  display: none; // For future purpose maintain
  flex-direction: row;
  background: #000000 0% 0% no-repeat padding-box;
  position: relative;
  right: 40px;
}

// .epg-channel-program {
//   left: 0;
//   max-height: 560px;
// }

.channel-program-list {
  display: inline-flex;
  flex-direction: row;
  position: relative;
}

.epg-channel-style {
  background-color: #000000;
  position: fixed !important;
  z-index: 3;
  left: 0;
  border-right: 1px solid #585858;
  padding-right: 10px;
  top: 519px;
  border-bottom: 9px solid #000000;
  height: 570px;
}

.main-alert-container {
  display: flex;
  position: relative;
  top: 148px;
}

.epg-program-style {
  left: 216px;
  overflow: scroll;
  scroll-snap-type: x mandatory;
  border-top: 1px solid #585858;
}

.channel-program-row {
  margin: 0;
  left: 0;
  display: $display-flex;
  width: auto !important;
}

.main-epg-nav {
  display: flex;
  flex-direction: row;
}

.epg-channel {
  position: $position-sticky !important;
  left: 0;
  z-index: 1;
  opacity: 1;
  height: 108px;
  width: 216.28px;
  margin: 2.5px 7.5px 2.5px 0px;
  display: $display-flex;
  justify-content: $position-center;
  align-items: $position-center;
}

.epg-program {
  box-sizing: border-box;
  height: 112px;
  background-color: #1a1a1a;
  vertical-align: middle;
}

.epg-channel-container {
  display: $display-flex;
}

.epg-channel-image {
  display: $display-block;
  height: 64px;
  width: 144px;
}

.epg-channel-details {
  display: $display-flex;
  flex-direction: column;
  justify-content: $position-center;
  align-items: $position-center;
}

.error-notification {
  position: fixed;
  z-index: 3;
  width: 100%;
  height: 624px;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.71) 42.99%,
    rgba(0, 0, 0, 0.96) 100%
  );
}

.favorite-channel-error {
  background-color: #2e303d;
  border-radius: 17px;
  height: 115px;
  width: 70%;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  bottom: 75px;
}

.favorite-error-msg {
  color: #ffffff;
  font-size: 35px;
  position: absolute;
  left: 123px;
  top: 0px;
  font-weight: bold;
}

.favorite-msg {
  color: #ffffff;
  font-size: 32px;
  position: absolute;
  left: 613px;
  top: 3px;
}

// commented as this class is nowhere used
// .epg-channel-name,
// .epg-channel-number {
//   text-align: $position-left;
//   font: normal normal normal 28px/31px Roboto;
//   letter-spacing: 0px;
//   color: $color-white;
//   opacity: 1;
// }

.epg-day-font {
  margin: 0 40px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 34px;
  font-weight: 900;
  letter-spacing: 0;
  line-height: 30px;
  text-transform: uppercase;
}

.block-channel {
  z-index: 3;
  .block-icon-image {
    height: 32px;
    position: absolute;
    top: 11px;
    right: 7px;
  }
}

// .epg-grid-program-record-img {
//   // left: 20px; not needed for current implementation ,hence commented but kept for future referrence
//   // bottom: 10px;
//   position: relative;
//   // top: 5px;
//   margin-left: 21.1px;
//   // display: flex;
//   display: flex;
//   align-items: center;
//   flex-direction: row;
//   flex-wrap: nowrap;
// }

// .epg-grid-program-record-img2 {
//   position: relative;
//   // top: 5px;
//   display: flex;
//   align-items: center;
//   flex-direction: row;
//   flex-wrap: nowrap;
// }

.epg-grid-record-text {
  color: $color-white;
  font-family: Roboto;
  font-size: 25px;
  letter-spacing: 0;
  line-height: 32px;
  margin-left: 15px;
}

.epg-grid-program-reminder-img {
  position: absolute;
  margin-left: 25px;
}

.extra-div-content {
  background: #585858 0% 0% no-repeat padding-box;
}

.extra-div-content:focus {
  // scroll-snap-align: center;
  background: #981c15 0% 0% no-repeat padding-box !important;
  opacity: 1;
  border-radius: 3px;
  border: $display-none;
  color: $color-white;
  outline: $display-none;
  font-weight: bold !important;
}

.extra-div-content-title {
  text-align: left;
  color: #ffffff;
  opacity: 1;
  font-family: Roboto;
  font-size: 36px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 39px;
  padding-left: 10px;
}

.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  left: 50%;
  right: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.center-content1 {
  display: flex;
  justify-content: center;
  align-items: center;
  left: 50%;
  right: 50%;
  text-align: center;
}

.center-content2 {
  display: flex;
  text-align: left;
}

.program-details-wrapper.visible {
  opacity: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 50%;
  right: 50%;
  transform: translateX(-50%);
  text-align: center;
}
