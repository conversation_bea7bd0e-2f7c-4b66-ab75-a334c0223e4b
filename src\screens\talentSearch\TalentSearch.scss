.talent-search-layout {
  width: 1912px;
  height: 1064px;
  position: 'relative';
  opacity: 1;
  display: flex;
  flex-direction: column;
  background-color: black;

  .regresser-box {
    display: flex;
    justify-content: flex-end;
    width: 1906px;

    .talent-warning-backscreen {
      box-sizing: border-box;
      height: 58px;
      width: 298px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 20px;
      margin-left: 96px;
      margin-right: 53px;
      border-radius: 6.6px;
      background-color: #2e303d;

      &:focus,
      &:active {
        transform: scale(1.1);
        background: #981c15;
      }

      .talent-back-img-icon {
        margin-bottom: 17px;
        margin-left: 20px;
        margin-right: 3px;
        margin-top: 20px;
      }

      .talent-back-button-regresar-title {
        height: 33px;
        width: 208px;
        color: white;
        font-family: Roboto;
        font-size: 28px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 30px;
        text-align: center;
        margin-bottom: 17px;
        margin-right: 3px;
        margin-top: 20px;
      }
    }
  }

  .talent-search-title {
    height: 64px;
    color: #ffffff;
    font-family: <PERSON><PERSON>;
    font-size: 57px;
    letter-spacing: 0;
    line-height: 64px;
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 64px;
    margin-bottom: 35px;
  }

  .talent-search-container {
    gap: 5px;
    max-width: 1920px;
    // margin-bottom: 64px;
    margin-left: 20px;
    position: relative;
    overflow-x: scroll;
    height: 1040px;
    background-color: black;

    .talent-search-wrapper {
      display: flex;
      flex-wrap: wrap;
      margin-left: 51px;
      width: 1825px;
      .rectangle1 {
        height: 104px;
        width: 424px;
        background-color: #26272a;

        text-align: center;
      }
      .Rectangle {
        display: flex;
        gap: 5px;
      }

      .rectangle2 {
        height: 88px;
        width: 376px;
        background-color: #26272a;
        text-align: center;
      }

      .rectangle3 {
        height: 88px;
        width: 376px;
        background-color: #26272a;
        text-align: center;
      }
      .buttonText {
        height: 40px;
        width: 360px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 32px;
        letter-spacing: 0;
        line-height: 40px;
        text-align: center;
      }

      .talent-search-block {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        margin-right: 27px;
        margin-bottom: 33px;
        width: 420px;
        height: 240px;
        opacity: 0.8;
        box-sizing: border-box;
        // padding: 4px;
        
        &:hover {
          display: inline;
          cursor: pointer;
        }
        .focus-img {
          display: inline;
          cursor: pointer;
        }

        &:focus,
        &:active {
          border: 6px solid #fff !important;
          border-radius: 6px;
          transform: scale(1);
          opacity: 1;
          margin-top: 4px;
          margin-bottom: 4px;
        }
      }

      .talent-focus-img {
        display: flex;
        position: relative;
        top: 0;
        left: 9px;
        z-index: 99;
      }

      .talent-search-image {
        width: 412px;
        height: 232px;
        display: flex;
      }
      .talent-search-card-image {
        width: 408px;
        height: 232px;
        border-radius: 3px;
        background-size: 100% 100%;
      }

      .talent-search-card:focus > .talent-search-card-image,
      .talent-search-card:active > .talent-search-card-image {
        width: 432px;
        height: 242px;
      }

      .inline-progressbar-layouts {
        position: absolute;
        left: 9px;
        top: 99px;
        display: flex;
        height: 32px;
        width: 130px;
      }

      .proveedor-block-rail-alq {
        position: absolute;
        left: 8px;
        top: 8px;

        .tag-alq {
          width: 111px;
          height: 39px;
        }
      }

      .proveedors-block-rail {
        position: absolute;
        left: 9px;
        top: 7px;
        display: flex;
        height: 32px;
        width: 130px;

        .tags {
          width: 111px;
          height: 38px;
        }
      }

      .proveedor-block-rail {
        position: absolute;
        left: 7px;
        top: 7px;
        display: flex;

        .tag {
          width: 161px;
          height: 38px;
        }
      }
    }
  }
}

//ErrorEventmodule
.error-boundry-layout {
  width: 1908px;
  height: 930px;
  display: flex;
  flex-direction: column;

  .App-logo-left {
    height: 40px;
    width: 189px;
    margin-left: 96px;
    margin-top: 56.9px;
    .logo-img {
     width: 187.494px;
     height: 38.174px;
    }
  }

  .warning-icon-logo {
    height: 80px;
    width: 80px;
    display: flex;
    justify-content: center;
    margin-top: 96.92px;
    margin-left: 920px;
    margin-right: 920px;
    margin-bottom: 80px;
  }

  .warning-content {
    display: flex;
    flex-direction: column;
    margin-left: 400px;
    margin-right: 400px;

    .estamos-experimentan {
      height: 56px;
      width: 1120px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 48px;
      font-weight: 700;
      letter-spacing: 0;
      line-height: 52px;
      text-align: center;
    }

    .por-el-momento-no-pu {
      height: 104px;
      width: 1120px;
      color: #eeeeee;
      font-family: Roboto;
      font-size: 40px;
      letter-spacing: 0;
      line-height: 48px;
      text-align: center;
      margin-top: 80px;
      display: flex;
      justify-content: center;
      flex-direction: column;
    }


    .por-favor-intenta-ma {
      height: 48px;
      width: 1120px;
      color: #eeeeee;
      font-family: Roboto;
      font-size: 40px;
      letter-spacing: 0;
      line-height: 48px;
      text-align: center;
      margin-top: 8px;
    }

    .warning-button-div {
      flex-direction: column;
      display: flex;
      margin-top: 80px;
      height: 80px;
      width: 576px;
      justify-content: center;
      margin-left: 272px;
    }

    .warning-title-button {
      display: flex;
      height: 80px;
      width: 576px;
      border-radius: 10.03px;
      background-color: #981c15;
      opacity: 1;

      &:focus,
      &:active {
        z-index: 1;
        border-radius: 10.02px;
        opacity: 1;
        transform: scale(1);
      }
    }

    .warning-title-button-Contents {
      height: 32px;
      width: 528px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
      margin: 24px;
    }
  }
} 