.transactions-error-container {
  display: flex;
  height: 1080px;
  width: 1920px;
  position: absolute;
  flex-direction: column;

  .transactions-error-logo {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;

    .claro-logo-te {
      display: block;
      width: 187.494px;
      height: 38.174px;
      flex-shrink: 0;
      margin-left: 96px;
      margin-top: 56px;
    }
  }

  .transactions-error-base-layout {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .transactions-warning-sign {
    margin-top: 100px;
    height: 80px;
  }

  .transactions-error-title {
    margin-top: 80px;
    height: 56px;
    color: #fff;
    text-align: center;
    font-family: <PERSON><PERSON>;
    font-size: 48px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  .transactions-error-title-2 {
    margin-top: 80px;
    height: 48px;
    color: #eee;
    text-align: center;
    font-family: Roboto;
    font-size: 40px;
    font-style: normal;
    font-weight: 400;
    line-height: 48px; /* 120% */
  }

  .transactions-error-button-container {
    margin-top: 80px;
    height: 82px;

    .transactions-error-button {
      height: 72px;
      width: 504px;
      border-radius: 8.8px;
      flex-shrink: 0;
      border-radius: 8px;
      background: #981c15;
      display: flex;
      justify-content: center;
      align-items: center;

      .transactions-error-button-text {
        color: #fff;
        text-align: center;
        font-family: Roboto;
        font-size: 36.5px;
        font-style: normal;
        font-weight: 700;
        line-height: 42.18px; /* 115.562% */
        letter-spacing: -0.582px;
        text-transform: uppercase;
      }

      &:focus {
        width: 580.26px;
        height: 82.08px;
        flex-shrink: 0;
        border-radius: 10.032px;
        background: #981c15;
      }
    }
  }
}
