$color-white: #fff;
$btn-focus: #981c15;


.railsHome {
  height: 930px;
  top: 9rem;
  width: 100%;
  overflow: hidden;
}
.miscontenidos-background{
  background-color: #121212; 
}

.railsHome-regpopup {
  height: 75vh;
  top: 9rem;
  width: 100%;
  overflow: scroll;
}

.backBtn:focus {
  background: $btn-focus 0% 0% no-repeat padding-box;
}

.continueWatch-title {
  margin-bottom: 39px;
}

.backBtn-mycontent:focus {
  border: 2px solid $color-white;
}

.backText {
  color: $color-white;
  font-size: 29px;
  line-height: 29px;
}

.titleImage {
  color: $color-white;
  position: absolute;
  bottom: 0;
  flex-wrap: wrap;
}

.test:focus {
  box-shadow: 2px 1px 20px 8px red;
}


.buttonclass {
  margin-top: 88px;
}

.delete-image,.delete-image-src {
  width: 471.5px;
  height: 264.5px;
  margin-bottom: 23px;
  margin-top: 72px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.delete-image:focus {
  border: 2px solid $btn-focus;
}

.confirmdelete {
  margin-top: -100px;
}

.nocontentitle {
  color: #eeeeee;
  font-size: 36px;
}