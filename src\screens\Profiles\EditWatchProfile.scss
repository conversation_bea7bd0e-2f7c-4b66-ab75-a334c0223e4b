.watchingProfile{
    height:1080px;
    width:1920px;
    background-color: #121212;
.appLogo{
    width: 1920px;
    display: flex;
    justify-content: space-between;
    background-color: #121212;
    .logoImg{
        margin-top: 50px;
        margin-left: 96px;
        width: 171px;
        height: 36px;
     }
    
}
.backButton{
    height: 48px;
    width: 292px;
    border-radius: 6.6px;
    background-color: #2E303D;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 50px;
    margin-left: 96px;
    margin-right: 53px;
    
  .yellowDot{
    height: 20px;
    width: 20px;
}
.backArrow{
    height: 24px;
    width: 30px;
}
.backButtonText{  
    height: 30px;
    width: 146px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
}
  }
  .backButton:focus{
    background-color: #981c15;
  }
.profileWatch {
    height: 70px;
    width: 849px;
    color: #F4F4F4;
    font-family: Roboto;
    font-size: 48px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 50px;
    text-align: center;
    margin-left: 536px;
}

.profileImgContainer {
    display: flex;
    justify-content: center;
     margin-top: 80px;
    height:337px;
    align-items: center;
    .imageTitle {
        text-align: center;
        position: relative;

        .editUserImage { 
            background-repeat: no-repeat !important;
            background-size: cover !important;
            border-radius: 50%;
            display: flex;
            opacity:0.5;
            width:100%;
            height:100%;
          
            }
    }
}
.userProfileImage{
display: block;
width:247px;
height:247px;
margin-left:25px;
margin-right:25px;
}
.userProfileImage:focus{

    padding: 8px;
    border: 4px solid white;
    border-radius: 50%;
    margin-right: 4px;
    margin-left: 4px;
    height: 285px;
    width: 285px;

}
.userProfileImage:focus>.editIconImg,
.userProfileImage:active>.editIconImg {
    top: 125px;
    left: 120px;
}

.userProfileImage:focus>.editUserImage,
.userProfileImage:active>.editUserImage{
    opacity: 1;
}
.penIconImg{
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 224px;
    width: 224px;
    left:30px;
    top:19px;

        .editIconImg {
        margin: auto;
        display: flex;   
        box-sizing: border-box;
        height: 92.54px;
        width: 92.54px; 
     }

}
.userProfileImage:focus>.penIconImg{
    position: absolute;
    left: 56px;
    top: 43px;
}
.userProfName{
  height: 36px;
  width: 250px;
  color: #FFFFFF;
  opacity: 0.7;
  font-family: Roboto;
  font-size: 32px;
  letter-spacing: 0;
  line-height: 35px;
  text-align: center;
  text-shadow: 0 2px 4px 0 #000000;

}

.userProfileImage:focus>.UserImage,
.userProfileImage:active>.UserImage,
.userProfileImage:focus>.lazy-load-image-background,
.userProfileImage:active>.lazy-load-image-background {
    z-index: 1;
    background: transparent 0% 0% no-repeat;
    outline: unset;
    border-radius: 50%;
    padding: 10px;
    opacity: 1;
    margin-right: 30px;
    height: 285.08px;
    width: 285px;
   
}

.userProfileImage:focus>.userProfName,
.userProfileImage:active>.userProfName {
    height: 41.4px;
    width: 290.23px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 36.8px;
    letter-spacing: 0;
    line-height: 40px;
    text-align: center;
    text-shadow: 0 2px 5px 0 #000000;
    margin-top: 20px;
    opacity: 1;
}
.listoEditButton{
    height: 82.08px;
    width: 580.26px;
    border-radius: 10.03px;
    background-color: #981C15;
    margin-top: 149px;
    margin-left: 706px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: sticky;

    .listoText{
        height: 43px;
        width: 98px;
        color: #FFFFFF;
        font-family: Roboto;
        font-size: 36.5px;
        font-weight: bold;
        letter-spacing: -0.58px;
        line-height: 42.18px;
        text-align: center;
    }
}

.listoEditButton:focus{
    transform: scale(1.1);
    background: #981C15 0% 0% no-repeat padding-box;
    border-radius: 8.8px;
    opacity: 1;
}
.user-prof-name {
    height: 36px;
    width: 250px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 32px;
    letter-spacing: 0;
    line-height: 21px;
    text-align: center;
    text-shadow: 0 2px 4px 0 #000000;
    margin-left: 16px;    
  }
.user-profile-image:focus,
.user-profile-image:active {
    z-index: 1;
    background: transparent 0% 0% no-repeat;
    border: 3px solid #ffff; 
    outline: unset;
    border-radius: 50%;
    padding: 10px;
    opacity: 1;
    height: 285.08px;
    width: 285px;
    margin-right:6px;
    margin-left:6px;
    margin-top:0px;
   
}
.user-profile-image:focus>.user-prof-name,
.user-profile-image:active>.user-prof-name {
    height: 41.4px;
    width: 295.23px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 36.8px;
    letter-spacing: 0;
    line-height: 24.15px;
    text-align: center;
    text-shadow: 0 2px 5px 0 #000000;
    margin-top: 32.18px;
}
.user-image {
    height: 100%;
    width: 100%;

}
}