import React, { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import '../loginregister/Loginpage.scss'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const LoginForgotPasswordMail = () => {
    const { state } = useLocation()
    const [email, setEmail] = useState(state?.value)
    
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

    const region = localStorage.getItem('region')
    const translations =
        apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const apilanguage = translations?.language?.[region]
    const navigate = useNavigate()

    const navSignin = e => {
        e.preventDefault()
        navigate('/loginforgotpassword', {
            state: { focus: true, email: email, value: email }
        })
    }
    const handleChange = value => {
        setEmail(value)
    }

    const keypresshandler = event => {
        if (event?.keyCode === 10009 || event?.keyCode === 461) {
            navigate('/loginforgotpassword', { state: { value: state?.value } })
        }
        if (event.keyCode == 13 || event.keyCode == 65376 || event.keyCode == 40) {
            event.preventDefault()
            event.stopPropagation()
            document.getElementById('signinid').focus({ focusVisible: true })
        } else if (event.keyCode == 38) {
            event.preventDefault()
            event.stopPropagation()
            document.getElementById('email').focus({ focusVisible: true })
        }
    }

    useEffect(() => {
        document.addEventListener('keyup', keypresshandler)
        return () => {
            document.removeEventListener('keyup', keypresshandler)
        }
    }, [keypresshandler])

    useEffect(()=>{
        pushScreenViewEvent({screenName:'login_forgot_password_mail', screenData: userDetails, prevScreenName: 'login_forgot_password'})
    },[])

    const handleTranslationchange = useCallback(keyname => {
        if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
            return [keyname]
        } else {
            return apilanguage?.[keyname]
        }
    }, [])

    return (
        <div className="App">
            <div className="App-logo">
                <img
                    src={'images/claro-video-logo.png'}
                    className="logo-img"
                    alt="logo"
                />
            </div>
            <div className="login-forgot-email-container">
                <p className="recpassword-Title">
                    {handleTranslationchange('Onboarding_Inicio_sesionRCU_Form_TextoTitulo')}
                </p>
                <input
                    className="recregister-namerec-mail"
                    id="email"
                    name="email"
                    value={email}
                    onChange={e => handleChange(e.target.value)}
                    autoFocus
                    maxLength={46}
                />
            </div>
            <div className="login-forgot-email-btn">
                <button
                    className="recsign-next-email focusable"
                    disabled={!email}
                    id="signinid"
                    onClick={e => navSignin(e)}>
                    {handleTranslationchange('login_access_option_button_next')}
                </button>
            </div>
        </div>
    )
}

export default LoginForgotPasswordMail