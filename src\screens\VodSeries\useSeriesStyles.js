import { useCallback, useMemo } from 'react'

export const useContentSeriesTitleStyles = vodSeriesCastRedux => {
  const titleLength =
    vodSeriesCastRedux?.common?.extendedcommon?.media?.serie?.title?.length || 0
  const originalTitleLength =
    vodSeriesCastRedux?.common?.extendedcommon?.media?.originaltitle?.length ||
    0

  const calculateTop = useCallback(() => {
    if (titleLength > 28 && originalTitleLength <= 27) return '-39px'
    if (titleLength === 30 && originalTitleLength >= 27) return '30px'
    if (titleLength === 29 && originalTitleLength >= 27) return '75px'
    if (titleLength > 28 && originalTitleLength >= 27) return '4px'
    return ''
  }, [titleLength, originalTitleLength])

  const calculateMaxHeight = useCallback(() => {
    return titleLength > 28 ? '144px' : '72px'
  }, [titleLength])

  const calculateMaxWidth = useCallback(() => {
    if (titleLength === 29 && originalTitleLength >= 27) return '864px'
    if (titleLength === 29) return '824px'
    return '1035px'
  }, [titleLength, originalTitleLength])

  return useMemo(
    () => ({
      top: calculateTop(),
      position: 'relative',
      maxHeight: calculateMaxHeight(),
      maxWidth: calculateMaxWidth()
    }),
    [calculateTop, calculateMaxHeight, calculateMaxWidth]
  )
}
