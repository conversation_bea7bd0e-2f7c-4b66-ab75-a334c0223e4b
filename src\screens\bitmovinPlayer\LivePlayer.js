import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Player, PlayerEvent } from 'bitmovin-player'
import 'bitmovin-player/bitmovinplayer-ui.css'
import { useLocation, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import moment from 'moment'
import <PERSON><PERSON> from 'react-lottie-player'
import animationData from '../../../src/json/animationData.json'
import './Player.scss'
import MenuBar from '../../../src/screens/epg/EpgMainPage/MenuBar'
import {
  getClearAllSettingsState,
  getLiveIndex,
  getLockedChannelsList,
  getSubscriptionInfo
} from '../../store/slices/settingsSlice'
import {
  getLivePlayer,
  getChannelData,
  resetLivePlayer,
  getAvailableAudio,
  getDefaultAudio,
  setPlayerInstance,
  removePlayerInstance,
  getRecordPlayer,
  clearGetMediaRes,
  getAvailableSubtitles,
  setLivePlayerCertificate,
  clearRecordPlayerInfo,
  getPlayerTrackAPI
} from '../../store/slices/PlayerSlice'
import { useDispatch } from 'react-redux'
import { reqOptionsForGet } from '../../apiCall/query'
import PlayerErrorHandler from './PlayerErrorHandler'
import ProgramDetails from '../programdetails/ProgramDetails'
import {
  getProgramDetailsData,
  getAlerts,
  resetFavouriteAlerts,
  getLiveTvRecording,
  getLiveReminder,
  getFavouriteLive,
  getPopUpState,
  getEpgChannel,
  getLiveTvProgressRecording,
  getLiveTvCompleteRecording,
  getChannelDownId,
  getLiveTvSeriesRecordingList,
  setFromHomeToLive
} from '../../store/slices/EpgSlice'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import { getGuestUserPlayerData } from '../../store/slices/login'
import Epg from '../epg/EpgMainPage/Epg'
import MiniEpg from '../epg/EpgMainPage/MiniEpg'
import { store } from '../../store/sagaStore'
import appInfo from '../../../amx/appinfo.json'
import { COMMON_URL } from '../../utils/environment'
import {
  pushNewInteractionContentEvent
} from '../../GoogleAnalytics'
import { CONTENIDO_BLOQUEADO, interactionType, NOT_APPLICABLE, PROTECTED_CONTENT, TV } from '../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const LivePlayer = () => {
  const play_focus = 'images/Vod_Icons/play_focus.png'
  const play_unfocus = 'images/Vod_Icons/play_unfocus.png'
  const pause_focus = 'images/Vod_Icons/pause_focus.png'
  const pause_unfocus = 'images/Vod_Icons/pause_unfocus.png'
  const rewind_focus = 'images/Vod_Icons/rewind_focus.png'
  const rewind_unfocus = 'images/Vod_Icons/rewind_unfocus.png'
  const forward_focus = 'images/Vod_Icons/forward_focus.png'
  const forward_unfocus = 'images/Vod_Icons/forward_unfocus.png'
  const language_focus = 'images/Vod_Icons/language_focus.png'
  const language_unfocus = 'images/Vod_Icons/language_unfocus.png'
  const record_focus = 'images/LivePlayer_Icons/record_focus.png'
  const record_unfocus = 'images/LivePlayer_Icons/record_unfocus.png'
  const record_inprogress = 'images/LivePlayer_Icons/record_inprogress.png'
  const record_completed = 'images/LivePlayer_Icons/record_completed.png'
  const epg_focus = 'images/LivePlayer_Icons/epg_icon.png'
  const live_image = 'images/LivePlayer_Icons/live_icon.png'
  const start_over_image = 'images/LivePlayer_Icons/tag_ahora_inactive.png'
  const past_event_image = 'images/LiveTv/tag_emitido.png'

  const dispatch = useDispatch()
  const navigate = useNavigate()
  // Using the useLocation hook to get the current location object
  const { state } = useLocation()
  // const { getMedia } = state;
  const playerDiv = useRef()
  const ref = useRef(null)
  const region = localStorage.getItem('region')
  const lastTouch = localStorage.getItem('lasttouch')
  const seekCountRef = useRef(0)
  const liveEventName = localStorage.getItem('programName')
  const deviceId = localStorage.getItem('npaw.npawDeviceUUID')

  // The below variables should not be reset over re-render hence opted for useRef
  const subscribedChannels = useRef([])
  const subscribedChannelsWithTimeshift = useRef([])
  const filteredLiveChannels = useRef([])
  //Using the ref values here so that even after the page re-render we need the persistent values
  const numRef = useRef('')
  const numRefTimer = useRef(null)
  const timeoutRef = useRef(null)
  const alertPastRef = useRef(null)
  const alertTsRef = useRef(null)
  const alertAddFavourite = useRef(null)
  const pastChannelAlert = useRef(null)
  const audioData = useRef(false)
  const trackStartTimer = useRef('')//This useRef will be used to store the setInterval function and to set again only whern this value is ''
  const [player, setPlayer] = useState(null)
  const [currentVodData, setCurrentVodData] = useState([])
  const [currentButtonFocus, setCurrentButtonFocus] = useState('play')
  const [playerStatus, setPlayerStatus] = useState('')
  const [totalDuration, setTotalDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [showLiveControls, setShowLiveControls] = useState('')
  const [showLoader, setShowLoader] = useState(false)
  const [runningTime, setRunningTime] = useState(0)
  const [isSelectedChannelTS, setIsSelectedChannelTS] = useState(false)
  const [playerError, setPlayerError] = useState(false)
  const [liveTvError, setLiveTvError] = useState(false)
  const [showOptionsPanel, setShowOptionsPanel] = useState(false)
  const [selSubs, setSelSubs] = useState(-1)
  const [num, setNum] = useState(null)
  const [pausedTime, setPausedTime] = useState('')
  const [timeShiftError, setTimeShiftError] = useState('')
  const [isPlayerStalled, setIsPlayerStalled] = useState(false)
  const [isPastContent, setIsPastContent] = useState(false)
  const [liveTvLoader, setLiveTvLoader] = useState(false)
  const [livePlayerClose, setLivePlayerClose] = useState(false)
  const [addFavLastTouch, setAddFavLastTouch] = useState('')
  const [deleteFavLastTouch, setDeleteFavLastTouch] = useState('')
  const [subtitleText, setSubtitleText] = useState('')
  const [buttonClicked, setButtonClicked] = useState(false)
  const [channelInformation, setChannelInformation] = useState('')
  const [guestUserGrid, setGuestUserGrid] = useState(false)
  const [lockChannelFlag, setLockChannelFlag] = useState(true)
  const [lockedScreen, setLockedScreen] = useState(false)
  const [isCertificateAvailable, setIsCertificateAvailable] = useState(false)
  const [checkPastContent, setCheckPastContent] = useState(false)
  const [isFromPinCancel , setIsFromPinCancel] = useState(state?.pinCancel ? true : false )
  const [fromUnlockPinPage, setFromUnlockPinPage] = useState(state?.fromUnlockPinPage ? true : false)
  const focusButton = useRef(null)
  const paywayKeyRef = useRef([])
  const gaPaywayRef = useRef(null)
  const contentDataRef = useRef(null)
  const recordAlertMsg = useSelector(state => state?.epg?.recordingAlerts)
  const favouriteAlertMessage = useSelector(
    state => state?.epg?.favouriteAlerts
  )
  const showMyContentButton = useSelector(
    state => state?.epg?.showMyContentButton
  )

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const epgVersion = useSelector(state => state?.epg?.epgVersion)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const liveChannelsData = useSelector(state => state?.epg?.epgChannel)
  // const epgDataBackUp = useSelector(state => state?.epg?.epgChannelBackup)
  const liveChannnelInfo = useSelector(state => state?.player?.playerinfo)
  const addfavouriteList = useSelector(state => state?.epg?.addfavouriteList)
  const delfavourite = useSelector(state => state?.epg?.delfavourite)
  const programdetailsdata = useSelector(
    state => state?.epg?.viewProgramDetailsData
  )
  const startheaderinfo = useSelector(
    state => state?.initialReducer?.startHeaderInfo?.response
  )
  const scheduleReminder = useSelector(state => state?.epg?.scheduleReminder)
  const isReminderPopupVisible = useSelector(
    state => state.epg.isReminderPopupVisible
  )
  const youboraInstance = useSelector(
    state => state?.player?.npawPluginInstance
  )
  const liveCertificate = useSelector(
    state => state?.player?.livePlayerCertificate
  )
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )

  // {payway?.[0]?.npvrstorage > 0 && payway?.[0]?.timeshift > 0 &&
  //   programdetailsdata?.channelData?.group?.common?.timeshift !==null && programdetailsdata?.programData?.ext_recordable == '1'
  const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const playerChannelData = useSelector(state => state?.player?.channelData)
  const unlockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelDelete
  )
  const lockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelAdd
  )
  const navData = useSelector(state => state?.homeReducer?.navbarData)
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )

  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getSubsInfo
  )
  const playerControlsInterval =
    apaMetaData?.playerControls_auto_hide_time &&
    JSON?.parse(apaMetaData?.playerControls_auto_hide_time)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const supportedStream =
    apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)
  const apilanguage = translations?.language?.[region]
  const recordingplayer = useSelector(
    state => state?.player?.recordplayerinfo?.response
  )
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const guest = useSelector(state => state?.login?.anonymousUser)
  const recordingList = useSelector(
    state => state?.epg?.RecordingList?.response
  )
  const seriesRecordingList = useSelector(
    state => state?.epg?.RecordingListSeries
  )
  const checkFromHomeToLive = useSelector(state => state?.epg?.fromHomeToLive)

  const registerUser = localStorage.getItem('register_user')

  var timeOutFunc = ''
  let startOverCounter = 0
  const startOverCounterRef = useRef(0)
  // let miniEpgTimeout;
  const miniEpgTimeout = useRef()
  const startedOverRef = useRef(null)
  const currentEventRef = useRef(null)
  const startedOverTimeRef = useRef(0)
  const liveStartedRef = useRef(false)
  const isSelectedChannelTSRef = useRef(false)
  const rewindTimeRef = useRef(0)
  const forwardTimeRef = useRef(0)
  const rwFwRef = useRef(false)
  const playerInstanceRef = useRef({})
  const showScheduleReminderRef = useRef(false)
  const wasReminderPopupVisible = useRef(false)

  const liveChannelId = localStorage.getItem('live-playing-channel-id')

  const miniEpgAutoHideTimeOut =
    apaMetaData?.mini_epg_auto_hide_seconds &&
    JSON.parse(apaMetaData?.mini_epg_auto_hide_seconds)
  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  const getRecordText = () => {
    const recordCheckValue = currentEventRef.current?.event_alf_id
    const liveTVEpisodes = recordingList?.recordings?.find(
      each => each?.channel?.event?.event_alf_id == recordCheckValue
    )

    let liveTVSeries = []
    seriesRecordingList?.map(each =>
      each?.seriesResponse?.recordings?.map(every =>
        every?.channel?.event?.event_alf_id == recordCheckValue
          ? liveTVSeries.push(every)
          : null
      )
    )

    switch (
      liveTVEpisodes?.recording_status ??
      liveTVSeries?.[0]?.recording_status
    ) {
      case 0:
        return `${truncateText('Player_Boton_TextoAccion_Grabando', 30)}`
      case 2:
        return `${truncateText('Player_Boton_TextoAccion_Grabado', 30)}`
      default:
        return `${truncateText('Player_Boton_TextoAccion_Grabar', 30)}`
    }
  }

  const getRecordIcon = () => {
    const recordCheckValue = currentEventRef.current?.event_alf_id
    const liveTVEpisodes = recordingList?.recordings?.find(
      each => each?.channel?.event?.event_alf_id == recordCheckValue
    )

    let liveTVSeries = []
    seriesRecordingList?.map(each =>
      each?.seriesResponse?.recordings?.map(every =>
        every?.channel?.event?.event_alf_id == recordCheckValue
          ? liveTVSeries.push(every)
          : null
      )
    )

    switch (
      liveTVEpisodes?.recording_status ??
      liveTVSeries?.[0]?.recording_status
    ) {
      case 0:
        return record_inprogress
      case 2:
        return record_completed
      default:
        return record_unfocus
    }
  }

  useEffect(() => {
    var seriesApiCallData = []
    new Promise(async (resolve, reject) => {
      const recordingPromises = []
      recordingList?.series?.map(each => {
        const promise = fetch(
          `${COMMON_URL.BASE_URL}/services/recordings/v1/series/list?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json&by_serie=0
          &user_token=${userDetails?.user_token}&user_id=${userDetails?.parent_id}&serie_id=${each?.serie_id}
          &season_id=${each?.season_id}&group_id=${each?.group_id}&language=${each?.language}&region=${region}`
        ).then(async data => {
          let value = await data.json()
          seriesApiCallData.push({
            seriesResponse: value?.response
          })
        })
        recordingPromises.push(promise)
      })
      Promise.all(recordingPromises)
        .then(() => {
          setTimeout(() => {
            resolve(dispatch(getLiveTvSeriesRecordingList(seriesApiCallData)))
          }, 500)
        })
        .catch(reject)
    })
  }, [recordingList])

  //The below useeffect will be run for any changes in channel change for playback
  useEffect(() => {
    if (
      playerChannelData?.switchChannel == 'yes' &&
      state?.showControls === 'live'
    ) {
      setPlayerError(false)
      liveStartedRef.current = true
      startLivePlayer(
        (playerChannelData?.startTime && playerChannelData?.endTime) ||
          playerChannelData?.timeshift
          ? true
          : false,
        playerChannelData?.startTime,
        playerChannelData?.endTime
      )
      currentEventRef.current = getCurrentEvent(playerChannelData?.group_id)
      rewindTimeRef.current = 0
      rwFwRef.current = false
    } else if (playerChannelData?.switchChannel == 'no') {
      currentEventRef.current = getCurrentEvent(playerChannelData?.group_id)
    }
    if (Object.keys(playerChannelData)?.length > 0) {
      currentEventRef.current = getCurrentEvent(playerChannelData?.group_id)
    }
  }, [playerChannelData])

  useEffect(() => {
    if (state?.page === 'record') {
      dispatch(getRecordPlayer(state?.getmediaurl))
    }
    if (recordingplayer) {
      setCurrentVodData(recordingplayer)
      localStorage.setItem('currentVodId', recordingplayer?.id)
      setIsSelectedChannelTS(true)
    }
  }, [recordingplayer])

  const startLivePlayer = (ts, start, end) => {
    clearTimeout(trackStartTimer.current)
    trackStartTimer.current = ""
    let startValueFromRedux = start
    let endValueFromRedux = end
    let curMomentTime = moment().unix()
    setLiveTvLoader(true)
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    /*The start and end values are needed for past contents and compared with current time moment().unix() before giving to the getMedia api call
    //Because incase if we pass any unchecked values the stream url cannot be obtained .Ex - future time values
    length check is done here because as per requirement only if fetched unix time is 10 digit then we must append 7 zero at the end*/
    setIsPastContent(start < moment().unix() && end < moment().unix())
    start =
      start &&
      start < moment().unix() &&
      (start.toString().length == 10 ? parseInt(start + '0000000', 10) : start)
    end =
      end &&
      end < moment().unix() &&
      (end.toString().length == 10 ? parseInt(end + '0000000', 10) : end)
    //As a safer check for corner scenarios, clearing the video element created by player while unmounting the component
    videoElement && videoElement?.pause() && videoElement?.remove()
    let mainId = document.getElementById('mainEpg')
    // if (!mainId) { // commenting these as we have handled destroyPlayer effectively using sufficient checks
    // player && player?.unload()
    if (Object.keys(playerInstanceRef.current)?.length > 0) {
      player && player?.unload()
    }
    player && destroyPlayer()
    // }
    //its Commanded for Lg green button issue"
    setPlayer(null)
    dispatch(removePlayerInstance())
    playerInstanceRef.current = {}

    //The below to be run only for live player and hence the check for live
    if (state?.showControls === 'live') {
      let subsToken = ''
      let subsTokenDefault = ''
      let tsToken = null
      //The stored channel will fetch channel value from redux if already set
      let storedChannel = playerChannelData?.group_id
      paywayResponse.length > 0 &&
        paywayResponse.map((val, index) => {
          //Removing the val play check as we are not getting the expected value for every pack in lineal response
          // To store all the subscribed channels
          subscribedChannels.current = subscribedChannels.current?.concat(
            val?.groups?.split(',')
          )
          // To store the subscribed channels with timeshift available
          val?.timeshift &&
            val?.timeshift != '0' &&
            (subscribedChannelsWithTimeshift.current =
              subscribedChannelsWithTimeshift.current?.concat(
                val?.groups?.split(',')
              ))

          // To store the payway_token, it is now fetched from payway lineal response directly
          index == 0 && (subsTokenDefault = val?.payway_token)
          if (val?.groups?.split(',')?.includes(storedChannel)) {
            if (subsToken == '') {
              // get the token of the matched subscription pack
              subsToken = val?.payway_token
            }
            if (
              (ts || playerChannelData?.timeshift) &&
              val?.timeshift &&
              val?.timeshift != '0'
            ) {
              tsToken = tsToken ?? val?.payway_token
            }
          }
        })
      if (guest?.confirmscreen) {
        filteredLiveChannels.current =
          liveChannelsData[1]?.channelResponse?.filter(
            val => val?.group_id == guest?.data?.group_id
          )
        dispatch(getGuestUserPlayerData())
      } else {
        filteredLiveChannels.current = storedChannel
          ? liveChannelsData[1]?.channelResponse?.filter(
              val => val?.group_id == storedChannel
            )
          : liveChannelsData[1]?.channelResponse?.filter(val =>
              subscribedChannels.current?.includes(val.group_id)
            )
      }

      //The below dispatch helps to get live stream related info by passing group_id and payway token

      if (filteredLiveChannels.current) {
        const isContractChannel = getIsContractChanel(
          filteredLiveChannels.current[0]?.group_id
        )

        if (filteredLiveChannels.current?.length == 0) {
          setPlayerError(true)
          setShowLiveControls('miniEpg')
          return
        }
        //The state variable isSelectedChannelTS will be set to true/false based on whether channel timeshifted or not
        if (ts || playerChannelData?.timeshift) {
          setIsSelectedChannelTS(
            subscribedChannelsWithTimeshift.current?.includes(
              filteredLiveChannels.current?.[0]?.group_id
            )
          )
          isSelectedChannelTSRef.current =
            subscribedChannelsWithTimeshift.current?.includes(
              filteredLiveChannels.current?.[0]?.group_id
            )
        }
        let currentEvent = getCurrentEvent(
          filteredLiveChannels.current?.[0]?.group_id
        )
        let liveGetMediaObj = {
          streamType: supportedStream?.default?.[CURRENT_PLATFORM]?.live?.[0],
          payway_token:
            tsToken ?? (subsToken != '' ? subsToken : subsTokenDefault),
          user_id: watchFree ? 0 : userDetails?.user_id,
          user_token: loginInfo?.user_token,
          HKS:
            userDetails?.session_stringvalue ??
            startheaderinfo?.session_stringvalue,
          group_id: filteredLiveChannels.current?.[0]?.group_id,
          startTime: tsToken && currentEvent?.unix_begin + '0000000'
          // endTime: tsToken && end,
        }
        if (watchFree) {
          liveGetMediaObj.type = 'watchfree'
        }
        if (
          subscribedChannelsWithTimeshift.current?.includes(
            filteredLiveChannels.current?.[0]?.group_id
          ) &&
          startValueFromRedux < curMomentTime &&
          endValueFromRedux < curMomentTime
        ) {
          liveGetMediaObj.startTime = (tsToken && start)?.toString()
          liveGetMediaObj.endTime = (tsToken && end)?.toString()
          localStorage.setItem('pastEvent', true)
        } 

        //Checking guest user contract offer
        if (isContractChannel && watchFree) {
          navigate('/EPconfirmation', {
            state: {
              page: 'livePlayer',
              data: filteredLiveChannels.current?.[0]
            }
          })
        }
        //subscription contract offer
        if (isContractChannel) {
          callPurchaseApi(isContractChannel, filteredLiveChannels.current?.[0])
          return
        }
        if (
          !fromUnlockPinPage &&
          lockedChannelsList?.find(
            each => each.id === filteredLiveChannels.current?.[0]?.group_id
          ) &&
          !watchFree
        ) {
          if (isFromPinCancel) {
            if (Object.keys(playerInstanceRef.current)?.length > 0) {
              player && player?.unload()
            }
            destroyPlayer()
            setPlayerError(true)
            setLockedScreen(true)
            setIsFromPinCancel(false)
          } else {
            //The below loc may be needed as we noticed an issue with the flow of mosaic
            // if (playerChannelData?.fromMosaic != true) {
            destroyPlayer()
            dispatch(removePlayerInstance())
            // }
            navigate('/my-settings/help-And-Settings/security-pin/configure', {
              state: {
                data: 'epg',
                item: filteredLiveChannels.current?.[0],
                pageName: '/livePlayer',
                featureTag: state?.featureTag,
                lockChannel: lockChannelFlag ? state?.tabValue : '',
                page: state?.page,
                backfocusid: state?.backfocusid,
                fromZap: false,
                checkPastContent: checkPastContent
              }
            })
          }
        } else {
          setLockedScreen(false)
          state?.showControls === 'live' &&
            handleSetTotalDuration(currentEvent?.duration)
          setLockChannelFlag(false)
          localStorage.setItem(
            'pastEvent',
            liveGetMediaObj?.endTime ? true : false
          ) 
          setIsFromPinCancel(false)
          setFromUnlockPinPage(false)
          dispatch(getLivePlayer(liveGetMediaObj))
        }
      } else {
        setLiveTvError(true)
      }
    }
  }

  useEffect(() => {
    const shouldAlert =
      showMyContentButton ||
      recordAlertMsg?.status === 'add' ||
      recordAlertMsg?.status === 'storagealert'
    pastChannelAlert.current = recordAlertMsg?.status === 'channel-events-past'
    alertAddFavourite.current = shouldAlert
  }, [showMyContentButton, recordAlertMsg])

  // The below useeffect will start the live player logic when payway response is updated
  useEffect(() => {
    if (
      (paywayResponse?.length > 0 || watchFree || registerUser) &&
      player == null &&
      !liveStartedRef.current &&
      state?.showControls === 'live'
    ) {
      liveStartedRef.current = true
      startLivePlayer(playerChannelData?.timeshift)
    }
  }, [paywayResponse, watchFree])

  useEffect(() => {
    if (scheduleReminder) {
      showScheduleReminderRef.current = true
    } else {
      showScheduleReminderRef.current = false
    }
  }, [scheduleReminder])

  useEffect(() => {
    if (wasReminderPopupVisible.current && !isReminderPopupVisible) {
      store.dispatch(getPopUpState(false))
      handleMiniEpgAutoShowHide()
    }
    wasReminderPopupVisible.current = isReminderPopupVisible
  }, [isReminderPopupVisible])

  useEffect(() => {
    if (num && state?.showControls == 'live') {
      let reqChannel = null
      let channelNumbersList = []
      numRef.current = ''
      const channelResponse = liveChannelsData[1]?.channelResponse ?? '' //epg channel data
      //To store all the channel numbers into an array and sort it in ascending order
      channelResponse?.map(val => channelNumbersList.push(val.number))
      channelNumbersList.sort((a, b) => a - b)

      //If the user clicked channel is available then directly tune in
      //Else take nearest and tune in there

      if (channelNumbersList.includes(num)) {
        reqChannel = channelResponse?.filter(val => val.number == num)
      } else {
        let nearestChannel = channelNumbersList
          ?.reverse()
          ?.find(a => Number(a) < Number(num))
        //if no lesser number is there for zap, it will zap to the first channel
        if (nearestChannel) {
          reqChannel = channelResponse?.filter(
            val => val.number == nearestChannel
          )
        } else {
          reqChannel = [channelResponse?.[0]]
        }
      }

      //setting num to empty since the fetched value is utilized
      //Triggering Mini EPG after number zapping
      setNum(() => '')
      let mainId = document.getElementById('mainEpg')
      !mainId && handleMiniEpgAutoShowHide()
      const getLockedChannelsIcon = group_id => {
        const found = lockedChannelsList?.find(each => each.id === group_id)

        if (found) {
          return true
        }
        return false
      }
      if (reqChannel) {
        const isContractChannel = getIsContractChanel(reqChannel[0]?.group_id)
        const group_id = reqChannel[0]?.group_id
        const isLocked = getLockedChannelsIcon(group_id)

        if (isContractChannel && watchFree) {
          navigate('/EPconfirmation', {
            state: { page: 'livePlayer', data: reqChannel[0] }
          })
        }

        //checking channel subscribed
        if (isContractChannel) {
          callPurchaseApi(isContractChannel, reqChannel[0])
          return
        }
        if (isLocked) {
          // destroyPlayer()
          dispatch(removePlayerInstance())
          dispatch(
            getProgramDetailsData({
              programData: getCurrentEvent(reqChannel[0]?.group_id),
              channelData: reqChannel[0],
              fromLivePlayer: true
            })
          )
          dispatch(
            getChannelData({
              group_id: reqChannel[0]?.group_id,
              timeshift: reqChannel[0]?.group?.common?.timeshift,
              switchChannel: 'yes',
              epgIndex: liveChannelsData[1]?.channelResponse.findIndex(
                itrObj => itrObj.group_id == reqChannel[0]?.group_id
              )
            })
          )
          navigate('/my-settings/help-And-Settings/security-pin/configure', {
            state: {
              data: 'epg',
              item: reqChannel[0],
              pageName: '/livePlayer',
              featureTag: state?.featureTag,
              page: state?.page,
              backfocusid: state?.backfocusid,
              fromZap: true,
              checkPastContent: checkPastContent
            }
          })
        }
      }

      //reqChannel will hold only 1 matched value of channel number
      //Avoiding retrigger of same channel when zapping
      if (
        !getIsContractChanel(reqChannel[0]?.group_id) &&
        reqChannel &&
        playerChannelData?.group_id != reqChannel[0]?.group_id &&
        !getLockedChannelsIcon(reqChannel[0]?.group_id)
      ) {
        dispatch(
          getProgramDetailsData({
            programData: getCurrentEvent(reqChannel[0]?.group_id),
            channelData: reqChannel[0],
            fromLivePlayer: true
          })
        )
        dispatch(
          getChannelData({
            group_id: reqChannel[0]?.group_id,
            timeshift: reqChannel[0]?.group?.common?.timeshift,
            switchChannel: 'yes',
            epgIndex: liveChannelsData[1]?.channelResponse.findIndex(
              itrObj => itrObj.group_id == reqChannel[0]?.group_id
            )
          })
        )
      }
      startedOverRef.current = null
      startOverCounterRef.current = 0
    }
  }, [num])

  const getIsContractChanel = item => {
    const foundContract = paywayResponse?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return false
    }
    return true
  }

  const callPurchaseApi = useCallback((isContractChannel, channelInfo) => {
    if (isContractChannel) {
      setChannelInformation(channelInfo)
      setButtonClicked(true)
      dispatch(
        getSubscriptionInfo({
          userId: userDetails?.parent_id,
          hks: userDetails?.session_stringvalue,
          url: `group_id=${channelInfo?.group?.common?.id}`
        })
      )
    }
  }, [])

  const handlePastContentDay = data => {
    const date = new Date()
    const endDates = ['28', '29', '30', '31']
    if (
      endDates?.includes(data?.date_begin?.slice(8, 10)) &&
      date?.getDate() == 1
    ) {
      setCheckPastContent(true)
    } else if (data?.date_begin?.slice(8, 10) < date?.getDate()) {
      setCheckPastContent(true)
    } else {
      setCheckPastContent(false)
    }
  }

  useEffect(() => {
    if (buttonClicked && addSubscriptions?.msg == 'OK') {
      const subscriptionResponse = addSubscriptions?.response?.listButtons
      const buttonMap = subscriptionResponse?.button ?? []
      if (buttonMap?.length > 0) {
        if (buttonMap?.length == 1) {
          buttonMap?.map(each => {
            if (each?.purchasable) {
              dispatch(
                getLiveIndex(
                  liveChannelsData[1]?.channelResponse.findIndex(
                    itrObj =>
                      itrObj.group_id ==
                      playerChannelData?.group_id
                  )
                )
              )
              navigate('/premiumSubscription', {
                state: {
                  data: channelInformation?.group?.common,
                  priceDetails: each,
                  playerData: playerChannelData,
                  returnPage:
                    state?.returnPage == 'search' ? 'search' : 'epgToPlayer',
                  groupId:
                    liveChannnelInfo?.response?.group?.common?.id ??
                    liveChannelId,
                  waspurchased: each?.waspurchased
                }
              })
            } else {
              handleNavigateToNonContract(channelInformation)
            }
          })
        } else {
          const subScriptionValuesFalse = buttonMap.every(
            item => item?.purchasable === false
          )
          if (subScriptionValuesFalse) {
            handleNavigateToNonContract(channelInformation)
          } else {
            const subscriptionTrueValues = buttonMap.every(
              item => item?.purchasable === true
            )
            const singleSubscription = buttonMap.filter(
              item => item?.purchasable === true
            )
            const subscriptionNodeValueFalse = buttonMap.filter(
              item => item?.purchasable === false
            )
            const exactlySingleSubscriptionTrue =
              singleSubscription?.length === 1 &&
              subscriptionNodeValueFalse?.length === buttonMap?.length - 1
            if (subscriptionTrueValues) {
              navigate('/multiSubscription', {
                state: {
                  pageName: 'livePlayer',
                  data: channelInformation?.group?.common ?? channelInformation,
                  type: 'MultiSubscription',
                  playerData: playerChannelData,
                  groupId:
                    liveChannnelInfo?.response?.group?.common?.id ??
                    liveChannelId,
                  waspurchased: false
                }
              })
            } else if (exactlySingleSubscriptionTrue) {
              dispatch(
                getLiveIndex(
                  liveChannelsData[1]?.channelResponse.findIndex(
                    itrObj =>
                      itrObj.group_id ==
                      playerChannelData?.group_id
                  )
                )
              )
              navigate('/premiumSubscription', {
                state: {
                  data: channelInformation?.group?.common,
                  priceDetails: singleSubscription[0],
                  returnPage:
                    state?.returnPage == 'search' ? 'search' : 'epgToPlayer',
                  playerData: playerChannelData,
                  groupId:
                    liveChannnelInfo?.response?.group?.common?.id ??
                    liveChannelId,
                  waspurchased: false
                }
              })
            }
          }
        }
      } else {
        handleNavigateToNonContract(channelInformation)
      }
    }
  }, [addSubscriptions])

  const handleNavigateToNonContract = channelInfoData => {
    navigate('/subScriptionCallPage', {
      state: {
        data: channelInfoData?.group?.common
          ? channelInfoData?.group?.common
          : channelInfoData,
        pageName: 'livePlayer',
        playerData: playerChannelData,
        groupId: liveChannnelInfo?.response?.group?.common?.id ?? liveChannelId,
        waspurchased: false
      }
    })
  }

  useEffect(() => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    if (
      currentEventRef.current?.unix_end &&
      videoElement &&
      videoElement?.currentTime &&
      player &&
      getCurrentEvent(playerChannelData?.group_id)?.unix_begin
    ) {
      if (playerInstanceRef.current && Object.keys(playerInstanceRef.current)?.length > 0) {
      videoElement.currentTime =
        videoElement?.currentTime -
        (videoElement?.currentTime -
          getCurrentEvent(playerChannelData?.group_id)?.unix_begin?.toFixed(7))
      videoElement.currentTime && player?.seek(videoElement?.currentTime)
    }
  }
  }, [moment().unix() < currentEventRef.current?.unix_end])

  const updateTime = () => {
    Object.keys(playerInstanceRef.current)?.length > 0 &&
    playerInstanceRef.current?.isPaused()
      ? (rewindTimeRef.current += 1)
      : null
    startedOverRef.current
      ? setCurrentTime(
          moment()
            .hour(0)
            .minute(0)
            .second(
              startOverCounterRef.current++ -
                rewindTimeRef.current +
                forwardTimeRef.current
            )
            .format('HH:mm:ss')
        )
      : setCurrentTime(
          moment
            .utc(
              (moment().unix() -
                getCurrentEvent(playerChannelData?.group_id)?.unix_begin -
                rewindTimeRef.current +
                forwardTimeRef.current) *
                1000
            )
            .format('HH:mm:ss')
        )
  }

  const handleNavToMycontent = () => {
    dispatch(getAlerts({}))
    localStorage.setItem('currNavIdx', navData?.length - 1),
    localStorage.setItem('miscontenidos', true)
    dispatch(getNavTabValue('miscontenidos'))
    dispatch(clearRecordPlayerInfo())
    navigate('/home', {
      state: { myContentData: true, backfocusid: state?.backfocusid }
    })
  }

  const getListOfEvents = useCallback(
    channelId => {
      let allChannelData = liveChannelsData?.[1]?.channelResponse
      let selectedChannelData = allChannelData?.filter(
        val => channelId == val?.group_id
      )
      return selectedChannelData?.[0]?.events || []
    },
    [moment().unix() < currentEventRef.current?.unix_end]
  )

  const getCurrentEvent = grp_id => {
    let totalEvents = getListOfEvents(
      grp_id ?? filteredLiveChannels.current?.[0]?.group_id
    )
    let currentTime = moment().unix()
    let curEvent = {}
    totalEvents?.length > 0 &&
      totalEvents.forEach((event, i) => {
        if (event?.unix_begin < currentTime && currentTime < event?.unix_end) {
          curEvent = event
          //Logic to get previous event of currentTime
          // curEvent = totalEvents[i - 1]
        }
      })
    return curEvent
  }

  useEffect(() => {
    if (liveChannnelInfo?.response && Object.keys(liveChannnelInfo?.response)?.length > 0) {
      let genres =
        liveChannnelInfo?.response?.group?.common?.extendedcommon?.genres?.genre?.reduce(
          (resultant, value, index) =>
            index == 0
              ? resultant + value?.name?.toLowerCase()
              : resultant + ', ' + value?.name?.toLowerCase(),
          ''
        )
      localStorage.setItem(
        'live-playing-channel-id',
        liveChannnelInfo?.response?.group?.common?.id
      )
      paywayKeyRef.current = paywayResponse?.filter(val => {
        return val?.groups
          ?.split(',')
          ?.includes(liveChannnelInfo?.response?.group?.common?.id)
      })

      gaPaywayRef.current = paywayKeyRef.current?.reduce(
        (resultant, value, index) =>
          index == 0 ? resultant + value?.name : resultant + ', ' + value?.name,
        ''
      )
      contentDataRef.current = {
        user_id:
          userDetails?.user_id || loginInfo?.user_id || registerInfo?.user_id,
        parent_id: userDetails?.parent_id || loginInfo?.parent_id || registerInfo?.parent_id,
        sign_up_method: 'correo electronico',
        suscriptions: gaPaywayRef.current?.toLowerCase(),
        user_type: watchFree ? 'anonimo' : 'registrado',
        device: COMMON_URL?.device_type,
        device_model: COMMON_URL?.device_model,
        device_name: COMMON_URL?.device_name,
        authpn: COMMON_URL?.authpn,
        content_subsection: NOT_APPLICABLE,
        content_section: 'player',
        country:
          userDetails?.country_code?.toLowerCase() ||
          loginInfo?.country_code?.toLowerCase() ||
          registerInfo?.country_code?.toLowerCase(),
        content_type: TV,
        content_availability: 'por suscripcion',
        modulo_name: CONTENIDO_BLOQUEADO,
        provider: liveChannnelInfo?.response?.group?.common?.extendedcommon?.media?.proveedor?.codigo?.toLowerCase(),
        channel_name: liveChannnelInfo?.group?.common?.title?.toLowerCase(),
        content_id: liveChannnelInfo?.response?.group?.common?.id,
        content_name: liveChannnelInfo?.response?.group?.common?.extendedcommon?.media?.description_extended?.toLowerCase(),
        content_category: genres
      }
      checkFromHomeToLive && pushNewInteractionContentEvent(
        contentDataRef.current, 
        PROTECTED_CONTENT, 
        TV, 
        interactionType?.SIGUIENTE
      )
      if (liveCertificate == null) {
        const xhr = new XMLHttpRequest()
        xhr.responseType = 'arraybuffer'
        xhr.open('GET', liveChannnelInfo?.response?.media?.certificate_url)
        xhr.addEventListener('loadend', function (e) {
          if (xhr.readyState === XMLHttpRequest.DONE) {
            if (xhr.status === 200) {
              dispatch(setLivePlayerCertificate(xhr.response))
              preloadedCertificate = xhr.response
              state?.showControls === 'live' &&
                liveChannnelInfo?.response &&
                player == null &&
                setIsCertificateAvailable(true)
            }
          }
        })
        xhr.send()
      } else {
        setIsCertificateAvailable(true)
      }
      checkFromHomeToLive && dispatch(setFromHomeToLive(false))
    }
  }, [liveChannnelInfo])

  useEffect(() => {
    isCertificateAvailable === true && setupPlayer()
  }, [isCertificateAvailable])

  useEffect(() => {
    setShowLiveControls('mainEpg')
  }, [guestUserGrid])

  useEffect(() => {
    // const bodyElement = document.body
    // bodyElement && (bodyElement.style.margin = '0px')

    const interval = setInterval(() => {
      updateTime()
    }, 1000)
    // Correcting the listeners as for VOD and LIVE keyup is only needed.
    // If any scenario like zapping requires keydown then we may create one more listener for keydown and handle.
    document.addEventListener('keyup', handleRCU)
    document.addEventListener('keydown', handleMiniEpg)
    //Live controls setting shouldnot impact VOD, hence placing live check
    if (state?.showControls == 'live') {
      if (state?.grid == 'mainEpg') {
        setShowLiveControls('mainEpg')
        setGuestUserGrid(true)
      } else {
        state?.fromPage != 'pinCreation' && setShowLiveControls('miniEpg')
      }
      if (state?.fromPage != 'pinCreation' && state?.screenName != 'livePlayerRecord') {
        miniEpgTimeout.current = setTimeout(() => {
          let liveId = document.getElementById('live')
          let miniId = document.getElementById('miniEpg')
          let mainId = document.getElementById('mainEpg')
          let programDetailsId = document.getElementById('programDetailsId')
          miniId &&
            !programDetailsId &&
            !mainId &&
            !liveId &&
            setShowLiveControls('')
        }, miniEpgAutoHideTimeOut?.[region]?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.time * 1000 || 5000)
      }
    }
    !state?.featureTag && !state?.fromUnlockPinPage &&
      dispatch(
        getProgramDetailsData({
          programData: getCurrentEvent(playerChannelData?.group_id),
          channelData: filteredLiveChannels.current?.[0],
          fromLivePlayer: true
        })
      )
    state?.fromPage != 'pinCreation' && handleMiniEpgAutoShowHide()
    SpatialNavigation.focus()
    if (state?.showControls == 'live' && state?.featureTag) {
      setShowOptionsPanel(true)
    }
    return () => {
      //The get media response to be cleared out when player component is unmounted
      // const bodyElement = document.body
      // bodyElement && bodyElement.removeAttribute('style')
      dispatch && dispatch(resetLivePlayer())
      document.removeEventListener('keyup', handleRCU)
      document.removeEventListener('keydown', handleMiniEpg)
      clearInterval(interval)
      clearTimeout(miniEpgTimeout.current)
      clearTimeout(alertPastRef.current)
      clearTimeout(alertTsRef.current)
      if (state?.showControls == 'live') {
        // player?.unload()
        if (Object.keys(playerInstanceRef.current)?.length > 0) {
          player && player?.unload()
        }
        destroyPlayer()
      }
      clearInterval(trackStartTimer.current)
      trackStartTimer.current = ""
    }
  }, [])
  
  //Adding the below effect for checking if event is past content in present and past day. 
  useEffect(() => {
    state?.pastProgramData &&
      Object.keys(state?.pastProgramData)?.length > 0 &&
      handlePastContentDay(state?.pastProgramData)
  }, [state?.pastProgramData])

  useEffect(() => {
    const livePlayerCloseFn = () => {
      destroyPlayer()
      paywayKeyRef.current = []
      var alertId = document.getElementById('favouriteAlert')
      var alertButton = document.getElementById('alertMsg')
      switch (true) {
        case state?.returnPage === 'search':
          navigate('/search', {
            state: { inputValue: state?.inputValue },
            replace: true
          })
          localStorage.setItem('currNavIdx', -1)
          dispatch(getNavTabValue('search'))
          setLivePlayerClose(true)
          break
        case state?.returnPage === 'settings/profile-settings':
          navigate('/settings/profile-settings', {
            state: { pageName: 'recordatorios' }
          })
          break
        case alertId ?? alertButton:
          handleNavToMycontent()
          break
        case state.pageName === 'miscontenidos':
          navigate('/home', { item: state?.item })
          localStorage.setItem('currNavIdx', navData?.length - 1)
          break
        case state.page === 'record':
          handleNavToMycontent()
          break
        default:
          navigate('/home', { backfocusid: state?.backfocusid })
          dispatch(getNavTabValue('homeuser'))
          localStorage.setItem('currNavIdx', 0)
          break
      }
    }
    if (livePlayerClose && (playerInstanceRef.current || player)) {
      livePlayerCloseFn()
    }
  }, [livePlayerClose])

  useEffect(() => {
    if (
      addfavouriteList?.lasttouch?.favorited &&
      addfavouriteList?.lasttouch?.favorited != addFavLastTouch
    ) {
      setAddFavLastTouch(addfavouriteList?.lasttouch?.favorited)
      dispatch(
        getFavouriteLive({
          epg_version: epgVersion,
          hks: userDetails?.session_stringvalue,
          user_id: watchFree ? 0 : userDetails?.user_id,
          user_token: userDetails?.user_token,
          lasttouch: addfavouriteList?.lasttouch?.favorited ?? lastTouch,
          user_hash: userDetails?.session_userhash
        })
      )
      localStorage.setItem('lasttouch', addfavouriteList?.lasttouch?.favorited)
    }
  }, [addfavouriteList])

  useEffect(() => {
    if (
      delfavourite?.lasttouch?.favorited &&
      delfavourite?.lasttouch?.favorited != deleteFavLastTouch
    ) {
      setDeleteFavLastTouch(delfavourite?.lasttouch?.favorited)
      dispatch(
        getFavouriteLive({
          epg_version: epgVersion,
          hks: userDetails?.session_stringvalue,
          user_id: watchFree ? 0 : userDetails?.user_id,
          user_token: userDetails?.user_token,
          lasttouch: delfavourite?.lasttouch?.favorited ?? lastTouch,
          user_hash: userDetails?.session_userhash
        })
      )
      localStorage.setItem('lasttouch', delfavourite?.lasttouch?.favorited)
    }
  }, [delfavourite])

  const destroyPlayer = () => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    liveChannnelInfo?.response && dispatch(
      getPlayerTrackAPI({
        url: liveChannnelInfo?.response?.tracking?.urls?.stop
          ?.replace('(', '')
          ?.replace(')', ''), // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
        trackType: 'stop',
        timecode: Math.round(videoElement?.currentTime)
      })
    )
    if (Object.keys(playerInstanceRef.current)?.length > 0) {
      dispatch && dispatch(removePlayerInstance())
      //livePlayerInstanceRedux this value to be checked before destroying incase of any destroy related issues
      player?.destroy(() => {
        playerInstanceRef.current = {}
      })
    }
    //As a safer check for corner scenarios, clearing the video element created by player while unmounting the component
    videoElement && videoElement?.pause() && videoElement?.remove()
  }

  const handleSetTotalDuration = duration => {
    setTotalDuration(duration)
  }

  const handlePlayerStallStart = (e) => {
    playerStatus == 'play' && youboraInstance?.getAdapter()?.fireError(e?.code, e?.message, e?.data)
    setIsPlayerStalled(true)
    setPausedTime(currentTime)
  }

  const handlePlayerStallEnd = () => {
    setIsPlayerStalled(false)
    setPausedTime('')
  }

  //Handle Timeout function to show recording ALert
  useEffect(() => {
    if (
      state?.showControls == 'live' &&
      (lockchannel?.msg === 'OK' || unlockchannel?.msg === 'OK')
    ) {
      dispatch(getClearAllSettingsState())
      dispatch(
        getLockedChannelsList({
          hks: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
    }

    if (recordAlertMsg?.message) {
      state?.showControls == 'live' && setShowLiveControls('')
      dispatch(
        getLiveReminder({
          hks: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
      dispatch(getLiveTvRecording({ user_token: userDetails?.user_token }))
      dispatch(
        getLiveTvCompleteRecording({ user_token: userDetails?.user_token })
      )
      dispatch(
        getLiveTvProgressRecording({ user_token: userDetails?.user_token })
      )
      alertPastRef.current = setTimeout(() => {
        dispatch(getAlerts({}))
        handleMiniEpgAutoShowHide()
      }, 12000)
    } else if(Object.keys(recordAlertMsg)?.length == 0) {
      clearTimeout(alertPastRef.current)
      handleMiniEpgAutoShowHide()
    }
  }, [recordAlertMsg, unlockchannel, lockchannel])

  useEffect(() => {
    state?.showControls == 'live' && setShowLiveControls('')
    setTimeout(() => {
      favouriteAlertMessage?.length != 0 && dispatch(resetFavouriteAlerts())
    }, 12000)
  }, [favouriteAlertMessage])

  useEffect(() => {
    favouriteAlertMessage?.length == 0 && handleMiniEpgAutoShowHide()
  }, [favouriteAlertMessage])

  const handleMiniEpgAutoShowHide = () => {
    if (state?.showControls == 'live') {
      setShowLiveControls('miniEpg')
      miniEpgTimeout.current = setTimeout(() => {
        let liveId = document.getElementById('live')
        let miniId = document.getElementById('miniEpg')
        let mainId = document.getElementById('mainEpg')
        let programDetailsId = document.getElementById('programDetailsId')
        miniId &&
          !programDetailsId &&
          !mainId &&
          !liveId &&
          setShowLiveControls('')
      }, miniEpgAutoHideTimeOut?.[region]?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.time * 1000 || 5000)
    }
  }

  useEffect(() => {
    if (timeShiftError?.length > 0) {
      alertTsRef.current = setTimeout(() => {
        setTimeShiftError('')
        handleMiniEpgAutoShowHide()
      }, 12000)
    } else {
      clearTimeout(alertTsRef.current)
      handleMiniEpgAutoShowHide()
    }
  }, [timeShiftError])

  useEffect(() => {
    state?.showControls === 'live' && dispatch(clearGetMediaRes())
  }, [state?.showControls])

  const setPlay = e => {
    setPlayerStatus('play')
    setShowLoader(false)
  }

  const toUTF8 = str => {
    const encoded = encodeURIComponent(str)
    const utf8 = unescape(encoded)
    const result = new Uint8Array(utf8.length)
    for (var i = 0; i < utf8.length; ++i) {
      result[i] = utf8.charCodeAt(i)
    }
    return result.buffer
  }

  const subEnter = e => {
    setSubtitleText(e?.image)
  }

  var preloadedCertificate
  const setupPlayer = () => {
    if (player != null) {
      //The below method unload is to remove the urls attached
      //The below method destroy is to remove the player instance including the added UI
      if (Object.keys(playerInstanceRef.current)?.length > 0) {
        player && player?.unload()
      }
      destroyPlayer()
      setPlayer(null)
      dispatch(removePlayerInstance())
      playerInstanceRef.current = {}
    }
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    videoElement && videoElement?.pause() && videoElement?.remove()
    let retryCount = 0
    const adapterStringObject = COMMON_URL?.npawAdapterString
    let streamTypeFromMetadata = supportedStream?.default?.[CURRENT_PLATFORM]?.live?.[0]
    let streamTypeToBitmovin = ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') ? 'smooth':'dash')

    switch (true) {
      case streamTypeFromMetadata?.includes('hls'):
        streamTypeToBitmovin = 'hls'
        break
      case streamTypeFromMetadata?.includes('dash'):
        streamTypeToBitmovin = 'dash'
        break
      case streamTypeFromMetadata?.includes('smooth'):
        streamTypeToBitmovin = 'smooth'
        break
      default:
        streamTypeToBitmovin = 'dash'
    }
    const playerConfigLive = {
      key: 'fc3afd27-8180-4ac4-80ef-dbb6854f9637',
      ui: false,
      source: {
        dash: liveChannnelInfo?.response?.media?.video_url
      },
      tweaks: {
        file_protocol: true,
        app_id: 'com.bitmovin.claro.smarttvapp',
        enable_seek_for_live: true
      },
      playback: {
        autoplay: true,
        muted: false
      },
      //commenting analytics for time being to check live player performance
      // analytics: {
      //   key: '6dc21e2a-820a-4b2f-b7d1-dc94e252e49a',
      //   config: {
      //     origin: 'com.bitmovin.claro.smarttvapp'
      //   }
      // },
      adaptation: {
        bitrates: {
          maxSelectableVideoBitrate: '4mbps',
          minSelectedVideoBitrate: 0
        },
        maxStartupBitrate: '5mbps' //ignored if startupBitrate defined
      },
      buffer: {
        video: {
          forwardduration: 25,
          backwardduration: 20
        },
        audio: {
          forwardduration: 25,
          backwardduration: 20
        }
      },
      network: {
        retryHttpRequest: function (type, response) {
          // delay the retry by 1 second
          setLiveTvLoader(true)
          return new Promise(function (resolve, reject) {
            if (retryCount > 10) {
              playerDiv.current?.classList?.remove('bitmovinplayer-container')
              const videoElement = playerDiv.current?.children[0]
              if (videoElement) {
                videoElement?.pause()
                videoElement?.remove()
              }
              if (Object.keys(playerInstanceRef.current)?.length > 0) {
                player && player?.unload()
              }
              destroyPlayer()
              reject(response.request)
              youboraInstance
                ?.getAdapter()
                ?.fireFatalError(
                  response?.status,
                  response?.statusText,
                  response?.request
                )
              setLiveTvLoader(false)
              setPlayer(null)
              setPlayerError(true)
              dispatch(getChannelDownId(liveChannnelInfo?.response?.group?.common?.id))
              let mainId = document.getElementById('mainEpg')
              !mainId && setShowLiveControls('miniEpg')
            } else {
              setTimeout(function () {
                retryCount++
                resolve(response.request)
                setLiveTvLoader(false)
              }, 1000)
            }
          })
        }
      }
    }
    const playerInstance = new Player(playerDiv?.current, playerConfigLive)
    // Setting DRM configuration only if there is server_url
    let drmConfig = ''

    if (!!liveChannnelInfo?.response?.media?.server_url) {
      drmConfig = {
        widevine: {
          LA_URL: liveChannnelInfo?.response?.media?.server_url,
          serverCertificate: liveCertificate,
          prepareMessage: function (keyMessage) {
            return new Uint8Array(
              toUTF8(
                JSON.stringify({
                  token: JSON.parse(
                    liveChannnelInfo?.response?.media?.challenge
                  )?.token,
                  device_id: COMMON_URL?.device_id,
                  widevineBody: btoa(
                    String.fromCharCode.apply(
                      null,
                      new Uint8Array(keyMessage.message)
                    )
                  )
                })
              )
            ).buffer
          }
        },
        //As part of player VST optimization the license request to be done immediately
        immediateLicenseRequest: true
      }
    }

    const playerSource = {
      dash: liveChannnelInfo?.response?.media?.video_url,
      drm: drmConfig
    }

    youboraInstance &&
      youboraInstance?.registerAdapter(playerInstance, adapterStringObject)
    let analyticsOptions = {
      'app.name': appInfo?.title ?? 'NA',
      'app.releaseVersion': appInfo?.version ?? 'NA',
      'content.title':
        `${programdetailsdata?.payload?.channelData?.id}-${filteredLiveChannels.current?.[0]?.group?.common?.title}` ??
        'NA',
      'content.program':
        liveEventName ??
        programdetailsdata?.payload?.programData?.ext_original_name ??
        'NA',
      'content.channel':
        filteredLiveChannels.current?.[0]?.group?.common?.title ?? 'NA',
      'content.duration': moment(totalDuration, 'HH:mm:ss').diff(
        moment().startOf('day'),
        'seconds'
      ),
      'content.streamingProtocol': streamTypeToBitmovin.toUpperCase(),
      'content.playbackType': isPastContent ? 'VOD' : 'LIVE',
      'content.customDimension.1':
        loginInfo?.paywayProfile?.paymentMethods?.[0]?.user_category ?? 'NA',
      'content.customDimension.2': paywayKeyRef.current?.[0]?.key ?? 'NA',
      'content.customDimension.3': paywayKeyRef.current?.[0]?.key ?? 'NA',
      'content.customDimension.4': 'Type 7',
      'content.customDimension.5': userDetails?.email ?? 'NA',
      'content.customDimension.6':
        paywayKeyRef.current?.[0]?.paymentmethod?.gateway ?? 'NA',
      'content.customDimension.7': userDetails?.parent_id ?? 'NA',
      'content.customDimension.8': userDetails?.user_id ?? 'NA',
      'content.customDimension.9': region,
      'content.customDimension.10': 0,
      'content.customDimension.11': deviceId ?? COMMON_URL?.device_id ?? 'NA',
      'content.customDimension.12':
        `${
          localStorage.getItem('hks') ??
          loginInfo?.session_stringvalue ??
          registerInfo?.session_stringvalue ??
          userDetails?.session_stringvalue
        }${new Date().toTimeString().split(' ')[0]}` ?? 'NA',
      'content.customDimension.13': `${
        typeof tizen == 'undefined' ? 'WebOS' : 'Tizen'
      }`,
      'content.customDimension.14': appInfo?.version,
      'content.customDimension.15': epgVersion ?? 'NA',
      'content.customDimension.16': userDetails?.subregion ?? 'NA',
      'device.name': typeof tizen == 'undefined' ? 'LG' : 'Samsung',
      'device.osName': typeof tizen == 'undefined' ? 'WebOS' : 'Tizen',
      'device.type': 'Smart TV',
      'user.name': userDetails?.user_id ?? 'NA'
    }
    playerInstance.load(playerSource).then(
      () => {
        const availableAudios =
          state?.showControls == 'live' && playerInstance?.getAvailableAudio()
        const playerDefaultAudio =
          state?.showControls == 'live' && playerInstance?.getAudio()
        const selectedAudio = JSON?.parse(
          localStorage?.getItem('selectedAudio')
        )
        playerInstance?.on(PlayerEvent.StallStarted, e =>
          handlePlayerStallStart(e)
        )
        playerInstance?.on(PlayerEvent.StallEnded, handlePlayerStallEnd)

        youboraInstance?.setAnalyticsOptions(analyticsOptions)
        youboraInstance?.setLogLevel(5)
        dispatch(getAvailableAudio(availableAudios))
        dispatch(getDefaultAudio(playerDefaultAudio))
        if (state?.showControls == 'live') {
          const subsList = playerInstance?.subtitles?.list()
          dispatch(getAvailableSubtitles(subsList))
        }
        const playedDuration = state?.resume
          ? currentVodData?.media?.initial_playback_in_seconds
          : 0
        selectedAudio
          ? playerInstance?.setAudio(selectedAudio?.id)
          : playerInstance?.setAudio(playerDefaultAudio?.id)
        if (playedDuration > 0) {
          playerInstance?.play().then(() => {
            localStorage.setItem('hideTimer', playedDuration)
            // GA: Content Play Event
          })
            if (playerInstanceRef.current && Object.keys(playerInstanceRef.current)?.length > 0) {
              playerInstance?.seek(playedDuration)
            }
          // GA: Content Forward Event
        } else {
          //The below dispatch helps to set the current channel played so it can be reused for next launch of player
          state?.showControls === 'live' &&
            dispatch(
              getChannelData({
                group_id: liveChannnelInfo?.response?.group?.common?.id,
                timeshift:
                  liveChannnelInfo?.response?.group?.common?.extendedcommon
                    ?.media?.timeshift,
                switchChannel: 'no',
                epgIndex: liveChannelsData[1]?.channelResponse.findIndex(
                  itrObj =>
                    itrObj.group_id ==
                    liveChannnelInfo?.response?.group?.common?.id
                )
              })
            )
        }
        if (state?.showControls === 'live') {
          startOverCounterRef.current = 0
          startedOverRef.current = null
        }
        setPlayer(playerInstance)
        dispatch(setPlayerInstance(playerInstance))
        playerInstanceRef.current = playerInstance
        setPlayerError(false)
        playerInstance?.on(PlayerEvent.Ready, () => {
          setPlay()
          setLiveTvLoader(false)
          startedOverRef.current = isPastContent
          startedOverTimeRef.current = isPastContent
            ? moment().unix() - currentEventRef.current?.unix_begin
            : 0
        })
        playerInstance?.on(PlayerEvent.CueEnter, e => subEnter(e))
        playerInstance?.on(PlayerEvent.CueExit, () => setSubtitleText(''))
        playerInstance?.on(PlayerEvent.SubtitleEnabled, () =>
          dispatch(getAvailableSubtitles(playerInstance?.subtitles?.list()))
        )
        playerInstance?.on(PlayerEvent.Error, e =>
          youboraInstance?.getAdapter()?.fireError(e?.code, e?.message, e?.data)
        )
        playerInstance?.on(PlayerEvent.Warning, e =>
          youboraInstance?.getAdapter()?.fireError(e?.code, e?.message, e?.data)
        )
        //below is the dispatch of track/view(Start) API
        dispatch(
          getPlayerTrackAPI({
            url: liveChannnelInfo?.response?.tracking?.urls?.view
              ?.replace('(', '')
              ?.replace(')', '') // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
          })
        )        
        if (trackStartTimer.current == '') {
          trackStartTimer.current = setInterval(() => {            
            //below is the dispatch of track/tick(Update) API
            const videoElement = document.getElementById('bitmovinplayer-video-player')
            dispatch(
              getPlayerTrackAPI({
                url: liveChannnelInfo?.response?.tracking?.urls?.tick
                  ?.replace('(', '')
                  ?.replace(')', ''), // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
                trackType: 'tick',
                timecode: Math.round(videoElement?.currentTime)
              })
            )
          }, liveChannnelInfo?.response?.tracking?.policies?.tick_interval * 1000 || 60000)
        }
      },
      e => {
        console.warn('Error while loading source')
        //The below method destroy is to remove the player instance including the added UI
        youboraInstance?.setAnalyticsOptions(analyticsOptions)
        youboraInstance?.setLogLevel(5)
        youboraInstance?.getAdapter()?.fireFatalError(e)
        if (Object.keys(playerInstanceRef.current)?.length > 0) {
          player && player?.unload()
        }
        playerInstance?.destroy()
        setPlayerError(true)
        //When player run into issue of playback, we display miniepg to allow user to change channel -implemented As per design
        let mainId = document.getElementById('mainEpg')
        state?.showControls == 'live' &&
          !mainId &&
          setShowLiveControls('miniEpg')
      }
    )
    setIsCertificateAvailable(false)
  }

  const handleStartOver = () => {
    startOverCounterRef.current = 0
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    startedOverRef.current = true
    rewindTimeRef.current = 0
    forwardTimeRef.current = 0
    rwFwRef.current = true
    startedOverTimeRef.current =
      moment().unix() - currentEventRef.current?.unix_begin
      videoElement.currentTime = isPastContent
        ? videoElement.currentTime - playerInstanceRef.current?.getCurrentTime()
        : videoElement.currentTime -
          (videoElement.currentTime -
            currentEventRef.current?.unix_begin?.toFixed(7))
    if (playerInstanceRef.current && Object.keys(playerInstanceRef.current)?.length > 0) {
      playerInstanceRef.current?.seek(videoElement?.currentTime)
    }
}
  const focusButtonSet = () => {
    focusButton.current = 'idioma'
  }
  const enableSubtitles = (playerInstance, value, index) => {
    // GA: Player Interaction Event
    const selectedId = value?.option_id.split('-').pop().toLowerCase()
    const subtitleList = playerInstance?.subtitles.list()
    const audioList = playerInstance?.getAvailableAudio()
    setSelSubs(index)
    let audio = []
    let language = []
    const time = localStorage.getItem('runningTime')
    const dubSubChange = currentVodData?.tracking?.urls?.dubsubchange
    const checkSelValue = value?.option_id.toLowerCase().startsWith('s')
    if (checkSelValue) {
      subtitleList &&
        subtitleList.map(item => {
          if (item?.lang === selectedId) {
            language.push(item)
          }
        })
      playerInstance?.subtitles.enable(language?.[0]?.id)
      const url = new URL(dubSubChange)
      url.searchParams.set('content_id', value?.content_id)
      reqOptionsForGet(`${url}&timecode=${Math.round(time)}`)
    } else {
      audioList &&
        audioList?.map(item => {
          if (item?.lang === selectedId) {
            audio.push(item)
          }
        })
      playerInstance?.setAudio(audio?.[0]?.id)
    }
  }

  //Method for registering number keys for Samsung TV input
  const tizenNumberHandler = () => {
    const numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
    tizen.tvinputdevice.registerKeyBatch(numbers)
    return numbers?.map(number => tizen.tvinputdevice.getKey(number).code);
  }

  const handleRCU = event => {
    let codes = ''
    var liveId = document.getElementById('live')
    var miniId = document.getElementById('miniEpg')
    var mainId = document.getElementById('mainEpg')
    var programDetailsId = document.getElementById('programDetailsId')
    var alertId = document.getElementById('favouriteAlert')
    var alertButton = document.getElementById('alertMsg')
    var timeShiftAlertId = document.getElementById('timeShiftError')
    var alertDiv = document.getElementById('alertDiv')

    const fastForwardId = document.getElementById('fastForward')
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch([
        'ColorF2Yellow',
        'ColorF3Blue',
        'ColorF1Green',
        'ChannelUp',
        'ChannelDown',
        'MediaRewind',
        'MediaFastForward'
      ])
      codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code,
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code,
        numbercode: tizenNumberHandler(),
        chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
        chMinus: tizen.tvinputdevice.getKey('ChannelDown').code,
        rewind: tizen.tvinputdevice.getKey('MediaRewind').code,
        forward: tizen.tvinputdevice.getKey('MediaFastForward').code
      }
    }
    //Triggering Mini Guide, Main Guide and added a process to navigating home
    if (
      showScheduleReminderRef.current ||
      alertAddFavourite.current
      // pastChannelAlert
    ) {
      event.preventDefault()
    } else {
      if (
        (alertDiv || timeShiftAlertId) &&
        (event?.keyCode === 10009 ||
          event?.keyCode === 461 ||
          event?.keyCode === 8)
      ) {
        if(alertDiv) {
          dispatch(getAlerts({}))
          clearTimeout(alertPastRef.current)
        } else if(timeShiftAlertId) {
          setTimeShiftError('')
          clearTimeout(alertTsRef.current)
        }
        handleMiniEpgAutoShowHide()
      }
      else if (
        state?.showControls == 'live' &&
        (event?.code == 'KeyG' ||
          event?.keyCode === 404 ||
          event?.keyCode === 10009 ||
          event?.keyCode === 461 ||
          codes.greencode === event?.keyCode)
      ) {
        let programOptionsId = document.getElementById('programDetailsId')
        let miniEPGId = document.getElementById('miniEpg')
        let mainEPGId = document.getElementById('mainEpg')
        let lockError = document.getElementById('player-error-screen-div')
        let lockErrorNext = document.getElementById('player-error')
        if (programOptionsId) {
          setShowOptionsPanel(false)
          audioData.current = false
          focusButton.current = 'idioma'
          showLiveControls && setShowLiveControls('live')
        } else if (
          miniEPGId &&
          event?.keyCode != 404 &&
          codes.greencode != event?.keyCode
        ) {
          if (lockError || lockErrorNext || miniEPGId) {
            if(state?.returnPage === 'search'){
              // navigate('/search', {
              //   state: { inputValue: state?.inputValue },
              //   replace: true
              // })
              localStorage.setItem('currNavIdx', -1)
              dispatch(getNavTabValue('search'))
              setLivePlayerClose(true)
            }else{
              setLivePlayerClose(true)
              dispatch(getNavTabValue('homeuser'))
              localStorage.setItem('currNavIdx', 0)
            }
          } else {
            setShowLiveControls('')
          }
        } else if (miniEPGId) {
          if (event?.keyCode === 404 || codes.greencode === event?.keyCode) {
            if(state?.returnPage === 'search'){
              // navigate('/search', {
              //   state: { inputValue: state?.inputValue },
              //   replace: true
              // })
              localStorage.setItem('currNavIdx', -1)
              dispatch(getNavTabValue('search'))
              setLivePlayerClose(true)
            }else{
              setLivePlayerClose(true)
              dispatch(getNavTabValue('homeuser'))
              localStorage.setItem('currNavIdx', 0)
            }
          } else if (lockError || lockErrorNext || miniEPGId) {
            if(state?.returnPage === 'search'){
              // navigate('/search', {
              //   state: { inputValue: state?.inputValue },
              //   replace: true
              // })
              localStorage.setItem('currNavIdx', -1)
              dispatch(getNavTabValue('search'))
              setLivePlayerClose(true)
            }else{
              setLivePlayerClose(true)
              dispatch(getNavTabValue('homeuser'))
              localStorage.setItem('currNavIdx', 0)
            }
          }
        } else if (mainEPGId) {
          if (event?.keyCode === 404 || codes.greencode === event?.keyCode) {
            if(state?.returnPage === 'search'){
              // navigate('/search', {
              //   state: { inputValue: state?.inputValue },
              //   replace: true
              // })
              localStorage.setItem('currNavIdx', -1)
              dispatch(getNavTabValue('search'))
              setLivePlayerClose(true)
            }else{
              setShowLiveControls('mainEpg')
            }
          } else if (event?.keyCode === 10009 || event?.keyCode === 461) {
            handleMiniEpgAutoShowHide()
          }
        } else if (!miniEPGId && !mainEPGId && !programOptionsId) {
          if (
            event?.keyCode === 404 ||
            codes.greencode === event?.keyCode || 
            liveId
          ) {
            if(state?.returnPage === 'search'){
              // navigate('/search', {
              //   state: { inputValue: state?.inputValue },
              //   replace: true
              // })
              localStorage.setItem('currNavIdx', -1)
              dispatch(getNavTabValue('search'))
              setLivePlayerClose(true)
            }else{
              setShowLiveControls('')
            }
          } else {
            setLivePlayerClose(true)
          }
        } else {
          setLivePlayerClose(true)
        }
      }
      if (
        state?.showControls == 'live' &&
        !programDetailsId &&
        !mainId &&
        !alertId &&
        !alertDiv &&
        liveId &&
        isSelectedChannelTSRef.current &&
        (event?.keyCode == codes.rewind ||
          event?.keyCode == 412 ||
          event?.keyCode == 188)
      ) {
        handleRewind()
      }

      if (
        state?.showControls == 'live' &&
        fastForwardId &&
        !programDetailsId &&
        !mainId &&
        !alertId &&
        !alertDiv &&
        liveId &&
        isSelectedChannelTSRef.current &&
        (event?.keyCode == codes.forward ||
          event?.keyCode == 417 ||
          event?.keyCode == 190)
      ) {
        handleForward()
      }
    }
    // Logic to make mini-epg appear on below button click code
    if (
      (event?.keyCode == 38 ||
        event?.keyCode == 40 ||
        event?.keyCode == 37 ||
        event?.keyCode == 39 ||
        event?.keyCode == 33 ||
        event?.keyCode == 34 ||
        event?.keyCode == 405 ||
        event?.keyCode == 86 ||
        event?.keyCode == 13 ||
        codes.chPlus === event?.keyCode ||
        codes.chMinus === event?.keyCode) &&
      showLiveControls == '' &&
      !programDetailsId &&
      !mainId &&
      !alertId &&
      !alertDiv &&
      !alertButton &&
      !liveId &&
      state?.showControls == 'live'
    ) {
      clearTimeout(timeoutRef.current)
      if (state?.fromPage == 'pinCreation') {
        event?.keyCode != 13 && setShowLiveControls('miniEpg')
      }else{
        setShowLiveControls('miniEpg')
      }
      // logic for 12-second disappearance
      timeoutRef.current = setTimeout(() => {
        let mini_Id = document.getElementById('miniEpg')
        let main_Id = document.getElementById('mainEpg')
        let programDetails_Id = document.getElementById('programDetailsId')
        mini_Id && !main_Id && !programDetails_Id && setShowLiveControls('')
      }, miniEpgAutoHideTimeOut?.[region]?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.time * 1000 || 5000)
    }
    //Guide Switching Logic
    //The below logic to be executed only for live player hence placed the below live check
    let mosaicScreenOpen = document.getElementById('mosaicScreen')
    let filterScreenOpen = document.getElementById('epgFilter')
    if (
      state?.showControls == 'live' &&
      !programDetailsId &&
      !filterScreenOpen &&
      !mosaicScreenOpen &&
      (event?.keyCode === 89 ||
        event?.keyCode === 405 ||
        codes.yellowcode === event?.keyCode ||
        event?.keyCode === 76 ||
        event?.keyCode === 88)
    ) {
      if (alertId ?? alertButton) {
        if (alertAddFavourite.current) {
          handleNavToMycontent()
          setLivePlayerClose(true)
        }
      } else if (liveId) {
        setShowLiveControls('miniEpg')
        // logic for 12-second disappearance
        timeoutRef.current = setTimeout(() => {
          let miniId = document.getElementById('miniEpg')
          if (miniId) {
            setShowLiveControls('live')
            timeOutFunc = setTimeout(
              () => {
                let liveId = document.getElementById('live')
                state?.showControls == 'live' &&
                  liveId &&
                  setShowLiveControls('hide')
              },
              playerControlsInterval
                ? playerControlsInterval?.default?.seconds * 1000
                : 12000
            )
          }
        }, miniEpgAutoHideTimeOut?.[region]?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.time * 1000 || 5000)
        // return () => clearTimeout(timeoutRef.current);
      } else if (miniId) {
        clearTimeout(timeoutRef.current)
        setShowLiveControls('mainEpg')
      } else if (mainId) {
        handleMiniEpgAutoShowHide()
      }
    }

    if (
      state?.showControls == 'live' &&
      !miniId &&
      !mainId &&
      (event?.code == 'KeyB' ||
      event?.keyCode === 406 ||
      codes.bluecode === event?.keyCode)
    ) {
    if ((alertId && !alertDiv) || (!alertId && alertDiv)) {
     if (alertId) {
      dispatch(resetFavouriteAlerts())
     }
    if (alertDiv) {
      dispatch(getAlerts({}))
      setShowOptionsPanel(true)
     } else {
      handleShowOptions(event)
     }
    audioData.current = false
     }
    }

    if (
      state?.showControls == 'live' &&
      !liveId &&
      !mainId &&
      !programDetailsId &&
      (event?.keyCode === 82 || event?.keyCode === 403)
    ) {
      // setShowLiveControls('live')
      focusButton.current=''
      timeShiftAlertId && setTimeShiftError('')
      alertId && dispatch(resetFavouriteAlerts())
      clearTimeout(timeOutFunc)
      setShowLiveControls(state?.showControls)
      timeOutFunc = setTimeout(
        () => {
          if (!playerDiv?.current?.paused) {
            let liveId = document.getElementById('live')
            state?.showControls == 'live' &&
              liveId &&
              setShowLiveControls('hide')
          }
        },
        playerControlsInterval
          ? playerControlsInterval?.default?.seconds * 1000
          : 12000
      )
    } else if (
      state?.showControls == 'live' &&
      isSelectedChannelTSRef.current &&
      liveId &&
      !mainId &&
      !programDetailsId &&
      (event?.keyCode === 82 || event?.keyCode === 403)
    ) {
      handleStartOver()
    }

    if (
      event?.keyCode == 38 ||
      event?.keyCode == 40 ||
      event?.keyCode == 37 ||
      event?.keyCode == 39 ||
      event?.keyCode == 13 ||
      event?.keyCode == codes.rewind ||
      event?.keyCode == 412 ||
      event?.keyCode == 188 ||
      event?.keyCode == 190 ||
      event?.keyCode == codes.forward ||
      event?.keyCode == 417
    ) {
      let liveId = document.getElementById('live')
      if (liveId) {
        clearTimeout(timeOutFunc)
        timeOutFunc = setTimeout(
          () => {
            if (!playerDiv?.current?.paused) {
              let liveIdVal = document.getElementById('live') //getting liveId after 12 seconds
              state?.showControls == 'live' &&
                liveIdVal &&
                setShowLiveControls('hide')
            }
          },
          playerControlsInterval
            ? playerControlsInterval?.default?.seconds * 1000
            : 12000
        )
      }
    }
    //player controls
    let language = document.getElementById('language')
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    let value = Math.round(videoElement?.currentTime)
    const pausePlayControl = document.getElementById('livePlayPause')
    const seekbarFocus =
      document?.activeElement?.classList?.contains('bmpui-ui-seekbar')
    if (
      event?.key === 'ArrowLeft' &&
      (seekbarFocus || document?.activeElement?.id == 'progressRange')
    ) {
      event?.preventDefault()
      if (state?.showControls == 'live' && pausePlayControl) {
        videoElement.currentTime = value - 10
        player?.seek(value - 10)
        setRunningTime(value - 10)
        // GA: Content Delay/Rewind Event
        playerInstanceRef.current?.seek(value - 10)
        handleRewind()    
      } else {
        setTimeShiftError(
          truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_AccionNoDisponible',
            30
          ) +
            ' | ' +
            truncateText(
              'TvEnVivo_Notificacion_TextoCuerpo_AccionNoDisponible',
              70
            ) ||
            'Acción no disponible | Este canal no permite grabar ni avanzar o retroceder la reproducción'
        )
        setShowLiveControls('')
      }
    } else if (
      event?.key === 'ArrowRight' &&
      (seekbarFocus || document?.activeElement?.id == 'progressRange')
    ) {
      event?.preventDefault()
      // slider.value = Number(slider.value) + 10
      if (state?.showControls == 'live' && pausePlayControl) {
        videoElement.currentTime = value + 10
        player?.seek(value + 10)
        setRunningTime(value + 10)
        // GA: Content Forward Event
        playerInstanceRef.current?.seek(value + 10)
        handleForward()       
      } else {
        setTimeShiftError(
          truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_AccionNoDisponible',
            30
          ) +
            ' | ' +
            truncateText(
              'TvEnVivo_Notificacion_TextoCuerpo_AccionNoDisponible',
              70
            ) ||
            'Acción no disponible | Este canal no permite grabar ni avanzar o retroceder la reproducción'
        )
        setShowLiveControls('')
      }
    }

    //Only for the Arrowup and ok button. the player controls are made visible
    if (
      !language &&
      (event?.key == 'ArrowUp' ||
        (state?.showControls == 'live' && event?.keyCode === 38)) &&
      document.getElementById('miniEpg') == null &&
      document.getElementById('mainEpg') == null
    ) {
      state?.showControls == 'live' && setTimeShiftError('')
    }

    if (event?.keyCode === 415 || event?.keyCode === 19) {
      handlePlayPause()
    }
    if (event?.keyCode === 417) {
      handleForward()
    }
    if (event?.keyCode === 412) {
      handleRewind()
    }

    if (state?.showControls == 'live') {
      //listening to only numeric inputs here 48 to 57 upto 4 digits
      if (
        ((event?.keyCode >= 48 && event?.keyCode <= 57) ||
          codes?.numbercode?.includes(event?.keyCode)) &&
        numRef.current?.length < 4
      ) {
        numRef.current = numRef.current + event?.key
        numRefTimer.current && clearTimeout(numRefTimer.current)
        numRefTimer.current = setTimeout(() => {
          setNum(() => `${parseInt(numRef.current, 10)}`)
        }, 2500)
      } else {
        //In between if any other value occurs then setting num to empty as input is invalid
        setNum(() => '')
      }
    }
  }
  const handleGuiaFocusing = (e) => {
   if (isSelectedChannelTS && focusButton.current === 'idioma') {
     if (e.key === 'ArrowLeft' || e.keyCode === 37) {
      e.preventDefault();
      e.stopPropagation();
      document.getElementById('guia')?.focus();
      focusButton.current = ''
      } else if (e.key === 'ArrowDown' || e.keyCode === 40) {
      setCurrentButtonFocus('')
      e.preventDefault();
      e.stopPropagation();
      focusButton.current = ''
      document.getElementById('live-shortcut-green')?.focus();
      }else if(e.key === 'ArrowRight' || e.keyCode === 39){
      e.preventDefault();
      e.stopPropagation();
      }else if(e.key === 'Enter' || e.keyCode === 13){
      setShowOptionsPanel(true)
      audioData.current = true
      }else{
      focusButton.current = ''
      }
    }
  };
  useEffect(() => {
     const guiaListener = (e) => handleGuiaFocusing(e);
      document.addEventListener('keydown', guiaListener);
     return () => {
      document.removeEventListener('keydown', guiaListener);
     };
   }, [isSelectedChannelTS]);

  const handleMiniEpg = event => {
    let codes = ''
    var liveId = document.getElementById('live')
    var mainId = document.getElementById('mainEpg')
    var programDetailsId = document.getElementById('programDetailsId')
    var alertId = document.getElementById('favouriteAlert')
    var alertDiv = document.getElementById('alertDiv')

    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ChannelUp', 'ChannelDown'])
      codes = {
        chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
        chMinus: tizen.tvinputdevice.getKey('ChannelDown').code
      }
    }

    if (
      (event?.keyCode == 33 ||
        event?.keyCode == 34 ||
        event?.keyCode == 38 ||
        event?.keyCode == 40 ||
        event?.keyCode == 37 ||
        event?.keyCode == 39 ||
        event?.keyCode == 86 ||
        codes.chPlus === event?.keyCode ||
        codes.chMinus === event?.keyCode) &&
      showLiveControls == '' &&
      !programDetailsId &&
      !mainId &&
      !alertId &&
      !alertDiv &&
      !liveId &&
      state?.showControls == 'live'
    ) {
      clearTimeout(timeoutRef.current)
      setShowLiveControls('miniEpg')
      // logic for 12-second disappearance
      timeoutRef.current = setTimeout(() => {
        let mini_Id = document.getElementById('miniEpg')
        let main_Id = document.getElementById('mainEpg')
        let programDetails_Id = document.getElementById('programDetailsId')
        mini_Id && !main_Id && !programDetails_Id && setShowLiveControls('')
      }, miniEpgAutoHideTimeOut?.[region]?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.seconds * 1000 || miniEpgAutoHideTimeOut?.default?.time * 1000 || 5000)
    }
  }

  const getRange = ev => {
    const videoElement = playerDiv
    videoElement.currentTime = ev.target.value
    setRunningTime(ev.target.value)
  }

  const handlePlayPause = () => {
    if (playerStatus === 'play') {
      player?.pause()
      setPlayerStatus('pause')
      // GA: Content Pause Event
      state?.showControls == 'live' && setPausedTime(currentTime)
      rwFwRef.current = true
    } else {
      player?.play()
      setPlayerStatus('play')
      setPausedTime('')
      // GA: Content Play Event
    }
  }

  const handleForward = () => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    let playerTimeShift = Math.floor(
      Math.abs(playerInstanceRef.current?.getTimeShift())
    )
    let playerEndSeekRange = playerInstanceRef.current?.getSeekableRange()?.end
    let playerDiff = moment().unix().toFixed(7) - videoElement?.currentTime
    let interval = 0
    if (
      (playerTimeShift != 0 &&
        forwardTimeRef.current < playerTimeShift &&
        videoElement) || 
      isPastContent
    ) {
      interval = playerDiff < 10 && playerDiff >= 0 ? playerDiff : 10
      forwardTimeRef.current =
        forwardTimeRef.current + (interval + seekCountRef.current)
      videoElement.currentTime =
        videoElement?.currentTime + (interval + seekCountRef.current)
      playerInstanceRef.current.seek(
        Math.min(
          playerEndSeekRange,
          playerInstanceRef.current?.getCurrentTime() +
            (interval + seekCountRef.current)
        )
      )
      seekCountRef.current = 0
      // updateTime()
      // GA: Content Forward Event
    } else {
      rwFwRef.current = false
      startedOverRef.current = false
      playerStatus == 'pause' && handlePlayPause()
      document?.getElementById('progressRange')?.focus()
    }
    playerInstanceRef?.current?.isPaused() && setPausedTime(currentTime)
  }

  const handleRewind = () => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    let playerStartRange = playerInstanceRef.current?.getSeekableRange()?.start
    let playerDiff = videoElement?.currentTime - playerStartRange
    let interval = playerDiff < 10 && playerDiff >= 0 ? playerDiff : 10
    rewindTimeRef.current =
      rewindTimeRef.current + (interval + seekCountRef.current)
    videoElement.currentTime =
      videoElement?.currentTime - (interval + seekCountRef.current)
    playerInstanceRef.current?.seek(
      Math.max(
        0,
        playerInstanceRef.current?.getCurrentTime() -
          (interval + seekCountRef.current)
      )
    )
    seekCountRef.current = 0
    // updateTime()
    // GA: Content Delay/Rewind Event
    rwFwRef.current = true
    playerInstanceRef.current?.isPaused() && setPausedTime(currentTime)
  }

  const loaderContainer = () => {
    return (
      <div>
        <div className="container-loader">
          <Lottie
            options={{
              rendererSettings: {
                preserveAspectRatio: 'xMidYMid slice'
              }
            }}
            loop
            animationData={animationData}
            play
          />
        </div>
      </div>
    )
  }

  const handleMiniEpgShortcut = () => {
    var liveId = document.getElementById('live')
    liveId && !playerError && !liveTvError && setShowLiveControls('miniEpg')
  }

  const handleFastForwardRewind = useCallback(
    event => {
      if (showLiveControls == 'live') {
        if (
          event?.keyCode ===
            tizen.tvinputdevice.getKey('MediaTrackNext').code ||
          event?.keyCode === 75
        ) {
          seekCountRef.current = seekCountRef.current + 20
          handleForward()
        } else if (
          event?.keyCode ===
            tizen.tvinputdevice.getKey('MediaTrackPrevious').code ||
          event?.keyCode === 74
        ) {
          seekCountRef.current = seekCountRef.current + 20
          handleRewind()
        }
      }
    },
    [showLiveControls]
  )

  useEffect(() => {
    document.addEventListener('keydown', handleFastForwardRewind)
    return () => {
      document.removeEventListener('keydown', handleFastForwardRewind)
    }
  }, [])

  //filterig the live channel with the groupID
  let groupChannel = filteredLiveChannels.current?.[0]
  const payway = paywayResponse?.filter(
    each =>
      each?.groups?.includes(groupChannel?.group_id) &&
      each?.npvrstorage != 0 &&
      each?.timeshift != 0
  )

  const handleShowOptions = event => {
    let errorScreen = document.getElementsByClassName('player-err-screen')
    let playerError = document.getElementById('player-error')
    // !(errorScreen?.length > 0) && setShowOptionsPanel(true)
    if(!playerError && errorScreen?.length == 0) {
      setShowOptionsPanel(true)
    }
    event?.preventDefault()
  }

  const getProgressBarStyle = () => {
    let thumb = isPastContent
      ? (typeof playerInstanceRef.current?.getCurrentTime === 'function'
          ? Math.ceil(playerInstanceRef.current.getCurrentTime())
          : 0) - startedOverTimeRef.current
      : typeof playerInstanceRef.current?.getCurrentTime === 'function'
      ? playerInstanceRef.current.getCurrentTime()
      : 0

    let elapseValue =
      ((typeof playerInstanceRef.current?.getSeekableRange === 'function' &&
        playerInstanceRef.current?.getSeekableRange()?.end -
          10 -
          currentEventRef.current?.unix_begin) /
        (currentEventRef.current?.unix_end -
          currentEventRef.current?.unix_begin)) *
      100

    let value = isPastContent
      ? ((typeof playerInstanceRef.current?.getCurrentTime === 'function'
          ? playerInstanceRef.current.getCurrentTime()
          : 0) /
          (currentEventRef.current?.unix_end -
            currentEventRef.current?.unix_begin)) *
          100 +
        1
      : ((thumb - currentEventRef.current?.unix_begin) /
          (currentEventRef.current?.unix_end -
            currentEventRef.current?.unix_begin)) *
        100
      return isPastContent
        ? `linear-gradient(to right, #981c15 0%, #981c15 ${value}%,rgb(133, 133, 133) ${value}%, rgb(133, 133, 133) 100%)`
        : isSelectedChannelTS
        ? `linear-gradient(to right, #981c15, #981c15 ${value}%, rgb(133, 133, 133) ${
            value + 0.05
          }%, rgb(133, 133, 133) ${elapseValue - 1}%, rgba(26, 26, 26, 0.75) ${
            elapseValue - 1
          }%)`
        : `linear-gradient(to right, #981c15 0%, #981c15 ${value}%,rgba(26, 26, 26, 0.75) ${value}%, rgba(26, 26, 26, 0.75) 100%)`
  }

  const programDetailTitle = titleName => {
    if (!titleName) {
      return '' // Return an empty string or handle the null/undefined case as needed
    }

    if (titleName.length >= 30) {
      return `${titleName.slice(0, 30)}...`
    } else {
      return titleName
    }
  }

  const handlelockedScreenMessage = params => {
    let message = ''
    const channelNumber =
      filteredLiveChannels.current?.[0]?.group?.common?.channel_number
    const channelName = filteredLiveChannels.current?.[0]?.group?.common?.title

    if (params == 1) {
      message =
        apilanguage?.TvEnVivo_AlertaCanalBloqueado_TextoTitulo ??
        `El canal {${channelName} ${channelNumber}} está bloqueado`
    } else {
      message =
        apilanguage?.TVEnVivo_AlertaCanalBloqueado_TextoCuerpo ??
        'Para desbloquearlo entra a Opciones del canal e ingresá tu PIN de seguridad'
    }

    return message
  }

  return state?.showControls === 'live' &&
    !liveChannnelInfo?.response &&
    !lockedScreen ? (
    liveTvError ? (
      //The below code will render the player error component which will display the live tv unavailable message
      <PlayerErrorHandler
        data={{
          errFromPlayer: false,
          errorMessage1:
            apilanguage?.fallenChannel_alert_titleNotAvailable_label ||
            'fallenChannel_alert_titleNotAvailable_label',
          errorMessage2:
            apilanguage?.fallenChannel_alert_titleChannelNotAvailable2_label ||
            'fallenChannel_alert_titleChannelNotAvailable2_label'
        }}
      />
    ) : (
      loaderContainer()
    )
  ) : (
    <div className="bitmovin">
      <div id="player" ref={playerDiv} />
      {!!numRef.current && (
        <div id="zapChannelNumber" className="zap-channel-number">
          <span id="zapNumber" className="zap-number">
            {`${numRef.current}`?.padEnd(4, '_')}
          </span>
        </div>
      )}
      {state?.showControls === 'live' &&
        liveTvLoader &&
        !playerError &&
        !liveTvError &&
        loaderContainer()}

      {
        //The below code will render the player error component which will display the player error message
        playerError && (
          <PlayerErrorHandler
            data={{
              errFromPlayer: true,
              lockChannelFlag: lockedScreen,
              chNum:
                filteredLiveChannels.current?.[0]?.group?.common
                  ?.channel_number,
              chName: filteredLiveChannels.current?.[0]?.group?.common?.title,
              chImg: filteredLiveChannels.current?.[0]?.image,
              msg1: lockedScreen
                ? handlelockedScreenMessage(1)
                : apilanguage?.fallenChannel_access_titleChannelNotAvailable_label ||
                  'fallenChannel_access_titleChannelNotAvailable_label',
              msg2: lockedScreen
                ? handlelockedScreenMessage(2)
                : apilanguage?.fallenChannel_alert_titleNotAvailable1_label ||
                  'fallenChannel_alert_titleNotAvailable1_label'
            }}
          />
        )
      }

      {showOptionsPanel && !playerError && !liveTvError && (
        <ProgramDetails
          subtitles={enableSubtitles}
          id={'language'}
          data={liveChannnelInfo?.response}
          hideLanguage={setShowOptionsPanel}
          setShowLiveControls={setShowLiveControls}
          audioData={audioData}
          focusButton={focusButtonSet}
          subscriptions={gaPaywayRef.current?.toLowerCase()}
        />
      )}
      {!showOptionsPanel && !playerError && showLiveControls == 'live' ? (
        <>
          {!favouriteAlertMessage && (
            <div id="live" className="bottom-controls-live">
              <div className="metadata">
                <div className="channel-number">
                  {
                    filteredLiveChannels.current?.[0]?.group?.common
                      ?.channel_number
                  }
                </div>
                <img
                  className="channel-logo"
                  src={filteredLiveChannels.current?.[0]?.image}
                />
                <div className="event-title">
                  {programDetailTitle(
                    programdetailsdata?.payload?.programData?.ext_original_name
                  )}
                </div>
              </div>
              <div className="left-timer">
                <p className="timer-font">
                  {playerStatus == 'pause' || isPlayerStalled
                    ? pausedTime
                    : currentTime}
                </p>
              </div>
              <div className="right-timer">
                <p className="timer-font">
                  {currentTime == 0 ||
                  totalDuration == 0 ||
                  totalDuration == 'Invalid date'
                    ? ''
                    : totalDuration}
                </p>
                <img
                  className="live-icon"
                  src={
                    isPastContent
                      ? past_event_image
                      : startedOverRef.current || rwFwRef.current
                      ? start_over_image
                      : live_image
                  }
                ></img>
              </div>
              <div className="custom-slider-container">
                <input
                  className={'custom-slider focusable'}
                  onFocus={() => setCurrentButtonFocus('seekbar')}
                  onBlur={() => setCurrentButtonFocus('')}
                  type="range"
                  id="progressRange"
                  min={currentEventRef.current?.unix_begin}
                  max={currentEventRef.current?.unix_end}
                  value={
                    playerInstanceRef.current &&
                    typeof playerInstanceRef.current.getCurrentTime ===
                      'function'
                      ? Object.keys(playerInstanceRef.current)?.length > 0 &&
                        isPastContent
                        ? Math.abs(playerInstanceRef.current.getCurrentTime()) +
                          currentEventRef.current?.unix_begin
                        : playerInstanceRef.current.getCurrentTime()
                      : 0
                  }
                  step={1}
                  draggable
                  style={{ background: getProgressBarStyle() }}
                  onChange={getRange}
                  ref={ref}
                />
              </div>
              <div className="player-controls-wrapper">
                <div className="player-controls-cont-live">
                  {payway?.[0]?.npvrstorage > 0 &&
                    payway?.[0]?.timeshift > 0 &&
                    filteredLiveChannels.current?.[0]?.group?.common
                      ?.timeshift !== null &&
                    currentEventRef.current?.ext_recordable == '1' && (
                      <button
                        className="focusable button-wrapper"
                        onFocus={() => setCurrentButtonFocus('record')}
                        onBlur={() => setCurrentButtonFocus('')}
                        onClick={() => {
                          setShowOptionsPanel(true)
                        }}
                      >
                        <img
                          className="play-pause-btn"
                          src={
                            currentButtonFocus === 'record'
                              ? record_focus
                              : getRecordIcon()
                          }
                        />
                        <span className="button-text-wrapper">
                          <p
                            className={
                              currentButtonFocus !== 'record'
                                ? 'btn-text-focus-out'
                                : 'btn-text-focus-in'
                            }
                          >
                            {getRecordText()}
                          </p>
                        </span>
                      </button>
                    )}
                  {isSelectedChannelTS && (
                    <>
                      <button
                        id="livePlayPause"
                        className="focusable button-wrapper"
                        onFocus={() => setCurrentButtonFocus('rewind')}
                        onBlur={() => setCurrentButtonFocus('')}
                        onClick={handleRewind}
                        // onKeyUp={handleRewind}
                      >
                        <img
                          className="play-pause-btn"
                          src={
                            currentButtonFocus === 'rewind'
                              ? rewind_focus
                              : rewind_unfocus
                          }
                        />
                        <span className="button-text-wrapper">
                          <p
                            className={
                              currentButtonFocus !== 'rewind'
                                ? 'btn-text-focus-out'
                                : 'btn-text-focus-in'
                            }
                          >
                            {truncateText(
                              'Player_Boton_TextoAccion_Atrasar',
                              30
                            )}
                          </p>
                        </span>
                      </button>
                      <button
                        autoFocus={
                        focusButton.current == 'idioma'
                        ? false
                        : true
                        }
                        id="livePlayButton"
                        className="focusable button-wrapper"
                        onFocus={() => setCurrentButtonFocus('play')}
                        onBlur={() => setCurrentButtonFocus('')}
                        onClick={handlePlayPause}
                        data-sn-down={'#live-shortcut-green'}
                      >
                        <img
                          className="play-pause-btn"
                          src={
                            playerStatus === 'play'
                              ? currentButtonFocus === 'play'
                                ? pause_focus
                                : pause_unfocus
                              : currentButtonFocus === 'play'
                              ? play_focus
                              : play_unfocus
                          }
                        />
                        <span className="button-text-wrapper">
                          <p
                            className={
                              currentButtonFocus !== 'play'
                                ? 'btn-text-focus-out'
                                : 'btn-text-focus-in'
                            }
                          >
                            {playerStatus == 'play'
                              ? apilanguage?.playing_playerControls_toolbar_button_pause ??
                                'playing_playerControls_toolbar_button_pause'.slice(
                                  0,
                                  19
                                )
                              : truncateText(
                                  'Player_Boton_TextoAccion_Reproducir',
                                  30
                                )}
                          </p>
                        </span>
                      </button>
                      {(rwFwRef.current || isPastContent) && (
                        <button
                          id="fastForward"
                          className="focusable button-wrapper"
                          onFocus={() => setCurrentButtonFocus('forward')}
                          onBlur={() => setCurrentButtonFocus('')}
                          onClick={handleForward}
                          data-sn-down={'#live-shortcut-green'}
                        >
                          <img
                            className="play-pause-btn"
                            src={
                              currentButtonFocus === 'forward'
                                ? forward_focus
                                : forward_unfocus
                            }
                          />
                          <span className="button-text-wrapper">
                            <p
                              className={
                                currentButtonFocus !== 'forward'
                                  ? 'btn-text-focus-out'
                                  : 'btn-text-focus-in'
                              }
                            >
                              {truncateText('Player_Boton_TextoAccion', 30)}
                            </p>
                          </span>{' '}
                        </button>
                      )}
                    </>
                  )}
                  <button
                    id="guia"
                    autoFocus={
                      isSelectedChannelTS
                        ? false
                        : focusButton.current == 'idioma'
                        ? false
                        : true
                    }
                    className="focusable button-wrapper"
                    onFocus={() => setCurrentButtonFocus('guia')}
                    onBlur={() => setCurrentButtonFocus('')}
                    onKeyUp={event =>
                      (event?.keyCode === 13 || event?.key == 'Enter') &&
                      setShowLiveControls('mainEpg')
                    }
                    data-sn-down={'#live-shortcut-green'}
                  >
                    <div
                      className={
                        currentButtonFocus === 'guia' ? 'dot-red' : 'dot'
                      }
                    >
                      <img className="control-btns" src={epg_focus} />
                    </div>
                    <span className="button-text-wrapper">
                      <p
                        className={
                          currentButtonFocus !== 'guia'
                            ? 'btn-text-focus-out'
                            : 'btn-text-focus-in'
                        }
                      >
                        {truncateText('Player_Boton_TextoAccion_Guia', 30)}
                      </p>
                    </span>
                  </button>
                  <button
                    className="focusable button-wrapper"
                    onFocus={() => setCurrentButtonFocus('language')}
                    onBlur={() => setCurrentButtonFocus('')}
                    data-sn-down={'#live-shortcut-green'}
                    onClick={() => {
                      setShowOptionsPanel(true)
                      audioData.current = true
                    }}
                    autoFocus={
                      isSelectedChannelTS
                        ? false
                        : focusButton.current == 'idioma'
                        ? true
                        : false
                    }
                    onKeyDown={handleGuiaFocusing}
                  >
                    <img
                      className="play-pause-btn"
                      src={
                        currentButtonFocus === 'language'
                          ? language_focus
                          : language_unfocus
                      }
                    />
                    <span className="button-text-wrapper">
                      <p
                        className={
                          currentButtonFocus !== 'language'
                            ? 'btn-text-focus-out'
                            : 'btn-text-focus-in'
                        }
                      >
                        {truncateText('Player_Boton_TextoAccion_Idiomas', 30)}
                      </p>
                    </span>
                  </button>
                </div>
              </div>
              <MenuBar
                inLivePlayer={true}
                isSelectedChannelTS={isSelectedChannelTS}
                handleMiniEpgShortcut={handleMiniEpgShortcut}
                handleShowOptions={handleShowOptions}
                handleStartOver={handleStartOver}
                handleBackHome={setLivePlayerClose}
              />
            </div>
          )}
        </>
      ) : showLiveControls === 'miniEpg' && !showOptionsPanel && Object.keys(recordAlertMsg)?.length == 0 ? (
        <div id="miniEpg">
          <MiniEpg
            channelErr={playerError}
            setShowLiveControls={setShowLiveControls}
            handlePastContentDay={handlePastContentDay}
            checkPastContent={checkPastContent}
            checkPastContentState={state?.checkPastContent}
            isPastContent={isPastContent}
            lockedScreen={lockedScreen}
            subscriptions={gaPaywayRef.current?.toLowerCase()}
          />
        </div>
      ) : (
        showLiveControls === 'mainEpg' &&
        state?.showControls === 'live' && (
          <div id="mainEpg">
            <Epg
              channelErr={playerError}
              setShowLiveControls={setShowLiveControls}
              isPastContent={isPastContent}
              handlePastContentDay={handlePastContentDay}
              checkPastContentState={state?.checkPastContent}
              checkPastContent={checkPastContent}
              subscriptions={gaPaywayRef.current?.toLowerCase()}
            />
          </div>
        )
      )}
      {favouriteAlertMessage?.length > 0 && (
        <div className="record-tooltip-main-div">
          <div className="record-tooltip">
            <div className="favourite-icon-container">
              <img
                className="favourite-alert-icon"
                src="images/programDetails_favourite_Icon.png"
              />
            </div>
            <span>
              <span id="favouriteAlert" className="tooltip-main-text">
                <span className="action">
                  {favouriteAlertMessage.split('|')[0]}
                </span>
                <span>{'|' + favouriteAlertMessage.split('|')[1]}</span>
              </span>
            </span>
          </div>
          {showMyContentButton && (
            <button
              onClick={() => handleNavToMycontent()}
              autoFocus={true}
              className="shortcut-tooltip focusable"
            >
              <span className="shortcut-bar-icon shortcut-bar-icon-yellow"></span>
              <span className="tooptip-sub-text">
                {truncateText('BotonShortcut_TextoTitulo_MisContenidos', 15)}
              </span>
            </button>
          )}
        </div>
      )}
      {recordAlertMsg?.message &&
        (recordAlertMsg?.status == 'lock' ? (
          recordAlertMsg?.subStatus == 'add-lock' ? (
            <div id="alertDiv" className="lock-unlock-tooltip-main-div">
              <div className="lock-unlock-tooltip">
                <div className="lock-unlock-image">
                  <img
                    src={recordAlertMsg?.image}
                    className="lock-unlock-image-dimensions"
                  />
                </div>
                <span>
                  <span className="lock-alert">{recordAlertMsg?.message}</span>
                  <span className="tooltip-main-text-lock">
                    {recordAlertMsg?.message2}
                  </span>
                </span>
              </div>
            </div>
          ) : (
            <div id="alertDiv" className="lock-unlock-tooltip-main-div">
              <div className="lock-unlock-tooltip">
                <div className="lock-unlock-image">
                  <img
                    src={recordAlertMsg?.image}
                    className="lock-unlock-image-dimensions"
                  />
                </div>
                <span>
                  <span className="tooltip-main-text-lock">
                    {recordAlertMsg?.message}
                  </span>
                </span>
              </div>
            </div>
          )
        ) : recordAlertMsg?.status == 'reminder' ? (
          <div id="alertDiv" className="reminder-tooltip-main-div">
            <div className="reminder-tooltip">
              <div className="reminder-image">
                <img src={recordAlertMsg?.image} />
              </div>
              <span>
                <span className="tooltip-main-text">
                  {recordAlertMsg?.message}
                </span>
              </span>
            </div>
          </div>
        ) : recordAlertMsg?.status == 'storagealert' ? (
          <div id="alertDiv" className="reminder-tooltip-main-div">
            <div className="reminder-tooltip">
              <div className="reminder-image">
                <img src={recordAlertMsg?.image} className="reminder-width" />
              </div>
              {recordAlertMsg?.message1 && (
                <span className="lock-alert">{recordAlertMsg?.message1}</span>
              )}
              <span>
                <span className="tooltip-main-text">
                  {recordAlertMsg?.message}
                </span>
              </span>
            </div>
            <button
              className="shortcut-tooltip focusable"
              onClick={() => handleNavToMycontent()}
              id="alertMsg"
              autoFocus
            >
              <span className="shortcut-bar-icon shortcut-bar-icon-yellow"></span>
              <span className="tooptip-sub-text">
                {apilanguage?.recording_alert_link_label}
              </span>
            </button>
          </div>
        ) : (
          <>
            <div id="alertDivWrapper"></div>
            <div id="alertDiv" className="record-tooltip-main-div">
              <div className="record-tooltip">
                <div className="add-record-dimensions">
                  <img src={recordAlertMsg?.image} />
                </div>
                <span>
                  <span className="tooltip-main-text">
                    <span className="action">
                      {recordAlertMsg?.message.split('|')[0]}
                    </span>
                    <span>{'|' + recordAlertMsg?.message.split('|')[1]}</span>
                  </span>
                </span>
              </div>
              {recordAlertMsg?.status == 'add' ||
              recordAlertMsg?.status == 'channel-events-future' ? (
                <button
                  className="shortcut-tooltip focusable"
                  onClick={() => {
                    recordAlertMsg?.status == 'add'
                      ? handleNavToMycontent()
                      : (setShowOptionsPanel(true), dispatch(getAlerts({})))
                  }}
                  id="alertMsg"
                  autoFocus={true}
                >
                  {recordAlertMsg?.status == 'add' ? (
                    <span className="shortcut-bar-icon shortcut-bar-icon-yellow"></span>
                  ) : (
                    <img
                      className="shortcut-bar-icon"
                      src={'images/Home_icons/bluedot.png'}
                    />
                  )}
                  <span className="tooptip-sub-text">
                    {recordAlertMsg?.status == 'add'
                      ? apilanguage?.recording_alert_link_label
                      : apilanguage?.timeshift_alert_option_button_menuOptions}
                  </span>
                </button>
              ) : null}
            </div>
          </>
        ))}
      {timeShiftError && (
        <div id="timeShiftError" className="record-tooltip-main-div">
          <div className="record-tooltip">
            <span>
              <span className="tooltip-main-text">{timeShiftError}</span>
            </span>
          </div>
        </div>
      )}
      {
        <div
          className="subtitle-container"
          style={{
            bottom:
              !showOptionsPanel &&
              (showLiveControls === 'miniEpg' || showLiveControls === 'live')
                ? '30%'
                : '5%'
          }}
        >
          <img className="subtitle-image" src={subtitleText}></img>
        </div>
      }
    </div>
  )
}

export default LivePlayer
