import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configure<PERSON>tore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import MobileOTPpage from "./MobileOTPpage";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);

export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const mockerrorresponse = {
   'status':1
}

describe('Mobile otp Landing page test', () => {

	test('should render api mock data', () => {
			initialState.settingsReducer = {
				paymentsConfirm: {
					errors:{
						status:'1'
					}
				},
			}
			renderWithState(<MobileOTPpage />)
	})

	test('should render api mock data', () => {
			initialState.settingsReducer = {
				paymentsConfirm: {
							response: {
									status: 200,
							}
					},
			}
			renderWithState(<MobileOTPpage />)
	})

  test('input should change after onChange', () => {
    const { container } = renderWithState(<MobileOTPpage/>)
    let input = container.querySelector('input[name="input-otp"]')
    fireEvent.change(input, { target: { value: '123456' } })
    renderWithState(<MobileOTPpage />)
  })

	test('handle input resend otp click', () => {
		const { container } = renderWithState(<MobileOTPpage />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'resend-otp')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true,
			})
		)
	})

	test('handle input submit next click', () => {
		const { container } = renderWithState(<MobileOTPpage />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'next')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true,
			})
		)
	})

})
