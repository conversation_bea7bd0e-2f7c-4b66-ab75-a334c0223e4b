import React, { useEffect, useState } from 'react'
import './PlayerEpisode.scss'
import './FinPlayerEpisodeBanner.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useSelector } from 'react-redux'
import { getMediaAPI } from '../store/slices/PlayerSlice'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import {
  getControlPin,
  getStatusControlPin,
  getViewSubscribeData,
  getVodID
} from '../store/slices/settingsSlice'
import { getVodMoviesCastData } from '../store/slices/VodMoviesSlice'
import { pushPlayerInteractionEvent } from '../GoogleAnalytics'
import { CURRENT_PLATFORM } from '../utils/devicePlatform'

const FinPlayerEpisodeBanner = props => {
  const {
    id,
    player,
    playerStatus,
    setClickedNxtEp,
    nxtEpDuration,
    totalDuration,
    nxtEventData,
    backToVcard,
    hidePlayerCont,
    data,
    preLoadSubsInfo,
    autoPlay,
    disabled,
    playbackFinish,
    enablePinPage,
    gaContentData
  } = props

  const dispatch = useDispatch()
  const navigate = useNavigate()
  const subscriptionsInfo = useSelector(
    state => state?.settingsReducer?.getVodSubsInfo?.response
  )
  const vodSeriesDataRedux = useSelector(
    state => state?.getVodSeries?.seriesData
  )
  const region = localStorage.getItem('region')

  const [duration, setDuration] = useState(nxtEpDuration)
  const [play, setPlay] = useState(false)
  const [playbackRetry, setPlaybackRetry] = useState(false)
  const [streamType, setStreamType] = useState('')
  const [visiblePlayButton, setVisiblePlayButton] = useState('')

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const vodDetails = useSelector(state => state?.login?.vcardDetails)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const media = nxtEventData?.common?.id ? nxtEventData?.common : nxtEventData
  const subscriptionBtnInfo =
    preLoadSubsInfo?.listButtons?.button ??
    subscriptionsInfo?.listButtons?.button
  const isSeries =
    media?.is_series ?? media?.extendedcommon?.media?.episode?.number
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const getMediaRedux = useSelector(
    state => state?.player?.getMediaRes?.response
  )
  const getMediaError = useSelector(
    state => state?.player?.getMediaError?.errors?.[0]
  )
  const vodMoviesCast = useSelector(state => state?.vodMovies?.castData)
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles[0]?.parental
        ?.active
  )
  const statusControlPin = useSelector(
    state =>
      state?.settingsReducer?.statusControlPin?.response?.pin_parental?.info
        ?.value
  )
  const age_rating = vodMoviesCast?.common?.extendedcommon?.media?.rating
  const userId = vodDetails?.confirmscreen
    ? loginInfo?.user_id ?? registerInfo?.user_id
    : userDetails?.user_id
  const supportedStream =
    apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)

  useEffect(() => {
    setStreamType(
     !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
        ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
        : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
    )
  }, [])

  useEffect(() => {
    if (!disabled) {
      document.getElementById('finWatchBtn')?.focus() ??
        document.getElementById('seasonBtn')?.focus()
    }
  }, [disabled])

  useEffect(() => {
    if (play && subscriptionsInfo?.playButton?.payway_token) {
      if (
        (securityPinCheck &&
          !isSeries &&
          statusControlPin == 50 &&
          parseInt(age_rating?.code) > 18) ||
        (statusControlPin == 40 && parseInt(age_rating?.code) >= 16) ||
        (statusControlPin == 30 && parseInt(age_rating?.code) >= 13) ||
        (statusControlPin == 20 && parseInt(age_rating?.code) >= 7)
      ) {
        enablePinPage(true)
      }
      setPlay(false)
      handleNext()
    }
    if (
      preLoadSubsInfo?.playButton?.visible ||
      subscriptionsInfo?.playButton?.visible
    ) {
      setVisiblePlayButton(
        preLoadSubsInfo?.playButton?.visible != '0' ? true : false
      )
    }
  }, [play, subscriptionsInfo, preLoadSubsInfo])

  useEffect(() => {
    if (nxtEpDuration) {
      const countTown = Math.round(totalDuration) - Math.round(nxtEpDuration)
      setDuration(countTown >= 0 ? countTown : 0)
    }
  }, [nxtEpDuration])

  useEffect(() => {
    if (nxtEventData && !isSeries) {
      dispatch(
        getVodMoviesCastData({
          id: nxtEventData?.id,
          userId: userId,
          hks: userDetails?.session_stringvalue
            ? userDetails?.session_stringvalue
            : loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue,
          is_kids: vodDetails?.confirmscreen
            ? loginInfo?.is_kids ?? registerInfo?.is_kids
            : userDetails?.is_kids
        })
      )
      dispatch(
        getControlPin({
          hks: userDetails?.session_stringvalue
            ? userDetails?.session_stringvalue
            : loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue,
          user_id: userDetails?.user_id,
          user_token: userDetails?.user_token
        })
      )
      dispatch(
        getStatusControlPin({
          hks: userDetails?.session_stringvalue,
          userId: userDetails?.user_id
        })
      )
    }
  }, [nxtEventData])

  useEffect(() => {
    if (
      !play &&
      duration === 0 &&
      autoPlay &&
      subscriptionsInfo?.playButton?.visible == 1
    ) {
      handleNext()
    } else if (
      (duration === 0 && subscriptionsInfo?.playButton?.visible == 0) ||
      (duration === 0 && !autoPlay)
    ) {
      // playbackFinish(true)
      backToVcard(true)
      enablePinPage(false)
    }
  }, [duration, subscriptionsInfo])

  useEffect(() => {
    const code = getMediaError?.code
    if (getMediaRedux?.media || code) {
      if (code == 'PLY_PLY_00009') {
        setStreamType(
         supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
            ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
        )
        setPlaybackRetry(true)
      }
      if (playbackRetry) {
        setPlaybackRetry(false)
        setStreamType(
           !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
            ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
        )
      }
    }
  }, [getMediaRedux, getMediaError, playbackRetry])

  useEffect(() => {
    if (playbackRetry && streamType) {
      callGetMedia()
    }
  }, [playbackRetry, streamType])

  const handleSubscription = () => {
    const subscriptionButton = preLoadSubsInfo?.listButtons?.button
    let monthData
    subscriptionButton &&
      subscriptionButton?.map(eachdata => (monthData = eachdata))
    const viewPlan = {
      logo: settingLogoUrl(monthData?.family),
      workflowStart: monthData?.linkworkflowstart,
      verticalImage: vodSeriesDataRedux?.image_medium,
      family: monthData?.family,
      periodicity: monthData?.periodicity,
      price: monthData?.price,
      currency: monthData?.currency,
      styles: monthData?.style,
      taxLabel: getTaxLabel(monthData?.family),
      infoString: getFreeChargeString(monthData?.bonus),
      subscribeButton: getSubscribeButton(monthData?.family),
      viewButton: getViewDetailsButton(monthData?.family),
      frequency: monthData?.frequency,
      producttype: monthData?.producttype,
      offertype: monthData?.oneoffertype
    }
    dispatch(getViewSubscribeData(viewPlan))
    dispatch(getVodID(media?.id))
    hidePlayerCont()
    props?.clearVodData()
    player?.destroy().then(() => {
      navigate(
        '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
        {
          state: {
            pageName: data?.extendedcommon?.media?.episode?.season
              ? '/series'
              : '/movies',
            dataId: data,
            vCardBackFocus: props?.vCardBackFocus
          }
        }
      )
    })
  }

  const handleClarovideoBtn = () => {
    navigate('/EPconfirmation', {
      state: {
        page: data?.extendedcommon?.media?.episode?.season
          ? 'series'
          : 'movies',
        vodMoviesData: data,
        seriesEpisodeData: data,
        vCardBackFocus: props?.vCardBackFocus,
        gaContentData: gaContentData
      }
    })
  }

  const handleNext = () => {
    player?.pause()
    playerStatus('stop')
    setClickedNxtEp(true)
    watchFree
      ? dispatch(
          getMediaAPI({
            id: media?.id,
            payway_token: subscriptionsInfo?.playButton?.payway_token,
            type: 'watchfree',
            streamType
          })
        )
      : callGetMedia()
  }

  const callGetMedia = () => {
    const payload = {
      id: media?.id,
      payway_token: subscriptionsInfo?.playButton?.payway_token,
      HKS: vodDetails?.confirmscreen
        ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
        : userDetails?.session_stringvalue,
      userId: vodDetails?.confirmscreen
        ? loginInfo?.user_id ?? registerInfo?.user_id
        : userDetails?.user_id,
      user_token: loginInfo?.user_token ?? registerInfo?.user_token,
      streamType
    }
    dispatch(getMediaAPI(payload))
  }

  const handlePlay = e => {
    pushPlayerInteractionEvent(
      gaContentData,
      e?.target?.innerText?.toLowerCase()
    )
    setPlay(true)
  }

  const truncateText = (str, length, ending) => {
    if (length == null) {
      length = 100
    }
    if (ending == null) {
      ending = '...'
    }
    if (str?.length > length) {
      return str?.substring(0, length - ending?.length) + ending
    } else {
      return str
    }
  }

  const settingLogoUrl = data => {
    return apaAssetsImages[`transactional_${data}_logo`] //checking the data is there in apa/assets
  }

  const getTaxLabel = data => {
    return apilanguage?.[
      `${data}_subscriptionDescription_costTaxIncluded_label`
    ]
  }

  const getFreeChargeString = data => {
    return apilanguage?.[
      `transactional_${data}_subscription_plan_tryInvite_description${data}`
    ]
  }

  const getSubscribeButton = data => {
    return apilanguage?.['buy_' + data]
  }

  const getViewDetailsButton = data => {
    return apilanguage?.['includes_' + data]
  }

  const handleButtonId = index => {
    return document.getElementById(`seasonBtn-${index}`)
  }

  return (
    <div id={id} className="fin-player-main-container">
      <div className="episode-container">
        <div
          id="episodeBtn"
          className="episode-card-img"
          data-sn-down="#back"
          data-sn-up="#seasonBtn"
        >
          <LazyLoadImage
            className="episode-card-lazy"
            src={media?.image_small}
            placeholderSrc={'images/landscape_card.png'}
          />
        </div>
      </div>
      <div className="episodeText">
        <div className="episode-detail">
          <div className="episode-card-Title">
            {nxtEventData?.id && (
              <div className="episode-movieCard-Title">
                {truncateText('También podría gustarte:', 50)}
              </div>
            )}
            <div>
              {truncateText(
                media?.extendedcommon?.media?.originaltitle ?? media?.title,
                50
              )}
            </div>
          </div>
          {isSeries && (
            <div className="episode-number">
              {`${
                apilanguage?.Metadata_TextoTem ??
                'Metadata_TextoTem'.slice(0, 5)
              } ${
                media?.season_number ??
                media?.extendedcommon?.media?.episode?.season
              } | ${
                apilanguage?.Metadata_TextoEpisodio ??
                'Metadata_TextoEpisodio'.slice(0, 3)
              } ${
                media?.episode_number ??
                media?.extendedcommon?.media?.episode?.number
              }: ${truncateText(media?.title_episode ?? media?.title, 18)}`}
            </div>
          )}
          <div
            className="episode-description"
            style={{ marginTop: isSeries ? 15 : 30 }}
          >
            {truncateText(media?.description, 110)}
          </div>
        </div>
      </div>

      <div className="episode-button-container">
        <div
          id="episodeTimelineContainer"
          className="episode-timline-container"
        >
          {preLoadSubsInfo?.playButton?.visible != '0' || visiblePlayButton ? (
            <>
              {isSeries && nxtEpDuration > 0 && autoPlay && (
                <div className="episode-timeline">
                  {' '}
                  {truncateText(
                    apilanguage?.FinPlayer_TarjetaSerie_TextoContador ??
                      'FinPlayer_TarjetaSerie_TextoContador',
                    15
                  )}
                  <span style={{ fontWeight: 300 }}>
                    {' '}
                    {truncateText(
                      `${duration} ${
                        apilanguage?.FinPlayer_TarjetaSerie_TextoContadorBold ??
                        'FinPlayer_TarjetaSerie_TextoContadorBold'
                      }`,
                      15
                    )}
                  </span>
                </div>
              )}
              <div className="episode-button-size">
                <button
                  id="finWatchBtn"
                  autoFocus
                  className={`status-button focusable`}
                  onClick={(e) => handlePlay(e)}
                  data-sn-down={'#back'}
                  disabled={disabled}
                >
                  <p className="status-title">
                    {isSeries
                      ? truncateText(
                          apilanguage?.FinPlayer_TarjetaSerie_BotonPrimario_TextoTitulo ??
                            'FinPlayer_TarjetaSerie_BotonPrimario_TextoTitulo',
                          45
                        )
                      : truncateText(
                          apilanguage?.FinPlayer_TarjetaPelicula_BotonPrimario_TextoTitulo ??
                            'FinPlayer_TarjetaPelicula_BotonPrimario_TextoTitulo',
                          45
                        )}
                  </p>
                </button>
              </div>
            </>
          ) : (
          (  preLoadSubsInfo?.playButton?.visible == '0' || !visiblePlayButton) &&
            subscriptionBtnInfo &&
            subscriptionBtnInfo?.length > 0 &&
            subscriptionBtnInfo?.map((buttonInfo, index) => (
              <>
             
        {buttonInfo?.oneoffertype == 'download_buy'  && buttonInfo?.producttype == 'CV_EPISODEBUY'  && !watchFree && (
                  <div className="episode-button-size">
                    <button
                      id={`seasonBtn-${index}`}
                      autoFocus
                      className={`episode-btn-color status-buttonText focusable`}
                      onClick={handleSubscription}
                      data-sn-up={
                        document.getElementById('seasonBtn-0') && index == 1 ? '#seasonBtn-0' : '#finWatchBtn' 
                      }
                      data-sn-down={
                        index == 0 && document.getElementById('seasonBtn-1') ? '#seasonBtn-1' : index == 1 ? '#back' : ''
                      }
                      disabled={disabled}
                    >
                       <p className="status-title">
                            {truncateText(
                              `${apilanguage?.FinPlayer_TarjetaAdquirirSerie_BotonEpisodio_TextoTitulo} ${buttonInfo?.currency}${buttonInfo?.price}`,
                              45
                            )}
                          </p>
                    </button>
                  </div>
                )}


                {buttonInfo?.oneoffertype == 'download_buy' && buttonInfo?.producttype == 'CV_SEASONBUY' && !watchFree && (
                  <div className="episode-button-size">
                    <button
                      id={`seasonBtn-${index}`}
                      autoFocus
                      className={`temporado-btn-color status-buttonText focusable`}
                      onClick={handleSubscription}
                      data-sn-up={
                        document.getElementById('seasonBtn-0') && index == 1 ? '#seasonBtn-0' : '#finWatchBtn' 
                      }
                      // data-sn-down={
                      //   index == 0 && document.getElementById('seasonBtn-1') ? '#seasonBtn-1' : index == 1 ? '#back' : ''
                      // }
                      disabled={disabled}
                    >
                       <p className="status-title">
                            {truncateText(
                              `${apilanguage?.FinPlayer_TarjetaAdquirirSerie_BotonTemporada_TextoTitulo}${buttonInfo?.currency}${buttonInfo?.price}`,
                              75
                            )}
                          </p>
                    </button>
                  </div>
                )}

                {buttonInfo?.oneoffertype == 'subscrition' && (buttonInfo?.producttype != 'CV_EPISODEBUY' || buttonInfo?.producttype != 'CV_SEASONBUY') && !watchFree && (
                  <div className="episode-button-size">
                    <button
                      id={`seasonBtn-${index}`}
                      autoFocus
                      className={`episode-btn-color status-buttonText focusable`}
                      onClick={handleSubscription}
                      data-sn-up={
                        document.getElementById('seasonBtn-0') && index == 1 ? '#seasonBtn-0' : '#finWatchBtn' 
                      }
                      data-sn-down={
                        index == 0 && document.getElementById('seasonBtn-1') ? '#seasonBtn-1' : index == 1 ? '#back' : ''
                      }
                      disabled={disabled}
                    >
                      <p className="status-title">
                        {!buttonInfo?.price && !buttonInfo?.currency
                          ? truncateText(buttonInfo?.oneofferdesc, 45)
                          : truncateText(
                              `${buttonInfo?.oneofferdesc} $ ${buttonInfo?.price}`,
                              45
                            )}
                      </p>
                    </button>
                  </div>
                )}

                {(buttonInfo?.oneoffertype != 'subscrition' && (buttonInfo?.producttype != 'CV_EPISODEBUY' && buttonInfo?.producttype != 'CV_SEASONBUY') && !watchFree) && (
                  <div className="episode-button-size">
                    {buttonInfo?.periodicity == 'hour' &&
                      (buttonInfo?.oneoffertype == 'rent' ||
                        buttonInfo?.oneoffertype == 'download_rent'  && (buttonInfo?.producttype != 'CV_EPISODEBUY' || buttonInfo?.producttype != 'CV_SEASONBUY')) && (
                        <button
                          id="finWatchBtn"
                          autoFocus
                          className={`rent-btn-color status-buttonText focusable`}
                          onClick={handleSubscription}
                          data-sn-down={'#back'}
                          disabled={disabled}
                        >
                          <p className="status-title">
                            {truncateText(
                              `${apilanguage?.FinPlayer_TarjetaAdquirirPelicula_BotonAlquilar_TextoTitulo} ${buttonInfo?.frequency}${apilanguage?.FinPlayer_TarjetaAdquirirPelicula_BotonAlquilar_TextoHS} ${buttonInfo?.currency}${buttonInfo?.price}`,
                              45
                            )}
                          </p>
                        </button>
                      )}
                    {buttonInfo?.periodicity == 'hour' &&
                      ((buttonInfo?.oneoffertype == 'buy' ||
                        buttonInfo?.oneoffertype == 'download_buy') && buttonInfo?.producttype != 'CV_EPISODEBUY' || buttonInfo?.producttype != 'CV_SEASONBUY') && (
                        <button
                          id={`seasonBtn-${index}`}
                          autoFocus={!document.getElementById('finWatchBtn')}
                          className={`subscribtion-btn-color status-buttonText focusable`}
                          onClick={handleSubscription}
                          data-sn-up={
                            document.getElementById('seasonBtn-0') && index == 1 ? '#seasonBtn-0' : '#finWatchBtn'
                          }
                          disabled={disabled}
                        >
                          <p className="status-title">
                            {truncateText(
                              `${apilanguage?.FinPlayer_TarjetaAdquirirPelicula_BotonComprar_TextoTitulo} ${buttonInfo?.currency}${buttonInfo?.price}`,
                              45
                            )}
                          </p>
                        </button>
                      )}
                  </div>
                )}
                {isSeries &&
                  watchFree &&
                  nxtEventData?.common?.extendedcommon?.format?.types !==
                    'free' &&
                  nxtEventData?.common?.extendedcommon?.format?.types !==
                    'free,download' && (
                    <div className="episode-button-size">
                      <button
                        id="finWatchBtn"
                        autoFocus
                        className={`clarovideo-btn-color focusable`}
                        onClick={(e) => handleClarovideoBtn(e)}
                        data-sn-down={index == 0 && document.getElementById('seasonBtn-1') ? '#seasonBtn-1' : index == 1 ? '#back' : '' }
                        disabled={disabled}
                      >
                        <p className="clarovideo-status-title">
                          {apilanguage?.offer_button_desc_subscription_telmexmexico_subscription_svod_30d ??
                            'offer_button_desc_subscription_telmexmexico_subscription_svod_30d'.slice(
                              0,
                              15
                            )}
                        </p>
                      </button>
                    </div>
                  )}
              </>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default FinPlayerEpisodeBanner
