import React, { useCallback, useEffect } from 'react'
import './DeleteProfile.scss'
import { useDispatch } from 'react-redux'
import {
  getProfileDeleteData,
  getProfileReadData,
} from '../../store/slices/ProfileSlice'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { setLastTouch } from '../../store/slices/settingsSlice'
import { pushDeleteProfileEvent, pushScreenViewEvent } from '../../GoogleAnalytics'

const DeleteProfile = () => {
  const { state } = useLocation()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { data } = state

  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response,
  )
  const profileDeleteRedux = useSelector(
    state => state?.profile?.profileDeleteData,
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')
  const deleteButton = translations?.language?.[region]
    ?.Perfiles_EliminarPerfil_TextoBotonPrimario
    ? translations?.language?.[region]
        ?.Perfiles_EliminarPerfil_TextoBotonPrimario
    : 'Perfiles_EliminarPerfil_TextoBotonPrimario'
  const cancelButton = translations?.language?.[region]
    ?.Perfiles_EliminarPerfil_TextoBotonSecundario
    ? translations?.language?.[region]
        ?.Perfiles_EliminarPerfil_TextoBotonSecundario
    : 'Perfiles_EliminarPerfil_TextoBotonSecundario'
  const moduleName = translations?.language?.[region]
    ?.Perfiles_EliminarPerfil_Título_TextoTitulo
    ? translations?.language?.[region]
        ?.Perfiles_EliminarPerfil_Título_TextoTitulo
    : 'Perfiles_EliminarPerfil_Título_TextoTitulo'

  useEffect(() => {
    if (profileDeleteRedux?.msg === 'OK') {
      const payload = {
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue,
        userid: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
        gamificationid: loginInfo
          ? loginInfo?.gamification_id
          : registerInfo?.gamification_id,
        token: loginInfo ? loginInfo?.user_token : registerInfo?.user_token,
        lasttouch: profileDeleteRedux?.response?.lasttouch?.profile,
      }
      dispatch(setLastTouch(profileDeleteRedux?.response?.lasttouch?.profile))
      dispatch(getProfileReadData(payload))
      navigate('/watcheditprofile', {
        state: { data: 'Profile Deleted' },
      })
    }
  }, [profileDeleteRedux])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate('/editprofile', {
        state: { data, selectedProf: data?.user_image },
      })
    }
  }
  const handleLgkey = (keycode) => {
    if (keycode === 405 || keycode === 461) {
      navigate('/editprofile', {
        state: { data, selectedProf: data?.user_image },
      })
    }
  }

  const goToWatchEdit = useCallback(event => {
    if (
      event.keyCode === 10009 ||
      event.keyCode === 8 ||
      event.keyCode === 461
    ) {
      navigate('/editprofile', {
        state: { data, selectedProf: data?.user_image },
        replace: true,
      })
      return null
    }
  }, [])

  useEffect(() => {
    document.body.addEventListener('keyup', goToWatchEdit)

    return () => {
      document.body.removeEventListener('keyup', goToWatchEdit)
    }
  }, [goToWatchEdit])

  const keypresshandler = (event) => {
    const keycode = event.keyCode;
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow']);
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode);
    } else {
      handleLgkey(keycode);
    }
  };

  useEffect(() => {
    document.addEventListener("keyup", keypresshandler);
    return () => {
      document.removeEventListener("keyup", keypresshandler);
    };
  }, [keypresshandler]);

  useEffect(()=>{
    pushScreenViewEvent({screenName:'delete_profile', screenData: loginInfo ?? registerInfo, prevScreenName: 'edit_profile'})
  },[])



  const handleDelete = () => {
    const payload = {
      hks: loginInfo
        ? loginInfo?.session_stringvalue
        : registerInfo?.session_stringvalue,
      userid: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
      gamificationid: data?.gamification_id,
      user_token: loginInfo ? loginInfo?.user_token : registerInfo?.user_token,
    }
    dispatch(getProfileDeleteData(payload))
    //GA for profile event
    pushDeleteProfileEvent(loginInfo,deleteButton?.toLowerCase(),moduleName?.toLowerCase())
  }
  const handle_delcancel = () => {
    navigate('/editprofile', {
      state: { data, selectedProf: data?.user_image },
      // replace: true,
    })
    //GA for profile event
    pushDeleteProfileEvent(loginInfo,cancelButton?.toLowerCase(),moduleName?.toLowerCase())
  }

  return (
    <div className="delete-profile">
      <div className="profile-app-logo">
        <img
          src={'images/claro-profile-logo.png'}
          className="logo-img"
          alt="logo"
        />
        <button className="back-button focusable" id='back-button-del'
          onClick={handle_delcancel}>
          <img
            src={'images/Profile_Icons/ic_shortcut_amarillo.png'}
            className="yellow-dot"
            alt="img not found"
          />
          <img
            src={'images/Profile_Icons/ic_shortcut_back.png'}
            className="back-arrow"
            alt="img not found"
          />
          <span className="back-button-text">
            {translations?.language?.[region]?.atv_back_notification}
          </span>
        </button>
      </div>
      <div className="delete-profile-container">
        <h5 className="del-profile-heading">
          {moduleName}
        </h5>
        <img
          src={data?.user_image}
          className="del-profile-image"
          alt="img not found"
        ></img>
        <p className="del-profile-title">{data?.username}</p>
        <div className="del-profile-height">
          <p className="del-profile-name">
            {translations?.language?.[region]
              ?.Perfiles_EliminarPerfil_TextoDescriptivo1
              ? translations?.language?.[region]
                ?.Perfiles_EliminarPerfil_TextoDescriptivo1
              : 'Perfiles_EliminarPerfil_TextoDescriptivo1'}
              {translations?.language?.[region]
              ?.Perfiles_EliminarPerfil_TextoDescriptivo2
              ? translations?.language?.[region]
                ?.Perfiles_EliminarPerfil_TextoDescriptivo2
              : 'Perfiles_EliminarPerfil_TextoDescriptivo2'}
              {translations?.language?.[region]
              ?.Perfiles_EliminarPerfil_TextoDescriptivo3
              ? translations?.language?.[region]
                ?.Perfiles_EliminarPerfil_TextoDescriptivo3
              : 'Perfiles_EliminarPerfil_TextoDescriptivo3'}

          </p>
        </div>
        <div className='parent-deleteprofile-container'>
          <button
            autoFocus
            className="del-profile-button focusable"
            onClick={handleDelete}
          >
            <span className="del-profile-txt">
              {deleteButton}
            </span>{' '}
          </button>
          <button
            id="del-button-id"
            onClick={handle_delcancel}
            className="cancel-button-del focusable"
          >
            <span className="cancel-del-txt">
              {' '}
              {cancelButton}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default DeleteProfile
