import React from "react";
import { fireEvent, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import Loginpage from "./Loginpage";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mockerrorresponse = {
    "status": "1"
}
const mocksuccessresponse = {
    "status": "0"
}
const loginsuccessmock = {
    "status": "0"
}
const Errormock = {
    "type": "internal",
    "error": [
        "El usuario o contraseña ingresado es incorrecto, favor de intentarlo nuevamente."
    ],
    "code": "user_login_invalido"
}

describe('Landing page test', () => {
    test('should render api mock data', () => {
        initialState.login = {
            userInfo: mockerrorresponse
        }
        renderWithState(<Loginpage />)
    })
    test('should render api mock data', () => {
        initialState.login = {
            userInfo: mocksuccessresponse
        }
        renderWithState(<Loginpage />)
    })
    test('should render api mock data', () => {
        initialState.login = {
            loginError: {
                errors: Errormock
            }
        }
        renderWithState(<Loginpage />)
    })
    test('should render api mock data', () => {
        initialState.login = {
            userInfo: mocksuccessresponse
        }
        initialState.login = {
            loginSuccess: loginsuccessmock
        }
        renderWithState(<Loginpage />)
    })
    test('it should render the Loginpage Page onclick enter email button', () => {
        const { container } = renderWithState(<Loginpage />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'email');
        fireEvent.keyPress(scroll, { key: "Enter", charCode: 13 })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/loginmail')
    })

    test('it should render the Loginpage Page onclick enter password button', () => {
        const { container } = renderWithState(<Loginpage />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'password');
        fireEvent.keyPress(scroll, { key: "Enter", charCode: 13 })
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/loginpassword')
    })
    test('it should render the Loginpage Page onclick next button', () => {
        const { container } = renderWithState(<Loginpage />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'registernext');
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('it should render the Loginpage Page onclick next button', () => {
        const { container } = renderWithState(<Loginpage />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'rigisterbtn');
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('it should render the Loginpage Page onclick next button', () => {
        const { container } = renderWithState(<Loginpage />)
        localStorage.getItem('signinemail', "<EMAIL>")
        localStorage.getItem('pwd', 'JLiF2g0tN@')
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'signid');
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})