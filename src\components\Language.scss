.language-container {
  height: 1080px;
  width: 1920px;
  position: absolute;
  flex-direction: row;
  display: flex;
  z-index: 999;
  top: 0;
  .language-left-container {
    background-color: #000000;
    width: 1312px;
    height: 1080px;
    opacity: 0.95;
    .back-btn {
      height: 48px !important;
      width: 292px !important;
      border-radius: 6.6px;
      background-color: #2e303d;
      margin: 40px 82px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
 .back-icon{
        position: absolute;
        left: 145px;
        height: 26px;
        width: 30px;
        margin-bottom: 3px;
      }
.yellow-dot-netrange {
 position: absolute;
        left: 104px;
        height: 20px;
        width: 20px;
      }
      .back-text-netrange {
 position: absolute;
        left: 202px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 29.04px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 29.04px;
      }
      .yellow-dot {
        height: 20px;
        width: 20px;
      }
      .back-text {
        color: #ffffff;
        font-family: Roboto;
        font-size: 29.04px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 29.04px;
      }
      &:focus {
          background-color: #C60000;
  
      }
    }
    .details-container {
      margin-top: 190px;
      margin-left: 120px;
      .content-title {
        width: 100%;
        height: auto;
        color: #ffffff;
        font-family: Roboto;
        font-size: 65px;
        font-weight: 900;
        letter-spacing: 0;
        line-height: 71px;
        margin: 0;
        margin-bottom: 38px;
      }
      .episode-details {
        height: 40px;
        color: #ffffff;
        font-family: Roboto;
        font-weight: 400;
        font-size: 35px;
        letter-spacing: 0;
        line-height: 40px;
        margin: 0;
        margin-top: 16px;
      }
      .episode-description {
        height: 160px;
        width: 1077px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 32px;
        font-weight: 900;
        letter-spacing: 0;
        line-height: 40px;
        word-wrap: break-word;
        margin-top: 28px;
      }
    }
  }
  .language-right-container {
    width: 608px;
    height: 1080px;
    background-color: #282828;
    opacity: 1;
    .language-sub-container {
      margin-top: 152px;
      .language-label {
        height: 40px;
        margin-left: 40px;
        color: #999999;
        font-family: Roboto;
        font-size: 32px;
        letter-spacing: 0;
        line-height: 40px;
        font-weight: 400;
      }
      .language-btn {
        height: 120px;
        width: 608px;
        display: flex;
        align-items: center;
        .language-icon {
          width: 56px;
          height: 39.2px;
          margin-left: 32px;
        }
        .language-title {
          height: 48px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: 0;
          line-height: 48px;
          margin-left: 16px;
        }
        .language-sel-icon {
          width: 45px;
          height: 34px;
          margin-left: auto;
          margin-right: 30px;
        }
      }
      .language-btn:focus {
        background-color: #c60000;
      }
    }
  }
}
