import React, { useEffect } from 'react'
import './EpConfirmation.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { store } from '../../store/sagaStore'
import {
  getSeriesData,
  getVcardData,
  getGuestUserPlayerData,
  getRegisterNavigation,
  getLoginNavigation
} from '../../store/slices/login'
import {
  EnableplayerEpisodeScreen,
  getChannelData
} from '../../store/slices/PlayerSlice'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import { pushNewInteractionContentEvent } from '../../GoogleAnalytics'
import { CONTINUA_VIENDO, INTERACTION_PLAYER, interactionType } from '../../GoogleAnalyticsConstants'

const EpConfirmation = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()
  const data = state?.seriesEpisodeData
  const livePlayerData = state?.data
  const vodMovies = state?.vodMoviesData
  const page = state?.page ?? props?.page

  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const keypressfun = event => {
    if (
      event?.keyCode == 461 ||
      event?.keyCode == 10009 ||
      event?.keyCode == 8
    ) {
      pushNewInteractionContentEvent(
        {modulo_name: CONTINUA_VIENDO, ...state?.gaContentData}, 
        INTERACTION_PLAYER, 
        state?.gaContentData?.content_type,
        interactionType.CANCELAR
      )
      switch (page) {
        case 'details':
        case 'loginmail':
        case 'registermail':
          if (state?.fromDetailsPage) {
            navigate(
              '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
              {
                state: {
                  seriesEpisodeData:
                    data ?? state?.vodMoviesData ?? state?.vodData,
                  page: state?.page,
                  fromDetailsPage: true,
                  pageName: state?.pageName,
                  vodMoviesData:
                    vodMovies ??
                    state?.vodData ??
                    state?.vodMoviesData ??
                    state?.seriesEpisodeData ??
                    data
                }
              }
            )
          } else if (state?.pageName == '/series') {
            navigate('/series', {
              state: {
                data: data,
                episodeItemClicked: state?.episodeItemClicked,
                backfocusid: state?.backfocusid
              }
            })
          } else {
            navigate('/movies', {
              state: {
                vodData:
                  vodMovies ??
                  state?.vodData ??
                  state?.vodMoviesData ??
                  state?.seriesEpisodeData ??
                  data
              }
            })
          }
          break
        case 'livePlayer':
          handleBackLivePlayer()
          break;
        case 'search':
          navigate('/search', {
            state: { inputValue: state?.inputValue, page: state?.page },
            replace: true
          })
          localStorage.setItem('currNavIdx', -1)
          dispatch(getNavTabValue('search'))
          break;
        case 'series':
          navigate('/series', { state: { data: data, backfocusid: state?.backfocusid, episodeItemClicked: state?.episodeItemClicked } })
          break
        case 'playerrecord':
          props?.playplayer?.play()
          store.dispatch(EnableplayerEpisodeScreen(false))
          break
        default:
          navigate('/movies', {
            state: {
              vodData:
                vodMovies ??
                state?.vodData ??
                state?.vodMoviesData ??
                state?.seriesEpisodeData ??
                data
            }
          })
      }
    }
  }

  const handleBackLivePlayer = () => {
    if (state?.page == 'search') {
      navigate('/search', {
        state: { inputValue: state?.inputValue },
        replace: true
      })
      localStorage.setItem('currNavIdx', -1)
      dispatch(getNavTabValue('search'))
    } else {
      const group_id = paywayResponse[0]?.groups?.split(',').pop() || ''
      dispatch(
        getChannelData({
          group_id: group_id,
          switchChannel: 'yes',
          epgIndex: epgSevenDaysData[1]?.channelResponse?.findIndex(
            itrObj => itrObj.group_id == group_id
          )
        })
      )
      navigate('/livePlayer', {
        state: {
          data: livePlayerData,
          showControls: 'live',
          grid: state?.grid
        }
      })
    }
  }

  const handleCancel = () => {
    pushNewInteractionContentEvent(
      {modulo_name: CONTINUA_VIENDO, ...state?.gaContentData}, 
      INTERACTION_PLAYER, 
      state?.gaContentData?.content_type,
      interactionType.CANCELAR
    )
    localStorage.setItem('subMenu', 1)
    switch (page) {
      case 'livePlayer':
        handleBackLivePlayer()
        break
      case 'playerrecord':
        props?.playplayer?.play()
        store.dispatch(EnableplayerEpisodeScreen(false))
        break
      case 'search':
        navigate('/search', {
          state: { inputValue: state?.inputValue, page: state?.page },
          replace: true
        })
        localStorage.setItem('currNavIdx', -1)
        dispatch(getNavTabValue('search'))
        break
      case 'details':
      case 'loginmail':
      case 'registermail':
        if (state?.fromDetailsPage) {
          navigate(
            '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
            {
              state: {
                seriesEpisodeData:
                  data ?? state?.vodMoviesData ?? state?.vodData,
                page: state?.page,
                fromDetailsPage: true,
                pageName: state?.pageName,
                vodMoviesData:
                  vodMovies ??
                  state?.vodData ??
                  state?.vodMoviesData ??
                  state?.seriesEpisodeData ??
                  data
              }
            }
          )
        } else if (state?.pageName == '/series') {
          navigate('/series', {
            state: {
              data: data,
              episodeItemClicked: state?.episodeItemClicked,
              backfocusid: state?.backfocusid
            }
          })
        } else {
          navigate('/movies', {
            state: {
              vodData:
                vodMovies ??
                state?.vodData ??
                state?.vodMoviesData ??
                state?.seriesEpisodeData ??
                data
            }
          })
        }
        break
      case 'series':
        navigate('/series', { state: { data: data, episodeItemClicked: state?.episodeItemClicked, backfocusid: state?.backfocusid } })
        break
      default:
        navigate('/movies', {
          state: {
            vodData:
              vodMovies ??
              state?.vodData ??
              state?.vodMoviesData ??
              state?.seriesEpisodeData ??
              data
          }
        })
    }
  }

  const capitalizeSentences = text => {
    return text
      .split('.')
      .map(sentence => {
        const words = sentence.trim().split(' ')
        const capitalizedFirstWord =
          words[0].charAt(0).toUpperCase() + words[0].slice(1).toLowerCase()
        const remainingWords = words.slice(1).map(word => word.toLowerCase())
        return [capitalizedFirstWord, ...remainingWords].join(' ')
      })
      .join('. ')
  }

  const titleHandle = page => {
    if (page === 'livePlayer') {
      return {
        title: '¿Quieres reproducir este canal',
        message: ' Para reproducir este canal iniciá sesión o regístrate'
      }
    } else if (page === 'playerrecord') {
      return {
        title: capitalizeSentences(
          apilanguage?.continuePlayback_modal_option_button_continueToSee ??
          'continuePlayback_modal_option_button_continueToSee'
        ),
        message: capitalizeSentences(
          apilanguage?.keepWatching_modal_description_label ??
          'keepWatching_modal_description_label'
        )
      }
    } else {
      return {
        title: '¿Quieres reproducir este contenido?',
        message: 'Para reproducir este contenido iniciá sesión o regístrate'
      }
    }
  }

  const { title, message } = titleHandle(page)

  const navigateLoginAndRegister = () => {
    pushNewInteractionContentEvent(
      {modulo_name: CONTINUA_VIENDO, ...state?.gaContentData}, 
      INTERACTION_PLAYER, 
      state?.gaContentData?.content_type,
      interactionType.CANCELAR
    )
    if (state?.page === 'livePlayer') {
      store.dispatch(
        getGuestUserPlayerData({
          confirmscreen: true,
          data: livePlayerData,
          page: state?.page,
          grid: state?.grid
        })
      )
    } else {
      store.dispatch(EnableplayerEpisodeScreen(false))
      store.dispatch(
        getVcardData({
          confirmscreen: true,
          vodMoviesData: vodMovies,
          page: state?.page
        })
      )
      store.dispatch(
        getSeriesData({
          confirmscreen: true,
          vodSeries: data,
          page: state?.page,
          playerpage: props?.page,
          playerepisode: props?.clickedepisode
        })
      )
    }
  }

  const registerNavigate = () => {
    pushNewInteractionContentEvent(
      {modulo_name: CONTINUA_VIENDO, ...state?.gaContentData}, 
      INTERACTION_PLAYER, 
      state?.gaContentData?.content_type,
      interactionType.REGISTRATE
    )
    navigateLoginAndRegister()
    navigate('/register', {
      state: {
        seriesEpisodeData:
          state?.seriesEpisodeData ??
          data ??
          state?.vodMoviesData ??
          state?.vodData,
        vodMoviesData: state?.vodMoviesData ?? state?.vodData,
        page: state?.page,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage,
        inputValue: state?.inputValue
      }
    })
  }

  const signNavigate = () => {
    pushNewInteractionContentEvent(
      {modulo_name: CONTINUA_VIENDO, ...state?.gaContentData}, 
      INTERACTION_PLAYER, 
      state?.gaContentData?.content_type,
      interactionType.INICIO_SESSION
    )
    navigateLoginAndRegister()
    navigate('/signin', {
      state: {
        seriesEpisodeData:
          state?.seriesEpisodeData ??
          data ??
          state?.vodMoviesData ??
          state?.vodData,
        vodMoviesData: state?.vodMoviesData ?? state?.vodData,
        page: state?.page,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage,
        inputValue: state?.inputValue
      }
    })
  }
  useEffect(() => {
    store.dispatch(getRegisterNavigation(false))
    store.dispatch(getLoginNavigation(false))
    document.addEventListener('keyup', keypressfun)

    return () => {
      document.removeEventListener('keyup', keypressfun)
    }
  }, [keypressfun])

  return (
    <div className="epconf-body">
      <img
        src={'images/claro-video-logo.png'}
        className="confirmation-logo-img"
        alt="logo"
      />
      <p
        className={
          page == 'playerrecord'
            ? 'play-confirmation-main-title'
            : 'confirmation-main-title'
        }
      >
        {title}
      </p>
      <p
        className={
          page == 'playerrecord'
            ? 'play-confirmation-login-title'
            : 'confirmation-login-title'
        }
      >
        {message}
      </p>
      <div className={'main-buttons'}>
        {' '}
        //"Main-buttons"
        <button
          autoFocus
          onClick={() => registerNavigate()}
          id="registerId"
          className="confirm-register-name focusable"
        >
          {apilanguage?.keepWatching_option_button_register ??
            'keepWatching_option_button_register'}
        </button>
        <button
          onClick={() => signNavigate()}
          id="signinId"
          className="confirm-sign-name focusable"
        >
          {apilanguage?.keepWatching_option_button_login ??
            'keepWatching_option_button_login'}
        </button>
        <button
          id="cancelid"
          className="confirm-cancel-button focusable"
          onClick={() => handleCancel()}
        >
          {apilanguage?.keepWatching_option_button_cancel ??
            'keepWatching_option_button_cancel'}
        </button>
      </div>
    </div>
  )
}

export default EpConfirmation
