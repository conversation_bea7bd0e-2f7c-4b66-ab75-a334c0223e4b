import { call, takeEvery } from '@redux-saga/core/effects'
import { store } from '../store/sagaStore'
import { getPasswordSccess, recoverEmail } from '../store/slices/login'
import { URL } from '../utils/environment'
import { request } from '../utils/request'

function* getPasswordApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.PASSWORD_URL}&email=${payload}&region=${region}`,
      {
        method: 'POST'
      },
      {
        onSuccess(response) {
          store.dispatch(getPasswordSccess(response))
        },
        onError(error) {
          store.dispatch(recoverEmail(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* RecoverPasswordSaga() {
  yield takeEvery('login/getPassword', getPasswordApi)
}
