import React, { useCallback, useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import AlphaNumericKeyboard from '../../Keyboard/AlphaNumericKeboard'
import { useNavigate, useLocation } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import <PERSON><PERSON> from 'react-lottie-player'
import '../../../styles/Checkout.scss'
import animationData from '../../../json/animationData.json'
import FibraLinesPopup from './fibraLinesPopup'
import {
  clearPromoCode,
  getAddPromoCode,
  getAddPayments,
  getAddFibraLinePayments,
  setlastTypedText,
  getPaymentsConfirm,
  clearFibraLineData,
  getFibraPaymentsConfirm,
  addTelcelPayment,
  confirmTelcel,
  addClient,
  addCreditCard,
  updateCreditCard,
  creditCardPayment,
  clearPaymentError,
  getClearAllPaymentsData
} from '../../../store/slices/settingsSlice'
import { BackButtonComponent } from '../../../components/CommonComponent'
import CreditCardDataPopup from './CreditCardDataPopup'
import {
  creditCardPlaceHolder,
  hubfacturaPlaceholder
} from '../../../utils/constant'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'

const Checkout = () => {
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()
  const [errMsg, setErrMsg] = useState('')
  const [checkoutInputValue, setCheckoutInputValue] = useState('')
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [isOtpPage, setIsOtpPage] = useState(false)
  const [accountNumber, setAccountNumber] = useState('')
  const [paywayText, setPaywayText] = useState('')
  const [cardName, setCardName] = useState('')
  const [cardCVV, setCardCVV] = useState('')
  const [focusedInputBoxIndex, setFocusedInputBoxIndex] = useState(0)
  const [creditPopupType, setCreditPopupType] = useState('')
  const [isCreditPopupPage, setIsCreditPopupPage] = useState(false)
  const [monthValue, setMonthValue] = useState('')
  const [yearValue, setYearValue] = useState('')
  const [areaValue, setAreaValue] = useState('')
  const [isCreditAddressPage, setIsCreditAddressPage] = useState(false)
  const [pinCode, setPinCode] = useState('')
  const [addressLine, setAddressLine] = useState('')
  const [cityLine, setCityLine] = useState('')
  const [mobileNumber, setMobileNumber] = useState('')
  const [mobileNumberError, setMobileNumberError] = useState('')
  const [isCustomerDataAvailable, setIsCustomerDataAvailable] = useState(false)
  const [confirmPayment, setConfirmPayment] = useState(false)
  const [claroPagosPayment, setClaroPagosPayment] = useState([])
  const [cardConfirmCVV, setCardConfirmCVV] = useState('')
  const [creditCardNumber, setCreditCardNumber] = useState('')
  const [creditCardNumberError, setCreditCardNumberError] = useState(false)
  const [yearError, setYearError] = useState(false)
  const [monthError, setMonthError] = useState(false)
  const [cvvError, setCvvError] = useState(false)
  const [isTelmexPayment, setIsTelmexPayment] = useState(false)
  const [maskedCVV, setMaskedCVV] = useState('')
  const [areaCode, setAreaCode] = useState('')
  const [pinCodeError, setPinCodeError] = useState('')
  const [hubfacturaText, setHubfacturaText] = useState('')

  const apilanguage = translations?.language?.[region]
  const claroPagosGateConfig =
    apaMetaData?.claropagos_configuration &&
    JSON?.parse(apaMetaData?.claropagos_configuration)?.[region]?.request
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const promoCodeResponse = useSelector(
    state => state?.settingsReducer?.addPromoCode
  )
  const [showPopup, setShowPopup] = useState(false)
  const [fibraLineupValue, setFibraLineupValue] = useState('')
  const [fibraLineServiceId, setFibraLineServiceId] = useState('')
  const focusedInputBoxIndexRef = useRef(0)

  const creditCardRegex =
    /^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|2(?:2[2-9][0-9]{2}|[3-6][0-9]{3}|7[01][0-9]{2}|720[0-9]{2})[0-9]{12})$/
  const numberRegex = /^\d+$/
  const visaRegex = /^4[0-9]{0,15}$/
  const masterCardRegex = /^5[1-5][0-9]{0,14}$/

  const paymentProcessLoading = useSelector(
    state => state?.settingsReducer?.isLoading
  )
  const fibraPaymentResponse = useSelector(
    state => state?.settingsReducer?.addFibraLinePayments
  )
  const fibraPaymentsConfirm = useSelector(
    state => state?.settingsReducer?.fibraPaymentsConfirm
  )
  const workflowResponse = useSelector(
    state => state?.settingsReducer?.paywayWorkFlow?.response
  )
  const mobilePaymentResponse = useSelector(
    state => state?.settingsReducer?.addPayments
  )
  const mobileOtpResponse = useSelector(
    state => state?.settingsReducer?.paymentsConfirm
  )
  const telcelAddPayment = useSelector(
    state => state?.settingsReducer?.telcelPayment
  )
  const telcelOtpData = useSelector(state => state?.settingsReducer?.telcelOtp)
  const getCustomerDetails = useSelector(
    state => state?.settingsReducer?.getClientDetails
  )
  const getCreditCardsData = useSelector(
    state => state?.settingsReducer?.getCardDetails
  )
  const addedCreditCardDetails = useSelector(
    state => state?.settingsReducer?.addCardDetails
  )
  const addClientDetails = useSelector(
    state => state?.settingsReducer?.addClientDetails
  )
  const updatedCardDetails = useSelector(
    state => state?.settingsReducer?.updateCardDetails
  )
  const creditCardPaymentData = useSelector(
    state => state?.settingsReducer?.creditCardPaymentData
  )
  const paymentError = useSelector(state => state?.settingsReducer?.error)

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009 || keycode === 8) {
      handleGoPreviousPage()
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || keycode == 89) {
      handleGoPreviousPage()
    }
  }

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
        numbercode: tizenNumberHandler()
      }
      handlesamsungkey(codes, keycode)
      handleRemoteNumberInput(event, codes)
    } else {
      handleLgkey(keycode)
      handleRemoteNumberInput(event)
    }
  }, [])

  useEffect(() => {
    pushScreenViewEvent({screenName:'add_payment_screen', screenData: userDetails, prevScreenName: 'payments_method'})
    document.addEventListener('keyup', keyPressFunc)
    // document.addEventListener('keyup', handleRemoteNumberInput)
    // keeping this line for reference just in case if needed
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
      // document.removeEventListener('keyup', handleRemoteNumberInput)
      dispatch(clearPromoCode())
      setErrMsg('')
      dispatch(clearPaymentError())
      dispatch(getClearAllPaymentsData())
    }
  }, [])

  useEffect(() => {
    if (checkoutInputValue) {
      numberRegex.test(checkoutInputValue)
        ? setMobileNumberError('')
        : setMobileNumberError(
            truncateText('form_tooltip_onlyNum_label_validation', 70)
          )
      checkoutInputValue?.length == 0 && setErrMsg('')
      checkoutInputValue?.length == 0 && setMobileNumberError('')
    } else if (creditCardNumber) {
      !creditCardRegex.test(creditCardNumber) &&
        creditCardNumber?.length > 15 &&
        setCreditCardNumberError(
          truncateText('form_tooltip_invalidPan_label_validation', 70)
        )
      !numberRegex.test(creditCardNumber) &&
        creditCardNumber?.length > 0 &&
        setCreditCardNumberError(
          truncateText('form_tooltip_onlyNum_label_validation', 70)
        )
    } else if (cardCVV) {
      !numberRegex.test(cardCVV) && cardCVV?.length > 0
        ? setCvvError(truncateText('form_tooltip_onlyNum_label_validation', 70))
        : setCvvError('')
      cardCVV?.length == 0 && setErrMsg('')
      cardCVV?.length == 0 &&
        setCvvError(truncateText('form_tooltip_empty_label_validation', 70))
    } else if (cardConfirmCVV) {
      !numberRegex.test(cardConfirmCVV) && cardConfirmCVV?.length > 0
        ? setCvvError(truncateText('form_tooltip_onlyNum_label_validation', 70))
        : setCvvError('')
      cardConfirmCVV?.length == 0 &&
        setCvvError(truncateText('form_tooltip_empty_label_validation', 70))
      cardConfirmCVV?.length == 0 && setErrMsg('')
    } else if (mobileNumber) {
      numberRegex.test(mobileNumber)
        ? setMobileNumberError('')
        : setMobileNumberError(
            truncateText('form_tooltip_onlyNum_label_validation', 70)
          )
      mobileNumber?.length == 0 && setMobileNumberError('')
    } else if (pinCode) {
      !numberRegex.test(pinCode)
        ? setPinCodeError(
            truncateText('form_tooltip_onlyNum_label_validation', 70)
          )
        : setPinCodeError('')
      pinCode?.length == 0 && setErrMsg('')
    }
  }, [
    checkoutInputValue,
    creditCardNumber,
    cardCVV,
    cardConfirmCVV,
    mobileNumber,
    pinCode
  ])

  useEffect(() => {
    isCustomerDataAvailable &&
      !state?.confirmCvv &&
      dispatch(
        addCreditCard({
          url: claroPagosGateConfig?.server_url,
          clientId:
            getCustomerDetails?.data?.cliente?.id ??
            addClientDetails?.data?.cliente?.id,
          headerToken:
            claroPagosGateConfig?.create_client?.header?.authorization,
          firstname: cardName,
          cardNumber: creditCardNumber,
          cvv2: cardCVV,
          expiryMonth: monthValue,
          expiryYear: yearValue,
          lineal: addressLine,
          ciudad: areaValue,
          cp: pinCode,
          mobileNumber: parseInt(mobileNumber, 10),
          estado: areaCode,
          pais: areaCode,
          isDefault: !state?.selectedPaymentMethodName,
          singleUse: true
        })
      )
  }, [isCustomerDataAvailable])

  useEffect(() => {
    if (workflowResponse && Object.keys(workflowResponse)?.length > 0) {
      let workFlowPaymentMethods =
        workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
      let claroPagos = workFlowPaymentMethods?.filter(item => {
        return item?.gateway == 'claropagosgate'
      })
      setClaroPagosPayment(claroPagos)
      let hubfacturaPayment = workFlowPaymentMethods?.filter(item => {
        return item?.gateway == 'hubfacturafijagate'
      })
      setHubfacturaText(hubfacturaPayment?.[0]?.gatewaytext)
    }
  }, [workflowResponse])

  useEffect(() => {
    isCreditAddressPage && setMobileNumber('')
  }, [isCreditAddressPage])

  useEffect(() => {
    claroPagosPayment?.length > 0 &&
      (confirmPayment || updatedCardDetails?.data?.status == 'success') &&
      dispatch(
        creditCardPayment({
          apiUrl: claroPagosPayment?.[0]?.buyLink,
          userToken: userDetails?.user_token,
          payway: claroPagosPayment?.[0]?.gateway,
          userId: userDetails?.user_id,
          buyToken: claroPagosPayment?.[0]?.buyToken,
          clientId:
            addClientDetails?.data?.cliente?.id ??
            getCustomerDetails?.data?.cliente?.id ??
            claroPagosPayment?.[0]?.paymentMethodData?.account,
          cardToken:
            addedCreditCardDetails?.data?.tarjeta?.token ??
            claroPagosPayment?.[0]?.paymentMethodData?.cardsData?.[0]?.token,
          cardType:
            addedCreditCardDetails?.data?.tarjeta?.marca == 'visa' ? 1 : 2
        })
      )
  }, [confirmPayment, updatedCardDetails])

  useEffect(() => {
    creditCardPaymentData &&
      Object.keys(creditCardPaymentData)?.length > 0 &&
      handleNavigate(creditCardPaymentData)
  }, [creditCardPaymentData])

  useEffect(() => {
    addClientDetails &&
      Object.keys(addClientDetails)?.length > 0 &&
      setIsCustomerDataAvailable(true)
  }, [addClientDetails])

  useEffect(() => {
    addedCreditCardDetails &&
      Object.keys(addedCreditCardDetails)?.length > 0 &&
      !state?.confirmCvv &&
      setConfirmPayment(true)
  }, [addedCreditCardDetails])

  useEffect(() => {
    updatedCardDetails &&
      Object.keys(updatedCardDetails)?.length > 0 &&
      setConfirmPayment(true)
  }, [updatedCardDetails])

  useEffect(() => {
    isOtpPage && document.getElementById('Key_0')?.focus()
  }, [isOtpPage])

  useEffect(() => {
    mobilePaymentResponse?.response?.sms_response && setIsOtpPage(true)
    mobilePaymentResponse?.response?.sms_response && setCheckoutInputValue('')
    telcelAddPayment?.errors?.access_pin_token && setIsOtpPage(true)
    telcelAddPayment?.errors?.access_pin_token && setCheckoutInputValue('')
  }, [mobilePaymentResponse, telcelAddPayment])

  useEffect(() => {
    if (cardCVV) {
      setMaskedCVV(handleMaskCVV(cardCVV))
    } else {
      setMaskedCVV(handleMaskCVV(cardConfirmCVV))
    }
  }, [cardCVV, cardConfirmCVV])

  const handleMaskedCreditCardNumber = data => {
    if (data) {
      const maskedNumber = data?.slice(0, -12).replace(/\d/g, '*')
      const visibleNumber = data?.slice(-4)
      return `${maskedNumber}${visibleNumber}`
    }
  }

  const handleGoPreviousPage = e => {
    let creditCardPopupDataDiv = document.getElementById(
      'creditCardPopupDataDiv'
    )
    const addressPageDiv = document.getElementById('addressLine')
    dispatch(clearPromoCode())
    dispatch(clearFibraLineData())
    dispatch(clearPaymentError())
    setErrMsg('')
    isCreditAddressPage || addressPageDiv
      ? (setIsCreditAddressPage(false),
        setFocusedInputBoxIndex(0),
        (focusedInputBoxIndexRef.current = 0))
      : creditCardPopupDataDiv
      ? setIsCreditPopupPage(false)
      : (state?.confirmCvv && !creditCardPopupDataDiv) ||
        workflowResponse?.selectedPaymentMethod == 'promogate'
      ? navigate(
          '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          {
            state: {
              previousPageName: state?.previousPageName,
              vodData: state?.vodData,
              dataId: state?.dataId ?? state?.data,
              pageName: state?.pageName,
              subscribeInfoData: state?.subscribeInfoData,
              returnPage: state?.returnPage,
              sendIndex: state?.sendIndex,
              previousPage: state?.previousPage,
              groupId: state?.groupId
            }
          }
        )
      : navigate('/my-settings/my-Accounts/manage-payments/payments-method', {
          state: {
            previousPageName: state?.previousPageName,
            vodData: state?.vodData,
            dataId: state?.dataId ?? state?.data,
            pageName: state?.pageName,
            subscribeInfoData: state?.subscribeInfoData,
            returnPage: state?.returnPage,
            sendIndex: state?.sendIndex,
            previousPage: state?.previousPage,
            groupId: state?.groupId,
            selectedPaymentMethodName: state?.paymentMethod
          }
        })
  }

  const handleMaskCVV = data => {
    let value = ''
    data?.split('')?.forEach(() => {
      value += '*'
    })
    return value
  }

  const handleSubscription = paymentType => {
    switch (paymentType) {
      case 'promogate':
        return submitPromoCode()
      case 'hubgate':
        return isOtpPage ? confirmOtp() : validateMobilePayment()
      case 'telmexmexicogate':
        return validateTelmexPayment()
      case 'hubfacturafijagate':
        return fibraLineServiceId
          ? confirmServiceId()
          : validateLandlinePayment()
      case 'claropagosgate':
        return !isCreditAddressPage && !state?.confirmCvv
          ? handleCreditAddressPage()
          : handleCreditCardPayment()
      default:
        break
    }
  }

  const submitPromoCode = () => {
    let workFlowPaymentMethods =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
    let promoCodePayment = workFlowPaymentMethods?.filter(item => {
      return item?.gateway == 'promogate'
    })
    setPaywayText(promoCodePayment?.[0]?.gatewaytext)
    dispatch(
      getAddPromoCode({
        apiUrl: promoCodePayment?.[0]?.buyLink,
        userToken: userDetails?.user_token,
        promoCode: checkoutInputValue
      })
    )
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleNavigate = paymentResponse => {
    navigate(
      '/my-settings/my-Accounts/manage-payments/payment/finish-subscription',
      {
        state: {
          endDay: paymentResponse?.response?.end_date.day,
          endMonth: paymentResponse?.response?.end_date.month,
          endYear: paymentResponse?.response?.end_date.year,
          paymentAccount: paymentResponse?.response?.paymentMethodData?.account,
          vodData: state?.vodData,
          pageName: state?.pageName,
          dataId: state?.dataId,
          paymentGateway: paywayText,
          groupId: state?.groupId
        }
      }
    )
  }

  useEffect(() => {
    switch (true) {
      case telcelAddPayment?.errors?.code != 'error_pin':
        setErrMsg(truncateText(telcelAddPayment?.errors?.code, 100))
        break
      case !!mobileOtpResponse?.errors?.length > 0:
        setErrMsg(truncateText(mobileOtpResponse?.errors?.code, 100))
        break
      case !!mobilePaymentResponse?.errors?.length > 0:
        setErrMsg(truncateText(mobilePaymentResponse?.errors?.[0]?.code, 100))
        break
      case !!(fibraPaymentResponse?.errors?.length > 0):
        setErrMsg(truncateText(fibraPaymentResponse?.errors?.[0]?.code, 100))
        break
      case !!telcelOtpData?.response?.paymentMethodData?.account:
        handleNavigate(telcelOtpData)
        break
      case !!mobileOtpResponse?.response?.paymentMethodData?.account:
        handleNavigate(mobileOtpResponse)
        break
      case !!fibraPaymentResponse?.response?.paymentMethodData?.account:
        handleNavigate(fibraPaymentResponse)
        break
      case !!mobilePaymentResponse?.response?.paymentMethodData?.account:
        handleNavigate(mobilePaymentResponse)
        break
      case !!fibraPaymentsConfirm?.response?.paymentMethodData?.account:
        handleNavigate(fibraPaymentsConfirm)
        break
      default:
        null
        break
    }
  }, [
    mobilePaymentResponse,
    fibraPaymentResponse,
    mobileOtpResponse,
    fibraPaymentsConfirm,
    telcelAddPayment,
    telcelOtpData
  ])

  useEffect(() => {
    if (promoCodeResponse?.msg == 'OK') handleNavigate(promoCodeResponse)
    else if (!!promoCodeResponse?.errors)
      setErrMsg(truncateText(promoCodeResponse?.errors?.code, 100))
  }, [promoCodeResponse])

  useEffect(() => {
    paymentError?.errors?.length > 0 && state?.confirmCvv
      ? setCvvError(
          truncateText(paymentError?.errors?.[0]?.code, 100) ??
            paymentError?.error?.message
        )
      : setErrMsg(truncateText(paymentError?.errors?.[0]?.code, 100))
  }, [paymentError])

  const tizenNumberHandler = () => {
    const numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
    tizen.tvinputdevice.registerKeyBatch(numbers)
    return numbers?.map(number => tizen.tvinputdevice.getKey(number).code)
  }

  const handleRemoteNumberInput = (event, codes = {}) => {
    if (
      (event?.keyCode >= 48 && event?.keyCode <= 57) ||
      codes?.numbercode?.includes(event?.keyCode)
    ) {
      if (state?.paymentMethod != 'claropagosgate') {
        setCheckoutInputValue(prevValue => {
          if (prevValue?.length <= 15) {
            return prevValue + event?.key
          } else {
            return prevValue
          }
        })
      } else {
        focusedInputBoxIndexRef.current == 0
          ? setCreditCardNumber(prevValue => {
              if (prevValue?.length <= 15) {
                return prevValue + event?.key
              } else {
                return prevValue
              }
            })
          : focusedInputBoxIndexRef.current == 2
          ? state?.confirmCvv
            ? setCardConfirmCVV(prevValue => {
                if (prevValue?.length <= 2) {
                  return prevValue + event?.key
                } else {
                  return prevValue
                }
              })
            : setCardCVV(prevValue => {
                if (prevValue?.length <= 2) {
                  return prevValue + event?.key
                } else {
                  return prevValue
                }
              })
          : focusedInputBoxIndexRef.current == 5
          ? setPinCode(prevValue => {
              if (prevValue?.length <= 7) {
                return prevValue + event?.key
              } else {
                return prevValue
              }
            })
          : focusedInputBoxIndexRef.current == 6
          ? setMobileNumber(prevValue => {
              if (prevValue?.length <= 14) {
                return prevValue + event?.key
              } else {
                return prevValue
              }
            })
          : ''
      }
    }
  }

  const handleInputChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    numberRegex.test(inputVal)
      ? setMobileNumberError('')
      : setMobileNumberError(
          truncateText('form_tooltip_onlyNum_label_validation', 70)
        )
    inputVal?.length == 0 && setMobileNumberError('')
    inputVal = inputVal.replace(/^\s+-/, '').replace(/[^\w\s-.@_+ñ]/gi, '')
    if (state?.paymentMethod == 'hubgate' && region == 'mexico') {
      inputVal?.length <= 10 && setCheckoutInputValue(inputVal)
    } else if (state?.paymentMethod == 'hubgate') {
      setCheckoutInputValue(inputVal)
    } else if (inputVal.length <= 25) {
      setCheckoutInputValue(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleCreditCardNumber = e => {
    setKeyboardFocus(true)
    let inputVal = e
    setCreditCardNumberError('')
    inputVal?.length == 0 && setErrMsg('')
    inputVal?.length == 0 &&
      setCreditCardNumberError(
        truncateText('form_tooltip_empty_label_validation', 70)
      )
    inputVal = inputVal.replace(/^\s+-/, '').replace(/[^\w\s-.@_+ñ]/gi, '')
    !creditCardRegex.test(inputVal) &&
      inputVal?.length > 15 &&
      setCreditCardNumberError(
        truncateText('form_tooltip_invalidPan_label_validation', 70)
      )
    !numberRegex.test(inputVal) &&
      inputVal?.length > 0 &&
      setCreditCardNumberError(
        truncateText('form_tooltip_onlyNum_label_validation', 70)
      )
    setCreditCardNumber(inputVal)
    dispatch(setlastTypedText(inputVal))
  }

  const handleCardNameChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    inputVal = inputVal.replace(/^\s+-/, '').replace(/[^\w\s-.@_+ñ]/gi, '')
    if (inputVal.length <= 25) {
      setCardName(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleCVVChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    !numberRegex.test(inputVal) && inputVal?.length > 0
      ? setCvvError(truncateText('form_tooltip_onlyNum_label_validation', 70))
      : setCvvError('')
    inputVal?.length == 0 &&
      setCvvError(truncateText('form_tooltip_empty_label_validation', 70))
    if (inputVal?.length <= 3) {
      setCardCVV(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleConfirmCVVChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    !numberRegex.test(inputVal) && inputVal?.length > 0
      ? setCvvError(truncateText('form_tooltip_onlyNum_label_validation', 70))
      : setCvvError('')
    inputVal?.length == 0 &&
      setCvvError(truncateText('form_tooltip_empty_label_validation', 70))
    if (inputVal?.length <= 3) {
      setCardConfirmCVV(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleAddressLineChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')

    inputVal = inputVal.replace(/^\s+-/, '').replace(/[^\w\s-.@_+ñ]/gi, '')
    if (inputVal.length <= 25) {
      setAddressLine(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleCityLineChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    if (inputVal.length <= 25) {
      setCityLine(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleMobileNumberChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    numberRegex.test(inputVal)
      ? setMobileNumberError('')
      : setMobileNumberError(
          truncateText('form_tooltip_onlyNum_label_validation', 70)
        )
    inputVal?.length == 0 && setMobileNumberError('')
    if (inputVal.length <= 15) {
      setMobileNumber(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handlePinCodeChange = e => {
    setKeyboardFocus(true)
    let inputVal = e
    inputVal?.length == 0 && setErrMsg('')
    inputVal = inputVal.replace(/^\s+-/, '').replace(/[^\w\s-.@_+ñ]/gi, '')
    !numberRegex.test(inputVal)
      ? setPinCodeError(
          truncateText('form_tooltip_onlyNum_label_validation', 70)
        )
      : setPinCodeError('')
    inputVal?.length == 0 && setPinCodeError('')
    if (inputVal.length <= 8) {
      setPinCode(inputVal)
      dispatch(setlastTypedText(inputVal))
    }
  }

  const handleFocusedInputIndex = (e, value) => {
    e.preventDefault()
    setFocusedInputBoxIndex(value)
    focusedInputBoxIndexRef.current = value
  }

  const handleCreditCardFocus = (e, param) => {
    e.preventDefault()
    if (param) {
      handleFocusedInputIndex(e, 0)
      document
        .getElementById('checkOutInput')
        ?.classList?.remove('checkout-input-error')
    } else {
      creditCardNumberError &&
        document
          .getElementById('checkOutInput')
          ?.classList?.add('checkout-input-error')
    }
    handleFocusedInputIndex(e, 0)
  }

  const handleOnCloseCreditPopup = () => {
    setIsCreditPopupPage(false)
  }

  const handleCreditCardPopup = (e, type) => {
    e.preventDefault()
    setIsCreditPopupPage(true)
    setCreditPopupType(type)
    setMonthError('')
    setYearError('')
    setCvvError('')
  }

  const handleCreditAddressPage = () => {
    if (creditCardNumber?.length == 0) {
      setCreditCardNumberError(
        truncateText('form_tooltip_empty_label_validation', 70)
      )
    } else if (creditCardNumber?.length < 13 || creditCardNumber?.length > 18) {
      setCreditCardNumberError(
        truncateText('form_tooltip_lengthPan_label_validation', 70)
      )
    } else if (!creditCardRegex.test(creditCardNumber)) {
      setCreditCardNumberError(
        truncateText('form_tooltip_invalidPan_label_validation', 70)
      )
    } else if (!monthValue) {
      setMonthError(truncateText('form_tooltip_empty_label_validation', 70))
    } else if (!yearValue) {
      setYearError(truncateText('form_tooltip_empty_label_validation', 70))
    } else if (!cardCVV) {
      setCvvError(truncateText('form_tooltip_empty_label_validation', 70))
    } else {
      setIsCreditAddressPage(true)
      setFocusedInputBoxIndex(3)
      focusedInputBoxIndexRef.current = 3
      document.getElementById('Key_0')?.focus()
    }
  }

  const handleCreditCardPayment = () => {
    if (state?.confirmCvv) {
      dispatch(
        updateCreditCard({
          url: claroPagosGateConfig?.server_url,
          headerToken:
            claroPagosGateConfig?.create_client?.header?.authorization,
          cardToken:
            addedCreditCardDetails?.data?.tarjeta?.token ??
            claroPagosPayment?.[0]?.paymentMethodData?.cardsData?.[0]?.token,
          updateFields: {
            cvv2: cardConfirmCVV
          }
        })
      )
    } else if (Object.keys(getCustomerDetails)?.length > 0) {
      setIsCustomerDataAvailable(true)
    } else {
      dispatch(
        addClient({
          url: claroPagosGateConfig?.server_url,
          headerToken:
            claroPagosGateConfig?.create_client?.header?.authorization,
          firstname: cardName,
          lastname: userDetails?.lastname,
          userId: userDetails?.user_id,
          email: userDetails?.email,
          lineal: addressLine,
          ciudad: areaValue,
          cp: pinCode
        })
      )
    }
  }

  const handleInputForm = paymentType => {
    // add payment method flow name for paymentType i.e. promo etc.
    switch (paymentType) {
      case 'promogate':
        return displayPromogate()
      case 'hubgate':
        return displayHubgate()
      case 'telmexmexicogate':
        setIsTelmexPayment(true)
        break
      case 'hubfacturafijagate':
        return displayHubFactura()
      case 'claropagosgate':
        return displayCreditCard()
      default:
        break
    }
  }

  const validateMobilePayment = () => {
    let workFlowPaymentMethods =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
    let hubgatePayment = workFlowPaymentMethods?.filter(item => {
      return item?.gateway == 'hubgate'
    })
    setAccountNumber(checkoutInputValue)
    setPaywayText(hubgatePayment?.[0]?.gatewaytext)
    hubgatePayment?.[0]?.gatewaytext == 'Telcel'
      ? dispatch(
          addTelcelPayment({
            apiUrl: hubgatePayment?.[0]?.buyLink,
            userToken: userDetails?.user_token,
            payway: hubgatePayment?.[0]?.gateway,
            userId: userDetails?.parent_id,
            number: checkoutInputValue ?? accountNumber
          })
        )
      : dispatch(
          getAddPayments({
            apiUrl: hubgatePayment?.[0]?.buyLink,
            userToken: userDetails?.user_token,
            payway: hubgatePayment?.[0]?.gateway,
            userId: userDetails?.parent_id,
            buyToken: hubgatePayment?.[0]?.buyToken,
            number: checkoutInputValue
          })
        )
  }

  const validateLandlinePayment = () => {
    let workFlowPaymentMethods =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
    let hubfacturaPayment = workFlowPaymentMethods?.filter(item => {
      return item?.gateway == 'hubfacturafijagate'
    })
    setAccountNumber(checkoutInputValue)
    setPaywayText(hubfacturaPayment?.[0]?.gatewaytext)
    dispatch(
      getAddFibraLinePayments({
        apiUrl: hubfacturaPayment?.[0]?.buyLink,
        userToken: userDetails?.user_token,
        payway: hubfacturaPayment?.[0]?.gateway,
        userId: userDetails?.parent_id,
        buyToken: hubfacturaPayment?.[0]?.buyToken,
        inputValue: checkoutInputValue,
        email: userDetails?.email
      })
    )
  }

  const validateTelmexPayment = () => {
    let workFlowPaymentMethods =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
    let telmexPayment = workFlowPaymentMethods?.filter(item => {
      return item?.gateway == 'telmexmexicogate'
    })
    dispatch(
      getAddFibraLinePayments({
        apiUrl: telmexPayment?.[0]?.buyLink,
        userToken: userDetails?.user_token,
        payway: telmexPayment?.[0]?.gateway,
        userId: userDetails?.parent_id,
        buyToken: telmexPayment?.[0]?.buyToken,
        inputValue: checkoutInputValue,
        email: userDetails?.email
      })
    )
  }

  const confirmServiceId = () => {
    let workFlowPaymentMethods =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
    let hubfacturaPayment = workFlowPaymentMethods?.filter(item => {
      return item?.gateway == 'hubfacturafijagate' || 'hubfijogate'
    })

    //kept this code for future action
    dispatch(
      getFibraPaymentsConfirm({
        apiUrl: hubfacturaPayment?.[0]?.buyLink,
        userToken: userDetails?.user_token,
        payway: hubfacturaPayment?.[0]?.gateway,
        userId: userDetails?.parent_id,
        buyToken: hubfacturaPayment?.[0]?.buyToken,
        serviceId: fibraLineServiceId,
        number: accountNumber,
        userId: userDetails?.parent_id
      })
    )
  }

  const confirmOtp = () => {
    let workFlowPaymentMethods =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
    let hubgatePayment = workFlowPaymentMethods?.filter(item => {
      return item?.gateway == 'hubgate'
    })
    hubgatePayment?.[0]?.gatewaytext == 'Telcel'
      ? dispatch(
          confirmTelcel({
            apiUrl: hubgatePayment?.[0]?.buyLink,
            userToken: userDetails?.user_token,
            payway: hubgatePayment?.[0]?.gateway,
            userId: userDetails?.parent_id,
            otp: checkoutInputValue,
            number: accountNumber,
            userId: userDetails?.parent_id
          })
        )
      : dispatch(
          getPaymentsConfirm({
            apiUrl: hubgatePayment?.[0]?.buyLink,
            userToken: userDetails?.user_token,
            payway: hubgatePayment?.[0]?.gateway,
            userId: userDetails?.parent_id,
            buyToken: hubgatePayment?.[0]?.buyToken,
            otp: checkoutInputValue,
            accessPinToken: mobilePaymentResponse?.response?.access_pin_token,
            number: accountNumber,
            userId: userDetails?.parent_id
          })
        )
  }

  const displayTelmex = () => {
    pushScreenViewEvent({screenName:'telmex_payment_info_screen', screenData: userDetails, prevScreenName: 'payments_method'})
    return (
      <div className="telmex-payment-error-container">
        <LazyLoadImage
          className="claro-image-telmex"
          src={
            apaAssetsImages?.MDP_Telmex_Alerta_Icono ||
            'images/Telmex_Alert.png'
          }
        />
        <span className="telmex-error-text">
          <span>{truncateText('MDP_Telmex_Alerta_Texto1', 65)}</span>
          <span>{truncateText('MDP_Telmex_Alerta_Texto2', 65)}</span>
          <span>{truncateText('MDP_Telmex_Alerta_Texto3', 50)}</span>
        </span>
        <button
          className="telmex-acceptar-button focusable"
          onClick={() => handleGoPreviousPage()}
          autoFocus={true}
        >
          <span className="telmex-acceptar-text">
            {truncateText('MDP_Telmex_Alerta_TextoBotonPrimario', 10)}
          </span>
        </button>
      </div>
    )
  }

  const displayHubFactura = () => {
    return (
      <>
        <span className="checkout-title-1">{hubfacturaText}</span>
        <button
          className="checkout-input focusable"
          id="checkOutInput"
          tabIndex={1}
        >
          <span className="checkout-cursor">
            {checkoutInputValue?.length == 0 && '|'}
          </span>
          <input
            className="input-text"
            autoComplete="off"
            name="search"
            type="text"
            value={checkoutInputValue}
            onChange={e => {
              handleInputChange(e?.target?.value)
            }}
            inputMode="none"
            data-testid="inputTest"
            disabled
            placeholder={hubfacturaPlaceholder}
          />
        </button>
        {!paymentProcessLoading && (
          <div className="error-box">
            <div
              id="promoCodeError"
              className={`err-text ${errMsg ? 'promocode-error' : 'invisible'}`}
            >
              {errMsg}
            </div>
          </div>
        )}
        {fibraPaymentResponse?.response?.account_data && (
          <>
            <span className="checkout-title-2">
              {truncateText(
                `${state?.paymentMethod}_access_subtitle_label`,
                40
              )}
            </span>
            <button
              className="checkout-input-2 focusable"
              id="checkOutInput"
              tabIndex={1}
              onClick={handleFibraPopup}
            >
              <input
                className="input-text"
                autoComplete="off"
                placeholder={truncateText(
                  'payment_hubfacturafijagate_hubcorp_combo_placeholder',
                  70
                )}
                name="search"
                type="text"
                value={fibraLineupValue}
                inputMode="none"
                data-testid="inputTest"
                disabled
              />
            </button>
          </>
        )}
      </>
    )
  }
  const handleFibraPopup = () => {
    setShowPopup(!showPopup)
  }

  const updateFibraLineupValue = value => {
    setFibraLineupValue(value?.description)
    setFibraLineServiceId(value?.service_id)
  }

  const handleConfirmCvvClick = event => {
    document.getElementById('Key_0')?.focus()
    event.preventDefault()
  }

  const displayPromogate = () => {
    return (
      <>
        <span className="checkout-title-1">
          {truncateText('promogate_access_title_label', 40)}
        </span>
        <button
          className="checkout-input checkout-input-active focusable"
          id="checkOutInput"
          tabIndex={1}
        >
          <span className="checkout-cursor">
            {checkoutInputValue?.length == 0 && '|'}
          </span>
          <input
            className="input-text"
            autoComplete="off"
            name="search"
            type="text"
            value={checkoutInputValue}
            onChange={e => {
              handleInputChange(e?.target?.value)
            }}
            inputMode="none"
            data-testid="inputTest"
            disabled
            placeholder={truncateText(
              'promogate_access_placeHolder_textfield',
              40
            )}
          />
        </button>
        {!paymentProcessLoading && (
          <div className="error-box">
            <div
              id="promoCodeError"
              className={`err-text ${errMsg ? 'promocode-error' : 'invisible'}`}
            >
              {errMsg}
            </div>
          </div>
        )}
      </>
    )
  }

  const displayHubgate = () => {
    return (
      <>
        <span className="checkout-title-1 hubgate-width">
          {region == 'mexico' &&
            truncateText(
              !isOtpPage
                ? 'ticket_subscription_hubgate_label'
                : `${state?.paymentMethod}_validationCode_subtitle`,
              90
            )}
          {region != 'mexico' &&
            truncateText(
              !isOtpPage
                ? `${state?.paymentMethod}_number_title_label`
                : `${state?.paymentMethod}_validationCode_subtitle`,
              90
            )}
        </span>
        {isOtpPage ? (
          displayOtpInput()
        ) : (
          <>
            <button
              className="checkout-input checkout-input-active focusable"
              id="checkOutInput"
              tabIndex={1}
            >
              <span className="checkout-cursor">
                {checkoutInputValue?.length == 0 && '|'}
              </span>
              <input
                className="input-text"
                autoComplete="off"
                name="search"
                type="text"
                value={checkoutInputValue}
                onChange={e => {
                  handleInputChange(e?.target?.value)
                }}
                inputMode="none"
                data-testid="inputTest"
                disabled
                placeholder={truncateText(
                  region == 'mexico'
                    ? `MDP_Formulario_SingleInput_Input_Texto_${state?.paymentMethod}`
                    : `${state?.paymentMethod}_phoneNumber_placeHolder_textfield`,
                  50
                )}
              />
            </button>
            {!paymentProcessLoading && (
              <div className="error-box">
                <div
                  id="hubGateError"
                  className={`err-text ${
                    errMsg || mobileNumberError ? 'hubgate-error' : 'invisible'
                  }`}
                >
                  {errMsg ? errMsg : mobileNumberError ? mobileNumberError : ''}
                </div>
              </div>
            )}
          </>
        )}
      </>
    )
  }

  const displayOtpInput = () => {
    return (
      <div className="checkout-otp-container">
        <button
          className={`${
            isOtpPage ? 'checkout-hubgate-otp' : 'checkout-input'
          } focusable`}
          id="checkOutInput"
          tabIndex={1}
        >
          <span className="checkout-cursor">
            {checkoutInputValue?.length == 0 && '|'}
          </span>
          <input
            className="input-text"
            autoComplete="off"
            name="search"
            type="text"
            value={checkoutInputValue}
            onChange={e => {
              handleInputChange(e?.target?.value)
            }}
            inputMode="none"
            data-testid="inputTest"
            disabled
          />
        </button>
        <button
          className="checkout-confirm-otp focusable"
          onClick={() => validateMobilePayment()}
        >
          <span className="checkout-confirm-otp-text">
            {truncateText(
              region == 'mexico'
                ? `medio_de_pago_reenviar`
                : `${state?.paymentMethod}_validationCode_sendPIN_button_sendPIN`,
              17
            )}
          </span>
        </button>
        {!paymentProcessLoading && (
          <div className="error-box-otp">
            <div
              id="hubGateErrorOtp"
              className={`err-text ${
                errMsg || mobileNumberError ? 'hubgate-error-otp' : 'invisible'
              }`}
            >
              {errMsg ? errMsg : mobileNumberError ? mobileNumberError : ''}
            </div>
          </div>
        )}
      </div>
    )
  }

  const displayCreditCard = () => {
    return state?.confirmCvv ? (
      <>
        <span className="confirm-cvv-title">
          {truncateText('MDP_AgregarTarjeta_Confirmacion_TextoTitulo', 40)}
        </span>
        <div className="confirm-cvv-card-details">
          <LazyLoadImage
            className="confirm-cvv-card-image"
            src={
              claroPagosPayment?.[0]?.paymentMethodData?.cardsData?.[0]
                ?.card_type == 1
                ? apaAssetsImages?.MDP_AgregarTarjeta_LogoTarjeta_Visa ||
                  'images/visa.png'
                : apaAssetsImages?.MDP_AgregarTarjeta_LogoTarjeta_Mastercard ||
                  'images/mastercard.png'
            }
          />
          <span className="confirm-cvv-card-number">
            {handleMaskedCreditCardNumber(
              addedCreditCardDetails?.data?.tarjeta ||
                claroPagosPayment?.[0]?.paymentMethodData?.cardsData?.[0]?.pan
            )}
          </span>
        </div>
        <div className="confirm-cvv-container">
          <span className="confirm-cvv-text">
            {`${truncateText(
              'MDP_AgregarTarjeta_Confirmacion_Texto1',
              25
            )} ${truncateText('MDP_AgregarTarjeta_Confirmacion_Texto2', 30)}`}
          </span>
          <button
            className={`button-cvv ${
              cvvError
                ? 'button-error'
                : focusedInputBoxIndex == 2
                ? 'checkout-input-active'
                : ''
            } focusable`}
            id="cvvConfirmInput"
            tabIndex={1}
            onFocus={e => handleFocusedInputIndex(e, 2)}
            autoFocus={true}
            onClick={e => handleConfirmCvvClick(e)}
          >
            <input
              className="button-input-cvv focusable"
              autoComplete="off"
              name="cardCVV"
              id="creditCardCVVInput"
              type="text"
              value={maskedCVV}
              maxLength={3}
              placeholder={truncateText(
                'MDP_AgregarTarjeta_Confirmacion_Input_TextoCVV',
                5
              )}
              onChange={e => {
                handleConfirmCVVChange(e?.target?.value)
              }}
              inputMode="none"
              disabled
            />
          </button>
          {cvvError && (
            <div className="error-box " style={{ right: '0px' }}>
              <div
                id="promoCodeError"
                className={`err-text ${
                  cvvError
                    ? 'credit-card-error-cvv confirm-cvv-error'
                    : 'invisible'
                }`}
              >
                <span className="error-contents">{cvvError}</span>
              </div>
            </div>
          )}
        </div>
        <div className="disclaimer-wrapper">
          <span className="disclaimer-text">
            {truncateText(
              'MDP_AgregarTarjeta_Confirmacion_TextoNotaInformativa',
              31
            )}
          </span>
        </div>
      </>
    ) : (
      <>
        <span className="checkout-title-1">
          {truncateText('MDP_AgregarTarjeta_TextoTitulo', 40)}
        </span>
        {!isCreditAddressPage ? (
          <>
            <button
              className={`checkout-input ${
                creditCardNumberError
                  ? 'checkout-input-error'
                  : focusedInputBoxIndex == 0
                  ? 'checkout-input-active'
                  : 'checkout-input-inactive'
              } focusable`}
              id="checkOutInput"
              tabIndex={1}
              onFocus={e => handleCreditCardFocus(e, true)}
              onBlur={e => handleCreditCardFocus(e, false)}
            >
              <span className="checkout-cursor">
                {creditCardNumber?.length == 0 &&
                  focusedInputBoxIndex == 0 &&
                  '|'}
              </span>
              <input
                className="input-text"
                autoComplete="off"
                name="cardNumber"
                id="creditCardNumberInput"
                type="text"
                value={creditCardNumber}
                onChange={e => {
                  handleCreditCardNumber(e?.target?.value)
                }}
                // placeholder value is not available in node values.
                placeholder={
                  truncateText(
                    'MDP_AgregarTarjeta_Input_NoTarjeta_Texto',
                    35
                  ) || creditCardPlaceHolder
                }
                inputMode="none"
                data-testid="inputTest"
                disabled
              />
              {visaRegex.test(creditCardNumber) &&
                creditCardNumber?.length > 3 && (
                  <img
                    className="credit-img"
                    src={
                      apaAssetsImages?.MDP_AgregarTarjeta_LogoTarjeta_Visa ||
                      'images/visa.png'
                    }
                  />
                )}
              {masterCardRegex.test(creditCardNumber) &&
                creditCardNumber?.length > 3 && (
                  <img
                    className="credit-img"
                    src={
                      apaAssetsImages?.MDP_AgregarTarjeta_LogoTarjeta_Mastercard ||
                      'images/mastercard.png'
                    }
                  />
                )}
            </button>

            <div className="error-box">
              <div
                id="promoCodeError"
                style={{ marginTop: '100px' }}
                className={`err-text ${
                  creditCardNumberError ? 'credit-card-error-year' : 'invisible'
                }`}
              >
                <span className="error-contents">{creditCardNumberError}</span>
              </div>
            </div>

            <button
              className={`checkout-input ${
                focusedInputBoxIndex == 1
                  ? 'checkout-input-active'
                  : 'checkout-input-inactive'
              } margin-for-input focusable`}
              id="cardNameInput"
              tabIndex={1}
              onFocus={e => handleFocusedInputIndex(e, 1)}
            >
              <span className="checkout-cursor">
                {cardName?.length == 0 && focusedInputBoxIndex == 1 && '|'}
              </span>
              <input
                className="input-text focusable"
                autoComplete="off"
                name="cardName"
                id="creditCardNameInput"
                type="text"
                value={cardName}
                placeholder={truncateText(
                  'MDP_AgregarTarjeta_Input_Nombre_Texto',
                  35
                )}
                onChange={e => {
                  handleCardNameChange(e?.target?.value)
                }}
                inputMode="none"
                disabled
              />
            </button>
            <div className="card-data-sub-div margin-card-data-sub">
              <button
                className={`button button-margin ${
                  monthError ? 'button-error' : ''
                } focusable`}
                onClick={e => handleCreditCardPopup(e, 'month')}
                id="month-selector"
                onFocus={e => handleFocusedInputIndex(e, -1)}
                onBlur={e => handleFocusedInputIndex(e, 13)}
              >
                <span
                  className="button-contents"
                  style={{
                    color:
                      monthValue || focusedInputBoxIndex == -1
                        ? '#fff'
                        : ' #7f8086'
                  }}
                >
                  {monthValue
                    ? monthValue
                    : truncateText('MDP_AgregarTarjeta_Input_Fecha_Texto', 5)}
                </span>
                <img
                  className="chevron-down-img"
                  src="images/btn_chevron_down.png"
                />
              </button>

              <button
                className={`button button-margin ${
                  yearError ? 'button-error' : ''
                } focusable`}
                onClick={e => handleCreditCardPopup(e, 'year')}
                id="year-selector"
                onFocus={e => handleFocusedInputIndex(e, -2)}
              >
                <span
                  className="button-contents"
                  style={{
                    color:
                      yearValue || focusedInputBoxIndex == -2
                        ? '#fff'
                        : ' #7f8086'
                  }}
                >
                  {yearValue
                    ? yearValue
                    : truncateText('MDP_AgregarTarjeta_Input_Anio_Texto', 5)}
                </span>
                <img
                  className="chevron-down-img"
                  src="images/btn_chevron_down.png"
                />
              </button>
              <button
                className={`button-cvv ${cvvError ? 'button-error' : ''} ${
                  focusedInputBoxIndex == 2 ? 'button-active' : ''
                } focusable`}
                id="cvvInput"
                tabIndex={1}
                onFocus={e => {
                  handleFocusedInputIndex(e, 2)
                  document.getElementById('Key_0')?.focus()
                }}
              >
                <span className="checkout-cursor">
                  {cardCVV?.length == 0 && focusedInputBoxIndex == 2 && '|'}
                </span>
                <input
                  className="button-input-cvv focusable"
                  autoComplete="off"
                  name="cardCVV"
                  id="creditCardCVVInput"
                  type="text"
                  value={maskedCVV}
                  maxLength={3}
                  placeholder={truncateText(
                    'MDP_AgregarTarjeta_Input_CVV_Texto',
                    5
                  )}
                  onChange={e => {
                    handleCVVChange(e?.target?.value)
                  }}
                  inputMode="none"
                  disabled
                />
              </button>
            </div>
            {(monthError || true || yearError || cvvError) && (
              <div className="error-box">
                <div
                  id="promoCodeError"
                  className={`err-text ${
                    monthError
                      ? 'credit-card-error-month'
                      : yearError
                      ? 'credit-card-error-year'
                      : cvvError
                      ? 'credit-card-error-cvv'
                      : 'invisible'
                  }`}
                >
                  <span className="error-contents">
                    {monthError
                      ? monthError
                      : yearError
                      ? yearError
                      : cvvError && cvvError}
                  </span>
                </div>
              </div>
            )}
          </>
        ) : (
          <>
            <button
              className={`checkout-input ${
                focusedInputBoxIndex == 3
                  ? 'checkout-input-active'
                  : 'checkout-input-inactive'
              } focusable`}
              id="addressLine"
              tabIndex={1}
              onFocus={e => handleFocusedInputIndex(e, 3)}
            >
              <span className="checkout-cursor">
                {addressLine?.length == 0 && focusedInputBoxIndex == 3 && '|'}
              </span>
              <input
                className="input-text"
                autoComplete="off"
                name="addressLine"
                id="creditCardAddressInput"
                type="text"
                value={addressLine}
                onChange={e => {
                  handleAddressLineChange(e?.target?.value)
                }}
                placeholder={truncateText(
                  'MDP_AgregarTarjetaDatos_Input_Direccion_Texto',
                  40
                )}
                inputMode="none"
                data-testid="inputTest"
                disabled
              />
            </button>

            <button
              className={`checkout-input ${
                focusedInputBoxIndex == 4
                  ? 'checkout-input-active'
                  : 'checkout-input-inactive'
              } margin-for-input focusable`}
              id="cityLine"
              tabIndex={1}
              onFocus={e => handleFocusedInputIndex(e, 4)}
            >
              <span className="checkout-cursor">
                {cityLine?.length == 0 && focusedInputBoxIndex == 4 && '|'}
              </span>
              <input
                className="input-text"
                autoComplete="off"
                name="cityLine"
                id="creditCardCityInput"
                type="text"
                value={cityLine}
                onChange={e => {
                  handleCityLineChange(e?.target?.value)
                }}
                placeholder={truncateText(
                  'MDP_AgregarTarjetaDatos_Input_Ciudad_Texto',
                  35
                )}
                inputMode="none"
                data-testid="inputTest"
                disabled
              />
            </button>

            <div className="card-data-sub-div">
              <button
                className="address-button button-margin focusable"
                onClick={e => handleCreditCardPopup(e, 'area')}
                onFocus={e => handleFocusedInputIndex(e, -3)}
              >
                <span
                  className="address-button-contents"
                  style={{
                    color:
                      areaValue || focusedInputBoxIndex == -3
                        ? '#fff'
                        : ' #7f8086'
                  }}
                >
                  {areaValue
                    ? areaValue
                    : truncateText(
                        'MDP_AgregarTarjetaDatos_Input_Estado_Texto',
                        10
                      )}
                </span>
                <img
                  className="chevron-down-img"
                  src="images/btn_chevron_down.png"
                />
              </button>

              <button
                className={`button-cvv ${
                  focusedInputBoxIndex == 5
                    ? 'button-active'
                    : 'checkout-input-inactive'
                } address-button-input focusable`}
                tabIndex={1}
                onFocus={e => {
                  handleFocusedInputIndex(e, 5)
                  document.getElementById('Key_0')?.focus()
                }}
              >
                <span className="checkout-cursor" style={{ right: '320px' }}>
                  {pinCode?.length == 0 && focusedInputBoxIndex == 5 && '|'}
                </span>
                <input
                  className="button-input-cvv focusable"
                  autoComplete="off"
                  name="pinCode"
                  id="creditCardPinInput"
                  type="text"
                  value={pinCode}
                  maxLength={5}
                  placeholder={truncateText(
                    'MDP_AgregarTarjetaDatos_Input_CP_Texto',
                    10
                  )}
                  onChange={e => {
                    handlePinCodeChange(e?.target?.value)
                  }}
                  inputMode="none"
                  disabled
                />
              </button>
              {pinCodeError && (
                <div className="error-box">
                  <div
                    id="promoCodeError"
                    className={`err-text ${
                      pinCodeError
                        ? 'credit-card-error-cvv pincode-error'
                        : 'invisible'
                    }`}
                  >
                    <span className="error-contents">{pinCodeError}</span>
                  </div>
                </div>
              )}
            </div>

            <button
              className={`checkout-input ${
                focusedInputBoxIndex == 6
                  ? 'checkout-input-active'
                  : 'checkout-input-inactive'
              } position-change  focusable`}
              id="checkOutInput"
              tabIndex={1}
              onFocus={e => handleFocusedInputIndex(e, 6)}
            >
              <span className="checkout-cursor">
                {mobileNumber?.length == 0 && focusedInputBoxIndex == 6 && '|'}
              </span>
              <input
                className="input-text"
                autoComplete="off"
                name="mobileNumber"
                id="creditCardMobileNumberInput"
                type="text"
                value={mobileNumber}
                onChange={e => {
                  handleMobileNumberChange(e?.target?.value)
                }}
                placeholder={truncateText(
                  'MDP_AgregarTarjetaDatos_Input_Telefono_Texto',
                  35
                )}
                inputMode="none"
                data-testid="inputTest"
                disabled
              />
            </button>
            {mobileNumberError && (
              <div className="error-box">
                <div
                  id="promoCodeError"
                  className={`err-text ${
                    mobileNumberError ? 'hubgate-error address-mobile-error' : 'invisible'
                  }`}
                >
                  <span className="error-contents">{mobileNumberError}</span>
                </div>
              </div>
            )}
          </>
        )}
        {!paymentProcessLoading && (
          <div className="error-box">
            <div
              id="promoCodeError"
              className={`err-text ${errMsg ? 'promocode-error' : 'invisible'}`}
            >
              {errMsg}
            </div>
          </div>
        )}
      </>
    )
  }

  const handlePrimaryButtonText = paymentMethod => {
    switch (paymentMethod) {
      case 'hubgate':
        return isOtpPage
          ? truncateText(`MDP_Formulario_LineaMovil_TextoBotonPrimario`, 10)
          : truncateText(`MDP_Formulario_SingleInput_TextoBotonPrimario`, 10)
      case 'hubfacturafijagate':
      case 'hubfijogate':
        return (
          truncateText(`MDP_Formulario_SingleInput_TextoBotonPrimario`, 10) ||
          truncateText(`MDP_Formulario_DoubleInput_TextoBotonPrimario`, 10)
        )
      case 'claropagosgate':
        return isCreditAddressPage
          ? truncateText(`MDP_AgregarTarjetaDatos_TextoBotonPrimario`, 10)
          : truncateText(`MDP_AgregarTarjeta_TextoBotonPrimario`, 10)
      default:
        return truncateText(`MDP_CodigoPromo_TextoBotonPrimario`, 10)
    }
  }

  const handleSecondaryButtonText = paymentMethod => {
    switch (paymentMethod) {
      case 'hubgate':
        return isOtpPage
          ? truncateText(`MDP_Formulario_LineaMovil_TextoBotonSecundario`, 10)
          : truncateText(`MDP_Formulario_SingleInput_TextoBotonSecundario`, 10)
      case 'hubfacturafijagate':
      case 'hubfijogate':
        return (
          truncateText(`MDP_Formulario_SingleInput_TextoBotonSecundario`, 10) ||
          truncateText(`MDP_Formulario_DoubleInput_TextoBotonSecundario`, 10)
        )
      case 'claropagosgate':
        return isCreditAddressPage
          ? truncateText(`MDP_AgregarTarjetaDatos_TextoBotonSecundario`, 10)
          : truncateText(`MDP_AgregarTarjeta_TextoBotonSecundario`, 10)
      default:
        return truncateText(`MDP_CodigoPromo_TextoBotonSecundario`, 10)
    }
  }

  const handleCheckoutButton = gateway => {
    switch (gateway) {
      case 'claropagosgate':
        if (isCreditAddressPage) {
          return (
            mobileNumber?.length == 0 ||
            pinCode?.length == 0 ||
            areaValue?.length == 0 ||
            cityLine?.length == 0 ||
            addressLine?.length == 0
          )
        } else if (state?.confirmCvv) {
          return cardConfirmCVV?.length != 3 || cvvError
        } else {
          return (
            creditCardNumber?.length == 0 ||
            cardCVV?.length == 0 ||
            yearValue?.length == 0 ||
            monthValue?.length == 0 ||
            cardName?.length == 0 ||
            cvvError
          )
        }
      case 'hubgate':
        return (
          !numberRegex.test(checkoutInputValue) &&
          checkoutInputValue?.length < 6
        )
      default:
        return checkoutInputValue?.length == 0
    }
  }

  return isCreditPopupPage ? (
    <CreditCardDataPopup
      type={creditPopupType}
      onClose={handleOnCloseCreditPopup}
      setMonthValue={setMonthValue}
      setYearValue={setYearValue}
      setAreaValue={setAreaValue}
      setAreaCode={setAreaCode}
    />
  ) : !showPopup ? (
    <>
      {paymentProcessLoading && (
        <div className="checkout-common-loader">
          <Lottie
            options={{
              rendererSettings: {
                preserveAspectRatio: 'xMidYMid slice'
              }
            }}
            loop
            animationData={animationData}
            play
          />
        </div>
      )}
      <div
        className={`checkout-container ${
          paymentProcessLoading ? 'checkout-loading-indicator' : ''
        }`}
      >
        <div
          className="checkout-title-back-container"
          data-sn-down={'.search-keyboard-button'}
          data-sn-left={'#abcBtn'}
        >
          <BackButtonComponent
            uid="paymentmethod-page-back"
            text={truncateText('BotonShortcut_TextoTitulo_Regresar', 10)}
            onCustomFocus={() => handleBackFocus()}
            onCustomBlur={() => handleBackBlur()}
            onCustomClick={() => handleGoPreviousPage()}
          />
        </div>
        {isTelmexPayment ? (
          displayTelmex()
        ) : (
          <div className="checkout-layout-container">
            <div className="checkout-keyboard-container">
              <div
                className="checkout-keyboard-layout"
                id="checkoutKeyboardLayout"
              >
                <AlphaNumericKeyboard
                  type="alphaNumeric"
                  onChange={
                    focusedInputBoxIndex == 0
                      ? state?.paymentMethod == 'claropagosgate'
                        ? handleCreditCardNumber
                        : handleInputChange
                      : focusedInputBoxIndex == 1
                      ? handleCardNameChange
                      : focusedInputBoxIndex == 2
                      ? state?.confirmCvv
                        ? handleConfirmCVVChange
                        : handleCVVChange
                      : focusedInputBoxIndex == 3
                      ? handleAddressLineChange
                      : focusedInputBoxIndex == 4
                      ? handleCityLineChange
                      : focusedInputBoxIndex == 5
                      ? handlePinCodeChange
                      : focusedInputBoxIndex == 6 && handleMobileNumberChange
                  }
                  autoFocus={keyboardFocus}
                  value={
                    focusedInputBoxIndex == 0
                      ? state?.paymentMethod == 'claropagosgate'
                        ? creditCardNumber
                        : checkoutInputValue
                      : focusedInputBoxIndex == 1
                      ? cardName
                      : focusedInputBoxIndex == 2
                      ? state?.confirmCvv
                        ? cardConfirmCVV
                        : cardCVV
                      : focusedInputBoxIndex == 3
                      ? addressLine
                      : focusedInputBoxIndex == 4
                      ? cityLine
                      : focusedInputBoxIndex == 5
                      ? pinCode
                      : focusedInputBoxIndex == 6 && mobileNumber
                  }
                />
              </div>
            </div>
            <div
              className="checkout-details-container"
              id="checkoutDetailsContainer"
            >
              {handleInputForm(state?.paymentMethod)}
              <button
                className={`checkout-confirm-button ${
                  state?.paymentMethod == 'claropagosgate'
                    ? isCreditAddressPage
                      ? 'claropagos-address'
                      : state?.confirmCvv
                      ? 'claropagos-confirm-cvv'
                      : 'claropagos-confirm'
                    : ''
                } focusable`}
                onClick={() => handleSubscription(state?.paymentMethod)}
                tabIndex={1}
                disabled={
                  paymentProcessLoading ||
                  handleCheckoutButton(state?.paymentMethod)
                }
              >
                <span className="checkout-button-text">
                  {handlePrimaryButtonText(state?.paymentMethod)}
                </span>
              </button>
              <button
                className="checkout-cancel-button focusable"
                onClick={handleGoPreviousPage}
                tabIndex={1}
              >
                <span className="checkout-button-text">
                  {handleSecondaryButtonText(state?.paymentMethod)}
                </span>
              </button>
              {state?.confirmCvv ? (
                <div className="cvv-secure-wrapper">
                  <img
                    className="cvv-secure-img"
                    src={
                      apaAssetsImages?.MDP_AgregarTarjeta_Confirmacion_Icono ||
                      'images/secure-cvv.png'
                    }
                  />
                  <span className="cvv-secure-text">
                    {truncateText(
                      'MDP_AgregarTarjeta_Confirmacion_TextoInformativo',
                      16
                    )}
                  </span>
                </div>
              ) : null}
            </div>
          </div>
        )}
      </div>
    </>
  ) : (
    <FibraLinesPopup
      onClose={handleFibraPopup}
      fibraLineResponse={fibraPaymentResponse?.response?.account_data}
      updateFibraLineupValue={updateFibraLineupValue}
    />
  )
}
export default Checkout
