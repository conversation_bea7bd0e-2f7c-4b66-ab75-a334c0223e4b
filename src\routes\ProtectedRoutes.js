import React from "react";
import { useSelector } from "react-redux";

import { Navigate, Outlet } from "react-router-dom";

const useAuth = () => {
	const user = localStorage.getItem("token")
	if (user) {
		return {
			auth: true
		}
	} else {
		return {
			auth: false
		}
	}
}

const ProtectedRoutes = (props) => {
	const watchFree = useSelector(state => state?.login?.watchFreestate)
	const { auth } = useAuth()
	return auth || watchFree ? <Outlet /> : <Navigate replace to="/" />
}

export default ProtectedRoutes;