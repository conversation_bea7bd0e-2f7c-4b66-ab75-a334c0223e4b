import React, { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './ContinueWatching.scss'
import './RecordingList.scss'
import { store } from '../../../store/sagaStore'
import {
  getLiveTvCompleteRecording,
  getLiveTvEpisodeRecoding
} from '../../../store/slices/EpgSlice'
import moment from 'moment'
import ShowRecordingHoursPopup from './ShowRecordingHoursPopup'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import {
  clearGetMediaRes,
  getRecordPlayer
} from '../../../store/slices/PlayerSlice'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { contentSelectionType } from '../../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../../utils/devicePlatform'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const RecordingList = props => {
  const navigate = useNavigate()
  const { state } = useLocation()

  const [content, setContent] = useState([])
  const [focusContent, setFocusContent] = useState(false)
  const [showRecordingPop, setShowRecordingPop] = useState(false)
  const [focusedId, setFocusedId] = useState(null)
  const [playBtnClicked, setPlayBtnClicked] = useState(false)
  const [selectedContent, setSelectedContent] = useState('')
  const [episodeIds, setEpisodeIds] = useState([])
  const [seriesIds, setSeriesIds] = useState([])
  const [lockedEpisodeIds, setLockedEpisodeIds] = useState([])
  const [lockedEpisodeContent, setLockedEpisodeContent] = useState([])
  const [lockedSeriesContent, setLockedSeriesContent] = useState([])

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const railImage = useSelector(
    state => state?.epg?.CompletedRecordingList?.response
  )
  const recordingplayer =
    useSelector(state => state?.player?.recordplayerinfo?.response) ?? {}
  const railEpisodes = railImage?.recordings
  const railImageseries = railImage?.series
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const mycontentdata = localStorage.getItem('miscontenidos')
  const backelement = document.getElementById(props?.backfocusid)
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys( userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbarTab?.page,
        content_list : props?.title,
        modulo_name : 'carrusel'
      }
  useEffect(() => {
    if (railImageseries?.length) {
      const ids = railImageseries.map(episode => episode?.group_id)
      setSeriesIds(ids)
    }
    if (railEpisodes?.length) {
      const ids = railEpisodes.map(episode => episode?.channel?.group_id)
      setEpisodeIds(ids)
    }
    if (lockedChannelsList?.length) {
      const ids = lockedChannelsList.map(channel => channel?.id)
      setLockedEpisodeIds(ids)
    }
  }, [railEpisodes, lockedChannelsList])

  useEffect(() => {
    setLockedEpisodeContent(
      episodeIds.filter(id => lockedEpisodeIds.includes(String(id)))
    )
    setLockedSeriesContent(
      seriesIds.filter(id => lockedEpisodeIds.includes(String(id)))
    )
  }, [episodeIds, lockedEpisodeIds])

  useEffect(() => {
    if (backelement) {
      backelement?.focus()
      backelement?.scrollIntoView({
        block: 'start',
        inline: 'start',
        behavior: 'smooth'
      })
    }
  }, [backelement])

  useEffect(() => {
    if (focusContent) {
      let myContents = document.getElementById(`recordListPage`)
      myContents?.focus()
      myContents?.scrollIntoView({
        block: 'start',
        inline: 'start',
        behavior: 'smooth'
      })
    }
  }, [focusContent])

  useEffect(() => {
    if (
      recordingplayer &&
      Object.keys(recordingplayer).length > 0 &&
      playBtnClicked
    ) {
      navigate('/vodPlayer', {
        state: {
          page: 'record',
          backfocusid: focusedId,
          recordTitle: selectedContent
        }
      })
      setPlayBtnClicked(false)
    }
  }, [recordingplayer])

  const goToMoviesSeries = (item, index) => {
    setFocusedId(`index${props?.index}${index}`)
    localStorage.setItem('subMenu', 1)
    if (item?.actions?.episodes) {
      store.dispatch(getLiveTvEpisodeRecoding(item?.actions?.episodes))
      pushContentSelectionEvent(userData,item,index, contentSelectionType.HOME_SERIES)
      navigate('/episodescreen', {
        state: {
          episodes: item?.actions?.episodes,
          sericetitle: item?.serie_name,
          seasonid: item?.season_id,
          page: 'recorded',
          backfocusid: focusedId
        }
      })
    }
  }

  const goToLivepage = (item, index) => {
    if (lockedChannelsList?.find(e => e?.id == item?.channel?.group_id)) {
      setFocusedId(`index${props?.index}${index}`)
      pushContentSelectionEvent(userData,item,index, contentSelectionType.HOME_SERIES)
      navigate('/my-settings/help-And-Settings/security-pin/configure', {
        state: {
          data: 'record',
          item: item,
          pageName: '/vodPlayer',
          contentName: 'record',
          selectedContent: item,
          backfocusid: focusedId,
          returnPage: 'myContent'
        },
        replace: true
      })
    } else {
      pushContentSelectionEvent(userData,item,index, contentSelectionType.HOME_SERIES)
      setFocusedId(`index${props?.index}${index}`)
      let recordgetmedia = ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') ? item?.actions?.play?.smooth_streaming : item?.actions?.play?.dashwv)
      store.dispatch(clearGetMediaRes())
      store.dispatch(getRecordPlayer(recordgetmedia))
      setPlayBtnClicked(true)
      setSelectedContent(item?.channel?.event?.name)
    }
  }

  const handleFocus = data => {
    setFocusContent(true)
    setContent(data)
    setShowRecordingPop(true)
  }
  const handleBlur = data => {
    setFocusContent(false)
    setShowRecordingPop(false)
  }
  const handleTranslationchange = useCallback(keyname => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  useEffect(() => {
    store.dispatch(
      getLiveTvCompleteRecording({ user_token: userDetails?.user_token })
    )
  }, [userDetails])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (key.redcode == keycode) {
        navigate('/deleterecording', {
          state: {
            recordingData: content,
            deletecontent: 'recordig',
            url: content?.actions?.delete,
            backfocusid: focusedId
          }
        })
      }
    }
  }
  const handleLgkey = keycode => {
    if (focusContent) {
      if (keycode == 403 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 118)
) {
        navigate('/deleterecording', {
          state: {
            recordingData: content,
            deletecontent: 'recordig',
            url: content?.actions?.delete,
            backfocusid: focusedId
          }
        })
      }
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  function secondsToHms(d) {
    d = Number(d)
    let h = Math.floor(d / 3600)
    let m = Math.floor((d % 3600) / 60)
    let hDisplay = h > 0 ? h + ' hr ' : ''
    let mDisplay = m > 0 ? m + ' min ' : ''
    return hDisplay + mDisplay
  }

  const dateToYMD = curdate => {
    return moment(curdate).format('MMM DD, YYYY')
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className="recordingcontainer" style={{height : railEpisodes?.[0] || railImageseries?.[0] ? 291 : 316}} id="recordListPage">
      {railEpisodes?.[0] || railImageseries?.[0] ? (
        <div className="railTitle">
          <SafeHTML html={props?.title || ''} />
        </div>
      ) : (
        ''
      )}
      {
        <div className="recording-list-container">
          {railEpisodes?.[0] || railImageseries?.[0] ? (
            <div className="rail-wrapper continue-wrapper">
              {railEpisodes?.map((each, index, array) => (
                <div className="mycontent-recording-block">
                  <>
                    <button
                      className={`${
                        lockedEpisodeContent.includes(each?.channel?.group_id)
                          ? 'rail_block-recordig-locked focusable'
                          : 'rail_block-recordig focusable'
                      }`}
                      key={index}
                      onClick={() => goToLivepage(each, index)}
                      onFocus={() => {
                        handleFocus(each)
                        setFocusedId(`index${props?.index}${index}`)
                      }}
                      onBlur={() => handleBlur()}
                      data-sn-down={document?.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                      data-sn-up={document?.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                      data-sn-right={index != array.lenght - 1 && undefined}
                      data-testid={`rail_card_click${index}`}
                      id={`index${props?.index}${index}`}
                    >
                      {lockedEpisodeContent.includes(
                        each?.channel?.group_id
                      ) && (
                        <div className="locked-channel-icon">
                          <img
                            src={'images/lock_icon_liveTV.png'}
                            className="tag-alq"
                            alt="Locked Channel"
                          />
                        </div>
                      )}

                      {each?.channel?.image ? (
                        <LazyLoadImage
                          src={each?.channel?.image}
                          placeholderSrc="images/record_placeholder.png"
                          key={index}
                          className={`rail-image ${
                            lockedEpisodeContent.includes(
                              each?.channel?.group_id
                            )
                              ? 'image-layout-lock'
                              : ''
                          }`}
                          id={`railfocus${index}`}
                        />
                      ) : (
                        <LazyLoadImage
                          src="images/record_placeholder.png"
                          loading="lazy"
                          alt="PlaceHolder"
                          className={`rail-image ${
                            lockedEpisodeContent.includes(
                              each?.channel?.group_id
                            )
                              ? 'image-layout-lock'
                              : ''
                          }`}
                        />
                      )}

                      <LazyLoadImage
                        src={'images/Search_Icons/ic_card_play.png'}
                        loading="lazy"
                        alt="PlaceHolder"
                        className={'record-play-icon'}
                        placeholderSrc={'images/Search_Icons/ic_card_play.png'}
                      />
                      <div className="deleteIcons-record">
                        <img
                          src={'images/Home_icons/red.png'}
                          className="redDot"
                        />
                        <img
                          src={'images/Home_icons/delete.png'}
                          className="delete"
                        />
                      </div>
                    </button>
                    <div className="record-metadata-block">
                      <div className="record-metadata-position">
                        <div className="content-title-rec">
                          {each?.channel?.event?.name?.length >= 32
                            ? `${each?.channel?.event?.name.slice(0, 32)}...`
                            : each?.channel?.event?.name}
                        </div>
                      </div>
                      <div className="duration-time">
                        {secondsToHms(each?.channel?.event?.duration)}{' '}
                        {dateToYMD(each?.date)}
                      </div>
                      <div className="duration-time-display">
                        <div className="recording-image">
                          <img
                            src={'images/Search_Icons/ic_grabado.png'}
                            className="grabado"
                          />
                        </div>
                        <div className="record-status">
                          {handleTranslationchange(
                            'recording_npvr_recordingBlink_label'
                          )}
                        </div>
                      </div>
                    </div>
                  </>
                </div>
              ))}
              {railImageseries?.map((each, index, array) => (
                <div className="mycontent-recording-block">
                  <>
                    <button
                      className={`${
                        lockedSeriesContent.includes(each?.group_id) 
                          ? 'rail_block-recordig-locked focusable'
                          : 'rail_block-recordig focusable'
                      }`}
                      key={index}
                      onClick={() => goToMoviesSeries(each, index)}
                      onFocus={() => {
                        handleFocus(each)
                        setFocusedId(
                          `index${props?.index}${index}`
                        )
                      }}
                      onBlur={() => handleBlur()}
                      data-sn-down={document?.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                      data-sn-up={document?.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                      data-testid={`rail_card_click${index}`}
                      id={ `index${props?.index}${index}`}
                      data-sn-right={index != array?.length - 1 && undefined}
                    >
                      {lockedSeriesContent.includes(each?.group_id) && (
                        <div className="locked-channel-icon">
                          <img
                            src={'images/lock_icon_liveTV.png'}
                            className="tag-alq"
                            alt="Locked Channel"
                          />
                        </div>
                      )}

                      {each?.image ? (
                        <>
                          <LazyLoadImage
                            src={each?.image}
                            placeholderSrc="images/record_placeholder.png"
                            key={index}
                            className={`rail-image ${
                              lockedSeriesContent.includes(each?.group_id)
                                ? 'image-layout-lock'
                                : ''
                            }`}
                            id={`railfocus${index}`}
                          />
                        </>
                      ) : (
                        <LazyLoadImage
                          src="images/record_placeholder.png"
                          loading="lazy"
                          alt="PlaceHolder"
                          className={`rail-image ${
                            lockedSeriesContent.includes(each?.group_id)
                              ? 'image-layout-lock'
                              : ''
                          }`}
                        />
                      )}
                      <div className="deleteIcons-record">
                        <img
                          src={'images/Home_icons/red.png'}
                          className="redDot"
                        />
                        <img
                          src={'images/Home_icons/delete.png'}
                          className="delete"
                        />
                      </div>
                    </button>
                    <div className="record-metadata-block">
                      <div className="record-metadata-position">
                        <div className="content-title-rec">
                          {each?.serie_name?.length >= 32
                            ? `${each?.serie_name.slice(0, 32)}...`
                            : each?.serie_name}
                        </div>
                      </div>
                      <div className="recording-image-block">
                        <div className="recording-image">
                          <img
                            src={'images/episode_icon.png'}
                            className="grabado"
                          />
                        </div>
                        <div className="record-status">
                          {each?.records?.length}{' '}
                          {apilanguage?.content_data_episodes_label
                            ? apilanguage?.content_data_episodes_label.toLowerCase()
                            : 'content_data_episodes_label'}
                        </div>
                      </div>
                      <div className="record-status-position">
                        <div className="recording-image">
                          <img
                            src={'images/Search_Icons/ic_grabado.png'}
                            className="img-grabando"
                          />
                        </div>
                        <div className="record-status">
                          {handleTranslationchange(
                            'recording_npvr_recordingBlink_label'
                          )}
                        </div>
                      </div>
                    </div>
                  </>
                </div>
              ))}
              {showRecordingPop && <ShowRecordingHoursPopup />}
            </div>
          ) : (
            mycontentdata && (
              <div>
                <div className="mycontent-railTitle">
                  <SafeHTML html={props?.title || ''} />
                </div>
                <div className="nocontent-card-main">
                  <button
                    className="nocontent-card focusable"
                    id={`index${props?.index}0`}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    data-sn-right
                    data-sn-left
                    data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                    data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                  >
                    <p className="mycontent-rail-text">
                      {' '}
                      {handleTranslationchange(
                        'myContent_noSaved_presentEvent_label'
                      )}
                    </p>
                    <div className="nocontent-sub-card">
                      <img
                        className="cardimage1 focusable"
                        src={'images/mycontent_placeholder.png'}
                      />
                      <img
                        className="cardimage2"
                        src={'images/mycontent_placeholder.png'}
                      />
                      <img
                        className="cardimage3"
                        src={'images/mycontent_placeholder.png'}
                      />
                    </div>
                  </button>
                </div>
              </div>
            )
          )}
        </div>
      }
    </div>
  )
}

export default RecordingList
