.appCss {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: #121212;

	.claro<PERSON><PERSON> {
		height: 34.36px;
		width: 169.64px;
		margin-top: 67px;
		margin-left: 90px;
	}

	.backIndicator {
		display: flex;
		height: 48px;
		width: 292px;
		border-radius: 6.6px;
		background-color: #2E303D;
		align-items: center;
		float: right;
		margin-top: 47px;
		margin-right: 64px;

		.yellowIndicator {
			height: 20px;
			width: 20px;
			margin-left: 24px;
			margin-right: 24px;
		}

		.backImage {
			height: 24px;
			width: 30px;
			margin-right: 24px;
		}

		.backText {
			height: 30px;
			width: 146px;
			color: #FFFFFF;
			font-family: <PERSON><PERSON>;
			font-size: 29.04px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 29.04px;
		}
	}

	//Subscription Management
	.subsTitle {
		height: 57px;
		width: 1920px;
		color: #FFFFFF;
		font-family: <PERSON><PERSON>;
		font-size: 48px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 57px;
		text-align: center;
		display: block;
	}

	//Active Subscriptions
	.sub-desc {
		font-family: Roboto;
		font-size: 40px;
		letter-spacing: 0px;
		color: #FFFFFF;
		line-height: 47px;
		display: flex;
		width: 1018px;
		text-align: center;
		margin-left: 24%;
		height: 94px;
	}

	.active-cards {
		display: flex;
		margin-left: 120px;
		margin-top: 130px;
		width: 1828px;
		overflow-x: scroll;

		.sub-div-active {
			display: flex;
			flex-direction: column;
			margin-right: 40px;

			.active-focus-card {
				height: 336px;
				width: 512px;
				background-color: #232323;

				.text-card{
					text-align: 'center';
				

				.plan-logo-css {
					margin-top: 24px;
					height: 48px;
				}

				.card-desc-subs {
					margin-top: 8px;
					color: #979797;
					font-family: Roboto;
					font-size: 28px;
					letter-spacing: 0;
					line-height: 32px;
					text-align: center;
				}

				.price-css {
					height: 48px;
					width: 106px;
					color: #EEEEEE;
					font-family: Roboto;
					font-size: 48px;
					font-weight: bold;
					letter-spacing: -1.5px;
					line-height: 48px;
					text-align: right;
				}

				.message {
					margin-left: 9px;
					height: 32px;
					width: 85px;
					color: #EEEEEE;
					font-family: Roboto;
					font-size: 32px;
					letter-spacing: -1px;
					line-height: 32px;
					text-align: center;
				}

			}

				.info-div {
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					bottom: 29px;

					.warning-image {
						width: 60px;
						height: 56px;
						margin-left: 84px;
					}

					.info-text {
						margin-left: 16px;
						height: 64px;
						color: #FFFFFF;
						font-family: Roboto;
						font-size: 28px;
						letter-spacing: -0.87px;
						line-height: 32px;
					}
				}

				.info-text1 {
					position: relative;
					bottom: 27px;
					height: 32px;
					color: #FFFFFF;
					font-family: Roboto;
					font-size: 28px;
					letter-spacing: -0.87px;
					line-height: 32px;
					text-align: center;
				}

				.date-text {
					position: relative;
					bottom: 24px;
					height: 32px;
					color: #FFFFFF;
					font-family: Roboto;
					font-size: 28px;
					letter-spacing: 0;
					line-height: 32px;
					text-align: center;
				}

				.card-desc-date {
					text-align: center;
					margin-top: 24px;

					.next-payment-desc {
						font-weight: bold;
						color: #FFFFFF;
						font-family: Roboto;
						font-size: 28px;
						letter-spacing: 0;
						line-height: 32px;
					}

					.date-css {
						font-weight: 500;
						color: #FFFFFF;
						font-family: Roboto;
						font-size: 28px;
						letter-spacing: 0;
						line-height: 32px;
					}
				}

				.card-desc-payment {
					text-align: center;
					margin-top: 15px;

					.custom-class-1 {
						font-weight: bold;
						color: #FFFFFF;
						font-family: Roboto;
						font-size: 28px;
						letter-spacing: 0;
						line-height: 32px;
					}

					.custom-class-2 {
						color: #FFFFFF;
						font-family: Roboto;
						font-size: 28px;
						letter-spacing: 0;
						line-height: 32px;
					}



				}
			}

			.active-focus-card:focus {
				border: 3px solid #981C15;
				opacity: 1;
			}

			.active-subs-cancel-button-div {
				height: 72px;
				width: 443px;
				border-radius: 10.12px;
				background-color: #981C15;
				display: flex;
				color: #FFFFFF;
				font-family: Roboto;
				font-size: 32px;
				font-weight: bold;
				letter-spacing: -0.51px;
				line-height: 38px;
				text-align: center;
				justify-content: center;
				align-items: center;
				margin-left: 33px;
			}

			.active-subs-cancel-button {
				margin-top: 25px;
			}

			.active-subs-cancel-button:focus {
				padding: 4px;
				border-radius: 10.12px;

				.active-subs-cancel-button-div {
					height: 80.8px;
					width: 494.45px;
					margin-left: 0px;
				}
			}

		}
	}


	//Cancel Subscriptions
	.cancelSubDiv {
		margin-top: 34px;
		text-align: center;

		.cancelTitle {
			height: 48px;
			// width: 1621px;
			color: #FFFFFF;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 48px;
		}

		.cancelLogoCss {
			height: 96px;
			width: 232px;
		}

		.cancelPriceCss {
			height: 80px;
			width: 158px;
			color: #EEEEEE;
			font-family: Roboto;
			font-size: 72px;
			font-weight: bold;
			letter-spacing: -2.25px;
			line-height: 80px;
		}

		.message {
			margin-left: 12px;
			height: 48px;
			width: 323px;
			color: #EEEEEE;
			font-family: Roboto;
			font-size: 48px;
			letter-spacing: -1.5px;
			line-height: 48px;
		}

		.includesInfo {
			display: inline-block;
			height: 32px;
			width: 510.1px;
			color: #EEEEEE;
			font-family: Roboto;
			font-size: 28px;
			letter-spacing: -0.87px;
			line-height: 32px;
		}

		.disclamerInfo {
			height: 128px;
			width: 510.1px;
			color: #EEEEEE;
			font-family: Roboto;
			font-size: 28px;
			letter-spacing: 0;
			line-height: 32px;
			margin-left: 37%;
			margin-top: 56px;
		}

		.confirmCancelButton {
			margin-top: 85px;
			height: 82.08px;
			width: 580.26px;
			border-radius: 10.03px;
			background-color: #981C15;
			color: #FFFFFF;
			font-family: Roboto;
			font-size: 36.5px;
			font-weight: bold;
			letter-spacing: -0.58px;
			line-height: 42.18px;
			text-align: center;
		}
	}

	//CancelSubscriptionSuccess
	.cancelSuccessDiv {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 96px;

		.greenTick {
			width: 80px;
			height: 80px;
		}

		.subscriptionCancelText {
			height: 56px;
			width: 1120px;
			color: #FFFFFF;
			font-family: Roboto;
			font-size: 48px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 57px;
			text-align: center;
			margin-top: 80px;
		}

		.subscriptionCancelErrorText {
			height: 104px;
			width: 934px;
			color: #EEEEEE;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 48px;
			text-align: center;
			margin-top: 80px;
		}

		.cancelSuccessAccept {
			margin-top: 80px;
			height: 82.08px;
			width: 580.26px;
			border-radius: 10.03px;
			background-color: #981C15;
			color: #FFFFFF;
			font-family: Roboto;
			font-size: 36.5px;
			font-weight: bold;
			letter-spacing: -0.58px;
			line-height: 42.18px;
			text-align: center;
		}
	}

}

//Add Subscriptions
.AddSubstitle {
	/* height: 96px; */
	/* width: 1104px; */
	color: #FFFFFF;
	font-family: Roboto;
	font-size: 40px;
	letter-spacing: 0;
	/* line-height: 48px; */
	text-align: center;
}

.addsubscriptionhome {
	display: flex;
	margin-left: 61px;
	overflow-x: scroll;
}

.addSubscriptionsDiv {
	display: flex;
	margin-left: 100px;
	margin-right: 147px;
	overflow-x: scroll;
	padding-left: 15px;
	scroll-snap-type: x mandatory;

	.addCards {
		margin-top: 57px;

		.banner {
			height: 590px;
			width: 254px;
		}

		.subsMetaData {
			position: relative;
			margin-top: -280px;
			display: flex;
			flex-wrap: wrap;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			right: 10px;

			.addSubLogo {
				display: flex;
				height: 50px;
				}

			.pricingInfo {
				text-align: center;
				letter-spacing: -0.98px;
				color: #FFFFFF;
				opacity: 1;
				font: normal normal normal 20px Roboto;
				display: block;
				margin-top: 25px;

				.priceCss {
					width: 68px;
					height: 41px;
					text-align: center;
					letter-spacing: -0.98px;
					color: #FFFFFF;
					opacity: 1;
				}
			}

			.taxLabel {
				letter-spacing: -0.68px;
				color: #FFFFFF;
				opacity: 1;
				margin-top: 2%;
				font: normal normal normal 22px Roboto;
				text-align: center;
			}

			.freeChargeStr {
				width: 127px;
				height: 54px;
				text-align: center;
				font: normal normal normal 22px Roboto;
				letter-spacing: -0.68px;
				color: #FFFFFF;
				opacity: 1;
				display: flex;
				margin-top: 15px;

			}

			.subButtonsDiv {
				margin-top: 20px;

				.subsButton {
					height: 40px;
					width: 200px;
					opacity: 1;
					margin-bottom: 5%;
					border-radius: 4px;
					text-transform: uppercase;
					color: #FFFFFF;
					font-family: Roboto;
					font-size: 16.1px;
					font-weight: bold;
					letter-spacing: 0;
					line-height: 19px;
					text-align: center;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
				}
			}

		}

		.viewPlanButton {
			height: 48px;
			width: 25px;
			border-radius: 6.6px;
			background-color: #2E303D;
			position: relative;
			opacity: 0;
			display: flex;
			align-items: center;
			margin-top: 50px !important;

			.blueShortcut {
				width: 20px;
				margin-left: 15px;
				margin-right: 24px;
			}

			.viewButtonContents {
				color: #FFFFFF;
				font-family: Roboto;
				font-size: 25px;
				font-weight: bold;
				letter-spacing: 0;
				line-height: 29.04px;
				height: 30px;
				width: 211px;
			}
		}
	}

	.addCards:focus {
		margin-right: 30px;

		.subsMetaData {
			right: 0px;
		}
      

		.viewPlanButton {
			opacity: 1;
			height: 48px;
			width: 325px;
			display: flex;
			align-items: center;
		}
	}
}