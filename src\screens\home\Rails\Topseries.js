import React, { useState, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import './TopSeries.scss'
import { RailsSkeletonLoading } from '../../SkeletonScreenLoading/SkeletonScreenloading'
import { useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useDispatch } from 'react-redux'
import { getRegisterPopup } from '../../../store/slices/login'
import { COMMON_URL } from '../../../utils/environment'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { contentSelectionType } from '../../../GoogleAnalyticsConstants'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const TopSeries = props => {
  const one = 'images/one.svg'
  const two = 'images/two.svg'
  const three = 'images/three.svg'
  const four = 'images/four.svg'
  const five = 'images/five.svg'
  const six = 'images/six.svg'
  const seven = 'images/seven.svg'
  const eight = 'images/eight.svg'
  const nine = 'images/nine.svg'
  const ten = 'images/ten.svg'

  const dispatch = useDispatch()
  const railRef = useRef([])
  const [data, setData] = useState([])
  const imageCount = [
    {
      id: 1,
      url: one
    },
    {
      id: 2,
      url: two
    },
    {
      id: 3,
      url: three
    },
    {
      id: 4,
      url: four
    },
    {
      id: 5,
      url: five
    },
    {
      id: 6,
      url: six
    },
    {
      id: 7,
      url: seven
    },
    {
      id: 8,
      url: eight
    },
    {
      id: 9,
      url: nine
    },
    {
      id: 10,
      url: ten
    }
  ]

  const topSeries = props.byUser
    ? data?.response?.groups
    : props?.dataObject?.groups

  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
    
    const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }
    const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
    const nav = useSelector(state => state?.homeReducer?.navbarData)
    const navData = userDetails?.is_kids === 'true' ? navKids : nav
    const seriesIndex = navData?.findIndex(item => item?.code === 'seriesnv' && item?.page === 'Series' );
    const moviesIndex = navData?.findIndex(item => item?.code === 'peliculas' && item?.page === 'Películas' );
  
  const navigate = useNavigate()

  useEffect(() => {
    const element = document.getElementById(props?.id)
    element && element.scrollIntoView({ behavior: 'smooth' })
    const topseriescarousel = document.getElementById('topseriesCarousel')
    topseriescarousel &&
      topseriescarousel.addEventListener('keydown', handleKeyDown)
    return () => {
      topseriescarousel &&
        topseriescarousel.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  const handleFocus = (data,index) => {
    railRef?.current?.[index]?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    });
  }

  const goToMoviesSeries = (item,index) => {
    localStorage.setItem('subMenu', 1)
     let userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbarTab,
        content_list : props?.title,
        modulo_name : 'carrusel'
      }
    dispatch(getRegisterPopup(false))
    if (item?.is_series || item?.contentType === 'chapter') {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', seriesIndex)
      pushContentSelectionEvent(userData,item,index, contentSelectionType.HOME_SERIES)
      navigate('/series', { state: { data: item } })
    } else {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', moviesIndex)
      pushContentSelectionEvent(userData,item,index, contentSelectionType.HOME_MOVIES)
      navigate('/movies', { state: { vodData: item } })
    }
  }

  const [visibleCards, setVisibleCards] = useState(10)

  const handleScroll = e => {
    const { scrollWidth, scrollLeft, clientWidth } = e.target
    // Check if the user has scrolled to the end
    if (scrollLeft + clientWidth === scrollWidth) {
      setVisibleCards(prevVisibleCards => {
        return prevVisibleCards + 0
      })
    }
  }

  const fetchApi = async params => {
    if (params !== undefined) {
      return await fetch(
        `${COMMON_URL.BASE_URL}/${params}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}`
      ).then(async data => {
        const value = await data.json()
        setData(value)
      })
    }
  }

  useEffect(() => {
    if (props.byUser) {
      fetchApi(props?.dataObject)
    }
  }, [props?.dataObject, props?.byUser])

  const handleKeyDown = event => {
    const element = document.getElementById('topseriesCarousel')
    if (element) {
      const cards = document.querySelectorAll('.series_block')
      const focusedElement = document.activeElement
      const focusedIndex = Array.from(cards).indexOf(focusedElement)

      if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
        const nextCard = cards[focusedIndex + 1]
        const containerRect = element.getBoundingClientRect()
        const nextCardRect = nextCard.getBoundingClientRect()

        if (nextCardRect.right > containerRect.right) {
          element.scrollLeft += nextCardRect.right - containerRect.right
        }
      } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
        const prevCard = cards[focusedIndex - 1]
        const containerRect = element.getBoundingClientRect()
        const prevCardRect = prevCard.getBoundingClientRect()

        if (prevCardRect.left < containerRect.left) {
          element.scrollLeft -= containerRect.left - prevCardRect.left
        }
      }
    }
  }

  return (
    <div className={`${topSeries?.[0] && "toprailContainer"}`}>
      <div className="railTitleTop">
        <SafeHTML html={props?.title || ''} />
      </div>
      {topSeries?.[0] ? (
        <div
          className="rail-wrapper"
          onScroll={handleScroll}
          id="topseriesCarousel"
        >
          {topSeries?.slice(0, visibleCards)?.map((item, index, array) => (
            <>
              <div className="count_block">
                <img
                  className="no_img"
                  src={item.image_small || item?.images?.small ? imageCount[index]?.url : null}
                />
              </div>
              <button
                className="series_block focusable"
                style={{
                  border: props?.id === item.id ? '4px solid #fff' : ''
                }}
                ref={el => (railRef.current[index] = el)}
                onClick={() => goToMoviesSeries(item,index)}
                onFocus={() => {
                  handleFocus(item,index)
                }}
                key={index}
                id={`index${props?.index}${index}`}
                data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` :  undefined}
                data-testid={`rail_card_click${index}`}
                data-sn-right={index != array?.length - 1 && undefined}
                data-sn-left={index != 0 && undefined}
              >
                <LazyLoadImage
                  src={item.image_small || item?.images?.small}
                  key={item.id}
                  placeholderSrc="images/landscape_card.png"
                  className="top-image-series"
                />

                {item?.provider?.code == 'amco' || item?.proveedor_code == 'amco' ? (
                  item?.format_types === 'ppe,download' || item?.format_types === 'ppe' || item?.contentAttributes?.marketingType === 'ppe,download' || item?.contentAttributes?.marketingType === 'ppe' ? (
                    <div className="proveedorBlockRail">
                      <img src={'images/Alquilar.svg'} className="tagAlq" />
                    </div>
                  ) :
                    item?.format_types === 'ppe,est' || item?.contentAttributes?.marketingType === 'ppe,est'? (
                      <>
                        <div className="comprar-tag-movies" style={item?.is_series || item?.contentType === 'chapter' ? { backgroundColor: '#40336f' } : null}>COMPRAR</div>
                        <div className="proveedor-block-rail-alq-comp">
                          <img
                            src={'images/Alquilar.svg'}
                            className="tagAlq"
                          />
                        </div>
                      </>
                    ) : item?.format_types === 'est' || item?.contentAttributes?.marketingType === 'est' &&
                    <div className="comprar-tag-movies" style={item?.is_series ? { backgroundColor: '#40336f' } : null}>COMPRAR</div>
                ) : item?.provider?.code && item?.images?.medium || item?.proveedor_code && item?.image_medium ? (
                  <div className="proveedorBlockRail">
                    <img id="#icon1"
                      className={item?.provider?.code === 'picardia2' || item?.proveedor_code === 'picardia2' ? 'picardia-image' : 'premium-icon'}
                      src={Addproveedor(providerLabel?.[item?.proveedor_code ?? item?.provider?.code]?.susc)} />
                    {item?.format_types === 'free' || item?.contentAttributes?.marketingType === 'free'? (
                      <div className="proveedorBlockRail">
                        <img src={'images/verahora.png'} className="tag" />
                      </div>
                    ) : null}
                    {item?.provider?.code === 'picardia2' &&
                      item?.images?.medium || item?.proveedor_code === 'picardia2' &&
                      item?.image_medium  &&
                      <div
                        className="picardia-proveedorBlockRail">
                        <img src={'images/Adultus.svg'} className="picardia-tag" />
                      </div>
                    }
                  </div>
                ) : null}
              </button>
            </>
          ))}
        </div>
      ) : (
        <div style={{ display: 'flex' }}>
          <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
        </div>
      )}
    </div>
  )
}

export default TopSeries
