body {
  margin: 0px;
}

.upper-button-back {
  margin-right: 40px;
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  // justify-content: space-evenly;
  border-radius: 6.6px;
  background-color: #2e303d;
  color: #ffffff;
  font-family: <PERSON>o;
  font-size: 29.04px;
  font-weight: bold;
  float: right;
  width: 292px;
  line-height: 0px;
  &:focus {
    background-color: #c60000;
  }
  .yellow-indicator-back {
    height: 20px;
    width: 20px;
    margin-left: 20px;
  }

  .image-back {
    height: 24px;
    width: 30px;
    margin-left: 20px;
  }
  .text-back {
    margin-left: 20px;
  }
}

.checkout-subsription-page {
  width: var(--maxWidth);
  height: var(--maxHeight);
  display: flex;
  flex-direction: column;
  .upper-back-button-area {
    float: right;
    margin-right: 74px;
    margin-top: 34px;
  }
  .contract-title {
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 48px;
    text-align: center;
    margin: 55px 0px;
  }

  .asset-container {
    display: flex;
    flex-direction: column;
  }

  .asset-title {
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 48px;
  }

  .checkout-description {
    color: #eeeeee;
    font-family: Roboto;
    font-size: 28px;
    letter-spacing: 0;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
  }

  .image-and-text {
    display: flex;
    flex-direction: row;
    justify-content: center;
    max-height: 483px;
    width: 805px;
    align-self: center;
    .image-area {
      .banner {
        height: 364.02px;
        width: 239.11px;
      }
    }
    .text-area {
      display: flex;
      flex-direction: column;
      margin-left: 55.79px;

      .free-charge-string-live {
        color: #0af;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
        font-family: Roboto;
        font-size: 28px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px; /* 114.286% */
      }

      .free-scope-live {
        color: #fff;
        font-family: Roboto;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
      }

      .free-detail-wrapper {
        display: flex;
        flex-direction: row;
        margin-top: 14.16px;
      }

      .live-contract-total {
        color: #fff;
        font-family: Helvetica;
        font-size: 48px;
        font-style: normal;
        font-weight: 400;
        line-height: 48px; /* 100% */
        margin-top: 8px;
      }

      .live-scope-description {
        color: #eee;
        font-family: Helvetica;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.874px;
        margin-top: 10px;
      }

      .live-checkout-description {
        color: #858585;
        font-family: Helvetica;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 114.286% */
      }

      .suscripcion-checkout-description,
      .renta-checkout-description,
      .compra-checkout-description {
        width: 460px;
        height: 160px;
        flex-shrink: 0;
        color: #eee;
        font-family: Roboto;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 114.286% */
      }

      .compra-checkout-description,
      .renta-checkout-description {
        margin-top: 20px;
      }

      .live-subscription-info {
        margin-top: 89px;
      }

      .multi-live-subscription-info {
        margin-top: 131px;
      }

      .free-charge-string-view {
        color: #00a9ff;
        font-family: Roboto;
        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.999px;
      }

      .suscripcion-total-wrapper {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
      }

      .buy-image-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
      }

      .total-suscripcion-label {
        margin-top: 16px;
        color: #fff;
        font-family: Roboto;
        font-size: 40px;
        font-style: normal;
        font-weight: 400;
        line-height: 48px; /* 120% */
        width: 100px;
      }

      .total-suscripcion-price {
        margin-left: 10px;
        color: #eee;
        font-family: Roboto;
        font-size: 36px;
        font-style: normal;
        font-weight: 700;
        line-height: 40px; /* 111.111% */
        width: 105px;
      }

      .total-3digit-price {
        width: 85px !important;
      }

      .total-suscripcion-periodicity {
        margin-left: 10px;
        width: 82px;
        color: #eee;
        font-family: Roboto;
        font-size: 25px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px; /* 128% */
      }

      .total-compra-price {
        margin-top: 16px;
      }

      .multi-live-contract-total-wrapper {
        display: flex;
        flex-direction: row;
      }

      .multi-live-contract-total {
        color: #fff;
        font-family: Roboto;
        font-size: 48px;
        font-style: normal;
        font-weight: 700;
        line-height: 48px; /* 100% */
        margin-top: 54px;
        display: flex;
        flex-direction: row;
      }

      .multi-live-buy-sub-logo {
        height: 88px;
        flex-shrink: 0;
      }

      .multi-live-tax {
        color: #fff;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
        font-family: Roboto;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 114.286% */
        margin-top: 67px;
        margin-left: 10px;
      }

      .buy-sub-logo {
        height: 65.73px;
        width: 156.42px;
      }
      .total-label {
        color: #ffffff;
        font-family: Roboto;
        font-size: 38px;
        margin-top: 47.74px;
        .pricing-currency {
          color: #ffffff;
          font-family: Roboto;
          font-size: 38px;
          font-weight: bold;
          letter-spacing: 0;
          line-height: 26px;
        }
        .periodicity {
          color: #eeeeee;
          font-family: Roboto;
          font-size: 25.58px;
        }
      }

      .tax-label {
        color: #ffffff;
        font-family: Roboto;
        font-size: 11px;
        letter-spacing: 0;
        line-height: 12px;
        margin-left: 73px;
        margin-top: 5px;
      }
      .scope-description {
        color: #eeeeee;
        font-family: Roboto;
        font-size: 28px;
        line-height: 32px;
        max-width: 510px;
        margin-top: 8.48px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.874px;
      }
      .subscription-info {
        color: #ffffff;
        font-family: Roboto;
        font-size: 28px;
        letter-spacing: 0;
        line-height: 32px;
        max-width: 510px;
        margin-top: 81.79px;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .buttons-suscripcion {
    margin-top: 20px !important;
  }

  .buttons-area {
    display: flex;
    flex-direction: column;
    margin-top: 62px;
    align-items: center;

    .text-payment {
      margin-top: 16.96px;
      color: #eeeeee;
      font-family: Roboto;
      font-size: 24px;
      font-weight: 500;
    }

    .subscription-page-buttons {
      height: 72px;
      width: 504px;
      border-radius: 11.37px;
      font-size: 28px;
      display: flex;
      justify-content: center;
      align-items: center;

      &:focus {
        height: 82.08px;
        width: 574.56px;
        border-radius: 12.96px;
      }
      &:disabled {
        opacity: 0.5;
      }

      .subscription-button-text {
        height: 38px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 31.92px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 35px;
        text-align: center;
      }
    }
    .contrator {
      background-color: #981c15;
    }

    .cancelar {
      background-color: #2e303d;
    }
  }
  #subscriptionPageCancelButton {
    margin-top: 29px;
  }

  .subscription-cancel-button-with-account {
    margin-top: 40px !important;
  }

  .subscription-container-loader {
    position: absolute;
    top: 207px;
    right: 767px;
  }
}

.checkout-subscription-page-loader {
  opacity: 0.5;
}
