import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { store } from '../../store/sagaStore'
import * as regex from './Regex'
import { pushRegisterEvent,pushScreenViewEvent} from '../../GoogleAnalytics'
import {
    getBackNavigate,
    getRegisterNavigation,
} from '../../store/slices/login'
import '../loginregister/Registerpage.scss'

const Registeremail = () => {
    const navigate = useNavigate()
    const { state } = useLocation()
    const [inputValue, setInputValue] = useState(state?.regmail)
    const [isValidEmail, setIsValidEmail] = useState(false)
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const region = localStorage.getItem('region')
    const translations =
        apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const apilanguage = translations?.language?.[region]
    const startTimeRef = useRef(null)
    const registerNavigation = useSelector(
        state => state?.login?.registerNavigation
    )

    const validateEmailForm = () => {
        if (isErrEmail) {
            setIsValidEmail(false)
        }
    }
    useEffect(() => {
      startTimeRef.current = Date.now()
      return () => {
      startTimeRef.current = null
     }
    }, [])
    const clickNext = e => {
        const engagement_time_msec = Date.now() - startTimeRef.current
        e.preventDefault()
        localStorage.setItem('username', inputValue)
        store.dispatch(getRegisterNavigation(true))
        //GA for sign_up event
          pushRegisterEvent(
            handleTranslationchange(
              'Onboarding_RegistroRCU_Form_TextoBotonPrimario'
            )?.toLowerCase(),
            handleTranslationchange(
              'Onboarding_RegistroRCU_Form_TextoTitulo'
            )?.toLowerCase(),
            engagement_time_msec
          )
        if (registerNavigation == true) {
            navigate('/register', {
                state: {
                    focus: true,
                    regmail: inputValue,
                    regpassword: state?.regpassword,
                    seriesEpisodeData: state?.seriesEpisodeData,
                    fromDetailsPage: state?.fromDetailsPage,
                    pageName: state?.pageName
                }
            })
        } else {
            store.dispatch(getBackNavigate(false))
            navigate('/regpassword', {
                state: {
                    page: 'registermail',
                    regmail: inputValue,
                    regpassword: state?.regpassword,
                    seriesEpisodeData: state?.seriesEpisodeData,
                    fromDetailsPage: state?.fromDetailsPage,
                    pageName: state?.pageName,
                    gaPageName:'register_mail_screen'
                }
            })
        }
        startTimeRef.current = Date.now()
    }
    let isErrEmail = false

    const validate = (name, value) => {
        if (name === 'email') {
            const mail1 = RegExp(/^$/)
            const emailRegex = RegExp(regex.emailPattern)
            const isEmailValid = emailRegex.test(value)
            isErrEmail = mail1.test(value)
            setIsValidEmail(isEmailValid)
            validateEmailForm()
        }
    }

    const handleChange = e => {
        let key = e.target.name
        let values = e.target.value
        values = e.target.value.trimStart()
        setInputValue(values)
        const emoji =
            /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g
        const names = key
        const value = values.replace(emoji, '').trim()
        if (inputValue?.length == 0 || inputValue) {
            validate(names, value)
        }
    }
    const keypresshandler = event => {
        if (event?.keyCode === 10009 || event?.keyCode === 461) {
            navigate('/register', { 
                state: {  
                    focus: true,
                    regmail: inputValue, 
                    regpassword: state?.regpassword, 
                    seriesEpisodeData: state?.seriesEpisodeData,
                    fromDetailsPage: state?.fromDetailsPage,
                    pageName: state?.pageName,
                    page: 'registermail'
                } 
            })
        }
        if (event.keyCode == 13 || event.keyCode == 65376 || event.keyCode == 40) {
            event.preventDefault()
            event.stopPropagation()
            document.getElementById('registernext').focus({ focusVisible: true })
        } else if (event.keyCode == 38) {
            event.preventDefault()
            event.stopPropagation()
            document.getElementById('email').focus({ focusVisible: true })
        }
    }

    useEffect(() => {
        document.addEventListener('keyup', keypresshandler)
        return () => {
            document.removeEventListener('keyup', keypresshandler)
        }
    }, [keypresshandler])

    const handleTranslationchange = useCallback(keyname => {
        if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
            return [keyname]
        } else {
            if (keyname == 'landing_menu_option_button_login') {
                return apilanguage?.[keyname].toUpperCase()
            } else {
                return apilanguage?.[keyname]
            }
        }
    }, [])

    useEffect(() => {
        inputValue?.length > 0 && validate('email', inputValue)
      }, [inputValue])

    useEffect(()=>{
     pushScreenViewEvent({screenName:'registermail_screen', prevScreenName: state?.pageName ?? state?.gaPageName})
    },[])  

    return (
        <div className="App">
            <div className="App-logo">
                <img
                    src={'images/claro-video-logo.png'}
                    className="logo-img"
                    alt="logo"
                />
            </div>
            <div className="register-email-title">
                <label className="registeremail-title">
                    {handleTranslationchange('Onboarding_RegistroRCU_Form_TextoTitulo')}
                </label>
                <input
                    className="register-email"
                    type="email"
                    name="email"
                    id="email"
                    value={inputValue}
                    onChange={e => handleChange(e)}
                    autoFocus
                />
            </div>
            <div className="registermaail-next-button">
                <button
                    className={`${!isValidEmail?'signature':'signature-enable'} focusable`}
                    id="registernext"
                    disabled={!isValidEmail}
                    onClick={e => clickNext(e)}>
                    {handleTranslationchange('Onboarding_RegistroRCU_Form_TextoBotonPrimario')}{' '}
                </button>
            </div>
        </div>
    )
}

export default Registeremail