$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;

.railTitle {
  font-size: 32px;
  font-weight: normal;
  text-align: left;
  letter-spacing: 0px;
  color: $color-white;
  opacity: 1;
  margin-left: 70px;
  font-family: $font-family-roboto;
}
.my-list-container{
  margin-bottom: 40px;
}


.railContainer {
  // overflow-x: hidden;
  scroll-snap-type: x mandatory;
  margin-left: 30px;

  .rail_block {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-left: 20px;
    margin-right: 20px;
    width: 412px;
    opacity: 0.8;
    padding: 4px 4px 4px 4px;

    .rail-title-icon-block {
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 1;
      position: $position-absolute;
      width: 100%;
      top: 246px;

      .mycontent-rail {
        .mycontent-rail-title {
          font-size: 28px;
          color: $color-white;
          font-family: $font-family-roboto;
        }
      }
    }
  }

  .rail_block:focus {
    scroll-snap-align: end;
    opacity: 1;
    transform: scale(1.03);
    border: 4px solid #fff !important;
    border-radius: 10px;
    // margin-right: 0px;
    // margin-left: 0px;
    .proveedorBlockRail_vero_hara {
        left: 13px;
        top: 13px;
      }
    
      .proveedorBlockRailAlq {
        left: 13px;
        top: 13px;
      }

    .deleteIcons-mylist {
      display: block;
    }

    .mycontent-rail {
      .mycontent-rail-title {
        top: 256px;
      }
    }

    .rail-title-icon-block {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // z-index: 1;
      position: $position-absolute;
      width: 100%;
      top: 246px;
    }

    .defaultTitle {
      opacity: 1;
      padding-left: 0.5rem;
    }


    .rail-image {
      z-index: 1;
    }
  }

  .rail_block:focus .seriesMoviesTitlerail {
    opacity: 1;
  }

  .rail-image {
    display: flex;
    width: 412px;
    height: 232px;
  }

  .redDot {
    width: 20px;
    height: 20px;
    border-radius: 100px;
    margin-right: 12px;
    margin-bottom: 8px
  }

  .delete {
    width: 31.43px;
    height: 30px;
    margin-top: 12px;
  }

  .deleteIcons-mylist {
    display: none;
  }



}

.mycontentlist-railtitle {
  font-size: 32px;
  font-weight: normal;
  text-align: left;
  letter-spacing: 0px;
  color: $color-white;
  opacity: 1;
  margin-top: 26px;
  font-family: $font-family-roboto;
  margin-left: 38px;
}

/*recordig rail*/
.recordingcontainer{
  height: 300px;
  margin-bottom: 40px;
}
.recording-list-container {
  overflow-x: hidden;
  scroll-snap-type: x mandatory;
  margin-left: 30px;
}

.rail_block-recordig {
  height: 232px;
  width: 408px;
  position: relative;
}

.mycontent-recording-block {
  height: 245px;
  width: 825px;
  position: relative;
  opacity: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  left: 22px;
}

.duration-time {
  height: 40px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 30px;
  letter-spacing: 0;
  line-height: 40px;
}

.duration-time-display {
  display: flex;
  flex-Direction: row
}

.record-status {
  height: 40px;
  width: 336px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 30px;
  letter-spacing: 0;
  line-height: 40px;
  margin-left: 25px;
}

.recording-image {
  margin-top: 5px;
}

.record-metadata-block {
  margin-left: 50px;
  height: 217px;
  width: 367px;

  .recording-image-block {
    display: flex;
    flex-Direction: row
  }

  .record-metadata-position {
    display: flex;
    flex-direction: column
  }

  .record-status-position {
    display: flex;
    flex-Direction: row
  }
}

.record-play-icon {
  position: absolute;
  left: 315px;
  top: 140px;
}

.deleteIcons-record {
  display: flex;
  position: absolute;
  top: 209px;
  margin-left: 689px;
  width: max-content;
}

.deleteIcons-record {
  display: none;
}


.content-title-rec {
  width: 360px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 36px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 40px;
  margin-bottom: 8px;
}

.rec-backBtn {
  display: flex;
  position: absolute;
  right: 87px;
  top: 47px;
  justify-content: space-between;
  align-items: center;
  width: 292px;
  height: 28px;
  padding: 13px;
  background-color: #2e303d;
  border-radius: 9px;
}

.rec-backBtn:focus {
  background: #981c15 0% 0% no-repeat padding-box;
}

.rail_block-recordig:focus {
  scroll-snap-align: center;

  .deleteIcons-record {
    display: block !important;
  }
}




.defaultTitle {
  font-size: 28px;
  text-wrap: wrap;
  color: #fff;
  font-family: Roboto;
  font-weight: bold;
  padding-left: 0.5rem;
}

.progress-bar {
  background: #2c2c2c;
  position: absolute;
}

.imageTitle {
  color: #fff;
  position: absolute;
  bottom: 40px;
  flex-wrap: wrap;
  line-height: 2.1rem;
}

.titleItem {
  font-size: 28px;
  text-wrap: wrap;
  position: absolute;
  bottom: 0px;
  left: 0px;
  color: #fff;
}

.rail-wrapper {
  display: flex;
  margin: 0.5rem;
  padding: 0.5rem;
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
  position: relative;
}


.proveedorBlockRail {
  position: absolute;
  left: 13px;
  top: 14px;
  display: flex;
}

.picardia-proveedorBlockRail {
  position: absolute;
  left: -11px;
  top: 26px;
  display: flex;


  .picardia-tag {
    position: absolute;
    width: 100px;
    height: 30px;
  }
}

.picardia-image {
  width: auto;
  height: 24px;
}
.premium-icon{
  width: auto;
  height: 34px;
}

.proveedorBlockRail_vero_hara {
  position: absolute;
  height: 34px;
  width: auto;
  left: 13px;
  top: 16px;
  display: flex;
}

#icon1 {
  width: 111px;
  height: 38px;
}

.rail-image-series {
  display: flex;
  width: 412px;
  margin-left: 3px;
  /* Adjust the image width as needed */
  height: 232px;
  margin-right: 1.5rem;
}

.seriesCount {
  font-size: 3rem;
  color: white;
}

.series_block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.count_block {
  display: flex;
  flex-direction: row;

}

.titleItem {
  color: white;
  opacity: 0;
  padding-left: 0.5rem;
}

.description {
  color: white;
}

.seriesMoviesTitlerail {
  position: absolute;
  bottom: 3px;
  left: 0rem;
  padding: 10px;
}

.defaultTitlerail {
  font-size: 28px;
  text-wrap: wrap;
  color: #fff;
  padding-left: 0.5rem;
  font-family: 'Roboto';
  font-weight: bold;
  width: 25rem;
}

.description {
  font-size: 28px;
  text-wrap: wrap;
  color: #fff;
  padding-left: 0.5rem;
  margin-top: 0.2rem;
  font-family: Roboto;
}

.proveedorBlockRailAlq {
  position: absolute;
  left: 13px;
  top: 16px;
}

.proveedor-block-rail-alq-comp {
  position: absolute;
  top: 48px;
  left: 11px;
}


.comprar-tag-movies {
  position: absolute;
  top: 10px;
  left: 10px;
  height: 34px;
  width: 108px;
  border-radius: 6px;
  background-color: #477f9B;
  color: #ffffff;
  font-family: Roboto;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
}

.tagAlq {
  width: 100px;
  height: 34px;
}

.tag {
  width: 161px;
  height: 38px;
}

.verahora-tag {
  height: 32px;
  width: 122px;
  border-radius: 6px;
  background-color: #68B75C;
  color: #FFFFFF;
  font-family: Roboto;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
  position: absolute;
  left: 8px;
  top: 8px;
}

progress-bar {
  width: 90%;
  position: absolute;
  bottom: 1rem;
  height: 6px;
  margin-left: 0.5rem;
}

progress {
  position: absolute;
  height: 16px;
  width: 390px;
  opacity: 1;
  border-radius: 8px;
  bottom: 43px;
  left: 12px;
}

progress::-webkit-progress-bar {
  background: #2c2c2c 0% 0% no-repeat padding-box;
  border-radius: 8px;
  opacity: 0.7;
}

progress::-webkit-progress-value {
  border-radius: 8px;
  background: #de1717 0% 0% no-repeat padding-box;
  opacity: 1;
}

.NotificationLayout {
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border: 2px solid #50595e;
  border-radius: 10px 0px 0px 10px;
  opacity: 1;
  width: 0;
  align-items: center;
  display: flex;
  position: absolute;
  white-space: nowrap;
  height: 140px;
  top: 60px;
  right: -4px;
  transition: 2s cubic-bezier(0, -0.31, 0.99, 1.24);
  overflow-x: hidden;
}

.NotificationLayout.show {
  width: 750px;
  opacity: 1 !important;
}

.NotificationTextLayout {
  margin-left: 42px;
}

.NotificationText {
  text-align: left;
  font: normal normal normal 28px/33px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  margin: 13px 0px;
  text-wrap: wrap;
  width: 500px !important;
}

.NotificationPath {
  text-align: left;
  font: normal normal normal 24px/28px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  margin: 13px 0px;
}

.total-hours-layout {
  top: 850px;
  padding: 0 35px;
  border: 2px solid #202020;
  display: flex;
  position: fixed;
  flex-direction: row;
  left: 0;
  height: 230px;
  z-index: 10001;
  background-color: black;
  width: 100%;
}


.left-container-recording {
  width: 65%;
  margin-right: 60px;
}

.total-hours-layout-sub-div {
  display: flex;
  column-gap: 62px;
  padding-top: 40px;
}

.total-hours-layout-title {
  color: white;
  font-size: 32px;
  line-height: 35px;
  font-weight: normal;
  line-height: 35px;
  font-family: "Roboto";
  letter-spacing: 0;
  white-space: nowrap;
}

.container-styles {
  background-color: #2c2c2c;
  height: 20px;
  border-radius: 50px;
  margin-top: 32px;
}

.d-flex-s-between {
  display: flex;
  margin-top: 24px;
  justify-content: space-between;
}

.d-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.f-32 {
  font-size: 32px;
  color: white;
}

.hours-span {
  padding: 0 30px;
}

.checkbox-label {
  color: white;
  font-size: 28px;
  margin-left: 40px;
  margin-top: 10px;
}

.filter-styles {
  height: 100%;
  background-color: #de1717;
}

.label-styles {
  padding: 5px;
  color: 'white'
}

.container1 {
  display: flex;
  position: relative;
  padding-left: 35px;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container1 input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 50px;
  width: 50px;
  background-color: #2c2c2c;
}

/* On mouse-over, add a grey background color */
.container1:hover input~.checkmark {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.container1 input:checked~.checkmark {
  background-color: #E1251B;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container1 input:checked~.checkmark:after {
  display: block;
}