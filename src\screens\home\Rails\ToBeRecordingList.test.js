import React from "react";
import { fireEvent, getByTestId, render, screen } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ToBeRecordingList from "./ToBeRecordingList";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => jest.fn(),
}));

jest.mock('react-redux', () => ({
    ...jest.requireActual('react-redux'),
    useDispatch: () => jest.fn(),
}));

jest.mock('../../../store/sagaStore', () => ({
    store: {
        dispatch: jest.fn()
    }
}));

const mocksuccessresponse = {
    "response": {
        "recordings": [
            {
                "channel": {
                    "event": {
                        "name": "Test Event",
                        "group_id": "784942",
                        "timeshift": "4000",
                        "duration": 3600
                    },
                    "image": "http://test-image.png",
                    "group_id": "784942"
                },
                "actions": {
                    "play": {
                        "dashwv": "test-dashwv-url"
                    },
                    "delete": "delete-url"
                },
                "date": "2023-01-01T00:00:00Z",
                "status": "recording"
            }
        ],
        "series": [
            {
                "serie_name": "Test Series",
                "group_id": "12345",
                "image": "http://test-series-image.png",
                "records": {
                    "length": 5
                },
                "status": "recording",
                "actions": {
                    "episodes": "episodes-url"
                },
                "season_id": "s123"
            }
        ],
        "total": 2
    }
};

const mockLockedChannelsList = {
    "response": {
        "groups": [
            { "id": "784942" },
            { "id": "12345" }
        ]
    }
};

describe('ToBeRecordingList page test case', () => {
    beforeEach(() => {
        window.HTMLElement.prototype.scrollIntoView = function () { };
        document.getElementById = jest.fn().mockImplementation((id) => {
            return {
                focus: jest.fn(),
                scrollIntoView: jest.fn()
            };
        });
    });

    test('should render onclick railcard', () => {
        initialState.epg = {
            InprogrssRecordingList: mocksuccessresponse,
            epgChannel: [null, { channelResponse: [{ group_id: "784942" }] }]
        };
        initialState.login = {
            isLoggedIn: { response: { user_token: "test-token" } }
        };
        initialState.initialReducer = {
            appMetaData: {
                translations: JSON.stringify({
                    language: {
                        pe: {
                            "recording_npvr_recorded_label": "Grabando",
                            "recording_npvr_toRecord_label": "Por grabar",
                            "content_data_episodes_label": "episodios",
                            "ribbons_placeholder_PorGrabar_label": "Por grabar"
                        }
                    }
                })
            }
        };
        initialState.player = {
            recordplayerinfo: { response: {} }
        };
        initialState.settingsReducer = {
            lockedChannelsList: mockLockedChannelsList
        };

        localStorage.setItem('region', 'pe');

        const { container } = renderWithState(<ToBeRecordingList />);
        const getbytestid = getByTestId(container, 'rail_card_click0');
        fireEvent.blur(getbytestid);
        fireEvent.focus(getbytestid);
        fireEvent.keyUp(getbytestid, { keyCode: '403' });
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        );
    });

    test('should handle goToMoviesSeries function for series content', () => {
        initialState.epg = {
            InprogrssRecordingList: mocksuccessresponse,
            epgChannel: [null, { channelResponse: [{ group_id: "784942" }] }]
        };
        initialState.login = {
            isLoggedIn: { response: { user_token: "test-token" } }
        };
        initialState.initialReducer = {
            appMetaData: {
                translations: JSON.stringify({
                    language: {
                        pe: {
                            "recording_npvr_recorded_label": "Grabando",
                            "recording_npvr_toRecord_label": "Por grabar",
                            "content_data_episodes_label": "episodios",
                            "ribbons_placeholder_PorGrabar_label": "Por grabar"
                        }
                    }
                })
            }
        };
        initialState.player = {
            recordplayerinfo: { response: {} }
        };
        initialState.settingsReducer = {
            lockedChannelsList: mockLockedChannelsList
        };

        localStorage.setItem('region', 'pe');

        const { container, getAllByRole } = renderWithState(<ToBeRecordingList />);
        const buttons = getAllByRole('button');
        expect(buttons.length).toBeGreaterThan(1);
        const seriesButton = buttons[1];
        fireEvent.focus(seriesButton);
        fireEvent.click(seriesButton);
        expect(require('../../../store/sagaStore').store.dispatch).toHaveBeenCalled();
    });

    test('should render series content with locked channels', () => {
        initialState.epg = {
            InprogrssRecordingList: mocksuccessresponse,
            epgChannel: [null, { channelResponse: [{ group_id: "784942" }] }]
        };
        initialState.login = {
            isLoggedIn: { response: { user_token: "test-token" } }
        };
        initialState.initialReducer = {
            appMetaData: {
                translations: JSON.stringify({
                    language: {
                        pe: {
                            "recording_npvr_recorded_label": "Grabando",
                            "recording_npvr_toRecord_label": "Por grabar",
                            "content_data_episodes_label": "episodios",
                            "ribbons_placeholder_PorGrabar_label": "Por grabar"
                        }
                    }
                })
            }
        };
        initialState.player = {
            recordplayerinfo: { response: {} }
        };
        initialState.settingsReducer = {
            lockedChannelsList: mockLockedChannelsList
        };

        localStorage.setItem('region', 'pe');

        const { container } = renderWithState(
            <ToBeRecordingList title="Test Title" index={0} />
        );
        const lockedChannelIcons = container.querySelectorAll('.locked-channel-icon');
        expect(lockedChannelIcons.length).toBeGreaterThan(0);
    });

    test('should handle title truncation for long titles', () => {
        const longTitlesResponse = {
            response: {
                recordings: [{
                    channel: {
                        event: {
                            name: "This is a very long title that should be truncated because it exceeds 25 characters",
                            group_id: "784942",
                            timeshift: "4000",
                            duration: 3600
                        },
                        image: "http://test-image.png",
                        group_id: "784942"
                    },
                    actions: {
                        play: { dashwv: "test-dashwv-url" },
                        delete: "delete-url"
                    },
                    date: "2023-01-01T00:00:00Z",
                    status: "recording"
                }],
                series: [],
                total: 1
            }
        };

        initialState.epg = {
            InprogrssRecordingList: longTitlesResponse,
            epgChannel: [null, { channelResponse: [{ group_id: "784942" }] }]
        };
        initialState.login = {
            isLoggedIn: { response: { user_token: "test-token" } }
        };
        initialState.initialReducer = {
            appMetaData: {
                translations: JSON.stringify({
                    language: {
                        pe: {
                            "recording_npvr_recorded_label": "Grabando",
                            "recording_npvr_toRecord_label": "Por grabar",
                            "content_data_episodes_label": "episodios",
                            "ribbons_placeholder_PorGrabar_label": "Por grabar"
                        }
                    }
                })
            }
        };
        initialState.player = {
            recordplayerinfo: { response: {} }
        };
        initialState.settingsReducer = {
            lockedChannelsList: { response: { groups: [] } }
        };

        localStorage.setItem('region', 'pe');

        const { container } = renderWithState(<ToBeRecordingList />);
        const titleElements = container.querySelectorAll('.content-title-rec');
        expect(titleElements.length).toBeGreaterThan(0);
        expect(titleElements[0].textContent).toContain('...');
    });

    test('should render empty state when no content is available', () => {
        const emptyResponse = {
            response: {
                recordings: [],
                series: [],
                total: 0
            }
        };

        initialState.epg = {
            InprogrssRecordingList: emptyResponse,
            epgChannel: []
        };
        initialState.login = {
            isLoggedIn: { response: { user_token: "test-token" } }
        };
        initialState.initialReducer = {
            appMetaData: {
                translations: JSON.stringify({
                    language: {
                        pe: {
                            "ribbons_placeholder_PorGrabar_label": "Por grabar"
                        }
                    }
                })
            }
        };
        initialState.settingsReducer = {
            lockedChannelsList: { response: { groups: [] } }
        };

        localStorage.setItem('region', 'pe');
        localStorage.setItem('miscontenidos', 'true');

        const { container } = renderWithState(
            <ToBeRecordingList title="Empty Rail" index={0} />
        );
        const noContentCard = container.querySelector('.nocontent-card');
        expect(noContentCard).toBeTruthy();
    });

    test('should navigate to record player when record play button is clicked', () => {
        initialState.epg = {
            InprogrssRecordingList: mocksuccessresponse,
            epgChannel: [null, { channelResponse: [{ group_id: "784942" }] }]
        };
        initialState.login = {
            isLoggedIn: { response: { user_token: "test-token" } }
        };
        initialState.initialReducer = {
            appMetaData: {
                translations: JSON.stringify({
                    language: {
                        pe: {
                            "recording_npvr_recorded_label": "Grabando",
                            "recording_npvr_toRecord_label": "Por grabar",
                            "content_data_episodes_label": "episodios"
                        }
                    }
                })
            }
        };
        initialState.player = {
            recordplayerinfo: { response: { url: "test-playback-url" } }
        };
        initialState.settingsReducer = {
            lockedChannelsList: { response: { groups: [] } }
        };

        localStorage.setItem('region', 'pe');

        const { container } = renderWithState(<ToBeRecordingList />);
        const episodeCard = getByTestId(container, 'rail_card_click0');
        fireEvent.focus(episodeCard);
        fireEvent(
            episodeCard,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        );
    });
});