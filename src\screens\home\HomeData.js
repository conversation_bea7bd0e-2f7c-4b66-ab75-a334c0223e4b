import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import Banner from './Banner'
import PremiumChannel from './Rails/PremiumChannel'
import './Rails/PremiumChannel.scss'
import './Home.scss'
import RailCard from './Rails/RailCard'
import TopSeries from './Rails/Topseries'
import NavSubMenu from './NavSubMenu'
import { getFirstIndex, getCMSLevelUserV1, getCMSLevelV1 } from '../../store/slices/HomeSlice'
import {
  getEpgChannelBackUp,
  getEpgFilterName,
  getPayWayToken,
  setFromHomeToLive
} from '../../store/slices/EpgSlice'
import VerticalCard from './Rails/VerticalCard'
import ToonKidsCard from './Rails/ToonKidsCard'
import {
  SkeletonScreenloading,
  RailsSkeletonLoading,
  TitleSkeletonLoading
} from '../SkeletonScreenLoading/SkeletonScreenloading'
import Search from '../searchMenu/Search'
import AddSubscriptions from '../appsettings/SubscriptionManagement/AddSubscriptions'
import '../appsettings/SubscriptionManagement/SubscriptionManagements.scss'
import ContinueWatching from './Rails/ContinueWatching'
import FavoriteList from './Rails/FavoriteList'
import RecordingList from './Rails/RecordingList'
import ToBeRecordingList from './Rails/ToBeRecordingList'
import { URL, COMMON_URL } from '../../utils/environment'
import { getEpgChannel, getFavouriteLive } from '../../store/slices/EpgSlice'
import { getLockedChannelsList } from '../../store/slices/settingsSlice'
import MycontentMyList from './Rails/MycontentMyList'
import MyContentContinueWatch from './Rails/MyContentContinueWatch'
import { premiumHeaderImages } from './Rails/premiumHeaderImage'

const HomeData = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const premiumTabValue = useSelector(
    state => state?.homeReducer?.premiumNodeValue
  )
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)

  const levelProfilesData = useSelector(state => state?.homeReducer?.level)
  const levelKidsData = useSelector(state => state?.homeReducer?.levelKids)
  const getFavouriteList = useSelector(state => state?.epg?.favouriteLive)
  const watchFree = useSelector(state => state?.login?.watchFreestate)

  const levelData =
    !userDetails || userDetails?.is_kids === 'false'
      ? levelProfilesData
      : levelKidsData
  const epgVersionForFavourite = sessionStorage.getItem('epgVersion')
  const lastTouch = localStorage.getItem('lasttouch')

  const levelDataModule = useSelector(
    state => state?.homeReducer?.levelV1Response?.response?.modules?.module
  )

  // cmslevelAPI response till component for premium tab

  const levelComponents = levelDataModule?.map(
    each => each?.components?.component
  )

  //Level User's
  const levelUserUrls = []

  // cmsleveruserapi till module
  const levelUserRailData = useSelector(
    state => state?.homeReducer?.levelUserV1Response?.response?.modules?.module
  )

  // cmsleveluserapi till component
  const levelUserComponents = levelUserRailData?.map(
    each => each?.components?.component
  )

  // taking only urls from cms leveruserapi, we have title from cmsapi
  levelUserComponents?.map(url => {
    url.map(each => levelUserUrls?.push({ urlData: each?.properties?.url }))
  })

  const filterTabs = levelData?.filter(
    each => each?.code === props.navbarTabValue
  )

  const filteringData = []
  const levelApiUrlsData = []
  const homePageUrlsData = []

  let flag = 0


  const channelLogoSet = data => {
    return apaAssetsImages[data + '_homeAddon_header_img']
  }

  // we get only  urls in settingleveluser so we use this urls for user specific rails like my list and continue watching,
  const settingLevelUserUrls = () => {
    const urls = levelUserUrls[flag]?.urlData
    flag = flag + 1
    return urls
  }
  let count = 0

  // Premium Channels CMSLevel Logic
  if (premiumTabValue && premiumTabValue?.length) {
    levelComponents?.map(url => {
      url.map((each =>{
        let index = (count += 1)
        each.type === 'Highlight'
          ? filteringData?.push({
              urlData: each?.properties?.url,
              type: each?.type,
              index: index
            })
          : filteringData?.push({
              urlData: each?.properties?.url,
              type: each?.type,
              title: each?.properties?.large,
              index: index
            })
          })
      )
    })

    filteringData.map((each, index) =>
      each.title
        ? levelApiUrlsData?.push({
            byUser: 'premium',
            dataObject: {
              title: each?.title,
              imageData: filteringData[index + 1]?.urlData,
              type: filteringData[index + 1]?.type
            },
            index: each?.index
          })
        : each?.type === 'Highlight'
        ? levelApiUrlsData?.push({
            byUser: 'premium',
            dataObject: {
              imageData: each.urlData,
              type: 'Highlight'
            },
            index: each?.index
          })
        : each?.type === 'Listadoinfinito'
        ? levelApiUrlsData?.push({
            byUser: 'premium',
            dataObject: {
              imageData: each.urlData,
              type: 'Listadoinfinito'
            },
            index: each?.index
          })
        : null
    )

    levelApiUrlsData.map((each, index) =>
      each?.dataObject?.imageData
        ? (homePageUrlsData?.push(each),
          index === 0
            ? homePageUrlsData.push({ dataObject: { type: 'submenu' } })
            : null)
        : homePageUrlsData?.push({
            byUser: 'premium',
            index: each?.index,
            dataObject: {
              title: each?.dataObject?.title,
              type: each?.dataObject?.type,
              imageData: settingLevelUserUrls()
            },
            index: each?.index
          })
    )
  } else {
    filterTabs?.map((each, index) =>
      each?.dataObject?.imageData
        ? (homePageUrlsData?.push(each),
          index === 0
            ? homePageUrlsData.push({ dataObject: { type: 'submenu' } })
            : null)
        : (homePageUrlsData?.push({
            page: each?.page,
            code: each?.code,
            byUser: true,
            index: each?.index,
            dataObject: {
              title: each?.dataObject?.title,
              type: each?.dataObject?.type,
              imageData: settingLevelUserUrls()
            }
          }),index === 0
            && !filterTabs?.some(item => item?.dataObject?.title === 'Mis Favoritos') && homePageUrlsData?.push({
            byUser: true,
            index: each?.index,
            dataObject: {
              title: 'Mis Favoritos', 
            }
          })
            )
    )
  }

  useEffect(() => {
    {
      premiumTabValue?.length
        ? dispatch(
          getCMSLevelV1({
              nodeValue: premiumTabValue,
              userId: userDetails?.user_id
            })
          )
        : null
    }
    dispatch(
      getPayWayToken({
        hks: userDetails?.session_stringvalue,
        user_id: watchFree ? 0 : userDetails?.user_id
      })
    )
  }, [props?.navbarTabValue, premiumTabValue])

  useEffect(() => {
    if (userDetails?.user_id) {
      {
        !premiumTabValue?.length
          ? userDetails?.is_kids === 'true'
            ? dispatch(
              getCMSLevelUserV1({
                  nodeValue: props?.navbarTabValue,
                  userId: userDetails?.user_id,
                  user_token: userDetails?.user_token,
                  type: 'kids'
                })
              )
            : dispatch(
              getCMSLevelUserV1({
                  nodeValue: props?.navbarTabValue,
                  userId: userDetails?.user_id,
                  user_token: userDetails?.user_token
                })
              )
          : dispatch(
            getCMSLevelUserV1({
                nodeValue: premiumTabValue,
                userId: userDetails?.user_id,
                user_token: userDetails?.user_token
              })
            )
      }
      localStorage.setItem('userId', userDetails?.user_id)
    }
  }, [props?.navbarTabValue, userDetails, premiumTabValue])

  useEffect(() => {
    SpatialNavigation.focus()
    addChannelData()
    dispatch(
      getEpgFilterName(
        apilanguage?.playingLive_miniEpg_option_button_fullEpg?.toUpperCase() ??
          'playingLive_miniEpg_option_button_fullEpg'
      )
    )
  }, [])

  useEffect(() => {
    props?.navbarTabValue === 'miscontenidos' ||
    props?.navbarTabValue == 'micontenido_kids' && dispatch(getFirstIndex(homePageUrlsData?.[0]?.index))
  }, [homePageUrlsData])

  const SkeletonLoadingInHomePage = () => {
    return (
      <>
        <div>
          <SkeletonScreenloading />
          <TitleSkeletonLoading />
          <div style={{ display: 'flex' }}>
            <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
          </div>
        </div>
        <TitleSkeletonLoading />
        <div style={{ display: 'flex' }}>
          <RailsSkeletonLoading listsToRender={4} flag={'Vertical'} />
        </div>
      </>
    )
  }

  function addChannelData() {
    let region = localStorage.getItem('region')
    let menuId = sessionStorage.getItem('nodeId')
    let epgVersion = sessionStorage.getItem('epgVersion')
    let soaVersion = sessionStorage.getItem('soaVersion')

    var sevenDayEpgData = []

    const epgDataPromise = new Promise(async (resolve, reject) => {
      const fetchPromises = []

      for (let i = 1; i <= 7; i++) {
        let startDate = getNextDays(i - 2)
        let endDate = getNextDays(i - 1)

        let formattedStartDate = `${startDate.getFullYear()}${
          startDate.getMonth() + 1 < 10
            ? '0' + (startDate.getMonth() + 1)
            : startDate.getMonth() + 1
        }${
          startDate.getDate() < 10
            ? '0' + startDate.getDate()
            : startDate.getDate()
        }00${'00'}00`

        let formattedEndDate = `${endDate.getFullYear()}${
          endDate.getMonth() + 1 < 10
            ? '0' + (endDate.getMonth() + 1)
            : endDate.getMonth() + 1
        }${
          endDate.getDate() < 10 ? '0' + endDate.getDate() : endDate.getDate()
        }23${'00'}00`

        const sevenDaysDataPromise = fetch(
          URL.EPG_CHANNEL_URL +
            `&region=${region}&device_id=${COMMON_URL.device_id}&format=json&quantity=2000&from=0` +
            `&date_from=${formattedStartDate}&date_to=${formattedEndDate}&node_id=${menuId}&epg_version=${epgVersion}&metaData=full&soaVersion=${soaVersion}`
        ).then(async data => {
          let value = await data.json()
          sevenDayEpgData.push({
            key: i,
            channelResponse: value?.response?.channels,
            index: i
          })
        })

        fetchPromises.push(sevenDaysDataPromise)
      }

      await Promise.all(fetchPromises)
        .then(() => {
          var sortedEpgData = []
          sortedEpgData = [...sevenDayEpgData].sort((a, b) => a.index - b.index)
          setTimeout(() => {
            resolve(
              dispatch(getEpgChannel(sortedEpgData)),
              dispatch(getEpgChannelBackUp(sortedEpgData))
            )
          }, 1000)
        })
        .catch(reject)
    })
  }

  const getNextDays = (daysToAdd, currentDate = new Date()) => {
    const nextDate = new Date(currentDate)
    nextDate.setDate(currentDate.getDate() + daysToAdd)
    return nextDate
  }

  //dispatching a favourite list and locked channels API for getting data before navigating to player
  const handleNavigateLive = () => {
    getFavouriteList?.length == 0 &&
      dispatch(
        getFavouriteLive({
          epg_version: epgVersionForFavourite,
          hks: userDetails?.session_stringvalue,
          user_id: watchFree ? 0 : userDetails?.user_id,
          user_token: userDetails?.user_token,
          lasttouch: lastTouch ?? userDetails?.lasttouch?.favorited,
          user_hash: userDetails?.session_userhash
        })
      )
    dispatch(
      getLockedChannelsList({
        hks: userDetails?.session_stringvalue,
        user_hash: watchFree ? 0 : userDetails?.session_userhash
      })
    )
    navigate('/livePlayer', {
      state: { tabValue: props?.navbarTabValue, showControls: 'live' }
    })
  }

  return (
    <div className="homeData-rail">
      {premiumTabValue &&
        premiumTabValue?.length > 0 &&
        props?.navbarTabValue != 'search' &&
        props?.navbarTabValue != 'settings' &&
        channelLogoSet(premiumTabValue) ? (
        <div className='premium-channel-logo'>
          <img
            src={channelLogoSet(premiumTabValue)}
            className='standardChannelLogo'
          />
        </div>
      ) : null}
      {props?.navbarTabValue === 'search' ? (
        <Search />
      ) : props?.nodeLayout == 'guidechannels' ? (
        dispatch(setFromHomeToLive(true)),
        handleNavigateLive()
      ) : (
        <>
          {props?.navbarTabValue === 'miscontenidos' ||
          props?.navbarTabValue == 'micontenido_kids' ? (
            <div className="miscontenidos-background">
              {
                homePageUrlsData[0]
                  ? homePageUrlsData.map((each, index) =>
                      each?.byUser === true &&
                      each?.dataObject?.imageData?.includes('seen') ? (
                        <MyContentContinueWatch
                          id={props?.cardId}
                          dataObject={each?.dataObject?.imageData}
                          title={each?.dataObject?.title}
                          byUser={each?.byUser}
                          backfocusid={props?.backfocusid}
                          fromPage={props?.fromPage}
                          index={index}
                        />
                      ) : each?.dataObject?.title === 'Mis Favoritos' ? (
                        <FavoriteList
                          title={each?.dataObject?.title}
                          backfocusid={props?.backfocusid}
                          index={index}
                        />
                      ) : each?.dataObject?.title === 'Mis grabaciones' ? (
                        <RecordingList
                          title={each?.dataObject?.title}
                          backfocusid={props?.backfocusid}
                          index={index}
                        />
                      ) : each?.dataObject?.title === 'Por grabar' ? (
                        <ToBeRecordingList
                          title={each?.dataObject?.title}
                          backfocusid={props?.backfocusid}
                          index={index}
                        />
                      ) : each?.dataObject?.title === 'Mi lista' ? (
                        <MycontentMyList
                          title={each?.dataObject?.title}
                          backfocusid={props?.backfocusid}
                          index={index}
                        />
                      ) : each?.dataObject?.type === 'Carrouselvertical' ||
                        ('Carrouselhorizontal' &&
                          each?.dataObject?.imageData) ? (
                        <RailCard
                          id={props?.cardId}
                          key={index}
                          dataObject={each?.dataObject?.imageData}
                          type={each?.dataObject?.type}
                          title={each?.dataObject?.title}
                          byUser={each?.byUser}
                          backfocusid={props?.backfocusid}
                          index={index}
                        />
                      ) : null
                    )
                  : null
                // : SkeletonLoadingInHomePage()
              }
            </div>
          ) : (
            <div>
              {/* <NavSubMenu tabValue={props?.navbarTabValue} /> */}

              <div>
                {homePageUrlsData &&
                  homePageUrlsData?.map((each, index) =>
                    each?.dataObject?.type === 'Highlight' ? (
                      <Banner
                        dataObject={each?.dataObject?.imageData}
                        premium={each?.byUser}
                        index={each?.index}
                      />
                    ) : each?.dataObject.type === 'submenu' ? (
                      <NavSubMenu tabValue={props?.navbarTabValue} />
                    ) : each?.dataObject?.type === 'tooncharacter' ? (
                      <ToonKidsCard
                        id={props?.cardId}
                        dataObject={each?.dataObject?.imageData}
                        title={each?.dataObject?.title}
                        index={each?.index}
                      />
                    ) : each?.dataObject?.type === 'premiumchannel' ? (
                      <PremiumChannel
                        id={props?.cardId}
                        dataObject={each?.dataObject?.imageData}
                        title={each?.dataObject?.title}
                        index={each?.index}
                      />
                    ) : each?.dataObject?.type ===
                      'Carrouselverticalampliado' ? (
                      <VerticalCard
                        dataObject={each?.dataObject?.imageData}
                        title={each?.dataObject?.title}
                        id={props?.cardId}
                        index={each?.index}
                      />
                    ) : each?.dataObject?.type === 'Carrousellomastop' ? (
                      <TopSeries
                        id={props?.cardId}
                        dataObject={each?.dataObject?.imageData}
                        title={each?.dataObject?.title}
                        byUser={each?.byUser}
                        index={each?.index}
                      />
                    ) : each?.byUser &&
                      each?.dataObject?.imageData?.includes('seen') ? (
                      <ContinueWatching
                        id={props?.cardId}
                        dataObject={each?.dataObject?.imageData}
                        title={each?.dataObject?.title}
                        byUser={each?.byUser}
                        backfocusid={props?.backfocusid}
                        fromPage={props?.fromPage}
                        index={each?.index}
                        delContinuewatch={props?.delContinuewatch}
                      />
                    ) : each?.dataObject?.type === "planselector" ? (
                      <AddSubscriptions
                        fromHome={true}
                        apiUrl={each?.dataObject?.imageData}
                        index={each?.index}
                        title={each?.dataObject?.title}
                      />
                    ) : (
                      <RailCard
                        id={props?.cardId}
                        index={each?.index}
                        dataObject={each?.dataObject?.imageData}
                        title={each?.dataObject?.title}
                        byUser={each?.byUser}
                        backfocusid={props?.backfocusid}
                        deleteHomeMycontent={props?.deleteHomeMycontent}
                        railIndex={props?.railIndex}
                      />
                    )
                  )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default React.memo(HomeData)
