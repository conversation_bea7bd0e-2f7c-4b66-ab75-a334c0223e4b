import vodMoviesSlice, {
    getVodMoviesData,getVodMoviesSuccess,getVodMoviesError,getVodMoviesCastData,
    getVodMoviesCastSuccess,getVodMoviesCastError,
    getVodMoviesWatchlist,getVodMoviesWatchListSuccess,getVodMoviesWatchlistError,
    addVodMoviesWatchList,addVodMoviesWatchlistSuccess,addVodMoviesWatchListError,
    deleteVodMoviesWatchList,deleteVodMoviesWatchListSuccess,deleteVodMoviesWatchListError} from './VodMoviesSlice';

const initialState = {
        data: [],
        castData: [],
        isLoading: false,
        error: null,
    
        watchList: [],
        addWatchList: {},
        delWatchList: {},
        isGetWatchlistLoading: false,
        isAddWatchlistLoading: false,
        isDelWatchlistLoading: false,
        getWatchlistError: {},
        addWatchlistError: {},
        delWatchlistError: {},
    }

describe('vodMoviesSlice reducer', () => {
    it('it should return initial state', () => {
       expect(vodMoviesSlice(undefined, {})).toEqual(initialState)
    });

    it('should handle vodMovies action',() => {
        expect(vodMoviesSlice(initialState, getVodMoviesData())).toEqual({
            ...initialState,
            isLoading: true
        })
    });

    it('should handle getVodMoviesSuccess action', () => {
        const action = {
            type: getVodMoviesSuccess.type,
            payload: { response: {groups: [
                {
                    "id": "544438",
                    "title": "Lista mortal",
                }
            ]} },
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            data: action.payload.response.groups,
            isLoading: false
        })
    });

    it('should handle getVodMoviesError action', () => {
        const action = {
            type: getVodMoviesError.type,
            payload: 'getVodMoviesError'
        };
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isLoading: false,
            error: action.payload
        })
    });

    it('should handle getVodMoviesCastData action', () => {
        expect(vodMoviesSlice(initialState, getVodMoviesCastData())).toEqual({
            ...initialState,
            isLoading: true
        })
    });

    it('should handle getVodMoviesCastSuccess action', () => {
        const action = {
            type: getVodMoviesCastSuccess.type,
            payload: {response: {group: [
                {
                    "id": "1117111",
                    "title": "Hullraisers",
                }
            ]}}
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isLoading: false,
            castData: action.payload.response.group
        })
    });

    it('should handle getVodMoviesCastError action', () => {
        const action = {
            type: getVodMoviesCastError.type,
            payload: 'castError'
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isLoading: false,
            error: action.payload
        })
    });

    it('should handle getVodMoviesWatchlist action', () => {
        expect(vodMoviesSlice(initialState, getVodMoviesWatchlist())).toEqual({
            ...initialState,
            isGetWatchlistLoading: true
        })
    })

    it('should handle getVodMoviesWatchListSuccess action', () => {
        const action = {
            type: getVodMoviesWatchListSuccess.type,
            payload: {response : {groups: [
                
            ]}}
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            watchList: action.payload.response.groups,
            isGetWatchlistLoading: false,
            addWatchList: {},
            delWatchList: {}
        })
    });

    it('should handle getVodMoviesWatchlistError action', () => {
        const action = {
            type: getVodMoviesWatchlistError.type,
            payload: 'WatchListError'
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isGetWatchlistLoading: false,
            getWatchlistError: action.payload   
        })
    });

    it('should handle addVodMoviesWatchList action', () => {
        expect(vodMoviesSlice(initialState, addVodMoviesWatchList())).toEqual({
            ...initialState,
            isAddWatchlistLoading: true  
        })
    });

    it('should handle addVodMoviesWatchlistSuccess action', () => {
        const action = {
            type: addVodMoviesWatchlistSuccess.type,
            payload: {}
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isAddWatchlistLoading: false,
            addWatchList: action.payload
        })
    });

    it('should handle addVodMoviesWatchListError action', () => {
        const action = {
            type: addVodMoviesWatchListError.type,
            payload: 'addMoviesWatchListError'
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isAddWatchlistLoading: false,
            addWatchlistError: action.payload
        })
    });

    it('should handle deleteVodMoviesWatchList action', () => {
        expect(vodMoviesSlice(initialState, deleteVodMoviesWatchList())).toEqual({
            ...initialState,
            isDelWatchlistLoading: true      
        })
    });

    it('should handle deleteVodMoviesWatchListSuccess action', () => {
        const action = {
            type: deleteVodMoviesWatchListSuccess.type,
            payload: {}
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            delWatchList: action.payload,
            isDelWatchlistLoading: false
        })
    });

    it('should handle deleteVodMoviesWatchListError action', () => {
        const action = {
            type: deleteVodMoviesWatchListError.type,
            payload: 'deleteVodMoviesWatchListError'
        }
        expect(vodMoviesSlice(initialState, action)).toEqual({
            ...initialState,
            isDelWatchlistLoading: false,
            delWatchlistError: action.payload
        })
    })
})