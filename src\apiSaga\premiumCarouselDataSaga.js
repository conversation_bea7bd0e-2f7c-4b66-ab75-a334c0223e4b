import { call, put, takeEvery } from '@redux-saga/core/effects'
import { URL, COMMON_URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  premiumCarouselDataSuccess,
  premiumCarouselDataFailure
} from '../store/slices/HomeSlice'

function* fetchPremiumCarouselSaga({ payload }) {
  try {
    yield call(
      request,
      URL.PREMIUM_URL +
        `${payload}` +
        '&api_version=v5.93&device_category=STB&device_model=androidTV&device_type=ptv&device_manufacturer=ZTE&authpn=tataelxsi&authpt=vofee7ohhecai',

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(premiumCarouselDataSuccess(response))
        },
        onError(error) {
          store.dispatch(premiumCarouselDataFailure(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* premiumCarouselDataSaga() {
  yield takeEvery('homeSlice/premiumCarouselData', fetchPremiumCarouselSaga)
}
