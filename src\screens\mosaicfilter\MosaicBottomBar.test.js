import React from 'react'
import { render, screen } from '@testing-library/react'
import MosaicBottomBar from './MosaicBottomBar'
import { MemoryRouter } from 'react-router-dom'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'


jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}))

describe('MosaicBottomBar Component', () => {
  beforeEach(() => {
    // Clear previous global objects
    delete global.tizen
  })

  test('renders MosaicBottomBar UI elements', () => {
    render(
      <MemoryRouter>
        <MosaicBottomBar />
      </MemoryRouter>
    )

    expect(screen.getByAltText('complete guide')).toBeInTheDocument()
    expect(screen.getByText(/VER CANALES EN GUÍA COMPLETA/i)).toBeInTheDocument()
  })

  test('calls handleLgkey for keyup when tizen is undefined and keycode is 406', () => {
    const navigateMock = jest.fn()
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockReturnValue(navigateMock)

    render(
      <MemoryRouter>
        <MosaicBottomBar />
      </MemoryRouter>
    )

    const keyEvent = new KeyboardEvent('keyup', { keyCode: 406 })
    document.dispatchEvent(keyEvent)

    expect(navigateMock).toHaveBeenCalledWith('/livePlayer')
  })

  test('calls handleLgkey for keyup when tizen is undefined and keycode is 8', () => {
    const navigateMock = jest.fn()
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockReturnValue(navigateMock)

    render(
      <MemoryRouter>
        <MosaicBottomBar />
      </MemoryRouter>
    )

    const keyEvent = new KeyboardEvent('keyup', { keyCode: 8 })
    document.dispatchEvent(keyEvent)

    expect(navigateMock).toHaveBeenCalledWith('/livePlayer')
  })

  test('calls handlesamsungkey if tizen is defined and yellow key pressed', () => {
    const navigateMock = jest.fn()
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockReturnValue(navigateMock)

    // Mock Tizen global object
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: () => ({ code: 1234 })
      }
    }

    render(
      <MemoryRouter>
        <MosaicBottomBar />
      </MemoryRouter>
    )

    const keyEvent = new KeyboardEvent('keyup', { keyCode: 1234 })
    document.dispatchEvent(keyEvent)

    expect(navigateMock).toHaveBeenCalledWith('/livePlayer')
  })

  test('does not navigate if an unrelated key is pressed', () => {
    const navigateMock = jest.fn()
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockReturnValue(navigateMock)

    render(
      <MemoryRouter>
        <MosaicBottomBar />
      </MemoryRouter>
    )

    const keyEvent = new KeyboardEvent('keyup', { keyCode: 500 })
    document.dispatchEvent(keyEvent)

    expect(navigateMock).not.toHaveBeenCalled()
  })
})
