export const playerConfig = {
  key: 'fc3afd27-8180-4ac4-80ef-dbb6854f9637', //older-key for ref d0584d33-f55f-4af6-8625-8b967ea115ae
  ui: false,
  tweaks: {
    file_protocol: true,
    app_id: 'com.bitmovin.claro.smarttvapp'
  },
  playback: {
    muted: true
  },
  ...((CURRENT_PLATFORM != 'netrange' || CURRENT_PLATFORM != 'zeasn') && {
    analytics: {
      key: '6dc21e2a-820a-4b2f-b7d1-dc94e252e49a',
      config: {
        origin: 'com.bitmovin.claro.smarttvapp'
      }
    }
  }),
  network: {
    retryHttpRequest: function (type, response) {
      // delay the retry by 1 second
      return new Promise(function (resolve) {
        setTimeout(function () {
          resolve(response.request)
        }, 1000)
      })
    }
  }
}
