const path = require('path')
const webpack = require('webpack')
const dotenv = require('dotenv')
const fs = require('fs')

const HtmlWebpackPlugin = require('html-webpack-plugin')
const ExtractCSSChunksPlugin = require('extract-css-chunks-webpack-plugin')

module.exports = env => {
  const envFile = dotenv.config().parsed

  const currentPath = path.join(__dirname)
  const basePath = currentPath + '/.env'
  const envPath = basePath + '.' + env.variant
  const finalPath = fs.existsSync(envPath) ? envPath : basePath
  const fileEnv = dotenv.config({ path: finalPath }).parsed
  const envKeys = Object.keys(fileEnv).reduce((prev, next) => {
    prev[`${next}`] = JSON.stringify(fileEnv[next])
    return prev
  }, {})

  return {
    mode: 'development',
    entry: './src/index.js',
    output: {
      path: path.join(__dirname, '/build'),
      filename: 'bundle.[contenthash].js',
      clean: true
    },
    devServer: {
      open: {
        app: {
          name: 'Google Chrome'
        }
      },
      historyApiFallback: true,
      static: {
        directory: path.join(__dirname + '/tv')
      },
      client: {
        overlay: {
          warnings: false,
          errors: true
        }
      }
    },

    module: {
      rules: [
        {
          test: /\.?js$/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react'],
              generatorOpts: { compact: false }
            }
          }
        },
        {
          test: /\.svg$/,
          use: {
            loader: 'svg-url-loader'
          }
        },
        // {
        //   test: /\.css$/,
        //   use: ['style-loader', 'css-loader'],
        // },
        {
          test: /\.(sa|sc|c)ss$/,
          use: [
            {
              loader: ExtractCSSChunksPlugin.loader,
              options: {
                hot: true,
                publicPath: path.join(__dirname, '/build') // Replace with your actual publicPath
              }
            },
            'css-loader',
            {
              loader: 'sass-loader',
              options: {
                sourceMap: true
              }
            }
          ]
        },
        {
          test: /\.svg$/,
          use: {
            loader: 'svg-url-loader'
          }
        }
      ]
    },

    optimization: {
      usedExports: true
    },

    plugins: [
      new webpack.DefinePlugin({
        'global.type': JSON.stringify(env.tv),
        'build.environment': JSON.stringify(env.environment),
        'build.variant': new webpack.DefinePlugin(envKeys)
      }),
      new HtmlWebpackPlugin({
        template: path.join(__dirname, 'src', 'index.html')
      }),
      new ExtractCSSChunksPlugin({
        ignoreOrder: true
      })
    ]
  }
}
