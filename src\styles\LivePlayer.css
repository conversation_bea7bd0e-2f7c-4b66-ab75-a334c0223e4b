@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

.custom-player-bar{
    display: flex;
    justify-content: center;
    gap:100px;
    align-items: center;
    width: 1194px;
    background-color: transparent;
    margin-top: 42vw;
    position: absolute;
    }

.video {
    width: 100vw;
    height: 100vh;
    position: fixed;
  }
  
.custom-player-name{
text-align: center;
font:  22px/25px Roboto;
letter-spacing: 0px;
color: #FFFFFF;
text-transform: uppercase;
opacity: 1;
}

.custom-player-item{
 text-align: center;
}

.timecontrols {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 18rem;
 
  }
  
  .time_progressbarContainer {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    border-radius: 4px;
    opacity: 1;
    width: 1298px;
    height: 12px;
    z-index: 30;
    position: relative;
    margin: 0 20px;
    display: flex;
  }
  
  .time_progressBar {
    border-radius: 4px;
    background-color: #981C15;
    height: 12px;
  }
  
  .controlsTime {
    font: normal normal normal 26px/20px Roboto;
    letter-spacing: 0px;
    color: #DDDDDD;
    opacity: 1;
  }

  .time_progressDot{
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #C60000 ;
    margin-left: -6px;
    margin-top: -3px;
  }

  