import React from 'react'
import { render, fireEvent, queryByAttribute } from '@testing-library/react/'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import { BrowserRouter as Router, useLocation } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import { fromJS } from 'immutable'
import GeneralErrorScreen from './GeneralErrorScreen'

const initialState = fromJS({})
const mockStore = configureStore([])
const history = createHistory()
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
)
export const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}

// Mocking useLocation with different return values for different tests
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: jest.fn()
}))

describe('landing page test', () => {
  beforeEach(() => {})
  test('it should render the landing Page', async () => {
    window.SpatialNavigation = {
      focus: jest.fn(),
      getCurrentPosition: jest.fn()
      // Add more mock methods or properties as needed
    }
    require('react-router-dom').useLocation.mockReturnValue({
      state: {
        vcardType: 'series'
      }
    })
    const wrapper = await renderWithState(<GeneralErrorScreen />)
    fireEvent.keyDown(wrapper.container, {
      key: 'ArrowDown',
      code: 'ArrowDown',
      keyCode: 8,
      charCode: 8
    })
  })
  test('it should render the landing Page', async () => {
    window.SpatialNavigation = {
      focus: jest.fn(),
      getCurrentPosition: jest.fn()
      // Add more mock methods or properties as needed
    }
    require('react-router-dom').useLocation.mockReturnValue({
      state: {
        vcardType: 'movies'
      }
    })
    jest.mock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useLocation: () => ({
        state: {
          vcardType: 'movies'
        }
      })
    }))
    const wrapper = await renderWithState(<GeneralErrorScreen />)
    fireEvent.keyDown(wrapper.container, {
      key: 'ArrowDown',
      code: 'ArrowDown',
      keyCode: 8,
      charCode: 8
    })
    const buttonClick = wrapper.container.querySelector('.accept-button')
    fireEvent.focus(buttonClick)
    fireEvent(
      buttonClick,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })
})
