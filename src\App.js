/* Imports */
import React, { lazy, Suspense } from 'react'
import { useSelector } from 'react-redux'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'
/* Styles */
import './App.css'
import '.././src/styles/Global.css'
import '.././src/styles/CommonComponent.css'
const LandingPage = lazy(() => import('./screens/loginregister/landing'))
const SplashScreen = lazy(() =>
  import('./../src/screens/splash&welcome/splashScreen')
)
const WelcomeScreen = lazy(() =>
  import('./../src/screens/splash&welcome/welcomeScreen')
)
const Loginpage = lazy(() => import('./screens/loginregister/Loginpage'))
const Loginpagemail = lazy(() =>
  import('./screens/loginregister/Loginpagemail')
)
const LoginPassword = lazy(() =>
  import('./screens/loginregister/LoginPassword')
)
const LoginTermsAndCondition = lazy(() =>
  import('./screens/loginregister/LoginTermsAndCondition')
)
const PrivacyAndPolicy = lazy(() =>
  import('./screens/loginregister/PrivacyAndPolicy')
)
const Register = lazy(() => import('./screens/loginregister/Register'))
const Registermail = lazy(() => import('./screens/loginregister/Registermail'))
const RegisterPassword = lazy(() =>
  import('./screens/loginregister/Registerpassword')
)
const LoginForgotPassword = lazy(() =>
  import('./screens/loginregister/LoginForgotPassword')
)
const LoginForgotPasswordMail = lazy(() =>
  import('./screens/loginregister/LoginForgotPasswordMail')
)
const LoginForgotPasswordMessage = lazy(() =>
  import('./screens/loginregister/LoginForgotPasswordMessage')
)
import { store } from './store/sagaStore'
import PublicRoutes from './routes/publicRoutes'
import ProtectedRoutes from './routes/ProtectedRoutes'
const ManagePayments = lazy(() =>
  import('./screens/appsettings/MyAccounts/ManagePayments')
)
const AddPaymentsMobile = lazy(() =>
  import('./screens/appsettings/MyAccounts/AddPaymentMobile')
)
const AddPaymentTelephone = lazy(() =>
  import('./screens/appsettings/MyAccounts/AddPaymentTelephone')
)
const SecurityPin = lazy(() =>
  import('./screens/appsettings/HelpAndSettings/SecurityPin')
)
const CreateSecurityPin = lazy(() =>
  import('./screens/appsettings/HelpAndSettings/CreateSecurityPin')
)
const ActiveSubscriptions = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/ActiveSubscriptions')
)
const AddSubscriptions = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/AddSubscriptions')
)
const CancelSubscription = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/CancelSubscription')
)
import Home from './screens/home/<USER>'
const LivePlayer = lazy(() => import('./screens/bitmovinPlayer/LivePlayer'))
const VodPlayer = lazy(() => import('./screens/bitmovinPlayer/VodPlayer'))
import PremiumChannelSubscriptionFlow from './screens/home/<USER>'
import EpConfirmation from './screens/VodSeries/EpConfirmation'
import { EpMoreInfo } from './screens/VodSeries/EpMoreInfo'
import ErrorEventModule from './screens/talentSearch/ErrorEventModule'
const RecordingDelete = lazy(() =>
  import('./screens/home/<USER>/RecordingDelete')
)
import ChannelDownErrorHandler from './screens/epg/EpgMainPage/ChannelDownErrorHandler'
import { getChannelData } from './store/slices/PlayerSlice'
const RecordingEpisodeList = lazy(() =>
  import('./screens/home/<USER>/RecordingEpisodeList')
)
const PurchaseHours = lazy(() =>
  import('./screens/home/<USER>/purchaseHours')
)

const MyDevices = lazy(() =>
  import('./screens/appsettings/HelpAndSettings/MyDevices')
)
const CancelSubscriptionSuccess = lazy(() =>
  import(
    './screens/appsettings/SubscriptionManagement/CancelSubscriptionSuccess'
  )
)
const CancelSubscriptionError = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/CancelSubscriptionError')
)
const VodMovies = lazy(() => import('./screens/VodMovies/VodMovies'))
const ViewPayments = lazy(() =>
  import('./screens/appsettings/MyAccounts/ViewPayments')
)
const SubscriptionViewDetails = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/SubscriptionViewDetails')
)
const VodSeries = lazy(() => import('./screens/VodSeries/VodSeries'))

const AddPayments = lazy(() =>
  import('./screens/appsettings/MyAccounts/Checkout')
)
const MosaicFilter = lazy(() => import('./screens/mosaicfilter/MosaicFilter'))
const ProgramDetails = lazy(() =>
  import('./screens/programdetails/ProgramDetails')
)
const PinConfirmation = lazy(() =>
  import('./screens/appsettings/HelpAndSettings/PinConfirmation')
)
const AddProfile = lazy(() => import('./screens/Profiles/AddProfile'))
const WatchingProfile = lazy(() => import('./screens/Profiles/WatchingProfile'))
const ChooseProfile = lazy(() => import('./screens/Profiles/ChooseProfileImg'))
const DeleteProfile = lazy(() => import('./screens/Profiles/DeleteProfile'))
const EditProfile = lazy(() => import('./screens/Profiles/EditProfile'))
const WatchProfileEdit = lazy(() =>
  import('./screens/Profiles/EditWatchProfile')
)
const Search = lazy(() => import('./screens/searchMenu/Search'))
const SubscriptionContract = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/SubscribeNonContract')
)
const MobileOTPpage = lazy(() =>
  import('./screens/appsettings/MyAccounts/MobileOTPpage')
)
const FinishSubsription = lazy(() =>
  import('./screens/appsettings/MyAccounts/FinishSubscription')
)
const AddTelephoneViaWeb = lazy(() =>
  import('./screens/appsettings/MyAccounts/AddTelephoneViaWeb')
)
const LockedChannelList = lazy(() =>
  import('./screens/appsettings/HelpAndSettings/LockedChannelList')
)
const MycontentDelete = lazy(() =>
  import('./screens/home/<USER>/MycontentDelete')
)
import CommonLoading from './screens/CommonLoading'
import RegTermsAndConditions from './screens/loginregister/RegTermsAndConditions'
/*****GLBOCK****/
const BuySubscriptionNew = lazy(() =>
  import('./screens/appsettings/SubscriptionManagement/BuySubscriptionNew')
)
//const PaymentMethods = lazy(() => import('./screens/appsettings/MyAccounts/PaymentMethods'));
const PaymentMethods = lazy(() =>
  import('./screens/appsettings/MyAccounts/PaymentMethodsNew')
)
/*****GLBOCK****/
const Settings = lazy(() => import('./screens/appsettings/Settings'))
const ProfileSettingsControl = lazy(() =>
  import('./screens/appsettings/ProfileSettingsControl')
)
const ActionScreen = lazy(() => import('./screens/appsettings/ActionScreen'))
const ChangePin = lazy(() => import('./screens/appsettings/ChangePin'))
const DeactivateControlPin = lazy(() =>
  import('./screens/appsettings/DeactivateControlPin')
)
const CheckSecurityPin = lazy(() =>
  import('./screens/appsettings/CheckSecurityPin')
)
const ContentClassification = lazy(() =>
  import('./screens/appsettings/ContentClassification')
)
const PinCreationSuccess = lazy(() =>
  import('./screens/appsettings/PinCreationSuccess')
)
import VideoTutorial from './screens/VideoTutorial/VideoTutorial'
import ShowReminder from './screens/ShowReminder/ShowReminder'
import GeneralErrorScreen from './screens/VodSeries/GeneralErrorScreen'
import LanguageSelection from './screens/appsettings/LanguageSelection'
import MultiSubscription from './screens/appsettings/SubscriptionManagement/MultiSubscription'
import SubscriptionCallPage from './screens/appsettings/SubscriptionManagement/SubscriptionCallPage'
import NewPlanSelector from './screens/appsettings/SubscriptionManagement/NewPlanSelector'
import AvatarServiceFailureScreen from './screens/appsettings/AvatarServiceFailureScreen'
import PreWelcomeScreen from './screens/splash&welcome/preWelcomeScreen'
const ChangePaymentConfirmationScreen = lazy(() =>
  import('./screens/appsettings/MyAccounts/ChangePaymentConfirmationScreen')
)
// import NetworkDetector from './screens/networkdisconnect/networkdetector'
require('./screenlog.min')
// const isScreeenLogRequired = false
// if (isScreeenLogRequired) {
//   screenLog.init()
// }
// //Below empty console logs are added to remove the debug logs in all pages for increasing the loading performance
const isConsoleLogsRequired = true
if (isConsoleLogsRequired) {
  console.error = () => {
    //empty console logs are added to remove the error logs
  }
  console.debug = () => {
    //empty console logs are added to remove the debug logs
  }
  console.log = () => {
    //empty console logs are added to remove the console logs
  }
  console.warn = () => {
    //empty console logs are added to remove the warning logs
  }
}

const RouteNav = () => {
  const loadingState = useSelector(state => state?.login?.loading)

  return (
    <div>
      <div>
        {loadingState && <CommonLoading />}
        <ShowReminder />
        <Suspense>
          <Routes>
            <Route element={<ProtectedRoutes />}>
              <Route exact path="/home" element={<Home />} />
              <Route
                exact
                path="/channelerrorpage"
                element={<ChannelDownErrorHandler />}
              />
              <Route exact path="/mosaicfilter" element={<MosaicFilter />} />
              <Route
                exact
                path="/programdetails"
                element={<ProgramDetails />}
              />
              <Route
                exact
                path="/PinConfirmation"
                element={<PinConfirmation />}
              />
              <Route exact path="/movies" element={<VodMovies />} />
              <Route exact path="/series" element={<VodSeries />} />
              <Route exact path="/search" element={<Search />} />
              <Route
                exact
                path="/alertWarning"
                element={<ErrorEventModule />}
              />
              <Route exact path="/deletecard" element={<MycontentDelete />} />
              <Route exact path="/livePlayer" element={<LivePlayer />} />
              <Route exact path="/vodPlayer" element={<VodPlayer />} />
              <Route
                exact
                path="/premiumSubscription"
                element={<PremiumChannelSubscriptionFlow />}
              />
              <Route
                exact
                path="/episodescreen"
                element={<RecordingEpisodeList />}
              />
              <Route
                exact
                path="/deleterecording"
                element={<RecordingDelete />}
              />
              <Route
                path="/EPconfirmation"
                element={<EpConfirmation />}
              ></Route>
              <Route path="/EpMoreInfo" element={<EpMoreInfo />}></Route>
              <Route
                path="/GeneralError"
                element={<GeneralErrorScreen />}
              ></Route>
              <Route path="/settings" element={<Settings />}></Route>
              <Route
                path="/service-error"
                element={<AvatarServiceFailureScreen />}
              ></Route>
              <Route
                path="/settings/profile-settings"
                element={<ProfileSettingsControl />}
              ></Route>
              <Route
                path="/settings/profile-settings/choose-language"
                element={<LanguageSelection />}
              ></Route>
              <Route
                path="/settings/actionScreenSettings"
                element={<ActionScreen />}
              ></Route>
              <Route
                path="/settings/profile-settings/change-pin"
                element={<ChangePin />}
              ></Route>
              <Route
                path="/settings/profile-settings/deactivate-pin"
                element={<DeactivateControlPin />}
              ></Route>
              <Route
                path="/settings/profile-settings/check-pin"
                element={<CheckSecurityPin />}
              ></Route>
              <Route
                path="/settings/profile-settings/content-classification"
                element={<ContentClassification />}
              ></Route>
              <Route
                path="/pinCreationSuccess"
                element={<PinCreationSuccess />}
              ></Route>
              <Route
                path="/multiSubscription"
                element={<MultiSubscription />}
              ></Route>
              <Route
                path="/subScriptionCallPage"
                element={<SubscriptionCallPage />}
              ></Route>

              {/* Inside My settings - my accounts */}
              <Route
                path="/my-settings/my-Accounts/manage-payments"
                element={<ManagePayments />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/payments-method"
                element={<PaymentMethods />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/mobile"
                element={<AddPaymentsMobile />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/mobile/otp"
                element={<MobileOTPpage />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/payment/finish-subscription"
                element={<FinishSubsription />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/landline/finish-subscription"
                element={<AddTelephoneViaWeb />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/confirmation-screen"
                element={<ChangePaymentConfirmationScreen />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/telephone"
                element={<AddPaymentTelephone />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/addPaymentMethods"
                element={<AddPayments />}
              />
              <Route
                path="/my-settings/my-Accounts/manage-payments/view-payments"
                element={<ViewPayments />}
              />

              {/* Inside My settings - my subscription */}
              <Route
                path="/my-settings/my-subscriptions/add-subscriptions"
                element={<AddSubscriptions />}
              />
              <Route
                path="/my-settings/my-subscriptions/new-plan-selector"
                element={<NewPlanSelector />}
              />
              <Route
                path="/my-settings/my-subscriptions/add-subscriptions/subscribe-new"
                element={<BuySubscriptionNew />}
              />
              <Route
                path="/my-settings/my-subscriptions/add-subscriptions/viewDetails"
                element={<SubscriptionViewDetails />}
              />
              <Route
                path="/my-settings/my-subscriptions/active-subscriptions"
                element={<ActiveSubscriptions />}
              />
              <Route
                path="/my-settings/my-subscriptions/cancel-subscription"
                element={<CancelSubscription />}
              />
              <Route
                path="/my-settings/my-subscriptions/cancel-subscription/success"
                element={<CancelSubscriptionSuccess />}
              />
              <Route
                path="/my-settings/my-subscriptions/cancel-subscription/error"
                element={<CancelSubscriptionError />}
              />
              <Route
                path="/my-settings/my-subscriptions/non-contract"
                element={<SubscriptionContract />}
              />

              {/* Inside My settings - help and settings */}
              <Route
                path="/my-settings/help-And-Settings/my-devices"
                element={<MyDevices />}
              />
              <Route
                path="/my-settings/help-And-Settings/security-pin"
                element={<SecurityPin />}
              />
              <Route
                path="/my-settings/help-And-Settings/security-pin/configure"
                element={<CreateSecurityPin />}
              />
              <Route
                path="/my-settings/help-And-Settings/parental-control/locked-channels"
                element={<LockedChannelList />}
              />
            </Route>

            <Route element={<PublicRoutes />}>
              <Route
                exact
                path="/"
                element={<SplashScreen screen="splash" />}
              />
              <Route
                exact
                path="/videoTutorial"
                element={<VideoTutorial screen="videoTutorial" />}
              />
              <Route
                exact
                path="/landing"
                element={<LandingPage screen="login" />}
              />
              <Route
                exact
                path="/register"
                element={<Register screen="register" />}
              />
              <Route
                exact
                path="/Terms-and-Conditons"
                element={<RegTermsAndConditions screen="termsAndConditions" />}
              />
              <Route
                exact
                path="/login-Terms-and-Conditons"
                element={
                  <LoginTermsAndCondition screen="logintermsAndConditions" />
                }
              />
              <Route
                exact
                path="/privacy-and-policy"
                element={<PrivacyAndPolicy screen="privacAndpolicy" />}
              />
              <Route
                exact
                path="/signin"
                element={<Loginpage screen="signin" />}
              />
              <Route
                exact
                path="/loginmail"
                element={<Loginpagemail screen="signin" />}
              />
              <Route
                exact
                path="/regemail"
                element={<Registermail screen="regemail" />}
              />
              <Route
                exact
                path="/loginpassword"
                element={<LoginPassword screen="password" />}
              />
              <Route
                exact
                path="/loginforgotpassword"
                element={<LoginForgotPassword screen="forgotpassword" />}
              />
              <Route
                exact
                path="/loginForgotPasswordMail"
                element={
                  <LoginForgotPasswordMail screen="LoginForgotPasswordMail" />
                }
              />
              <Route
                exact
                path="/loginForgotPasswordMessage"
                element={
                  <LoginForgotPasswordMessage screen="loginForgotPasswordMessage" />
                }
              />
              <Route
                exact
                path="/regpassword"
                element={<RegisterPassword screen="regpassword" />}
              />
              <Route
                exact
                path="/welcome"
                element={<WelcomeScreen screen="welcome" />}
              />
               <Route
                exact
                path="/prewelcome"
                element={<PreWelcomeScreen screen="preWelcome" />}
              />
              <Route
                exact
                path="/addprofile"
                element={<AddProfile screen="addprofile" />}
              />
              <Route
                exact
                path="/chooseprofile"
                element={<ChooseProfile screen="chooseprofile" />}
              />
              <Route
                exact
                path="/watchprofile"
                element={<WatchingProfile screen="watchprofile" />}
              />
              <Route
                exact
                path="/deleteprofile"
                element={<DeleteProfile screen="deleteprofile" />}
              />
              <Route
                exact
                path="/watcheditprofile"
                element={<WatchProfileEdit screen="watcheditprofile" />}
              />
              <Route
                exact
                path="/editprofile"
                element={<EditProfile screen="editprofile" />}
              />
              <Route exact path="/gethour" element={<PurchaseHours />} />
            </Route>
          </Routes>
        </Suspense>
      </div>
    </div>
  )
}

const App = props => {
  const isDisconnected = props?.data
  return (
    !isDisconnected && (
      <Router history={history}>
        <RouteNav />
      </Router>
    )
  )
}

export default App
