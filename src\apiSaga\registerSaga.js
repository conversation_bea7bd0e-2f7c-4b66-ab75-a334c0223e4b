import { call, takeEvery } from '@redux-saga/core/effects'
import { store } from '../store/sagaStore'
import {
  getRegisterError,
  getRegisterPopup,
  getRegisterSuccess,
  getWatchFree,
  setSkeltonLoading
} from '../store/slices/login'
import { URL } from '../utils/environment'
import { request } from '../utils/request'

function* getRegisterApi({ payload }) {
  let Username = localStorage.getItem('username')
  const region = localStorage.getItem('region')
  store.dispatch(setSkeltonLoading(true))

  try {
    yield call(
      request,
      `${URL.REGISTER_URL}&email=${Username}&password=${payload?.password}&region=${region}&HKS=${payload?.hks}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          localStorage.setItem('token', response.response.user_token)
          localStorage.setItem('user_session', response?.response?.user_session)
          localStorage.setItem('hks',response?.response?.hks)
          localStorage.setItem('register_user', true)
          store.dispatch(getRegisterSuccess(response))
          store.dispatch(getRegisterPopup(true))
          store.dispatch(getWatchFree())
          store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(getRegisterError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
    store.dispatch(setSkeltonLoading(false))
  }
}

export default function* registerSaga() {
  yield takeEvery('login/getRegister', getRegisterApi)
}
