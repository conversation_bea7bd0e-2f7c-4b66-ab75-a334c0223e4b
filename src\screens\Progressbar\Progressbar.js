import React from 'react'
import './Progressbar.scss'
const ProgressBar = props => {
  const {
    isLoading = props.percent === 'loading',
    percent,
    size = 'small',
    showInfo = false,
    smallProgress = props?.smallprogress,
    seriesLargeProgress = props?.serieslargeProgress,
    sliderWidth
  } = props

  return (
    <div
      style={props?.style}
      className={
        smallProgress
          ? 'progress-small-outer'
          : seriesLargeProgress
          ? 'Serie-large-progress'
          : 'progress-outer'
      }
    >
      <div
        style={{ width: sliderWidth }}
        className={
          seriesLargeProgress
            ? 'Progres-Series'
            : `progress ${size ? 'progress--' + size : ''} ${
                isLoading ? 'progress--' + 'loading' : ''
              }`
        }
      >
        <div
          className={
            seriesLargeProgress
              ? 'largebar-progress'
              : smallProgress
              ? 'progress-small-red-bar'
              : 'progress-bar'
          }
          style={{ width: percent + '%', maxWidth: '100%' }}
        ></div>
      </div>
    </div>
  )
}

export default ProgressBar
