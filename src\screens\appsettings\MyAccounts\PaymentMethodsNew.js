import React, { useEffect, useState, useCallback } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './PaymentMethodsNew.scss'
import { BackButtonComponent } from '../../../components/CommonComponent'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'

const PaymentMethods = () => {
  const navigate = useNavigate()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const [paymentFocusImage, setPaymentFocusImage] = useState(null)
  const [isCreditCardPayment, setIsCreditCardPayment] = useState(false)
  const [creditCardDetails, setCreditCardDetails] = useState([])

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const workflowResponse = useSelector(
    state => state?.settingsReducer?.paywayWorkFlow?.response
  )
  const checkEnabledPaymentMethods = useSelector(
    state =>
      state?.settingsReducer?.paywayWorkFlow?.response?.list ??
      state?.settingsReducer?.paywayWorkFlow?.response?.workflow?.listBuyLinks
  )
  const getCreditCardsData = useSelector(
    state => state?.settingsReducer?.getCardDetails
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleAddPayments = (e, selectedPaymentMethod) => {
    e.preventDefault()
    if (
      workflowResponse?.selectedPaymentMethod == selectedPaymentMethod &&
      workflowResponse?.hasSavedPayway == '1'
    ) {
      if (selectedPaymentMethod == 'claropagosgate') {
        setIsCreditCardPayment(true)
      } else {
        handleGoPreviousPage(state)
      }
    } else {
      navigate('/my-settings/my-Accounts/manage-payments/addPaymentMethods', {
        state: {
          paymentMethod: selectedPaymentMethod,
          previousPageName: state?.previousPageName,
          previousPage: state?.previousPage,
          vodData: state?.vodData,
          dataId: state?.dataId ?? state?.data,
          pageName: state?.pageName,
          returnPage: state?.returnPage,
          sendIndex: state?.sendIndex,
          subscribeInfoData: state?.subscribeInfoData,
          selectedPaymentIndex: state?.selectedPaymentIndex,
          selectedPaymentMethodName: state?.selectedPaymentMethodName,
          groupId: state?.groupId
        }
      })
    }
  }

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009 || keycode === 8) {
      handleGoPreviousPage(state)
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || keycode == 89) {
      handleGoPreviousPage(state)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  useEffect(() => {
    getCreditCardsData &&
      Object.keys(getCreditCardsData)?.length > 0 &&
      setCreditCardDetails(getCreditCardsData?.data?.tarjetas?.data)
  }, [getCreditCardsData])

  useEffect(() => {
    pushScreenViewEvent({screenName:'payment_methods',screenData:userDetails,prevScreenName:state?.pageName })
   }, [])

  //IT LOOKS CODE MORE READBLE
  const handleGoPreviousPage = state => {
    if (
      state?.pageName ==
      '/my-settings/my-subscriptions/add-subscriptions/subscribe-new'
    ) {
      navigate(
        '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
        {
          state: {
            previousPageName: state?.previousPageName,
            pageName: state?.pageName,
            vodData: state?.vodData,
            dataId: state?.dataId ?? state?.data,
            returnPage: state?.returnPage,
            subscribeInfoData: state?.subscribeInfoData,
            sendIndex: state?.sendIndex,
            previousPage: state?.previousPage,
            selectedPaymentIndex: state?.selectedPaymentIndex
          }
        }
      )
    } else {
      navigate('/home')
    }
  }

  const handleFocus = index => {
    isCreditCardPayment
      ? setPaymentFocusImage(`creditCard${index}`)
      : setPaymentFocusImage(`paymentImage${index}`)
  }

  const handleCardPayment = type => {
    navigate('/my-settings/my-Accounts/manage-payments/addPaymentMethods', {
      state: {
        paymentMethod: 'claropagosgate',
        previousPageName: state?.previousPageName,
        previousPage: state?.previousPage,
        vodData: state?.vodData,
        dataId: state?.dataId ?? state?.data,
        pageName: state?.pageName,
        returnPage: state?.returnPage,
        sendIndex: state?.sendIndex,
        subscribeInfoData: state?.subscribeInfoData,
        selectedPaymentIndex: state?.selectedPaymentIndex,
        confirmCvv: type == 'select',
        cardData: creditCardDetails?.[0],
        groupId: state?.groupId
      }
    })
  }

  const handlePaymentMethodImage = data => {
    return apaAssetsImages?.[`${region}_paymentSelector_access_icon_${data}`]
  }

  return (
    <div className="payment-method-setting">
      <div className="upper-back-button-area">
        <BackButtonComponent
          uid="paymentmethod-page-back"
          text={translations?.language?.[region]?.BotonShortcut_TextoTitulo}
          onCustomClick={() => {
            handleGoPreviousPage(state)
          }}
        />
      </div>
      <div className="payment-method-title">
        {isCreditCardPayment
          ? truncateText('lista_medios_pago_claropagosgate', 30)
          : state?.selectedPaymentMethodName
          ? truncateText('paymentSelector_changePayment_title_label', 30)
          : truncateText('paymentSelector_addPayment_title_label', 30)}
      </div>
      {isCreditCardPayment && (
        <span
          className={
            isCreditCardPayment
              ? 'credit-card-title-2'
              : 'payment-method-title-2'
          }
        >
          {truncateText(
            'updatePaymentMethod_claropagosgate_register_label',
            30
          )}
        </span>
      )}
      <div
        className={
          isCreditCardPayment
            ? 'payment-option-div-credit-card'
            : 'payment-option-div'
        }
      >
        {isCreditCardPayment ? (
          <>
            {creditCardDetails?.map((each, index) => {
              return (
                <button
                  key={index}
                  className="credit-card-button focusable"
                  autoFocus={true}
                  id={`creditCard${index}`}
                  onFocus={() => handleFocus(index)}
                  onBlur={() => setPaymentFocusImage(null)}
                >
                  {
                    <img
                      className=""
                      id={`creditCard${index}`}
                      src={
                        `images/payment_Icons/btn_tdc_${each?.marca}_focus.png`
                        // paymentFocusImage == `creditCard${index}`
                        //   ? `images/payment_Icons/btn_tdc_${each?.marca}_focus.png`
                        //   : `images/payment_Icons/btn_tdc_${each?.marca}.png`
                      }
                    />
                  }
                  <span id="cardMaskedNumber">{each?.pan?.slice(8)}</span>
                </button>
              )
            })}
          </>
        ) : (
          <>
            {checkEnabledPaymentMethods?.map((each, index) => {
              return (
                <button
                  key={index}
                  className="payment-buttons focusable"
                  autoFocus={
                    state?.selectedPaymentMethodName
                      ? state?.selectedPaymentMethodName == each?.gateway
                      : index == 0
                  }
                  id={`paymentMethod${index}`}
                  onClick={e => handleAddPayments(e, each?.gateway)}
                  onFocus={() => handleFocus(index)}
                  onBlur={() => setPaymentFocusImage(null)}
                  data-sn-right={
                    checkEnabledPaymentMethods?.length - 1 == index
                      ? `#paymentMethod${index}`
                      : `#paymentMethod${index + 1}`
                  }
                >
                  {
                    <img
                      className="payment-button-image"
                      id={`paymentImage${index}`}
                      src={
                        paymentFocusImage == `paymentImage${index}`
                          ? handlePaymentMethodImage(`${each?.gateway}_focus`)
                          : handlePaymentMethodImage(`${each?.gateway}`)
                      }
                    />
                  }
                </button>
              )
            })}
          </>
        )}
      </div>
      <div className="bottom-cancel-button-area">
        {isCreditCardPayment ? (
          <div id="creditCardButtons">
            <button
              id="seleccionar"
              autoFocus
              onClick={() => {
                handleCardPayment('select')
              }}
              className="focusable"
            >
              <span className="credit-card-seleccionar-text">
                {truncateText(
                  'updatePaymentMethod_access_option_button_select',
                  15
                )}
              </span>
            </button>
            <button
              id="reemplazar"
              onClick={() => {
                handleCardPayment('replace')
              }}
              className="focusable"
            >
              <span className="credit-card-reemplazar-text">
                {truncateText(
                  'updatePaymentMethod_access_opton_button_replace',
                  15
                )}
              </span>
            </button>
          </div>
        ) : (
          <button
            onMouseOver={e => {
              e.target.focus()
            }}
            id="paymentMethodCancelButton"
            onClick={() => {
              handleGoPreviousPage(state)
            }}
            className="default-btn focusable"
          >
            <span className="payment-cancel-text">
              {truncateText('paymentSelector_access_option_button_cancel', 30)}
            </span>
          </button>
        )}
      </div>
    </div>
  )
}
export default PaymentMethods
