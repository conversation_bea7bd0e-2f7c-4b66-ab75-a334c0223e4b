import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configure<PERSON><PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import Loginpagemail from "./Loginpagemail";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mockerrorresponse = {
    "status": "1"
}
const mocksuccessresponse = {
    "status": "0"
}
describe('Loginpagemail page test', () => {
    test('should render success api mock data', () => {
        initialState.login = {
            userInfo: mockerrorresponse
        }
        renderWithState(<Loginpagemail />)
    })
    test('should render error api mock data', () => {
        initialState.login = {
            userInfo: mocksuccessresponse
        }
        renderWithState(<Loginpagemail />)
    })
    test('input should change after onChange', () => {
        const result = renderWithState(<Loginpagemail />)
        let input = result.container.querySelector('input[name="email"]')
        fireEvent.change(input, { target: { value: '<EMAIL>' } })
        renderWithState(<Loginpagemail />)
    })
    test('input should change after onChange', () => {
        const result = renderWithState(<Loginpagemail />)
        let input = result.container.querySelector('input[name="email"]')
        fireEvent.change(input, { target: { value: '<EMAIL>' } })
        fireEvent.keyUp(input, { keyCode: '13' })
        renderWithState(<Loginpagemail />)
    })
    test('input should change after onChange', () => {
        const result = renderWithState(<Loginpagemail />)
        let input = result.container.querySelector('input[name="email"]')
        fireEvent.change(input, { target: { value: '<EMAIL>' } })
        fireEvent.keyUp(input, { keyCode: '38' })
        renderWithState(<Loginpagemail />)
    })
    test('onclick signin button', () => {
        initialState.login = {
            loginNavigation: true
        }
        initialState.login = {
            userInfo: {
                status: '1'
            }
        }
        const result = renderWithState(<Loginpagemail />)
        let input = result.container.querySelector('input[name="email"]')
        fireEvent.change(input, { target: { value: '<EMAIL>' } })
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(result.container, 'registernext')
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        renderWithState(<Loginpagemail />)
    })
})