import { call, takeEvery } from '@redux-saga/core/effects'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
	getCMSLevelV1Success,
	getCMSLevelV1Error,
	getCMSLevelUserV1Success,
	getCMSLevelUserV1Error
} from '../store/slices/HomeSlice'

function* getCMSLevelV1Api({ payload }) {
	const region = localStorage.getItem('region')
	try {
		yield call(
			request,
			payload?.type === 'kids'
				? `${URL.CMS_LEVEL_V1_URL}&region=${region}&user_id=${payload?.userId}&node=${payload?.nodeValue}&type=kids`
				: `${URL.CMS_LEVEL_V1_URL}&region=${region}&user_id=${payload?.userId}&node=${payload?.nodeValue}`,
			{
				method: 'GET'
			},
			{
				onSuccess(response) {
					store.dispatch(getCMSLevelV1Success(response))
				},
				onError(error) {
					store.dispatch(getCMSLevelV1Error(error))
				}
			}
		)
	} catch (error) {
		console.error('catch error --> ', error)
	}
}

function* getCMSLevelUserV1Api({ payload }) {
	const region = localStorage.getItem('region')
	try {
		yield call(
			request,
			payload?.type === 'kids'
				? `${URL.CMS_LEVEL_USER_V1_URL}&region=${region}&user_id=${payload?.userId}&node=${payload?.nodeValue}&user_token=${payload?.user_token}&type=kids`
				: `${URL.CMS_LEVEL_USER_V1_URL}&region=${region}&user_id=${payload?.userId}&node=${payload?.nodeValue}&user_token=${payload?.user_token}`,
			{
				method: 'GET'
			},
			{
				onSuccess(response) {
					store.dispatch(getCMSLevelUserV1Success(response))
				},
				onError(error) {
					store.dispatch(getCMSLevelUserV1Error(error))
				}
			}
		)
	} catch (error) {
		console.error('catch error --> ', error)
	}
}


export default function* homeSaga() {
	yield takeEvery('homeSlice/getCMSLevelV1', getCMSLevelV1Api)
	yield takeEvery('homeSlice/getCMSLevelUserV1', getCMSLevelUserV1Api)

}