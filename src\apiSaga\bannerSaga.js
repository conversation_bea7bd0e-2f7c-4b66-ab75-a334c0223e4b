
import { call, put, takeEvery } from "@redux-saga/core/effects";
import { URL, COMMON_URL } from '../utils/environment';
import { request } from "../utils/request";
import { store } from "../store/sagaStore";
import { getBannerSuccess, getBannerError } from "../store/slices/HomeSlice";


function* fetchBannerSaga({ payload }) {
  try {
    yield call(request, 
      `${COMMON_URL.BASE_URL}${payload}${URL.BANNER_URL}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getBannerSuccess(response))
        },
        onError(error) {
          store.dispatch(getBannerError(error))
        }
      }
    );
  } catch (error) {
  }
}

export default function* bannerDataSaga() {
  yield takeEvery('homeSlice/getBannerData', fetchBannerSaga)
}





