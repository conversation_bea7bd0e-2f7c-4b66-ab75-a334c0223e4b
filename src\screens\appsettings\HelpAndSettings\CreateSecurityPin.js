import React, { useState, useEffect, useRef } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from 'react-redux'
import {
  getCheckControlPin,
  getClearAllSettingsState,
  getRemindControlPin,
  setControlPin
} from '../../../store/slices/settingsSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import { ActivateTransaction, UnlockChannel } from './ChangeSecurityPin'
import './HelpAndSettings.scss'
import AlphaNumericKeyboard from '../../Keyboard/AlphaNumericKeboard'
import { getLockedChannelAdd } from '../../../store/slices/settingsSlice'
import PlayerModelSelection from '../../VodMovies/PlayerModelSelection'
import { getNavTabValue } from '../../../store/slices/HomeSlice'
import {
  getChannelData,
} from '../../../store/slices/PlayerSlice'
import { pushNewInteractionContentEvent } from '../../../GoogleAnalytics'
import { interactionType, PIN_PROTECTION, PROTECTED_CONTENT, TV } from '../../../GoogleAnalyticsConstants'

const CreateSecurityPin = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { state } = useLocation()
  const {
    data,
    item,
    purchase,
    rating,
    pageName,
    returnPage,
    featureTag,
    lockChannel
  } = state

  const pinRef = useRef([])

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const securityPin = useSelector(
    state => state?.settingsReducer?.setControlPinData
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const remindSecurityPin = useSelector(
    state => state?.settingsReducer?.remindControlPin?.response
  )
  const checkSecurityPin = useSelector(
    state => state?.settingsReducer?.checkControlPin
  )
  const navData = useSelector(state => state?.homeReducer?.navbarData)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  let channelIndex = epgSevenDaysData[1]?.channelResponse?.findIndex(
    itrObj => itrObj.group_id == props?.item?.group_id
  )
  const invisible = 'images/Icono_Onboarding_Close.png'
  const visible = 'images/Icono_Onboarding_Open.png'
  const region = localStorage.getItem('region')

  const [pin, setPin] = useState(new Array(6).fill(''))
  const [visbilityIcon, setVisibilityIcon] = useState(invisible)
  const [buttonDisable, setButtonDisable] = useState(true)
  const [title, setTitle] = useState('')
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [focusedIdx, setFocusedIdx] = useState(0)
  const [pinInvalid, setPinInvalid] = useState('')
  const [navigatePlayer, setNavigateplayer] = useState(false)
  const [navigatePlayerModelScreen, setNavigatePlayerModelScreen] =
    useState(false)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  useEffect(() => {
    setTitle(data)
    setKeyboardFocus(true)
    // const blinkerText = () => {
    //   if (document.querySelector('.pin-focused')) {
    //     let p = document.querySelector('.pin-focused').value
    //     if (p?.substr(-1, 1) == '|') {
    //       let removePipe = p?.replace('|', '')
    //       document.querySelector('.pin-focused').value = removePipe
    //     } else if (p?.length == 0) {
    //       document.querySelector('.pin-focused').value = p + '|'
    //     }
    //   }
    // }

    // const blinkerInterval = setInterval(blinkerText, 1000)
    // return () => {
    //   clearInterval(blinkerInterval)
    // }
  }, [])

  useEffect(() => {
    if (
      securityPin?.profiles?.[0]?.parental?.active &&
      securityPin?.profiles?.[0]?.purchase?.active == false &&
      securityPin?.profiles?.[0]?.channel?.active &&
      !rating &&
      (title == 'Create' || data == 'Create')
    ) {
      if (returnPage == '/livePlayer') {
        const payload = {
          hks: userDetails?.session_stringvalue,
          group_id: item?.group_id ?? item?.common?.id,
          user_hash: userDetails?.session_userhash
        }
        dispatch(getLockedChannelAdd(payload))
        navigate('/pinCreationSuccess', {
          state: { pageName: 'livePlayer', data: item }
        })
      } else {
        navigate('/pinCreationSuccess', {
          state: { pageName: 'parentalControl' }
        })
        setButtonDisable(true)
      }
    } else if (securityPin?.profiles?.[0]?.parental?.active && purchase == 1) {
      navigate('/my-settings/help-And-Settings/parental-control')
      setButtonDisable(true)
    } else if (securityPin?.profiles?.[0]?.parental?.active && rating) {
      navigate('/my-settings/help-And-Settings/parental-control', {
        state: { rating },
        replace: true
      })
      setButtonDisable(true)
    }
  }, [securityPin])

  useEffect(() => {
    navigateToVodPlayer()
  }, [checkSecurityPin])

  const backNavigate = () => {
    state?.resume &&
      navigatePlayerModelScreen &&
      setNavigatePlayerModelScreen(false)
    setTitle('Create')
    handleLgkey('back')
  }
  
  const navigateToVodPlayer = (value) => {
    if (navigatePlayer) {
      if (
        checkSecurityPin?.msg === 'OK' &&
        (state?.pageName == 'movies' || state?.pageName == 'series')
      ) {
        if (state?.resume && !navigatePlayerModelScreen) {
          setNavigatePlayerModelScreen(true)
          setTitle('')
        } else if (value == 'securitypin') {
          setNavigatePlayerModelScreen(false)
          dispatch(getClearAllSettingsState())
          navigate('/vodPlayer', {
            state: {
              data: state?.item,
              episodeData: '',
              showControls: 'vod',
              getMedia: state?.getMediaData,
              episodeData: state?.episodeData,
              contentDataplayer: state?.contentDataplayer,
              resume: value == 'begin' ? false : true,
              backfocusid: state?.backfocusid,
              seasonTabStatus: state?.seasonTabStatus,
              episodeIndexToFocus: state?.episodeIndexToFocus,
              episodeItemClicked: state?.episodeItemClicked
            }
          })
        } else {
          dispatch(getClearAllSettingsState())
          navigate('/vodPlayer', {
            state: {
              data: state?.item,
              episodeData: '',
              showControls: 'vod',
              getMedia: state?.getMediaData,
              episodeData: state?.episodeData,
              contentDataplayer: state?.contentDataplayer,
              resume: value == 'begin' ? false : true,
              returnPage: state?.returnPage,
              inputValue: state?.inputValue,
              backfocusid: state?.backfocusid,
              seasonTabStatus: state?.seasonTabStatus,
              episodeIndexToFocus: state?.episodeIndexToFocus,
              episodeItemClicked: state?.episodeItemClicked
            }
          })
        }
      } else if (checkSecurityPin?.errors) {
        setPinInvalid(checkSecurityPin?.errors)
        dispatch(getClearAllSettingsState())
        setPin(new Array(6).fill(''))
        document.getElementById('Key_0')?.focus()
        setFocusedIdx(0)
      }
    }
  }

  useEffect(() => {
    if (
      pin &&
      pin[0]?.length > 0 &&
      pin[1]?.length > 0 &&
      pin[2]?.length > 0 &&
      pin[3]?.length > 0
    ) {
      setButtonDisable(false)
    } else {
      setButtonDisable(true)
    }
    pin[5]?.length > 0 && document.getElementById('siguienteButton')?.focus()
  }, [pin])

  useEffect(() => {
    if (remindSecurityPin) {
      pushNewInteractionContentEvent(
        state?.gaContentData,
        PIN_PROTECTION,
        TV,
        interactionType?.OLVIDASTE_TU_PIN_DE_SEGURIDAD
      )
      navigate('/PinConfirmation', {
        state: {
          data: state?.data,
          item: state?.item,
          pageName: state?.pageName,
          gaContentData: state?.gaContentData
        }
      })
    }
  }, [remindSecurityPin])
  const handleLivePlayerPayload = () => {
    dispatch(
      getChannelData({
        group_id: epgSevenDaysData[1]?.channelResponse[channelIndex]?.group_id,
        timeshift:
          epgSevenDaysData[1]?.channelResponse[channelIndex]?.group?.common
            ?.timeshift,
        switchChannel: 'yes',
        epgIndex: channelIndex
      })
    )
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
    handleTVRemoteKey(event)
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (
      (key.yellowcode === keycode ||
        keycode === 10009 ||
        keycode == 405 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
        keycode?.target?.id == 'backId' ||
        keycode?.target?.id == 'cancelId') &&
      (title != 'epg' ?? title != 'unlock')
    ) {
      pushNewInteractionContentEvent(
        state?.gaContentData, 
        PROTECTED_CONTENT, 
        TV, 
        interactionType?.CANCELAR
      )
      const vodData = state?.getMediaData?.group?.common
      if (
        state?.returnPage === 'search' &&
        (state?.pageName == '/livePlayer' ||
          state?.pageName == 'movies' ||
          state?.pageName == 'series')
      ) {
        navigate('/search', {
          state: { inputValue: state?.inputValue },
          replace: true
        })
      } else if (state?.pageName === 'movies') {
        navigate('/movies', { state: { vodData : state?.moviedata ?? vodData, backfocusid: state?.backfocusid, }, replace: true })
      } else if (state?.pageName === 'series') {
        navigate('/series', { state: { data: state?.item ?? vodData, backfocusid: state?.backfocusid, }, replace: true })
      } else if (state?.pageName === 'search') {
        navigate('/search', {
          state: { inputValue: state?.inputValue }
        })
      } else if (state?.pageName === '/livePlayer') {
        (handleLivePlayerPayload(),
        navigate('/livePlayer', { state: { showControls: 'live' } }))
      } else if (state?.pageName == 'parental-control') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'parentalControl', returnPageName: returnPage }
        })
      } else if (state?.page == 'record') {
        navigate('/home', {
          state: { myContentData: true, backfocusid: state?.backfocusid }
        }),
          localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      }
    }
  }

  const handleLgkey = keycode => {
    if (
      (keycode == 405 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
        keycode === 461 ||
        keycode == 10009 || keycode == 'back' ||
        keycode?.target?.id == 'backId' ||
        keycode?.target?.id == 'cancelId') &&
      (title != 'epg' ?? title != 'unlock')
    ) {
      pushNewInteractionContentEvent(
        state?.gaContentData, 
        PROTECTED_CONTENT, 
        TV, 
        interactionType?.CANCELAR
      )
      const vodData = state?.getMediaData?.group?.common
      if (state?.returnPage === 'search') {
        navigate('/search', {
          state: { inputValue: state?.inputValue },
          replace: true
        })
      }else if (state?.pageName === '/livePlayer') {
        (handleLivePlayerPayload(),
        navigate('/livePlayer', { state: { showControls: 'live' } }))
      }else if (state?.pageName === 'movies') {
        navigate('/movies', { state: { vodData : state?.moviedata ?? vodData , backfocusid: state?.backfocusid, }, replace: true })
      } else if (state?.pageName === 'series') {
        navigate('/series', { state: { data: state?.item ?? vodData, backfocusid: state?.backfocusid, }, replace: true })
      } else if (state?.pageName == 'parental-control') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'parentalControl', returnPageName: returnPage }
        })
      } else if (state?.page == 'record') {
        navigate('/home', {
          state: { myContentData: true, backfocusid: state?.backfocusid }
        }),
          localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      }
    }
  }

  const handleTVRemoteKey = (event) => {    
    const key = event.key;
    if (/^\d$/.test(key)) {
      const currentIndex = pin.findIndex((digit) => digit === '');
      if (currentIndex !== -1) {
        const newOtp = [...pin];
        newOtp[currentIndex] = key;
        key?.length == 1 && setPin(newOtp);
        if (currentIndex < 5) {
          setFocusedIdx(currentIndex + 1)
        }
      }
      setPinInvalid('')
    }    
  };

  const handleOTPChange = (element, index) => {
    const value = element.value ?? element

    if (value == 'cl') {
      setPin(pin.map((d, idx) => (idx === focusedIdx ? '' : d)))
      setFocusedIdx(index === 0 ? index : index - 1)
      return
    }
    if (value == 'clr') {
      setPin(new Array(pin.length).fill(''))
      setFocusedIdx(0)
      setPinInvalid('')
      return 
    }     
    if (/^\d$/.test(value)) {
      setPin(pin.map((d, idx) => (idx === index ? value : d)))
      setFocusedIdx(index === 5 ? index : index + 1)
      } else {
        setPin(pin.map((d, idx) => (idx === index ? '' : d)))
        setPinInvalid('')
      }
      setPinInvalid('')
   }

  const handlePinVisbility = () => {
    visbilityIcon === invisible
      ? setVisibilityIcon(visible)
      : setVisibilityIcon(invisible)
  }

  const handleForgotPin = () => {
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_hash: userDetails?.session_userhash
    }
    pushNewInteractionContentEvent(
      state?.gaContentData,
      PROTECTED_CONTENT,
      TV,
      interactionType?.OLVIDASTE_TU_PIN_DE_SEGURIDAD
    )
    dispatch(getRemindControlPin(payload))
  }

  const handleSaveClick = () => {
    const payload = {
      hks: userDetails?.session_stringvalue,
      userId: userDetails?.user_id,
      user_token: userDetails?.user_token,
      parental: 1,
      purchase: purchase ?? 0,
      channel: 1,
      rating: rating ?? 30,
      code: pin.join('')
    }
    dispatch(setControlPin(payload))
  }

  const navigateToPlayer = () => {
    pushNewInteractionContentEvent(
      state?.gaContentData,
      PROTECTED_CONTENT,
      TV,
      interactionType?.SIGUIENTE
    )
    setNavigateplayer(true)
    const payload = {
      controlPIN: pin.join(''),
      userId: userDetails?.user_id,
      hks: userDetails?.session_stringvalue,
      parental: 1
    }
    dispatch(getCheckControlPin(payload))
  }

  const inputFocused = () => {
    pinRef?.current[focusedIdx]?.focus()
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  return title == 'Create' ? (
    <div className="app-css-block-channel">
      <div className="back-indicator-regrassar">
        <img
          src={'images/Logos_Claro_Video.svg'}
          className="claro-logo"
          alt="logo"
        />

        <button
          className="back-indicator-button-pin focusable"
          id="backId"
          onClick={e => handleLgkey('back')}
        >
          <img
            className="yellow-indicator-button"
            src={'images/yellow_shortcut.png'}
          />
          <img
            className="back-indicator-image"
            src={'images/back_button.png'}
          />
          <span>{truncateText('top_head_option_button_back', 30)}</span>
        </button>
      </div>
      <div className="left-container-div">
        <AlphaNumericKeyboard
          data-testid="numericKeyboard"
          type="Numeric"
          onChange={e => handleOTPChange(e, focusedIdx)}
          submitDisable={buttonDisable}
          autoFocus={keyboardFocus}
        />
      </div>
      <div className="right-container-div">
        <span className="blocked-title">
          {truncateText('setupPin_modal_title_label', 30)}
        </span>
        <span className="sub-title-unlock">
          {state?.pageName == 'movies' || state?.pageName == 'series'
            ? truncateText('lockChannel_access_subtitleContent_label', 100)
            : truncateText('setupPin_modal_instruction_label', 100)}
        </span>
        <div>
          <div className="pin-box-div">
            {pin?.map((item, index) => {
              return (
                <div key={index} className="pin-wrapper">
                <input
                  className={
                    pinInvalid
                      ? 'pin-field-invalid'
                      : focusedIdx == index
                        ? 'pin-focused'
                        : 'pin-field'
                  }
                  type={'text'}
                  name="pin"
                  onKeyUp={e => !/[0-9]/.test(e.key) && e.preventDefault()}
                  id={`pin${index}`}
                  ref={ref => (pinRef.current[index] = ref)}
                  maxLength={1}
                  key={index}
                  value={
                    visbilityIcon === invisible && item?.length > 0 ? '*' : item
                  }
                  onChange={e => handleOTPChange(e.target, index)}
                  inputMode="none"
                  readOnly
                  onFocus={inputFocused}
                  data-testid={`pin${index}`}
                />
                 {/* Show blinking pipe cursor if input is focused and empty */}
                 {focusedIdx === index && item === '' && (
                      <span className="pin-cursor">|</span>
                    )}
                  </div>
              )
            })}
            <button
              onClick={handlePinVisbility}
              className="see-pin-button focusable"
              id="see-pin"
            >
              <img src={visbilityIcon} />
            </button>
          </div>
          {pinInvalid ? (
            <p className="pin-error">
              <span className="pin-error-contents">
                {truncateText('lockChannel_tooltip_valid_label_validation', 30)}
              </span>
            </p>
          ) : null}
          <button
            className="block-channel-button focusable"
            id="siguienteButton"
            disabled={buttonDisable}
            onClick={
              state?.pageName == 'movies' || state?.pageName == 'series'
                ? navigateToPlayer
                : handleSaveClick
            }
            style={{ marginTop: '84px' }}
          >
            {truncateText('bt_suscripcion_siguiente', 30)}
          </button>
          <button
            className="block-channel-button-cancel focusable"
            id="cancelId"
            onClick={e => handleLgkey(e)}
          >
            {truncateText('modal_pin_cancel_button', 30)}
          </button>
          <button
            onClick={handleForgotPin}
            className={`${state?.pageName == 'movies' || state?.pageName == 'series'
                ? 'forgot-pin-button focusable'
                : 'hiding-forgot-pin'
              }`}
            data-testid={`forgotPin`}
          >
            <p className="forgot-pin-content">
              {truncateText('modal_pin_forgot_pin', 35)}
            </p>
          </button>
        </div>
      </div>
    </div>
  ) : title == 'Unlock' || title == 'epg' || title == 'record' ? (
    <UnlockChannel
      title={title}
      item={item}
      lockPropChannel={lockChannel}
      pageName={pageName}
      inputValue={state?.inputValue}
      returnPage={state?.returnPage}
      featureTag={state?.featureTag}
      page={state?.page}
      contentName={state?.contentName}
      selectedContent = {state?.selectedContent}
      backfocusid = {state?.backfocusid}
      eventData = {state?.eventData}
      pastProgramData = {state?.pastProgramData}
      fromZap = {state?.fromZap}
      checkPastContent = {state?.checkPastContent}
      fromEpg = {state?.fromEpg}
    />
  ) : navigatePlayerModelScreen ? (
    <PlayerModelSelection
      handleBackInfoTab={backNavigate}
      handleIconClick={navigateToVodPlayer}
      keyParam={'securitypin'}
      id={'videoPlayerWarning'}
      gaContentData={state?.gaContentData}
    />
  ) : null
}

export default CreateSecurityPin
