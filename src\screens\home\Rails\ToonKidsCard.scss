$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;


.ToonrailTitle {
  font-size: 32px;
  font-weight: normal;
  text-align: left;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  left: 87px;
  padding-left: 14px;
  margin-bottom: 17px;
  font-family: 'Roboto';
}
.Toonrail-Container {
  overflow-x: hidden;
  .Toonrail-wrapper {
    display: flex;
    margin: 0.5rem;
    padding: 0.5rem;
    overflow: scroll ;
    scroll-snap-type: x mandatory;

    .ToonKids-block {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      position: relative;
      .toon-btn {
        display: flex;
        flex-direction: column;

        .Toonical_block {
          display: flex;
          flex-direction: column;
          border-radius: 50%;
          margin: 0rem 1.5rem;
          position: relative;
          width: 299px;
          height: 298px;
          padding:6px;
          border: 4px solid transparent !important;
          

          .rail-image-Toon {
            display: flex;
            width: 298px;
          }
        }

        .Toonical_block:focus {
          transform: scale(1.02);
           transition: transform 0.2s ease-in-out;
          scroll-snap-align: end;
          border: 4px solid #fff !important;
        }
        .toon-title {
          color: #fff;
          text-align: center;
          font-size: 32px;
          font-family: 'Roboto';
          width: 310px;
          height: 76px;
          margin-top: 18px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          text-overflow: ellipsis;
          word-wrap: break-word;
        }
      }
    }
  }
}



::-webkit-scrollbar {
  display: none;
}