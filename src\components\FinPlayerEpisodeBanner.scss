$color-white: #ffffff;
$font-family-roboto: Roboto;
$position-absolute: absolute;
$text-wrap-nowrap: nowrap;
$font-weight-bold: bold;
$box-sizing-border: border-box;
$position-relative: relative;
$text-overflow-ellipsis: ellipsis;
$overflow-hidden: hidden;

.fin-player-main-container {
  position: $position-absolute;
  border-radius: 12px;
  background-color: #242424;
  width: 1785px;
  height: 241px;
  flex-direction: row;
  display: flex;
  z-index: 999;
  bottom: 124px;
  left: 80px;

  .episode-container {
    display: flex;
    height: 250px;
    width: 407px;
    align-items: center;
    justify-content: center;

    .episode-card-img {
      width: 358px;
      height: 193px;
      flex-shrink: 0;
      background: lightgray 50% / cover no-repeat;
      display: flex;

      .episode-card-lazy {
        width: 358px;
        height: 193px;
        flex-shrink: 0;
        background: lightgray 50% / cover no-repeat;
      }
    }
  }

  .episodeText {
    width: 799px;
    height: 242px;
    display: flex;

    .episode-detail {
      justify-content: center;
      display: flex;
      flex-direction: column;

      .episode-movieCard-Title {
        height: 40px;
        width: 799px;
        color: #ffffff;
        font-family: <PERSON><PERSON>;
        font-size: 34px;
        font-weight: 300;
        letter-spacing: 0;
        line-height: 40px;
        white-space: nowrap;
        margin-bottom: 6px;
      }

      .episode-card-Title {
        height: 40px;
        width: 799px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 34px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 40px;
        white-space: nowrap;
        display: contents;
      }

      .episode-number {
        height: 40px;
        width: 799px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 34px;
        letter-spacing: 0;
        line-height: 40px;
        margin-top: 8px;
      }

      .episode-description {
        height: auto;
        width: 799px;
        opacity: 0.8;
        color: #ffffff;
        font-family: Roboto;
        font-size: 28px;
        letter-spacing: 0;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 114.286% */
        letter-spacing: -0.908px;
      }
    }
  }

  .episode-button-container {
    width: 465px;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    display: flex;

    .episode-timline-container {
      display: flex;
      flex-direction: column;

      .episode-timeline {
        height: 33px;
        width: 425px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 28px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 33px;
        text-align: center;
      }
      .episode-button-size {
        display: flex;
        align-items: center;
        flex-direction: column;
        .status-button {
          width: 335px;
          height: 71px;
          flex-shrink: 0;
          border-radius: 5.865px;
          background-color: #981c15;
          margin-top: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          align-content: center;
          &:focus,
          &:active {
            width: 394px;
            height: 83px;
            flex-shrink: 0;
            border-radius: 6.9px;
            background: #981C15;
          }
          .status-title {
            display: inline-block;
            justify-content: center;
            align-items: center;
            color: #ffffff;
            font-family: Roboto;
            font-size: 27px;
            font-weight: bold;
            width: 400px;
            text-align: center;
            letter-spacing: 0px;
            line-height: 32px;
            text-transform: uppercase;
            margin: 0px auto;
            padding: 3px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .status-buttonText {
          width: 405px;
          height: 71px;
          flex-shrink: 0;
          border-radius: 5.865px;
          margin-top: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          align-content: center;
          &:focus,
          &:active {
            width: 394px;
            height: 83px;
            flex-shrink: 0;
            border-radius: 6.9px;
          }
          .status-title {
            display: inline-block;
            justify-content: center;
            align-items: center;
            color: #ffffff;
            font-family: Roboto;
            font-size: 27px;
            font-weight: bold;
            width: 400px;
            text-align: center;
            letter-spacing: 0px;
            line-height: 32px;
            text-transform: uppercase;
            margin: 0px auto;
            padding: 3px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .clarovideo-btn-color {
          background-color: #4c6fa7;
          border-radius: 6.74px;
          margin-top: 14px;
          width: 450px;
          height: 89px;
          &:focus,
          &:active {
            transform: scale(1.1);
            outline: 3px solid white;
            border: 5px solid white;
            border-color: #1a1a1a;
            height: 83px;
          }
          .clarovideo-status-title {
            height: 33px;
            color: #ffffff;
            font-family: Roboto;
            font-size: 28px;
            width: 100%;
            align-items: center;
            font-weight: bold;
            text-wrap: pretty;
            text-align: center;
            letter-spacing: 0;
            line-height: 33px;
          }
        }
        .episode-btn-color {
          background-color: #40336f;
          &:focus {
            width: 545.99px;
            height: 82.8px;
            flex-shrink: 0;
          }
        }
        .temporado-btn-color {
          background-color: #8065df;
          &:focus {
            width: 545.99px;
            height: 82.8px;
            flex-shrink: 0;
          }
        }
        .rent-btn-color {
          background-color: #8A4CA7;
          &:focus {
            width: 545.99px;
            height: 82.8px;
            flex-shrink: 0;
          }
        }
        .subscribtion-btn-color {
          background-color: #4c6fa7;
          &:focus {
            width: 545.99px;
            height: 82.8px;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
