.app-css-locked-channel-list {
	height: 1080px;
	position: fixed;
	color: #121212;

	.locked-channel-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 60px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 66px;
	}

	.locked-channel-button {
		box-sizing: border-box;
		height: 71px;
		width: 310px;
		border: 3px solid #ffffff;
		border-radius: 30px;
		display: inline-flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		float: right;

		.icon-green-lock-channel {
			height: 16px;
			width: 16px;
			margin-right: 20px;
		}

		.locked-channel-button-contents {
			color: #ffffff;
			font-family: Robot<PERSON>;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
		}
	}

	.locked-channel-button:focus {
		border: 8px solid #ffffff
	}

	.horizontal-line {
		box-sizing: border-box;
		height: 1px;
		width: 1781px;
		border: 1px solid #475865;
		margin-bottom: 40px;
	}

	.locked-channel-subtitle {
		color: #ffffff;
		font-family: Roboto;
		font-size: 30px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 33px;
	}

	.locked-channels-container {
		display: flex;
		flex-direction: row;
		margin-top: 28px;
		width: 1724px;
		overflow-x: scroll;
		position: relative;
		scroll-snap-type: x mandatory;
	}

	.locked-channels-card {
		height: 189px;
		width: 319.5px;
		margin-right: 25.58px;

		.channel-number {
			position: absolute;
			color: white;
			width: 64px;
			height: 37px;
			display: flex;
			background-color: black;
			opacity: 0.5;
			font-size: 27px;
			justify-content: center;
			align-items: center;
			font-family: Roboto;
		}

		.Channel-img {
			height: 189px;
			width: 319.5px;
		}

		.unlock-channel-div {
			opacity: 0;
			background-color: #000000;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 188px;
			width: 321px;
			position: relative;
			bottom: 193px;

			.lock-icon {
				height: 23.38px;
				width: 19px;
				margin-right: 10px;
			}

			.unlock-title {
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 35px;
			}
		}
	}

	.locked-channels-card:focus {
		box-sizing: border-box;
		height: 197px;
		width: 329px;
		border: 4px solid #ffffff;
		scroll-snap-align: end;

		.unlock-channel-div {
			opacity: 0.9;
		}
	}

	.empty-channel {
		box-sizing: border-box;
		height: 174px;
		width: 975px;
		border: 2px dashed #ffffff;
		border-radius: 15px;
		display: flex;
		flex-direction: column;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		margin-top: 40px;
		justify-content: center;

		.empty-channel-txt {
			color: #ffffff;
			font-family: Roboto;
			font-size: 44px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 52px;
			padding-left: 40px;
		}
	}
}