import React, { useEffect } from 'react'
import { useSelector } from 'react-redux'
import './NetworkServiceError.scss'
import * as firebaseAnalytics from '../../GoogleAnalyticsConstants'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const NetworkErrorScreen = (props) => {

	const region = localStorage.getItem('region')

	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

	const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const apilanguage = translations?.language?.[region]


	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	}

	const handleRetryClick = () => {
		navigator.onLine ? props?.setIsInternetThere(true) : props?.setIsInternetThere(false)
	}

	const handlesamsungkey = (key, keycode) => {
		if (key.yellowcode === keycode || keycode === 10009) {
			props?.setIsInternetThere(true)
		}
	}

	const handleLgkey = keycode => {
		if (keycode == 405 || keycode === 461 || keycode == 'backClick') {
			props?.setIsInternetThere(true)
		}
	}

	const keypresshandler = event => {
		const keycode = event.keyCode
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
			}
			handlesamsungkey(codes, keycode)
		} else {
			handleLgkey(keycode)
		}
	}

	useEffect(() => {
		document.addEventListener('keyup', keypresshandler)
		return () => {
			document.removeEventListener('keyup', keypresshandler)
		}
	}, [keypresshandler])

	useEffect(()=>{
  	  pushScreenViewEvent({screenName:'network_error', screenData: userDetails, prevScreenName: firebaseAnalytics.NOT_APPLICABLE})
	},[])


	return (
		<div className='network-service-error-div'>
			<img
				className="claro-video-logo"
				src={'images/logo.png'}
			/>
			<button className="back-indicator-button-pin focusable"
				id="back-button"
				onClick={(e) => handleLgkey('backClick')}
			>
				<img className='yellow-indicator-button' src={'images/yellow_shortcut.png'} />
				<img className='back-indicator-image' src={'images/back_button.png'} />
				<span>{truncateText('atv_back_notification', 30)}</span>
			</button>

			<div className="network-error-screen-container">
				<img className="alert-img" src="images/ic_alert.png" alt="Alert Icon" />
				<h1 className="no-internet-text">{truncateText('atv_network3_unavailable_vodp', 30)}</h1>
				<p className="network-error-msg">{truncateText('atv_network_msg', 200)}</p>
				<button className="network-error-btn-container focusable" autoFocus onClick={handleRetryClick}>
					{truncateText('atv_network_settings_vodp', 30)}
				</button>
			</div>

		</div>
	)
}

export default NetworkErrorScreen