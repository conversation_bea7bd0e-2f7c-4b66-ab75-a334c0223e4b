import React, { useEffect, useCallback, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'

import './../../styles/VodMovies.css'
import moment from 'moment/moment'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import VodSkeletonLoading from '../VodMoviesDetailpage/VodSkeletonLoading'
import {
  RailsSkeletonLoading,
  CastSkeletonLoading
} from '../SkeletonScreenLoading/SkeletonScreenloading'

const VodMoviesDetailpage = ({
  data,
  watchListRef,
  currentButtonFocus,
  setCurrentButtonFocus,
  showNotification,
  genreData,
  castDetails,
  recomendation,
  handleClick,
  watchList,
  handleMltDetails,
  handleWatchListDel,
  isLoading,
  handleWatchListAdd
}) => {
  const userId = localStorage.getItem('userId')
  const userDetails = useSelector(
    state => state?.login?.isLoggedIn?.response?.session_userhash
  )
  const navigate = useNavigate()

  // useEffect(() => {

  //   document.addEventListener("keydown", keyPressFunc);
  //   return () => {
  //     document.removeEventListener("keydown", keyPressFunc);
  //   };
  // }, [keyPressFunc])

  // const keyPressFunc = ((event) => {

  //   if (event.keyCode === 10009 || event.keyCode === 8|| event.keyCode === 461 || event.keyCode === 27 || event.key == 'Escape') {
  //     navigate(`${event?.view?.origin}/#/home`)
  //     // window.history.go(-1);
  //   }
  // }, []);

  const keyPressFunc = useCallback(event => {
    if (
      event.keyCode === 10009 ||
      event.keyCode === 8 ||
      event.keyCode === 461
    ) {
      navigate('/home')
    }
  }, [])

  useEffect(() => {
    document.body.addEventListener('keyup', keyPressFunc)

    return () => {
      document.body.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  return castDetails?.[0] || recomendation?.[0] || !isLoading ? (
    <div className="VodLayout">
      <div
        className={
          showNotification ? 'NotificationLayout show' : 'NotificationLayout'
        }
      >
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={'images/Vod_Movies_Icons/info_black_large.png'}
          placeholderSrc={'images/Vod_Movies_Icons/info_black_large.png'}
          width={67}
          height={67}
        />
        <div className="NotificationTextLayout">
          <h6 className="NotificationText">
            {watchList ? 'Added to Watchlist' : 'Removed from watchlist'}
          </h6>
          <h6 className="NotificationPath">
            {watchList ? 'My Contents > Watchlist' : ''}
          </h6>
        </div>
      </div>

      <div
        className="BackgroundImages"
        style={{
          backgroundImage: data?.image_large ? (
            `url(${data?.image_large}), linear-gradient(85deg, rgb(0 0 0 / 78%) 0%, rgb(85 71 71) 50%, rgb(165 94 94 / 0%) 100%)`
          ) : (
            <VodSkeletonLoading />
          ),
          paddingTop: 100
        }}
      >
        <div className="DetailsLayout">
          <div className="TitleLayout">
            <h1 className="ContentTitle">{data?.title}</h1>
            <h6 className="SeasonDetails">
              {data?.title} |{' '}
              {moment(data?.duration, 'HH:mm:ss').format('HH:mm:ss')} |
              {genreData && ` ${genreData?.toString()}`} | {data?.year} |{' '}
              <span className="ageAlert ageAlert1">{data?.rating_code}</span>
            </h6>
          </div>

          <div className="DescriptionLayouts">
            <h6 className="Description">{data?.description}</h6>
          </div>
        </div>

        <div className="ButtonLayout">
          <button
            className="focusable"
            onClick={handleClick}
            autoFocus={currentButtonFocus === 'play'}
            onFocus={() => setCurrentButtonFocus('play')}
            data-testid="playbutton"
          >
            <div className="switchImage focusImg">
              {currentButtonFocus === 'play' ? (
                <img
                  className="ButtonImage"
                  src={'images/Vod_Movies_Icons/play_focus.png'}
                />
              ) : (
                <img
                  className="ButtonImage"
                  src={'images/Vod_Movies_Icons/play_unfocus.png'}
                />
              )}
              <p
                className={
                  currentButtonFocus === 'play'
                    ? 'ButtonText'
                    : 'ButtonTextFocusOut'
                }
              >
                play
              </p>
            </div>
          </button>
          <button
            className="focusable"
            onClick={handleClick}
            onFocus={() => setCurrentButtonFocus('trailer')}
            data-testid="trailerbutton"
          >
            <div>
              {currentButtonFocus !== 'trailer' ? (
                <img
                  className="ButtonImage"
                  src={'images/Vod_Movies_Icons/trailer_unfocus.png'}
                />
              ) : (
                <img
                  className="ButtonImage focusImg"
                  src={'images/Vod_Movies_Icons/trailerfocus.png'}
                />
              )}
              <p
                className={
                  currentButtonFocus === 'trailer'
                    ? 'ButtonText'
                    : 'ButtonTextFocusOut'
                }
              >
                trailer
              </p>
            </div>
          </button>
          {watchList ? (
            <button
              onClick={handleWatchListDel}
              data-testid="delWatchListTest"
              ref={watchListRef}
              className={userId && userDetails ? 'focusable' : ''}
              style={{ visibility: userId && userDetails ? '' : 'hidden' }}
              onFocus={() => setCurrentButtonFocus('watchlist')}
            >
              <div>
                {currentButtonFocus !== 'watchlist' ? (
                  <img
                    className="ButtonImage"
                    src={'images/Vod_Movies_Icons/watchlist_unfocus.png'}
                  />
                ) : (
                  <img
                    className="ButtonImage"
                    src={'images/Vod_Movies_Icons/watchlist_focus.png'}
                  />
                )}
                <p
                  className={
                    currentButtonFocus === 'watchlist'
                      ? 'ButtonText'
                      : 'ButtonTextFocusOut'
                  }
                >
                  watchlist
                </p>
              </div>
            </button>
          ) : (
            <button
              onClick={handleWatchListAdd}
              data-testid="addwatchlisttest"
              onFocus={() => setCurrentButtonFocus('addToWatchlist')}
              ref={watchListRef}
              className={userId && userDetails ? 'focusable' : ''}
              style={{ visibility: userId && userDetails ? '' : 'hidden' }}
            >
              <div>
                {currentButtonFocus !== 'addToWatchlist' ? (
                  <img
                    className="ButtonImage"
                    src={'images/Vod_Movies_Icons/addtowatchlist_unfocus.png'}
                  />
                ) : (
                  <img
                    className="ButtonImage focusImg"
                    src={'images/Vod_Movies_Icons/addtowatchlist_focus.png'}
                  />
                )}
                <p
                  className={
                    currentButtonFocus === 'addToWatchlist'
                      ? 'ButtonText'
                      : 'ButtonTextFocusOut'
                  }
                >
                  watchlist
                </p>
              </div>
            </button>
          )}
        </div>

        <div className="CastLayout">
          <h5 className="mltTitle">Cast</h5>
          {recomendation && recomendation ? (
            <div className="container Casts" style={{ justifyContent: 'left' }}>
              {castDetails?.map((cast, index) => {
                return (
                  <button
                    style={{ marginRight: 26 }}
                    data-testid="castTest"
                    className="cast_block focusable"
                    onFocus={() => setCurrentButtonFocus('cast')}
                    key={index}
                  >
                    {cast?.image ? (
                      <LazyLoadImage
                        className="CastImage"
                        src={
                          cast?.image !== undefined || ''
                            ? cast?.image
                            : 'images/Vod_Movies_Icons/cast_thumbnail.png'
                        }
                        key={index}
                        placeholderSrc={
                          'images/Vod_Movies_Icons/cast_thumbnail.png'
                        }
                      />
                    ) : (
                      <LazyLoadImage
                        className="CastImage"
                        src={'images/Vod_Movies_Icons/cast_thumbnail.png'}
                        placeholderSrc={
                          'images/Vod_Movies_Icons/cast_thumbnail.png'
                        }
                      />
                    )}
                    <h5 className="CastNames">
                      {cast?.first_name ? `${cast?.name} ` : `${cast?.name}`}
                    </h5>
                    <h6 className="CastRoles">{cast?.role}</h6>
                  </button>
                )
              })}
            </div>
          ) : (
            <div style={{ display: 'flex' }}>
              <CastSkeletonLoading listsToRender={7} />
              {/* <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} /> */}
            </div>
          )}
        </div>

        <div className="mltLayout">
          <h5 className="mltTitle">More Like This</h5>

          <div className="mltContainer">
            {recomendation && recomendation ? (
              <div className="mlt-wrapper focusImg">
                {recomendation?.map((item, index, array) => (
                  <button
                    data-testid="mltTest"
                    className="mlt_block focusable focusImg"
                    key={index}
                    data-sn-left={index == 0 ? ' ' : undefined}
                    data-sn-right={index != array.length - 1 && undefined}
                    onClick={() => handleMltDetails(item)}
                    onFocus={() => setCurrentButtonFocus(`mlt-${index}`)}
                  >
                    <div className="mltCardImage">
                      {item?.image_small !== '' ||
                      item?.image_small !== undefined ? (
                        <LazyLoadImage
                          src={item?.image_small}
                          loading="lazy"
                          alt="PlaceHolder"
                          className="mlt-image"
                          key={index}
                          id={`mltfocus${index}`}
                          placeholderSrc={'images/landscape_card.png'}
                        />
                      ) : (
                        <LazyLoadImage
                          src="images/landscape_card.png"
                          loading="lazy"
                          alt="PlaceHolder"
                          className="mlt-image"
                          placeholderSrc={'images/landscape_card.png'}
                        />
                      )}
                      <div
                        style={{
                          display:
                            currentButtonFocus === `mlt-${index}` ? '' : 'none'
                        }}
                        className="MLT_Detail"
                      >
                        {/* <div className="seriesMoviesTitlerailvod"> */}
                        <div className="defaultTitlerailvod">{item?.title}</div>
                        <div className="descriptionvod">
                          <div>{item?.duration}</div>
                        </div>
                        {/* </div> */}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div style={{ display: 'flex' }}>
                <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
              </div>
            )}
          </div>
        </div>
        <div className="Footer">
          <button className="FooterButton">
            <LazyLoadImage
              style={{ marginTop: 'auto', marginBottom: 'auto' }}
              width={44}
              height={44}
              src="images/close.png"
            />
            <p className="FooterText">Back</p>
          </button>
        </div>
      </div>
    </div>
  ) : (
    <>
      <VodSkeletonLoading />
    </>
  )
}

export default VodMoviesDetailpage
