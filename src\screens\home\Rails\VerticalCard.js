import React, { useEffect, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import './VerticalCard.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useNavigate } from 'react-router-dom'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { contentSelectionType } from '../../../GoogleAnalyticsConstants'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const VerticalCard = props => {
  const navigate = useNavigate()

  const railRef = useRef([])
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const verticalAmpliado = props?.dataObject?.groups
  let indexValue = verticalAmpliado?.[0] && props?.index ? props?.index : ''
  const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
  const nav = useSelector(state => state?.homeReducer?.navbarData)
  const navData = userDetails?.is_kids === 'true' ? navKids : nav
  const seriesIndex = navData?.findIndex(item => item?.code === 'seriesnv' && item?.page === 'Series' );
  const moviesIndex = navData?.findIndex(item => item?.code === 'peliculas' && item?.page === 'Películas' );

  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
    useEffect(() => {
    const element = document.getElementById(props?.id)
    element && element.scrollIntoView({ behavior: 'smooth' })
    const rows = document.querySelectorAll('.vertrail-wrapper')
    rows.length > 0 &&
      rows.forEach(row => {
        row.addEventListener('keydown', handleKeyDown)
      })
    return () => {
      rows.length > 0 &&
        rows.forEach(row => {
          row.removeEventListener('keydown', handleKeyDown)
        })
    }
  }, [])

  const handleFocus = (data,index) => {
    railRef?.current?.[index]?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    });
  }
  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
    const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbarTab,
        content_list : props?.title,
        modulo_name : 'carrusel'
      }
    if (item?.is_series || item?.contentType === 'chapter') {
      // GA : Content Selection Event
      pushContentSelectionEvent(userData,item, index,contentSelectionType.HOME_SERIES)
      localStorage.setItem('currNavIdx', seriesIndex)
      navigate('/series', { state: { data: item, card: 'series', backfocusid: `index${indexValue}${index}`, pageName:'home' } })
    } else {
      // GA : Content Selection Event
      pushContentSelectionEvent(userData,item,index, contentSelectionType.HOME_MOVIES)
      localStorage.setItem('currNavIdx', moviesIndex)
      navigate('/movies', { state: { vodData: item, card: 'movies', backfocusid: `index${indexValue}${index}`, pageName:'home' } })
    }
  }

  const handleKeyDown = event => {
    const rows = document.querySelectorAll('.vertrail-wrapper')
    if (rows.length > 0) {
      rows.forEach(row => {
        const cards = row.querySelectorAll('.vertical_block')
        const focusedElement = document.activeElement
        const focusedIndex = Array.from(cards).indexOf(focusedElement)

        if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
          const nextCard = cards[focusedIndex + 1]
          const containerRect = row.getBoundingClientRect()
          const nextCardRect = nextCard.getBoundingClientRect()

          if (nextCardRect.right > containerRect.right) {
            row.scrollLeft += nextCardRect.right - containerRect.right
          }
        } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
          const prevCard = cards[focusedIndex - 1]
          const containerRect = row.getBoundingClientRect()
          const prevCardRect = prevCard.getBoundingClientRect()

          if (prevCardRect.left < containerRect.left) {
            row.scrollLeft -= containerRect.left - prevCardRect.left
          }
        }
      })
    }
  }

  return (
    <div className={`${verticalAmpliado?.[0] && "Vertrail-Container"}`}>
      {verticalAmpliado?.[0] ? (
        <div className="vertrailTitle">
          <SafeHTML html={props?.title || ''} />
        </div>
      ) : null}
      {verticalAmpliado?.[0] ? (
        <div className="vertrail-wrapper" id="vertical-rail">
          <div className="ampliado_block">
            {verticalAmpliado?.map((item, index, array) => (
              <>
                <button
                  className="vertical_block focusable"
                  style={{
                    border: props?.id === item.id ? '4px solid #fff' : ''
                  }}
                  ref={el => (railRef.current[index] = el)}
                  key={index}
                  id={`index${props?.index}${index}`}
                  data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined} 
                  data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                  data-testid={`rail_card_click${index}`}
                  onClick={() => goToMoviesSeries(item, index)}
                  onFocus={() => {
                    handleFocus(item,index)
                  }}
                  data-sn-right={index != array.length - 1 && undefined}
                  data-sn-left={index == 0 ? '' : undefined}
                >
                  {item?.images?.medium && item?.images?.medium !== '' || item.image_medium && item.image_medium !== '' ? (
                    <>
                      <LazyLoadImage
                        src={item.image_medium ?? item?.images?.medium}
                        placeholderSrc="images/vertical_card.png"
                        key={item.id}
                        className="rail-image-vert"
                      />
                      {item?.images?.medium && item?.provider?.code == 'amco' ? (
                        item?.format_types === 'ppe,download' || item?.format_types === 'ppe' || item?.contentAttributes?.marketingType === 'ppe,download' || item?.contentAttributes?.marketingType === 'ppe'? (
                          <div className="proveedorBlockRailAlq">
                            <img
                              src={'images/Alquilar.svg'}
                              className="tagAlq"
                            />
                          </div>
                        ) :
                          item?.format_types === 'ppe,est' || item?.contentAttributes?.marketingType === 'ppe,est' ? (
                            <>
                              <div className="comprar-tag-movies" style={item?.is_series ? { backgroundColor: '#40336f' } : null}>COMPRAR</div>
                              <div className="proveedor-block-rail-alq-comp">
                                <img
                                  src={'images/Alquilar.svg'}
                                  className="tagAlq"
                                />
                              </div>
                            </>
                          ) : item?.format_types === 'est' || item?.contentAttributes?.marketingType === 'est' &&
                          <div className="comprar-tag-movies" style={item?.is_series ? { backgroundColor: '#40336f' } : null}>COMPRAR</div>

                      ) : item?.images?.medium &&
                        item?.provider?.code &&
                        item?.images?.medium || item.image_medium &&
                        item?.proveedor_code &&
                        item?.image_medium ? (
                        <div className="proveedorBlockRail_vero_hara">
                          {Addproveedor(providerLabel?.[item?.proveedor_code ?? item?.provider?.code]?.susc) && (
                            <img
                              id="#icon1"
                              className={item?.provider?.code === 'picardia2' || item?.proveedor_code === 'picardia2' ? 'picardia-image' :
                                'premium-icon'
                              }
                              src={Addproveedor(providerLabel?.[item?.proveedor_code ?? item?.provider?.code]?.susc)}
                            />
                          )}
                          {item?.images?.medium &&
                            item?.contentAttributes?.marketingType === 'free' && userDetails?.subscriptions?.length == 0 || item?.image_medium &&
                            item?.format_types === 'free' && userDetails?.subscriptions?.length == 0 ? (
                            <div className="verahora-tag">VER AHORA</div>
                          ) : null}
                          {item?.images?.medium &&
                            item?.provider?.code === 'picardia2' &&
                            item?.images?.medium || item.image_medium &&
                            item?.proveedor_code === 'picardia2' &&
                            item?.image_medium &&
                            <div className="picardia-proveedorBlockRail">
                              <img src={'images/Adultus.svg'} className="picardia-tag" />
                            </div>}
                        </div>
                      ) : null}
                    </>
                  ) : (
                    <img
                      src={
                        <LazyLoadImage
                          src="images/vertical_card.png"
                          loading="lazy"
                          alt="PlaceHolder"
                        />
                      }
                      className="rail-image-vert"
                    />
                  )}
                </button>
              </>
            ))}
          </div>
        </div>
      ) : null}
    </div>
  )
}

export default VerticalCard
