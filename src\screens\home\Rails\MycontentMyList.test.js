import React from "react";
import { fireEvent, getByTestId, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import MycontentMyList from "./MycontentMyList";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mocksuccessresponse = [
    {
        "id": "1136958",
        "title": "El bufón",
        "title_episode": null,
        "title_uri": "El-bufon",
        "title_original": "Jester, The",
        "description": "Dos hermanas separadas se encuentran mientras son acechadas por un ser malévolo.",
        "description_large": "Después de la reciente muerte de su padre, dos hermanas separadas se encuentran siendo acechadas por un ser malévolo conocido como 'El Bufón'. La entidad maligna comienza a atormentar a los habitantes de un pequeño pueblo en la noche de Halloween.",
        "short_description": null,
        "image_large": "https://clarovideocdn7.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SS/JESTERTHEWHORIZONTAL.jpg?size=529x297",
        "image_medium": "https://clarovideocdn5.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SS/JESTERTHEWVERTICAL.jpg?size=200x300",
        "image_small": "https://clarovideocdn7.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SS/JESTERTHEWHORIZONTAL.jpg?size=290x163",
        "image_still": "https://clarovideocdn6.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/STILLS/JESTERTHE-STILL-01.jpg",
        "image_background": "https://clarovideocdn7.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/CLEAN/JESTERTHE_e-1280x720.jpg",
        "url_imagen_t1": "https://clarovideocdn8.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/CLEAN/JESTERTHEWVERTICAL.jpg?size=200x300",
        "url_imagen_t2": "https://clarovideocdn0.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/CLEAN/JESTERTHEWHORIZONTAL.jpg?size=290x163",
        "image_base_horizontal": "https://clarovideocdn4.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SS/JESTERTHEWHORIZONTAL.jpg",
        "image_base_vertical": "https://clarovideocdn2.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SS/JESTERTHEWVERTICAL.jpg",
        "image_base_square": "https://clarovideocdn2.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SS/JESTERTHEWCUADRADO.jpg",
        "image_clean_horizontal": "https://clarovideocdn7.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/CLEAN/JESTERTHEWHORIZONTAL.jpg",
        "image_clean_vertical": "https://clarovideocdn5.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/CLEAN/JESTERTHEWVERTICAL.jpg",
        "image_clean_square": "https://clarovideocdn5.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/CLEAN/JESTERTHEWCUADRADO.jpg",
        "image_sprites": "https://clarovideocdn8.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SPRITES/JESTERTHE-SPRITEBAR.jpg",
        "image_frames": "https://clarovideocdn4.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SPRITES/JESTERTHE-00h-00m-00s-00f.jpg",
        "image_trickplay": "https://clarovideocdn8.clarovideo.net/PELICULAS/JESTERTHE/EXPORTACION_WEB/SPRITES/JESTERTHE-TRICKPLAY.bif",
        "image_external": null,
        "duration": "01:30:05",
        "date": "05/01/2024",
        "year": "2023",
        "preview": "true",
        "season_number": null,
        "episode_number": null,
        "format_types": "ppe,download",
        "live_enabled": "0",
        "live_type": null,
        "live_ref": null,
        "timeshift": null,
        "votes_average": 4,
        "rating_code": "R",
        "proveedor_name": "AMCO",
        "proveedor_code": "amco",
        "encoder_tecnology": {
            "id": null,
            "desc": null
        },
        "recorder_technology": {
            "id": null,
            "desc": null
        },
        "resource_name": null,
        "rollingcreditstime": null,
        "rollingcreditstimedb": null,
        "is_series": false,
        "channel_number": null
    }
]

describe('MycontentMyList page test case', () => {
    beforeEach(() => {
        // Mock scrollIntoView
        Element.prototype.scrollIntoView = jest.fn();
      });
    test('should render onclick railcard', () => {
        initialState.vodMovies = {
            watchList: mocksuccessresponse
        }
        const { container } = renderWithState(<MycontentMyList />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})