import React from 'react'
import {
  fireEvent,
  getByTestId,
  getByText,
  queryByAttribute,
  render
} from '@testing-library/react/'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import EditProfile from './EditProfile'
import { MemoryRouter } from 'react-router-dom'

const initialState = {}
const mockStore = configureStore([])
const history = createHistory()
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <MemoryRouter
      history={history}
      initialEntries={[
        {
          state: {
            data: {
              gamification_id: '65796b',
              username: '<PERSON>s Fakelll',
              user_image:
                'http://clarovideocdn1.clarovideo.net/lanegociadora01.png',
              rol: 'admin',
              admin: true,
              change_name: false,
              is_kids: 'false',
              partnerUserId: '92820606',
              user_hash:
                'OTI4YjI1YWNlNWI3YjcxODkyNQ=='
            }
          }
        }
      ]}
    >
      {children}
    </MemoryRouter>
  </Provider>
)
export const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}
describe('Add Profile page test', () => {
  test('should render api mock data', () => {
    renderWithState(<EditProfile />)
  })

  test('should do button click', () => {
    const props = {
      handleInputChange: jest.fn(),
      onChange: jest.fn()
    }
    const { container } = renderWithState(<EditProfile {...props} />)
    const getById = queryByAttribute.bind(null, 'id')
    const scroll = getById(container, 'abcBtn')
    fireEvent.focus(scroll)
    fireEvent.keyUp(scroll, { keyCode: '461' })
    fireEvent(
      scroll,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      }),
    )
  })

  test('should do button click', () => {
    const props = {
      replace: jest.fn()
    }
    const result = renderWithState(<EditProfile {...props} />)
    let input = result.container.querySelector('input[name="profilename"]')
    fireEvent.focus(input, { target: { value: 'Ls Fakelll' } })
    renderWithState(<EditProfile />)
  })

  test('should do button click', () => {
    const props = {
      replace: jest.fn()
    }
    const result = renderWithState(<EditProfile {...props} />)
    let input = result.container.querySelector('input[name="profilename"]')
    fireEvent.blur(input, { target: { value: 'Ls Fakelll' } })
    renderWithState(<EditProfile />)
  })

  test('should render onclick railcard moviesrailcardmockdata', () => {
    const { container } = renderWithState(<EditProfile />)
    const getbytestid = getByTestId(container, 'edit-btns')
    fireEvent.blur(getbytestid)
    fireEvent.focus(getbytestid)
    fireEvent.keyUp(getbytestid, { keyCode: '403' })
    fireEvent(
        getbytestid,
        new MouseEvent('click', {
            bubbles: true,
            cancelable: true
        })
    )
})

test('should render onclick', () => {
  const { container } = renderWithState(<EditProfile />)
  const getById = queryByAttribute.bind(null, 'id');
  const scroll = getById(container, 'profButton');
  
  fireEvent.blur( scroll)
  fireEvent.focus( scroll)
  fireEvent(
    scroll,
      new MouseEvent('click', {
          bubbles: true,
          cancelable: true
      })
  )
})

test('should render onclick chooseProfile', () => {
  const { container } = renderWithState(<EditProfile />)
  const getById = queryByAttribute.bind(null, 'id');
  const scroll = getById(container, 'ChooseProfileText_Container_id');
  fireEvent.keyUp(scroll, { keyCode: '38' })
  fireEvent(
    scroll,
      new MouseEvent('click', {
          bubbles: true,
          cancelable: true
      })
  )
})

test('handles Samsung TV green key', () => {
  const props = {
    onChange: jest.fn(),
    type: 'EditProfile',
    setCurrentButtonFocus: jest.fn()
  };
  global.tizen = {
    tvinputdevice: {
      registerKeyBatch: jest.fn(),
      getKey: jest.fn().mockReturnValue({ code: 10009 }),
    },
  };
  const { container } = renderWithState(<EditProfile {...props} />);
  const getById = queryByAttribute.bind(null, 'id');
  const scroll = getById(container, 'ChooseProfileText_Container_id');
  fireEvent.keyUp(scroll, { keyCode: 10009 });
  fireEvent(
    scroll,
      new MouseEvent('click', {
          bubbles: true,
          cancelable: true
      })
  )
  delete global.tizen;
})


test('should render onclick handleProfile', () => {
  const { container } = renderWithState(<EditProfile />)
  const getById = queryByAttribute.bind(null, 'id');
  const scroll = getById(container, 'handle-profile');
  fireEvent(
    scroll,
      new MouseEvent('click', {
          bubbles: true,
          cancelable: true
      })
  )
})

})
