import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import LockedChannelList from "./LockedChannelList";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<Router history={history}>{children}</Router>
	</Provider>
);
export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper });
};
const mockErrorResponse = {
	"status": "1"
}

const mockLockedChannelListSuccessResponse = {
	response: {
		groups: [
			{
				channelNumber: "3",
				data: {
					image_large: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/L1/EXPORTACION_WEB/SS/L1WHORIZONTAL.jpg?size=529x297"
				}
			},
			{
				channelNumber: "7",
				data: {
					image_large: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/L1/EXPORTACION_WEB/SS/L1WHORIZONTAL.jpg?size=529x297"
				}
			}
		]
	},
}


describe('Locked channel List Landing page test', () => {

	test('should render without api mock data', () => {
		initialState.settingsReducer = {
			lockedChannelsList: mockErrorResponse
		}
		renderWithState(<LockedChannelList />)
	})

	test('should render with api mock data', () => {
		initialState.settingsReducer = {
			lockedChannelsList: mockLockedChannelListSuccessResponse
		}
		renderWithState(<LockedChannelList />)
	})

	test('navigation to parental control settings page', () => {
		const props = {
			setScreenName: jest.fn()
		}
		const { container } = renderWithState(<LockedChannelList {...props} />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'terminarClick')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		fireEvent.keyUp(buttonClick, { keyCode: '10009' })
		fireEvent.keyUp(buttonClick, { keyCode: '404' })
	})

	test('navigation to action screen for unlocking channel page', () => {
		initialState.settingsReducer = {
			lockedChannelsList: mockLockedChannelListSuccessResponse
		}
		const { container } = renderWithState(<LockedChannelList />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'lock0')
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		history.push('/settings/actionScreenSettings')

	})
})
