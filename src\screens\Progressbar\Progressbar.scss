@keyframes moveToRight {
  from {
    left: 0;
  }

  to {
    left: 100%;
  }
}

.progress-outer {
  display: flex;
  align-items: center;
  background-color: #2c2c2c;
  height: 16px;
  border-radius: 8px;
  width: 228px;
  text-align: justify;
  top: 64px;
  left: 8px;
  position: absolute;
}

.Serie-large-progress {
  display: flex;
  align-items: center;
  background-color: #2c2c2c;
  height: 16px;
  border-radius: 8px;
  width: 1054px;
}

.progress-small-outer {
  width: 100%;
  background-color: #2c2c2c;
  border-radius: 8px;
  height: 16px;
  padding: 0 0 0 3px;


  .progress-small-red-bar {
    height: 8px;
    border-radius: 4px;
    background-color: #981c15;
    position: absolute;
  }
}

.progress-info {
  width: 30px;
  padding-left: 5px;
}

.progress {
  height: 16px;
  display: flex;
  align-items: center;

  .progress-bar {
    height: 8px;
    border-radius: 4px;
    background-color: #de1717;
    position: relative;
    left: 5px;
  }
}

.Progres-Series {
  height: 16px;
  border-radius: 8px;
  background-color: #2c2c2c;
  margin-top: 1px;
}

.largebar-progress {
  height: 10px;
  width: 455px;
  top: 3px;
  left: 5px;
  position: relative;
  border-radius: 4px;
  background-color: #981c15;
}

.progress.progress--medium {
  height: 5px;
}

.progress.progress--large {
  height: 10px;
}

.progress.progress--loading .progress-bar {
  width: 30%;
  animation: moveToRight 1s infinite linear;
  border-radius: 3%;
}
