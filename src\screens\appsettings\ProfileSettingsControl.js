import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './ProfileSettingsControls.scss'
import Navbar from '../home/<USER>'
import ReminderViewSettings from './ReminderViewSettings'
import ParentalControlSettings from './ParentalControlSettings'
import { getControlPin } from '../../store/slices/settingsSlice'
import LanguageSettings from "./LanguageSettings";

const ProfileSettingsControl = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const [activeScreen, setActiveScreen] = useState('idioma')

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleScreenChangeClick = (e, screenName) => {
    e.preventDefault()
    setActiveScreen(screenName)
  }


  const keypresshandler = event => {
    const keycode = event.keyCode
    if (!state?.subPage && (keycode === 10009 || keycode === 461)) {
      state?.returnPageName && navigate('/livePlayer', {
        state: { showControls: 'live', featureTag: true },
        replace: true
      })
      !state?.returnPageName && navigate('/settings', { state: { activeTab: 'profile-settings' } })
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler, state])


  useEffect(() => {
    if (state?.defaultFocus) {
      document.getElementById('idiomaButton')?.focus()
    } else if (state?.screenActive == 'recordatorios') {
      document.getElementById('recordatoriosButton')?.focus()
    } else document.getElementById('parentalControl')?.focus()
  }, [state?.screenActive, state?.defaultFocus])

  useEffect(() => {
    state?.pageName && setActiveScreen(state?.pageName)
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_id: userDetails?.user_id,
      user_token: userDetails?.user_token
    }
    dispatch(getControlPin(payload))
  }, [])

  return (
    <div>
      <Navbar page={'settings'} />
      <div className="profile-settings-control-div focusable">
        <div className="btn-container">
          <button
            className={`idioma-button ${activeScreen == 'idioma' ? 'idioma-button-active' : ''} focusable`}
            id="idiomaButton"
            onClick={e => handleScreenChangeClick(e, 'idioma')}
          >
            {truncateText('language_title', 30)}
          </button>
          <button
            className={`btn-text ${activeScreen == 'recordatorios' ? 'recordatorios-active' : ''} focusable`}
            id="recordatoriosButton"
            onClick={e => handleScreenChangeClick(e, 'recordatorios')}
          >
            {truncateText('reminders_title', 30)}
          </button>
          <button
            className={`control-parental-btn ${activeScreen == 'parentalControl' ? 'parentalControl-active' : ''} focusable`}
            id="parentalControlButton"
            onClick={e => handleScreenChangeClick(e, 'parentalControl')}
          >
            {truncateText('parental_title', 30)}
          </button>
        </div>
      </div>
      {activeScreen == 'idioma' ? (
        <LanguageSettings />
      ) : activeScreen == 'recordatorios' ? (
        <ReminderViewSettings
          screenName={'recordatorios'}
          defaultFocus={false}
        />
      ) : activeScreen == 'parentalControl' ? (
        <ParentalControlSettings
          screenName={'parentalControl'}
          defaultFocus={false}
          selectedValue={state?.selectedValue}
        />
      ) : null}
    </div>
  )
}

export default React.memo(ProfileSettingsControl)
