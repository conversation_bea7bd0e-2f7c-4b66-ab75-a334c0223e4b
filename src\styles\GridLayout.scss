// Focus css
.mosaic-channel-item:focus {
  border: 4px solid #fffff0;
  opacity: 1;
  border-radius: 3px;
  outline: unset;
}

// Common properties for favourite and lock icons
$lock-favourite-icon-common-properties: (
  position: absolute,
  top: 0,
  width: 75px,
  height: 35px,
  margin-top: 10px
);

// Favourite channel icon
.favourite-channel-icon {
  @each $property, $value in $lock-favourite-icon-common-properties {
    #{$property}: $value;
  }
  left: 0;
}

// Lock channel icon
.lock-channel-icon {
  @each $property, $value in $lock-favourite-icon-common-properties {
    #{$property}: $value;
  }
  right: 0;
}


// Grid Layout 1
.grid-layout-1-container {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(1, 1fr);
  background-color: #121212;
  padding: 40px 0px 0px 0px;
  place-items: center;

  .grid-layout-1 {
    width: 1332px !important;
    height: 768px !important;
    background: #282828;
    align-items: center;
    justify-content: center;
    display: flex;
    position: relative;

    .grid-layout-1-channel-logo {
      width: 316px;
      height: 128.44px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// Grid Layout 2
.grid-layout-2-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  background-color: #121212;
  padding: 40px 68px 201px 68px;
  display: flex;
  justify-content: space-between;
  
  width: 1784px;

  .grid-layout-2 {
    background: #282828;
    width: 880px;
    height: 504px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .grid-layout-2-channel-logo {
      width: 316px;
      height: 128.44px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// Grid Layout 3
.grid-layout-3-container {
  display: grid;
  grid-gap: 24px;
  grid-template-columns: repeat(3, calc(38.53% - 24px));
  background: #121212;
  padding: 40px 68px 37px 68px;
  width: 1760px;
  height: 731px;

  .grid-layout-3-item-1 {
    grid-area: 1 / 1 / span 3 / span 2;
    background: #282828;
    width: 1332px;
    height: 768px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .grid-layout-3-item {
    width: 428px;
    height: 240px;
    background: #282828;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .grid-layout-3-channel-logo {
    width: 316px;
    height: 128.44px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}


// Grid Layout 4
.grid-layout-4-container {
  display: grid;
  grid-gap: 24px;
  grid-template-columns: repeat(3, 1fr);
  background: #121212;
  padding: 40px 68px 37px 68px;
  width: 1784px;
  height: 731px;
  place-items: center;

  .grid-layout-4-item-1 {
    grid-area: 1 / 1 / span 3 / span 2;
    background: #282828;
    width: 1332px;
    height: 768px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .grid-layout-4-item {
    width: 428px;
    height: 240px;
    background: #282828;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }

  .grid-layout-4-channel-logo {
    width: 316px;
    height: 128.44px;
    display: flex;
    justify-content: center;
    align-items: center;

    &-image {
      width: 316px;
      height: 128px;
    }
  }
}


// Grid Layout 5
.grid-layout-5-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px;
  background-color: #121212;
  justify-content: center;
  grid-template-columns: 0px;
  gap: 24px;
  width: 1554px;
  padding: 40px 183px 37px 183px;
}

.grid-layout-5-item-1 {
  grid-area: 1 /1 / span 2 / span 3;
  width: 880px;
  height: 504px;
  background: #282828;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.grid-layout-5-item-4 {
  grid-area: 3 /3;
  width: 428px;
  height: 240px;
  margin-top: -253px;
  background: #282828;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.grid-layout-5-item-2 {
  grid-area: 1 /4;
  width: 650px;
  height: 372px;
  background: #282828;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.grid-layout-5-item-5 {
  grid-area: 2 /4;
  width: 650px;
  height: 372px;
  background: #282828;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.grid-layout-5-item-3 {
  width: 428px;
  height: 240px;
  margin-top: -253px;
  background: #282828;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.grid-layout-5-channel-logo {
  width: 316px;
  height: 128.44px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Grid Layout6
.grid-layout-6-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 24px;
  background-color: #121212;
  width: 1784px;
  height: 1018px;
  padding: 40px 68px 37px 68px;
}

.grid-layout-6 {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #282828;
  width: 428px;
  height: 240px;
  position: relative;
}

.grid-layout-6:nth-child(1) {
  grid-column-start: 1;
  grid-row-start: 1;
}

.grid-layout-6:nth-child(2) {
  grid-column-start: 3;
  grid-row-start: 1;
  justify-self: center;
  align-self: center;
}

.grid-layout-6:nth-child(3) {
  grid-column-start: 3;
  grid-row-start: 1;
}

.grid-layout-6:nth-child(4) {
  grid-column-start: 4;
  grid-row-start: 1;
}

.grid-layout-6:nth-child(5) {
  grid-column-start: 1;
  grid-row-start: 2;
}

.grid-layout-6:nth-child(6) {
  grid-column-start: 2;
  grid-row-start: 2;
}

.grid-layout-6-item1 {
  width: 880px;
  height: 504px;
  grid-column-end: span 2;
  grid-row-end: span 2;
}

.grid-layout-6-item2 {
  width: 880px;
  height: 504px;
  grid-column-end: span 2;
  grid-row-end: span 2;
}

.grid-layout-6-item3 {
  width: 428px;
  height: 240px;
  grid-column-end: span 1;
  grid-row-end: span 1;
}

.grid-layout-6-channel-logo {
  width: 316px;
  height: 128px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Grid Layout 7
.grid-layout-7-container {
  display: grid;
  grid-template-columns: 650px 650px 428px;
  gap: 24px;
  background-color: #121212;
  width: 1776px;
  height: 1080px;
  padding: 40px 72px 37px 72px;
  justify-content: center;
}

.grid-layout-7 {
  background-color: #282828;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-layout-7-column1 {
  grid-column: 1;
  position: relative;
}

.grid-layout-7-column2 {
  grid-column: 2;
  position: relative;
}

.grid-layout-7-column3 {
  grid-column: 3;
  position: relative;
}

.grid-layout-7-row1 {
  grid-row: 1;
  position: relative;
}

.grid-layout-7-row2 {
  grid-row: 2;
  margin-bottom: 313px;
  position: relative;
}

.grid-layout-7.grid-layout-7-column1,
.grid-layout-7.grid-layout-7column2 {
  width: 650px;
  height: 372px;
  position: relative;
}

.grid-layout-7.grid-layout-7-column3 {
  width: 428px;
  height: 240px;
  position: relative;
}

.grid-layout-7-column3.data6 {
  margin-top: -134px;
  position: relative;
}

.grid-layout-7-column3.data7 {
  margin-top: 129px;
  position: relative;
}

.grid-layout-7-channel-logo {
  width: 316px;
  height: 128px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Grid Layout 8
.grid-layout-8-container {
  display: flex;
  padding: 40px 183px 37px 183px;
  background: #121212;
  // grid-gap: 24px;
  column-gap: 24px;
  // gap:24px;
}

.grid-layout-8-left {
  display: flex;
  flex-direction: column;
  // grid-gap: 24px;
  row-gap: 24px;
}

.grid-layout-8-left-item {
  flex: 1;

  background-color: #282828;

  width: 650px;
  height: 372px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-layout-8-right {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  // grid-gap: 24px;
  column-gap: 24px;
  row-gap: 24px;
}

.grid-layout-8-right-item {
  flex-basis: calc(49% - 10px);

  background-color: #282828;
  width: 428px;
  height: 240px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-layout-8-right-item:nth-child(2) {
  order: 2;
}

.grid-layout-8-right-item:nth-child(3) {
  order: 3;
}

.grid-layout-8-right-item:nth-child(4) {
  order: 1;
}

.grid-layout-8-right-item:nth-child(5) {
  order: 2;
}

.grid-layout-8-right-item:nth-child(6) {
  order: 3;
}

.grid-layout-8-channel-logo {
  width: 316px;
  height: 128px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Grid Layout 9
.grid-layout-9-container {
  width: 1784px !important;
  padding: 40px 68px 37px 68px !important;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-row-gap: 18px;
  grid-column-gap: 10px;
  background-color: #121212;
}

.grid-layout-9-item-1 {
  grid-area: 1 / 1 / span 2 / span 2;
  background: #282828;
  height: 504px;
  width: 880px;
  grid-column: span 2;
  grid-row: span 2;
}

.grid-layout-9-item-rest {
  background: #282828;
  height: 240px;
  width: 428px;
}

.grid-layout-9-item {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.grid-layout-9-channel-logo {
  width: 190px;
  height: 77px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Grid Layout > 9
.grid-layout-greater-9-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 0px 68px 37px 68px;
  overflow: scroll;
  height: 768px;
  top: 0;
  margin-top: 40px;
}

.grid-layout-greater-9-item {
  display: flex;
  justify-content: center;
  align-items: center;
 position: relative;
}

.grid-layout-gretaer-9-channel-logo {
  width: 190px;
  height: 77px;
  display: flex;
  justify-content: center;
  align-items: center;
}
