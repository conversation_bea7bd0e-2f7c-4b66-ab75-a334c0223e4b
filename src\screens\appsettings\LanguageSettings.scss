$font-family: '<PERSON>o', sans-serif;
$primary-color: #fff;
$border-color: #475865;

@mixin flex-container($justify: space-between, $align: start) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
}

@mixin text-style($size, $line-height, $color: $primary-color) {
  color: $color;
  font-family: $font-family;
  font-size: $size;
  line-height: $line-height;
  letter-spacing: 0;
}

.selected-container {
  margin: 0 47px 0 45px;

  .lang-title-text {
    @include text-style(60px, 71px);
    margin: 0px;
  }

  .horizontal-line {
    height: 1px;
    border: 1px solid $border-color;
    width: 1781px;
  }

  .lang-setting-main-container {
    @include flex-container;

    .text-detail-container-left-side {
      .text-detail-title-left {
        @include text-style(30px, 35px);
        max-width: 876px;
      }
    }

    .text-detail-container-right-side {
      margin-right: 75px;

      .text-detail-title-right {
        @include text-style(24px, 28px);
        margin-bottom: 10px;
      }

      .dropdown-container {
        width: 433px;

        .dropdown-btn {
          @include flex-container(left, center);
          box-sizing: border-box;
          width: 400px;
          height: 60px;
          border: 2px solid $primary-color;
          border-radius: 30px;
          @include text-style(30px, 35px);

          .dropdown-btn-text{
            margin-left: 20px;
          }
          .dropdown-bottom-chevron {
            .dropdown-chevron-down-img {
              height: 10px;
              width: 21px;
              margin-left: 10px;
              padding-bottom: 3px;
            }
          }
        }
        .dropdown-btn:focus{
          border: 6px solid #FFFFFF;
		      border-radius: 30px;
        }
      }
    }
  }
}