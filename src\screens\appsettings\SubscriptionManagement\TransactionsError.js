import React, { useCallback,useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import './TransactionsError.scss'
import { clearPaymentError } from '../../../store/slices/settingsSlice'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'

const TransactionsError = props => {
  const region = localStorage.getItem('region')
  const dispatch = useDispatch()
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  
  const translations =
    apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const apilanguage = translations?.language?.[region]

  const { paymentErrorResponse, handleGoPreviousPage } = props

  const handleTranslationchange = useCallback(
    (str, length) => {
      const text = apilanguage?.[str] ?? str
      if (!length) {
        length = 100
      }
      if (text?.length >= length) {
        return `${text?.slice(0, length)}...`
      } else {
        return text
      }
    },
    [apilanguage]
  )

  const backNavigation = () => {
    dispatch(clearPaymentError())
    handleGoPreviousPage()
  }

  useEffect(()=>{
   pushScreenViewEvent({screenName:'transaction_error_screen', screenData: userDetails, prevScreenName: 'check_out_screen'})
  },[])

  return (
    <div className="transactions-error-container">
      <div className="transactions-error-logo">
        <img
          src={'images/claro-profile-logo.png'}
          className="claro-logo-te"
          alt="logo"
        />
      </div>
      <div className="transactions-warning-sign transactions-error-base-layout">
        <img
          src={
            apaAssetsImages?.Transaccionales_ErrorGeneral_IconoAlerta ||
            'images/ic_alert.png'
          }
          className="claro-logo-te"
          alt="logo"
        />
      </div>
      <span className="transactions-error-title transactions-error-base-layout">
        {handleTranslationchange(
          apilanguage?.[
            `Transaccionales_ErrorGeneral_TextoTitulo_${paymentErrorResponse?.errors?.[0]?.code}`
          ]
            ? `Transaccionales_ErrorGeneral_TextoTitulo_${paymentErrorResponse?.errors?.[0]?.code}`
            : `Transaccionales_ErrorGeneral_TextoTitulo`
        )}
      </span>
      <span className="transactions-error-title-2 transactions-error-base-layout">
        {handleTranslationchange(
          apilanguage?.[
            `Transaccionales_ErrorGeneral_TextoComplementario_${paymentErrorResponse?.errors?.[0]?.code}`
          ]
            ? `Transaccionales_ErrorGeneral_TextoComplementario_${paymentErrorResponse?.errors?.[0]?.code}`
            : `Transaccionales_ErrorGeneral_TextoComplementario`
        )}
      </span>
      <span className="transactions-error-button-container transactions-error-base-layout">
        <button
          className="transactions-error-button focusable"
          autoFocus={true}
          onClick={backNavigation}
          style={{
            backgroundColor: handleTranslationchange(
              apilanguage?.Color_ColorPrimario
                ? 'Color_ColorPrimario'
                : '#981C15'
            )
          }}
        >
          <span className="transactions-error-button-text">
            {handleTranslationchange(
              `Transaccionales_ErrorGeneral_TextoBotonPrimario`
            )}
          </span>
        </button>
      </span>
    </div>
  )
}

export default TransactionsError
