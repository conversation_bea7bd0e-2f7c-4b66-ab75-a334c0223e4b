import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { emailPattern } from './Regex'
import {
  getBackNavigate,
  getClearAllLoginStates,
  getLoginNavigation
} from '../../store/slices/login'
import { store } from '../../store/sagaStore'
import '../loginregister/Loginpage.scss'
import { pushLoginEvent,pushScreenViewEvent } from '../../../src/GoogleAnalytics'

const Loginpagemail = () => {
  let data
  const { state } = useLocation()
  const navigate = useNavigate()
  const [email, setEmail] = useState(state?.value)
  const [validEmail, setValidEmail] = useState(false)
  const [inputFocus, setInputFocus] = useState(false)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const loginNavigation = useSelector(state => state?.login?.loginNavigation)
  const userInfo = useSelector(state => state?.login?.userInfo)
  const startTimeRef = useRef(null)

  const handleInputChange = e => {
    let inputVal = e.target.value
    const re = /\s/g
    if (re.test(inputVal)) {
      inputVal = inputVal.replace(re, '').trim()
    }
    setEmail(inputVal)
    const emoji =
      /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g
    const name = 'email'
    const value = inputVal.replace(emoji, '').trim()
    validate(name, value)
  }

  useEffect(() => {
      startTimeRef.current = Date.now()
      return () => {
      startTimeRef.current = null
    }
   }, [])

  const handleTranslationchange = useCallback(
    keyname => {
      if (!apilanguage?.[keyname]) {
        return [keyname]
      } else {
        return apilanguage?.[keyname]
      }
    },
    [apilanguage]
  )

  const validate = (name, value) => {
    if (name === 'email') {
      const isEmailValid = emailPattern.test(value)
      setValidEmail(isEmailValid)
    }
  }
  const clickNext = e => {
    const engagement_time_msec = Date.now() - startTimeRef.current
    data = { email: email }
    e.preventDefault()
    localStorage.setItem('signinemail', email)
    store.dispatch(getLoginNavigation(true))
     //GA for Login Event
      pushLoginEvent(
        handleTranslationchange(
          'Onboarding_Inicio_sesionRCU_Form_Texto'
        )?.toLowerCase(),
        handleTranslationchange(
          'Onboarding_Inicio_sesionRCU_Form_TextoTitulo'
        )?.toLowerCase(),
        engagement_time_msec
      )
    startTimeRef.current = Date.now()
    if (loginNavigation == true && userInfo && userInfo?.status == '1') {
      navigate('/signin', {
        state: {
          focus: true,
          value: email,
          password: state?.password,
          seriesEpisodeData: state?.seriesEpisodeData,
          fromDetailsPage: state?.fromDetailsPage,
          pageName: state?.pageName,
          validEmail
        }
      })
    } else {
      store.dispatch(getBackNavigate(false))
      navigate('/loginpassword', {
        state: { 
          page: 'loginmail', 
          value: email, 
          validEmail, 
          seriesEpisodeData: state?.seriesEpisodeData, 
          fromDetailsPage: state?.fromDetailsPage,
          pageName: state?.pageName
        }
      })
    }
    store.dispatch(getClearAllLoginStates())
  }

  const keypresshandler = e => {
    if (e?.keyCode === 10009 || e?.keyCode === 461) {
      navigate('/signin', {
        state: {
          page: 'loginmail',
          value: email,
          password: state?.password,
          seriesEpisodeData: state?.seriesEpisodeData,
          fromDetailsPage: state?.fromDetailsPage,
          pageName: state?.pageName,
          focus: true
        }
      })
    }
    if (e.keyCode == 13 || e.keyCode == 65376 || e.keyCode == 40) {
      e.preventDefault()
      e.stopPropagation()
      document.getElementById('registernext')?.focus({ focusVisible: true })
    } else if (e.keyCode == 38) {
      e.preventDefault()
      e.stopPropagation()
      document.getElementById('emailAddress')?.focus({ focusVisible: true })
    }
  }

useEffect(()=>{
  pushScreenViewEvent({screenName:'login_mail_screen',  prevScreenName: 'signin_screen'})
 },[])

  useEffect(() => {
    email?.length > 0 && validate('email', email)
  }, [email])

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className="App">
      <div className="App-logo">
        <img
          src={'images/claro-video-logo.png'}
          className="logo-img"
          alt="logo"
        />
      </div>
      <div className="login-email-container">
        <label className="login-email" htmlFor="emailAddress">
          {handleTranslationchange('Onboarding_Inicio_sesionRCU_Form_TextoTitulo')}
        </label>
        <input
          className='login-email-input'
          value={email}
          type="email"
          name="email"
          id="emailAddress"
          onChange={e => handleInputChange(e)}
          autoFocus
        />
      </div>
      <div className="sign-in-btn-container">
        <button
          className={`${!validEmail ? 'loginmail-signature' : 'loginmail-signature-enable'} focusable`}
          id="registernext"
          disabled={!validEmail}
          onFocus={() => setInputFocus(true)}
          onBlur={() => setInputFocus(false)}
          data-sn-up={'#emailAddress'}
          onClick={e => clickNext(e)}
        >
          {handleTranslationchange('Onboarding_Inicio_sesionRCU_Form_Texto')}
        </button>
      </div>
    </div>
  )
}

export default Loginpagemail
