import React, { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
	getChangeControlPin,
	getClearAllSettingsState,
	getRemindControlPin,
	getCheckControlPin
} from '../../store/slices/settingsSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import AlphaNumericKeyboard from '../Keyboard/AlphaNumericKeboard'
import './Settings.scss'
import { pushNewInteractionContentEvent, pushScreenViewEvent } from '../../GoogleAnalytics'
import { interactionType, PIN_PROTECTION, TV } from '../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const ChangePin = () => {
	const navigate = useNavigate()
	const dispatch = useDispatch()
	const { state } = useLocation()

	const region = localStorage.getItem('region')

	const pinRef = useRef([])

	const invisible = 'images/Icono_Onboarding_Close.png'
	const visible = 'images/Icono_Onboarding_Open.png'

	const [currentPin, setCurrentPin] = useState(new Array(6).fill(''))
	const [newPin, setNewPin] = useState(new Array(6).fill(''))
	const [visbilityIcon, setVisibilityIcon] = useState(invisible)
	const [buttonDisable, setButtonDisable] = useState(true)
	const [enableNewPin, setEnableNewPin] = useState(false)
	const [pinInvalid, setPinInvalid] = useState('')
	const [keyboardFocus, setKeyboardFocus] = useState(false)
	const [focusedIdx, setFocusedIdx] = useState(0)

	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
	const checkSecurityPin = useSelector(
		state => state?.settingsReducer?.checkControlPin
	)
	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
	const changeSecurityPin = useSelector(
		state => state?.settingsReducer?.changeControlPin
	)
	const changeSecurityPinError = useSelector(
		state => state?.settingsReducer?.changeControlPinError
	)
	const remindSecurityPin = useSelector(
		state => state?.settingsReducer?.remindControlPin?.response
	)

	const translations =
		apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const apilanguage = translations?.language?.[region]

	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	}

	const handlePinVisbility = () => {
		visbilityIcon === invisible
			? setVisibilityIcon(visible)
			: setVisibilityIcon(invisible)
	}

	const handleOTPChange = (element, index) => {
		const value = element.value ?? element

		if (value == 'cl' && !enableNewPin) {
			setCurrentPin(currentPin.map((d, idx) => (idx === focusedIdx ? '' : d)))
			setFocusedIdx(index === 0 ? index : index - 1)
			return
		} else if (value == 'cl' && enableNewPin) {
			setNewPin(newPin.map((d, idx) => (idx === focusedIdx ? '' : d)))
			setFocusedIdx(index === 0 ? index : index - 1)
			return
		} else if (value == 'clr' && !enableNewPin) {
			setCurrentPin(currentPin.fill(''))
			setFocusedIdx(0)
		} else if (value == 'clr' && enableNewPin) {
			setNewPin(newPin.fill(''))
			setFocusedIdx(0)
		}
		enableNewPin
			? setNewPin(
				newPin.map((d, idx) => (idx === index && !isNaN(value) ? value : d))
			)
			: setCurrentPin(
				currentPin.map((d, idx) =>
					idx === index && !isNaN(value) ? value : d
				)
			)

		if (!element.value && !isNaN(value)) {
			setFocusedIdx(index === 5 ? index : index + 1)
		}
		setPinInvalid('')
	}

	const handleCheckOldPin = () => {
		pushNewInteractionContentEvent(
			state?.gaContentData,
			PIN_PROTECTION,
			TV,
			interactionType?.SIGUIENTE
    	)
		const payload = {
			controlPIN: currentPin.join(''),
			userId: userDetails?.user_id,
			hks: userDetails?.session_stringvalue,
			parental: 1
		}
		dispatch(getCheckControlPin(payload))
	}

	const handleNewPin = () => {
		pushNewInteractionContentEvent(
			state?.gaContentData,
			PIN_PROTECTION,
			TV,
			interactionType?.SIGUIENTE
		)
		const payload = {
			new_code: newPin.join(''),
			code: currentPin.join(''),
			user_token: userDetails?.user_token,
			user_id: userDetails?.user_id,
			hks: userDetails?.session_stringvalue
		}
		dispatch(getChangeControlPin(payload))
	}

	const handleForgotPin = () => {
		pushNewInteractionContentEvent(
			state?.gaContentData,
			PIN_PROTECTION,
			TV,
			interactionType?.OLVIDASTE_TU_PIN_DE_SEGURIDAD
    	)
		const payload = {
			hks: userDetails?.session_stringvalue,
			user_hash: userDetails?.session_userhash
		}
		dispatch(getRemindControlPin(payload))
	}

	useEffect(() => {
		remindSecurityPin &&
			navigate('/PinConfirmation', {
				state: {
					pageName: 'changePin'
				}
			})
	}, [remindSecurityPin])

	useEffect(() => {
		if (
			currentPin &&
			currentPin[0]?.length > 0 &&
			currentPin[1]?.length > 0 &&
			currentPin[2]?.length > 0
		) {
			setButtonDisable(false)
		} else {
			setButtonDisable(true)
		}
		currentPin[5]?.length > 0 &&
			document.getElementById('siguienteButton')?.focus()
	}, [currentPin])

	useEffect(() => {
		if (
			newPin &&
			newPin[0]?.length > 0 &&
			newPin[1]?.length > 0 &&
			newPin[2]?.length > 0 &&
			newPin[4]?.length > 0 &&
			newPin[5]?.length > 0
		) {
			setButtonDisable(false)
			setTimeout(() => {
				document.getElementById('siguienteButton')?.focus()
			}, 200)
		} else {
			setButtonDisable(true)
		}
	}, [newPin])

	useEffect(() => {
		pushScreenViewEvent({screenName:'change_pin', screenData: userDetails, prevScreenName: 'parental_control'})
		setKeyboardFocus(true)
		// const blinkerText = () => {
		// 	if (document.querySelector('.pin-focused')) {
		// 		let p = document.querySelector('.pin-focused').value
		// 		if (p?.substr(-1, 1) == '|') {
		// 			let removePipe = p?.replace('|', '')
		// 			document.querySelector('.pin-focused').value = removePipe
		// 		} else if (p?.length == 0) {
		// 			document.querySelector('.pin-focused').value = p + '|'
		// 		}
		// 	}
		// }

		// const blinkerInterval = setInterval(blinkerText, 1000)
		// return () => {
		// 	clearInterval(blinkerInterval)
		// }
	}, [])

	useEffect(() => {
		if (checkSecurityPin?.msg === 'OK') {
			setEnableNewPin(true)
			setVisibilityIcon(invisible)
			setButtonDisable(true)
			setPinInvalid('')
			setFocusedIdx(0)
			document.getElementById('Key_0')?.focus()
		} else if (checkSecurityPin?.msg === 'ERROR') {
			setPinInvalid(checkSecurityPin?.errors)
			dispatch(getClearAllSettingsState())
			setCurrentPin(new Array(6).fill(''))
			setVisibilityIcon(invisible)
			document.getElementById('Key_0')?.focus()
			setButtonDisable(true)
			setFocusedIdx(0)
		}
	}, [checkSecurityPin])

	useEffect(() => {
		if (changeSecurityPin?.status == 200) {
			navigate('/settings/profile-settings', {
				state: { pageName: 'parentalControl' }
			})
			dispatch(getClearAllSettingsState())
		} else if (changeSecurityPinError?.errors?.[0]?.error) {
			setPinInvalid(changeSecurityPinError?.errors?.[0]?.error)
			dispatch(getClearAllSettingsState())
			setEnableNewPin(false)
			setCurrentPin(new Array(6).fill(''))
			setNewPin(new Array(6).fill(''))
			setKeyboardFocus(true)
		}
	}, [changeSecurityPin, changeSecurityPinError])


	const handlesamsungkey = (key, keycode) => {
		if (
			key.yellowcode === keycode ||
			keycode === 10009 ||
			keycode == 405 ||((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
			((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 121) || keycode === 406
		) {
			pushNewInteractionContentEvent(
				state?.gaContentData,
				PIN_PROTECTION,
				TV,
				interactionType?.CANCELAR
      		)
			enableNewPin
				? (setEnableNewPin(false), setButtonDisable(false))
				: navigate('/settings/profile-settings', {
					state: { pageName: 'parentalControl' }
				})
		}
	}

	const handleLgkey = keycode => {
		if (
			keycode == 405 ||
			keycode === 461 ||
			keycode == 'backClick'
||((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
			keycode == 10009
		) {
			pushNewInteractionContentEvent(
				state?.gaContentData,
				PIN_PROTECTION,
				TV,
				interactionType?.CANCELAR
      		)
			enableNewPin
				? (setEnableNewPin(false), setButtonDisable(false))
				: navigate('/settings/profile-settings', {
					state: { pageName: 'parentalControl' }
				})
		}
	}
	const handleTVRemoteKey = (event) => {    
		const key = event.key;
		if (/^\d$/.test(key)) {
		  const newPinIndex = newPin.findIndex((digit) => digit === '');
		  const currentpinIndex = currentPin.findIndex((digit) => digit === '');
		  if (newPinIndex !== -1 && enableNewPin) {
			const newOtp = [...newPin];
			newOtp[newPinIndex] = key;
			key?.length == 1 && setNewPin(newOtp)
			if (newPinIndex < 5) {
				setFocusedIdx(newPinIndex + 1)
			  }
			}
			if(currentpinIndex !== -1 && !enableNewPin) {
			const currentOtp = [...currentPin];
			currentOtp[currentpinIndex] = key;
			key?.length == 1 && setCurrentPin(currentOtp)
			if (currentpinIndex < 5) {
				setFocusedIdx(currentpinIndex + 1)
			  }
		  }
		  setPinInvalid('')
		}    
	  };
	  
	const keypresshandler = event => {
		const keycode = event.keyCode
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
			}
			handlesamsungkey(codes, keycode)
		} else {
			handleLgkey(keycode)
		}
		handleTVRemoteKey(event)
	}

	useEffect(() => {
		document.addEventListener('keyup', keypresshandler)
		return () => {
			document.removeEventListener('keyup', keypresshandler)
		}
	}, [keypresshandler])

	return (
		<div className="app-css-change-pin">
			<div className="change-pin-logo-container">
				<img className="change-pin-logo" src={'images/logo.png'} />

				<button
					className="back-indicator-button-pin focusable"
					id="back-button"
					onClick={e => handleLgkey('backClick')}
				>
					<img
						className="yellow-indicator-button"
						src={'images/yellow_shortcut.png'}
					/>
					<img
						className="back-indicator-image"
						src={'images/back_button.png'}
					/>
					<span>{truncateText('top_head_option_button_back', 30)}</span>
				</button>
			</div>
			<div className="left-container-div">
				<AlphaNumericKeyboard
					type="alphaNumeric"
					onChange={e => handleOTPChange(e, focusedIdx)}
					autoFocus={keyboardFocus}
					name="security-pin"
				/>
			</div>
			<div className="right-container-div">
				<span className="pin-screen-title">
					{truncateText('modal_pin_default_title', 30)}
				</span>
				{enableNewPin ? (
					<span className="pin-screen-sub-title">
						{truncateText('setupPin_modal_instruction_label', 100)}
					</span>
				) : (
					<span className="pin-screen-sub-title">
						{truncateText('modal_now_pin_subtitle', 100)}
					</span>
				)}
				<div>
					<>
						{enableNewPin ? (
							<div className="pin-box-div">
								{newPin?.map((item, index) => {
									return (
										<div key={index} className="pin-wrapper">
										<input
											style={
												visbilityIcon === invisible
													? { fontSize: '128px' }
													: { fontSize: '32px' }
											}
											className={
												pinInvalid
													? 'pin-field-invalid'
													: focusedIdx == index
														? 'pin-focused'
														: 'pin-field'
											}
											type={'text'}
											name="newPin"
											onKeyUp={e => !/[0-9]/.test(e.key) && e.preventDefault()}
											id={`currentPin${index}`}
											ref={ref => (pinRef.current[index] = ref)}
											maxLength={1}
											key={index}
											value={
												visbilityIcon === invisible && item?.length > 0
													? '*'
													: item
											}
											onChange={e => handleOTPChange(e.target, index)}
											inputMode="none"
											pattern="[0-9]*"
											readOnly
											data-testid={`currentPin${index}`}
										/>
										  {/* Show blinking pipe cursor if input is focused and empty */}
										  {focusedIdx === index && item === '' && (
											<span className="pin-cursor">|</span>
											)}
										</div>
									)
								})}
								<button
									onClick={handlePinVisbility}
									className="see-pin-button focusable"
									data-testid={`pinVisible`}
									id="see-pin"
								>
									<img src={visbilityIcon} />
								</button>
							</div>
						) : (
							<div className="pin-box-div">
								{currentPin?.map((item, index) => {
									return (
										<div key={index} className="pin-wrapper">
										<input
											style={
												visbilityIcon === invisible
													? { fontSize: '128px' }
													: { fontSize: '32px' }
											}
											className={
												pinInvalid
													? 'pin-field-invalid'
													: focusedIdx == index
														? 'pin-focused'
														: 'pin-field'
											}
											type={'text'}
											name="pin"
											onKeyUp={e => !/[0-9]/.test(e.key) && e.preventDefault()}
											id={`currentPin${index}`}
											ref={ref => (pinRef.current[index] = ref)}
											maxLength={1}
											key={index}
											value={
												visbilityIcon === invisible && item?.length > 0
													? '*'
													: item
											}
											onChange={e => handleOTPChange(e.target, index)}
											inputMode="none"
											readOnly
											data-testid={`pin${index}`}
										/>
										  {/* Show blinking pipe cursor if input is focused and empty */}
										  {focusedIdx === index && item === '' && (
											<span className="pin-cursor">|</span>
										  )}
										</div>
									)
								})}
								<button
									onClick={handlePinVisbility}
									className="see-pin-button focusable"
									data-testid={`pinVisible`}
									id="see-pin"
								>
									<img src={visbilityIcon} />
								</button>
							</div>
						)}
						{pinInvalid ? (
							<p className="pin-error">
								<span className="pin-error-contents">
									{truncateText(
										'lockChannel_tooltip_valid_label_validation',
										50
									)}
								</span>
							</p>
						) : null}

						<button
							className="pin-screen-button pin-screen-button-top focusable"
							id="siguienteButton"
							disabled={buttonDisable}
							onClick={enableNewPin ? handleNewPin : handleCheckOldPin}
						>
							{truncateText('bt_suscripcion_siguiente', 30)}
						</button>

						<button
							className="pin-screen-button-cancel focusable"
							onClick={e => handleLgkey('backClick')}
						>
							{truncateText('modal_pin_cancel_button', 30)}
						</button>

						<button
							onClick={handleForgotPin}
							className="forgot-pin-button focusable"
							data-testid={`forgotPin`}
						>
							<p className="forgot-pin-content">
								{truncateText('modal_pin_forgot_pin', 35)}
							</p>
						</button>
					</>
				</div>
			</div>
		</div>
	)
}

export default React.memo(ChangePin)
