import { calculateLargeVODPosition, calculateLargeVODTop, calculateVODTop } from "./seriesHelpers"






 
describe('landing page test', () => {
    test('it should render the landing Page', async () => {
        expect(calculateVODTop({
          common:{
            extendedcommon:{
              media:{
                serie:{
                  title:"test_test_test_test_test_test_test_test_test_test_test_test_test_test_"
                },
                originaltitle:"test"
              }
            }
          }
        })).toBe("111px")
        
    })
    test("case2",()=>{
      expect(calculateVODTop({
        common:{
          extendedcommon:{
            media:{
              serie:{
                title:"test_test_tes"
              },
              originaltitle:"test_test_test_test_test_test_test_test_test_test_test_test_test_test_"
            }
          }
        }
      })).toBe("")
    })
    test("case3",()=>{
      expect(calculateVODTop({
        common:{
          extendedcommon:{
            media:{
              serie:{
                title:"test_"
              },
              originaltitle:"test_test_test_test_test_test_test_test_test_test_test_test_test_test_"
            }
          }
        }
      })).toBe("")
    })
    test("case4",()=>{
      expect(calculateLargeVODTop({
        common:{
          extendedcommon:{
            media:{
              serie:{
                title:"test_"
              },
              originaltitle:"test_test_test_test_test_test_test_test_test_test_test_test_test_test_"
            }
          }
        }
      })).toBe("100px")
    })
    test("case5",()=>{
      expect(calculateLargeVODPosition({
        common:{
          extendedcommon:{
            media:{
              serie:{
                title:"test_"
              },
              originaltitle:"test_test_test_test_test_test_test_test_test_test_test_test_test_test_"
            }
          }
        }
      })).toBe("absolute")
    })
})