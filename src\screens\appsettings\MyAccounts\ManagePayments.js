import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import BottomBar from "../BottomBar";
import '../../../styles/ManagePayments.css';
import { getPaywayWorkflow } from "../../../store/slices/settingsSlice";

const ManagePayments = (props) => {

	const navigate = useNavigate();
	const dispatch = useDispatch();

	const ClaroImg = "images/claroLogo.png";

	const userDetails = useSelector((state) => state?.login?.isLoggedIn?.response)
	const paymentDetials = useSelector((state) => state?.settingsReducer?.paywayWorkFlow?.response?.workflow)


	const [isPayments,setisPayments] = useState(true);

	const handleAddPayments = () => {
		navigate('payments-method')
	};
 
	const handleView = () => {
		navigate('view-payments');
	}

	useEffect(() => {
		dispatch(getPaywayWorkflow({'userId':userDetails?.parent_id,'hks': userDetails?.session_stringvalue}))
		document.getElementById('add-payments').focus();

	},[])

  return (
		<div className="App-Settings">
			<div className="App-logo">
        <img src={'images/logo.png'} className="logo-img" alt="logo" />
      </div>
			<span className="title">Manage Payment Methods</span>
				<div>
					{!isPayments ?
					<span className="subTitle">Currently, you have no registered payment method</span>
					:
					<div className="paymentsDiv">
						<button className="payment-buttons focusable" style={{gap:'30px'}} id='mobile-number' onClick={handleView}>
							<img src={ClaroImg} style={{height:'38px', marginBottom:'2%'}} />
							<span >Mobile Number</span>
						</button>
						<button className="payment-buttons focusable" style={{gap:'15px'}}>
							<img src={ClaroImg} style={{height:'38px', marginBottom:'2%'}} />
							<span >Telephone Number</span>
						</button>
					</div>
					}
					<div className="add-payments">
						<button className="addPaymentsMethodButton focusable" onClick={handleAddPayments} id='add-payments'>
							<span className="buttonContents">Add Payment Method</span>
						</button>
					</div>
				</div>
			<BottomBar image={"images/selectBack.png"} title={"Back"}/>
		</div>
	)
};

export default ManagePayments;