.mossaic-main-container {
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  font-family: <PERSON><PERSON>;
  position: fixed;
  top: 0px;
  z-index: 4;
  background-color: #000000;

  .epg-mossaic-header {
    background-color: #000000;
    position: fixed;
    top: 0px;
    width: 1920px;
    height: 240px;
    z-index: 10;

    h1 {
      margin-top: 0px !important;
      margin-bottom: 0 !important;
      margin-bottom: 75px;
      color: #ffffff;
      font-family: <PERSON><PERSON>;
      font-size: 57px;
      letter-spacing: 0.2px;
      line-height: 66px;
      text-align: center;
    }

    .epg-mossaic-top-button {
      background-color: #000000;
      display: flex;
      justify-content: flex-end;
      padding-top: 55px;
      flex-direction: row;

      .green-button {
        margin-right: 62px;
        height: 48px;
        width: 168px;
      }

      .yellow-button {
        margin-right: 80px;
        height: 48px;
        width: 292px;
      }

      .green-button,
      .yellow-button {
        display: flex;
        align-items: center;
        border-radius: 8.8px;
        background-color: #2e303d;
        height: 48px;

        .back-image {
          margin: 0 10px 0 20px;
        }

        &:focus {
          background-color: #981c15;
        }
      }

      .green-button-image,
      .yellow-button-image {
        margin-left: 20px;
      }

      .yellow-button-text,
      .green-button-text {
        height: 30px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 29.04px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 29.04px;
        margin-left: 15px;
        margin: 0 !important;
        text-transform: uppercase;
      }

      .yellow-button-text {
        width: 146px;
      }

      .green-button-text {
        width: 120px;
        text-align: center;
      }

      .yellow-button-text {
        margin-right: 25px;
      }
    }
  }

  .category-grid-main {
    background-color: #000000;
    color: #ffffff;
    text-align: center;
    width: 1920px;
    height: 840px;
    position: fixed;
    top: 240px;
    overflow: scroll;


    .catagory-grid-flow {
      display: flex;
      flex-wrap: wrap;
      margin-left: 80px;
      margin-bottom: 30px;
      margin-top: 5px;

      .channel-bg {
        background-color: #282828;
      }

      .category-item {
        width: 408px;
        height: 232px;
        flex-shrink: 0;
        flex-grow: 0;
        position: relative;
        margin: 16px;

        &:focus {
          box-shadow: 0 0 0 4px #000000, 0 0 0 8px #fff;
          border-radius: 3px;
          transform: scale(1.03);
        }

        .category-title {
          left: 16px;
          top: 16px;
          position: absolute;
          color: #ffffff;
          font-family: Arial;
          font-size: 30px;
          letter-spacing: 0;
          line-height: 33.46px;
        }

        .veg-category-title {
          position: absolute;
          color: #ffffff;
          font-family: Arial;
          font-size: 30px;
          letter-spacing: 0;
          line-height: 33.46px;
          left: 46%;
          top: 50%;
        }

        .hide-title {
          display: none;
        }

       

        .catagory-image {
           width: 100%;
           height: 100%; 
        }
      }
    }
  }
}