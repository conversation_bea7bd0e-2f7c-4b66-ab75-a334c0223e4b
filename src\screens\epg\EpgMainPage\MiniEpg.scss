.mini-epg-container {
  top: 717px;
  left: 66px;
  width: 1778px;
  height: 268px;
  opacity: 1;
  position: absolute;
  display: flex;
  z-index: 2;
}

.mini-channel-wrapper {
  /* Layout Properties */
  top: 717px;
  left: 66px;
  width: 291px;
  height: 268px;
  /* UI Properties */
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.mini-program-wrapper {
  top: 717px;
  left: 66px;
  /* width: 1515px; */
  height: 268px;
  opacity: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.mini-channel-container {
  /* Layout Properties */
  top: 0px;
  left: 0px;
  width: 291px;
  height: 150px;
  /* UI Properties */
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border-radius: 3px;
  opacity: 1;
  position: relative;
}

.mini-program-container-1,
.mini-program-container-2 {
  top: 775px;
  left: 1108px;
  width: 685px;
  height: 152px;
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border-radius: 3px;
  opacity: 1;
}

.mini-program-container-1:focus,
.mini-program-container-2:focus,
.mini-program-container-1:active,
.mini-program-container-2:active,
.mini-channel-container:focus,
.mini-channel-container:active {
  background: #282828 0% 0% no-repeat padding-box;
  /* border: 3px solid #981C15; */
  outline: 4px solid #981c15;
    box-shadow: 1px 1px 10px 9px #981c15;
  opacity: 1;
}

.mini-channel-number {
  top: 19px;
  left: 225px;
  right: 18px;
  bottom: 98px;
  width: 48px;
  height: 33px;
  text-align: left;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  position: relative;
  display: block;
  font: normal normal normal 28px/28px Roboto;
}

.mini-program-recorder {
  position: relative;
  top: 12px;
  right: 12px;
  left: 620px;
  bottom: 92px;
  display: block;
  height: 48px;
  width: 48px;
}

.mini-channel {
  top: 0px;
  left: 40px;
  right: 40px;
  bottom: 43px;
  width: 211px;
  height: 95px;
  display: block;
  position: relative;
}

.mini-channel-up,
.mini-channel-down {
  display: flex;
  justify-content: center;
}

.mini-program-image {
  display: block;
  height: 120px;
  width: 214px;
  top: -32px;
  left: 16px;
  position: relative;
}

.mini-program {
  display: block;
  height: 38px;
  width: 386px;
  left: 240.5px;
  top: -150px;
  position: relative;
  text-align: left;
  font: normal normal normal 32px/31px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
}

.mini-program-duration {
  display: flex;
  width: 200px;
  height: 33px;
  text-align: left;
  align-self: center;
  position: relative;
  text-align: left;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal normal 28px/28px Roboto;
}

.mini-program-date {
  display: flex;
  width: 80px;
  height: 33px;
  text-align: left;
  align-self: left;
  align-items: center;
  position: relative;
  text-align: left;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal normal 28px/28px Roboto;
}

.mini-program-details {
  position: relative;
  display: flex;
  gap: 10px;
  top: -130px;
  left: 240.5px;
  width: 400px;
  height: 35px;
}

.program-label-now {
  width: 128px;
  height: 32px;
  background: #eb0045 0% 0% no-repeat padding-box;
  opacity: 1;
  display: flex;
  justify-content: center;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal normal 28px/28px Roboto;
}

.program-label-later {
  width: 140px;
  height: 32px;
  background: #1D7D16 0% 0% no-repeat padding-box;
  opacity: 1;
  display: flex;
  justify-content: center;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal normal 28px/28px Roboto;
}

.program-label-previous {
  width: 140px;
  height: 32px;
  background: #C97521 0% 0% no-repeat padding-box;
  opacity: 1;
  display: flex;
  justify-content: center;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal normal 28px/28px Roboto;
}

.mini-progress-bar {
  display: block;
  position: relative;
  background: #99999939 0% 0% no-repeat padding-box;
  width: 420px;
  height: 5px;
  top: -110px;
  left: 240.5px;
}

.program-list-virtual {
  top: 10px;
  left: 10px;
  position: relative;
}