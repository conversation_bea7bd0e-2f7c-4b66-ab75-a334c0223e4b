import React from 'react'
import '../../../styles/LiveDetailPage.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'

const RecordingWarning = () => {
  return (
    <div>
      <div
        className="button-container-div"
        style={{ display: 'flex', flexDirection: 'column' }}
      >
        <div>
          <span className="RecWarningTitle">RECORD FORM</span>
        </div>

        <div>
          <button className="RecWarningTitleButton focusable">
            <span className="RecWarningTitleButtonContents">Full Series</span>
          </button>
          <button className="RecWarningTitleButton focusable">
            <span className="RecWarningTitleButtonContents">This Episode</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export const Remindernotification = props => {
  return (
    <div
      className={
        props.showNotification
          ? 'NotificationLayout show'
          : 'NotificationLayout'
      }
    >
      {props?.favouriteFlag == true ? (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={props?.image}
          width={75}
          height={72}
        />
      ) : (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={'images/LiveTv/ReminderUnfocus.png'}
          width={85}
          height={77}
        />
      )}
      <div className="NotificationTextLayout">
        <h6 className="NotificationText">
          {props?.ReminderFlag == true
            ? `Reminder is Set`
            : `Reminder is removed`}
        </h6>
        <h6 className="NotificationPath">
          {props?.ReminderFlag == true
            ? ` Notification will appear at ${`${props?.programTime}`}`
            : ''}
        </h6>
      </div>
    </div>
  )
}

export const Favouritenotification = props => {
  return (
    <div
      className={
        props.showNotification
          ? 'NotificationLayout show'
          : 'NotificationLayout'
      }
    >
      {props?.favouriteFlag == true ? (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={props?.image}
          width={85}
          height={75}
        />
      ) : (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={'images/LiveTv/favClickUnFocus.png'}
          width={75}
          height={72}
        />
      )}
      <div className="NotificationTextLayout">
        <h6 className="NotificationText">
          {props?.favouriteFlag == true
            ? `Channel is added to favourite`
            : `Channel removed from favourite`}
        </h6>
        <h6 className="NotificationPath">
          {props?.favouriteFlag == true ? `My Contents > Favourite` : ''}
        </h6>
      </div>
    </div>
  )
}

export const LockChannelNotification = props => {
  return (
    <div
      className={
        props.showNotification
          ? 'NotificationLayout show'
          : 'NotificationLayout'
      }
    >
      {props?.lockChannelFlag == true ? (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={props?.image}
          width={85}
          height={75}
        />
      ) : (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={'images/LiveTv/UnlockUnfocus.png'}
          width={75}
          height={72}
        />
      )}
      <div className="NotificationTextLayout">
        <h6 className="NotificationText">
          {props?.lockChannelFlag == true
            ? `${props?.contentName}` + ' ' + `${props?.name}` + ' ' + ` locked`
            : `${props?.contentName}` +
              ' ' +
              `${props?.name}` +
              ' ' +
              ` unlocked`}
        </h6>
        {/* <h6 className="NotificationPath">
          { props?.favouriteFlag == true ? `My Contents > Favourite` : ''}
        </h6> */}
      </div>
    </div>
  )
}

export const ErrorRecordNotification = props => {
  return (
    <div
      className={
        props.showNotification
          ? 'NotificationLayout show'
          : 'NotificationLayout'
      }
    >
      {props?.errorRecordFlag != true ? (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={props?.image}
          width={85}
          height={75}
        />
      ) : (
        <LazyLoadImage
          style={{ marginLeft: 44 }}
          src={'images/LivePLayer_Icons/warningIcon.png'}
          width={75}
          height={72}
        />
      )}
      <div className="NotificationTextLayout">
        <h6 className="NotificationText">
          {props?.errorRecordFlag != true
            ? `Channel is not allowed to record the video`
            : ``}
        </h6>
        <h6 className="NotificationPath">
          {props?.errorRecordFlag != true ? `Error code > PLY_REC_00015` : ''}
        </h6>
      </div>
    </div>
  )
}
export default RecordingWarning
