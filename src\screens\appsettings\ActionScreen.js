import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './Settings.scss'
import { getChannelData } from '../../store/slices/PlayerSlice'
import {
  delLiveReminder,
  getLiveReminder,
  clearReminderState
} from '../../store/slices/EpgSlice'
import {
  getClearAllSettingsState,
  getLockedChannelDelete,
  getSubscriptionInfo,
  getLockedChannelsList
} from '../../store/slices/settingsSlice'
import { getProgramDetailsData } from '../../store/slices/EpgSlice'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const ActionScreen = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const [screen, setScreen] = useState('actionScreen')
  const [activeButton, setActiveButton] = useState('verButton')
  const [channelInfoData, setChannelInfoData] = useState({})
  const [buttonClicked, setButtonClicked] = useState(false)


  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const deleteReminder = useSelector(state => state?.epg?.delReminder?.response)
  const unlockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelDelete
  )
  const payWayToken = useSelector(
    state => state?.epg?.paywayToken?.response?.paqs?.paq
  )
  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getSubsInfo
  )

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const today = new Date()

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const getIsContractChanel = item => {
    const foundContract = payWayToken?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return true
    }
    return false
  }

  const callPurchaseApi = channelInfo => {
    setButtonClicked(true)
    setChannelInfoData(channelInfo)
    dispatch(
      getSubscriptionInfo({
        userId: userDetails?.parent_id,
        hks: userDetails?.session_stringvalue,
        url: `group_id=${channelInfo?.channel_group?.common?.id}`
      })
    )
  }

  useEffect(() => {
    if (buttonClicked && addSubscriptions?.msg == 'OK') {
      const subscriptionResponse = addSubscriptions?.response?.listButtons
      const buttonMap = subscriptionResponse?.button ?? []
      if (buttonMap?.length > 0) {
        if (buttonMap?.length == 1) {
          buttonMap?.map(each => {
            if (each?.purchasable) {
              navigate('/premiumSubscription', {
                state: {
                  data:
                    channelInfoData?.channel_group?.common ?? channelInfoData,
                  priceDetails: each,
                  returnPage: 'settings/profile-settings',
                  pageName: 'settings/profile-settings',
                }
              })
            } else {
              handleNavigateToNonContract(channelInfoData)
            }
          })
        } else {
          const subScriptionValuesFalse = buttonMap.every(
            item => item?.purchasable === false
          )
          if (subScriptionValuesFalse) {
            handleNavigateToNonContract(channelInfoData)
          } else {
            const subscriptionTrueValues = buttonMap?.every(
              item => item?.purchasable === true
            )
            const singleSubscription = buttonMap?.filter(
              item => item?.purchasable === true
            )
            const subscriptionNodeValueFalse = buttonMap?.filter(
              item => item?.purchasable === false
            )
            const exactlySingleSubscriptionTrue =
              singleSubscription?.length === 1 &&
              subscriptionNodeValueFalse?.length === buttonMap?.length - 1
            if (subscriptionTrueValues) {
              navigate('/multiSubscription', {
                state: {
                  pageName: 'settings/profile-settings',
                  returnPage: 'settings/profile-settings',
                  data:
                    channelInfoData?.channel_group?.common ?? channelInfoData,
                  type: 'MultiSubscription'
                }
              })
            } else if (exactlySingleSubscriptionTrue) {
              navigate('/premiumSubscription', {
                state: {
                  data:
                    channelInfoData?.channel_group?.common ?? channelInfoData,
                  priceDetails: singleSubscription[0],
                  returnPage: 'settings/profile-settings',
                }
              })
            }
          }
        }
      } else {
        handleNavigateToNonContract(channelInfoData)
      }
    }
  }, [addSubscriptions])

  const handleNavigateToNonContract = channelInfoData => {
    navigate('/subScriptionCallPage', {
      state: {
        data: channelInfoData?.channel_group?.common ?? channelInfoData,
        returnPage: 'settings/profile-settings',
        pageName: 'settings/profile-settings'
      }
    })
  }

  const handleViewClick = (e, programData) => {
    e.preventDefault()
    const ChannelDataPayload = {
      group_id: programData?.data?.channel_group_id,
      name: programData?.data?.channel_name,
      number: programData?.data?.channel_number,
      image: programData?.data?.channel_group?.common?.image_small,
      timeshift: programData?.data?.channel_group?.common?.timeshift
    }
    const now =
      today.getFullYear() +
      '/' +
      (today.getMonth() + 1).toString().padStart(2, '0') +
      '/' +
      today.getDate().toString().padStart(2, '0') +
      ' ' +
      today.toLocaleTimeString('en-US', { hour12: false })
    const channelIndex = epgSevenDaysData[1]?.channelResponse.findIndex(
      itrObj => itrObj.group_id === programData?.data?.channel_group?.common?.id
    )
    if (getIsContractChanel(epgSevenDaysData[1]?.channelResponse[channelIndex]?.group_id)) {
      dispatch(
        getChannelData({
          group_id: epgSevenDaysData[1]?.channelResponse[channelIndex]?.group_id,
          timeshift:
            epgSevenDaysData[1]?.channelResponse[channelIndex]?.group?.common
              ?.timeshift,
          switchChannel: 'yes',
          epgIndex: channelIndex
        })
      )
      dispatch(
        getProgramDetailsData({
          programData: programData?.data,
          channelData: ChannelDataPayload,
          fromSearch: true
        })
      )
      if (now < programData?.data?.begintime) {
        navigate('/livePlayer', {
          state: {
            showControls: 'live',
            pageName: 'settings/profile-settings',
            returnPage: 'settings/profile-settings'
          },
          replace: true
        })
      } else {
        navigate('/livePlayer', {
          state: { showControls: 'live' },
          replace: true
        })
      }
    }
    else {
      callPurchaseApi(programData?.data)
    }
  }

  const handleDeleteScreenClick = () => {
    setScreen('deleteScreen')
    setActiveButton('noButton')
  }

  const handleActionBackClick = () => {
    state?.pageName == 'parentalControl'
      ?
      navigate('/settings/profile-settings', {
        state: {
          pageName: state?.pageName,
          subPage: state?.subPage,
          screenActive: state?.screenActive,
          data: state?.data,
          focusButtonId: state?.focusButtonId
        }
      })
      :
      setScreen('actionScreen')
    setActiveButton('borrarButton')
  }

  const handleDeleteReminderClick = (e, programData) => {
    e.preventDefault()
    const payload = {
      reminder_id: programData?.id,
      user_hash: userDetails?.session_userhash,
      hks: userDetails?.session_stringvalue
    }
    dispatch(delLiveReminder(payload))
  }

  const handleUnlockChannel = (e, chanenlData) => {
    e.preventDefault()
    const payload = {
      hks: userDetails?.session_stringvalue,
      group_id: chanenlData?.data?.id,
      user_hash: userDetails?.session_userhash
    }
    dispatch(getLockedChannelDelete(payload))
  }

  const handleLgkey = keycode => {
    if (keycode === 10009 || keycode === 461 || keycode == 'backClick') {
      screen == 'actionScreen'
        ? navigate('/settings/profile-settings', {
          state: {
            pageName: state?.pageName,
            subPage: state?.subPage,
            screenActive: state?.screenActive,
            data: state?.data,
            focusButtonId: state?.focusButtonId
          }
        })
        : (setScreen('actionScreen'), setActiveButton('borrarButton'))
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    handleLgkey(keycode)
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    if (deleteReminder) {
      dispatch(clearReminderState())
      dispatch(
        getLiveReminder({
          hks: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
      navigate('/settings/profile-settings', {
        state: { pageName: state?.pageName }
      })
    }
  }, [deleteReminder])

  useEffect(() => {
    if (unlockchannel?.msg === 'OK') {
      dispatch(getClearAllSettingsState())
      dispatch(
        getLockedChannelsList({
          hks: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
      navigate('/settings/profile-settings', {
        state: { pageName: state?.pageName, subPage: state?.subPage }
      })
    }
  }, [unlockchannel])

  useEffect(()=>{
    pushScreenViewEvent({screenName:'action_screen',screenData:userDetails,prevScreenName:'profile_settings'})
  },[])

  return (
    <div className="action-screen-settings focusable">
      {screen == 'actionScreen' ? (
        <>
          <button
            className={`${activeButton == 'verButton'
              ? 'action-screen-buttons-active'
              : 'action-screen-buttons'
              } focusable`}
            id="verButton"
            autoFocus
            onClick={
              state?.pageName == 'parentalControl'
                ? e => handleLgkey('backClick')
                : e => handleViewClick(e, state?.data)
            }
          >
            <span className="action-screen-button-contents">
              {state?.pageName == 'parentalControl' ? truncateText('profiles_settings_blockedchannels_option_cancel', 10) : truncateText('profiles_settings_reminders_option_watch', 10)}
            </span>
          </button>
          <button
            className={`${activeButton == 'borrarButton'
              ? 'action-screen-buttons-active'
              : 'action-screen-buttons'
              } focusable`}
            id="borrarButton"
            onClick={handleDeleteScreenClick}
          >
            <span className="action-screen-button-contents">{state?.pageName == 'parentalControl' ? truncateText('profiles_settings_blockedchannels_option_delete', 10) : truncateText('profiles_settings_reminders_option_delete', 10)}</span>
          </button>
        </>
      ) : (
        <>
          <span className="delete-screen-title">
            {state?.pageName == 'parentalControl'
              ? truncateText('unlock_channel_modal_title', 40)
              : truncateText('delete_reminder_modal_title', 40)}
          </span>
          <span className="delete-screen-sub-title">
            {state?.pageName == 'parentalControl'
              ? truncateText('unlock_channel_modal_content', 100)
              : truncateText('delete_reminder_modal_content', 100)}
          </span>
          <div className="delete-button-div">
            <button
              className={`${activeButton == 'noButton'
                ? 'action-screen-buttons-active'
                : 'action-screen-buttons'
                } focusable`}
              id="noButton"
              style={{ marginRight: '43px' }}
              autoFocus
              onClick={handleActionBackClick}
            >
              <span className="action-screen-button-contents">
                {state?.pageName == 'parentalControl'
                  ? truncateText('unlock_channel_modal_button_cancel', 30)
                  : truncateText('delete_reminder_modal_button_cancel', 30)}
              </span>
            </button>
            <button
              className="action-screen-buttons focusable"
              id="yesButton"
              onClick={
                state?.pageName == 'parentalControl'
                  ? e => handleUnlockChannel(e, state?.data)
                  : e => handleDeleteReminderClick(e, state?.data)
              }
            >
              <span className="action-screen-button-contents">
                {state?.pageName == 'parentalControl'
                  ? truncateText('unlock_channel_modal_button_confirm', 30)
                  : truncateText('delete_reminder_modal_button_confirm', 30)}
              </span>
            </button>
          </div>
        </>
      )}
    </div>
  )
}

export default React.memo(ActionScreen)
