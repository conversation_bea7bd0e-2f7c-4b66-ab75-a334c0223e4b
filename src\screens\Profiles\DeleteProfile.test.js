import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router, MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import DeleteProfile from "./DeleteProfile";
 
 
const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <MemoryRouter
      history={history}
      initialEntries={[
        {
          state: {
            data: {
              gamification_id: '6579a68439b44705076d056b',
              username: '<PERSON>s <PERSON>akelll',
              user_image:
                'http://clarovideocdn1.clarovideo.net/lanegociadora01.png?**********',
              rol: 'admin',
              admin: true,
              change_name: false,
              is_kids: 'false',
              partnerUserId: '92820606',
              user_hash:
                'OTI4MjAQxNDllM2Q'
            }
          }
        }
      ]}
    >
      {children}
    </MemoryRouter>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
describe('Delete Profile test', () => {
    test('should do button click', () => {
        const { container } = renderWithState(<DeleteProfile />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'del-button-id');
        fireEvent.focus(scroll)
        fireEvent.keyUp(scroll,{keyCode: '405'})
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    
    test('should do button click', () => {
        const { container } = renderWithState(<DeleteProfile />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'del-button-id');
        fireEvent.focus(scroll)
        fireEvent.keyUp(scroll,{keyCode: '8'})
        fireEvent.keyUp(scroll,{keyCode: '461'})
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/editProfile')
    })
    
    test('handles Samsung TV green key', () => {
      const props = {
        onChange: jest.fn(),
        type: 'DeleteProfile',
        setCurrentButtonFocus: jest.fn()
      };
      global.tizen = {
        tvinputdevice: {
          registerKeyBatch: jest.fn(),
          getKey: jest.fn().mockReturnValue({ code: 10009 })
        },
      };
      const { container } = renderWithState(<DeleteProfile {...props} />);
      const getById = queryByAttribute.bind(null, 'id');
      const scroll = getById(container, 'del-button-id');
      fireEvent.keyUp(scroll, { keyCode: 10009 });
      fireEvent(
        scroll,
          new MouseEvent('click', {
              bubbles: true,
              cancelable: true
          })
      )
      delete global.tizen;
    })


})