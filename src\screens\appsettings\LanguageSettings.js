import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useLocation } from 'react-router-dom'
import './LanguageSettings.scss'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const LanguageSettings = () => {
  const navigate = useNavigate()
  const { state } = useLocation()

  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const [onDemandLanguage, setOnDemandLanguage] = useState(truncateText('original_language', 30))
  const [tvLanguage, setTvLanguage] = useState(truncateText('original_language', 30))

  const languages = [
    {
      name: truncateText('subtitled_in_spanish', 30),
      code: 'S-ES'
    },
    {
      name: truncateText('subtitled_in_portuguese', 30),
      code: 'S-PT'
    },
    {
      name: truncateText('original_language', 30),
      code: 'O-OR'
    },
    {
      name: truncateText('portuguese_language', 30),
      code: 'D-PT'
    },
    {
      name: truncateText('spanish_language', 30),
      code: 'D-ES'
    }
  ]
  const backelement = document.getElementById(state?.backFocus)

  useEffect(() => {
    backelement?.focus()
  }, [backelement])

  useEffect(() => {
    languages.map(
      each =>
        each.code == localStorage.getItem('vodContentLanguage') &&
        setOnDemandLanguage(each?.name)
    )
    languages.map(
      each =>
        each.code == localStorage.getItem('liveContentLanguage') &&
        setTvLanguage(each?.name)
    )

     pushScreenViewEvent({screenName:'idioma',screenData:userDetails,prevScreenName:'settings'})
  }, [])

  const handleLanguageClick = type => {
    navigate('/settings/profile-settings/choose-language', {
      state: {
        type,
        currentLanguage: type === 'onDemand' ? onDemandLanguage : tvLanguage,
        backFocus: type === 'onDemand' ? 'vod-dropdown-id' : 'tv-dropdown-id'
      }
    })
  }

  const keySeriesPressFunc = event => {
    if (
      event.keyCode === 10009 ||
      event.keyCode === 461 ||
      event?.keyCode === 8
    ) {
      navigate('/settings', { state: { activeTab: 'adjustingProfiles' } })
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keySeriesPressFunc)

    return () => {
      document.removeEventListener('keyup', keySeriesPressFunc)
    }
  }, [keySeriesPressFunc])

  const renderLanguageSection = (title, description, buttonText, type, id) => (
    <div className="selected-container">
      <p className="lang-title-text">{title}</p>
      <hr className="horizontal-line" />
      <div className="lang-setting-main-container">
        <div className="text-detail-container-left-side">
          <p className="text-detail-title-left">{description}</p>
        </div>
        <div className="text-detail-container-right-side">
          <p className="text-detail-title-right">
            {apilanguage?.choose_language_settings}
          </p>
          <div className="dropdown-container ">
            <button
              className="dropdown-btn focusable"
              id={id}
              onClick={() => handleLanguageClick(type)}
            >
              <span className="dropdown-btn-text"> {buttonText} </span>
              <span className="dropdown-bottom-chevron">
                <img
                  className="dropdown-chevron-down-img"
                  src="images/LiveTv/ic_chevron_down.png"
                  alt="Dropdown chevron"
                />
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {renderLanguageSection(
        `${truncateText('label_on_demand_title', 30)}`,
        `${truncateText('label_on_demand_description', 200)}`,
        onDemandLanguage,
        'onDemand',
        'vod-dropdown-id'
      )}
      {renderLanguageSection(
        `${truncateText('label_on_live_title', 30)}`,
        `${truncateText('label_on_live_description', 200)}`,
        tvLanguage,
        'tv',
        'tv-dropdown-id'
      )}
    </>
  )
}

export default LanguageSettings
