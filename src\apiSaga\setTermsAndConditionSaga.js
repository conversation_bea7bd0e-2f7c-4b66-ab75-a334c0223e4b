import { call, takeEvery } from "@redux-saga/core/effects";
import { store } from "../store/sagaStore";
import { getTermsAndConditionError, getTermsAndConditionSuccess, getWatchFree, setSkeltonLoading } from "../store/slices/login";
import { URL } from '../utils/environment'
import { request } from "../utils/request";

function* getTermsAndCondition({ payload }) {
    payload?.type != 'splashLogin' && store.dispatch(setSkeltonLoading(true))
    try {
        yield call(request,
            `${URL.SET_TERMS_AND_CONDITION}&username=${payload?.username}&password=${payload?.password}&HKS=${payload?.HKS}`,
            {
                method: 'GET'
            },
            {
                onSuccess(response) {
                    localStorage.setItem('region', response?.response?.userData?.region)
                    localStorage.setItem('login_user', true)
                    localStorage.setItem('token', response?.response?.userData?.user_token)
                    store.dispatch(getTermsAndConditionSuccess(response))
                    store.dispatch(getWatchFree())
                    store.dispatch(setSkeltonLoading(false))
                },
                onError(error) {
                    store.dispatch(setSkeltonLoading(false))
                    store.dispatch(getTermsAndConditionError(error))
                }
            }
        )
    } catch (error) {
        console.error('catch error --> ', error)
    }
}

export default function* getTermsAndConditionSaga() {
    yield takeEvery('login/getTermsAndCondition', getTermsAndCondition)
}