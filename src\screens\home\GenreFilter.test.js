import React from "react";
import { fireEvent, getAllByText, getByAltText, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import GenreFilter from "./GenreFilter";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

describe('Landing page test', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('should render the profile select', () => {
        const { container } = renderWithState(<GenreFilter />)
        initialState.SubMenuFilter = {
            filterclickdata: true
        }
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'genrebackbutton');
        fireEvent.keyUp(scroll,{keyCode: '461'})
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })

    test('should render the profile select', () => {
        const { container } = renderWithState(<GenreFilter />)
        initialState.SubMenuFilter = {
            filterclickdata: true
        }
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'genrebackbutton');
        fireEvent.keyUp(scroll,{keyCode: '405'})
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })


    test('When user press the back space it should go the vCard screen', async () => {
        const wrapper = await renderWithState(<GenreFilter />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
    })

    test('When user press the back space it should go the vCard screen', async () => {
        const wrapper = await renderWithState(<GenreFilter />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 461,
                charCode: 461
          });
    })

    test('When user press the back space it should go the vCard screen', async () => {
        const wrapper = await renderWithState(<GenreFilter />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 405,
                charCode: 405
          });
    })

    test('should render the profile select', () => {
        const { container } = renderWithState(<GenreFilter />)
        initialState.SubMenuFilter = {
            filterclickdata: true
        }
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'genrebackbutton');
        fireEvent.keyUp(scroll,{keyCode: '10009'})
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})