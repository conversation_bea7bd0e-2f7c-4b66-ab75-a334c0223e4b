import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  getSubscriptionInfo,
  getViewSubscribeData,
  clearGetContractBackHandle
} from '../../../store/slices/settingsSlice'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './MultiSubscription.scss'
import { pushScreenViewEvent,pushSubscriptionEvent } from '../../../GoogleAnalytics'
import { HOME, SUBSCRIPTION_CAROUSEL_LIVE } from '../../../GoogleAnalyticsConstants'

const MultiSubscription = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { state } = useLocation()
  const [focusedIndex, setFocusedIndex] = useState(null)
  const [subscriptionData, setSubscriptionData] = useState([])

  const region = localStorage.getItem('region')
  const liveChannelId = localStorage.getItem('live-playing-channel-id') !=
  'undefined'
    ? localStorage.getItem('live-playing-channel-id')
    : ''
  const focusRef = useRef([])
  const viewPlanRef = useRef(null)
  const focusIndexRef = useRef(null)
  const startTimeRef = useRef(null)
  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getSubsInfo?.response?.listButtons?.button
  )
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)  
  const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const loginResponse = useSelector(
    state => state?.login?.loginSuccess?.response
  )
  const isAnyBack = useSelector(state => state?.settingsReducer?.toBackPage)
  const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []
  const liveChannnelInfo = useSelector(state => state?.player?.playerinfo)
  let channelIndex = epgSevenDaysData?.[1]?.channelResponse.findIndex(
    itrObj => itrObj.group_id == liveChannelId
  )

  channelIndex = channelIndex != -1 ? channelIndex : 0

  const translations =
    apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const apilanguage = translations?.language?.[region]

  const handleSubscribe = (e, data, indx) => {
    const getData = state?.data ?? state?.data?.group?.common
    const engagementTime = Date.now() - startTimeRef.current
    dispatch(getViewSubscribeData(data))
     const userData = {
        suscriptions : userDetails?.subscriptions && Object.keys( userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        content_section : navbarTab,
        action_type : 'click boton',
        page_path: HOME,
        page_title: navbarTab,
        engagement_time_msec: engagementTime
      }
    pushSubscriptionEvent(userData,data,indx,SUBSCRIPTION_CAROUSEL_LIVE)
    navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new', {
      state: {
        pageName: '/multiSubscription',
        data: getData,
        sendIndex: indx,
        returnPage: state?.pageName,
        inputValue: state?.inputValue,
        groupId: state?.groupId
      }
    })
    startTimeRef.current = Date.now();
  }

  const handlePlanFocus = (e, data, index) => {
    e.preventDefault()
    viewPlanRef.current = data
    focusIndexRef.current = index
    setFocusedIndex(index)
  }

  const handlePlanBlur = () => {
    setFocusedIndex(null)
  }

  const handleColorChange = data => {
    const styleColors = {
      style_hbo: '#2d6284',
      style_clarovideo: '#981c15',
      style_noggin: '#8656d4',
      style_edye: '#214695',
      style_tv_envivo: '#2a295b',
      style_stingraykaraoke: '#0797c8',
      style_atresplayer: '#ca001d',
      style_picardia_nacional: '#eb0045',
      style_rtveplay: '#ff7800',
      style_starzplay: '#ba0b13',
      style_universal: '#981c15',
      style_premium_hot: '#540000',
      style_mgm: '#981c15'
    }
    return styleColors[data.styles] || '#981c15'
  }

  const settingBannerUrl = data => {
    return (
      apaAssetsImages?.[
        `Transaccionales_ContratarPaquetes_ImagenDeContenido_${data}`
      ] ||
      apaAssetsImages?.[
        `Transaccionales_ContratarPaquetes_ImagenDeContenido_${data?.toUpperCase()}`
      ] ||
      'images/checkout_banner_placeholder.png'
    ) //checking the data is there in apa/assets
  }

  const settingLogoUrl = data => {
    return (
      apaAssetsImages?.[
        `Transaccionales_ContratarPaquetes_ImagenLogoCanal_${data}`
      ] ||
      apaAssetsImages?.[
        `Transaccionales_ContratarPaquetes_ImagenLogoCanal_${data?.toUpperCase()}`
      ] ||
      'images/CV_MENSUAL.png'
    ) //checking the data is there in apa/assets
  }

  const getTaxLabel = data => {
    return translations?.language?.[region]?.[
      data + '_subscriptionDescription_costTaxIncluded_label'
    ]
  }

  const getFreeChargeString = data => {
    return translations?.language?.[region]?.[
      'transactional_' + data + '_subscription_plan_tryInvite_description'
    ]
  }

  const getSubscribeButton = data => {
    return handleTranslationchange(
      `SelectorDePlan_Tarjeta_TextoBotonSuscripcion_${data}`,
      15
    )
  }

  const getViewDetailsButton = data => {
    return handleTranslationchange(`BotonShortcut_TextoTitulo_QueIncluye`, 15)
  }

  const getPeriodicity = data => {
    return translations?.language?.[region]?.[
      'subscription_' + data + '_month_label'
    ]
  }

  const settingPlanLogo = data => {
    return (
      apaAssetsImages?.[`SelectorDePlan_Tarjeta_ImagenFondo_${data}`] ||
      apaAssetsImages?.[
        `SelectorDePlan_Tarjeta_ImagenFondo_${data?.toUpperCase()}`
      ] ||
      'images/plan_selector_placeholder.png'
    )
  }

  const handleTranslationchange = useCallback(
    (str, length) => {
      const text = apilanguage?.[str] ?? str
      if (!length) {
        length = 100
      }
      if (text?.length >= length) {
        return `${text?.slice(0, length)}...`
      } else {
        return text
      }
    },
    [apilanguage]
  )

  useEffect(() => {
    if (addSubscriptions?.length > 0) {
      const newSubscriptionData = addSubscriptions?.map(each => ({
        bannerUrl: settingBannerUrl(each?.family),
        planLogo: settingPlanLogo(each?.producttype),
        logo: settingLogoUrl(each?.family),
        family: each?.family,
        periodicity: each?.periodicity,
        price: each?.price,
        currency: each?.currency,
        styles: each?.style,
        workflowStart: each?.linkworkflowstart,
        taxLabel: getTaxLabel(each?.family),
        infoString: getFreeChargeString(each?.bonus),
        subscribeButton: getSubscribeButton(each?.family),
        viewButton: getViewDetailsButton(each?.family),
        producttype: each?.producttype,
        isMultiPack: true,
        offertype: each?.oneoffertype
      }))
      setSubscriptionData(newSubscriptionData)
    }
  }, [addSubscriptions])

  useEffect(() => {
    if (focusRef.current[0]) {
      const maintainIndex = state?.sendIndex
      focusRef.current[maintainIndex ?? 0].focus()
    }
  }, [subscriptionData])

  useEffect(() => {
    pushScreenViewEvent({screenName:'multi_subscription_page', screenData: userDetails, prevScreenName: state?.pageName})
    dispatch(
      getSubscriptionInfo({
        userId: userDetails?.parent_id,
        hks: userDetails?.session_stringvalue,
        url: `group_id=${state?.data?.group?.common?.id ?? state?.data?.id}`
      })
    )
  }, [])

  useEffect(() => {
    startTimeRef.current = Date.now()
    SpatialNavigation.focus()
    return () => {
      startTimeRef.current = null
    }
  }, [])

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      if (
        state?.pageName == 'livePlayer' ||
        state?.returnPage == 'livePlayer'
      ) {
        handleLivePlayer()
      } else if (state?.pageName == 'search') {
        localStorage.setItem('currNavIdx', -1)
        navigate('/search', {
          state: { inputValue: state?.inputValue, data: state?.data },
          replace: true
        })
      } else if (state?.returnPage == 'settings/profile-settings') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'recordatorios' }
        })
      } else {
        navigate(state?.pageName, {
          state: {
            data: state?.data,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            groupId: state?.groupId
          }
        })
      }
    } else if (key.bluecode === keycode) {
      handleView()
    }
  }

  const handleBackNavigation = () => {
    typeof tizen == 'undefined'
      ? handleLgkey('BackClick')
      : handlesamsungkey(10009)
  }

  const isSubscribed = item => {
    const foundContract = paywayResponse?.filter(each =>
      each?.groups?.includes(item)
    )
    return foundContract?.length > 0 ? true : false
  }

  const handleLivePlayer = () => {
    channelIndex = epgSevenDaysData?.[1]?.channelResponse.findIndex(
      itrObj =>
        itrObj.group_id == liveChannelId || 
        liveChannnelInfo?.response?.group?.common?.id ||
        state?.groupId
    )
    // only for reset channel data nonsubscribed user
    if (
      (loginResponse?.subscriptions &&
        loginResponse?.subscriptions?.length == 0) ||
      !isSubscribed(state?.data?.id)
    ) {
      const groupId =
        epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group_id ??
        paywayResponse?.[0]?.groups?.split(',').shift()
      dispatch(
        getChannelData({
          group_id: groupId,
          timeshift:
            epgSevenDaysData?.[1]?.channelResponse[channelIndex]?.group?.common
              ?.timeshift,
          switchChannel: 'yes',
          epgIndex: channelIndex
        })
      )
      navigate('/livePlayer', {
        state: {
          showControls: 'live',
          returnPage: 'livePlayer',
          groupId: state?.groupId,
          gaPreviousPath: 'subscription'
        },
        replace: true
      })
    } else if (isAnyBack?.backPage) {
      dispatch(clearGetContractBackHandle())
      navigate(isAnyBack?.backPage, {
        state: {
          backfocusid: isAnyBack?.focusedId
        }
      })
    } else {
      navigate('/livePlayer', {
        state: {
          showControls: 'live',
          returnPage: 'livePlayer',
          groupId: state?.groupId,
          gaPreviousPath: 'subscription'
        },
        replace: true
      })
    }
  }

  const handleLgkey = keycode => {
    if (
      keycode == 405 ||
      keycode === 461 ||
      keycode == 'BackClick' ||
      keycode == 8
    ) {
      if (
        state?.pageName == 'livePlayer' ||
        state?.returnPage == 'livePlayer'
      ) {
        handleLivePlayer()
      } else if (state?.pageName == 'search') {
        localStorage.setItem('currNavIdx', -1)
        navigate('/search', {
          state: { inputValue: state?.inputValue, data: state?.data },
          replace: true
        })
      } else if (state?.returnPage == 'settings/profile-settings') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'recordatorios' }
        })
      } else {
        navigate(state?.pageName, {
          state: {
            data: state?.data,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            groupId: state?.groupId
          }
        })
      }
    } else if (keycode == 406) {
      handleView()
    }
  }

  const handleView = () => {
    const getData = state?.data
    dispatch(getViewSubscribeData(viewPlanRef.current))
    navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
      state: {
        pageName: '/multiSubscription',
        data: getData,
        sendIndex: focusIndexRef.current,
        returnPage: state?.pageName,
        groupId: state?.groupId
      }
    })
  }

  return (
    <div className={'app-subscription-css'}>
      <button
        className="multi-back-indicator focusable"
        id="subscriptionPageBackButton"
        onClick={() => handleBackNavigation()}
      >
        {' '}
        <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
        <img className="back-image" src={'images/back_button.png'} />
        <p className="back-text">
          {handleTranslationchange('BotonShortcut_TextoTitulo_Regresar', 10)}
        </p>
      </button>
      {subscriptionData?.length > 0 && (
        <div className="subscription-warapper">
          <p className="add-subs-title">
            {handleTranslationchange(
              `Transaccionales_SeleccionPaquetes_TextoTitulo`,
              100
            )}
          </p>
          <div className="multi-subscriptions-div">
            {subscriptionData?.map((each, index, array) =>
              each?.bannerUrl ? (
                <button
                  className="multi-cards focusable"
                  key={index}
                  id={'add-cards' + index}
                  onClick={e => handleSubscribe(e, each, index)}
                  ref={el => (focusRef.current[index] = el)}
                  onBlur={handlePlanBlur}
                  onFocus={e => handlePlanFocus(e, each, index)}
                  data-sn-left={index == 0 ? '' : undefined}
                  data-sn-right={index != array.length - 1 && undefined}
                >
                  <LazyLoadImage
                    className="image-banner"
                    src={settingBannerUrl(each?.family)}
                    style={{
                      height: focusedIndex === index ? '650px' : 'auto',
                      width: focusedIndex === index ? '285px' : 'auto'
                    }}
                  />
                  <div className="multi-subs-meta-data">
                    <LazyLoadImage className="add-Sub-Logo" src={each?.logo} />
                    <span className="pricing-info">
                      <span className="price-type">{each?.currency}</span>
                      <span className="price-css">{each?.price}</span>
                      <span className="tax-slash">
                        {each?.currency ? '/' : ''}
                      </span>
                      <span className="periodicity">
                        {getPeriodicity(each?.family)}
                      </span>
                    </span>
                    <span className="tax-label">{each?.taxLabel}</span>
                    <div className="sub-buttons-div">
                      <span
                        className="subs-button "
                        style={{ backgroundColor: handleColorChange(each) }}
                        id="subscribe-button"
                      >
                        {each?.subscribeButton}
                      </span>
                    </div>
                  </div>
                  <button
                    className="multi-plan-button focusable"
                    data-sn-left={index == 0 ? ' ' : undefined}
                    data-sn-right={index != array.length - 1 && undefined}
                    id="view-details"
                  >
                    <LazyLoadImage
                      src={'images/blue_shortcut.png'}
                      className="blue-short-cut"
                    />
                    <span className="view-button-contents">
                      {handleTranslationchange(
                        'planSelector_access_option_button_subscription',
                        20
                      )}
                    </span>
                  </button>
                </button>
              ) : null
            )}
          </div>
        </div>
      )}
    </div>
  )
}
export default MultiSubscription
