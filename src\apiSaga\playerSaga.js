import { call, takeEvery } from '@redux-saga/core/effects'
import { store } from '../store/sagaStore'
import {
  getLinealPaywayApiError,
  getLinealPaywayApiSuccess,
  getLivePlayerError,
  getLivePlayerSuccess,
  getMediaAPIError,
  getMediaAPISuccess,
  getTrailerPlayerSuccess,
  getTrailerPlayerError,
  getRecordPlayerSuccess,
  getRecordPlayerError,
  getPlayerTrackAPISuccess,
  getPlayerTrackAPIError
} from '../store/slices/PlayerSlice'
import { COMMON_URL, URL } from '../utils/environment'
import { request } from '../utils/request'
import { CURRENT_PLATFORM } from '../utils/devicePlatform'

function* getLivePlayerApi({ payload }) {
  const region = localStorage.getItem('region')
  const getLivePlayerUrl = `${URL.GETMEDIA_LIVE}&payway_token=${
    payload?.payway_token
  }&user_id=${payload?.user_id}&HKS=${payload?.HKS}&region=${region}${
    payload?.startTime ? '&' + 'startTime=' + payload?.startTime : ''
  }${payload?.endTime ? '&' + 'endTime=' + payload?.endTime : ''}&stream_type=${
    payload?.streamType ?? "dashwv"//CHANGE NEEDED ONLY FOR lg/sAMSUNG - adding a fallback of dashwv incase of no data from apa/metadata api
  }&group_id=${payload.group_id}`
  try {
    yield call(
      request,
      payload?.type == 'watchfree'
        ? getLivePlayerUrl
        : `${getLivePlayerUrl}&user_token=${payload?.user_token}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLivePlayerSuccess(response))
        },
        onError(error) {
          store.dispatch(getLivePlayerError(error))
        }
      },
      false
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getMediaAPI({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      payload?.type !== 'watchfree'
        ? `${URL.GETMEDIA_VOD}&group_id=${payload.id}&HKS=${
            payload.HKS
          }&payway_token=${
            payload.payway_token
          }&preview=0&region=${region}&stream_type=${
            payload?.streamType ?? ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') ? 'smoothstreaming_ma' : 'dashwv_ma')
          }&user_id=${payload?.userId}&user_token=${payload?.user_token}&${
            payload?.content_id ? '&content_id=' + payload?.content_id : ''
          }`
        : `${URL.GETMEDIA_VOD}&group_id=${payload.id}&payway_token=${
            payload.payway_token
          }&preview=0&region=${region}&stream_type=${
            payload?.streamType ?? ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') ? 'smoothstreaming_ma' : 'dashwv_ma')
          }&${payload?.content_id ? '&content_id=' + payload?.content_id : ''}`,
      {
        method: 'POST'
      },
      {
        onSuccess(response) {
          if (response?.errors?.[0]?.code) {
            store.dispatch(getMediaAPIError(response))
            return
          }
          store.dispatch(getMediaAPISuccess(response))
          localStorage.setItem(
            'continueWatchLasttouch',
            response?.response?.user?.lasttouch
          )
        },
        onError(error) {
          store.dispatch(getMediaAPIError(error))
        }
      },
      false
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getTrailerPlayerApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      payload?.type !== 'watchfree'
        ? payload?.userId
          ? `${URL.GETMEDIA_VOD}&group_id=${payload.id}&HKS=${payload.HKS}&preview=1&region=${region}&stream_type=${payload?.streamType}&user_id=${payload?.userId}&user_token=${payload?.user_token}`
          : `${URL.GETMEDIA_VOD}&group_id=${payload.id}&preview=1&region=${region}&stream_type=${payload?.streamType}`
        : `${URL.GETMEDIA_VOD}&group_id=${payload.id}&HKS=${payload.HKS}&preview=1&region=${region}&stream_type=${payload?.streamType}`,
      {
        method: 'POST'
      },
      {
        onSuccess(response) {
          store.dispatch(getTrailerPlayerSuccess(response))
        },
        onError(error) {
          store.dispatch(getTrailerPlayerError(error))
        }
      },
      false
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getLinealPaywayApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.GETLINEAL_PAYWAY}&region=${region}&user_id=${payload?.user_id}&HKS=${payload?.HKS}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLinealPaywayApiSuccess(response))
        },
        onError(error) {
          store.dispatch(getLinealPaywayApiError(error))
        }
      },
      false
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getRecordPlayerApi({ payload }) {
  try {
    yield call(
      request,
      payload,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getRecordPlayerSuccess(response))
        },
        onError(error) {
          store.dispatch(getRecordPlayerError(error))
        }
      },
      false
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}
function* getPlayerTrackAPI({ payload }) {
  try {
    yield call(
      request,
      payload?.trackType &&
        (payload?.trackType == 'tick' ||
          payload?.trackType == 'stop' ||
          payload?.trackType == 'pause' ||
          payload?.trackType == 'resume' ||
          payload?.trackType == 'seek')
        ? `${payload?.url + '&timecode=' + payload?.timecode}`
        : payload?.url,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getPlayerTrackAPISuccess(response))
        },
        onError(error) {
          store.dispatch(getPlayerTrackAPIError(error))
        }
      },
      false
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* playerSaga() {
  yield takeEvery('player/getLivePlayer', getLivePlayerApi)
  yield takeEvery('player/getMediaAPI', getMediaAPI)
  yield takeEvery('player/getTrailerPlayer', getTrailerPlayerApi)
  yield takeEvery('player/getLinealPayway', getLinealPaywayApi)
  yield takeEvery('player/getRecordPlayer', getRecordPlayerApi)
  yield takeEvery('player/getPlayerTrackAPI', getPlayerTrackAPI)
}
