
$color-white: #ffffff;
$position-center: center;
$background-color: #333547;
$background-color-grey: #212224;
$background-color-red: #981c15 0% 0% no-repeat padding-box;
$white-space-nowrap: nowrap;
$overflow-hidden: hidden;
$text-overflow-ellipsis: ellipsis;
.App {
  top: 0px;
  left: 0px;
  width: 1920px;
  position: fixed;
  height: 1080px;
}
.App-logo {
  top: -1px;
  left: 0px;
  width: 1920px;
  height: 104px;
}
.promotion-telmex-telcel-outer-container {
  width: 1920px;
  margin-top: 20px;
}
.promotion-telmex-telcel-inner-container {
  height: 750px;
  color: #ffffff;
  display: flex;
  justify-content: center;
  width: 1920px;
}
.modal-promotion-telcel-telmex {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 1700px;
}
.modal-promotion-header {
  color: #ffffff;
  font-size: 45px;
  font-family: 'Roboto';
}
.modal-contenedor-tabla {
  width: 1700px;
  margin: 35px 0;
  letter-spacing: -0.51px;
  line-height: 38px;
}
.modal-contenedor-fila {
  display: flex;
  padding: 0 20px;
  flex-direction: row;
}
.modal-contenedor-columna-telmex,
.modal-contenedor-columna-telcel {
  display: flex;
  flex-direction: column;
  text-align: justify;
  padding: 0 20px;
  width: 960px;
  height: 600px;
  justify-content: space-between;
  font-size: 34px;
  font-family: 'Roboto';
}
.modal-contenedor-columna-img-line {
  color: #ffffff;
  border: 0.5px solid #ffffff;
}
.modal-promotion-telcel-text3 {
  padding-bottom: 10px;
  border-bottom: 0.5px solid white;
}
.modal-promotion-text4 {
  padding-top: 10px;
  border-top: 0.5px solid white;
}
.model-promotion-text3 {
  padding: 10px 0;
}
.model-text-bold,
.model-text-bold-promotion,
.modal-promotion-text1 {
  font-weight: bold;
}
.promotion-telmex-telcel-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.promotion-telmex-telcel-button {
  height: 72px;
  width: 719px;
  border-radius: 8.8px;
  background-color: #981c15;
  padding: 10px;
  color: #ffffff;
  font-size: 32px;
  font-family: 'Roboto';
  letter-spacing: -0.51px;
  line-height: 38px;
  text-align: center;
  .promotion-font {
    font-size: 40px;
    font-weight: bold;
  }
  &:focus {
    background-color: #981c15;
    height: 83px;
    width: 828px;
    color: #ffffff;
    font-size: 32px;
    letter-spacing: -0.51px;
    line-height: 38px;
    text-align: center;
  }
}
.logo-img {
  margin-top: 20px;
  margin-left: 60px;
  height: 121px;
  opacity: 1;
}
button {
  all: unset;
}
.registerpage-height {
  height: 491px;
}
.register-header {
  margin-top: 10px;
  left: 873px;
  height: 64px;
  text-align: center;
  letter-spacing: 0px;
  color: #ffffff;
  font-family: 'Roboto';
  font-weight: bold;
  line-height: 56px;
  font-size: 48px;
  opacity: 1;
}
.choose-reg-option {
  height: 86px;
  width: 770px;
  background-color: #1d1e26;
  margin-left: 566px;
  border-radius: 44px;
}
.Reg-mobile {
  color: $color-white;
  height: 55px;
  font-size: 26px;
  margin-top: 16px;
  margin-left: 23px;
}
.Reg-Remote {
  color: $color-white;
  font-size: 26px;
  margin-left: 51px;
  text-align: $position-center;
}
.Reg-Remote:focus {
  background-color: $background-color;
  border-radius: 32px;
  width: 369px;
  height: 68px;
  margin-left: 41px;
}
.Reg-Remote.active {
  background-color: $background-color;
  border-radius: 32px;
  width: 369px;
  height: 68px;
  margin-left: 41px;
}
.Reg-mobile:focus {
  background-color: $background-color;
  border-radius: 6px;
}
.register-title {
  margin-top: 147px;
  height: 54px;
}
.register-email-title {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 64px;
}
.email {
  margin-top: 63px;
  margin-left: 648px;
  width: 109px;
  height: 33px;
  text-align: center;
  font-family: 'Roboto';
  font-size: 40px;
  line-height: 48px;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
}

.registeremail-title {
  height: 33px;
  text-align: center;
  font-family: 'Roboto';
  font-size: 40px;
  line-height: 48px;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
}
.placeholder-color {
  color: #999;
  margin-top: 19px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
}
.register-namereg {
  margin-top: 19px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.regemail-redborder {
  margin-top: 19px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
}
.register-namereg:focus {
  border: 3px solid #4c6f94;
}
.placeholder-color:focus {
  border: 3px solid #4c6f94;
}
.regemail-redborder:focus {
  margin-top: 19px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
  border: 3px solid #981c15;
}
.register-password {
  margin-top: 40px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.regpassword-redborder {
  margin-top: 40px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
}
.password-placeholder {
  margin-top: 40px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: #999;
}
.register-password:focus {
  border: 3px solid #4c6f94;
}
.password-placeholder:focus {
  border: 3px solid #4c6f94;
}
.regpassword-redborder:focus {
  margin-top: 40px;
  margin-left: 600px;
  padding: 13px 119px 13px 26px;
  width: 564px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
  border: 3px solid #981c15;
}
.button {
  all: unset;
}
.register-email {
  all: unset;
}
.register-namepass {
  all: unset;
}
/*sigin*/
/* .error-box {
    margin-top: 7px
} */
.Error-case-name {
  margin-top: -28px;
}
.error-box:before {
  border-right-color: red;
  border-width: 0;
  margin-top: 0;
}
.email-error-box {
  position: absolute;
  left: 601px;
  top: 489px;
  .invalid-mail-msg {
    width: 708px;
    height: 83px;
    background: #eeeeee 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
    font-size: 28px;
    text-align: center;
    color: #981c15;
    position: absolute;
  }
  .invalid-text {
    width: 708px;
    height: 102px;
    background: #eeeeee 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
    font-size: 28px;
    text-align: center;
    color: #981c15;
    position: absolute;

    .email-error-msg {
      margin-top: 15px;
      margin-left: 99px;
      width: 517px;
    }
  }
}
.register-input-height {
  height: 207px;
}
.invalid-text:after,
.invalid-text:before {
  left: 343px;
  bottom: 96px;
  border: solid transparent;
  content: ' ';
  width: 0;
  position: absolute;
  pointer-events: none;
}
.invalid-text:after {
  border-width: 34px 13px 18px 14px;
  border-bottom: 20px solid #eeeeee;
}
.invalid-mail-msg:after,
.invalid-mail-msg:before {
  left: 343px;
  bottom: 78px;
  border: solid transparent;
  content: ' ';
  width: 0;
  position: absolute;
  pointer-events: none;
}
.invalid-mail-msg:after {
  border-width: 34px 13px 18px 14px;
  border-bottom: 20px solid #eeeeee;
}
.invisible {
  display: none;
}
.radio-box {
  height: 114px;
}
.checkbox-containerreg {
  display: flex;
  align-items: $position-center;
  outline: unset;
  margin-left: 597px;
  margin-top: 15px;
}
.checkbox-containerreg:focus input ~ .checkmarkreg {
  z-index: 1;
  background: transparent 0% 0% no-repeat padding-box;
  border: 4px solid #981c15;
  border-radius: 6px;
}
.checkbox-subcontainereg {
  margin-top: -65px;
  display: block;
  position: relative;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.checkbox-subcontainereg input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.checkmarkreg {
  position: absolute;
  top: 0;
  left: 0;
  height: 71px;
  width: 73px;
  background-color: $background-color-grey;
  border-radius: 6px;
}
.checkbox-subcontainereg input:checked ~ .checkmarkreg {
  background-color: $background-color-grey;
}
.checkbox-subcontainereg input:checked ~ .checkmarkreg:after {
  display: block;
}
.checkbox-subcontainereg .checkmarkreg:after {
  left: 26px;
  top: 14px;
  width: 14px;
  height: 25px;
  font-weight: bold;
  border: solid white;
  border-width: 0px 7px 7px 0;
  -webkit-transform: rotate(35deg);
  -ms-transform: rotate(35deg);
  transform: rotate(35deg);
}
.checkmarkreg:after {
  content: '';
  position: absolute;
  display: none;
}
.invalid-text-check {
  width: 714px;
  height: 54px;
  background: #eeeeee 0% 0% no-repeat padding-box;
  border-radius: 6px;
  opacity: 1;
  font-size: 28px;
  text-align: $position-center;
  padding-top: 16px;
  letter-spacing: 0px;
  color: #981c15;
  opacity: 1;
  position: absolute;
  margin-top: 65px;
}
.invalid-text-check:after,
.invalid-text-check:before {
  left: 29px;
  top: -74%;
  border: solid transparent;
  content: ' ';
  width: 0;
  position: absolute;
  pointer-events: none;
}
.invalid-text-check:after {
  border-width: 34px 13px 18px 14px;
  border-bottom: 20px solid #eeeeee;
}
.checkbox_name {
  letter-spacing: 0px;
  height: 111px;
  color: #ffffff;
  opacity: 1;
  margin-left: 91px;
  width: 272px;
  overflow: hidden;
  text-overflow: ellipsis;
  .text_terms_and{
    color: #ffffff;
    font-size: 28px;
    line-height: 20px;
    font-family: "Roboto";
  }
  .text_condition{
    color: #ffffff;
    font-size: 28px;
    line-height: 0px;
    font-family: "Roboto";
  }
}
.terms-and-conditon {
  line-height: 0px;
}
.terms-and-conditon-text{
  line-height: 10px;
}
.read-details {
  position: absolute;
  bottom: 382px;
  left: 19px;
  display: flex;
}
.read-detail:focus {
  background: $background-color-red;
}
.read-detail {
  margin-left: 969px;
  height: 75px;
  padding-left: 15px;
  padding-right: 12px;
  background: #212224;
  font-family: 'Roboto';
  text-align: center;
  line-height: 28px;
  opacity: 1;
  font-size: 24px;
  letter-spacing: 0px;
  border-radius: 6px;
  color: #ffffff;
  width: 297px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.next:focus {
  width: 772px;
  margin-left: 568px;
  border-radius: 6px;
  background: $background-color-red;
  color: #ffffff;
  transform: scale(1.1);
}
.next {
  margin-top: 22px;
  margin-left: 597px;
  width: 719px;
  border-radius: 6px;
  font-size: 32px;
  font-family: 'Roboto';
  font-weight: bold;
  letter-spacing: -0.51px;
  line-height: 35px;
  height: 72px;
  color: #787878;
  background: #4b1512 0% 0% no-repeat padding-box;
  opacity: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.next-enable{
  width: 719px;
  margin-left: 597px;
  border-radius: 6px;
  background: $background-color-red;
  color: #ffffff;
  margin-top: 22px;
  font-size: 32px;
  font-family: 'Roboto';
  font-weight: bold;
  letter-spacing: -0.51px;
  line-height: 35px;
  height: 72px;
  opacity: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.next-enable:focus,.next:focus{
  width: 772px;
  margin-left: 568px;
  background: $background-color-red;
}

.clear {
  margin-top: 32px;
  margin-left: 597px;
  width: 719px;
  font-size: 32px;
  height: 72px;
  color: #ffffff;
  background: #2e303d 0% 0% no-repeat padding-box;
  opacity: 1;
  text-align: center;
  font-family: 'Roboto';
  font-weight: bold;
  letter-spacing: -0.51px;
  border-radius: 8.8px;
  line-height: 35px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.clear:focus {
  width: 772px;
  margin-left: 568px;
}

.view-button {
  margin-top: 27px;
  margin-left: 978px;
  width: 339px;
  height: 73px;
  color: #ffffff;
  font-family: 'Roboto';
  font-size: 24px;
  letter-spacing: 0px;
  line-height: 28px;
  text-align: center;
  background-color: #212224;
  opacity: 1;
  border-radius: 6px;
}
.view-button:focus {
  position: absolute;
  width: 360px;
  right: 606px;
  .view-button-text{
    width: 339px;
    margin-left: 10px;
  }
}
.view-button-text{
  margin-top: 10px;
}

/*register password page*/

.password-Titlereg {
  margin-top: 60px;
  height: 35px;
  text-align: center;
  font: normal normal normal 40px / 30px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
}
.Passwordtlt {
  margin-top: 64px;
  margin-left: 602px;
  width: 124px;
  height: 33px;
  text-align: left;
  font: normal normal normal 28px/33px Roboto;
  letter-spacing: 0px;
  color: #a4a9ae;
  opacity: 1;
}
.register-namepass {
  padding: 0px 102px 0px 20px;
    width: 446px;
  margin-right: 23px;
  height: 72px;
  background: #212224 0% 0% no-repeat padding-box;
  text-align: left;
  font: normal normal normal 30px / 35px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  border-radius: 6px;
  opacity: 1;
}
.register-namepass:focus {
  border: 3px solid #4c6f94;
}
.hidesymbolreg {
  display: flex;
  width: 225px;
  align-items: center;
  justify-content: center;
  height: 72px;
  background-color: #2e303d;
  border-radius: 6px;
  position: relative;
}
.hidesymbolreg:focus {
  width: 259px;
  height: 83px;
  right: 12px;
}
.imageicon {
  width: 60px;
  height: 48px;
  margin-top: 1px;
}
.Reg-invalid-text {
  top: 435px;
  left: 137px;
  width: 426px;
  height: 70px;
  background: #eeeeee 0% 0% no-repeat padding-box;
  border-radius: 12px;
  opacity: 1;
  font-size: 28px;
  text-align: $position-center;
  padding-top: 31px;
  letter-spacing: -0.45px;
  color: #981c15;
  opacity: 1;
  margin-top: -93px;
  margin-left: 122px;
}
.Reg-invalid-text::after {
  border-width: 10px;
}
.Reg-invalid-text:after,
.Reg-invalid-text:before {
  left: 547px;
  top: 45.6%;
  border: solid transparent;
  content: ' ';
  width: 0;
  position: absolute;
  pointer-events: none;
}
.Reg-invalid-text:after {
  border-width: 10px 10px 10px 20px;
  border-left: 27px solid #eeeeee;
}
.passwordValidCheck {
  display: none;
  position: absolute;
  left: 650px;
}
.password-msg {
  position: absolute;
  left: 650px;
}
.tracker-box {
  width: 426px;
  height: 273px;
  background: #eeeeee 0% 0% no-repeat padding-box;
  border: 2px solid #eeeeee;
  border-radius: 6px;
  margin-top: 55px;
  position: absolute;
}
.tracker-box:after,
.tracker-box:before {
  left: 200px;
  bottom: 531px;
  bottom: 100%;
  border: solid transparent;
  content: ' ';
  width: 5px;
  position: absolute;
  pointer-events: none;
}
.tracker-box:after {
  border-width: 19px 11px 8px 10px;
  border-bottom: 19px solid #eeeeee;
}
.password-req {
  margin-top: -35px;
  margin-left: 37px;
  color: green;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.password-error-text {
  margin-top: -35px;
  margin-left: 6px;
  color: #34353b;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.redtext {
  margin-top: -35px;
  margin-left: 37px;
  color: #981c15;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.Password-req {
  margin-top: 11px;
  margin-left: 26px;
  height: 33px;
  text-align: left;
  letter-spacing: -0.45px;
  color: #34353b;
  opacity: 1;
  font-size: 28px;
  font-weight: bold;
  font-family: 'Roboto-Bold';
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}

.Password-req-name {
  width: 324px;
  height: 34px;
  text-align: left;
  letter-spacing: -0.45px;
  color: #34353b;
  opacity: 1;
  font-size: 28px;
  font-family: 'Roboto-Regular';
  margin-top: 20px;
  margin-left: 24px;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}

.Password-req-case {
  margin-top: 24px;
  margin-left: 24px;
  width: 324px;
  text-align: left;
  letter-spacing: -0.45px;
  color: #34353b;
  opacity: 1;
  font-size: 28px;
  font-family: 'Roboto-Regular';
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.register-next-enable{
  background: $background-color-red !important;
  color: #ffffff !important;
  width: 830px !important;
}

.register-next,.register-next-enable {
  width: 720px;
  height: 73px;
  background: #4b1512 0% 0% no-repeat padding-box;
  opacity: 1;
  text-align: $position-center;
  font-size: 34px;
  font-family: 'Roboto';
  font-weight: bold;
  color: #787878;
  border-radius: 6px;
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
}
.register-next:focus {
  background: $background-color-red;
  color: #ffffff;
  width: 830px;
}
.password-next {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 94px;
}

.registermaail-next-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

.signature:focus {
  background: $background-color-red;
  color: #ffffff;
  width: 830px;
}
.signature,.signature-enable {
  margin-top: 60px;
  width: 720px;
  height: 72px;
  color: #ffffff;
  color: #787878;
  background: #4b1512 0% 0% no-repeat padding-box;
  opacity: 1;
  border-radius: 8.8px;
  font-family: Roboto;
  font-size: 32px;
  font-weight: bold;
  letter-spacing: -0.51px;
  line-height: 35px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.signature-enable{
  background: $background-color-red !important;
  color: #ffffff !important;
  width: 830px !important;
}
.register-email {
  margin-top: 32px;
  padding: 13px 49px 13px 24px;
  width: 759px;
  height: 44px;
  background-color: $background-color-grey;
  font: normal normal normal 30px/35px Roboto;
  opacity: 1;
  border-radius: 6px;
  color: $color-white;
}
.register-email:focus {
  border: 3px solid #4c6f94;
  width: 759px;
}
