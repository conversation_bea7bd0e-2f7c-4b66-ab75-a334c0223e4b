import React from 'react'
import './EpgDescription.scss'
import { useSelector } from 'react-redux'

function EpgDescription(props) {
  const { channelErr } = props
  sessionStorage.setItem('selectedgroupdate', JSON.stringify(props))
  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const epgFilterName = useSelector(state => state?.epg?.epgFilterName?.payload)
  const channelDownId = useSelector(state => state?.epg?.channelDownId) 
  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ? apilanguage?.[str] : str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  const capitalizeSentences = text => {
    return text
      ?.split('.')
      ?.map(sentence => {
        const words = sentence.trim().split(' ')
        const capitalizedFirstWord =
          words[0]?.charAt(0)?.toUpperCase() + words[0]?.slice(1)?.toLowerCase()
        const remainingWords = words?.slice(1).map(word => word.toLowerCase())
        return [capitalizedFirstWord, ...remainingWords].join(' ')
      })
      ?.join('. ')
  }

  const filterHeaderChange = () => {
    if (
      epgFilterName ===
      (apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ||
        'playingLive_fullEpg_title_favoritesChannels_label')
    ) {
      return (
        apilanguage?.playingLive_miniEpg_option_button_fullEpg?.toUpperCase() ??
        'playingLive_miniEpg_option_button_fullEpg'
      )
    } else {
      return truncateText(
        'TvEnVivo_GuiaCompleta_PanelShortcuts_TextoFavoritos',
        30
      )
    }
  }

  const formatDuration = duration => {
    let hour =
      duration?.slice(0, 2) !== '00'
        ? parseInt(duration?.slice(0, 2), 10) + 'h '
        : ''
    let minutes =
      duration?.slice(3, 5) !== '00'
        ? parseInt(duration?.slice(3, 5), 10) + ' mins '
        : ''
    return hour + minutes
  }

  return (
    <>
      <div className="description-container">
        {props?.event && (
          <>
            <h1 className="description-title">
              {props?.event?.name === 'NA'
                ? 'No disponible'
                : props?.event?.name}
            </h1>
            <p className="description-subtitle">
              {props?.event?.name != 'NA' && (
                <>
                  {props?.event?.dvb_content && (
                    <span className="description-category">
                      {props?.event?.name === 'NA'
                        ? ''
                        : props?.event?.dvb_content}
                    </span>
                  )}
                  {props?.event?.date_end && (
                    <span
                      className={`description-year  ${
                        !props?.event?.dvb_content ? 'no-content' : ''
                      }`}
                    >
                      {props?.event?.name === 'NA'
                        ? ''
                        : props?.event?.date_end?.slice(0, 4)}
                    </span>
                  )}
                  <span className="age-alert">
                    {` ${props?.event?.parental_rating} Años`}
                  </span>
                  <span className="description-broadcast-schedule">{`${props?.event?.date_begin?.slice(
                    -8,
                    -6
                  )}.${props?.event?.date_begin?.slice(
                    -5,
                    -3
                  )}hs a ${props?.event?.date_end?.slice(
                    -8,
                    -6
                  )}.${props?.event?.date_end?.slice(-5, -3)}hs`}</span>
                  <span className="content-duration">
                    {formatDuration(props?.event?.duration)}
                  </span>
                </>
              )}
            </p>
            <div className="description-details">
              <p>
                {props?.event?.name === 'NA' ? '' : props?.event?.description}
              </p>
            </div>
            {props?.event?.name != 'NA' && (
              <span>
                {props?.event?.ext_director && (
                  <span className="talent-details">
                    <span className="talent-title">
                      {truncateText('Metadata_TextoDirector', 30) + ': '}
                    </span>
                    <span>{props?.event?.ext_director}</span>
                  </span>
                )}
                {props?.event?.talent && (
                  <span
                    className={`talent-details ${
                      props?.event?.ext_director ? 'epg-extra-details' : ''
                    }`}
                  >
                    <span className="talent-title">
                      {truncateText('Metadata_TextoProtagonistas', 30) + ': '}
                    </span>
                    <span>
                      {props?.event?.talent &&
                        props?.event?.talent.split(',').slice(0, 2).join(', ')}
                      {props?.event?.talent &&
                      props?.event?.talent?.split(',')?.length > 2 ? (
                        <span>, ...</span>
                      ) : (
                        ''
                      )}
                    </span>
                  </span>
                )}
                {props?.event?.ext_country && (
                  <span
                    className={`talent-details ${
                      props?.event?.ext_director || props?.event?.talent
                        ? 'epg-extra-details'
                        : ''
                    }`}
                  >
                    <span className="talent-title">
                      {truncateText('Metadata_TextoPais', 30) + ': '}
                    </span>
                    <span>{props?.event?.ext_country}</span>
                  </span>
                )}
              </span>
            )}
          </>
        )}
      </div>
      {!props?.liveTvDetail && (
        <div>
          <div className="shortcut-bar-epg">
            {/* <div className="epg-shortcut-bar"> //Needed for future reference 
            <span className="epg-bar-heading">
              {capitalizeSentences(epgFilterName)}
            </span>
          </div>
          <div className="epg-shorcut-container">
            <div className="epg-shortcut-bar">
              <span className="shortcut-bar-icon shortcut-bar-icon-yellow"></span>
              <span className="shortcut-bar-text">
                {apilanguage?.playingLive_fullEpg_option_button_exit ||
                  'playingLive_fullEpg_option_button_exit'}
              </span>
            </div>
            {!channelErr && (
              <div className="epg-shortcut-bar">
                <span className="shortcut-bar-icon shortcut-bar-icon-blue"></span>
                <span className="shortcut-bar-text">
                  {apilanguage?.panelOptions_sidemenu_title_channel_label ||
                    'panelOptions_sidemenu_title_channel_label'}
                </span>
              </div>
            )}
            <div className="epg-shortcut-bar">
              <span className="shortcut-bar-icon shortcut-bar-icon-red"></span>
              <span className="shortcut-bar-text">
                {apilanguage?.playingLive_fullEpg_option_button_filterSelector ||
                  'playingLive_fullEpg_option_button_filterSelector'}
              </span>
            </div>
            <div className="epg-shortcut-bar">
              <span className="shortcut-bar-icon shortcut-bar-icon-green"></span>
              <span className="shortcut-bar-text">{filterHeaderChange()}</span>
            </div>
          </div> */}
          </div>
          <div className="shortcut-bar-epg2">
            <div className="epg-shortcut-bar">
              <span className="epg-bar-heading">
                {capitalizeSentences(epgFilterName)}
              </span>
            </div>
            <div className="epg-shorcut-container">
              <div className="epg-shortcut-bar">
                <span className="shortcut-bar-icon shortcut-bar-icon-yellow"></span>
                <span className="shortcut-bar-text">
                  {truncateText(
                    'TvEnVivo_GuiaCompleta_PanelShortcuts_TextoRegresar',
                    30
                  )}
                </span>
              </div>
              {(props?.groupData?.group?.common?.id != channelDownId) && (
                <div className="epg-shortcut-bar">
                  <span className="shortcut-bar-icon shortcut-bar-icon-blue"></span>
                  <span className="shortcut-bar-text">
                    {truncateText(
                      'TvEnVivo_GuiaCompleta_PanelShortcuts_TextoOpciones',
                      30
                    )}
                  </span>
                </div>
              )}
              <div className="epg-shortcut-bar">
                <span className="shortcut-bar-icon shortcut-bar-icon-red"></span>
                <span className="shortcut-bar-text">
                  {truncateText(
                    'TvEnVivo_GuiaCompleta_PanelShortcuts_TextoCanales',
                    30
                  )}
                </span>
              </div>
              <div className="epg-shortcut-bar">
                <span className="shortcut-bar-icon shortcut-bar-icon-green"></span>
                <span className="shortcut-bar-text">
                  {filterHeaderChange()}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default EpgDescription
