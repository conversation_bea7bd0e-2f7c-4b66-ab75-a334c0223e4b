import React, { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  getClearAllSettingsState,
  getRemindControlPin,
  getCheckControlPin,
  disableControlPin,
  getControlPin
} from '../../store/slices/settingsSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import AlphaNumericKeyboard from '../Keyboard/AlphaNumericKeboard'
import './Settings.scss'
import { interactionType, PIN_PROTECTION, TV } from '../../GoogleAnalyticsConstants'
import { pushNewInteractionContentEvent, pushScreenViewEvent } from '../../GoogleAnalytics'

const DeactivateControlPin = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const pinRef = useRef([])
  const startTimeRef = useRef(null)

  const invisible = 'images/Icono_Onboarding_Close.png'
  const visible = 'images/Icono_Onboarding_Open.png'

  const [pin, setPin] = useState(new Array(6).fill(''))
  const [visbilityIcon, setVisibilityIcon] = useState(invisible)
  const [buttonDisable, setButtonDisable] = useState(true)
  const [pinInvalid, setPinInvalid] = useState('')
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [focusedIdx, setFocusedIdx] = useState(0)

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const checkSecurityPin = useSelector(
    state => state?.settingsReducer?.checkControlPin
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const disableSecurityPin = useSelector(
    state =>
      state?.settingsReducer?.disableControlPin?.response?.profiles?.[0]
        ?.parental.active
  )
  const disableSecurityPinError = useSelector(
    state => state?.settingsReducer?.disableControlPinError?.errors?.[0]?.error
  )
  const remindSecurityPin = useSelector(
    state => state?.settingsReducer?.remindControlPin?.response
  )

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handlePinVisbility = () => {
    visbilityIcon === invisible
      ? setVisibilityIcon(visible)
      : setVisibilityIcon(invisible)
  }

  const handleOTPChange = (element, index) => {
    const value = element.value ?? element

    if (value == 'cl') {
      setPin(pin.map((d, idx) => (idx === focusedIdx ? '' : d)))
      setFocusedIdx(index === 0 ? index : index - 1)
      return
    }
    if (value == 'clr') {
      setPin(new Array(pin.length).fill(''))
      setFocusedIdx(0)
      setPinInvalid('')
      return 
    }     
    if (/^\d$/.test(value)) {
      setPin(pin.map((d, idx) => (idx === index ? value : d)))
      setFocusedIdx(index === 5 ? index : index + 1)
      } else {
        setPin(pin.map((d, idx) => (idx === index ? '' : d)))
        setPinInvalid('')
      }
      setPinInvalid('')
   }

  const handleCheckPin = () => {
    state && state.gaContentData && (state.gaContentData['engagement_time_msec'] = Date.now() - startTimeRef.current);
    pushNewInteractionContentEvent(
      state?.gaContentData,
      PIN_PROTECTION,
      TV,
      interactionType?.SIGUIENTE
    )
    startTimeRef.current = Date.now()
    const payload = {
      controlPIN: pin.join(''),
      userId: userDetails?.user_id,
      hks: userDetails?.session_stringvalue,
      parental: 1
    }
    dispatch(getCheckControlPin(payload))
  }

  const handleForgotPin = () => {
    state && state.gaContentData && (state.gaContentData['engagement_time_msec'] = Date.now() - startTimeRef.current);
    pushNewInteractionContentEvent(
      state?.gaContentData, 
      PIN_PROTECTION, 
      TV,
      interactionType?.OLVIDASTE_TU_PIN_DE_SEGURIDAD
    )
    startTimeRef.current = Date.now()
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_hash: userDetails?.session_userhash
    }
    dispatch(getRemindControlPin(payload))
  }

  useEffect(() => {
    remindSecurityPin &&
      navigate('/PinConfirmation', {
        state: {
          pageName: 'deactivatePin'
        }
      })
  }, [remindSecurityPin])

  useEffect(() => {
    startTimeRef.current = Date.now()
    pushScreenViewEvent({screenName:'deactivate_pin', screenData:userDetails, prevScreenName : 'parental_control'})
    setKeyboardFocus(true)
    // const blinkerText = () => {
    //   if (document.querySelector('.pin-focused')) {
    //     let p = document.querySelector('.pin-focused').value
    //     if (p?.substr(-1, 1) == '|') {
    //       let removePipe = p?.replace('|', '')
    //       document.querySelector('.pin-focused').value = removePipe
    //     } else if (p?.length == 0) {
    //       document.querySelector('.pin-focused').value = p + '|'
    //     }
    //   }
    // }

    // const blinkerInterval = setInterval(blinkerText, 1000)
    // return () => {
    //   clearInterval(blinkerInterval)
    // }
    return () => {
      startTimeRef.current = null
    }
  }, [])

  useEffect(() => {
    if (pin && pin[0]?.length > 0 && pin[1]?.length > 0 && pin[2]?.length > 0) {
      setButtonDisable(false)
    } else {
      setButtonDisable(true)
    }
    pin[5]?.length > 0 && document.getElementById('siguienteButton')?.focus()
  }, [pin])

  useEffect(() => {
    if (checkSecurityPin?.msg === 'OK') {
      const payload = {
        code: pin.join(''),
        user_token: userDetails?.user_token,
        user_id: userDetails?.user_id,
        hks: userDetails?.session_stringvalue
      }
      dispatch(disableControlPin(payload))
      setVisibilityIcon(invisible)
      setButtonDisable(true)
      setPinInvalid('')
      setKeyboardFocus(true)
    } else if (checkSecurityPin?.msg === 'ERROR') {
      setPinInvalid(checkSecurityPin?.errors)
      dispatch(getClearAllSettingsState())
      setPin(new Array(6).fill(''))
      setVisibilityIcon(invisible)
      document.getElementById('Key_0')?.focus()
      setButtonDisable(true)
      setFocusedIdx(0)
    }
  }, [checkSecurityPin])

  useEffect(() => {
    if (disableSecurityPin === false) {
      const payload = {
        hks: userDetails?.session_stringvalue,
        user_id: userDetails?.user_id,
        user_token: userDetails?.user_token
      }
      dispatch(getControlPin(payload))
      dispatch(getClearAllSettingsState())
      navigate('/settings/profile-settings', {
        state: { pageName: 'parentalControl', focusElement: 'deactivateButton' }
      })
    } else if (disableSecurityPinError) {
      setPinInvalid(disableSecurityPinError)
      dispatch(getClearAllSettingsState())
      setPin(new Array(6).fill(''))
      setKeyboardFocus(true)
      setFocusedIdx(0)
    }
  }, [disableSecurityPin, disableSecurityPinError])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      state && state.gaContentData && (state.gaContentData['engagement_time_msec'] = Date.now() - startTimeRef.current);
      pushNewInteractionContentEvent(
        state?.gaContentData,
        PIN_PROTECTION,
        TV,
        interactionType?.CANCELAR
      )
      startTimeRef.current = Date.now()
      navigate('/settings/profile-settings', {
        state: { pageName: 'parentalControl', focusElement: 'deactivateButton' }
      })
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode == 'backClick') {
      state && state.gaContentData && (state.gaContentData['engagement_time_msec'] = Date.now() - startTimeRef.current);
      pushNewInteractionContentEvent(
        state?.gaContentData,
        PIN_PROTECTION,
        TV,
        interactionType?.CANCELAR
      )
      startTimeRef.current = Date.now()
      navigate('/settings/profile-settings', {
        state: { pageName: 'parentalControl', focusElement: 'deactivateButton' }
      })
    }
  }

  const handleTVRemoteKey = (event) => {    
    const key = event.key;
    if (/^\d$/.test(key)) {
      const currentIndex = pin.findIndex((digit) => digit === '');
      if (currentIndex !== -1) {
        const newOtp = [...pin];
        newOtp[currentIndex] = key;
        key?.length == 1 && setPin(newOtp);
        if (currentIndex < 5) {
          setFocusedIdx(currentIndex + 1)
        }
      }
      setPinInvalid('')
    }    
  };

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
    handleTVRemoteKey(event)
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])


  return (
    <div className="app-css-change-pin">
      <div className="deactivate-pin-logo-container">
        <img className="deactivate-pin-logo" src={'images/logo.png'} />
        <button
          className="back-indicator-button-pin focusable"
          id="backButton"
          onClick={e => handleLgkey('backClick')}
        >
          <img
            className="yellow-indicator-button"
            src={'images/yellow_shortcut.png'}
          />
          <img
            className="back-indicator-image"
            src={'images/back_button.png'}
          />
          <span>{truncateText('top_head_option_button_back', 30)}</span>
        </button>
      </div>

      <div className="left-container-div">
        <AlphaNumericKeyboard
          type="alphaNumeric"
          onChange={e => handleOTPChange(e, focusedIdx)}
          autoFocus={keyboardFocus}
          name="security-pin"
        />
      </div>
      <div className="right-container-div">
        <span className="pin-screen-title ">
          {truncateText('modal_deactivate_pin_confrim_title', 30)}
        </span>
        <span className="pin-screen-sub-title deactivate-pin-subtitle">
          {truncateText('modal_deactivate_pin_confrim_message', 200)}
        </span>
        <div >
          <>
            <div className="pin-box-div">
              {pin?.map((item, index) => {
                return (
                  <div key={index} className="pin-wrapper">
                  <input
                    style={
                      visbilityIcon === invisible
                        ? { fontSize: '128px' }
                        : { fontSize: '32px' }
                    }
                    className={
                      pinInvalid
                        ? 'pin-field-invalid'
                        : focusedIdx == index
                          ? 'pin-focused'
                          : 'pin-field'
                    }
                    type={'text'}
                    name="pin"
                    onKeyUp={e => !/[0-9]/.test(e.key) && e.preventDefault()}
                    id={`pin${index}`}
                    ref={ref => (pinRef.current[index] = ref)}
                    maxLength={1}
                    key={index}
                    value={
                      visbilityIcon === invisible && item?.length > 0
                        ? '*'
                        : item
                    }
                    onChange={e => handleOTPChange(e.target, index)}
                    inputMode="none"
                    readOnly
                    data-testid={`pin${index}`}
                  />
                    {/* Show blinking pipe cursor if input is focused and empty */}
                    {focusedIdx === index && item === '' && (
                      <span className="pin-cursor">|</span>
                    )}
                  </div>
                )
              })}
              <button
                onClick={handlePinVisbility}
                className="see-pin-button focusable"
                data-testid={`pinVisible`}
                id="seePin"
              >
                <img src={visbilityIcon} />
              </button>
            </div>

            {pinInvalid ? (
              <p className="pin-error">
                <span className="pin-error-contents">
                  {truncateText(
                    'lockChannel_tooltip_valid_label_validation',
                    50
                  )}
                </span>
              </p>
            ) : null}

            <button
              className="deactivate-pin-screen-button focusable"
              id="siguienteButton"
              disabled={buttonDisable}
              onClick={handleCheckPin}
            >
              {truncateText('bt_suscripcion_siguiente', 30)}
            </button>

            <button
              className="pin-screen-button-cancel focusable"
              onClick={e => handleLgkey('backClick')}
            >
              {truncateText('modal_pin_cancel_button', 30)}
            </button>

            <button
              onClick={handleForgotPin}
              className="deactivate-forgot-pin-button focusable"
              data-testid={`forgotPin`}
            >
              <p className="deactivate-forgot-pin-content">
                {truncateText('modal_pin_forgot_pin', 35)}
              </p>
            </button>
          </>
        </div>
      </div>
    </div>
  )
}

export default React.memo(DeactivateControlPin)
