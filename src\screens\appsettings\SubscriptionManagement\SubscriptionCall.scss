.background-call-page {
  width: 1920px;
  height: 1080px;
  position: fixed;

  .claro-symbol {
    height: 33.08px;
    width: 162.69px;
    margin-top: 22px;
    margin-left: 61px;
  }

  .back-button-indicator {
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 4.4px;
    background-color: #2e303d;
    float: right;
    margin-top: 22px;
    margin-right: 64px;
    padding: 8px 24px;

    &:focus {
      border: 3px solid #ffffff;
      border-radius: 14px;
      outline: none;
    }

    .yellow-indicator {
      height: 20px;
      width: 20px;
      margin-right: 24px;
      flex-shrink: 0;
    }

    .back-image {
      height: 24px;
      width: 30px;
      margin-right: 24px;
      flex-shrink: 0;
    }

    .back-text {
      color: #ffffff;
      font-family: Roboto;
      font-size: 19.36px;
      font-weight: bold;
      line-height: 19.36px;
      flex-shrink: 0;
      margin: 0;
    }
  }

  .call-wrapper {
    display: flex;
    width: 1920px;
    height: 800px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;

    .first-title-text {
      height: 47px;
      width: 831px;
      margin-top: 89.48px;
      color: #ffffff;
      font-family: <PERSON>o;
      font-size: 40px;
      letter-spacing: 0;
      line-height: 47px;
      text-align: center;
    }

    .second-title-text {
      height: 76px;
      width: 495px;
      margin-top: 88px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 38px;
      text-align: center;
    }

    .third-title-text {
      height: 38px;
      width: 402px;
      margin-top: 64px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      letter-spacing: 0;
      line-height: 38px;
      text-align: center;
    }

    .fourth-title-text {
      height: 42px;
      width: 299px;
      margin-top: 32px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 42px;
      text-align: center;
    }

    .fifth-title-text {
      height: 76px;
      width: 457px;
      margin-top: 40px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      letter-spacing: 0;
      line-height: 38px;
      text-align: center;
    }

    .acceptar-button {
      height: 82.08px;
      width: 520.26px;
      border-radius: 11.54px;
      margin-top: 104px;
      background-color: #2e303d;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36.5px;
      font-weight: bold;
      text-align: center;
      letter-spacing: -0.58px;
      line-height: 42.18px;

      &:focus {
        transform: scale(1.1);
        background-color: #981c15;
      }
    }
  }
}
