import epgSlice, {
    getEpgMenu,
  getEpgMenuSuccess,
  getEpgMenuError,
  getEpgVersion,
  getEpgVersionSuccess,
  getEpgVersionError,
  getEpgChannel,
  getEpgChannelBackUp,
  getEpgChannelSuccess,
  getEpgChannelError,
  getEpgLineup,
  getEpgLineupSuccess,
  getEpgLineupError,
  getEpgFullData,
  getFavouriteLive,
  getFavouriteLiveSuccess,
  getFavouriteLiveError,
  addFavouriteLive,
  addFavouriteLiveSuccess,
  addFavouriteLiveError,
  delFavouriteLive,
  delFavouriteLiveSuccess,
  delFavouriteLiveError,
  clearFavouriteRespose,

  getLiveReminder,
  addLiveReminder,
  delLiveReminder,

  getLiveReminderSuccess,
  getLiveReminderError,
  addLiveReminderSuccess,
  addLiveReminderError,
  delLiveReminderSuccess,
  delLiveReminderError,

  getLiveTvRecording,
  getLiveTvCompleteRecording,
  getLiveTvProgressRecording,
  getLiveTvEpisodeRecoding,
  addLiveTvSeriesRecording,
  addLiveTvEpisodeRecording,
  delLiveTvSeriesRecording,
  delLiveTvEpisodeRecording,
  deleteMyContentRecording,
  getClearRecordingState,
  getLiveTvRecordingSuccess,
  getLiveTvCompletedRecordingSuccess,
  getLiveTvCompletedRecordingError,
  getLiveTvProgressRecordingSuccess,
  getLiveTvProgressRecordingError,
  getLiveTvRecordingError,
  getLiveTvEpisodeRecordingSuccess,
  getLiveTvEpisodeRecordingError,
  addLiveTvSeriesRecordingSuccess,
  addLiveTvSeriesRecordingError,
  addLiveTvEpisodeRecordingSuccess,
  addLiveTvEpisodeRecordingError,
  delLiveTvSeriesRecordingSuccess,
  delLiveTvSeriesRecordingError,
  delLiveTvEpisodeRecordingSuccess,
  delLiveTvEpisodeRecordingError,
  deleteMyContentRecordingSuccess,
  deleteMyContentRecordingError,

  getcurrentcardindex,

  getPayWayToken,
  getPayWayTokenSuccess,
  getPayWayTokenError,

  getPayWayPurchase_ButtonInfo,
  getPayWayPurchase_ButtonInfoSuccess,
  getPayWayPurchase_ButtonInfoError,

  getProgramDetailsData,
  getEpgFilteredData,
  getEpgFilterName,
  getEventDetails,
  getAlerts,
  getFavouriteAlerts,
  resetFavouriteAlerts,
  clearReminderState,
  getNoLockChannels,
  getClearEPGState,
  getPopUpState,
  setReminderPopupVisible,
} from './EpgSlice'

describe('EpgSlice reducer', () => {
    const initialState = {
        epgMenu: {},
        epgVersion: '',
        soaVersion: '',
        epgChannel: [],
        epgChannelBackup: [],
        epgLineup: [],
        isLoading: false,
        isChannelLoading: false,
        error: {},
        favouriteLive: [],
        addfavouriteList: {},
        delfavourite: {},
        isAddfavouriteLoading: false,
        isDelfavouriteLoading: false,
        addfavouriteLiveError: {},
        delfavouriteLiveError: {},

        ReminderLive: [],
        addReminderList: {},
        delReminder: {},
        isAddReminderLoading: false,
        isDelReminderLoading: false,
        addReminderLiveError: {},
        delReminderLiveError: {},

        RecordingList: [],
        CompletedRecordingList: {},
        InprogrssRecordingList: {},
        EpisodeRecordingList: {},
        addSeriesRecording: {},
        addEpisodeRecording: {},
        deleteRecordingSeries: {},
        deleteRecordingEpisode: {},

        isSeriesRecordLoading: false,
        recordingSeriesError: {},
        isEpisodeRecordLoading: false,
        recordingEpisodeError: {},
        isDelSeriesRecording: false,
        deleteRecordingSeriesError: {},
        deleteMyContentRecording:{},
        deleteMyContentRecordingError:{},
        isDelEpisodeRecording: false,
        deleteRecordingEpisodeError: {},

        isPurchaseButtonloadding: false,
        purchaseButtonlist: {},
        purchaseButtonlistError: {},

        currentcard: '',
        paywayToken: [],
        viewProgramDetailsData: {},
        viewEpgFilteredData: {},
        epgFilterName: '',
        programEventData: {},
        recordingAlerts: {},
        favouriteAlerts: '',
        showMyContentButton: false,
        noLockChannelsList: {},
        scheduleReminder: '',
        isReminderPopupVisible: false,
        recordingErrors:{},
        RecordingListSeries: [],
        channelDownId: ''
    }
    
    it('should return initialState', () => {
      expect(epgSlice(undefined, {})).toEqual(initialState)  
    });

    it('should run getEpgMenu action', () => {
        const action = {type: getEpgMenu.type}
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgMenuSuccess action', () => {
        const payload = 'getEpgMenuSuccess'
        const action = {type: getEpgMenuSuccess.type, payload}
        const expectedState = {...initialState, epgMenu: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgMenuError action', () => {
        const payload = 'getEpgMenuError'
        const action = {type: getEpgMenuError.type, payload}
        const expectedState = {...initialState, error: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgVersion action', () => {
        const action = {type: getEpgVersion.type }
        const expectedState = {...initialState, isLoading: true }
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgVersionSuccess action', () => {
        const payload = {response: {epg_version: 'epg_version',soa_version: 'soa_version'}}
        const action = {type: getEpgVersionSuccess.type, payload}
        const expectedState = {...initialState, epgVersion:payload.response.epg_version, soaVersion: payload.response.soa_version}
        expect(epgSlice(initialState, action)).toEqual(expectedState) 
    });

    it('should run getEpgVersionError action', () => {
        const payload = 'getEpgVersionError'
        const action = {type: getEpgVersionError.type, payload}
        const expectedState = {...initialState, isLoading: false, error: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgChannel action', () => {
        const payload = 'channel'
        const action = {type: getEpgChannel.type, payload}
        const expectedState = {...initialState, epgChannel: payload, isChannelLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState) 
    });

    it('should run getEpgChannelBackUp action', () => {
        const payload = 'getEpgChannelBackUp'
        const action = {type: getEpgChannelBackUp.type, payload}
        const expectedState = {...initialState, epgChannelBackup: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgLineup action', () => {
        const action = {type: getEpgLineup.type}
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgLineupSuccess action', () => {
        const payload = 'getEpgLineupSuccess'
        const action = {type: getEpgLineupSuccess.type, payload}
        const expectedState = {...initialState, epgLineup: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getEpgLineupError action', () => {
        const payload = 'getEpgLineupError'
        const action = {type: getEpgLineupError.type, payload}
        const expectedState = {...initialState, error: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getFavouriteLive action', () => {
        const action = {type: getFavouriteLive.type}
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getFavouriteLiveSuccess action', () => {
        const payload = 'getFavouriteLiveSuccess'
        const action = {type: getFavouriteLiveSuccess.type, payload}
        const expectedState = {...initialState, favouriteLive: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getFavouriteLiveError action', () => {
        const payload = 'getFavouriteLiveError'
        const action = {type: getFavouriteLiveError.type, payload}
        const expectedState = {...initialState, error: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addFavouriteLive action', () => {
        const action = {type: addFavouriteLive.type}
        const expectedState = {...initialState, isAddfavouriteLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addFavouriteLiveSuccess action', () => {
        const payload = 'addFavouriteLiveSuccess'
        const action = {type: addFavouriteLiveSuccess.type,payload}
        const expectedState = {...initialState, addfavouriteList:payload, isAddfavouriteLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addFavouriteLiveError action', () => {
        const payload = 'addFavouriteLiveError'
        const action = {type: addFavouriteLiveError.type, payload}
        const expectedState = {...initialState, addfavouriteLiveError: payload, isAddfavouriteLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delFavouriteLive action', () => {
        const action = {type: delFavouriteLive.type}
        const expectedState = {...initialState, isDelfavouriteLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delFavouriteLiveSuccess action', () => {
        const payload = 'delFavouriteLiveSuccess'
        const action = {type: delFavouriteLiveSuccess.type, payload}
        const expectedState = {...initialState,delfavourite: payload, isDelfavouriteLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delFavouriteLiveError action', () => {
        const payload = 'delFavouriteLiveError'
        const action = {type: delFavouriteLiveError.type, payload}
        const expectedState = {...initialState, delfavouriteLiveError: payload, isDelfavouriteLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run clearFavouriteRespose action', () => {
        const action = {type: clearFavouriteRespose.type}
        const expectedState = {...initialState, addfavouriteList: {}, delfavourite: {}}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveReminder action', () => {
        const action = {type: getLiveReminder.type}
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveReminderSuccess action', () => {
        const payload = 'getLiveReminderSuccess'
        const action = {type: getLiveReminderSuccess.type, payload}
        const expectedState = {...initialState, ReminderLive: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveReminderError action', () => {
        const payload = 'getLiveReminderError'
        const action = {type: getLiveReminderError.type, payload}
        const expectedState = {...initialState, error: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveReminder action', () => {
        const action = {type: addLiveReminder.type}
        const expectedState = {...initialState, isAddReminderLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveReminderSuccess action', () => {
        const payload = 'addLiveReminderSuccess'
        const action = {type: addLiveReminderSuccess.type, payload }
        const expectedState = {...initialState, addReminderList: payload, isAddReminderLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveReminderError action', () => {
        const payload = 'addLiveReminderError'
        const action = {type: addLiveReminderError.type, payload }
        const expectedState = {...initialState, isAddReminderLoading: false, addReminderLiveError: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveReminder action', () => {
        const action = {type: delLiveReminder.type}
        const expectedState = {...initialState, isDelReminderLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveReminderSuccess action', () => {
        const payload = 'delLiveReminderSuccess'
        const action = {type: delLiveReminderSuccess.type, payload }
        const expectedState = {...initialState, delReminder: payload, isDelReminderLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveReminderError action', () => {
        const payload = 'delLiveReminderError'
        const action = {type: delLiveReminderError.type, payload}
        const expectedState = {...initialState, delReminderLiveError: payload, isDelReminderLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvRecording action', () => {
        const action = {type: getLiveTvRecording.type }
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvRecordingSuccess action', () => {
        const payload = 'getLiveTvRecordingSuccess'
        const action = {type: getLiveTvRecordingSuccess.type, payload}
        const expectedState = {...initialState, RecordingList: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvRecordingError action', () => {
        const payload = 'getLiveTvRecordingError'
        const action = {type: getLiveTvRecordingError.type, payload}
        const expectedState = {...initialState, error: payload, isLoading: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvCompleteRecording action', () => {
        const payload = 'getLiveTvCompleteRecording'
        const action = {type: getLiveTvCompleteRecording.type, payload }
        const expectedState = {...initialState, isLoading: true }
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvCompletedRecordingSuccess action', () => {
        const payload = 'getLiveTvCompletedRecordingSuccess'
        const action = {type: getLiveTvCompletedRecordingSuccess.type, payload}
        const expectedState = {...initialState, CompletedRecordingList: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvCompletedRecordingError action', () => {
        const payload = 'getLiveTvCompletedRecordingError'
        const action = {type: getLiveTvCompletedRecordingError.type, payload}
        const expectedState = {...initialState, error: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvProgressRecording action', () => {
        const action = {type: getLiveTvProgressRecording.type}
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvProgressRecordingSuccess action', () => {
        const payload = 'getLiveTvProgressRecordingSuccess'
        const action = {type: getLiveTvProgressRecordingSuccess.type, payload}
        const expectedState = {...initialState, InprogrssRecordingList: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvProgressRecordingError action', () => {
        const payload = 'getLiveTvProgressRecordingError'
        const action = {type: getLiveTvProgressRecordingError.type, payload}
        const expectedState = {...initialState, error: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvEpisodeRecoding action', () => {
        const payload = 'getLiveTvEpisodeRecoding'
        const action = {type: getLiveTvEpisodeRecoding.type, payload}
        const expectedState = {...initialState, isLoading: true }
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvEpisodeRecordingSuccess action', () => {
        const payload = 'getLiveTvEpisodeRecordingSuccess'
        const action = {type: getLiveTvEpisodeRecordingSuccess.type, payload}
        const expectedState = {...initialState, EpisodeRecordingList: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getLiveTvEpisodeRecordingError action', () => {
        const payload = 'getLiveTvEpisodeRecordingError'
        const action = {type: getLiveTvEpisodeRecordingError.type, payload}
        const expectedState = {...initialState, error: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveTvSeriesRecording action', () => {
        const action = {type: addLiveTvSeriesRecording.type}
        const expectedState = {...initialState, isSeriesRecordLoading: true }
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveTvSeriesRecordingSuccess action', () => {
        const payload = 'addLiveTvSeriesRecordingSuccess'
        const action = {type: addLiveTvSeriesRecordingSuccess.type, payload}
        const expectedState = {...initialState, isSeriesRecordLoading: false, addSeriesRecording: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveTvSeriesRecordingError action', () => {
        const payload = 'addLiveTvSeriesRecordingError'
        const action = {type: addLiveTvSeriesRecordingError.type, payload }
        const expectedState = {...initialState, isSeriesRecordLoading: false, recordingSeriesError: payload, recordingErrors: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveTvEpisodeRecording action', () => {
        const action = {type: addLiveTvEpisodeRecording.type}
        const expectedState = {...initialState, isEpisodeRecordLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveTvEpisodeRecordingSuccess action', () => {
        const payload = 'addLiveTvEpisodeRecordingSuccess'
        const action = {type: addLiveTvEpisodeRecordingSuccess.type, payload}
        const expectedState = {...initialState, isEpisodeRecordLoading: false, addEpisodeRecording: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run addLiveTvEpisodeRecordingError action', () => {
        const payload = 'addLiveTvEpisodeRecordingError'
        const action = {type: addLiveTvEpisodeRecordingError.type, payload}
        const expectedState = {...initialState, isEpisodeRecordLoading: false, recordingEpisodeError: payload, recordingErrors: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveTvSeriesRecording action', () => {
        const action = {type: delLiveTvSeriesRecording.type}
        const expectedState = {...initialState, isDelSeriesRecording: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveTvSeriesRecordingSuccess action', () => {
        const payload = 'delLiveTvSeriesRecordingSuccess'
        const action = {type: delLiveTvSeriesRecordingSuccess.type, payload}
        const expectedState = {...initialState, deleteRecordingSeries: payload, isDelSeriesRecording: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveTvSeriesRecordingError action', () => {
        const payload = 'delLiveTvSeriesRecordingError'
        const action = {type: delLiveTvSeriesRecordingError.type, payload}
        const expectedState = {...initialState, isDelSeriesRecording: false, deleteRecordingSeriesError: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveTvEpisodeRecording action', () => {
        const action = {type: delLiveTvEpisodeRecording.type}
        const expectedState = {...initialState, isDelEpisodeRecording: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveTvEpisodeRecordingSuccess action', () => {
        const payload = 'delLiveTvEpisodeRecordingSuccess'
        const action = {type: delLiveTvEpisodeRecordingSuccess.type, payload}
        const expectedState = {...initialState, deleteRecordingEpisode: payload, isDelEpisodeRecording: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run delLiveTvEpisodeRecordingError action', () => {
        const payload = 'delLiveTvEpisodeRecordingError'
        const action = {type: delLiveTvEpisodeRecordingError.type, payload}
        const expectedState = {...initialState, isDelEpisodeRecording: false, deleteRecordingEpisodeError: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run deleteMyContentRecording action', () => {
        const action = {type: deleteMyContentRecording.type}
        const expectedState = {...initialState, isDelEpisodeRecording: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run deleteMyContentRecordingSuccess action', () => {
        const payload = 'deleteMyContentRecordingSuccess'
        const action = {type: deleteMyContentRecordingSuccess.type, payload}
        const expectedState = {...initialState, deleteMyContentRecording: payload, isDelEpisodeRecording: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run deleteMyContentRecordingError action', () => {
        const payload = 'deleteMyContentRecordingError'
        const action = {type: deleteMyContentRecordingError.type, payload}
        const expectedState = {...initialState, isDelEpisodeRecording: false, deleteMyContentRecordingError: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getClearRecordingState action', () => {
        const action = {type: getClearRecordingState.type}
        const expectedState = {...initialState, addSeriesRecording:{}, addEpisodeRecording: {}, deleteRecordingSeries: {},
                                deleteRecordingEpisode: {}, recordingSeriesError: {}, recordingEpisodeError: {}}
        expect(epgSlice(initialState, action)).toEqual(expectedState) 
    });

    it('should run getcurrentcardindex action', () => {
        const payload = 'getcurrentcardindex'
        const action = {type: getcurrentcardindex.type, payload }
        const expectedState = {...initialState, currentcard: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPayWayToken action', () => {
        const action = {type: getPayWayToken.type }
        const expectedState = {...initialState, isLoading: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPayWayTokenSuccess action', () => {
        const payload = 'getPayWayTokenSuccess'
        const action = {type: getPayWayTokenSuccess.type, payload }
        const expectedState = {...initialState, isLoading: false, paywayToken: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPayWayTokenError action', () => {
        const payload = 'getPayWayTokenError'
        const action = {type: getPayWayTokenError.type, payload}
        const expectedState = {...initialState, isLoading: false, error: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPayWayPurchase_ButtonInfo action', () => {
        const action = {type: getPayWayPurchase_ButtonInfo.type}
        const expectedState = {...initialState, isPurchaseButtonloadding: true}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPayWayPurchase_ButtonInfoSuccess action', () => {
        const payload = 'getPayWayPurchase_ButtonInfoSuccess'
        const action = {type: getPayWayPurchase_ButtonInfoSuccess.type, payload}
        const expectedState = {...initialState, purchaseButtonlist: payload, isPurchaseButtonloadding: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPayWayPurchase_ButtonInfoError action', () => {
        const payload = 'getPayWayPurchase_ButtonInfoError'
        const action = {type: getPayWayPurchase_ButtonInfoError.type, payload}
        const expectedState = {...initialState, purchaseButtonlistError: payload, isPurchaseButtonloadding: false}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    // it('should run getProgramDetailsData action', () => {
    //     const  payload = 'getProgramDetailsData'
    //     const action = {type: getProgramDetailsData.type, payload}
    //     const expectedState = {...initialState, viewProgramDetailsData: payload}
    //     expect(epgSlice(initialState, action)).toEqual(expectedState)
    // });

    // it('should run getEpgFilteredData action', () => {
    //     const payload = 'getEpgFilteredData'
    //     const action = {type: getEpgFilteredData.type, payload}
    //     const expectedState = {...initialState, viewEpgFilteredData: payload}
    //     expect(epgSlice(initialState, action)).toEqual(expectedState)
    // });

    // it('should run getEpgFilterName action', () => {
    //     const payload = 'getEpgFilterName'
    //     const action = {type: getEpgFilterName.type, payload}
    //     const expectedState = {...initialState, epgFilterName: payload }
    //     expect(epgSlice(initialState, action)).toEqual(expectedState)
    // });

    it('should run getEventDetails action', () => {
        const payload = 'getEventDetails'
        const action = {type: getEventDetails.type, payload}
        const expectedState = {...initialState, programEventData: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getAlerts action', () => {
        const payload = 'getAlerts'
        const action = {type: getAlerts.type, payload}
        const expectedState = {...initialState, recordingAlerts: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getFavouriteAlerts action', () => {
        const payload = {message: 'getFavouriteAlerts', displayMyContentButton: 'getFavouriteAlerts'}
        const action = {type: getFavouriteAlerts.type, payload}
        const expectedState = {...initialState, favouriteAlerts: payload.message, showMyContentButton: payload.displayMyContentButton }
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run resetFavouriteAlerts action', () => {
        const action = {type: resetFavouriteAlerts.type}
        const expectedState = {...initialState, resetFavouriteAlerts: initialState.resetFavouriteAlerts, showMyContentButton: initialState.showMyContentButton }
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run clearReminderState action', () => {
        const action = {type: clearReminderState.type}
        const expectedState = {...initialState, addReminderList: initialState.addReminderList, delReminder: initialState.delReminder}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getNoLockChannels action', () => {
        const payload = 'getNoLockChannels'
        const action = {type: getNoLockChannels.type, payload}
        const expectedState = {...initialState, noLockChannelsList: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getPopUpState action', () => {
        const payload = 'getPopUpState'
        const action = {type: getPopUpState.type, payload}
        const expectedState = {...initialState, scheduleReminder: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run setReminderPopupVisible action', () => {
        const payload = 'setReminderPopupVisible'
        const action = {type: setReminderPopupVisible.type, payload}
        const expectedState = {...initialState, isReminderPopupVisible: payload}
        expect(epgSlice(initialState, action)).toEqual(expectedState)
    });

    it('should run getClearEPGState action', () => {
        const action = {type: getClearEPGState.type}
        const expectedState = {...initialState, ReminderLive: {}, epgMenu: {}, epgVersion: '',
        soaVersion: '', epgChannel: [], epgChannelBackup: [], epgLineup: [], viewProgramDetailsData: {},
        viewEpgFilteredData: {}, epgFilterName: '', RecordingList: [], purchaseButtonlist: {},
        paywayToken: [], noLockChannelsList: {}, favouriteLive: [], addfavouriteList: {}, delfavourite: {},
        addfavouriteLiveError: {}, delfavouriteLiveError: {}, currentcard: '' }
    });
})