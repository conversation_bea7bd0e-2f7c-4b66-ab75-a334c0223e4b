import { store } from '../store/sagaStore'
import { call, takeEvery } from '@redux-saga/core/effects'
import {
  getLoginSuccess,
  getWatch<PERSON>ree,
  loginError,
  setSkeltonLoading
} from '../store/slices/login'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { getPushSession } from '../store/slices/ProfileSlice'

function* getLoginApi({ payload }) {
  let Username = localStorage.getItem('signinemail')
  const region = localStorage.getItem('region')
  payload?.type != 'splashLogin' && store.dispatch(setSkeltonLoading(true))

  try {
    yield call(
      request,
      payload?.type == 'nonAdmin'
        ? `${URL.LOGIN_URL}&HKS=${payload.hks}&userhash=${payload.userHash}&region=${region}`
        : payload?.type == 'splashLogin'
          ? `${URL.LOGIN_URL}&HKS=${payload.hks}&userhash=${payload.userHash}&region=${region}`
          : `${URL.LOGIN_URL}&password=${payload.password}&username=${Username}&HKS=${payload.hks}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          // Invalid region is coming in startheader API response for few rigions eg, CostaRica,Dominicana and Elsalavador. Hence again updating the valid region after login
          localStorage.setItem('region', response?.response?.region)
          localStorage.setItem('token', response.response.user_token)
          localStorage.setItem('login_user', true)
          store.dispatch(getLoginSuccess(response))
          store.dispatch(getWatchFree())
          payload?.type === 'nonAdmin'
            ? store.dispatch(
              getPushSession({
                hks: response.response?.session_stringvalue,
                userId: response.response.user_id,
                userSession: response.response?.user_session
              })
            )
            : null
          payload?.type != 'nonAdmin' &&
            store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(loginError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
    store.dispatch(setSkeltonLoading(false))
  }
}

export default function* loginSaga() {
  yield takeEvery('login/getLogin', getLoginApi)
}
