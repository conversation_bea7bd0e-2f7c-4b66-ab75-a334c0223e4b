.app-subscription-css {
	width: 100vw;
	height: 100vh;
	position: fixed;
	background: #121212;

	.multi-back-indicator {
		display: flex;
        align-items: center;
        height: 62px;
        width: 306px;
        border-radius: 6.6px;
        background-color: #2e303d;
        margin-left: auto;
        margin-right: 64px;
		margin-top: 47px;
		float: right;
        box-sizing: border-box;
        border: none;
        outline: none;

		&:focus {
			border: 3px solid #ffffff;
            border-radius: 14px;
            outline: none;
		}

		.yellow-indicator {
			height: 20px;
            width: 20px;
            margin-right: 24px;
                  margin-left: 24px;
            flex-shrink: 0;
		}

		.back-image {
			height: 24px;
            width: 30px;
            margin-right: 24px;
            flex-shrink: 0;
		}

		.back-text {
			color: #ffffff;
            font-family: Roboto, sans-serif;
            font-size: 29.04px;
            font-weight: bold;
            line-height: 30px;
            flex-shrink: 0;
            margin: 0;
		}
	}
}

.subscription-warapper {
	display: flex;
	flex-direction: column;
	position: absolute;
	top: 92px;
	align-items: center;


	.add-subs-title {
		color: #ffffff;
		font-family: <PERSON><PERSON>;
		font-size: 40px;
		letter-spacing: 0;
		text-align: center;
	}

	.multi-subscriptions-div {
		display: flex;
		overflow-x: scroll;
		height: 840px;
		width: 100vw;
		justify-content: center;
		align-content: center;
		scroll-snap-type: x mandatory;

		.multi-cards {

			.image-banner {
				height: 590px !important;
				width: 254px !important;
			}

			.multi-subs-meta-data {
				position: absolute;
				bottom: 199px;
				margin-left: 3px;
				display: flex;
				flex-wrap: wrap;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				.add-sub-logo {
					display: flex;
					height: 50px;
					width: 200px;
				}

				.pricing-info {
					text-align: center;
					letter-spacing: 0px;
					color: #eeeeee;
					opacity: 1;
					font-size: 19.55px;
					font-family: Roboto;
					display: flex;
					margin-top: 25px;

					.price-type,
					.price-css {
						height: 28px;
						color: #eeeeee;
						font-family: Roboto;
						font-size: 19.55px;
						font-weight: bold;
						letter-spacing: 0;
						line-height: 27.6px;
						text-align: center;
					}

					.tax-slash {
						height: 28px;
						width: 11px;
						color: #eeeeee;
						font-family: Roboto;
						font-size: 11.5px;
						letter-spacing: 0;
						line-height: 27.6px;
					}

					.periodicity {
						height: 28px;
						color: #eeeeee;
						font-family: Roboto;
						font-size: 11.5px;
						letter-spacing: 0;
						line-height: 27.6px;
					}
				}

				.tax-label {
					letter-spacing: 0px;
					color: #eeeeee;
					font-family: Roboto;
					font-size: 13.8px;
					letter-spacing: 0;
					line-height: 18.4px;
					text-align: center;
					opacity: 1;
					margin-top: 9px;
				}

				.sub-buttons-div {
					margin-top: 20px;

					.subs-button {
						height: 40px;
						width: 200px;
						opacity: 1;
						margin-bottom: 11px;
						border-radius: 4px;
						text-transform: uppercase;
						color: #ffffff;
						font-family: Roboto;
						font-size: 16.1px;
						font-weight: bold;
						letter-spacing: 0;
						line-height: 19px;
						text-align: center;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
					}
				}

			}

			.multi-plan-button {
				height: 48px;
				width: 260.4px;
				border-radius: 6.6px;
				background-color: #2e303d;
				position: relative;
				opacity: 0;
				display: flex;
				align-items: center;
				margin-top: 50px !important;

				.blue-short-cut {
					width: 20px;
					margin-left: 15px;
					margin-right: 24px;
				}

				.view-button-contents {
					color: #ffffff;
					font-family: Roboto;
					font-size: 25px;
					font-weight: bold;
					letter-spacing: 0;
					line-height: 29.04px;
					height: 30px;
					width: 211px;
				}
			}
		}

		.multi-cards:focus {
			margin-right: 25px;
			margin-left: 25px;
			transform: scale(1.1);

			.multi-plan-button {
				opacity: 1;
				height: 48px;
				display: flex;
				align-items: center;
			}
		}

		.multi-plan-button:focus{
			opacity: 1;
			height: 48px;
			display: flex;
			align-items: center;
			transform: scale(1.1);
		}
	}
}