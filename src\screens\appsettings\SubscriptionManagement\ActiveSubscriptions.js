import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getActiveSubscriptions, getCancelSubscribeViewData } from "../../../store/slices/settingsSlice";
import './SubscriptionManagements.scss';
import { useNavigate } from "react-router";

const ActiveSubscriptions = () => {

	const dispatch = useDispatch();
	const navigate = useNavigate();

	const region = localStorage.getItem('region')

	const userDetails = useSelector((state) => state?.login?.isLoggedIn?.response)
	const activeSubscriptionData = useSelector((state) => state?.settingsReducer?.activeSubscriptions?.response)
	const apaAssetsImages = useSelector((state) => state?.Images?.imageresponse)
	const apaMeta = useSelector((state) => state?.initialReducer?.appMetaData)
	const CancelSubscriptionStatus = useSelector((state) => state?.settingsReducer?.cancelSubscription)
	const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)

	const activeData = [];

	const [showNotification, setShowNotification] = useState(false)

	const handleCancelSubscription = (e, data) => {
		e.preventDefault();
		dispatch(getCancelSubscribeViewData(data))
		navigate('/my-settings/my-subscriptions/cancel-subscription')
	}

	const handleLogo = (data) => {
		const logo = data?.toLowerCase();
		return apaAssetsImages["transactional_" + logo + "_logo"]; //checking the data is there in apa/assets

	}

	const dateConvertor = (inputDate) => {
		const [day, month, year] = inputDate.split('/');
		const dateObject = new Date(`${year}-${month}-${day}`);
		const formattedDay = dateObject.getDate();
		const formattedMonth = dateObject.toLocaleString('es-AR', { month: 'short' });
		const formattedYear = dateObject.getFullYear();
		const formattedDate = `${formattedDay} ${formattedMonth} ${formattedYear}`;
		return formattedDate;
	}

	const myAccountConfiguration = apaMeta?.myaccount_configuration && JSON?.parse(apaMeta?.myaccount_configuration)
	const providerList = []

	myAccountConfiguration?.default?.suscription?.map((subs) => {
		providerList.push(subs?.provider)
	})

	const checkProductType = (data) => {
		
		return providerList.includes(data) ? true : false
	}

	activeSubscriptionData?.map((each) => checkProductType(each?.producttype) ? activeData.push(each) : null)

	useEffect(() => {
		dispatch(getActiveSubscriptions({ 'userId': userDetails?.parent_id, 'hks': userDetails?.session_stringvalue }))
		document?.getElementById('cancel-button')?.focus()
	}, [])

	useEffect(() => {
		if (CancelSubscriptionStatus?.status === '0') {
			setShowNotification(true)
			setTimeout(() => {
				setShowNotification(false)
			}, 4000)
		}
	}, [])

	const keypresshandler = (event) => {
		const keycode = event.keyCode;
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow']);
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
			}
			handlesamsungkey(codes, keycode);
		} else {
			handleLgkey(keycode);
		}
	};

	useEffect(() => {
		document.addEventListener("keyup", keypresshandler);
		return () => {
			document.removeEventListener("keyup", keypresshandler);
		};
	}, [keypresshandler]);

	const handlesamsungkey = (key, keycode) => {
		if (key.yellowcode === keycode || keycode === 10009) {
			navigate('/my-settings/my-subscriptions');
		}
	}

	const handleLgkey = (keycode) => {
		if (keycode == 405 || keycode === 461 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
			navigate('/my-settings/my-subscriptions');
		}
	}


	return (
		<div className="appCss">
			<div>
				<div>
					<img src={'images/Logos_Claro_Video.svg'} className="claroLogo" alt="logo" />
					<div className="backIndicator">
						<img className="yellowIndicator" src={'images/yellow_shortcut.png'} />
						<img className="backImage" src={'images/back_button.png'} />
						<p className="backText">{translations?.language?.[region]?.atv_back_notification}</p>
					</div>
				</div>

				<p className="sub-desc">{translations?.language?.[region]?.atv_active_subscripyion_plans_screen_description ? translations?.language?.[region]?.atv_active_subscripyion_plans_screen_description : 'atv_active_subscripyion_plans_screen_description'}</p>
			</div>
			<div className="active-cards">
				{activeData?.map((each, index) =>
					each.waspurchase === 1 ?
						<div className="sub-div-active" key={index}>
							<div className='active-focus-card' >
								<div className="text-card" >
									<img src={handleLogo(each?.producttype)} className="plan-logo-css" style={each?.purchase_data?.in_process_canceled === 1 ? { marginLeft: '0%', justifyContent: 'center' } : null} alt="logo" />
									<p className="card-desc-subs">{translations?.language?.[region]?.atv_subscription_title}</p>
									{each?.purchase_data?.in_process_canceled === 1 ? null :
										<div>
											<span className="price-css">{each?.currency}{each?.price}</span>
											<span className="message">/ mes</span>
										</div>
									}
								</div>
								{each?.purchase_data?.in_process_canceled === 1 ?
									<div className="info-div">
										<img className="warning-image" src={'images/warning.png'} />
										<p className="info-text">{translations?.language?.[region]?.atv_subscription_cancelled_title},{translations?.language?.[region]?.atv_payment_suspended_label}</p>
									</div>
									:
									null
								}
								{each?.purchase_data?.in_process_canceled === 1 ?
									<>
										<div className="info-text1" >{translations?.language?.[region]?.atv_keep_enjoying_your_subscription}:</div>
										<div className="date-text" > {dateConvertor(each?.purchase_data?.expiration)}</div>
									</>
									:
									<div className="card-desc-date"><span className="next-payment-desc">{translations?.language?.[region]?.atv_next_payment_date}:</span> <span className="date-css">{dateConvertor(each?.purchase_data?.expiration)}</span></div>
								}
								{each?.purchase_data?.in_process_canceled === 0 ? (
									<p className="card-desc-payment">
										{' '}
										<span className="custom-class-1">
											{translations?.language?.[region]?.atv_payment_method}:
										</span>{' '}
										<span className="custom-class-2">
											{each?.purchase_data?.gateway}
										</span>
									</p>
								) : null}
							</div>
							{each?.waspurchase === 1 && each?.purchase_data?.in_process_canceled === 0 ?
								<button className=" active-subs-cancel-button focusable" id='cancel-button' onClick={(e) => handleCancelSubscription(e, each)}>
									<div className="active-subs-cancel-button-div" >
										{translations?.language?.[region]?.atv_cancel_subscription_label}
									</div>
								</button>
								: null
							}
						</div> : null
				)
				}
			</div>
		</div>
	)
};

export default ActiveSubscriptions;