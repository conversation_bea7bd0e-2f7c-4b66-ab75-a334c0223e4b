import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import LoginPassword from "./LoginPassword";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

describe('LoginPassword page test', () => {
    test('enter password input', () => {
        initialState.login = {
            RegBackNavigate: true
        }
        const result = renderWithState(<LoginPassword />)
        let input = result.container.querySelector('input[name="password"]')
        fireEvent.change(input, { target: { value: 'Preethi1' } })
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(result.container, 'signin');
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        renderWithState(<LoginPassword />)
    })
    test('enter password input', () => {
        const result = renderWithState(<LoginPassword />)
        let input = result.container.querySelector('input[name="password"]')
        fireEvent.change(input, { target: { value: 'Preethi1' } })
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(result.container, 'signin');
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        renderWithState(<LoginPassword />)
    })

    test('it should render the LoginForgotPassword Page onclick enter email button', () => {
        const result = renderWithState(<LoginPassword />)
        let input = result.container.querySelector('input[name="password"]')
        fireEvent.change(input, { target: { value: 'Preethi1' } })
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(result.container, 'password');
        fireEvent.keyDown(scroll, { keyCode: '13' })

        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('it should render the LoginForgotPassword Page onclick enter email button', () => {
        const result = renderWithState(<LoginPassword />)
        let input = result.container.querySelector('input[name="password"]')
        fireEvent.change(input, { target: { value: 'Preethi1' } })
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(result.container, 'password');
        fireEvent.keyDown(scroll, { keyCode: '38' })

        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})