import React, { useEffect } from 'react'
import { useSelector } from 'react-redux'
import './fibraLinesPopup.scss'

const CreditCardDataPopup = (props) => {
	const region = localStorage.getItem('region')
	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
	const monthsData = JSON.parse(apaMetaData?.months)
	const provincesFormat = apaMetaData?.provinces_codes && JSON?.parse(apaMetaData?.provinces_codes)
	const province = provincesFormat?.mexico
	const monthList = monthsData?.[region]
	const currentYear = new Date().getFullYear()

	const displayYears = [];
	for (let i = 0; i < 8; i++) {
		displayYears.push(currentYear + i)
	}

	const handleButtonClick = (e, data) => {
		e.preventDefault()
		props?.type == 'month' && props?.setMonthValue((monthList?.indexOf(data) + 1)?.toString()?.padStart(2, '0'))
		props?.type == 'year' && props?.setYearValue(data)
		props?.type == 'area' && props?.setAreaValue(data?.name)
		props?.type == 'area' && props?.setAreaCode(data?.code)
		props?.onClose()
	}

	useEffect(() => {
		document.getElementById('creditCard0')?.focus()
	}, [])

	return (
		<div className='credit-card-popup-data-div' id='creditCardPopupDataDiv'>
			<div className='credit-card-popup-sub-div' style={{gridTemplateColumns: props?.type == 'month' || props?.type == 'year' ? '440px 440px 440px 440px' : province?.provinces?.length > 0  && '300px 300px 300px 300px 300px 300px'}}>
				{props?.type == 'month' ?
					monthList?.map((each, index) => (
						<button className='credit-card-popup-buttons focusable' onClick={(e) => handleButtonClick(e, each)} key={index} id={`creditCard${index}`}>
							<span className='credit-card-popup-button-contents'>{each}</span>
						</button>
					))
					:
					props?.type == 'year' ?
						displayYears?.length > 0 && displayYears?.map((each, index) => (
							<button className='credit-card-popup-buttons focusable' onClick={(e) => handleButtonClick(e, each)} key={index} id={`creditCard${index}`}>
								<span className='credit-card-popup-button-contents'>{each}</span>
							</button>
						))
						:
						province?.provinces?.length > 0 && province?.provinces?.map((each, index) => (
							<button className='province-buttons focusable' onClick={(e) => handleButtonClick(e, each)} key={index} id={`creditCard${index}`}>
								<span className='province-button-contents'>{each?.name}</span>
							</button>
						))
				}
			</div>
		</div>
	)
}

export default CreditCardDataPopup