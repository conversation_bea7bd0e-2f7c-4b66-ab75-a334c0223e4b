import React from "react";
import { fireEvent, queryByAttribute, render, screen } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import Register from "./Register";

jest.mock('react-redux', () => ({
    ...jest.requireActual('react-redux'),
    useDispatch: () => jest.fn().mockImplementation((action) => action),
    useSelector: jest.fn()
  }));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: () => ({ 
    state: {
      regmail: '<EMAIL>',
      regpassword: 'password123'
    } 
  }),
  useNavigate: () => jest.fn()
}));
  jest.mock('../../GoogleAnalytics', () => ({
    pushScreenViewEvent: jest.fn()
  }));
const initialState = {
  initialReducer: {
    appMetaData: {
      translations: JSON.stringify({
        language: {
          mexico: {
            'Onboarding_RegistroRCU_Texto1': 'Test Translation',
            'Onboarding_RegistroRCU_TextoPlaceholder1': 'Email Placeholder',
            'Onboarding_RegistroRCU_TextoPlaceholder2': 'Password Placeholder',
            'Onboarding_CorreoInvalidoRCU_Form_TextoTooltip1': 'Invalid Email',
            'Onboarding_RegistroCorreoVinculadoRCU_Form_TextoTooltip1': 'Email Error',
            'Onboarding_RegistroCorreoVinculadoRCU_Form_TextoTooltip2': 'Second Error',
            'Onboarding_RegistroErrorRCU_Form_TextoTooltip1': 'Checkbox Error',
            'Onboarding_RegistroRCU_Texto2': 'Terms Text',
            'Onboarding_RegistroRCU_Texto3': 'Conditions Text',
            'Onboarding_RegistroRCU_TextoTycos1': 'Terms and Conditions 1',
            'Onboarding_RegistroRCU_TextoTycos2': 'Terms and Conditions 2',
            'Onboarding_RegistroRCU_TextoBotonPrimario': 'Next',
            'Onboarding_RegistroRCU_TextoBotonSecundario': 'Clear',
            'Onboarding_RegistroRCU_TextoTelmexTelcel1': 'Telemax 1',
            'Onboarding_RegistroRCU_TextoTelmexTelce2': 'Telemax 2',
            'promotions_access_text_html': '<div>Promotion content</div>',
            'promotions_access_option_button_close': 'Close'
          }
        }
      })
    },
    startHeaderInfo: {
      response: {
        session_stringvalue: 'test-session-string'
      }
    }
  },
  login: {
    registerSuccess: {
      status: '0',
      response: {
        gamification_id: 'test-gamification-id',
        parent_id: 'test-user-id',
        lasttouch: {
          profile: 'test-lasttouch',
          seen: 'test-seen-lasttouch'
        },
        session_stringvalue: 'test-hks',
        session_userhash: 'test-hash'
      }
    },
    registerError: {
      status: '1'
    },
    vcardSeriesDetails: {},
    vcardDetails: {},
    anonymousUser: {}
  }
};

const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
);

export const renderWithState = (ui, customState = initialState) => {
  return render(ui, { wrapper: (props) => <Wrapper {...props} reduxStore={mockStore(customState)} /> });
};

describe('Register page test', () => {
  beforeEach(() => {
    const localStorageMock = {
      getItem: jest.fn((key) => {
        if (key === 'region') return 'mexico';
        if (key === 'username') return '<EMAIL>';
        if (key === 'pwd') return 'password123';
        if (key === 'token') return 'test-token';
        return null;
      }),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
    };
    
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });

    document.getElementById = jest.fn().mockImplementation((id) => {
      if (id === 'promotion-telmex-telcel') {
        return { innerHTML: '' };
      }
      return { focus: jest.fn() };
    });
  });

  test('should render success api mock data', () => {
    initialState.login = {
      userInfo: { status: '1' },
      registerError: { status: '1' },
      vcardSeriesDetails: {},
      vcardDetails: {},
      anonymousUser: {}
    };
    history.push('/regpassword');
    renderWithState(<Register />);
  });

  test('should render error api mock data', () => {
    initialState.login = {
      userInfo: { status: '0' },
      registerError: { status: '1' },
      vcardSeriesDetails: {},
      vcardDetails: {},
      anonymousUser: {}
    };
    renderWithState(<Register />);
  });



  test('should handle different keyboard events', () => {
    const { container } = renderWithState(<Register />);
    fireEvent.keyUp(container, { keyCode: 10009 });
    fireEvent.keyUp(container, { keyCode: 405 });
    fireEvent.keyUp(container, { keyCode: 89 });
    fireEvent.keyUp(container, { keyCode: 461 });
    fireEvent.keyUp(container, { keyCode: 409 });
    fireEvent.keyUp(container, { keyCode: 8 });
  });

  test('should handle register form submit with checkbox checked', () => {
    const { container } = renderWithState(<Register />);
    const getById = queryByAttribute.bind(null, 'id');
    const checkbox = getById(container, 'checkbox');
    fireEvent.click(checkbox);
    const nextButton = getById(container, 'registernext');
    fireEvent.click(nextButton);
  });





});
