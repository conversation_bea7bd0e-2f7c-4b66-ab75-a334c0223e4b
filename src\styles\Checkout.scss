body {
  margin: 0px;
}

.checkout-container {
  display: flex;
  background-color: #121212;
  height: 1080px;
  width: 1920px;
  flex-direction: column;
  position: absolute;
}

.checkout-common-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  height: 1080px;
  width: 1920px;
  z-index: 2;
}

.checkout-loading-indicator {
  opacity: 0.5;
}

.telmex-payment-error-container {
  position: relative;
  top: 150px;
  height: 550px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .claro-image-telmex {
    width: 100px;
    height: 100px;
  }

  .telmex-error-text {
    width: 1110px;
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 47px;
    text-align: center;
    margin-top: 15px;
    display: flex;
    flex-direction: column;
  }

  .telmex-acceptar-button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 72px;
    width: 504px;
    border-radius: 8.8px;
    background-color: #981c15;
    padding: 10px;
    margin-top: 20px;

    &:focus {
      background-color: #981c15;
      height: 82.08px;
      width: 574.56px;
      color: #ffffff;
      font-size: 32px;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }
  }

  .telmex-acceptar-text {
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 47px;
    text-align: center;
    font-weight: bold;
  }
}

.checkout-layout-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  top: 148px;
  position: relative;

  .checkout-keyboard-container {
    display: flex;
    width: 960px;
    position: relative;

    .checkout-keyboard-layout {
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      margin-left: 80px;
      width: 1000px;

      .keyboardCover {
        width: 1000px;
      }

      .keyboard-special-buttons,
      .keyboard-clear-buttons {
        height: 51.25px;
        width: 148.63px;
        border-radius: 6.6px;
        background-color: #2e303d;
        border: 3px solid #121212;

        &:focus,
        &:active {
          outline: 3px solid white;
        }
      }

      .keyboard-clear-buttons {
        .top-buttom-image {
          margin-left: 0px;
        }
      }
    }
  }

  .checkout-details-container {
    display: flex;
    width: 920px;
    flex-direction: column;
    align-items: center;

    .checkout-title-1 {
      color: #ffffff;
      font-family: Roboto;
      font-size: 40px;
      letter-spacing: 0;
      line-height: 47px;
      margin-left: 90px;
      align-self: flex-start;
      width: 570px;
    }

    .hubgate-width {
      width: 590px;
    }
    .checkout-title-2 {
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      letter-spacing: 0;
      line-height: 47px;
      margin-left: 98px;
      align-self: flex-start;
      display: flex;
      margin-top: 90px;
    }

    .checkout-otp-container {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
      top: 60px;
    }

    .checkout-input-inactive {
      outline: none !important;
    }

    .checkout-input {
      outline: 4px solid #4c6f94;
      position: relative;
      top: 85px;

      .checkout-cursor {
        position: absolute;
        left: 15px;
        top: 12px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 40px;
        animation: blink 2s step-end infinite;
      }

      .input-text {
        height: 72px;
        width: 700px;
        font-size: 36px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 40px;
        padding-left: 15px;
        color: #ffffff;
        background-color: #212224;
        border: none;
      }

      &:focus,
      &:active {
        outline: 4px solid #4c6f94;
        height: 76px;
        width: 722px;
      }

      .credit-img {
        position: absolute;
        right: 10px;
        top: 20%;
        width: 81px;
        height: 49.33px;
      }
    }

    // .checkout-input-active {
    //   box-sizing: border-box;
    //   outline: 4px solid #4c6f94;
    // }

    .checkout-input-error {
      box-sizing: border-box;
      outline: 4px solid #c1272d;
    }

    .margin-for-input {
      margin-top: 32px;
    }

    .position-change {
      top: 147px;
    }

    .checkout-input-2 {
      margin-top: 15px;

      .input-text {
        height: 72px;
        width: 700px;
        font-size: 36px;
        letter-spacing: 0;
        line-height: 40px;
        padding-left: 15px;
        color: #ffffff;
        background-color: #212224;
      }

      &:focus,
      &:active {
        outline: 4px solid #4c6f94;
        height: 76px;
        width: 722px;
      }

      .input-text::placeholder {
        color: #7f8086;
        font-family: Roboto;
        font-size: 36px;
        margin: 24px;
      }
    }

    .checkout-hubgate-otp {
      height: 72px;
      width: 454px;
      border-radius: 6px;
      background: #212224;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      right: 10px;

      .checkout-cursor {
        position: absolute;
        left: 15px;
        top: 12px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 40px;
        animation: blink 2s step-end infinite;
      }

      .input-text {
        height: 32px;
        width: 397.98px;
        background: #212224;
        color: #ffffff;
        font-family: Roboto;
        font-size: 32px;
        letter-spacing: -0.51px;
        line-height: 32px;
        border: none;
      }

      &:active,
      &:focus {
        border-radius: 6px;
        border: 4px solid #4c6f94;
        background: #212224;
      }
    }

    .checkout-confirm-otp {
      height: 72px;
      width: 251px;
      border-radius: 6px;
      background-color: #2e303d;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .checkout-confirm-otp-text {
        height: 63px;
        width: 199px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 28px;
        font-weight: bold;
        letter-spacing: -0.45px;
        line-height: 33px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &:focus {
        transform: scale(1.1);
        left: 10px;
      }
    }

    .error-box-otp {
      position: relative;
      top: 10px;

      .invisible {
        display: none;
      }

      align-self: flex-end;
      .hubgate-error-otp {
        width: 715px;
        height: 75px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
        background: #eeeeee 0% 0% no-repeat padding-box;
        border-radius: 6px;
        opacity: 1;
        font-size: 28px;
        text-align: center;
        letter-spacing: -0.45px;
        color: #981c15;
        opacity: 1;
        margin-top: 16px;
        right: 0px;
        position: absolute;

        &:before,
        &:after {
          border: solid transparent;
          content: ' ';
          width: 0;
          position: absolute;
          pointer-events: none;
        }

        &:after {
          border-width: 12px 14px 10px 15px;
          border-bottom: 27px solid #eeeeee;
          bottom: 70px;
        }
      }
    }

    .error-box {
      position: relative;
      top: 10px;
      right: 25px;
      align-self: flex-end;

      .invisible {
        display: none;
      }

      .address-mobile-error {
        top: 65px;
      }

      .promocode-error,
      .hubgate-error {
        width: 722px;
        height: 75px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
        background: #eeeeee 0% 0% no-repeat padding-box;
        border-radius: 6px;
        opacity: 1;
        font-size: 28px;
        text-align: center;
        letter-spacing: -0.45px;
        color: #981c15;
        opacity: 1;
        margin-top: 102px;
        right: 75px;
        position: absolute;

        &:before,
        &:after {
          border: solid transparent;
          content: ' ';
          width: 0;
          position: absolute;
          pointer-events: none;
        }

        &:after {
          border-width: 12px 14px 10px 15px;
          border-bottom: 27px solid #eeeeee;
          bottom: 70px;
        }
      }

      .credit-card-error-month {
        height: 75px;
        width: 720px;
        display: flex;
        align-items: center;
        z-index: 2;
        background: #f4f4f4 0% 0% no-repeat padding-box;
        border-radius: 6px;
        opacity: 1;
        margin-top: 40px;
        right: 75px;
        position: absolute;

        &:before,
        &:after {
          border: solid transparent;
          content: ' ';
          width: 0;
          position: absolute;
          pointer-events: none;
          margin-left: 100px;
        }

        &:after {
          border-width: 12px 18px 10px 13px;
          border-bottom: 23px solid #f4f4f4;
          bottom: 70px;
        }

        .error-contents {
          color: #c1272d;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 38px;
          margin-left: 24px;
        }
      }

      .credit-card-error-year {
        height: 75px;
        width: 720px;
        display: flex;
        align-items: center;
        z-index: 2;
        background: #f4f4f4 0% 0% no-repeat padding-box;
        border-radius: 6px;
        opacity: 1;
        margin-top: 40px;
        right: 75px;
        position: absolute;

        &:before,
        &:after {
          border: solid transparent;
          content: ' ';
          width: 0;
          position: absolute;
          pointer-events: none;
          margin-left: 340px;
        }

        &:after {
          border-width: 12px 18px 10px 13px;
          border-bottom: 23px solid #f4f4f4;
          bottom: 70px;
        }

        .error-contents {
          color: #c1272d;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 38px;
          margin-left: 24px;
        }
      }

      .credit-card-error-cvv {
        height: 75px;
        width: 720px;
        display: flex;
        align-items: center;
        z-index: 2;
        background: #f4f4f4 0% 0% no-repeat padding-box;
        border-radius: 6px;
        opacity: 1;
        margin-top: 40px;
        right: 75px;
        position: absolute;

        &:before,
        &:after {
          border: solid transparent;
          content: ' ';
          width: 0;
          position: absolute;
          pointer-events: none;
          margin-left: 601px;
        }

        &:after {
          border-width: 12px 18px 10px 13px;
          border-bottom: 23px solid #f4f4f4;
          bottom: 70px;
        }

        .error-contents {
          color: #c1272d;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 38px;
          margin-left: 24px;
        }
      }

      .confirm-cvv-error {
        margin-top: 0px;
        top: 10px;
        right: 0px;
      }

      .pincode-error {
        right: 0px;
      }
    }

    .claropagos-confirm {
      margin-top: 150px !important;
    }

    .claropagos-address {
      margin-top: 200px !important;
    }

    .claropagos-confirm-cvv {
      margin-top: 300px !important;
    }

    .checkout-confirm-button {
      height: 72px;
      width: 722px;
      opacity: 1;
      border-radius: 6px;
      background-color: #981c15;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 17.36px;
      margin-top: 300px;

      &:focus {
        height: 82.08px;
        width: 820.8px;
        border-radius: 6.84px;
        background-color: #981c15;
      }

      &:disabled {
        opacity: 0.5;
      }
    }

    .checkout-cancel-button {
      height: 72px;
      width: 722px;
      border-radius: 8.8px;
      background-color: #2e303d;
      display: flex;
      justify-content: center;
      align-items: center;

      &:focus {
        height: 82.08px;
        width: 820.8px;
        border-radius: 6.84px;
        background-color: #2e303d;
      }
    }

    .checkout-button-text {
      height: 38px;
      width: 157px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }

    .checkout-hubgate-icon {
      height: 48px;
      width: 292px;
      border-radius: 9px;
      background-color: #323437;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      top: 60px;
      margin-left: 90px;
      align-self: flex-start;

      .checkout-hubgate-content {
        height: 37px;
        width: 175px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .checkout-hubgate-flag {
          height: 37px;
          width: 63px;
        }

        .checkout-hubgate-text {
          height: 34px;
          width: 96px;
          color: #c4cbc4;
          font-family: Roboto;
          font-size: 29px;
          letter-spacing: 0.32px;
          line-height: 34px;
        }
      }
    }

    .checkout-loader {
      height: 350px;
      width: 722px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 80px;
    }

    .confirm-card-loader {
      height: 275px;
      width: 722px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
    }

    .card-data-sub-div {
      display: flex;
      position: relative;
      top: 118px;

      .button-margin {
        margin-right: 24px;
      }

      .button {
        height: 72px;
        width: 224px;
        border-radius: 6px;
        background-color: #212224;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .button-contents {
          height: 32px;
          width: 64px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 32px;
        }

        .chevron-down-img {
          height: 24px;
          width: 48px;
        }
      }

      .button-cvv {
        height: 72px;
        width: 224px;
        border-radius: 6px;
        background-color: #212224;

        &:focus {
          box-sizing: border-box;
          border: 4px solid #4c6f94;
        }

        .button-input-cvv {
          height: 61px;
          width: 184px;
          background-color: #212224;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 32px;
          margin-left: 25px;
          border: none;
        }

        .checkout-cursor {
          position: absolute;
          right: 190px;
          top: 12px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 36px;
          font-weight: bold;
          letter-spacing: 0;
          line-height: 40px;
          animation: blink 2s step-end infinite;
        }
      }

      .button-active,
      .button-cvv:focus,
      .button:focus,
      .address-button:focus {
        box-sizing: border-box;
        border: 4px solid #4c6f94;
      }

      .button-error {
        box-sizing: border-box;
        border: 4px solid #c1272d;
      }

      .address-button {
        height: 72px;
        width: 348px;
        border-radius: 6px;
        background-color: #212224;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .address-button-contents {
          height: 32px;
          color: #7f8086;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 32px;
        }

        .chevron-down-img {
          height: 24px;
          width: 48px;
        }
      }

      .address-button-input {
        height: 72px;
        width: 348px;
      }
    }

    .margin-card-data-sub {
      margin-bottom: 90px;
    }

    .confirm-cvv-title {
      color: #ffffff;
      font-family: Roboto;
      font-size: 40px;
      letter-spacing: 0;
      line-height: 47px;
      align-self: flex-start;
      margin-left: 90px;
    }

    .confirm-cvv-container {
      display: flex;
      position: relative;
      top: 100px;
      width: 700px;
      justify-content: space-between;

      .confirm-cvv-text {
        color: #fff;
        font-family: Roboto;
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 100% */
        letter-spacing: -0.51px;
        width: 356px;
        flex-shrink: 0;
      }

      .button-cvv {
        height: 72px;
        width: 224px;
        border-radius: 6px;
        background-color: #212224;
        display: flex;
        justify-content: center;
        align-items: center;

        &:focus {
          box-sizing: border-box;
          border: 4px solid #4c6f94;
        }

        .button-input-cvv {
          height: 61px;
          width: 184px;
          background-color: #212224;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 32px;
          margin-left: 20px;
          border: none;
        }

        .checkout-cursor {
          position: absolute;
          right: 190px;
          top: 12px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 36px;
          font-weight: bold;
          letter-spacing: 0;
          line-height: 40px;
          animation: blink 2s step-end infinite;
        }
      }

      .button-pincode {
        height: 72px;
        width: 224px;
        border-radius: 6px;
        background-color: #212224;

        .button-input-cvv {
          height: 61px;
          width: 184px;
          background-color: #212224;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          letter-spacing: -0.51px;
          line-height: 32px;
          margin-left: 30px;
          border: none;
        }

        .checkout-cursor {
          position: absolute;
          right: 320px;
          top: 12px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 36px;
          font-weight: bold;
          letter-spacing: 0;
          line-height: 40px;
          animation: blink 2s step-end infinite;
        }
      }
    }
    .disclaimer-wrapper {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 700px;
      position: relative;
      top: 270px;

      .disclaimer-text {
        color: #eee;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.48px;
      }
    }

    .cvv-secure-wrapper {
      display: flex;
      position: relative;
      justify-content: flex-start;
      align-items: center;
      top: 25px;
      width: 700px;

      .cvv-secure-img {
        display: block;
        height: 32px;
        width: 32px;
      }

      .cvv-secure-text {
        color: #eee;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0.48px;
        width: 233px;
        flex-shrink: 0;
        margin-left: 10px;
      }
    }

    .confirm-cvv-card-details {
      display: flex;
      flex-direction: row;
      width: 700px;
      justify-content: flex-start;
      margin-top: 70px;

      .confirm-cvv-card-image {
        width: 81px;
        height: 49.33px;
      }

      .confirm-cvv-card-number {
        color: #fff;
        font-family: Roboto;
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px; /* 100% */
        letter-spacing: -0.51px;
        margin-left: 50px;
      }
    }
  }
}
