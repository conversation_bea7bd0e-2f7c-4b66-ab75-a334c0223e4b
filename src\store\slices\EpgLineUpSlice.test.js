import epgLineupSlice, {getEpgLineup, getEpgLineupSuccess, getEpgLineupError } from './EpgLineUpSlice'

describe('epgLineupSlice reducer',
() => {
const initialState = {
    epgLineup: [],
    isLoading: false,
    error: {},
};

it('should handel initial state', 
() => {
    expect(epgLineupSlice(undefined,
        {})).toEqual(initialState);
    });

    it('should handle getEpgLineup', () => {
        const nextState = epgLineupSlice(initialState, getEpgLineup());
        expect(nextState).toEqual({
            epgLineup: [],
          isLoading: true,
          error: {}
        });
      });

it('should handle getEpgLineupSuccess', () => {
    const response =  [
            {
                "name": "TVREG,TVGRP",
                "offerid": "14328764",
                "purchaseid": "",
                "play": "1",
                "npvrstorage": "0",
                "timeshift": "0",
                "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9",
                "groups": "910420,815969,810713",
                "key": "",
                "paymentmethod": null
            },
        ]
    
    const action = {
      payload: { response }
    };
    const nextState = epgLineupSlice(initialState, getEpgLineupSuccess(action));
    expect(nextState).toEqual({
      epgLineup: response.channels,
      isLoading: false,
      error: {}
    });
  });
  

  it('should handle getEpgChannelError', () => {
    const error = { message: 'Error' };
  
    const nextState = epgLineupSlice(initialState, getEpgLineupError(error));
    expect(nextState).toStrictEqual({
      epgLineup: [],
      isLoading: false,
      error
    });
  });

});