.network-service-error-div {
	width: 1920px;
	height: 1080px;
	position: fixed;
	z-index: 10;
	background: #121212;

	.claro-video-logo {
		margin-top: 40px;
		margin-left: 88px;
	}

	.back-indicator-button-pin {
		display: inline-block;
		color: #ffffff;
		width: 290px;
		height: 48px;
		border-radius: 6.6px;
		font-size: 16px;
		background-color: #2e303d;
		vertical-align: middle;
		float: right;
		margin-top: 25px;
		margin-right: 40px;


		.yellow-indicator-button {
			width: 20px;
			height: 20px;
			padding: 0px 24px 0px 24px;
			vertical-align: middle;
		}

		.back-indicator-image {
			width: 35px;
			height: 28px;
			padding: 0px 24px 0px 0px;
			vertical-align: middle;
		}

		span {
			display: inline-block;
			vertical-align: middle;
			font-family: Roboto;
			font-weight: bold;
			font-size: 29px;
			color: #ffffff;
			width: 146px;
			height: 34px;
		}
	}

	.back-indicator-button-pin:focus {
		background-color: #981c15;
	}

	.network-error-screen-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 100px;
	}

	.alert-img {
		width: 138px;
		height: 121px;
	}

	.no-internet-text,
	.network-error-msg {
		margin: 0;
		width: 1120px;
		text-align: center;
		font-family: 'Roboto';
		letter-spacing: 0;
	}

	.no-internet-text {
		color: #fff;
		font-size: 48px;
		font-weight: bold;
		line-height: 1.19;
		margin-top: 50px;
	}

	.network-error-msg {
		width: 900px;
		color: #eee;
		font-size: 40px;
		line-height: 1.2;
		margin-top: 50px;
	}

	.network-error-btn-container {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 580.26px;
		height: 82.08px;
		border-radius: 10.03px;
		background-color: #2e303d;
		margin-top: 50px;
		color: #fff;
		font-family: 'Roboto';
		font-size: 36.5px;
		font-weight: bold;
		letter-spacing: -0.58px;
		line-height: 1.16;
		text-transform: uppercase;
	}

	.network-error-btn-container:focus {
		background-color: #981c15
	}
}