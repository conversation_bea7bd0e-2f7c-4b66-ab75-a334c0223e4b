import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import './SubscriptionManagements.scss';
import { getCancelSubscription } from "../../../store/slices/settingsSlice";
import { useNavigate } from "react-router-dom";

const CancelSubscription = () => {

	const dispatch = useDispatch();
	const navigate = useNavigate();

	const userDetails = useSelector((state) => state?.login?.isLoggedIn?.response);
	const cancelData = useSelector((state) => state?.settingsReducer?.cancelSubscribeViewData)
	const apaAssetsImages = useSelector((state) => state?.Images?.imageresponse)
	const apaMeta = useSelector((state) => state?.initialReducer?.appMetaData)
	const CancelSubscriptionStatus = useSelector((state) => state?.settingsReducer?.cancelSubscription)

	const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)
	const region = localStorage.getItem('region')

	const handleLogoChange = (data) => {
		const logo = data?.toLowerCase();
		return apaAssetsImages["transactional_"+logo+"_logo"]; //checking the data is there in apa/assets

	}

	const handleConfirmCancel = () => {
		dispatch(getCancelSubscription({'userId':userDetails?.parent_id,'hks': userDetails?.session_stringvalue,'purchaseId':cancelData?.purchase_data?.purchase_id}));
	}

	const getScopeDescription = (data) => {
		return translations?.language?.[region]?.[data+'_scope_description_label' ] 
	}

	const getDescriptionLabel = (data) => {
		return translations?.language?.[region]?.[data+'_disclaimer_description_label' ] 
	}

	const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

	const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
     navigate('/my-settings/my-subscriptions/active-subscriptions')
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
			navigate('/my-settings/my-subscriptions/active-subscriptions')
		}
  }

	useEffect(() => {
		if(CancelSubscriptionStatus?.status === '0'){
			navigate('/my-settings/my-subscriptions/cancel-subscription/success')
		}
		else if(CancelSubscriptionStatus?.status === '1'){
			navigate('/my-settings/my-subscriptions/cancel-subscription/error')
		}
	},[CancelSubscriptionStatus])

	
  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])


  return (
    <div className="appCss">
      <div>
        <img
          src={'images/Logos_Claro_Video.svg'}
          className="claroLogo"
          alt="logo"
        />
        <div className="backIndicator">
          <img className="yellowIndicator" src={'images/yellow_shortcut.png'} />
          <img className="backImage" src={'images/back_button.png'} />
          <p className="backText">
            {translations?.language?.[region]?.atv_back_notification}
          </p>
        </div>
      </div>

      <div className="cancelSubDiv">
        <p className="cancelTitle">
          ¿Estás seguro que deseas cancelar tu suscripción?
        </p>
        <img
          src={handleLogoChange(cancelData?.producttype)}
          className="cancelLogoCss"
          alt="logo"
        />
        <div style={{ marginTop: '47px' }}>
          <span className="cancelPriceCss">
            {cancelData?.currency}
            {cancelData?.price}
          </span>
          <span className="message">/ mes</span>
        </div>
        <p className="includesInfo">
          {getScopeDescription(cancelData?.producttype)}
        </p>
        <p className="disclamerInfo">
          {getDescriptionLabel(cancelData?.producttype)}
        </p>
        <button
          className="confirmCancelButton focusable"
          id="cancel-button"
          onClick={handleConfirmCancel}
        >
          {
            translations?.language?.[region]
              ?.claropagosgate_confirmCvv_option_button_next
          }
        </button>
      </div>
    </div>
  )
};

export default CancelSubscription;