import React, { useState } from 'react'
import getVodSeries, {
  vodSeries as VOD,
  vodSeriesCast,
  vodSeriesMLT
} from '../../apiSaga/vodSeriesSaga'
import { takeEvery } from 'redux-saga/effects'
import watchList<PERSON>aga, {
  getWatchList,
  addWatchList as addWatch<PERSON>ist<PERSON>pi,
  delWatchList
} from '../../apiSaga/watchListSaga'
import VodSeries from './VodSeries'
import {
  fireEvent,
  getByTestId,
  getByText,
  render,
  waitFor
} from '@testing-library/react'
import Redux, { Provider, useDispatch, useSelector } from 'react-redux'
import { runSaga } from 'redux-saga'
import configureStore from 'redux-mock-store'
import { createBrowserHistory } from 'history'
import { MemoryRouter } from 'react-router-dom'
import { store } from '../../store/sagaStore'
import * as redux from 'react-redux'

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn()
  // useSelector:jest.fn()
}))

const initialState = {}
const mockStore = configureStore([])
const history = createBrowserHistory()

const mockedData = [
  {
    id: '108438',
    title: 'Dragon Ball',
    title_original: 'Dragon Ball',
    description: 'anime series',
    seasons_count: '1',
    episodes_count: '83',
    last_season: '1',
    image_large:
      'https://clarovideocdn4.clarovideo.net/SERIES/DRAGONBALL-01-01-00/EXPORTACION_WEB/SS/DRAGONBALL-01-01-00WHORIZONTAL.jpg?size=675x380',
    is_series: true,
    channel_number: null,
    season_number: 1,
    status: '0',
    msg: 'OK'
  }
]

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <MemoryRouter
      history={history}
      initialEntries={[{ pathname: '/series', state: { data: mockedData[0] } }]}
    >
      {children}
    </MemoryRouter>
  </Provider>
)

const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}

describe('VodSeries', () => {
  global.SpatialNavigation = {
    focus: jest.fn(),
    init: jest.fn(),
    add: jest.fn(),
    makeFocusable: jest.fn()
  };
  let spyOnUseSelector
  beforeEach(() => {
    const spy = jest.spyOn(store, 'dispatch')
    const mockUseDispatch = jest.fn()
    const mockDispatch = jest.fn()
    const mockUseSelector = jest.fn()
    const mockSelector = jest.fn()

    jest.mock('react-redux', () => ({
      useDispatch: () => mockUseDispatch.mockReturnValue(mockDispatch),
      useSelector: () => mockUseSelector.mockReturnValue(mockSelector)
    }))

    // Mock useSelector hook
    spyOnUseSelector = jest.spyOn(redux, 'useSelector')
  })

  afterEach(() => {
    useSelector.mockClear()
  })

  it('renders V-card metadata while landing on VOD series screen', async () => {
    const dispatch = jest.fn()
    useDispatch.mockReturnValue(jest.fn())
    const wrapper = renderWithState(<VodSeries />, store)
    const genObject = getVodSeries()

    expect(genObject.next().value).toEqual(
      takeEvery('vodSeries/vodSeries', VOD)
    )
    expect(genObject.next().value).toEqual(
      takeEvery('vodSeries/vodSeriesCast', vodSeriesCast)
    )
    expect(genObject.next().value).toEqual(
      takeEvery('vodSeries/vodSeriesMLT', vodSeriesMLT)
    )
  })

  it('renders V-card watchlist data while landing on VOD series screen', async () => {
    const genObject = watchListSaga()

    expect(genObject.next().value).toEqual(
      takeEvery('watchList/getWatchList', getWatchList)
    )
  })

  // it('When user clicks AddToWatchlist button need to trigger the Watchlist API', async () => {
  //   const wrapper = renderWithState(<VodSeries />)
  //   const dispatchedActions = []
  //   const mockData = [
  //     {
  //       status: '200',
  //       msg: 'OK',
  //       favorited: { id: '12345678' }
  //     }
  //   ]
  //   const payload = { id: '932134', userId: '74402202' }
  //   fireEvent(
  //     getByText(wrapper.container, 'watchlist'),
  //     new MouseEvent('click', {
  //       bubbles: true,
  //       cancelable: true
  //     })
  //   )
  //   fireEvent(
  //     getByText(wrapper.container, 'watchlist'),
  //     new FocusEvent('focusin', {
  //       bubbles: true,
  //       cancelable: true
  //     })
  //   )
  //   const fakeStore = {
  //     getState: () => ({ addWatchList: mockData }),
  //     dispatch: action => dispatchedActions.push(action)
  //   }
  //   await runSaga(fakeStore, addWatchListApi).done

  //   const gen = watchListSaga(payload)

  //   gen.next()
  //   expect(gen.next(true).value).toEqual(
  //     takeEvery('watchList/addWatchList', addWatchListApi)
  //   )
  //   renderWithState(<VodSeries />, store)
  // })

  //   it('When user clicks RemoveFromWatchlist button need to trigger the Watchlist API', async () => {
  //   useSelector
  //     .mockImplementationOnce(() => mockedData)
  //     .mockImplementation(() => null) // ensures that doesn't affect other useSelectors
  //   await waitFor(async () => {
  //     const dispatchedActions = []
  //     const mockData = [
  //       {
  //         status: '200',
  //         msg: 'OK',
  //         favorited: { id: '12345678' }
  //       }
  //     ]
  //     const payload = { id: '932134', userId: '74402202' }
  //     const wrapper = renderWithState(<VodSeries />)
  //     fireEvent(
  //       getByTestId(wrapper.container, 'delWatchListTest'),
  //       new MouseEvent('click', {
  //         bubbles: true,
  //         cancelable: true
  //       })
  //     )
  //     fireEvent(
  //       getByTestId(wrapper.container, 'delWatchListTest'),
  //       new FocusEvent('focusin', {
  //         bubbles: true,
  //         cancelable: true
  //       })
  //     )
  //     const fakeStore = {
  //       getState: () => ({ delWatchList: mockData }),
  //       dispatch: action => dispatchedActions.push(action)
  //     }
  //     await runSaga(fakeStore, delWatchList).done
  //     const gen = watchListSaga(payload)

  //     gen.next()
  //     gen.next()
  //     expect(gen.next(true).value).toEqual(
  //       takeEvery('watchList/delWatchList', delWatchList)
  //     )
  //     renderWithState(<VodSeries />, store)
  //   })
  // })

  // it('when user clicks Trailer button it should trigger handleClick function', () => {
  //   const wrapper = renderWithState(<VodSeries />)
  //   fireEvent(
  //     getByTestId(wrapper.container, 'seriestrailerbutton'),
  //     new MouseEvent('click', {
  //       bubbles: true,
  //       cancelable: true
  //     })
  //   )
  //   fireEvent(
  //     getByTestId(wrapper.container, 'seriestrailerbutton'),
  //     new FocusEvent('focusin', {
  //       bubbles: true,
  //       cancelable: true
  //     })
  //   )
  //   renderWithState(<VodSeries />)
  // })

  it('mock getwatchlist useSelector value', () => {
    spyOnUseSelector.mockReturnValue(mockedData)

    renderWithState(<VodSeries />, store)
  })

  it('initially renders and focus on season 1', async () => {
    const mockSeasonData = {
      seasons_count: 1,
      seasons: [
        {
          id: '13206',
          title: 'Dragon Ball - Temporada 01',
          episodes_count: '83',
          first_episode: '1061677',
          image_small:
            'https://clarovideocdn4.clarovideo.net/SERIES/DRAGONBALL-01-01-00/EXPORTACION_WEB/SS/DRAGONBALL-01-01-00WHORIZONTAL.jpg?size=290x163',
          episodes: [
            {
              id: '1061677',
              title: 'Dragon Ball',
              title_episode: 'El secreto de la esfera del Dragón',
              title_uri: 'Dragon-Ball',
              title_original: 'Dragon Ball',
              description:
                'La tarde de pesca de Gokú es interrumpida por una chica mandona.',
              description_large:
                'La tarde de pesca de Gokú es interrumpida por una chica mandona que está decidida a hacerse con su posesión más preciada: una bola misteriosa que perteneció a su abuelo.',
              short_description: null,
              image_still:
                'https://clarovideocdn6.clarovideo.net/PELICULAS/DRAGONBALL-01-01-01/EXPORTACION_WEB/STILLS/DRAGONBALL-01-01-01-STILL-01.jpg',
              duration: '00:22:29',
              date: '20221124155616',
              year: '1986',
              season_number: '1',
              episode_number: '1',
              rating_code: 'G',
              is_series: true
            }
          ]
        }
      ]
    }
    useSelector
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => mockSeasonData)
      .mockImplementation(() => null)

    waitFor(async () => {
      const wrapper = renderWithState(<VodSeries />)
      fireEvent(
        getByTestId(wrapper.container, 'seasonTest'),
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      )
      fireEvent(
        getByTestId(wrapper.container, 'seasonTest'),
        new FocusEvent('focusin', {
          bubbles: true,
          cancelable: true
        })
      )
      renderWithState(<VodSeries />)
    })
  })

  it('renders cast data', async () => {
    const mockData = {
      external: {
        gracenote: {
          cast: { talents: [{ role_id: 1, role_name: 'actor' }] },
          genres: [{ name: 'drama' }, { name: 'action' }]
        }
      },
      common: {
        extendedcommon: {
          roles: {
            role: [
              {
                id: '13617516',
                name: 'Actor',
                desc: 'Actor',
                talents: {
                  talent: [
                    {
                      id: '170734534',
                      name: 'Abigail',
                      surname: 'Lawrie',
                      fullname: 'Lawrie, Abigail'
                    }
                  ]
                }
              },
              {
                id: '13617517',
                name: 'Director',
                desc: 'Director',
                talents: {
                  talent: [
                    {
                      id: '138708367',
                      name: 'Hans',
                      surname: 'Herbots',
                      fullname: 'Herbots, Hans'
                    }
                  ]
                }
              }
            ]
          }
        }
      }
    }
    useSelector
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => mockData)
      .mockImplementation(() => null)

    waitFor(async () => {
      const wrapper = renderWithState(<VodSeries />)
      fireEvent(
        getByTestId(wrapper.container, 'castTest'),
        new FocusEvent('focusin', {
          bubbles: true,
          cancelable: true
        })
      )
      renderWithState(<VodSeries />)
    })
  })

  it('renders cast data', async () => {
    useSelector
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => null)
      .mockImplementationOnce(() => mockedData)
      .mockImplementation(() => null)

    waitFor(async () => {
      const wrapper = renderWithState(<VodSeries />)
      fireEvent(
        getByTestId(wrapper.container, 'mltTest'),
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      )
      fireEvent(
        getByTestId(wrapper.container, 'mltTest'),
        new FocusEvent('focusin', {
          bubbles: true,
          cancelable: true
        })
      )
      renderWithState(<VodSeries />)
    })
  })
})
