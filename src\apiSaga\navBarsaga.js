import { call, put, takeEvery } from '@redux-saga/core/effects'
import { URL, COMMON_URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getNavSuccess,
  getNavError,
  getNavKidsSuccess,
  getNavKidsError,
  getNavSuccessallNodes
} from '../store/slices/HomeSlice'

function* fetchNavbarDataSaga({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.NAV_URL + '&region=' + region,
      {
        method: 'GET'
        // body: payload,
      },
      {
        onSuccess(response) {
          var navbarAndSubmenu = []

          response?.response?.nodes.map(node => {
            let submenu = {}
            let nodeValue = JSON.parse(node?.app_behaviour)
            let showNode = nodeValue?.node_config?.show_node
            if (showNode) {
              submenu['page'] = node?.text
              submenu['code'] = node?.code
              if (node?.childs) {
                submenu['menu'] = node?.childs
              }
              navbarAndSubmenu.push(submenu)
            }
          })
          store.dispatch(getNavSuccessallNodes(response)) //for home page premium cards submenus we need all nodes to compare
          store.dispatch(getNavSuccess(navbarAndSubmenu))
        },
        onError(error) {
          store.dispatch(getNavError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* fetchNavbarKidsSaga({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.NAV_URL + '&region=' + region + '&type=kids',
      {
        method: 'GET'
        // body: payload,
      },
      {
        onSuccess(response) {
          var navbarAndSubmenu = []

          response?.response?.nodes.map(node => {
            let submenu = {}
            let nodeValue = JSON.parse(node?.app_behaviour)
            let showNode = nodeValue?.node_config?.show_node
            if (showNode) {
              submenu['page'] = node?.text
              submenu['code'] = node?.code
              navbarAndSubmenu.push(submenu)
            }
          })
          // nav-bar kids saga
          store.dispatch(getNavKidsSuccess(navbarAndSubmenu))
        },
        onError(error) {
          store.dispatch(getNavKidsError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* navBarSaga() {
  yield takeEvery('homeSlice/getNavData', fetchNavbarDataSaga)
  yield takeEvery('homeSlice/getNavDataKids', fetchNavbarKidsSaga)
}
