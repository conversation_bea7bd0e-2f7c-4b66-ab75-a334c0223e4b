import React from "react";
import { render, screen, fireEvent, act, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import SecurityPin from "./SecurityPin";
import '@testing-library/jest-dom';

const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: () => ({
    state: { pageName: 'Transaction' }
  }),
  useNavigate: () => mockNavigate
}));

const localStorageMock = {
  getItem: jest.fn(() => 'US'),
  setItem: jest.fn(),
  clear: jest.fn()
};
global.localStorage = localStorageMock;

const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch
}));

const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
);

describe('SecurityPin component tests', () => {
  let initialState;
  let store;

  beforeEach(() => {
    jest.useFakeTimers();
    mockNavigate.mockReset();
    mockDispatch.mockReset();

    initialState = {
      settingsReducer: {
        controlPin: {
          response: {
            profiles: [
              {
                parental: {
                  active: false
                },
                purchase: {
                  active: false
                }
              }
            ]
          }
        },
        disableControlPin: {
          response: {
            profiles: [
              {
                parental: {
                  active: null
                }
              }
            ]
          }
        },
        remindControlPin: {
          response: {
            email_sent: false
          }
        }
      },
      initialReducer: {
        appMetaData: {
          translations: JSON.stringify({
            language: {
              US: {
                "setupPin_confirmation_title_label": "Create PIN",
                "setupPin_confirmation_detail_label": "Please set up a PIN",
                "setupPin_confirmation_option_button_continue​": "Continue",
                "userProfile_password_option_button_cancel": "Cancel",
                "atv_back_notification": "Back"
              }
            }
          })
        }
      }
    };

    store = mockStore(initialState);
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  test('renders the basic component structure', () => {
    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    expect(screen.getByAltText('logo')).toBeInTheDocument();
    expect(screen.getByText('atv_back_notification')).toBeInTheDocument();
  });

  test('shows notification when disableSecurityPin is false', () => {
    const initialStateWithNotification = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        disableControlPin: {
          response: {
            profiles: [
              {
                parental: {
                  active: false
                }
              }
            ]
          }
        }
      }
    };

    store = mockStore(initialStateWithNotification);

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    act(() => {
      jest.runAllTimers();
    });
    expect(mockDispatch).toHaveBeenCalled();
  });

  test('shows notification when remindSecurityPin is true', () => {
    const initialStateWithRemind = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        remindControlPin: {
          response: {
            email_sent: true
          }
        }
      }
    };

    store = mockStore(initialStateWithRemind);

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    act(() => {
      jest.runAllTimers();
    });
    expect(mockDispatch).toHaveBeenCalled();
  });

  test('handles Samsung key press', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn(() => ({ code: 403 }))
      }
    };

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    fireEvent.keyUp(document, { keyCode: 403 });
    expect(mockNavigate).toHaveBeenCalled();

    delete global.tizen;
  });

  test('handles LG key press', () => {
    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    fireEvent.keyUp(document, { keyCode: 461 });
    expect(mockNavigate).toHaveBeenCalled();
  });

  test('handles transaction page with purchase active', () => {
    const transactionState = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        controlPin: {
          response: {
            profiles: [
              {
                parental: {
                  active: true
                },
                purchase: {
                  active: true
                }
              }
            ]
          }
        }
      }
    };

    store = mockStore(transactionState);

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    act(() => {
      jest.runAllTimers();
    });
  });

  test('handles translation when key exists and does not exist', () => {
    const stateWithMixedTranslations = {
      ...initialState,
      initialReducer: {
        appMetaData: {
          translations: JSON.stringify({
            language: {
              US: {
                "atv_back_notification": "Back"
              }
            }
          })
        }
      }
    };

    store = mockStore(stateWithMixedTranslations);

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    expect(screen.getByText('atv_back_notification')).toBeInTheDocument();
  });

  test('handles undefined translations', () => {
    const stateWithoutTranslations = {
      ...initialState,
      initialReducer: {
        appMetaData: {}
      }
    };

    store = mockStore(stateWithoutTranslations);

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    expect(screen.getByAltText('logo')).toBeInTheDocument();
  });

  test('sets title to TransactionAct for transaction page with purchase not active', () => {
    const locationMock = jest.spyOn(require('react-router-dom'), 'useLocation')
      .mockImplementation(() => ({
        state: { pageName: 'Transaction' }
      }));

    render(
      <Wrapper reduxStore={store}>
        <SecurityPin />
      </Wrapper>
    );
    act(() => {
      jest.runAllTimers();
    });
    expect(screen.getByAltText('logo')).toBeInTheDocument();

    locationMock.mockRestore();
  });
});