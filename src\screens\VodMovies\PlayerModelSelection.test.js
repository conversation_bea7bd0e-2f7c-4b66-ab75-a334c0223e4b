import React from "react";
import { render,fireEvent,queryByAttribute } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router, useLocation } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import PlayerModelSelection from "./PlayerModelSelection";


const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
jest.mock("react-router-dom", () => ({
    ...jest.requireActual("react-router-dom"),
    useLocation: () => ({
      state: {
        page:"series"
      }
    })
  }));

describe('landing page test', () => {
    test('When user press the back space it should go the vCard screen', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 8,
                charCode: 8
          });
    })

    test('When user press the back space it should go the vCard screen', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
    })
    test('When user press the back space it should go the vCard screen2', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection keyParam="search"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 8,
                charCode: 8
          });
          fireEvent.click(wrapper.container.querySelector("#resume"))
          fireEvent.focusIn(wrapper.container.querySelector("#resume"))
          fireEvent.click(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#regresser"))
          fireEvent.click(wrapper.container.querySelector("#regresser"))
    })


    test('When user press the back space it should go the vCard screen2', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection keyParam="series"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 8,
                charCode: 8
          });
          fireEvent.click(wrapper.container.querySelector("#resume"))
          fireEvent.focusIn(wrapper.container.querySelector("#resume"))
          fireEvent.click(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#regresser"))
          fireEvent.click(wrapper.container.querySelector("#regresser"))
    })

    test('When user press the back space it should go the vCard screen2', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection keyParam="movies"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
          fireEvent.click(wrapper.container.querySelector("#resume"))
          fireEvent.focusIn(wrapper.container.querySelector("#resume"))
          fireEvent.click(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#regresser"))
          fireEvent.click(wrapper.container.querySelector("#regresser"))
    })

    test('When user press the back space it should go the vCard screen2', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection keyParam="search"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
          fireEvent.click(wrapper.container.querySelector("#resume"))
          fireEvent.focusIn(wrapper.container.querySelector("#resume"))
          fireEvent.click(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#regresser"))
          fireEvent.click(wrapper.container.querySelector("#regresser"))
    })
    test('it should render the landing Page 2 3', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection 
            keyParam="series"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 8,
                charCode: 8
          });
          fireEvent.click(wrapper.container.querySelector("#resume"))
          fireEvent.focusIn(wrapper.container.querySelector("#resume"))
          fireEvent.click(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#regresser"))
          fireEvent.click(wrapper.container.querySelector("#regresser"))
    })

    test('it should render the landing Page 2 3', async () => {
        const wrapper = await renderWithState(<PlayerModelSelection 
            keyParam="series"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
          fireEvent.click(wrapper.container.querySelector("#resume"))
          fireEvent.focusIn(wrapper.container.querySelector("#resume"))
          fireEvent.click(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#continue"))
          fireEvent.focusIn(wrapper.container.querySelector("#regresser"))
          fireEvent.click(wrapper.container.querySelector("#regresser"))
    })
    test('it should render the landing Page', async () => {
        window.tizen = {
            tvinputdevice:{
                getKey:()=>{
                    return {
                        code : "#ff0"
                    }
                },
                registerKeyBatch:()=>{
                    
                }
            }
        }
        const wrapper = await renderWithState(<PlayerModelSelection keyParam="search" 
            setSearchValue={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
    })
    test('it should render the landing Page 3', async () => {
        window.tizen = {
            tvinputdevice:{
                getKey:()=>{
                    return {
                        code : "#ff0"
                    }
                },
                registerKeyBatch:()=>{
                    
                }
            }
        }
        const wrapper = await renderWithState(<PlayerModelSelection keyParam="movies" 
            setSearchValue={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
    })
    
})