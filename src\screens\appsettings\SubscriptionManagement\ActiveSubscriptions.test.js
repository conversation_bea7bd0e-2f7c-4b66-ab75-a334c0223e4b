import React from "react";
import { fireEvent, getByText, queryByAttribute,render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import ActiveSubscriptions from "./ActiveSubscriptions";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mockerrorresponse = {
    "status": "1"
}
const mocksuccessresponse = {
    response: [
		{				
				waspurchase:1,
				purchase_data:{
					gateway:"hubgate",
					gateway_text:"TEXT_PGS_PAYMENT_PERU_GATEWAY_HUBGATE",
					gateway_data:"",
					beggining:"25/12/2023 08:49:48",
					expiration:"25/01/2024 08:49:47",
					expiration_promo:"25/01/2024 08:49:47",
					in_process_canceled:0,
					purchase_id:"**********"
				},
				offer_id:"14328333",
				user_id:********,
				price:"0.00",
				currency:"S/",
				offer_text:"TEXT_PGS_OFFER_TELMEXMEXICO_SUBSCRIPTION_SVOD_30D",
				producttype:"CV_MENSUAL",
				key:"Telmexmexico_Subscription_SVOD_30d"
			},
			
		]
}

const mockIsLoggedinsuccessresponse = {
	response:{
		accepted_terms: 1,
		admin: true,
		city: null,
		counterValidEmail: 0,
		country_code: "PE",
		email: "<EMAIL>",
		firstname: "Admin",
		gamification_id: "63ecf870de7e873ec21ca6c3",
		hasSavedPayway: 1,
		hasUserSusc: 1,
		is_kids: "false",
		is_user_logged_in: 1,
		lastname: "testing",
		lasttouch: {favorited: "657fe455ec4fd", profile: "657fe44c2a6be", purchased: "657fd244a1fab"},
		parent_id: "********",
		password_recovered: false,
		paymentMethods: {hubgate: true},
		region: "peru",
		session_parametername: "HKS",
		session_servername: "************",
		session_stringvalue: "ZTEATV412001224226580292a69e33",
		session_userhash: "************************************************************************************************",
		socialNetworks: [{id_usuario: "********", id_usuario_social: "63ecf870de7e873ec21ca6c3", redsocial: "IMUSICA"}],
		socialdata: null,
		subregion:null,
		subscriptions: {TV_EN_VIVO: true, MGM: true, AMCO: true},
		superhighlight: ["no_suscripto_nbatv", "no_suscripto_rtveplay", "susc", "no_suscripto_fox_sports", "no_suscripto_hbo"],
		user_id: "********",
		user_session: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I9OXS-jJomO5m_USqjIlt8XGd9Zzyypkdh2zFgZ9Jeg",
		user_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.85CTxu2wUyWObpGgpBiIEcGWwdGeGonriZotenfEtOk",
		username: "<EMAIL>",
		validEmail: true
	},
	status: "0"
}

const mockImageSuccessResponse ={
	AMCO_est_brasil_wp0:"https://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_brasil_wp0.png?**********",
	AMCO_est_chile_wp0:"https://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_chile_wp0.png?**********",
	AMCO_est_mexico_wp0:"https://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_mexico_wp0.png?**********",
	AMCO_est_peru_wp0:"https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_peru_wp0.png?**********",
	AMCO_ppe_argentina_wp0:"https://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_ppe_argentina_wp0.png?**********",
	AMCO_ppe_brasil_wp0:"https://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_ppe_brasil_wp0.png?1574457945",
	AMCO_ppe_chile_wp0:"https://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_ppe_chile_wp0.png?1574457945",
}


describe('Landing page test', () => {
    test('should render without api mock data', () => {
        initialState.settingsReducer = {
					activeSubscriptions: mockerrorresponse
        }
				initialState.login = {
					isLoggedIn: mockerrorresponse
        }
				initialState.Images = {
					imageresponse: mockerrorresponse
        }

        renderWithState(<ActiveSubscriptions />)
    })

    test('should render api mock data', () => {
		initialState.settingsReducer = {
			cancelSubscribeViewData: mocksuccessresponse,
			cancelSubscription:{
				status:'0'
			}
}
		initialState.login = {
			isLoggedIn: mockIsLoggedinsuccessresponse
}
		initialState.Images = {
			imageresponse: mockImageSuccessResponse
}
        renderWithState(<ActiveSubscriptions />)
    })
})