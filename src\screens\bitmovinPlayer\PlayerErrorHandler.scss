.live-tv-error {
  margin-top: 90px;
  width: 1920px;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-family: Roboto;
  .alert-icon {
    width: 113px;
    height: 100px;
  }

  .msg-1 {
    font-weight: bold;
    font-size: 48px;
    margin: 98px 0px 93px 0px;
  }

  .msg-2 {
    font-size: 32px;
    margin: 10px 0px;
  }
}

.player-err-screen {
  width: 1920px;
  color: #ffffff;
  font-family: Roboto;

  .channel-icon-div {
    display: flex;
    flex-direction: row;
    justify-content:flex-end;
    align-items: center;
    padding: 35px;
  }

  .channel-error-div {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-top: 80px;

    .live-msg-1 {
      font-weight: bold;
      font-size: 48px;
      margin: 65px 0px 70px 0px;
    }

    .live-msg-2 {
      font-size: 32px;
      margin: 10px 0px;
    }
  }

  .channel-icon {
    width: 170px;
    height: 90px;
  }

  .alert-icon {
    width: 113px;
    height: 100px;
  }
  .lock-icon {
    width: 104px;
    height: 104px;
  }
}

.player-error {
  display: flex;
  flex-direction: column;
  .player-error-back-button {
    display: flex;
    height: 48px;
    width: 292px;
    border-radius: 6.6px;
    background-color: #2e303d;
    align-items: center;
    margin: 47px 64px 0px auto;

    .yellow-indicator {
      height: 20px;
      width: 20px;
      margin-left: 24px;
      margin-right: 24px;
    }

    .back-image {
      height: 24px;
      width: 30px;
      margin-right: 24px;
    }

    .back-text {
      height: 30px;
      width: 146px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 29.04px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 29.04px;
      margin: 0;
    }

    &:focus {
      background-color: #de1717;
    }
  }
  .bottom-btn-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    margin-top: 129px;
    .retry-btn {
      height: 82px;
      width: 580px;
      border-radius: 10px;
      background-color: #981c15;
      &:focus {
        border: 3px solid #ffffff;
      }
    }
    .cancel-btn {
      height: 72px;
      width: 509px;
      border-radius: 8px;
      background-color: #2e303d;
      &:focus {
        border: 3px solid #ffffff;
      }
    }
    .bottom-btn-text {
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 40px;
      text-align: center;
      margin: 0;
    }
  }
}
