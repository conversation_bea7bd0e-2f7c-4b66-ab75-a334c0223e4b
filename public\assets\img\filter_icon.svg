<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="44" height="44" viewBox="0 0 44 44">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="0.5" x2="0.793" y2="0.868" gradientUnits="objectBoundingBox">
      <stop offset="0"/>
      <stop offset="1" stop-color="#303234"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="0.5" x2="0.207" y2="0.132" gradientUnits="objectBoundingBox">
      <stop offset="0"/>
      <stop offset="1" stop-color="#3b3b3b"/>
    </linearGradient>
  </defs>
  <g id="Group_28716" data-name="Group 28716" transform="translate(-1556 -1022)">
    <g id="Component_81_133" data-name="Component 81 – 133" transform="translate(1556 1022)">
      <circle id="Ellipse_295" data-name="Ellipse 295" cx="22" cy="22" r="22" fill="url(#linear-gradient)"/>
      <g id="Ellipse_294" data-name="Ellipse 294" transform="translate(2 2)" stroke="#000" stroke-width="1" fill="url(#linear-gradient-2)">
        <circle cx="20" cy="20" r="20" stroke="none"/>
        <circle cx="20" cy="20" r="19.5" fill="none"/>
      </g>
    </g>
    <circle id="Ellipse_311" data-name="Ellipse 311" cx="3" cy="3" r="3" transform="translate(1565 1041)" fill="#e1251b"/>
    <circle id="Ellipse_312" data-name="Ellipse 312" cx="3" cy="3" r="3" transform="translate(1572 1041)" fill="#17a402"/>
    <circle id="Ellipse_313" data-name="Ellipse 313" cx="3" cy="3" r="3" transform="translate(1579 1041)" fill="#e9e900"/>
    <circle id="Ellipse_314" data-name="Ellipse 314" cx="3" cy="3" r="3" transform="translate(1586 1041)" fill="#00c2e9"/>
  </g>
</svg>
