import React, { useEffect, useState, useCallback } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { setShowApiFailureModal } from '../../store/slices/SearchSlice'
import { useDispatch, useSelector } from 'react-redux'
import { pushScreenViewEvent } from '../../GoogleAnalytics'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const ErrorEventModule = props => {
  const [currentButtonFocus, setCurrentButtonFocus] = useState('accept')
  const { state } = useLocation()
  const dispatch = useDispatch()

  const { searchData, pageName } = state
  const navigate = useNavigate()
  pageName == 'search' && localStorage.setItem('searchValue', searchData)
  const showReturnFocus = useSelector(state => state?.search?.setReturnFocus)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')
  const apilanguage = translations?.language?.[region]

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(()=>{
  pushScreenViewEvent({screenName:'error_event_module', screenData: userDetails, prevScreenName:state?.pageName})
},[])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      dispatch(setShowApiFailureModal(false))
      props?.pageName == 'search' && props?.setSearchValue(props?.searchValue)
      goToSearch()
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode == 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
) {
      dispatch(setShowApiFailureModal(false))
      props?.pageName == 'search' && props?.setSearchValue(props?.searchValue)
      goToSearch()
    }
  }

  const goToSearch = () => {
    if (
      props?.pageName == 'vodMovies' ||
      (props?.pageName == 'vodSeries' && state?.returnPage !== 'search')
    ) {
      props?.setCurrentButtonFocus(showReturnFocus)
      dispatch(setShowApiFailureModal(false))
    } else if (
      props?.pageName == 'vodMovies' ||
      props?.pageName == 'vodSeries'
    ) {
      props?.setCurrentButtonFocus(showReturnFocus)
      dispatch(setShowApiFailureModal(false))
    } else if (state?.returnPage === 'search') {
      navigate('/search', {
        state: { inputValue: state?.inputValue },
        replace: true
      })
    } else if (props?.pageName == 'epmoreInfo') {
      props?.setCurrentButtonFocus(showReturnFocus)
      dispatch(setShowApiFailureModal(false))
    }
  }

  const handleTranslationchange = useCallback(
    keyname => {
      return apilanguage?.[keyname]
        ? apilanguage?.[keyname]
        : keyname?.slice(0, 13) + '...'
    },
    [apilanguage]
  )

  return (
    <div className="error-boundry-layout">
      <div className="App-logo-left">
        <LazyLoadImage
          className="logo-img"
          alt="logo"
          src={'images/logo.png'}
          placeholderSrc={'images/logo.png'}
        />
      </div>
      <div className="warning-icon-logo">
        <LazyLoadImage
          width={80}
          height={80}
          src={'images/ic_alert.png'}
          placeholderSrc={'images/ic_alert.png'}
        />
      </div>
      <div className="warning-content">
        <div className="estamos-experimentan">
          {handleTranslationchange(
            'Buscador_Error_Titulo_TextoTitulo'
          )}
        </div>
        <div className="por-el-momento-no-pu">
          <div className="talent-message-width">
            {handleTranslationchange(
              'Buscador_Error_TextoComplementario1'
            )}
          </div>
          <div className='talent_msg'>
              {handleTranslationchange(
              'Buscador_Error_TextoComplementario2'
            )}
          </div>
        </div>
        <div className="warning-button-div">
          <>
            <button
              className="warning-title-button focusable"
              onClick={() => goToSearch()}
              autoFocus={currentButtonFocus == 'accept'}
              onFocus={() => setCurrentButtonFocus('accept')}
              id="searchdata"
            >
              <span className="warning-title-button-Contents">
                {handleTranslationchange(
                  'Buscador_Error_Texto_Textoboton'
                )}
              </span>
            </button>
          </>
        </div>
      </div>
    </div>
  )
}

export default ErrorEventModule
