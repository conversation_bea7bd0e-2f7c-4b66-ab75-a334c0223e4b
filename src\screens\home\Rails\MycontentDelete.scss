$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;
$fontSize : 48px;
$grey-color: #2e303d;
$focus-color: #981c15;

.deletecard-mainclass {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .delete-title {
    color: $color-white;
    text-align: center;
    font-size: $fontSize;
    line-height: 52px;
    margin-top: 110px;
    font-family: $font-family-roboto;
  }

  .image-class {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .mycontent-title {
    color: #eeeeee;
    font-size: 34px;
    line-height: 54px;
    font-family: $font-family-roboto;
    text-align: center;
    margin-top: 48px;
    margin-bottom: 101px;
    width: 600px;
  }

  .button-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .cancel-button {
      width: 500px;
      height: 74px;
      color: #eeeeee;
      font: normal normal normal 30px/35px Roboto;
      font-size: 32px;
      text-align: center;
      background: $grey-color;
      opacity: 1;
      border: 3px solid #34353b;
      border-radius: 6.9px;
      margin-bottom: 34px;
    }

    .cancel-button:focus {
      border: 3px solid $focus-color;
      background: $focus-color;
      border-radius: 6.9px;
      transform: scale(1.03);
      width:570px;
    }

    .confirm-button {
      width: 500px;
      height: 74px;
      color: #eeeeee;
      font: normal normal normal 30px/35px Roboto;
      font-size: 32px;
      text-align: center;
      background: #2e303d;
      opacity: 1;
      border: 3px solid #34353b;
      border-radius: 6.9px;
      margin-bottom: 34px;
    }

    .confirm-button:focus {
      border: 3px solid $focus-color;
      background: $focus-color;
      border-radius: 6.9px;
      transform: scale(1.03);
      width:570px;
    }

    .yellowdot {
      margin-left: 20px;
    }
  }

  .backBtn-mycontent {
    display: flex;
    position: absolute;
    flex-wrap: wrap;
    right: 87px;
    top: 47px;
    justify-content: space-between;
    align-content: center;
    width: 292px;
    height: 28px;
    padding: 13px;
    background-color: $grey-color;
    border-radius: 9px;
  }

  .rec-backBtn {
    display: flex;
    position: absolute;
    right: 87px;
    top: 47px;
    justify-content: space-between;
    align-items: center;
    width: 292px;
    height: 28px;
    padding: 13px;
    background-color: #2e303d;
    border-radius: 9px;
  }

  .backBtn-mycontent:focus {
    background: #981c15 0% 0% no-repeat padding-box;
  }

  .backText {
    color: #fff;
    font-size: 29px;
    line-height: 29px;
  }
  .delete-regresser-box {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    top:10px;
    right:40px;

    .filter-backScreen {
        box-sizing: border-box;
        height: 62px;
        width: 306px;
        border-radius: 6px;
        background: #2e303d 0% 0% no-repeat padding-box;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 6px;
        margin-left: 96px;
        margin-right: 53px;

        &:focus,
        &:active {
            transform: scale(1.1);
            outline: 3px solid white;
            border: 5px solid white;
            border-color: #1a1a1a;
        }

        .filter-back-img-icon {
            margin-bottom: 17px;
            margin-left: 28px;
            margin-right: 3px;
            margin-top: 22px;
        }

        .filter-back-button-regresar-title {
            height: 33px;
            width: 208px;
            color: white;
            font-family: Roboto;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 30px;
            text-align: center;
            margin-bottom: 17px;
            margin-right: 3px;
            margin-top: 21px;
        }
    }
}

}