.yellow-indicator_{
    margin-right: 24px;
}
.back-image_{
    margin-right: 24px;
}
.back-text_{
    height: 30px;
    width: 146px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
    margin-left: 15px;
    margin-top: -30px;
    float: right;
    margin-right: -29px;
}
.back-button{
    display: flex;
    height: 3.14vh;
    width: 15.20vw;
    border-radius: 6.6px;
    background-color: #2E303D;
    align-items: center;
    padding-top: 9px;
    padding-bottom: 9px;
    padding-left: 24px;
    padding-right: 24px;
}
.btn-internal{
    width: 11.66vw;
    height: 2.77vh;
}
/*************Subscription Page start***********************/
.btnPromocode{
    width: 40vw;
    font-size: 5vh;
    text-align: center;
    background: rgb(46 48 61);
    color: white;
    margin-top: 3vh;
}
.btnPromocode:focus{
    background: #981C15 0% 0% no-repeat padding-box;
	transform: scale(1.05);
}
   
#subscription-page-back:focus {  
    border: 4px solid #fffff0;
    opacity: 1;
    border-radius: 3px;
    outline: unset;
}

#subscription-page-cancel-button{
    margin-top: 20px;
}

#paymentmobile-page-back:focus {  
    border: 4px solid #fffff0;
    opacity: 1;
    border-radius: 3px;
    outline: unset;
}
#paymentmobile-page-back{
    display: flex;
    height: 48px;
    width: 292px;
    border-radius: 6.6px;
    background-color: #2E303D;
    align-items: center;
    float: right;
    margin-top: 25px;
    margin-right: 64px;
}
#paymentmobile-page-back .back-text{
    height: 30px;
    width: 146px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
    margin-left: 15px;
}
#paymentmobile-page-back .yellow-indicator{
    height: 20px;
    width: 20px;
    margin-left: 24px;
    margin-right: 24px;
}
.textDataArea{
    display: flex;
    width: 200vw;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
    
}
/* .t2{
    width: 40vw;
    font-size: 5vh;
    text-align: center;
    background: rgb(46 48 61);
    color: white;
    margin-top: 3vh;
} */
.promoCodeInput{
    width: 40vw;
    height:7vh;
    background: rgb(46 48 61);
    border:5px solid gray;
    color: white;
    font-size: 5vh;
    text-align: center;   
    margin-bottom: 100px; 
}
.trans-button:focus{	
	background: #981C15 0% 0% no-repeat padding-box;
	transform: scale(1.05);
}
.trans-input:focus{
	transform: scale(1.05);
}
.mobile-input{
    width: 37vw;
    height: 6.6vh;
    background: #000000 0% 0% no-repeat padding-box;
    border:5px solid gray;
    opacity: 1;
    color: white;
    font-size: 5vh;
    text-align: center;
}
.btnMobile{
    width: 37vw;
    height: 6.6vh;
    font-size: 3vh;
    text-align: center;
    background: rgb(46, 48, 61);
    color: white;
    margin-top: 10vh;
  
}
.mobile-otp{
    width: 20vw;
    height: 6.6vh;
    font-size: 3vh;
    background: #000000 0% 0% no-repeat padding-box;
    border:5px solid gray;
    opacity: 1;
    color: white;
    text-align: center;
    margin-right: 1vw;
}
.btnOtp{
    width: 13vw;
    font-size: 3vh;
    height: 6.6vh;
    text-align: center;
    background: rgb(46 48 61);
    color: white;
    margin-left: 1vw;
}
.btnCancelMobileOtp{
    width: 37vw;
    font-size: 3vh;
    height: 6.6vh;
    text-align: center;
    background: rgb(46 48 61);
    color: white;
    margin-top: 3vh;
}
.finishPayment{
    padding-top: 147px;  
    text-align: center;
}
.imageAndText{ 
    display: flex;
    justify-content: center;
}
.logo-tick{
    display: flex;
    flex-direction: row;
}
.onlytext{
    text-align: left;
}
.finishPaymentBtn{
    width: 580px;
    height: 81px;
    font-size: 31px;
    text-align: center;
    background: rgb(46 48 61);
    color: white;
    margin-top: 10vh;
}
.finishPayment .upper-text{
    margin-bottom: 20px;
    font-size: 40px;
}
.onlytext .b1{
    font-size: 32px;
    color: #01a2f3;
}
.onlytext .b2{
    font-size: 28px;
    margin-bottom: 87px;
}
.finishPaymentBtn :focus{
    background: #981c15;
    transform: scale(1.05);
}
.poster-image-final-subs{
    width: 240px;
    height: 369px;
    color: #000;   
}
.logoimgfinal{
    width: 310px;
    height: auto;
}
.bannerimgfinal{
    height: 369px;
    width: auto;
}
.logo-text-final-subs{
    width: 512px;
    height: 488px;
    background:#2E303D;
}
.finishSubscribeLandLineInfo{
	display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
	margin-top: 5%;
}
.iconDiv{
    border-radius: 50px;
    width: 50px;
    background: white;
    color: black;
    font-size: 40px;
    padding: 10px 10px 10px 10px;
    text-align: center;
    margin-bottom: 6vh;
    margin-top: 10vh;
}
.landline-text-desc{
    width: 50vw;
    color: white;
    font-size: 22px;
    line-height: 2;
    text-align: center;
}
#paymentfinalsubslandlineback:focus {  
    border: 4px solid #fffff0;
  opacity: 1;
  border-radius: 3px;
  outline: unset;
}
#paymentfinalsubslandlineback{
    display: flex;
    height: 48px;
    width: 292px;
    border-radius: 6.6px;
    background-color: #2E303D;
    align-items: center;
    float: right;
    margin-top: 25px;
    margin-right: 64px;
}
#paymentfinalsubslandlineback .back-text{
    height: 30px;
    width: 146px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
    margin-left: 15px;
}
#paymentfinalsubslandlineback .yellow-indicator{
    height: 20px;
    width: 20px;
    margin-left: 24px;
    margin-right: 24px;
}
.upper-back-button :focus {  
    border: 4px solid #fffff0;
    opacity: 1;
    border-radius: 3px;
    outline: unset;
}
.upper-back-button {
    display: flex;
    height: 48px;
    width: 292px;
    border-radius: 6.6px;
    background-color: #2E303D;
    align-items: center;
    float: right;
    margin-top: 25px;
    margin-right: 64px;
}
.upper-back-button  .back-text{
    height: 30px;
    width: 146px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
    margin-left: 15px;
}
.upper-back-button  .yellow-indicator{
    height: 20px;
    width: 20px;
    margin-left: 24px;
    margin-right: 24px;
}
/**********subscription page new**********/
.subscriptionPageContainer{
    width: var(--maxWidth);
    height: var(--maxHeight);   
}
.subscriptionPage{
    margin-left: 30%;
    margin-right: 30%;
}
.subscriptionPageContainer .seasonEpisodeInfo{
    font-size: 30px;
    color:#969696;
    text-align: center;
}
.subscriptionPageContainer .assetTitle{
    font-size: 40px;
    color: #FFFFFF; 
    text-align: center;
    margin-bottom: 97px;
}
.subscriptionPageContainer .subscriptionPage .posterImage .banner{
    width: 240px;
    height: auto;
}
.subscriptionPageContainer .imageAndText{ 
    display: flex;
    justify-content: center;
}
.subscriptionPageContainer .posterImage{
    width: 240px;
    height: 360px;
}
.subscriptionPageContainer .smallLogo{
    width: 156px;
    height: 65px;
}
.subscriptionPageContainer .dollarText{
    font-size: 38px;
    color: #FFFFFF; 
}
.subscriptionPageContainer .dollarText .smlltext{
    font-size: 25px;
    color: #FFFFFF; 
}
.subscriptionPageContainer .coloredText{
    font-size: 28px;
    color: #00a9ff;
}
.subscriptionPageContainer .smallDesc{
    font-size: 28px;
    margin-bottom: 87px;
    color: #EEEEEE;
}
.subscriptionPageContainer .longDesc{
    font-size: 28px;
    margin-bottom: 87px;
    color: #858585;
}
.buttons-area{
    display: flex;
    flex-direction: column;
    text-align: center;
    margin-left: 37%;
    margin-right: 37%;
}
.btnSubscribePage{   
    height: 72px;
    width: 504px;
    border-radius: 11.37px;
    background-color: #2E303D;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
}
.btnSubscribePage:focus{
    background: #981C15 0% 0% no-repeat padding-box;
	transform: scale(1.05);
}
#subscriptionDetailPageBack:focus{
    border: 4px solid #fffff0;
    opacity: 1;
    border-radius: 3px;
    outline: unset;
}
.subs-button-view-screen_{
    width: 501px;
    height: 69px;
    background: #2c2929 0% 0% no-repeat padding-box;
    border-radius: 35px;
    opacity: 1;
    text-align: center;
    font: normal normal bold 28px / 48px Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    margin-top: 45px;
}
.subs-button-view-screen_:focus{
    background: #981C15 0% 0% no-repeat padding-box;
	transform: scale(1.05);
    border: 4px solid #fffff0;
    outline: unset;
}
#subscriptionPageBackButton:focus{
    border: 4px solid #fffff0;
    opacity: 1;
    border-radius: 3px;
    outline: unset;
}
.subscriptionPageContainer{
    width: var(--maxWidth);
    height: var(--maxHeight);   
}
.subscriptionPage{
    margin-left: 30%;
    margin-right: 30%;
}
.subscriptionPageContainer .seasonEpisodeInfo{
    font-size: 30px;
    color:#969696;
    text-align: center;
}
.subscriptionPageContainer .assetTitle{
    font-size: 40px;
    color: #FFFFFF; 
    text-align: center;
    margin-bottom: 97px;
}
.subscriptionPageContainer .subscriptionPage .posterImage .banner{
    width: 240px;
    height: auto;
}
.subscriptionPageContainer .imageAndText{ 
    display: flex;
    justify-content: center;
}
.subscriptionPageContainer .posterImage{
    width: 240px;
    height: 360px;
}
.subscriptionPageContainer .smallLogo{
    width: 156px;
    height: 65px;
}
.subscriptionPageContainer .dollarText{
    font-size: 38px;
    color: #FFFFFF; 
}
.subscriptionPageContainer .dollarText .smlltext{
    font-size: 25px;
    color: #FFFFFF; 
}
.subscriptionPageContainer .coloredText{
    font-size: 28px;
    color: #00a9ff;
}
.subscriptionPageContainer .smallDesc{
    font-size: 28px;
    margin-bottom: 87px;
    color: #EEEEEE;
}
.subscriptionPageContainer .longDesc{
    font-size: 28px;
    margin-bottom: 87px;
    color: #858585;
}
.buttons-area{
    display: flex;
    flex-direction: column;
    text-align: center;
    margin-left: 37%;
    margin-right: 37%;
}
.btnSubscribePage{   
    height: 72px;
    width: 504px;
    border-radius: 11.37px;
    background-color: #2E303D;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
}
.btnSubscribePage:focus{
    background: #981C15 0% 0% no-repeat padding-box;
	transform: scale(1.05);
}

.flowRight{
    float: right;
}
.btnAddMobileNumber{
    gap:30px;
    display: flex;
    flex-direction: column;
}
/************subscription page new end**************************/
/*************Subscription Page end**********************/