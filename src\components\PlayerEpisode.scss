$color-white: #fff;
$font-family-roboto: Roboto;
$pos-abs: absolute;
$text-wrap-nowrap: nowrap;
$font-weight-bold: bold;
$box-sizing-border: border-box;
$position-relative: relative;
$text-overflow-ellipsis: ellipsis;
$overflow-hidden: hidden;

.player-Episode-season-container {
  height: 1080px;
  width: 1920px;
  position: $pos-abs;
  flex-direction: row;
  display: flex;
  z-index: 999;
  top: 0;

  .season-main-container {
    position: $position-relative;

    .season-main-block {
      border-radius: 12px;
      background-color: #131313;
      width: 1838px;
      height: 413px;
      position: absolute;
      left: 41px;
      bottom: 124px;

      .Season-button-header {
        display: flex;
        align-items: center;
        overflow: scroll hidden;
        width: 1780px;
        height: 82.8px;
        margin: 23px 19px;
        scroll-snap-type: x mandatory;

        .season-btn-series-focused {
          background-color: #242424;
          border-radius: 6.9px;
          text-align: center;
          min-width: 280px;
          height: 72px;
          margin: 27px 20px;
          scroll-snap-type: x mandatory;

          &:focus {
            border: 2px solid #fff;
            min-width: 322px !important;
            border-radius: 6.9px;
            scroll-snap-align: end;
          }

          &:active {
            background-color: #981c15;
            min-width: 322px !important;
            border-radius: 6.9px;
          }
        }

        .focused {
          background-color: #981c15;
        }

        .season-title {
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 28px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 21px;
        }
      }

      .carousel-main {
        display: flex;
        padding: 0;
        margin: 20px;
        overflow: hidden;
        // scroll-behavior: smooth;

        .carousel-container {
          position: relative;
          overflow-x: auto;
          .carousel {
            display: flex;
            transition: transform 330ms ease-in-out;
            .episode-container {
              margin-right: 40px;
              .episode-card-title {
                color: #bbbbbb;
                margin-top: 10px;
                line-height: 30px;
                font-size: 28px;
                font-family: Roboto;
                text-align: center;
                height: 33px;
                width: 384px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            .episode-card-img {
              width: 384px;
              height: 216px;
              display: flex;
              flex-direction: row;
              position: $position-relative;
              opacity: 0.6;
              justify-content: center;
              
              .carousel-image {
                -webkit-box-shadow: 5px 5px 5px 0px rgba(0, 0, 0, 0.5);
                -moz-box-shadow: 5px 5px 5px 0px rgba(0, 0, 0, 0.5);
                box-shadow: 5px 5px 5px 0px rgba(0, 0, 0, 0.5);
              }
              .episode-card-lazy {
                width: 370px;
                height: 206px;
                margin: 5px;
              }

              .episode-progress-bar {
                position: absolute;
                bottom: 10px;
                left: 28.5px;
                width: 84%;
              }

              .episode-play-btn {
                position: absolute;
                bottom: 64px;
                right: 145px;

                .play-pause-btn {
                  width: 88px;
                  height: 88px;
                }
              }
            }
          }
            // .carousel.sliding-transition {
            //   transition: transform 330ms ease-in-out;
            // }
            .carousel-border {
              scroll-snap-align: end;
              opacity: 1 !important;
              width: 381px;
              height: 217px;
              border: 4px solid #fff;
              border-radius: 9px;
            }
            
          }
          .arrow-button {
            padding: 0;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: #000;
            cursor: pointer;
            background: rgb(255, 255, 255, 0.3);
            width: 36px;
            font-size: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: none;
            border-radius: 50%;
            aspect-ratio: 1;
            -webkit-box-shadow: 5px 5px 5px -2px rgba(0, 0, 0, 0.5);
            -moz-box-shadow: 5px 5px 5px -2px rgba(0, 0, 0, 0.5);
            box-shadow: 5px 5px 5px -2px rgba(0, 0, 0, 0.5);
            .sp {
              height: 100%;
              width: 100%;
            }
          }

          .left-arrow {
            left: 20px;
          }
          
          .right-arrow {
            right: 20px;
          }
        }
      }

      .episode-body {
        overflow-x: scroll;
        margin-left: 20px;

        .EpisodeCardImginseries {
          width: 300px;
          height: 200px;
        }
      }
    }
  }
}