import { call, put, takeEvery } from "redux-saga/effects";
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from "../store/sagaStore";
import { getEpgVersionError, getEpgVersionSuccess } from "../store/slices/EpgVersionSlice";

function* workGetEpgVersion() {
  try {
    yield call(
        request,
        URL.EPG_VERSION_URL,
        {
          method: 'GET',
        },
        {
          onSuccess(response) {
            store.dispatch(getEpgVersionSuccess(response))
          },
          onError(error) {
            store.dispatch(getEpgVersionError(error))
          },
        },
      )
  } catch (error){
    store.dispatch(getEpgVersionError(error));
  }
}

export default function* epgVersionSaga() {
  yield takeEvery("epgVersion/getEpgVersion", workGetEpgVersion);
}
