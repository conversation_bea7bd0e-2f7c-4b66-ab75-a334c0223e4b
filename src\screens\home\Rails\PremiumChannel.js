import React, { useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  getPremiumNodeValue,
  getPremiumCardFocus,
  getClearStatePremiumCard,
  getPremiumValueStateClear
} from '../../../store/slices/HomeSlice'
import './PremiumChannel.scss'
import { RailsSkeletonLoading } from '../../SkeletonScreenLoading/SkeletonScreenloading'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { clearSubmenuCard, getTabvalue, subMenuRailIndex } from '../../../store/slices/subMenuDataSlice'
import { getNavBarClicked, getRegisterPopup } from '../../../store/slices/login'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { useNavigate } from 'react-router-dom'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { contentSelectionType } from '../../../GoogleAnalyticsConstants'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const PremiumChannel = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const railRef = useRef([])
  const railImage = props?.dataObject?.highlight
  const liveChannelsData = useSelector(state => state?.epg?.epgChannel)
  const navbarTab = useSelector(state => state?.homeReducer?.NavTabValue)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const nav = useSelector(state => state?.homeReducer?.storenavdata)  

  const handlePremiumTabValue = (e, item, index) => {
    localStorage.setItem('focusedEle', item?.section)
    localStorage.setItem('subMenu', 1)
    e.preventDefault()
    const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : nav,
        content_list : props?.title,
        modulo_name : 'carrusel',
        content_list_id : 'carrusel horizontal'
      }
    dispatch(getRegisterPopup(false))
    dispatch(subMenuRailIndex({}))
    dispatch(getClearStatePremiumCard({}))
    dispatch(getPremiumValueStateClear({}))
    dispatch(clearSubmenuCard())
    dispatch(getPremiumCardFocus(`index${props?.index}${index}`))
    dispatch(getPremiumNodeValue(item?.section))
    dispatch(getTabvalue(item?.section))
    dispatch(getNavBarClicked(true))
    pushContentSelectionEvent(userData,item,index,contentSelectionType.HOME_SERIES)
  }
  const handleFocus = (data,index) => {
    railRef?.current?.[index]?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    });
  }

  const navigateToLivePlayer = (data,index) => {
    const payload = {
      group_id: data?.group_id,
      timeshift: data?.timeshift,
      switchChannel: 'yes',
      epgIndex: liveChannelsData[1]?.channelResponse?.findIndex(
        itrObj => itrObj.group_id == data?.group_id
      ),
      user_id : userDetails?.user_id,
      parent_id : userDetails?.parent_id,
      suscriptions : Object.keys( userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
      user_type : userDetails ? 'registrado':'anonimo',
      content_section : nav,
      content_list : props?.title,
      modulo_name : 'carrusel'
    }
    dispatch(getChannelData(payload)),
      pushContentSelectionEvent(payload,data,index, contentSelectionType.HOME_SERIES)
      navigate('/livePlayer', {
        state: { showControls: 'live', data: data, pageName: navbarTab }, //Added showControls flag which will enable live player after navigation
        replace: true
      })
  }
  const handleKeyDown = event => {
    dispatch(getNavBarClicked(false))
    const element = document.getElementById('carousel-container')
    if (element) {
      const cards = document.querySelectorAll('.premium-carousel-image')
      const focusedElement = document.activeElement
      const focusedIndex = Array.from(cards).indexOf(focusedElement)

      if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
        const nextCard = cards[focusedIndex + 1]
        const containerRect = element.getBoundingClientRect()
        const nextCardRect = nextCard.getBoundingClientRect()

        if (nextCardRect.right > containerRect.right) {
          element.scrollLeft += nextCardRect.right - containerRect.right
        }
      } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
        const prevCard = cards[focusedIndex - 1]
        const containerRect = element.getBoundingClientRect()
        const prevCardRect = prevCard.getBoundingClientRect()

        if (prevCardRect.left < containerRect.left) {
          element.scrollLeft -= containerRect.left - prevCardRect.left
        }
      }
    }
  }
  const premiumTabValue = useSelector(
    state => state?.homeReducer?.premiumCardFocus
  )

  useEffect(() => {
    if (premiumTabValue) {
      const element = document.getElementById(premiumTabValue)
      if (element) {
        element.focus()
      }
    }
  }, [premiumTabValue])

  useEffect(() => {
    const element = document.getElementById(props?.id)
    element && element.scrollIntoView({ behavior: 'smooth' })
    const carouselContainer = document.getElementById('carousel-container')
    carouselContainer &&
      carouselContainer.addEventListener('keydown', handleKeyDown)
    return () => {
      carouselContainer &&
        carouselContainer.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  return (
    <div className="premium-carousel-Container">
      {railImage?.[0] ? (
        <div className="premium-carousel-Title">
          <p className="premium-text"><SafeHTML html={props?.title || ''} /></p>
        </div>
      ) : null}
      {!railImage?.[0] ? (
        <div style={{ display: 'flex' }} className="carousel">
          <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
        </div>
      ) : (
        <div className="premium-carousel">
          <div
            className="premium-carousel-wrapper"
            id="carousel-container"
            tabIndex="0" // Make the container focusable
          >
            {railImage?.map((each, index, array) => (
              <button
                ref={el => (railRef.current[index] = el)}
                key={each.group_id || index}
                id={`index${props?.index}${index}`}
                data-testid={`rail_card_click${index}`}
                className="premium-carousel-image focusable"
                tabIndex={index === 0 ? '0' : '-1'} // Make the first card focusable
                onFocus={() => {
                  handleFocus(each,index)
                }}
                onClick={e => {
                  each.type === 'node'
                    ? handlePremiumTabValue(e, each, index)
                    : navigateToLivePlayer(each, index)
                }}
                data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                data-sn-up={document?.getElementById('carousel_id-0') ? undefined : document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                data-sn-right={
                  railImage?.length - 1 == index
                    ? `#index${props?.index}${railImage?.length - 1}`
                    : `#index${props?.index}${index + 1}`
                }
              >
                {each.image_small && each.image_small !== '' ? (
                  <LazyLoadImage
                    src={each.image_small}
                    key={each.group_id}
                    loading="lazy"
                    className="premiumImage"
                    placeholderSrc="images/landscape_card.png"
                  />
                ) : (
                  <LazyLoadImage
                    src="images/landscape_card.png"
                    alt="PlaceHolder"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default PremiumChannel
