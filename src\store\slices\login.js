import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  loginInfo: {},
  userInfo: {},
  loginError: {},
  loginSuccess: {},
  registerError: {},
  registerSuccess: {},
  isLoggedInStatus: '',
  accessToken: '',
  refreshToken: '',
  isLoading: false,
  recovermail: {},
  loginNavigation: false,
  registerNavigation: false,
  watchFreestate: false,
  registerPopup: false,
  vcardDetails: {},
  loading: false,
  RegBackNavigate: false,
  newpasswordCode: {},
  vcardSeriesDetails: {},
  anonymousUser: {},
  navBarVariable: '',
  settingClicked: false,
  termsAndConditionSuccess: {},
  termsAndConditionError: {},
  isLoggedInRefresh: {},
  isLoggedInRefreshError: {},
  isLoggedIn: {},
  isLoggedInV1Error: {},
}

export const loginSlice = createSlice({
  name: 'login',
  initialState,
  reducers: {
    getLogin: (state, { payload }) => {
      state.isLoading = true
    },
    getLoginSuccess: (state, { payload }) => {
      state.loginSuccess = payload
    },
    loginError: (state, { payload }) => {
      state.loginError = payload
    },
    getRegister: (state, { payload }) => {
      state.isLoading = true
    },
    getRegisterSuccess: (state, { payload }) => {
      state.registerSuccess = payload
    },
    getRegisterError: (state, { payload }) => {
      state.registerError = payload
    },

    getUserInfo: (state, { payload }) => {
      state.isLoading = true
    },
    getUserInfoSuccess: (state, { payload }) => {
      state.userInfo = payload
    },

    loginSuccess: (state, { payload }) => {
      state.accessToken = payload.accessToken
      state.refreshToken = payload.refreshToken
    },
    getPassword: (state, { payload }) => {
      state.isLoading = true
    },
    getPasswordSccess: (state, { payload }) => {
      state.newpasswordCode = payload
    },
    recoverEmail: (state, { payload }) => {
      state.recovermail = payload
    },
    getIsLoggedinV1: state => {
      state.isLoading = true
    },
    getIsLoggedinV1Success: (state, { payload }) => {
      state.isLoggedIn = payload
       state.isLoggedInStatus = 'Success'
      state.isLoading = false
    },
    getIsLoggedinV1Error: (state, { payload }) => {
      state.isLoggedInV1Error = payload
      state.isLoading = false
    },
    getIsLoggedinRefresh: state => {
      state.isLoading = true
    },
    getIsLoggedinRefreshSuccess: (state, { payload }) => {
      state.isLoggedInRefresh = payload
    },
    getIsLoggedinRefreshError: (state, { payload }) => {
      state.isLoggedInRefreshError = payload
    },

    getLoginNavigation: (state, { payload }) => {
      state.loginNavigation = payload
    },
    getRegisterNavigation: (state, { payload }) => {
      state.registerNavigation = payload
    },
    getWatchFree: (state, { payload }) => {
      state.watchFreestate = payload
    },
    getRegisterPopup: (state, { payload }) => {
      state.registerPopup = payload
    },
    getTermsAndCondition: (state, { payload }) => {
      state.isLoading = true
    },
    getTermsAndConditionSuccess: (state, { payload }) => {
      state.termsAndConditionSuccess = payload
    },
    getTermsAndConditionError: (state, { payload }) => {
      state.termsAndConditionError = payload
    },
    getVcardData: (state, { payload }) => {
      state.vcardDetails = payload
    },
    getSeriesData: (state, { payload }) => {
      state.vcardSeriesDetails = payload
    },
    getClearAllLoginStates: state => {
      state.loginInfo = {}
      state.userInfo = {}
      state.loginError = {}
      state.loginSuccess = {}
      state.registerError = {}
      state.registerSuccess = {}
      state.isLoggedIn = {}
      state.isLoggedInV1Error = {}
      state.accessToken = ''
      state.refreshToken = ''
      state.isLoading = false
      state.recovermail = ''
      state.newpasswordCode = ''
      state.isLoggedInStatus = ''
      state.isLoggedInRefreshError = ''
      state.isLoggedInRefresh = ''
      state.settingClicked = false
      state.termsAndConditionSuccess = {}
      state.termsAndConditionError = {}
    },
    getBackNavigate: (state, { payload }) => {
      state.RegBackNavigate = payload
    },

    clearIsloggedInStatus: state => {
      state.isLoggedInStatus = ''
    },

    clearLoginInfoState: state => {
      state.loginSuccess = {}
    },

    clearLoginUserInfoState: state => {
      state.userInfo = {}
    },

    getGuestUserPlayerData: (state, { payload }) => {
      state.anonymousUser = payload
    },
    setSkeltonLoading: (state, { payload }) => {
      state.loading = payload
    },
    getNavBarClicked: (state, { payload }) => {
      state.navBarVariable = payload
    },
    getSettingsClicked: (state, { payload }) => {
      state.settingClicked = payload
    }
  }
})

export const {
  setSkeltonLoading,
  clearIsloggedInStatus,
  getLogin,
  getVcardData,
  getSeriesData,
  checkLoggedin,
  getImage,
  getImageSuccess,
  loginSuccess,
  getRegister,
  getUserInfo,
  getUserInfoSuccess,
  getLoginSuccess,
  loginError,
  getRegisterSuccess,
  getRegisterError,
  getPassword,
  recoverEmail,
  getClearAllLoginStates,
  clearLoginInfoState,
  clearLoginUserInfoState,
  getIsLoggedinV1,
  getIsLoggedinV1Success,
  getIsLoggedinV1Error,
  getLoginNavigation,
  getRegisterNavigation,
  getWatchFree,
  getRegisterPopup,
  getBackNavigate,
  getPasswordSccess,
  getTermsAndCondition,
  getTermsAndConditionSuccess,
  getTermsAndConditionError,
  getGuestUserPlayerData,
  getNavBarClicked,
  getSettingsClicked,
  getIsLoggedinRefresh,
  getIsLoggedinRefreshSuccess,
  getIsLoggedinRefreshError
} = loginSlice.actions
export default loginSlice.reducer
