function detectSmartTVPlatform() {
  const uan = navigator.userAgent.toLowerCase();

  if (uan.includes('whaletv')) return 'zeasn';
  if (uan.includes('tizen')) return 'samsung';
  if (uan.includes('netrange')) return 'netrange';
  if (uan.includes('vidaa') || uan.includes('hisense')) return 'hisense';
  if (uan.includes('web0s')) return 'lg';

  return 'lg';
}

export const CURRENT_PLATFORM = detectSmartTVPlatform();


