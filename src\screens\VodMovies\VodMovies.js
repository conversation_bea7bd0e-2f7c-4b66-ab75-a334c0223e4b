import React, { useEffect, useCallback, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import {
  getClearVodState,
  getVodMoviesData,
  getVodMoviesCastData,
  getVodMoviesWatchlist,
  addVodMoviesWatchList,
  deleteVodMoviesWatchList
} from '../../store/slices/VodMoviesSlice'
import {
  clearGetMediaRes,
  clearTrailerPlayerError,
  getMediaAPI,
  getTrailerPlayer,
  setGenreDetails
} from '../../store/slices/PlayerSlice'
import {
  getControlPin,
  getViewSubscribeData,
  getVodSubscriptionInfo,
  clrSubscriptionInfo,
  getStatusControlPin,
  getVodID,
  getFinishSubscriptionPlayerData
} from '../../store/slices/settingsSlice'
import Lottie from 'react-lottie-player'
import animationData from '../../../src/json/animationData.json'
import './VodMovies.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import ProgressBar from '../Progressbar/Progressbar'
import {
  continueWatchlist,
  lastSeenWatch
} from '../../store/slices/getWatchListSlice'
import Navbar from '../home/<USER>'
import PlayerModelSelection from './PlayerModelSelection'
import PlayerErrorHandler from '../bitmovinPlayer/PlayerErrorHandler'

import {
  getTalentSearchData,
  setShowApiFailureModal,
  getTalentSearch,
  setShowTalentModule,
  setVodReturnFocusById,
  removeVodReturnFocusById,
  getCleartalentState
} from '../../store/slices/SearchSlice'
import ErrorEventModule from '../talentSearch/ErrorEventModule'
import TalentSearch from '../talentSearch/TalentSearch'
import {
  pushContentDetailsEvent,
  pushContentSelectionEvent,
  pushInteractionContentEvent,
  pushAddSubscriptionEvent
} from '../../GoogleAnalytics'
import { CONTENIDO_BLOQUEADO, contentSelectionType, interactionType, MOVIE, NOT_APPLICABLE, PLAYER, SERIES } from '../../GoogleAnalyticsConstants'
import { getIsLoggedinV1 } from '../../store/slices/login'
import { COMMON_URL } from '../../utils/environment'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const VodMovies = () => {
  const navigate = useNavigate()

  const [recomendationFlag, setRecomendationFlag] = useState(false)
  const [recomendationTab, setRecomendationTab] = useState(true)
  const [isOnClickRecome, setIsOnClickRecome] = useState(false)
  const [moreInfoTab, setMoreInfoTab] = useState(false)
  const [videoplayerWarning, setVideoPlayerWarning] = useState(false)
  const [hideVodDetailPage, setHideVodDetailPage] = useState(false)

  const [trailerBtnClkFocus, setTrailerBtnClkFocus] = useState(false)
  const [playBtnClkFocus, setPlayBtnClkFocus] = useState(true)
  const [watchlistButtonClickFocus, setWatchlistButtonClickFocus] =
    useState(false)
  const [subscriptionFocus, setSubscriptionFocus] = useState(false)
  const [channelSubscriptionFocus, setChannelSubscriptionFocus] =
    useState(false)

  const [subscriptionComprarFocus, setSubscriptionComprarFocus] =
    useState(false)
  const [subscriptionRentarFocus, setSubscriptionRentarFocus] = useState(false)
  const [subscriptionPreventaFocus, setSubscriptionPreventaFocus] =
    useState(false)

  const [focusRecomendRed, setRecomendFocusRed] = useState(false)
  const [defaultRecomendRed, setDefaultFocusRed] = useState(false)

  const [focusMoreinfoRed, setMoreinfoFocusRed] = useState(false)
  const [contentData, setContentData] = useState([])
  const [vodSimultaneous, setVodSimultaneous] = useState({})
  const [playbackRetry, setPlaybackRetry] = useState(false)
  const [streamType, setStreamType] = useState('')
  const navIndex = localStorage.getItem('currNavIdx')

  //vodmain
  const { state } = useLocation()
  const { vodData, card } = state
  const upBackKeyFocus = useRef(false)

  const cardRef = useRef(card)
  const contentDataRef = useRef(null)
  const subscriptionKeyRef = useRef(null)

  useEffect(() => {
    card ? (cardRef.current = card) : (cardRef.current = '')
  }, [card])

  const watchListRef = useRef(null)
  const region = localStorage.getItem('region')

  const dispatch = useDispatch()
  const [watchListData, setWatchListData] = useState([])
  const [watchList, setWatchList] = useState(false)
  const [talentSearchfield, setTalentSearchfield] = useState('')
  const [filterlist, setFilterlist] = useState('')
  const [apiCallStart, setApiCallStart] = useState(false)
  const [moviesData, setMoviesData] = useState(vodData)
  const [genreData, setGenreData] = useState([])
  const [castDetail, setCastDetail] = useState([])
  const [currentButtonFocus, setCurrentButtonFocus] = useState('')
  const [viewDetailButton, setViewDetailButton] = useState('')
  const [viewRentButton, setViewRentButton] = useState('')
  const [viewbuyButton, setViewBuyButton] = useState('')
  const [buttonsInfo, setButtonsInfo] = useState('')
  const [promoLabel, setPromoLabel] = useState('')
  const [familyPromoLabel, setFamilyPromoLabel] = useState('')
  const [producttype, setproductType] = useState('')
  const [periodicity, setPeriodicity] = useState('')

  const [priceMonth, setPriceMonth] = useState('')
  const [symbolCurrency, setSymbolCurrency] = useState('')
  const [lastSeenPercentage, setLastSeenPercentage] = useState(0)

  const vodMoviesMltData = useSelector(state => state?.vodMovies?.data)
  const vodMoviesCast = useSelector(state => state?.vodMovies?.castData)
  const getWatchListRedux = useSelector(state => state?.vodMovies?.watchList)
  const addWatchLists = useSelector(state => state?.vodMovies?.addWatchList)
  const deleteWatchList = useSelector(state => state?.vodMovies?.delWatchList)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const getMediaRes = useSelector(state => state?.player?.getMediaRes?.response)
  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getVodSubsInfo?.response
  )
    const navbarTab = useSelector(state => state?.homeReducer?.NavTabValue)
    const navbar = useSelector(state => state?.homeReducer?.storenavdata)   
    const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
    const nav = useSelector(state => state?.homeReducer?.navbarData)
    const navData = userDetails?.is_kids === 'true' ? navKids : nav
    const homeIndex = navData?.findIndex(item => item?.code === 'homeuser' && item?.page === 'Inicio' );
    const moviesIndex = navData?.findIndex(item => item?.code === 'peliculas' && item?.page === 'Películas' );
    const mycontentIndex = navData?.findIndex(item =>item?.code === 'miscontenidos');
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const trailerRedux = useSelector(
    state => state?.player?.trailerplayerinfo?.response
  )
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles?.[0]?.parental
        ?.active
  )
  const continueWatchingList = useSelector(
    state => state?.watchList?.continuewatchlist?.response?.groups
  )
  const getLastSeenData = useSelector(
    state => state?.watchList?.lastSeenWatchListData?.response
  )
  const getMediaError = useSelector(
    state => state?.player?.getMediaError?.errors?.[0]
  )
  const getTrailerMediaError = useSelector(
    state => state?.player?.trailerplayerResponseError?.errors?.[0]
  )
  const vodDetails = useSelector(state => state?.login?.vcardDetails)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )

  let talentSearchData = useSelector(state => state?.search?.talentSearchData)
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)

  let filteredRegionLists = apaMetaData?.byr_filterlist_configuration
    ? JSON.parse(apaMetaData?.byr_filterlist_configuration)
    : ''
  const showApiFailureModal = useSelector(
    state => state?.search?.showApiFailureModal
  )
  const showTalentModule = useSelector(state => state?.search?.showTalentModule)
  const setVodReturnFocus = useSelector(
    state => state?.search?.setVodReturnFocus
  )
  const posterTitle =
    apaMetaData?.poster_title_configuration &&
    JSON?.parse(apaMetaData?.poster_title_configuration)

  const supportedStream =
    apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)
  const userId = vodDetails?.confirmscreen
    ? loginInfo?.user_id ?? registerInfo?.user_id
    : userDetails?.user_id
  const serieslargeProgress = true
  const apilanguage = translations?.language?.[region]
  const lastTouch = localStorage.getItem('lasttouch')
  const age_rating = vodMoviesCast?.common?.extendedcommon?.media?.rating
  const trailerButtonEnable = /true/.test(
    vodMoviesCast?.common?.extendedcommon?.media?.haspreview
  )
  const subscribtionButton =
    addSubscriptions && addSubscriptions?.listButtons?.button
  const statusControlPin = useSelector(
    state =>
      state?.settingsReducer?.statusControlPin?.response?.pin_parental?.info
        ?.value
  )
  const contentMenu = useSelector(state => state?.homeReducer?.contentMenu)
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
 
  const navbarData = useSelector(state => state?.homeReducer?.navbarNodeData?.response?.nodes) ?? []
 const contentSection = navbarData?.find(each => each?.code === navbarTab )

  const handleDefaultFocus = useCallback(() => {
    if (buttonsInfo == 1) {
      if(state?.vCardBackFocus === 'trailer'){
        setCurrentButtonFocus('trailer')
        document.getElementById('trailerButton')?.focus()
        setTrailerBtnClkFocus(
            true
        )
        setPlayBtnClkFocus(false)
      }else{
        setPlayBtnClkFocus(
          (buttonsInfo == 1 ||
            state?.vCardBackFocus === 'play') ||
            playBtnClkFocus
        )
        setCurrentButtonFocus('play')
      }
    } else if(buttonsInfo == 0 && state?.vCardBackFocus === 'trailer'){
      setPlayBtnClkFocus(false)
      setCurrentButtonFocus('trailer')
      setTrailerBtnClkFocus(
        true
    )
      document.getElementById('trailerButton')?.focus()
    } else if (buttonsInfo == 0 && addSubscriptions?.listButtons?.button) {
      if (viewDetailButton) {
        setCurrentButtonFocus('channelSubscription')
        setChannelSubscriptionFocus(true)
        setTrailerBtnClkFocus(false)
        document.getElementById('longTermSubscription')?.focus()
      } else if (viewbuyButton) {
        setCurrentButtonFocus('subscriptionComprar')
        setSubscriptionComprarFocus(true)
      } else if (viewRentButton) {
        setCurrentButtonFocus('subscriptionRentar')
        setSubscriptionRentarFocus(true)
        setTrailerBtnClkFocus(false)
        document.getElementById('rentalSubscription')?.focus()
      } else if (viewbuyButton && viewRentButton) {
        setCurrentButtonFocus('subscriptionComprar')
        setSubscriptionComprarFocus(true)
      } else if (viewDetailButton && viewRentButton) {
        setCurrentButtonFocus('subscriptionRentar')
        setSubscriptionRentarFocus(true)
        setChannelSubscriptionFocus(false)
      }
    }else if(currentButtonFocus != 'play' && trailerButtonEnable ||
      vodMoviesCast?.common?.extendedcommon?.media?.haspreview){
      setPlayBtnClkFocus(false)
      setCurrentButtonFocus('trailer')
      setTrailerBtnClkFocus(
        false
    )
      document.getElementById('trailerButton')?.focus()
    }
    setDefaultFocusRed(true)
  }, [
    buttonsInfo,
    viewDetailButton,
    trailerButtonEnable,
    addSubscriptions,
    vodData,
    videoplayerWarning
  ])

  const handleBackInfoTab = () => {
    dispatch(removeVodReturnFocusById())
    dispatch(setVodReturnFocusById())
    dispatch(setShowTalentModule(false))
    dispatch(setShowApiFailureModal(false))
    dispatch(getTalentSearchData())
    handleDefaultFocus()
    setRecomendationTab(false), setHideVodDetailPage(false)
    setMoreInfoTab(false), setMoreinfoFocusRed(false)
    setIsOnClickRecome(false)
    setVideoPlayerWarning(false)
    setWatchlistButtonClickFocus(false)
    setTrailerBtnClkFocus(false)
    setSubscriptionFocus(false)
    setChannelSubscriptionFocus(false)
    setSubscriptionComprarFocus(false)
    setSubscriptionRentarFocus(false)
    setSubscriptionPreventaFocus(false)
    // setWatchListData([])
    //setWatchList(false)
  }

  const handleDefaultRecom = () => {
    if (hideVodDetailPage) {
      moreInfoTab ? setMoreInfoTab(true) : setRecomendationTab(true)
      moreInfoTab
        ? setCurrentButtonFocus('moreinfo')
        : setCurrentButtonFocus('recomendation')
      moreInfoTab ? setMoreinfoFocusRed(true) : setRecomendFocusRed(true)
      moreInfoTab &&
        currentButtonFocus != 'moreinfo' &&
        setCurrentButtonFocus('cast')
      setDefaultFocusRed(false)
    }
  }

  const handleRentbuttonChnage = () => {
    setViewRentButton(true)
    setViewDetailButton(false)
  }

  const handleScrollUp = () => {
    const element = document.querySelector('.info-description-detail')
    if (element) {
      element.scrollTop -= 10
      if (element.scrollTop == 0) {
        if (element.scrollTop == 0 && currentButtonFocus == 'moreinfo') {
          if (!upBackKeyFocus.current) {
            document.getElementById('nav-1')?.focus()
          } else {
            upBackKeyFocus.current = false
          }
        } else if (
          element.scrollTop == 0 &&
          currentButtonFocus == 'recomendation'
        ) {
          if (!upBackKeyFocus.current) {
            document.getElementById('nav-1')?.focus()
          } else {
            upBackKeyFocus.current = false
          }
        }
      }
    }
  }

  const handleScrollDown = () => {
    const element = document.querySelector('.info-description-detail')
    if (element) {
      element.scrollTop += 10
      const maxScroll = element.scrollHeight - element.clientHeight
      if (element.scrollTop == maxScroll) {
        if (
          currentButtonFocus == 'moreinfo' ||
          currentButtonFocus == 'recomendation'
        ) {
          const castElement = document.getElementById('castImage-0')
          if (castElement) castElement.focus()
        }
      }
    }
  }

  const handleScrolling = event => {
    const element = document.querySelector('.info-description-detail')
    if (element && event.key === 'ArrowDown') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollDown(event)
    } else if (element && event.key === 'ArrowUp') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollUp(event)
    }
  }

  const handleScrollingRecommendation = event => {
    const element = document.querySelector('.info-description-detail')
    if (element && event.key === 'ArrowDown') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollDown(event)
    } else if (element && event.key === 'ArrowUp') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollUp(event)
    } else {
      handleFocusredRecom(event)
    }
  }

  const handleScrollingMoreInfo = event => {
    const element = document.querySelector('.info-description-detail')
    if (element && event.key === 'ArrowDown') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollDown(event)
    } else if (element && event.key === 'ArrowUp') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollUp(event)
    } else {
      handleFocusredMoreInfo(event)
    }
  }

  const scrollFunction = () => {
    setHideVodDetailPage(true)
    setRecomendationFlag(true)
    setRecomendationTab(true)
    setCurrentButtonFocus('recomendation')
    setRecomendationTab(true)
    setMoreInfoTab(false)
    handleDefaultRecom()
    window.scrollTo({
      top: 0,
      behaviour: 'smooth'
    })
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      var codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
    }

    if (
      event?.code == 'ArrowDown' ||
      event?.keyCode == 40 ||
      event.key === 'ArrowDown'
    ) {
      if (document.getElementById('recom-tab-one')) {
        setCurrentButtonFocus('recomendation')
        currentButtonFocus == 'recomendation' && scrollFunction()
      }
    }

    if (document.getElementById('recommendedpage')) {
      if (
        event.keyCode === 10009 ||
        event.keyCode === 461 ||
        event.keyCode == 8 ||
        codes?.yellowcode === keycode
      ) {
        handleBackInfoTab()
      }
    }

    if (document.getElementById('reco')) {
      if (
        event?.code == 'ArrowUp' ||
        event?.keyCode == 38 ||
        event.key === 'ArrowUp'
      ) {
        if (currentButtonFocus == 'recomendation') {
          let testElement = document.getElementById('reco')
          if (testElement) {
            if (document.activeElement != testElement) {
              handleBackInfoTab()
              handleDefaultFocus()
            }
          }
        }
      }
    }

    if (document.getElementById('moreInfo')) {
      if (
        event?.code == 'ArrowUp' ||
        event?.keyCode == 38 ||
        event.key === 'ArrowUp'
      ) {
        if (currentButtonFocus == 'moreinfo') {
          let testElement = document.getElementById('moreInfo')
          if (testElement) {
            if (document.activeElement != testElement) {
              handleBackInfoTab()
              handleDefaultFocus()
            }
          }
        }
      }
    }

    if (document.getElementById('background-movie-image')) {
      if (
        event.keyCode === 10009 ||
        event.keyCode === 461 ||
        event.keyCode == 8
      ) {
        if (state?.returnPage === 'search') {
          navigate('/search', {
            state: { inputValue: state?.inputValue },
            replace: true
          })
          state?.talentData &&
            dispatch(
              getTalentSearch({
                data: state?.talentData,
                name: state?.talentName
              })
            )
        } else {
          localStorage.setItem('currNavIdx', navbarTab == 'homeuser' ? homeIndex : navbarTab == 'miscontenidos'? mycontentIndex : moviesIndex)
          navigate('/home', {
            state: {
              id:
                cardRef.current + vodData?.id ??
                cardRef.current + vodData?.group_id,
              backfocusid: state?.backfocusid,
              fromPage: state?.fromPage
            }
          })
        }
      } else if (event.keyCode == 38) {
        if (!upBackKeyFocus.current) {
          document.getElementById(`nav-${navIndex}`)?.focus()
        } else {
          upBackKeyFocus.current = false
        }
      }
    }
    if (
      document.getElementById('videoPlayerWarnings') ||
      document.getElementsByClassName('vod-player-warning-container')
    ) {
      if (
        event.keyCode === 10009 ||
        event.keyCode === 461 ||
        event?.keyCode == 8 ||
        event?.keyCode == 405 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
      ) {
        setVideoPlayerWarning(false)
      }
    }
  }

  useEffect(() => {
    if (hideVodDetailPage) {
      handleDefaultRecom()
    } else {
      handleDefaultFocus()
    }
  }, [hideVodDetailPage])

  useEffect(() => {
    handleDefaultFocus()
  }, [
    buttonsInfo,
    viewDetailButton,
    trailerButtonEnable,
    addSubscriptions,
    videoplayerWarning
  ])

  useEffect(() => {
    if (vodData?.id || vodData?.group_id) {
      vodDetailfetchApi(vodData?.id ?? vodData?.group_id)
    }
    if (state?.flag == true) {
      talentSearchData = null
      handleBackInfoTab()
      dispatch(setShowApiFailureModal(false))
      dispatch(setShowTalentModule(false))
    }
  }, [vodData])

  useEffect(() => {
    const seenLastTouch = localStorage.getItem('seenLastTouch')
    if (userDetails?.lasttouch?.seen && apiCallStart) {
      dispatch(
        lastSeenWatch({
          user_id: userId,
          group_id: vodData?.id ?? vodData?.group_id,
          user_hash: userDetails?.session_userhash,
          filterlist: filterlist,
          lasttouch: userDetails?.lasttouch?.seen ?? seenLastTouch
        })
      )
      localStorage.setItem('seenLastTouch', userDetails?.lasttouch?.seen)
      setApiCallStart(false)
    }
  }, [userDetails])

  const vodDetailfetchApi = async id => {
    setApiCallStart(true)
    dispatch(
      getIsLoggedinV1({HKS:
        userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
  })
    )
    dispatch(
      getVodMoviesData({
        id: id,
        userId: userId,
        hks: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids,
        display_only: 'movies',
        user_hash: userDetails?.session_userhash,
        user_token: userDetails?.user_token,
        filterlist:
          filteredRegionLists?.[`${region}`]?.filterlist ??
          filteredRegionLists?.default?.filterlist
      })
    )

    dispatch(
      getVodMoviesCastData({
        id: id,
        userId: userId,
        hks: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids
      })
    )
    dispatch(
      getVodMoviesWatchlist({
        id: moviesData?.lasttouch?.favorited ?? lastTouch,
        userId,
        HKS: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        user_hash: vodDetails?.confirmscreen
          ? loginInfo?.session_userhash ?? registerInfo?.session_userhash
          : userDetails?.session_userhash,
        filterlist:
          filteredRegionLists?.[`${region}`]?.filterlist ??
          filteredRegionLists?.default?.filterlist
      })
    )
    if (watchFree) {
      dispatch(
        getVodSubscriptionInfo({
          is_kids: vodDetails?.confirmscreen
            ? loginInfo?.is_kids ?? registerInfo?.is_kids
            : userDetails?.is_kids,
          url: `group_id=${id}`,
          type: 'watchfree'
        })
      )
    } else {
      dispatch(
        getVodSubscriptionInfo({
          userId: vodDetails?.confirmscreen
            ? loginInfo?.parent_id ?? registerInfo?.parent_id
            : userDetails?.parent_id,
          hks: vodDetails?.confirmscreen
            ? loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue
            : userDetails?.session_stringvalue,
          is_kids: vodDetails?.confirmscreen
            ? loginInfo?.is_kids ?? registerInfo?.is_kids
            : userDetails?.is_kids,
          url: `group_id=${id}`
        })
      ),
        dispatch(getVodID(id))
    }

    dispatch(
      getControlPin({
        hks: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        user_id: userDetails?.user_id,
        user_token: userDetails?.user_token
      })
    )
    dispatch(
      getStatusControlPin({
        hks: userDetails?.session_stringvalue,
        userId: userDetails?.user_id
      })
    )
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    if (hideVodDetailPage) {
      handleDefaultRecom()
    } else {
      handleDefaultFocus()
    }
  }, [hideVodDetailPage])

  useEffect(() => {
    if (contentData?.playButton?.visible == 1) {
      const totalDuration = contentData?.playButton?.media?.duration?.seconds
      const playedDuration =
        contentData?.playButton?.media?.initial_playback_in_seconds
      const percentage = ((playedDuration / totalDuration) * 100).toFixed(2)
    }
  }, [contentData])

  useEffect(() => {
    handleDefaultFocus()
  }, [
    buttonsInfo,
    viewDetailButton,
    trailerButtonEnable,
    addSubscriptions,
    videoplayerWarning
  ])



  useEffect(() => {
    trailerRedux && dispatch(clearTrailerPlayerError())
  }, [trailerRedux])

  useEffect(() => {
    subscribtionButton?.map(each => {
      ;(each?.periodicity == 'month' || each?.periodicity == 'year') &&
      each?.oneoffertype == 'subscrition'
        ? (setViewDetailButton(true),
          setPromoLabel(each?.bonus),
          setFamilyPromoLabel(each?.family),
          setproductType(each?.producttype),
          setPeriodicity(each?.periodicity),
          setPriceMonth(each?.price),
          setSymbolCurrency(each?.currency))
        : each?.oneoffertype === 'buy' || each?.oneoffertype == 'download_buy'
        ? setViewBuyButton(true)
        : (each?.oneoffertype === 'download_rent' ||
            each?.oneoffertype === 'rent') &&
          each?.periodicity === 'hour'
        ? handleRentbuttonChnage()
        : setViewDetailButton(false)
    })
  }, [addSubscriptions])

  useEffect(() => {
    const seenLastTouch = localStorage.getItem('seenLastTouch')
    if (userDetails?.lasttouch?.seen != seenLastTouch) {
      dispatch(
        continueWatchlist({
          id: userDetails?.lasttouch?.seen ?? seenLastTouch,
          userId: userId,
          HKS:
            userDetails?.session_stringvalue ??
            loginInfo?.session_stringvalue ??
            registerInfo?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
      localStorage.setItem('seenLastTouch', userDetails?.lasttouch?.seen)
    }
  }, [userDetails])

  const handleWatchListAdd = async () => {
    dispatch(
      addVodMoviesWatchList({
        id: moviesData?.id ?? moviesData?.group_id,
        userId,
        HKS: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        user_hash: vodDetails?.confirmscreen
          ? loginInfo?.session_userhash ?? registerInfo?.session_userhash
          : userDetails?.session_userhash
      })
    )
  }

  const handleWatchListDel = async () => {
    dispatch(
      deleteVodMoviesWatchList({
        id: moviesData?.id ?? moviesData?.group_id,
        userId,
        HKS: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        user_hash: vodDetails?.confirmscreen
          ? loginInfo?.session_userhash ?? registerInfo?.session_userhash
          : userDetails?.session_userhash
      })
    )
  }

  const handleCastClick = (cast, focusIndex) => {
    dispatch(setVodReturnFocusById(focusIndex))
    setTalentSearchfield(cast?.first_name ? `${cast?.name} ` : `${cast?.name}`)
    dispatch(
      getTalentSearchData({
        hks: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        value: cast?.id,
        typemusic: 'album',
        provider_id: '3',
        filterlist: filterlist,
        field: 'TALENT'
      })
    )
  }

  const addProveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }
  useEffect(() => {
    if (talentSearchData) {
      talentSearchData?.length > 0
        ? dispatch(setShowTalentModule(true))
        : dispatch(setShowApiFailureModal(true))
    }
  }, [talentSearchData])

  useEffect(() => {
    setFilterlist(
      filteredRegionLists?.[`${region}`]?.filterlist ??
        filteredRegionLists?.default?.filterlist
    )
  }, [filteredRegionLists])

  useEffect(() => {
    setWatchListData(getWatchListRedux)
  }, [getWatchListRedux])

  useEffect(() => {
    if (
      (addSubscriptions && Object.keys(addSubscriptions)?.length > 0) ||
      (moviesData && Object.keys(moviesData)?.length > 0)
    ) {
      setContentData(addSubscriptions)
      setButtonsInfo(addSubscriptions?.playButton?.visible)
      const payload = {
        id: moviesData?.id || vodData?.id || vodData?.group_id,
        payway_token: addSubscriptions?.playButton.payway_token,
        HKS: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        userId: userDetails?.user_id,
        user_token: loginInfo?.user_token,
        streamType
      }
      const watchFreepayload = {
        id: moviesData?.id || vodData?.id || vodData?.group_id,
        payway_token: addSubscriptions?.playButton.payway_token,
        type: 'watchfree',
        streamType
      }
      if (watchFree) {
       if(addSubscriptions?.playButton.payway_token) dispatch(getMediaAPI(watchFreepayload))
        if(trailerButtonEnable){
                  dispatch(
          getTrailerPlayer({
            id: moviesData?.id || vodData?.id || vodData?.group_id,
            type: 'watchfree',
             streamType: supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes(
              '_ma'
            )
             ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
              : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
          })
        )
      }
      } else {
         if(addSubscriptions?.playButton.payway_token) dispatch(getMediaAPI(payload))
           if(trailerButtonEnable){
        dispatch(
          getTrailerPlayer({
            id: moviesData?.id || vodData?.id || vodData?.group_id,
            HKS: userDetails?.session_stringvalue
              ? userDetails?.session_stringvalue
              : loginInfo?.session_stringvalue ??
                registerInfo?.session_stringvalue,
            userId: vodDetails?.confirmscreen
              ? loginInfo?.user_id ?? registerInfo?.user_id
              : userDetails?.user_id,
            user_token: loginInfo?.user_token ?? registerInfo?.user_token,
            streamType: supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes(
              '_ma'
            )
              ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
              : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
          })
        )
      }
      }
    }
  }, [addSubscriptions, moviesData])

  useEffect(() => {
    if (playbackRetry && streamType) {
      const payload = {
        id: vodData?.id ?? vodData?.group_id,
        payway_token: addSubscriptions?.playButton.payway_token,
        HKS: userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
        userId: userDetails?.user_id,
        user_token: loginInfo?.user_token,
        streamType
      }
      if(addSubscriptions?.playButton.payway_token)  dispatch(getMediaAPI(payload))
    }
  }, [playbackRetry, streamType])

  useEffect(() => {
    if (playbackRetry && (getMediaRes?.media || getMediaError?.code)) {
      setVodSimultaneous({})
      setPlaybackRetry(false)
      setStreamType(
        !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      getMediaError?.code != 'PLY_PLY_00009' && handleCheckPlayerStatus()
    }

    contentDataRef.current = {
      user_id:
        userDetails?.user_id || loginInfo?.user_id || registerInfo?.user_id,
      parent_id: userDetails?.parent_id,
      sign_up_method: 'correo electronico',
      suscriptions: subscriptionKeyRef.current?.toLowerCase(),
      user_type: watchFree ? 'anonimo' : 'registrado',
      device: 'tv',
      device_model: COMMON_URL?.device_model,
      device_name: COMMON_URL?.device_name,
      authpn: COMMON_URL?.authpn,
      content_subsection: NOT_APPLICABLE,
      content_section: contentMenu,
      country:
        userDetails?.country_code?.toLowerCase() ||
        loginInfo?.country_code?.toLowerCase() ||
        registerInfo?.country_code?.toLowerCase(),
      content_id: getMediaRes?.group?.common?.id,
      content_name: getMediaRes?.group?.common?.title?.toLowerCase(),
      content_type: getMediaRes?.group?.common?.extendedcommon?.media?.episode
        ?.number
        ? SERIES
        : MOVIE,
      content_category: genreData,
      content_availability: 'por suscripcion',
      content_episode: NOT_APPLICABLE,
      modulo_name: 'continua con tu reproduccion',
      content_price: '0.0',
      content_season: getMediaRes?.group?.common?.extendedcommon?.media?.episode
        ?.season
        ? `temporada ${getMediaRes?.group?.common?.extendedcommon?.media?.episode?.season}`
        : 'no aplica',
      content_episode: getMediaRes?.group?.common?.extendedcommon?.media
        ?.episode?.number
        ? `episodio ${getMediaRes?.group?.common?.extendedcommon?.media?.episode?.number} ${getMediaRes?.group?.common?.extendedcommon?.media?.serie?.title}`
        : 'no aplica',
      provider:
        getMediaRes?.group?.common?.extendedcommon?.media?.proveedor?.codigo?.toLowerCase()
    }
  }, [getMediaRes, getMediaError, playbackRetry])

  useEffect(() => {
    const code = getMediaError?.code
    if (code && playbackRetry) {
      setPlaybackRetry(false)
      setStreamType(
        !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
    }
    if (code == 'PLY_PLY_00009' && !playbackRetry) {
      setStreamType(
         supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      setPlaybackRetry(true)
    }
  }, [getMediaError])

  useEffect(() => {
    setLastSeenPercentage(getLastSeenData?.vistime?.last?.progress || 0)
  }, [getLastSeenData])

  useEffect(() => {
    if (deleteWatchList?.msg === 'OK') {
      setWatchList(false)
      dispatch(
        getVodMoviesWatchlist({
          id: deleteWatchList?.lasttouch?.favorited ?? lastTouch,
          userId,
          HKS: userDetails?.session_stringvalue
            ? userDetails?.session_stringvalue
            : loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue,
          user_hash: userDetails?.session_userhash,
          filterlist:
            filteredRegionLists?.[`${region}`]?.filterlist ??
            filteredRegionLists?.default?.filterlist
        })
      )
      localStorage.setItem('lasttouch', deleteWatchList?.lasttouch?.favorited)
      watchListRef?.current?.focus()
      setCurrentButtonFocus('addToWatchlist')
    }
  }, [deleteWatchList])

  useEffect(() => {
    if (addWatchLists?.msg === 'OK') {
      setWatchList(true)
      dispatch(
        getVodMoviesWatchlist({
          id: addWatchLists?.lasttouch?.favorited ?? lastTouch,
          userId,
          HKS: userDetails?.session_stringvalue
            ? userDetails?.session_stringvalue
            : loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue,
          user_hash: userDetails?.session_userhash,
          filterlist:
            filteredRegionLists?.[`${region}`]?.filterlist ??
            filteredRegionLists?.default?.filterlist
        })
      )
      localStorage.setItem('lasttouch', addWatchLists?.lasttouch?.favorited)
      watchListRef?.current?.focus()
      setCurrentButtonFocus('watchlist')
    }
  }, [addWatchLists])

  useEffect(() => {
    if (watchListData?.length > 0) {
      let res
      watchListData?.map(item => {
        if (moviesData?.id === item?.id || moviesData?.group_id === item?.id) {
          res = true
        }
      })
      setWatchList(res ?? false)
    }
  }, [watchListData])

  useEffect(() => {
    const element = document.getElementById(setVodReturnFocus)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
        block: 'center'
      })
      element.focus()
      setCurrentButtonFocus(setVodReturnFocus)
    }
    if (currentButtonFocus?.slice(0, 9) == 'castImage') {
      setCurrentButtonFocus('cast')
    }
  }, [showTalentModule])

  const handleMltDetails = (item,index) => {
    setMoviesData(item)
    let userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbar,
        content_list : handleTranslationchange('Vcard_CarruselRecomendaciones_TextoTitulo'),
        modulo_name : 'carrusel vcard',
        content_list_id : item?.id
      }
    pushContentSelectionEvent(userData,item,index,contentSelectionType.VCARD)
    if (watchFree) {
      dispatch(
        getVodSubscriptionInfo({
          is_kids: vodDetails?.confirmscreen
            ? loginInfo?.is_kids ?? registerInfo?.is_kids
            : userDetails?.is_kids,
          url: `group_id=${item?.id}`,
          type: 'watchfree'
        })
      )
        if(addSubscriptions?.playButton.payway_token) {
        dispatch(
          getMediaAPI({
            id: moviesData?.id || vodData?.id || vodData?.group_id,
            payway_token: addSubscriptions?.playButton.payway_token,
            HKS: userDetails?.session_stringvalue
              ? userDetails?.session_stringvalue
              : loginInfo?.session_stringvalue ??
                registerInfo?.session_stringvalue,
            userId: userDetails?.user_id,
            user_token: loginInfo?.user_token,
            streamType
          })
        )
      }
    } else {
      dispatch(
        getVodSubscriptionInfo({
          userId: vodDetails?.confirmscreen
            ? loginInfo?.parent_id ?? registerInfo?.parent_id
            : userDetails?.parent_id,
          hks: vodDetails?.confirmscreen
            ? loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue
            : userDetails?.session_stringvalue,
          is_kids: vodDetails?.confirmscreen
            ? loginInfo?.is_kids ?? registerInfo?.is_kids
            : userDetails?.is_kids,
          url: `group_id=${item?.id}`
        })
      ),
        dispatch(getVodID(item?.id))
          if(addSubscriptions?.playButton.payway_token) {
        dispatch(
          getMediaAPI({
            id: moviesData?.id || vodData?.id || vodData?.group_id,
            payway_token: addSubscriptions?.playButton.payway_token,
            HKS: userDetails?.session_stringvalue
              ? userDetails?.session_stringvalue
              : loginInfo?.session_stringvalue ??
                registerInfo?.session_stringvalue,
            userId: userDetails?.user_id,
            user_token: loginInfo?.user_token,
            streamType
          })
        )
      }
    }
    vodDetailfetchApi(item?.id)
    if (buttonsInfo == 1 && !addSubscriptions?.listButtons?.button) {
      setPlayBtnClkFocus(true)
      setCurrentButtonFocus('play')
    }
  }

   useEffect(() => {
  // GA: Content details event
    vodMoviesCast && addSubscriptions && pushContentDetailsEvent({
      contentData:vodMoviesCast, 
      contentType:"movies",
      screenData:userDetails,
      contentSection:contentSection?.text,
      subscriptionInfo:addSubscriptions?.listButtons?.button?.[0]})
  },[vodMoviesCast,addSubscriptions])

  useEffect(() => {
     const moviesCast =
      vodMoviesCast?.external?.gracenote?.cast ??
      vodMoviesCast?.common?.extendedcommon?.roles?.role

    const genre =
      vodMoviesCast?.external?.gracenote?.genres ??
      vodMoviesCast?.common?.extendedcommon?.genres?.genre
    let genredata = []
    genre &&
      genre?.length > 0 &&
      genre?.map((item) => {
        if (item?.desc) {
          genredata.push(item?.desc)
        } else if (item) {
          genredata.push( item)
        }
      })
      let removedExtraGenre = genredata.slice(0,2)
      let filteredGenreData = removedExtraGenre.map((item=>{
        if(typeof item === 'string' && item?.endsWith(',')){
          return item?.slice(0,-1)
      }
      return item
      }))
    setGenreData(filteredGenreData?.join(', '))
    dispatch(setGenreDetails(filteredGenreData?.join(', ')))

    let detailsOnCast = []
    moviesCast?.map(item => {
      if (item?.role_name == 'Actor' || item?.role_name == 'Director') {
        item?.talents?.map(e => {
          detailsOnCast.push({
            ...e,
            image: e?.image,
            name: e?.first_name + ' ' + e?.last_name,
            role: item.role_name
          })
        })
      } else if (item?.name == 'Director' || item?.name == 'Actor') {
        item?.talents?.talent?.map(e => {
          detailsOnCast.push({
            ...e,
            image: e?.image,
            name: e?.name + ' ' + e?.surname,
            role: item.name
          })
        })
      }
    });
    const removeDuplicateCast = Array.from(new Set(detailsOnCast.map(obj => obj.id)))
    .map(id => {
    return detailsOnCast.find(obj => obj.id === id);
  });
    setCastDetail(removeDuplicateCast)
  }, [vodMoviesCast])

  const handleDownClick = e => {
    if (e.key === 'ArrowDown') {
      e.preventDefault()
      document.getElementById('recom')?.focus()
    }
  }

  const handleIconClick = value => {
    if (
      (securityPinCheck &&
        statusControlPin == 50 &&
        parseInt(age_rating?.code) > 18) ||
      (statusControlPin == 40 && parseInt(age_rating?.code) >= 16) ||
      (statusControlPin == 30 && parseInt(age_rating?.code) >= 13) ||
      (statusControlPin == 20 && parseInt(age_rating?.code) >= 7)
    ) {
      contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
      contentDataRef.current['content_section'] = PLAYER
      navigate('/my-settings/help-And-Settings/security-pin/configure', {
        state: {
          data: 'Create',
          item: vodData,
          pageName: 'movies',
          moviedata: vodData,
          getMediaData: getMediaRes,
          resume: value == 'begin' ? false : true,
          gaContentData: contentDataRef.current,
          backfocusid: state?.backfocusid
        }
      })
    } else if (value == 'resume') {
      setVideoPlayerWarning(true)
    } else if (
      watchFree &&
      vodData?.format_types !== 'free' &&
      vodData?.extendedcommon?.format?.types !== 'free' &&
      vodData?.extendedcommon?.format?.types !== 'free,download' &&
      vodData?.format_types !== 'free,download' && 
      vodData?.contentAttributes?.marketingType !== 'free' &&
       vodData?.contentAttributes?.marketingType !== 'free,download'
    ) {
      navigate('/EPconfirmation', {
        state: { vodMoviesData: vodData, page: 'movies', pageName: '/movies', gaContentData: contentDataRef.current }
      })
    } else {
      navigate('/vodPlayer', {
        state: {
          data: vodMoviesCast?.common,
          episodeData: '',
          getMedia: getMediaRes,
          resume: value == 'begin' ? false : true,
          inputValue: state?.inputValue,
          returnPage: state?.returnPage ?? '',
          backfocusid: state?.backfocusid,
          vCardBackFocus: 'play'
        }
      })
    }
  }

  const handleTrailer = () => {
    const code = getTrailerMediaError?.code
    if (code) {
      navigate('/GeneralError', {
        state: { vcardMovies: vodData, vcardType: 'movies' }
      })
    } else {
      trailerRedux &&
        navigate('/vodPlayer', {
          state: {
            data: vodMoviesCast?.common,
            episodeData: '',
            trailerGetMedia: trailerRedux,
            inputValue: state?.inputValue,
            returnPage: state?.returnPage ?? ' ',
            backfocusid: state?.backfocusid,
            vCardBackFocus: 'trailer'
          }
        })
    }
  }

  const handleCheckPlayerStatus = () => {
    const code = getMediaError?.code
    if (
      code == 'PLY_DEV_00006' ||
      code == 'PLY_CSS_00001' ||
      code == 'PLY_CSS_00004'
    ) {
      const data = {
        errFromPlayer: false,
        errorMessage1:
          code == 'PLY_DEV_00006'
            ? (apilanguage?.PLY_DEV_00006_title ?? 'PLY_DEV_00006_title').slice(
                0,
                19
              )
            : (apilanguage?.PLY_CSS_00001_title ?? 'PLY_CSS_00001_title').slice(
                0,
                19
              ),
        errorMessage2:
          code == 'PLY_DEV_00006'
            ? (apilanguage?.PLY_DEV_00010 ?? 'PLY_DEV_00010').slice(0, 19)
            : (apilanguage?.PLY_CSS_00001 ?? 'PLY_CSS_00001').slice(0, 19),
        errorType: code == 'PLY_DEV_00006' ? 'device' : 'playback'
      }
      setVodSimultaneous(data)
      return
    }
    if (lastSeenPercentage > 0 && lastSeenPercentage !== 100) {
      handleIconClick('resume')
      setCurrentButtonFocus('resume')
    } else {
      handleIconClick('begin')
    }
  }
  //event checking
  const handleButtonFocus = event => {
    currentButtonFocus == 'play'
      ? setPlayBtnClkFocus(true)
      : setPlayBtnClkFocus(false)
    currentButtonFocus == 'trailer'
      ? setTrailerBtnClkFocus(true)
      : setTrailerBtnClkFocus(false)
    currentButtonFocus == 'watchlist' || currentButtonFocus == 'addToWatchlist'
      ? setWatchlistButtonClickFocus(true)
      : setWatchlistButtonClickFocus(false)
    currentButtonFocus == 'subscriptionPreventa'
      ? setSubscriptionPreventaFocus(true)
      : setSubscriptionPreventaFocus(false)
    currentButtonFocus == 'subscriptionRentar'
      ? setSubscriptionRentarFocus(true)
      : setSubscriptionRentarFocus(false)
    currentButtonFocus == 'subscriptionComprar'
      ? setSubscriptionComprarFocus(true)
      : setSubscriptionComprarFocus(false)
    currentButtonFocus == 'channelSubscription'
      ? setChannelSubscriptionFocus(true)
      : setChannelSubscriptionFocus(false)
    currentButtonFocus == 'subscription'
      ? setSubscriptionFocus(true)
      : setSubscriptionFocus(false)
  }

  const handleRecomendationTab = () => {
    setRecomendationTab(true), setMoreInfoTab(false), setHideVodDetailPage(true)
    setIsOnClickRecome(false)
    setRecomendationFlag(true)
  }

  const handleMoreInfoTab = () => {
    setMoreInfoTab(true), setRecomendationTab(false), setHideVodDetailPage(true)
    setIsOnClickRecome(true)
    setRecomendationFlag(false)
    setMoreinfoFocusRed(true)
    setRecomendFocusRed(false)
    setCurrentButtonFocus('moreinfo')
  }

  const handleAgerating = rating => {
    if (rating?.id == '10') {
      return 'Todos'
    } else if (rating?.id == '20' && rating?.code == '+7') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.id == '30' && rating?.code == '+12') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.id == '40' && rating?.code == '+16') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.id == '50' && rating?.code == '+18') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.code) {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    }
  }

  const formatDuration = duration => {
    let hour =
      duration?.slice(0, 2) !== '00'
        ? parseInt(duration?.slice(0, 2), 10) + ' h '
        : ''
    let minutes =
      duration?.slice(3, 5) !== '00'
        ? parseInt(duration?.slice(3, 5), 10) + ' min '
        : ''
    return hour + minutes
  }

  const handleFocusredRecom = event => {
    if (event.code == 'Enter' && event.keyCode == 13) {
      setRecomendFocusRed(true)
      setMoreinfoFocusRed(false)
    } else if (event.keyCode === 38) {
      upBackKeyFocus.current = true
      handleBackInfoTab()
      handleDefaultFocus()
    }
  }

  const handleFocusredMoreInfo = event => {
    if (event.code == 'Enter' && event.keyCode == 13) {
      setRecomendFocusRed(false)
      setMoreinfoFocusRed(true)
    } else if (event.keyCode === 38) {
      upBackKeyFocus.current = true
      handleBackInfoTab()
      handleDefaultFocus()
    }
  }

  const handleTranslationchange = useCallback(keyname => {
    return !apilanguage?.[keyname] ? keyname : apilanguage?.[keyname]
  }, [])

  const truncateText = (str, length, ending) => {
    if (length == null) {
      length = 100
    }
    if (ending == null) {
      ending = '...'
    }
    if (str?.length > length) {
      return str?.substring(0, length - ending?.length) + ending
    } else {
      return str
    }
  }

  const getPromotitle = data => {
    return apilanguage?.[`transactional_${data}_subscription_vcard_description`] //checking the data is there in apa/assets
  }

  const getPromolabel = data => {
    return apilanguage?.[`${data}_access_commercialInfo_label`]
  }
  const getPromoFamily = data => {
    return apilanguage?.[`${data}`]
  }

  const getPromoFamilySlash = (producttype,periodicity) => {
    return apilanguage?.[`subscription_${producttype}_periodicitySeparator_${periodicity}_label`] 
  }

  const settingLogoUrl = data => {
    return apaAssetsImages?.[
      `Transaccionales_Suscripcion_ImagenLogoCanal_${data}`
    ] //checking the data is there in apa/assets
  }

  const getTaxLabel = data => {
    return apilanguage?.[
      `${data}_subscriptionDescription_costTaxIncluded_label`
    ]
  }

  const getFreeChargeString = data => {
    return apilanguage?.[
      `transactional_${data}_subscription_plan_tryInvite_description${data}`
    ]
  }

  const getSubscribeButton = data => {
    return apilanguage?.['buy_' + data]
  }

  const getViewDetailsButton = data => {
    return apilanguage?.['includes_' + data]
  }

  useEffect(() => {
    let accountInfo = loginInfo || registerInfo || userDetails
    subscriptionKeyRef.current =
      accountInfo?.paywayProfile?.subscriptions?.reduce(
        (resultant, value, index) =>
          index == 0 ? resultant + value?.key : resultant + ', ' + value?.key,
        ''
      )
    dispatch(clearGetMediaRes())
    setStreamType(
      !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
    handleDefaultFocus()
    dispatch(setShowApiFailureModal(false))
    dispatch(setShowTalentModule(false))
    return () => {
      setRecomendationFlag(false)
      setRecomendationTab(true)
      setIsOnClickRecome(false)
      setMoreInfoTab(false)
      setVideoPlayerWarning(false)
      setHideVodDetailPage(false)
      setTrailerBtnClkFocus(false)
      setPlayBtnClkFocus(false)
      setWatchlistButtonClickFocus(false)
      setSubscriptionFocus(false)
      setChannelSubscriptionFocus(false)
      setSubscriptionComprarFocus(false)
      setSubscriptionRentarFocus(false)
      setSubscriptionPreventaFocus(false)
      setRecomendFocusRed(false)
      setMoreinfoFocusRed(false)
      setContentData([])
      setWatchListData([])
      setWatchList(false)
      setMoviesData('')
      setGenreData([])
      setCastDetail([])
      setCurrentButtonFocus('')
      setViewDetailButton('')
      setViewRentButton('')
      setViewBuyButton('')
      setButtonsInfo('')
      setPromoLabel('')
      setFamilyPromoLabel('')
      setproductType(''),
      setPeriodicity(''),
      setPriceMonth('')
      setSymbolCurrency('')
      dispatch(getClearVodState())
      dispatch(clrSubscriptionInfo())
      dispatch(getCleartalentState())
      dispatch(setShowTalentModule(false))
      dispatch(removeVodReturnFocusById())
      dispatch(setVodReturnFocusById())
      contentDataRef.current = null
    }
  }, [])

  const handleGenreChar = genrechar => {
    let GenrechartTitle = genrechar || ''
    const maxCharacters = 27 // Total character limit for two lines
    const parts = GenrechartTitle.split(', ') // Split the string by commas
    const result = parts.map((part, index) =>
      index < parts.length - 1 ? part.trim() + ', ' : part.trim()
    ) // Add space after each part (except the last one)
    let resultchar = result.join('')
    if (resultchar.length > maxCharacters) {
      // Truncate content to fit within two lines
      const truncatedText = resultchar.substring(0, maxCharacters)
      // Update the truncated content
      resultchar = truncatedText + '...'
    }
    return resultchar
  }

  const handleMainTitle = tle => {
    let largecontentTitle = tle || ''
    const maxCharacters = 50 // Total character limit for two lines
    // Check if content length exceeds the limit
    if (largecontentTitle.length > maxCharacters) {
      // Truncate content to fit within two lines
      const truncatedText = largecontentTitle.substring(0, maxCharacters)
      // Find the last space within the truncated text to avoid cutting off words
      const lastSpaceIndex = truncatedText.lastIndexOf(' ')
      // Update the truncated content
      largecontentTitle = truncatedText.substring(0, lastSpaceIndex) + '...'
    }
    return largecontentTitle
  }

  const handleViewDetailSubscription = (
    e,
    data,
    subscriptionType,
    index = 0
  ) => {
    // if (
    //   watchFree &&
    //   vodMoviesCast?.common?.extendedcommon?.format?.types !==
    //     'free,download' &&
    //   vodMoviesCast?.common?.extendedcommon?.format?.types !== 'free'
    // ) {
    //   navigate('/EPconfirmation', {
    //     state: { vodMoviesData: vodData, page: 'movies' }
    //   })
    // } else {

    //commenting the above lines for timebeing
    e.preventDefault()
    let monthData
    monthData = data?.[index]
    const viewPlan = {
      verticalImage:
        moviesData?.image_medium || vodData?.image_medium || moviesData?.images?.medium || vodData?.images?.medium,
      workflowStart: monthData.linkworkflowstart,
      logo: settingLogoUrl(monthData?.family),
      title:
        moviesData?.title ??
        vodMoviesCast?.external?.gracenote?.title ??
        vodMoviesCast?.common?.title,
      family: monthData?.family,
      periodicity: monthData?.periodicity,
      price: monthData?.price,
      currency: monthData?.currency,
      styles: monthData?.style,
      taxLabel: getTaxLabel(monthData?.family),
      infoString: getFreeChargeString(monthData?.bonus),
      subscribeButton: getSubscribeButton(monthData?.family),
      viewButton: getViewDetailsButton(monthData?.family),
      producttype: monthData?.producttype,
      offertype: monthData?.oneoffertype,
      frequency: monthData?.frequency,
      isSeries: false
    }
    dispatch(getViewSubscribeData(viewPlan))
    subscriptionType == 'longTermSub'
      ? navigate(
          '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
          {
            state: {
              pageName: '/movies',
              vodData: vodData ?? vodMoviesCast?.common
            }
          }
        )
      : watchFree
      ? navigate('/EPconfirmation', {
          state: {
            vodMoviesData: vodData ?? vodMoviesCast?.common,
            page: 'movies',
            pageName: '/movies',
            gaContentData: contentDataRef.current
          }
        })
      : (dispatch(
          getFinishSubscriptionPlayerData({
            data: vodMoviesCast?.common,
            episodeData: '',
            inputValue: state?.inputValue
          })
        ),
        navigate(
          '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          { state: { pageName: '/movies', vodData: moviesData || vodData } }
        ))
    // }
  }

  const handleGAInteractionEvent = (interactionType) => {
    pushInteractionContentEvent({
      contentData:vodMoviesCast,
      interactionType:interactionType,
      contentType:"movies",
      screenData:userDetails,
      contentSection:contentSection?.text,
      subscriptionInfo:addSubscriptions?.listButtons?.button?.[0]
    })
  }

 const handleGAAddSubscriptionEvent = (interactionType) => {
    pushAddSubscriptionEvent({
      contentData:vodMoviesCast,
      interactionType:interactionType,
      contentType:"movies",
      screenData:userDetails,
      contentSection:contentSection?.text,
      subscriptionInfo:addSubscriptions?.listButtons?.button?.[0]
    })
  }

  return !buttonsInfo ||
    !vodMoviesCast ||
    !vodMoviesCast?.common ||
    !vodMoviesCast?.common?.extendedcommon ||
    !addSubscriptions ||
    !vodData ||
    !castDetail ||
    (addSubscriptions?.listButtons?.button
      ? !addSubscriptions?.listButtons?.button
      : false) ? (
    <>
      <Navbar page={'voddetail'} disableFocus={true} myContentData={true} />
      <div className="vod-container-loader">
        <Lottie
          options={{
            rendererSettings: {
              preserveAspectRatio: 'xMidYMid slice'
            }
          }}
          loop
          animationData={animationData}
          play
        />
      </div>
    </>
  ) : (
    <>
      {videoplayerWarning && !vodSimultaneous?.errorType ? (
        <PlayerModelSelection
          handleBackInfoTab={handleBackInfoTab}
          handleIconClick={handleIconClick}
          keyParam={'movies'}
          id={'videoPlayerWarnings'}
          gaContentData={contentDataRef.current}
        />
      ) : vodSimultaneous?.errorType ? (
        <PlayerErrorHandler
          retry={setPlaybackRetry}
          closePopup={setVodSimultaneous}
          data={vodSimultaneous}
        />
      ) : showTalentModule ? (
        <TalentSearch
          filterlist={filterlist}
          talentSearchData={talentSearchData}
          talentSearchfield={talentSearchfield}
          Addproveedor={addProveedor}
          setCurrentButtonFocus={setCurrentButtonFocus}
          userDeatilResp={userDetails}
          currentButtonFocus={currentButtonFocus}
          keyParam={'vodMovies'}
        />
      ) : showApiFailureModal ? (
        <ErrorEventModule
          handlebackButton={handleBackInfoTab}
          pageName={'vodMovies'}
          setCurrentButtonFocus={setCurrentButtonFocus}
        />
      ) : (
        <>
          {!videoplayerWarning && (
            <Navbar
              page={'voddetail'}
              disableFocus={false}
              myContentData={true}
            />
          )}
          <div className="vod-layout">
            {!hideVodDetailPage ? (
              <>
                <div
                  className="back-ground-images"
                  style={{
                    backgroundImage: `url(${vodMoviesCast?.common?.image_base_horizontal}),linear-gradient(180deg, rgba(18,18,18,0) 0%, rgba(18,18,18,0.08) 58.3%, rgba(18,18,18,0.7) 100%), linear-gradient(270deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0) 0.08%, rgba(18,18,18,0.8) 35.98%, rgba(18,18,18,0.9) 45.73%, #121212 55.25%, #000000 100%)`
                  }}
                  id="background-movie-image"
                ></div>
                <div className="vod-details-layout">
                  <div className="content-layout">
                    <div
                      className={
                        vodMoviesCast?.common?.title?.length > 35
                          ? 'content-title1'
                          : 'content-title'
                      }
                    >
                      {handleMainTitle(vodMoviesCast?.common?.title)}
                    </div>
                    <div
                      className={
                        vodMoviesCast?.common?.title?.length > 35
                          ? 'vod-margin-layouts'
                          : 'vod-season-details'
                      }
                    >
                      <div className="lineHeight">
                        <span className="content-name">
                          {
                            vodMoviesCast?.common?.extendedcommon?.media
                              ?.originaltitle
                          }
                        </span>
                        {vodMoviesCast?.common?.extendedcommon?.media
                          ?.originaltitle?.length > 35 ? (
                          <br />
                        ) : (
                          <span className="pipe">|</span>
                        )}
                        <span className="vod-genero">{genreData}</span>
                        <span className="vod-duration-year">
                          {
                            vodMoviesCast?.common?.extendedcommon?.media
                              ?.publishyear
                          }
                        </span>
                        <span className="stroke vod-duration-ano">
                          {handleAgerating(age_rating)}
                        </span>
                        <span className="duracion-ano-copy ">
                          {formatDuration(vodMoviesCast?.common?.duration)}
                        </span>
                      </div>
                    </div>

                    <div
                      className={
                        vodMoviesCast?.common?.title?.length > 30
                          ? 'vod-description-length-layouts'
                          : 'vod-description-layouts'
                      }
                    >
                      <div className="vod-description">
                        {truncateText(vodMoviesCast?.common?.description, 180)}
                      </div>
                    </div>
                    {contentData?.playButton?.visible == 1 ? (
                      lastSeenPercentage > 0 &&
                      lastSeenPercentage !== 100 &&
                      userId && (
                        <div className="ltv-progressbar-position">
                          <ProgressBar
                            style={{ visibility: 'visible' }}
                            isLoading={false}
                            percent={lastSeenPercentage}
                            size={'large'}
                            showInfo={true}
                            serieslargeProgress={serieslargeProgress}
                            sliderWidth={1045}
                          />
                        </div>
                      )
                    ) : viewDetailButton ? (
                      <div className="vod-promo-card">
                        <span className="vod-promo-first-title">
                          {`${getPromolabel(
                            familyPromoLabel
                          )}${' '}${symbolCurrency}${priceMonth}${
                            priceMonth ? `${getPromoFamilySlash(producttype,periodicity)}${apilanguage?.[`${periodicity}`]}` : ''
                          }`}
                        </span>
                        <span className="vod-promo-second-title">
                          {' '}
                          {!getPromoFamily(promoLabel)
                            ? null
                            : `${getPromotitle(
                                promoLabel
                              )}${' '}${getPromoFamily(promoLabel)}`}
                        </span>
                      </div>
                    ) : null}

                    <div className="button-layout" id="button-layout">
                      <>
                        {subscribtionButton &&
                        subscribtionButton?.length > 0 &&
                        addSubscriptions?.playButton?.visible == 0 &&
                        (subscribtionButton[0]?.periodicity ==
                          ('month' || 'year') ||
                          subscribtionButton[0]?.oneoffertype ==
                            'subscrition') ? (
                          <button
                            className="button-chanelsubscription-icons focusable"
                            onFocus={() => {
                              setCurrentButtonFocus('channelSubscription')
                            }}
                            autoFocus={
                              viewDetailButton && viewRentButton
                                ? false
                                : true || viewDetailButton
                                ? true
                                : false
                            }
                            onKeyDown={e => handleDownClick(e)}
                            data-testid="channelSubscription"
                            data-sn-down={'#recom'}
                            onClick={e => {
                              handleViewDetailSubscription(
                                e,
                                addSubscriptions?.listButtons?.button,
                                'longTermSub',
                                subscribtionButton?.length - 1
                              ),
                                handleButtonFocus(e),
                                handleGAAddSubscriptionEvent(interactionType.VER_CON_CLARO_VIDEO_INTERACTION_TYPE)
                            }}
                            id="longTermSubscription"
                          >
                            <div className={'button-chanelsubscription-copy1'}>
                              <div className={'textPosition'}>
                                <span className="chanelsubscription-yellow-button">
                                 {`${truncateText( apilanguage?.[`${familyPromoLabel}_access_title_button_subscriptionDescription`] || apilanguage?.[`${familyPromoLabel?.toUpperCase()}_access_title_button_subscriptionDescription`] || '_access_title_button_subscriptionDescription',50)}` }
                                </span>
                                <span className="chanelsubscription-yellow-button-detail">
                                 {`${truncateText(apilanguage?.[`${familyPromoLabel}_access_description_button_subscriptionDescription`] || apilanguage?.[`${familyPromoLabel?.toUpperCase()}_access_description_button_subscriptionDescription`] || '_access_description_button_subscriptionDescription',30)}` }
                                </span>
                              </div>
                            </div>
                          </button>
                        ) : null}
                        {subscribtionButton &&
                          subscribtionButton?.length > 0 &&
                          subscribtionButton?.map((buttonInfo, index) => (
                            <>
                              {viewDetailButton &&
                              (buttonInfo?.periodicity == 'month' ||
                                buttonInfo?.periodicity == 'year') &&
                              buttonInfo?.oneoffertype == 'subscrition' &&
                              contentData?.playButton?.visible == 0 ? (
                                <>
                                  <button
                                    className="button-subscription-icons focusable"
                                    onClick={event => {
                                      handleButtonFocus(event),
                                        handleViewDetailSubscription(
                                          event,
                                          addSubscriptions?.listButtons?.button,
                                          'shortTermSub',
                                          index
                                        ),
                                     handleGAAddSubscriptionEvent(interactionType.SUBSCRIPTION_INTERACTION_TYPE)
                                    }}
                                    onFocus={() =>
                                      setCurrentButtonFocus('subscription')
                                    }
                                    data-testid="subscriptionbutton"
                                    data-sn-down={'#recom'}
                                    key={index}
                                  >
                                    <div
                                      style={{ backgroundColor: '#4C6FA7' }}
                                      className={
                                        subscriptionFocus
                                          ? 'button-subscription-copy1'
                                          : 'button-subscription-copy2'
                                      }
                                    >
                                      <div className="sub-button-inline-layout">
                                        {' '}
                                        <span 
                                        className={
                                            getTaxLabel(buttonInfo?.producttype)?.trim() !== ''
                                                ? 'subscription-price'
                                                : 'subscription-price-alone'
                                            }
                                          >
                                          {buttonInfo?.currency}
                                          {buttonInfo?.price
                                            ? buttonInfo?.price
                                            : ' '}
                                        </span>
                                        <span className="subscription-price-detail">
                                          {getTaxLabel(buttonInfo?.producttype)}
                                        </span>
                                      </div>
                                    </div>
                                    <p
                                      className={
                                        currentButtonFocus === 'subscription'
                                          ? 'button-text-sub'
                                          : 'button-textfocus-out-sub'
                                      }
                                    >
                                      {handleTranslationchange(
                                        buttonInfo?.oneofferdesc
                                      )}
                                    </p>
                                  </button>
                                </>
                              ) : null}

                              {buttonInfo?.periodicity == 'hour' &&
                              (buttonInfo?.oneoffertype == 'buy' ||
                                buttonInfo?.oneoffertype == 'download_buy') &&
                              contentData?.playButton?.visible == 0 ? (
                                <button
                                  className="button-subscription-icons focusable"
                                  onClick={event => {
                                    handleButtonFocus(event),
                                      handleViewDetailSubscription(
                                        event,
                                        addSubscriptions?.listButtons?.button,
                                        'shortTermSub',
                                        index
                                      ),
                                      handleGAAddSubscriptionEvent(interactionType.COMPRAR_INTERACTION_TYPE)
                                  }}
                                  onFocus={() =>
                                    setCurrentButtonFocus('subscriptionComprar')
                                  }
                                  autoFocus={viewbuyButton ? true : false}
                                  onKeyDown={e => handleDownClick(e)}
                                  data-testid="subscriptionbutton"
                                  data-sn-down={'#recom'}
                                >
                                  <div
                                    style={{ backgroundColor: '#477F9B' }}
                                    className={
                                      subscriptionComprarFocus
                                        ? 'button-subscription-copy1'
                                        : 'button-subscription-copy2'
                                    }
                                  >
                                    <div className="sub-button-inline-layout">
                                      {' '}
                                      <span
                                       className={
                                            getTaxLabel(buttonInfo?.producttype)?.trim() !== ''
                                                ? 'subscription-price'
                                                : 'subscription-price-alone'
                                            }
                                          >
                                        {buttonInfo?.currency
                                          ? buttonInfo?.currency
                                          : '$'}
                                        {buttonInfo?.price
                                          ? buttonInfo?.price
                                          : ' '}
                                      </span>
                                      <span className="subscription-price-detail">
                                        {getTaxLabel(buttonInfo?.producttype)}
                                      </span>
                                    </div>
                                  </div>
                                  <p
                                    className={
                                      currentButtonFocus ===
                                      'subscriptionComprar'
                                        ? 'button-text-sub'
                                        : 'button-textfocus-out-sub'
                                    }
                                  >
                                    {handleTranslationchange(
                                      buttonInfo?.oneofferdesc
                                    )}
                                  </p>
                                </button>
                              ) : null}

                              {viewRentButton == true &&
                              buttonInfo?.periodicity == 'hour' &&
                              (buttonInfo?.oneoffertype == 'download_rent' ||
                                buttonInfo?.oneoffertype == 'rent') &&
                              contentData?.playButton?.visible == 0 && (!buttonInfo?.oneofferdesckey?.includes('promogate' || buttonInfo?.oneofferdesckey != 'offer_button_desc_subscription_cv_stdrent_promogate' )) ? (
                                <button
                                  className="button-subscription-icons focusable"
                                  onClick={event => {
                                    handleButtonFocus(event),
                                      handleViewDetailSubscription(
                                        event,
                                        addSubscriptions?.listButtons?.button,
                                        'shortTermSub',
                                        index
                                      ),
                                    handleGAAddSubscriptionEvent(interactionType.RENTAR_INTERACTION_TYPE)
                                  }}
                                  onFocus={() =>
                                    setCurrentButtonFocus('subscriptionRentar')
                                  }
                                  autoFocus={
                                    !viewbuyButton && viewRentButton
                                      ? true
                                      : false ||
                                        (viewDetailButton && viewRentButton)
                                      ? true
                                      : false
                                  }
                                  onKeyDown={e => handleDownClick(e)}
                                  id="rentalSubscription"
                                  data-testid="subscriptionRentar"
                                  data-sn-down={'#recom'}
                                >
                                  <div
                                    style={{ backgroundColor: '#8A4CA7' }}
                                    className={
                                      subscriptionRentarFocus
                                        ? 'button-subscription-copy1'
                                        : 'button-subscription-copy2'
                                    }
                                  >
                                    <div className="sub-button-inline-layout">
                                      {' '}
                                      <span 
                                       className={
                                            getTaxLabel(buttonInfo?.producttype)?.trim() !== ''
                                                ? 'subscription-price'
                                                : 'subscription-price-alone'
                                            }
                                          >
                                        {buttonInfo?.currency
                                          ? buttonInfo?.currency
                                          : '$'}
                                        {buttonInfo?.price
                                          ? buttonInfo?.price
                                          : ' '}
                                      </span>
                                      <span className="subscription-price-detail">
                                        {getTaxLabel(buttonInfo?.producttype)}
                                      </span>
                                    </div>
                                  </div>
                                  <p
                                    className={
                                      currentButtonFocus ===
                                      'subscriptionRentar'
                                        ? 'button-text-sub'
                                        : 'button-textfocus-out-sub'
                                    }
                                  >
                                    {(buttonInfo?.oneofferdesc).replace(
                                      'h',
                                      ' h'
                                    )}
                                  </p>
                                </button>
                              ) : null}
                            </>
                          ))}
                      </>
                      {/* no need for now */}

                      {/* <button className="focusable" onClick={(event) => { handleButtonFocus(event) }}
                          onFocus={() => setCurrentButtonFocus('subscriptionPreventa')}
                          data-testid='subscriptionPreventa'
                          onKeyDown={(event) => handleButtonUnfocus(event)}
                          ref={subscriptionPreventaRef?.current?.focus()}

                        >
                          <div style={{ backgroundColor: '#269B87' }} className={subscriptionPreventaFocus ? 'button-subscription-copy1 focusable' : currentButtonFocus == 'subscriptionPreventa' ? 'button-subscription-reproducer focusable focus-img' : "button-subscription-icons focusable focus-img"}>
                            <div className='sub-button-inline-layout'>  <span className='subscription-price'>$119.00</span>
                              <span className='subscription-price-detail'>IVA incluido</span>
                            </div>
                            <p className={subscriptionPreventaFocus || currentButtonFocus === 'subscriptionPreventa' ? "button-text" : "button-textfocus-out"}>Preventa</p>
                          </div>
                        </button> */}
                      {/* sub end.................................. */}

                      {buttonsInfo == 1 && (
                        <button
                          className="button-icons focusable"
                          autoFocus={
                            playBtnClkFocus ||
                            state?.vCardBackFocus == 'play' ||
                            (buttonsInfo == 1 &&
                              state?.vCardBackFocus != 'trailer')
                          }
                          onKeyDown={e => handleDownClick(e)}
                          id="playButton"
                          onFocus={() => setCurrentButtonFocus('play')}
                          data-testid="playbutton"
                          onClick={event => {
                            handleCheckPlayerStatus()
                            handleButtonFocus(event)

                            // GA: Interaction Content Event
                            handleGAInteractionEvent(interactionType.PLAY_INTERACTION_TYPE)
                          }}
                          data-sn-down={'#recom'}
                        >
                          <div
                            className={
                              playBtnClkFocus
                                ? 'button-reproducer-copy1'
                                : 'button-icons-trailer'
                            }
                          >
                            {!playBtnClkFocus ? (
                              <img
                                className="icons-vcards"
                                src={'images/Vcard_Icons/play_button_white.png'}
                                placeholderSrc={
                                  'images/Vcard_Icons/play_button_white.png'
                                }
                              />
                            ) : (
                              <img
                                className="icons-vcards"
                                src={'images/Vcard_Icons/play-reproducera.png'}
                                placeholderSrc={
                                  'images/Vcard_Icons/play-reproducera.png'
                                }
                              />
                            )}
                          </div>
                          <p
                            className={
                              playBtnClkFocus || currentButtonFocus === 'play'
                                ? 'button-text'
                                : 'button-textfocus-out'
                            }
                          >
                            {handleTranslationchange(
                              'vcard_access_option_button_play'
                            )}
                          </p>
                        </button>
                      )}
                      {trailerButtonEnable && (
                        <button
                          className="button-icons focusable"
                          onClick={event => {
                            handleTrailer()
                            handleButtonFocus(event)
                            // GA: Interaction Content Event
                            handleGAInteractionEvent(interactionType.TRAILER_INTERACTION_TYPE)
                          }}
                          onFocus={() => setCurrentButtonFocus('trailer')}
                          autoFocus={
                            (state?.vCardBackFocus == 'trailer' &&
                              !playBtnClkFocus) ||
                            (buttonsInfo == 0 &&
                              !viewRentButton &&
                              !viewbuyButton &&
                              !viewDetailButton)
                          }
                          onKeyDown={e => handleDownClick(e)}
                          id="trailerButton"
                          data-testid="trailerbutton"
                          data-sn-down={'#recom'}
                        >
                          <div
                            className={
                              trailerBtnClkFocus
                                ? 'button-reproducer-copy1'
                                : 'button-icons-trailer'
                            }
                          >
                            {!trailerBtnClkFocus ? (
                              <img
                                className="icons-vcards"
                                src={'images/Vcard_Icons/trailer.png'}
                                placeholderSrc={
                                  'images/Vcard_Icons/trailer.png'
                                }
                              />
                            ) : (
                              <img
                                className="icons-vcards"
                                src={'images/Vcard_Icons/trailer_black.png'}
                                placeholderSrc={
                                  'images/Vcard_Icons/trailer_black.png'
                                }
                              />
                            )}
                          </div>
                          <p
                            className={
                              currentButtonFocus === 'trailer'
                                ? 'button-text'
                                : 'button-textfocus-out'
                            }
                          >
                            {handleTranslationchange(
                              'vcard_access_option_button_trailer'
                            )}
                          </p>
                        </button>
                      )}
                      {watchList ? (
                        <button
                          onClick={event => {
                            handleWatchListDel(), handleButtonFocus(event),
                            handleGAInteractionEvent(interactionType.REMOVE_LIST_INTERACTION_TYPE)
                          }}
                          data-testid="delWatchListTest"
                          autoFocus={
                            buttonsInfo == 0 &&
                            !addSubscriptions?.listButtons &&
                            !trailerBtnClkFocus
                              ? true
                              : false
                          }
                          onKeyDown={e => handleDownClick(e)}
                          ref={watchListRef}
                          className={
                            userId && userDetails
                              ? 'button-icons focusable'
                              : ''
                          }
                          style={{
                            visibility:
                              userId &&
                              (vodDetails?.confirmscreen
                                ? loginInfo ?? registerInfo
                                : userDetails)
                                ? ''
                                : 'hidden'
                          }}
                          onFocus={() => setCurrentButtonFocus('watchlist')}
                          onBlur={() => {
                            setCurrentButtonFocus('')
                          }}
                          data-sn-down={'#recom'}
                          data-sn-right={true}
                        >
                          <div
                            className={
                              watchlistButtonClickFocus
                                ? 'button-reproducer-copy1'
                                : 'button-icons-trailer'
                            }
                          >
                            {!watchlistButtonClickFocus ? (
                              <img
                                className="icons-vcards"
                                src={
                                  'images/Vcard_Icons/Icons_Vcards_minus.png'
                                }
                                placeholderSrc={
                                  'images/Vcard_Icons/Icons_Vcards_minus.png'
                                }
                              />
                            ) : (
                              <img
                                className="icons-vcards"
                                src={
                                  'images/Vcard_Icons/removeWatchlist_black.png'
                                }
                                placeholderSrc={
                                  'images/Vcard_Icons/removeWatchlist_black.png'
                                }
                              />
                            )}
                          </div>
                          <p
                            className={
                              watchlistButtonClickFocus ||
                              currentButtonFocus === 'watchlist'
                                ? 'button-text'
                                : 'button-textfocus-out'
                            }
                          >
                            {handleTranslationchange(
                              'playing_playerControls_toolbar_button_deleteList'
                            )
                              .toLowerCase()
                              .replace(/^q/, 'Q')
                              .replace(/(\s)m/, ' M')}
                          </p>
                        </button>
                      ) : (
                        <button
                          onClick={event => {
                            handleWatchListAdd(), handleButtonFocus(event),
                            handleGAInteractionEvent(interactionType.ADD_LIST_INTERACTION_TYPE)
                          }}
                          data-testid="addwatchlisttest"
                          onFocus={() =>
                            setCurrentButtonFocus('addToWatchlist')
                          }
                          autoFocus={
                            buttonsInfo == 0 &&
                            !addSubscriptions?.listButtons &&
                            !trailerBtnClkFocus
                              ? true
                              : false
                          }
                          onKeyDown={e => handleDownClick(e)}
                          ref={watchListRef}
                          className={
                            userId && userDetails
                              ? 'button-icons focusable'
                              : ''
                          }
                          style={{
                            visibility:
                              userId &&
                              (vodDetails?.confirmscreen
                                ? loginInfo ?? registerInfo
                                : userDetails)
                                ? ''
                                : 'hidden'
                          }}
                          data-sn-down={'#recom'}
                          data-sn-right={true}
                        >
                          <div
                            className={
                              watchlistButtonClickFocus
                                ? 'button-reproducer-copy1'
                                : 'button-icons-trailer'
                            }
                          >
                            {!watchlistButtonClickFocus ? (
                              <img
                                className="icons-vcards"
                                src={'images/Vcard_Icons/Icons_Vcards_Add.png'}
                                placeholderSrc={
                                  'images/Vcard_Icons/Icons_Vcards_Add.png'
                                }
                              />
                            ) : (
                              <img
                                className="icons-vcards"
                                src={
                                  'images/Vcard_Icons/addwatchlist_Black.png'
                                }
                                placeholderSrc={
                                  'images/Vcard_Icons/addwatchlist_Black.png'
                                }
                              />
                            )}
                          </div>
                          <p
                            className={
                              watchlistButtonClickFocus ||
                              currentButtonFocus === 'addToWatchlist'
                                ? 'button-text'
                                : 'button-textfocus-out'
                            }
                          >
                            {handleTranslationchange(
                              'playing_playerControls_toolbar_button_addList'
                            )
                              .toLowerCase()
                              .replace(/^a/, 'A')
                              .replace(/(\s)m/, ' M')}
                          </p>
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="tab-first-tray-wrapper" id={'recom-tab-one'}>
                  <button
                    autoFocus={currentButtonFocus === 'recomendation'}
                    onFocus={() => {
                      setCurrentButtonFocus('recomendation')
                    }}
                    onKeyDown={event => {
                      handleScrolling(event)
                    }}
                    data-testid="playbutton"
                    className={
                      recomendationTab
                        ? 'recommended-tab-focus focusable'
                        : defaultRecomendRed
                        ? 'recommended-tab-default'
                        : currentButtonFocus == 'recomendation'
                        ? 'recommended-tab focusable'
                        : 'more-information-tab focusable'
                    }
                    onClick={() => {
                      handleRecomendationTab()
                    }}
                    id={'recom'}
                  >
                    <span className="recomendation-title">
                      {`${truncateText( apilanguage?.[`Vcard_TabsRecomendaciones_TextoTitulo`] ?? 'Vcard_TabsRecomendaciones_TextoTitulo' ,30)}` }
                    </span>
                  </button>
                  <button
                    onFocus={() => setCurrentButtonFocus('moreinfo')}
                    onKeyDown={event => {
                      handleScrolling(event)
                    }}
                    data-testid="playbutton"
                    className={'more-information-tab'}
                    onClick={() => {
                      handleMoreInfoTab()
                    }}
                  >
                    <span className="recomendation-title">
                      {handleTranslationchange(
                        'Vcard_TabsMasInformacion_TextoTitulo'
                      )[0].toUpperCase() +
                        handleTranslationchange(
                          'Vcard_TabsMasInformacion_TextoTitulo'
                        )
                          .slice(1)
                          .toLowerCase()}
                    </span>
                  </button>
                </div>
              </>
            ) : (
              <div className="moviestab-tray">
                <div className="tab-tray-wrapper" id={'recommendedpage'}>
                  <button
                    autoFocus={currentButtonFocus === 'recomendation'}
                    onFocus={() => setCurrentButtonFocus('recomendation')}
                    data-testid="playbutton"
                    className={
                      focusRecomendRed
                        ? 'recommended-tab-focus focusable'
                        : currentButtonFocus == 'recomendation'
                        ? 'recommended-tab focusable'
                        : 'more-information-tab focusable'
                    }
                    onClick={() => {
                      handleRecomendationTab()
                    }}
                    onKeyDown={event => {
                      handleScrollingRecommendation(event)
                    }}
                    id={'reco'}
                    tabIndex={2}
                    data-sn-down={
                      currentButtonFocus === 'recomendation' && focusMoreinfoRed
                        ? '#castImage-0'
                        : '#vodMoviesRecommend-0'
                    }
                  >
                    <span
                      className={
                        currentButtonFocus == 'recomendation'
                          ? 'recomendation-title-focus'
                          : 'recomendation-title'
                      }
                    >
                     {`${truncateText( apilanguage?.[`Vcard_TabsRecomendaciones_TextoTitulo`] ?? 'Vcard_TabsRecomendaciones_TextoTitulo' ,30)}` }
                      {/* {handleTranslationchange(
                            'Vcard_TabsRecomendaciones_TextoTitulo'
                          )} */}
                    </span>
                  </button>
                  <button
                    autoFocus={moreInfoTab && currentButtonFocus == 'moreinfo'}
                    onFocus={() => setCurrentButtonFocus('moreinfo')}
                    data-testid="playbutton"
                    className={
                      focusMoreinfoRed
                        ? 'recommended-tab-focus focusable'
                        : currentButtonFocus == 'moreinfo'
                        ? 'recommended-tab focusable'
                        : 'more-information-tab focusable'
                    }
                    onClick={() => {
                      handleMoreInfoTab()
                    }}
                    onKeyDown={event => {
                      handleScrollingMoreInfo(event)
                    }}
                    id={'moreInfo'}
                    tabIndex={2}
                    data-sn-down={
                      currentButtonFocus === 'moreinfo' && focusRecomendRed
                        ? '#vodMoviesRecommend-0'
                        : '#castImage-0'
                    }
                  >
                    <span
                      className={
                        currentButtonFocus == 'moreinfo'
                          ? 'recomendation-title-focus'
                          : 'recomendation-title'
                      }
                    >
                      {handleTranslationchange(
                        'Vcard_TabsMasInformacion_TextoTitulo'
                      )[0].toUpperCase() +
                        handleTranslationchange(
                          'Vcard_TabsMasInformacion_TextoTitulo'
                        )
                          .slice(1)
                          .toLowerCase()}
                    </span>
                  </button>
                </div>
                {moreInfoTab && (
                  <>
                    <div className="more-info-layout">
                      {currentButtonFocus !== 'cast' && (
                        <>
                          <div className="moreInfo-tab-title">
                            {vodMoviesCast?.common?.title}
                          </div>

                          <div className="infomore-description-layouts">
                            <div className="info-detail-layout info-description">
                              <div className="info-description-detail">
                                {vodMoviesCast?.common?.large_description}
                              </div>
                            </div>
                            <div className="info-detail-layout info-vod-detail">
                              <div className="duration-div1">
                                <div className="info-duration">{`${handleTranslationchange(
                                  'DURATION'
                                )}:`}</div>
                                <div className="info-duration-copy">
                                  {formatDuration(
                                    vodMoviesCast?.common?.duration
                                  )}
                                </div>
                              </div>

                              <div className="year-div2">
                                <div className="info-year">
                                  {handleTranslationchange('Vcard_MasInformacion_TextoTituloAnio_TituloAnio')}
                                </div>
                                <div className="info-year-copy">
                                  {
                                    vodMoviesCast?.common?.extendedcommon?.media
                                      ?.publishyear
                                  }
                                </div>
                              </div>

                              <div className="genero-div">
                                <div className="genero">{handleTranslationchange(
                                    'Vcard_MasInformacion_TextoTituloGenero_TituloGenero'
                                )}</div>

                                <div className="genero-copy">
                                  {genreData}
                                  {/* {handleGenreChar(genreData?.toString())} */}
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                      <div
                        className="cast-layout"
                        style={
                          currentButtonFocus == 'cast'
                            ? { marginTop: '0px' }
                            : { marginTop: '50px' }
                        }
                      >
                        <span className="cast-title"> {handleTranslationchange(
                            'Vcard_CarruselReparto_TextoTitulo_Titulo'
                          )}</span>
                        {vodMoviesMltData && vodMoviesMltData?.length > 0 ? (
                          <div
                            className="container Casts"
                            id={'castDetailpage'}
                          >
                            {castDetail?.map((cast, index, array) => {
                              return (
                                <button
                                  data-testid="castTest"
                                  className={'container-block focusable'}
                                  data-sn-up={'#moreInfo'}
                                  id={`castImage-${index}`}
                                  onFocus={() => {
                                    setCurrentButtonFocus('cast')
                                    document
                                      .getElementById(`castImage-${index}`)
                                      .scrollIntoView({
                                        behavior: 'smooth',
                                        inline: 'center',
                                        block: 'center'
                                      })
                                  }}
                                  autoFocus={
                                    setVodReturnFocus == `castImage-${index}`
                                  }
                                  onClick={() =>
                                    handleCastClick(cast, `castImage-${index}`)
                                  }
                                  key={index}
                                  onKeyDown={() =>
                                    currentButtonFocus == 'cast'
                                      ? setIsOnClickRecome(true)
                                      : ''
                                  }
                                  tabIndex={1}
                                >
                                  <div className="cast-card-image">
                                    <LazyLoadImage
                                      className="cast-image"
                                      src={
                                        cast?.image && cast?.image != 'undefined' &&
                                        cast?.image != 'null' &&  cast?.image != ''
                                          ? cast?.image
                                          : 'images/Vod_Movies_Icons/cast_thumbnail.png'
                                      }
                                      key={index}
                                      placeholderSrc={
                                        'images/Vod_Movies_Icons/cast_thumbnail.png'
                                      }
                                    />
                                    <div className="cast-detail">
                                      <div className="cast-roles">
                                        {cast?.role}
                                      </div>
                                      <div className="cast-names">
                                        {cast?.first_name
                                          ? truncateText(cast?.name, 14)
                                          : truncateText(cast?.name, 14)}
                                      </div>
                                    </div>
                                  </div>
                                </button>
                              )
                            })}
                          </div>
                        ) : (
                          <div className="vod-container-loader">
                            <Lottie
                              options={{
                                rendererSettings: {
                                  preserveAspectRatio: 'xMidYMid slice'
                                }
                              }}
                              loop
                              animationData={animationData}
                              play
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
                {recomendationFlag && (
                  <>
                    {!moreInfoTab && (
                      <div className="mlt-layout">
                        <span className="recomendation-content-title">
                          {handleTranslationchange(
                            'Vcard_CarruselRecomendaciones_TextoTitulo'
                          )}
                        </span>

                        {vodMoviesMltData && vodMoviesMltData?.length > 0 ? (
                          <div className="mlt-wrapper">
                            {vodMoviesMltData?.map((item, index, array) => (
                              <button
                                data-testid="mltTest"
                                id={`vodMoviesRecommend-${index}`}
                                className="mlt-block focusable"
                                key={item?.id}
                                onClick={() => {
                                  handleMltDetails(item,index), handleBackInfoTab()
                                }}
                                onFocus={() => {
                                  setCurrentButtonFocus(`mlt-${index}`)
                                  document
                                    .getElementById(
                                      `vodMoviesRecommend-${index}`
                                    )
                                    .scrollIntoView({
                                      behavior: 'smooth',
                                      inline: 'center',
                                      block: 'center'
                                    })
                                }}
                                data-sn-up={'#reco'}
                              >
                                <div className="mlt-card-image">
                                  {item?.image_small !== '' ||
                                  item?.image_small !== undefined ? (
                                    <LazyLoadImage
                                      src={item?.image_small}
                                      loading="lazy"
                                      alt="PlaceHolder"
                                      className="mlt-card-image"
                                      key={index}
                                      id={`mltfocus${index}`}
                                      placeholderSrc={
                                        'images/landscape_card.png'
                                      }
                                    />
                                  ) : (
                                    <LazyLoadImage
                                      src="images/landscape_card.png"
                                      loading="lazy"
                                      alt="PlaceHolder"
                                      className="mlt-card-image"
                                      placeholderSrc={
                                        'images/landscape_card.png'
                                      }
                                    />
                                  )}
                                  <div
                                    style={{
                                      display:
                                        currentButtonFocus === `mlt-${index}`
                                          ? ''
                                          : 'none'
                                    }}
                                    className="mlt-detail"
                                  >
                                    <div className="default-titlerail-vod">
                                      {posterTitle?.default[
                                        item?.proveedor_code
                                      ]?.landscape
                                        ? item?.title
                                        : ''}
                                    </div>
                                  </div>
                                  {item.image_small &&
                                  item?.proveedor_code == 'amco' ? (
                                    item?.format_types === 'ppe,download' ? (
                                      <div className="proveedorBlockRailAlq">
                                        <img
                                          src={'images/Alquilar.svg'}
                                          className="tagAlq"
                                        />
                                      </div>
                                    ) : item?.format_types === 'ppe' ? (
                                      <div className="proveedorBlockRailAlq">
                                        <img
                                          src={'images/Alquilar.svg'}
                                          className="tagAlq"
                                        />
                                      </div>
                                    ) : null
                                  ) : item.image_small &&
                                    item?.proveedor_code &&
                                    item?.image_medium ? (
                                    <div className="proveedorBlockRail_vero_hara">
                                      {addProveedor(providerLabel?.[item?.proveedor_code]?.susc) && (
                                        <img
                                          id="#icon1"
                                          className={
                                            item?.proveedor_code ===
                                              'picardia2' && 'picardia-image'
                                          }
                                          src={addProveedor(
                                            providerLabel?.[item?.proveedor_code]?.susc
                                          )}
                                        />
                                      )}
                                      {item.image_small &&
                                        item?.proveedor_code === 'picardia2' &&
                                        item?.image_medium && (
                                          <div className="picardia-proveedorBlockRail">
                                            <img
                                              src={'images/Adultus.svg'}
                                              className="picardia-tag"
                                            />
                                          </div>
                                        )}
                                      {item?.format_types === 'free' &&
                                      userDetails?.subscriptions?.length ==
                                        0 ? (
                                        <div className="verahora-tag">
                                          VER AHORA
                                        </div>
                                      ) : null}
                                    </div>
                                  ) : null}
                                </div>
                              </button>
                            ))}
                          </div>
                        ) : (
                          <div className="vod-container-loader">
                            <Lottie
                              options={{
                                rendererSettings: {
                                  preserveAspectRatio: 'xMidYMid slice'
                                }
                              }}
                              loop
                              animationData={animationData}
                              play
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </>
  )
}

export default VodMovies
