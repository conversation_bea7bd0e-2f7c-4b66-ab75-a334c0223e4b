import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import '../../../styles/ManagePayments.css'
import { getPaymentDetails } from '../../../store/slices/settingsSlice'

const ConfirmSubscription = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const ClaroImg = 'images/claroLogo.png'
  const region = localStorage.getItem('region')
  const apilanguage = translations?.language?.[region]
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =     apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const handleGoPreviousPage = e => {
    navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new');
  }

  return (
    <div className="Landline-Finish-Subscription-Page">
      <div className="App-logo">        <img src={'images/logo.png'} className="logo-img" alt="logo" /> </div>
      <button className="back-indicator  focusable" onMouseEnter={e => { e.target.focus()}} onClick={e => {handleGoPreviousPage(e)}} id="paymentmethod-page-back"  >
        <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
        <img className="back-image" src={'images/back_button.png'} />
        <p className="back-text">REGRESAR</p>
      </button>
      <div className="finishSubscribeLandLineInfo">
		<div className='iconDiv' >ℹ️</div>
		<div className='landline-text-desc'><p>Te invitamos a agregar el mdedo de pago Temlmex/Telnor desde un navedador web,para que ,puedas realizer tu transddionesy disgurtar del conetinao en este dipsitiovo</p></div>     
      </div>
      <div style={{ marginTop: '10vh', textAlign: 'center' }}>
        <button onMouseEnter={e => {   e.target.focus()  }} id="landline-finish-subscription-ok-btn" onClick={e => {   handleGoPreviousPage(e) }} class="default-btn focusable" > CANCELAR </button>
      </div>
    </div>
  )
}

export default ConfirmSubscription





