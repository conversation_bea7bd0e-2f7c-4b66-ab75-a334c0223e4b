import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  data: [],
  predictiveData: [],
  suggest: [],
  isPredictive: false,
  isLoading: false,
  error: null,
  popularSearch: '',
  isMulticontent: false,
  multiContentSearch: [],
  isProgressBarContent: false,
  isLoadingTalentSearch: false,
  progressbarContent: [],
  talentSearchData: null,
  showTalentModule: false,
  showApiFailureModal: false,
  talentSearch: {},
  setReturnFocus: null,
  setVodReturnFocus: null,
  isRecomendationLoading: false,
  isRecomErrorLoading: false
}

export const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    getSearchData: state => {
      state.isRecomendationLoading = true
    },
    getSearchSuccess: (state, { payload }) => {
      state.isRecomendationLoading = false
      state.data = payload?.response?.groups
      payload?.response?.groups &&
        sessionStorage.setItem(
          'recomendation',
          JSON.stringify(payload?.response?.groups)
        )
    },
    getSearchError: (state, { payload }) => {
      state.isRecomErrorLoading = false
      state.error = payload
    },

    getProgressbarBookmark: state => {
      state.isProgressBarContent = true
    },
    getProgressbarBookmarkSuccess: (state, { payload }) => {
      state.isProgressBarContent = false
      state.progressbarContent = payload?.response?.groups
    },
    getProgressbarBookmarkError: (state, { payload }) => {
      state.isProgressBarContent = false
      state.error = payload
    },

    getPredictiveSearchData: state => {
      state.isPredictive = true
    },

    getPredictiveSearchSuccess: (state, { payload }) => {
      state.predictiveData = payload?.response
      state.suggest = payload?.response?.suggest
      state.isPredictive = false
    },
    getPredictiveSearchError: (state, { payload }) => {
      ;(state.isPredictive = false), (state.error = payload)
    },

    getMulticontentSearchData: state => {
      state.isMulticontent = true
    },
    getMultiContentSearchSuccess: (state, { payload }) => {
      state.isMulticontent = false
      state.multiContentSearch = payload?.data
    },
    getMultiContentSearchError: (state, { payload }) => {
      state.isMulticontent = false
      state.error = payload
    },

    getTalentSearchData: state => {
      state.isLoadingTalentSearch = true
    },
    getTalentSearchSuccess: (state, { payload }) => {
      state.isLoadingTalentSearch = false
      state.talentSearchData = payload?.response?.groups
    },
    getTalentSearchError: (state, { payload }) => {
      state.isLoadingTalentSearch = false, 
      state.error = payload
    },
    getCleartalentState: (state, {  }) => {
      state.talentSearchData = {}
    },
    setShowTalentModule: (state, { payload }) => {
      state.showTalentModule = payload
    },
    setShowApiFailureModal: (state, { payload }) => {
      state.showApiFailureModal = payload
    },
    getTalentSearch: (state, { payload }) => {
      state.talentSearch = payload
    },
    setReturnFocusById: (state, { payload }) => {
      state.setReturnFocus = payload
    },
    setVodReturnFocusById: (state, { payload }) => {
      state.setVodReturnFocus = payload
    },
    removeVodReturnFocusById: (state, { payload }) => {
      state.setVodReturnFocus = initialState.setVodReturnFocus
    },
    removeReturnFocusById: state => {
      state.setReturnFocus = initialState.setReturnFocus
    }
  }
})

export const {
  getSearchData,
  getSearchSuccess,
  getSearchError,
  getPredictiveSearchData,
  getPredictiveSearchSuccess,
  getPredictiveSearchError,
  getMulticontentSearchData,
  getMultiContentSearchSuccess,
  getMultiContentSearchError,
  getProgressbarBookmark,
  getProgressbarBookmarkSuccess,
  getProgressbarBookmarkError,
  getTalentSearchData,
  getTalentSearchSuccess,
  getTalentSearchError,
  getCleartalentState,
  setShowTalentModule,
  setShowApiFailureModal,
  getTalentSearch,
  setReturnFocusById,
  setVodReturnFocusById,
  removeReturnFocusById,
  removeVodReturnFocusById
} = searchSlice.actions

export default searchSlice.reducer
