
.keyboardBox{
  max-width: 40.98vw;
  max-height: 49.62vh;
}
.UpperButtons{

  margin-right: 25px;
  text-align: center;

}
.top-buttom-image:focus{
  outline: 3px solid white;
}

.payment-keyboard-button {
  width: 147px;
  height: 61px;
  opacity: 1;
  margin: 5px;
  font-family: <PERSON><PERSON>;
  letter-spacing: 0;
  line-height: 45px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  display: flex;
  color: #eee;
  border: #eee;
  outline: none;
  border: 3px solid black;
  /*border-radius: 50% !important;*/
  font-size: 43px;
  margin: 1.15% !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

  &:focus {
    /* border: 3px solid white;*/
    background: #981c15;
    transform: scale(1.1);
    color: white;
    border-radius: 100% !important;
  }
}

.payment-keyboard-button#shftId {
  justify-content: center;
  margin-right: 32px !important;
  width: 73px;

  &:focus {
    /* border: 3px solid white; */
    background-color: red;
    transform: scale(1.1);
    color: white;
    border-radius: 100% !important;
    justify-content: center;
  }
}

.payment-keyboard-button#spcId {
  justify-content: center;
  margin-left: 4px !important;
  width: 100px;

  &:focus {
   /* border: 3px solid white; */
   background: #981c15;
   transform: scale(1.1);
    color: white;
    border-radius: 50% !important;
    justify-content: center;
  }
}

.payment-keyboard-button#Key_28 {
  margin-left: 65px !important;
  border-radius: 50% !important;
}

.numeric-button:focus,
.numeric-button:active,
.numeric-button:hover {
  border: solid;
}

.top-buttom-image {
  height: 35px;
  margin-top: 8.33px;
  margin-bottom: 8.33px;
  margin-left: 21.11px;
  margin-right: 21.11px;
}

.top-buttons-key {
  display: flex;
  flex-direction: row;
  margin-bottom: '52.66px';
  margin-top: '113px';
}

.keyboardCover {
  margin-top: 72px;
  padding: 15px;
  justify-content: center;
  height: 491px;
  display: flex;
  flex-wrap: wrap;
  float: right;
  border-radius: 16px 16px 0px 0px;
  opacity: 1;
}

.numeric-keyboard-cover {
  width: 185px;
  height: 224px;
  display: flex;
  flex-wrap: wrap;
  margin: auto;
}

.key-width {
  width: 62px !important;
}

.key-width-abc {
  width: 90px !important;
}

.key-width-spl {
  width: 93px !important;
}

.key-width-ok {
  width: 85px !important;
}

@media screen and (max-width: 1440px) {
  .payment-keyboard-button {
    height: 36px;
    border-radius: 50% !important;
  }

  .keyboardCover {
    height: 240px;
    width: 520px;
  }

  .numeric-keyboard-cover {
    width: 150px;
  }
}

@media screen and (min-width: 1920px) {
  .btn-space {
    margin: 3% !important;
  }

  .gmail_button {
    width: 250px !important;
  }

  .key-width-spl {
    width: 92.5px !important;
  }

  .key-width-ok {
    width: 160px !important;
  }

  .numeric-button {
    height: 73px;
    width: 73px;
    margin: 5px;
    background-color: #1f2023;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    display: flex;
    color: #eee;
    border: none;
    outline: none;
    border-radius: 5px;
    font-size: 30px;
  }

  .numeric-keyboard-cover {
    /* width: 185px;
        height: 224px; */
    width: 281px;
    height: 347px;
    display: flex;
    flex-wrap: wrap;
    margin: auto;
  }
}

@media screen and (min-width: 1280px) {
  .btn-space {
    margin: 3% !important;
  }

  .gmail_button {
    width: 247px !important;
  }

  .numeric-button {
    height: 73px;
    width: 73px;
    margin: 5px;
    background-color: #1f2023;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    display: flex;
    color: #eee;
    border: none;
    outline: none;
    border-radius: 5px;
    font-size: 30px;
  }

  .numeric-keyboard-cover {
    /* width: 185px;
        height: 224px; */
    width: 281px;
    height: 347px;
    display: flex;
    flex-wrap: wrap;
    margin: auto;
  }
}

@media screen and (min-width: 1366px) {
  .btn-space {
    margin: 3% !important;
  }

  .numeric-button {
    height: 55px;
    width: 55px;
    margin: 5px;
    background-color: #1f2023;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    display: flex;
    color: #eee;
    border: none;
    outline: none;
    border-radius: 5px;
    font-size: 30px;
  }

  .numeric-keyboard-cover {
    width: 250px;
    height: 290px;
    display: flex;
    flex-wrap: wrap;
    margin: auto;
  }
}

@media screen and (max-width: 1280px) {
  .numeric-keyboard-cover {
    width: 172px;
    height: 230px;
  }

  .numeric-button {
    height: 40px;
    width: 40px;
    font-size: 30px;
  }
}

/* NumberPad Keyboard */

.Keyboard-cont {
  width: 600px;
  border-radius: 14px;
  height: 410px;
  margin: auto auto 10px auto;
  justify-content: center;
  display: flex;
  // flex-direction: row;
  align-items: center;
  padding-top: 10px;
  background-color: #1d2228;
}

.Keyboard-btn-focused {
  background: #981c15 0% 0% no-repeat padding-box;
  border-radius: 7px;
  opacity: 1;
  width: 113px;
  height: 72px;
  margin: 10px 10px;
  padding: 5px;
}

.Keyboard-btn-unfocused {
  background: #2a3139 0% 0% no-repeat padding-box;
  border-radius: 8px;
  opacity: 1;
  width: 113px;
  height: 72px;
  margin: 10px 10px;
  padding: 5px;
}

.Keyboard-txt {
  text-align: center;
  font: normal normal bolder 40px/53px Roboto;
  letter-spacing: 0px;
  opacity: 1;
  margin: 0;
}

.BackSpace-imgfocus {
  display: flex;
  margin: 0px auto;
  width: 75px;
  height: 65px;
}

.Back-space-img {
  display: flex;
  margin: 15px auto;
  width: 65px;
  height: 45px;
}

.Submit-img-focus {
  display: flex;
  margin: 0px auto;
  width: 75px;
  height: 65px;
}

#comId,
#arId,
#gmlId,
#yhoId {
  font-family: Roboto;
  font-size: 30px;
  letter-spacing: 0;
  line-height: 29.04px;
}

/* NumberPad Keyboard */
#abcBtn {
  margin-left: 60px;
  margin-right: 17.6px;
}

#numBtn {
  margin-right: 57.3px;
}

#clearBtn {
  margin-right: 24px;
}