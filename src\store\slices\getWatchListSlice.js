import { createSlice } from '@reduxjs/toolkit'

export const watchListSlice = createSlice({
  name: 'watchList',
  initialState: {
    watchList: [],
    addWatchList: {},
    delWatchList: {},
    isGetWatchlistLoading: false,
    isAddWatchlistLoading: false,
    isDelWatchlistLoading: false,
    getWatchlistError: {},
    addWatchlistError: {},
    delWatchlistError: {},
    continueWatch: false,
    continuewatchlist: {},
    continuewatcherror: {},
    deleteContinueWatch: false,
    deletecontinuewatchlist: {},
    delContinueWatchError: {},
    lastSeenWatchState: false,
    lastSeenWatchListData: {},
    lastSeenWatchListError: {},
    getEditImageFocus: {}
  },
  reducers: {
    getWatchList: state => {
      state.isGetWatchlistLoading = true
    },
    getWatchListSuccess: (state, action) => {
      state.watchList = action?.payload?.response?.groups
      state.isGetWatchlistLoading = false
      state.addWatchList = {}
      state.delWatchList = {}
    },
    getWatchListError: (state, action) => {
      state.isGetWatchlistLoading = false
      state.getWatchlistError = action.payload?.data?.response
    },

    addWatchList: state => {
      state.isAddWatchlistLoading = true
    },
    addWatchListSuccess: (state, { payload }) => {
      localStorage.setItem('lasttouch', payload?.lasttouch?.favorited)
      state.addWatchList = payload
      state.isAddWatchlistLoading = false
    },
    addWatchListError: (state, action) => {
      state.isAddWatchlistLoading = false
      state.addWatchlistError = action.payload
    },

    delWatchList: state => {
      state.isDelWatchlistLoading = true
    },
    delWatchListSuccess: (state, { payload }) => {
      localStorage.setItem('lasttouch', payload?.lasttouch?.favorited)
      state.delWatchList = payload
      state.isDelWatchlistLoading = false
    },
    delWatchListError: (state, action) => {
      state.isDelWatchlistLoading = false
      state.delWatchlistError = action.payload
    },
    continueWatchlist: (state, { payload }) => {
      state.continueWatch = false
    },
    continueWatchlistSuccess: (state, { payload }) => {
      state.continuewatchlist = payload
    },
    continueWatchlistError: (state, { payload }) => {
      state.continuewatcherror = payload
    },
    delContinueWatch: (state, { payload }) => {
      state.deleteContinueWatch = false
    },
    delContinueWatchSuccess: (state, { payload }) => {
      state.deletecontinuewatchlist = payload
    },
    delContinueWatchError: (state, { payload }) => {
      state.delContinueWatchError = payload
    },
    lastSeenWatch: state => {
      state.lastSeenWatchState = true
    },
    lastSeenWatchListResponse: (state, { payload }) => {
      state.lastSeenWatchListData = payload
      state.lastSeenWatchState = false
    },
    lastSeenWatchListResponseError: (state, { payload }) => {
      state.lastSeenWatchListError = payload
      state.lastSeenWatchState = false
    },
    clearContentState: state => {
      state.delWatchList = {}
      state.deletecontinuewatchlist = {}
    },
    getImageFocusInEdit: (state, { payload }) => {
      state.getEditImageFocus = payload
    }
  }
})

export const {
  getWatchList,
  getWatchListSuccess,
  getWatchListError,
  addWatchList,
  addWatchListSuccess,
  addWatchListError,
  delWatchList,
  delWatchListSuccess,
  delWatchListError,
  continueWatchlist,
  continueWatchlistSuccess,
  continueWatchlistError,
  clearContentState,
  delContinueWatch,
  delContinueWatchSuccess,
  delContinueWatchError,
  lastSeenWatch,
  lastSeenWatchListResponse,
  lastSeenWatchListResponseError,
  getImageFocusInEdit
} = watchListSlice.actions

export default watchListSlice.reducer
