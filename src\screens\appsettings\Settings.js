import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './Settings.scss'
import { useDispatch } from 'react-redux'
import {
  getUserLogout,
  getClearAllSettingsState
} from '../../store/slices/settingsSlice'
import {
  getClearAllLoginStates,
  getLoginNavigation,
  clearLoginInfoState,
  getLogin,
  getRegisterNavigation,
  getSettingsClicked,
  getNavBarClicked
} from '../../store/slices/login'
import {
  getClearState,
  getNavTabValueClear,
  getPremiumValueStateClear
} from '../../store/slices/HomeSlice'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import {
  getUserProfile,
  getBackProfile,
  getClearProfileState
} from '../../store/slices/ProfileSlice'
import { pushMenuProfileEvent, pushScreenViewEvent, pushLogoutEvent } from '../../GoogleAnalytics'
import { getClearEPGState } from '../../store/slices/EpgSlice'

const Settings = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const [activeTab, setActiveTab] = useState('admin')

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const profileData = useSelector(
    state => state?.profile?.profileData?.response?.data
  )
  const isLoggedInStatus = useSelector(state => state?.login?.isLoggedInStatus)
  const userLogoutCheck = useSelector(
    state => state?.settingsReducer?.userLogout?.status
  )
  const userProfileData = useSelector(state => state?.profile?.userProfile)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const loginError = useSelector(state => state?.login?.loginError?.errors)
  const pushSessionError = useSelector(
    state => state?.profile?.pushSessionError?.errors
  )
  const isLoggedInError = useSelector(
    state => state?.login?.isLoggedInV1Error?.errors
  )
  const loadingState = useSelector(state => state?.login?.loading)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const nav = useSelector(state => state?.homeReducer?.navbarData)
  const navIndex = localStorage.getItem('currNavIdx')
  

  const handleProfilesClick = () => {
    //GA for menu events
    pushMenuProfileEvent(
      userDetails,
      truncateText('menu_manage_profiles_label', 30)?.toLowerCase(),
      nav[navIndex]?.page?.toLowerCase()
    )
    navigate('/watchprofile', {
      state: { data: '', page: 'home', img: userProfileData }
    })
    dispatch(getBackProfile(true))
  }

  const handleProfileSettingControlClick = () => {
    //GA for menu event
    pushMenuProfileEvent(
      userDetails,
      truncateText('stb_social_settings', 30)?.toLowerCase(),
      nav[navIndex]?.page?.toLowerCase()
    )
    navigate('/settings/profile-settings', { state: { defaultFocus: true } })
  }

  const handleMyDevicesClick = () => {
    //GA for menu events
    pushMenuProfileEvent(
      userDetails,
      'mis dispositivos',
      nav[navIndex]?.page?.toLowerCase()
    )
    navigate('/my-settings/help-And-Settings/my-devices')
  }

  const handleLogoutClick = () => {
    localStorage.removeItem('isLoginUserTutorialShown')
    dispatch(getLoginNavigation(false))
    dispatch(getRegisterNavigation(false))
    dispatch(getUserLogout({ hks: userDetails?.session_stringvalue }))
    //GA for logout event
    pushLogoutEvent(
      userDetails,
      truncateText('social_close_session', 30)?.toLowerCase(),
      nav[navIndex]?.page?.toLowerCase(),
      truncateText('stb_social_settings', 30)?.toLowerCase()
    )
    //GA for menu events
    pushMenuProfileEvent(
      userDetails,
      truncateText('social_close_session', 30)?.toLowerCase(),
      nav[navIndex]?.page?.toLowerCase()
    )
  }

  const handleProfileSelection = item => {
    //GA for menu events
    pushMenuProfileEvent(
      userDetails,
      item?.username?.toLowerCase(),
      nav[navIndex]?.page?.toLowerCase()
    )
    if (item?.gamification_id == userDetails?.gamification_id) {
      navigate('/home')
      // Added these lines to ensure banner focus works for same profile
      dispatch(getNavBarClicked(true))
      dispatch(getSettingsClicked(false))
    }
    else {
      dispatch(clearLoginInfoState()),
        dispatch(
          getLogin({
            userHash: item?.user_hash,
            type: 'nonAdmin',
            hks: loginInfo?.session_stringvalue
          })
        )
      dispatch(getUserProfile(item))
      dispatch(getNavTabValue('homeuser'))
      if (localStorage.getItem('currNavIdx')) {
        localStorage.removeItem('currNavIdx')
      }
      if (localStorage.getItem('cardContent')) {
        localStorage.removeItem('cardContent')
      }
      if (localStorage.getItem('searchValue')) {
        localStorage.removeItem('searchValue')
      }
      if (localStorage.getItem('miscontenidos')) {
        localStorage.removeItem('miscontenidos')
      }
    }
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  useEffect(() => {
    if (isLoggedInStatus && isLoggedInStatus == 'Success') {
      navigate('/home')
      dispatch(getSettingsClicked(false))
      dispatch(getNavBarClicked(true))
    }
  }, [isLoggedInStatus])

  useEffect(() => {
    state?.activeTab && setActiveTab(state?.activeTab)
    pushScreenViewEvent({screenName:'settings', screenData:userDetails, prevScreenName:'inicio'})
  }, [])

  useEffect(() => {
    if (userLogoutCheck === '0') {
      localStorage.removeItem('userId')
      localStorage.removeItem('signinemail')
      localStorage.removeItem('token')
      localStorage.removeItem('login_user')
      localStorage.removeItem('register_user')
      localStorage.removeItem('hks')
      localStorage.removeItem('pwd')
      localStorage.removeItem('count')
      localStorage.removeItem('region')
      localStorage.removeItem('gamificationid')
      localStorage.removeItem('lasttouch')
      localStorage.removeItem('loginId')
      localStorage.removeItem('live-channel-id')
      localStorage.removeItem('live-playing-channel-id')
      localStorage.removeItem('programId')
      localStorage.removeItem('pastEvent')
      localStorage.removeItem('user_hash')
      localStorage.removeItem('currNavIdx')
      localStorage.removeItem('focusedEle')
      localStorage.removeItem('continueWatchLasttouch')
      localStorage.removeItem('hideTimer')
      localStorage.removeItem('currentVod')
      localStorage.removeItem('introFinished')
      localStorage.removeItem('currentVodId')
      localStorage.removeItem('vodContentLanguage')
      dispatch(getClearAllLoginStates())
      dispatch(getClearAllSettingsState())
      dispatch(getPremiumValueStateClear())
      dispatch(getClearProfileState())
      dispatch(getClearState())
      dispatch(getNavTabValueClear())
      dispatch(getClearEPGState())
      SpatialNavigation.clear()
      SpatialNavigation.uninit()
      SpatialNavigation.init()
      navigate('/')
    }
  }, [userLogoutCheck])

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (keycode === 10009 || keycode === 461 || keycode == 8) {
      navigate('/home')
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])


  const capitalizeFirstLetter = text => {
    if (!text) {
      return ''
    }
    const [firstChar, ...rest] = text
    return firstChar.toUpperCase() + rest.join('').toLowerCase()
  }

  useEffect(() => {
    const getProfileIndex =
      profileData &&
      profileData?.members.findIndex(
        itrObj => itrObj.gamification_id == userDetails?.gamification_id
      )
    activeTab == 'admin' &&
      document.getElementById('administratorProfiles')?.focus()
    activeTab == 'profile-settings' &&
      document.getElementById('adjustingProfiles')?.focus()
    activeTab == 'my-devices' && document.getElementById('myDevices')?.focus()
    !userDetails?.admin &&
      document.getElementById(`image${getProfileIndex}`)?.focus()
  }, [activeTab])

  useEffect(() => {
    if (loginError || pushSessionError || isLoggedInError) {
      navigate('/service-error', { state: { pageName: 'watchProfile' } })
    }
  }, [loginError, pushSessionError, isLoggedInError])

  return (
    !loadingState && (
      <div className="app-css-settings">
        <div className="main-container-settings">
          {userDetails?.admin && (
            <>
              <button
                className={`setting-buttons ${activeTab == 'admin' ? 'setting-buttons-active' : ''
                  } focusable`}
                id="administratorProfiles"
                onClick={handleProfilesClick}
              >
                <span className="button-content-settings">
                  {capitalizeFirstLetter(
                    truncateText(
                      'menu_manage_profiles_label',
                      30
                    )
                  )}
                </span>
              </button>
              <button
                className={`setting-buttons ${activeTab == 'profile-settings'
                    ? 'setting-buttons-active'
                    : ''
                  } focusable`}
                id="adjustingProfiles"
                onClick={handleProfileSettingControlClick}
              >
                <span className="button-content-settings">
                  {capitalizeFirstLetter(
                    truncateText('stb_social_settings', 30)
                  )}
                </span>
              </button>
              <button
                className={`setting-buttons ${activeTab == 'my-devices' ? 'setting-buttons-active' : ''
                  } focusable`}
                id="myDevices"
                onClick={handleMyDevicesClick}
              >
                <span className="button-content-settings">
                  Mis dispositivos
                  {/* {truncateText('stb_social_settings', 30)} */}
                </span>
              </button>
            </>
          )}
          {profileData &&
            profileData?.members.map((item, index) => (
              index < 5 &&
              <button
                className="img-button focusable"
                key={index}
                id={`image${index}`}
                onClick={() => handleProfileSelection(item)}
              >
                <img
                  className="user-image"
                  src={
                    item?.user_image && item?.user_image != 'null'&& 
                    item?.user_image != 'undefined'
                      ? item?.user_image
                      : 'images/Profile_Icons/profile_image.png'
                  }
                />
                <span className="img-button-content-settings">
                  {
                    item?.username.length > 14
                      ? `${item?.username.slice(0, 14)}...`
                      : item?.username
                  }
                </span>
              </button>
            ))}
          <button
            className="setting-buttons focusable"
            id="logout"
            onClick={handleLogoutClick}
          >
            <span className="button-content-settings">
              {capitalizeFirstLetter(truncateText('social_close_session', 30))}
            </span>
          </button>
        </div>
      </div>
    )
  )
}

export default React.memo(Settings)
