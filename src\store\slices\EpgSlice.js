import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  epgMenu: {},
  epgVersion: '',
  soaVersion: '',
  epgChannel: [],
  epgChannelBackup: [],
  epgLineup: [],
  isLoading: false,
  isChannelLoading: false,
  error: {},
  favouriteLive: [],
  addfavouriteList: {},
  delfavourite: {},
  isAddfavouriteLoading: false,
  isDelfavouriteLoading: false,
  addfavouriteLiveError: {},
  delfavouriteLiveError: {},

  ReminderLive: [],
  addReminderList: {},
  delReminder: {},
  isAddReminderLoading: false,
  isDelReminderLoading: false,
  addReminderLiveError: {},
  delReminderLiveError: {},

  RecordingList: [],
  RecordingListSeries: [],
  CompletedRecordingList: {},
  InprogrssRecordingList: {},
  EpisodeRecordingList: {},
  addSeriesRecording: {},
  addEpisodeRecording: {},
  deleteRecordingSeries: {},
  deleteRecordingEpisode: {},
  recordingErrors:{},

  isSeriesRecordLoading: false,
  recordingSeriesError: {},
  isEpisodeRecordLoading: false,
  recordingEpisodeError: {},
  isDelSeriesRecording: false,
  deleteRecordingSeriesError: {},
  deleteMyContentRecording: {},
  deleteMyContentRecordingError: {},
  isDelEpisodeRecording: false,
  deleteRecordingEpisodeError: {},

  isPurchaseButtonloadding: false,
  purchaseButtonlist: {},
  purchaseButtonlistError: {},

  currentcard: '',
  paywayToken: [],
  viewProgramDetailsData: {},
  viewEpgFilteredData: {},
  epgFilterName: '',
  programEventData: {},
  recordingAlerts: {},
  favouriteAlerts: '',
  showMyContentButton: false,
  noLockChannelsList: {},
  scheduleReminder: '',
  isReminderPopupVisible: false,
  channelDownId: '',
  fromHomeToLive: false
}

export const epgSlice = createSlice({
  name: 'epg',
  initialState,
  reducers: {
    getEpgMenu: state => {
      state.isLoading = true
    },
    getEpgMenuSuccess: (state, { payload }) => {
      state.epgMenu = payload

      state.isLoading = false
      sessionStorage.setItem('nodeId', payload?.response?.nodes?.[0].id)
      state.isLoading = false
    },
    getEpgMenuError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getEpgVersion: state => {
      state.isLoading = true
    },
    getEpgVersionSuccess: (state, { payload }) => {
      state.epgVersion = payload?.response?.epg_version
      state.soaVersion = payload?.response?.soa_version
      state.isLoading = false
      sessionStorage.setItem('epgVersion', payload?.response?.epg_version)
      sessionStorage.setItem('soaVersion', payload?.response?.soa_version)
    },
    getEpgVersionError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getEpgChannel: (state, { payload }) => {
      state.epgChannel = payload
      state.isChannelLoading = false
    },
    getEpgChannelBackUp: (state, { payload }) => {
      state.epgChannelBackup = payload
    },
    getEpgLineup: state => {
      state.isLoading = true
    },
    getEpgLineupSuccess: (state, { payload }) => {
      state.epgLineup = payload
      state.isLoading = false
    },
    getEpgLineupError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getFavouriteLive: state => {
      state.isLoading = true
    },
    getFavouriteLiveSuccess: (state, { payload }) => {
      state.favouriteLive = payload
      state.isLoading = false
    },
    getFavouriteLiveError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    addFavouriteLive: state => {
      state.isAddfavouriteLoading = true
    },
    addFavouriteLiveSuccess: (state, { payload }) => {
      state.addfavouriteList = payload
      state.isAddfavouriteLoading = false
    },
    addFavouriteLiveError: (state, { payload }) => {
      state.isAddfavouriteLoading = false
      state.addfavouriteLiveError = payload
    },

    delFavouriteLive: state => {
      state.isDelfavouriteLoading = true
    },
    delFavouriteLiveSuccess: (state, { payload }) => {
      state.delfavourite = payload
      state.isDelfavouriteLoading = false
    },
    delFavouriteLiveError: (state, { payload }) => {
      state.isDelfavouriteLoading = false
      state.delfavouriteLiveError = payload
    },

    clearFavouriteRespose: state => {
      state.delfavourite = {}
      state.addfavouriteList = {}
    },

    getLiveReminder: state => {
      state.isLoading = true
    },
    getLiveReminderSuccess: (state, { payload }) => {
      state.ReminderLive = payload
      state.isLoading = false
    },
    getLiveReminderError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    addLiveReminder: state => {
      state.isAddReminderLoading = true
    },
    addLiveReminderSuccess: (state, { payload }) => {
      state.addReminderList = payload
      state.isAddReminderLoading = false
    },
    addLiveReminderError: (state, { payload }) => {
      state.isAddReminderLoading = false
      state.addReminderLiveError = payload
    },

    delLiveReminder: state => {
      state.isDelReminderLoading = true
    },
    delLiveReminderSuccess: (state, { payload }) => {
      state.delReminder = payload
      state.isDelReminderLoading = false
    },
    delLiveReminderError: (state, { payload }) => {
      state.isDelReminderLoading = false
      state.delReminderLiveError = payload
    },

    getLiveTvRecording: state => {
      state.isLoading = true
    },
    getLiveTvRecordingSuccess: (state, { payload }) => {
      state.RecordingList = payload
      state.isLoading = false
    },
    getLiveTvRecordingError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getLiveTvSeriesRecordingList: (state, { payload }) => {
      state.RecordingListSeries = payload
    },

    getLiveTvCompleteRecording: state => {
      state.isLoading = true
    },
    getLiveTvCompletedRecordingSuccess: (state, { payload }) => {
      state.CompletedRecordingList = payload
    },
    getLiveTvCompletedRecordingError: (state, { payload }) => {
      state.error = payload
    },
    getLiveTvProgressRecording: state => {
      state.isLoading = true
    },
    getLiveTvProgressRecordingSuccess: (state, { payload }) => {
      state.InprogrssRecordingList = payload
    },
    getLiveTvProgressRecordingError: (state, { payload }) => {
      state.error = payload
    },
    getLiveTvEpisodeRecoding: state => {
      state.isLoading = true
    },
    getLiveTvEpisodeRecordingSuccess: (state, { payload }) => {
      state.EpisodeRecordingList = payload
    },
    getLiveTvEpisodeRecordingError: (state, { payload }) => {
      state.error = payload
    },

    addLiveTvSeriesRecording: state => {
      state.isSeriesRecordLoading = true
    },
    addLiveTvSeriesRecordingSuccess: (state, { payload }) => {
      state.addSeriesRecording = payload
      state.isSeriesRecordLoading = false
    },
    addLiveTvSeriesRecordingError: (state, { payload }) => {
      state.isSeriesRecordLoading = false
      state.recordingSeriesError = payload
      state.recordingErrors = payload
    },

    addLiveTvEpisodeRecording: state => {
      state.isEpisodeRecordLoading = true
    },
    addLiveTvEpisodeRecordingSuccess: (state, { payload }) => {
      state.addEpisodeRecording = payload
      state.isEpisodeRecordLoading = false
    },
    addLiveTvEpisodeRecordingError: (state, { payload }) => {
      state.isEpisodeRecordLoading = false
      state.recordingEpisodeError = payload
      state.recordingErrors = payload
    },

    delLiveTvSeriesRecording: state => {
      state.isDelSeriesRecording = true
    },
    delLiveTvSeriesRecordingSuccess: (state, { payload }) => {
      state.deleteRecordingSeries = payload
      state.isDelSeriesRecording = false
    },
    delLiveTvSeriesRecordingError: (state, { payload }) => {
      state.isDelSeriesRecording = false
      state.deleteRecordingSeriesError = payload
    },

    delLiveTvEpisodeRecording: state => {
      state.isDelEpisodeRecording = true
    },
    delLiveTvEpisodeRecordingSuccess: (state, { payload }) => {
      state.deleteRecordingEpisode = payload
      state.isDelEpisodeRecording = false
    },
    delLiveTvEpisodeRecordingError: (state, { payload }) => {
      state.isDelEpisodeRecording = false
      state.deleteRecordingEpisodeError = payload
    },
    deleteMyContentRecording: state => {
      state.isDelEpisodeRecording = true
    },
    deleteMyContentRecordingSuccess: (state, { payload }) => {
      state.deleteMyContentRecording = payload
      state.isDelEpisodeRecording = false
    },
    deleteMyContentRecordingError: (state, { payload }) => {
      state.isDelEpisodeRecording = false
      state.deleteMyContentRecordingError = payload
    },

    getClearRecordingState: state => {
      state.addSeriesRecording = {}
      state.addEpisodeRecording = {}
      state.deleteRecordingSeries = {}
      state.deleteRecordingEpisode = {}
      state.recordingSeriesError = {}
      state.recordingEpisodeError = {}
      state.recordingErrors = {}
    },

    getcurrentcardindex: (state, { payload }) => {
      state.currentcard = payload
    },

    getPayWayToken: state => {
      state.isLoading = true
    },
    getPayWayTokenSuccess: (state, { payload }) => {
      state.paywayToken = payload
      state.isLoading = false
    },
    getPayWayTokenError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getPayWayPurchase_ButtonInfo: state => {
      state.isPurchaseButtonloadding = true
    },
    getPayWayPurchase_ButtonInfoSuccess: (state, { payload }) => {
      state.purchaseButtonlist = payload
      state.isPurchaseButtonloadding = false
    },
    getPayWayPurchase_ButtonInfoError: (state, { payload }) => {
      state.isPurchaseButtonloadding = false
      state.purchaseButtonlistError = payload
    },
    getProgramDetailsData: (state, payload) => {
      state.viewProgramDetailsData = payload
    },
    getEpgFilteredData: (state, payload) => {
      state.viewEpgFilteredData = payload
    },
    getEpgFilterName: (state, payload) => {
      state.epgFilterName = payload
    },
    getEventDetails: (state, { payload }) => {
      state.programEventData = payload
    },

    getAlerts: (state, { payload }) => {
      state.recordingAlerts = payload
    },

    getFavouriteAlerts: (state, { payload }) => {
      state.favouriteAlerts = payload.message
      state.showMyContentButton = payload.displayMyContentButton
    },
    resetFavouriteAlerts: state => {
      state.favouriteAlerts = initialState.favouriteAlerts
      state.showMyContentButton = initialState.showMyContentButton
    },
    clearReminderState: state => {
      (state.addReminderList = initialState.addReminderList),
        (state.delReminder = initialState.delReminder)
    },
    getNoLockChannels: (state, { payload }) => {
      state.noLockChannelsList = payload
    },
    getPopUpState: (state, { payload }) => {
      state.scheduleReminder = payload
    },
    setReminderPopupVisible(state, { payload }) {
      state.isReminderPopupVisible = payload
    },
    getChannelDownId: (state, { payload }) => {
      state.channelDownId = payload
    },
    setFromHomeToLive: (state, { payload }) => {
      state.fromHomeToLive = payload
    },
    getClearEPGState: state => {
      state.ReminderLive = {},
        (state.epgMenu = {}),
        (state.epgVersion = ''),
        (state.soaVersion = ''),
        (state.epgChannel = []),
        (state.epgChannelBackup = []),
        (state.epgLineup = []),
        (state.viewProgramDetailsData = {}),
        (state.viewEpgFilteredData = {}),
        (state.epgFilterName = ''),
        (state.RecordingList = []),
        (state.purchaseButtonlist = {}),
        (state.paywayToken = []),
        (state.noLockChannelsList = {}),
        (state.favouriteLive = []),
        (state.addfavouriteList = {}),
        (state.delfavourite = {}),
        (state.addfavouriteLiveError = {}),
        (state.delfavouriteLiveError = {}),
        (state.currentcard = ''),
        (state.channelDownId = '')
    }
  }
})

export const {
  getEpgMenu,
  getEpgMenuSuccess,
  getEpgMenuError,
  getEpgVersion,
  getEpgVersionSuccess,
  getEpgVersionError,
  getEpgChannel,
  getEpgChannelBackUp,
  getChannelDownId,
  getEpgChannelSuccess,
  getEpgChannelError,
  getEpgLineup,
  getEpgLineupSuccess,
  getEpgLineupError,
  getEpgFullData,
  getFavouriteLive,
  getFavouriteLiveSuccess,
  getFavouriteLiveError,
  addFavouriteLive,
  addFavouriteLiveSuccess,
  addFavouriteLiveError,
  delFavouriteLive,
  delFavouriteLiveSuccess,
  delFavouriteLiveError,
  clearFavouriteRespose,

  getLiveReminder,
  addLiveReminder,
  delLiveReminder,

  getLiveReminderSuccess,
  getLiveReminderError,
  addLiveReminderSuccess,
  addLiveReminderError,
  delLiveReminderSuccess,
  delLiveReminderError,

  getLiveTvRecording,
  getLiveTvSeriesRecordingList,
  getLiveTvCompleteRecording,
  getLiveTvProgressRecording,
  getLiveTvEpisodeRecoding,
  addLiveTvSeriesRecording,
  addLiveTvEpisodeRecording,
  delLiveTvSeriesRecording,
  delLiveTvEpisodeRecording,
  deleteMyContentRecording,
  getClearRecordingState,
  getLiveTvRecordingSuccess,
  getLiveTvCompletedRecordingSuccess,
  getLiveTvCompletedRecordingError,
  getLiveTvProgressRecordingSuccess,
  getLiveTvProgressRecordingError,
  getLiveTvRecordingError,
  getLiveTvEpisodeRecordingSuccess,
  getLiveTvEpisodeRecordingError,
  addLiveTvSeriesRecordingSuccess,
  addLiveTvSeriesRecordingError,
  addLiveTvEpisodeRecordingSuccess,
  addLiveTvEpisodeRecordingError,
  delLiveTvSeriesRecordingSuccess,
  delLiveTvSeriesRecordingError,
  delLiveTvEpisodeRecordingSuccess,
  delLiveTvEpisodeRecordingError,
  deleteMyContentRecordingSuccess,
  deleteMyContentRecordingError,

  getcurrentcardindex,

  getPayWayToken,
  getPayWayTokenSuccess,
  getPayWayTokenError,

  getPayWayPurchase_ButtonInfo,
  getPayWayPurchase_ButtonInfoSuccess,
  getPayWayPurchase_ButtonInfoError,

  getProgramDetailsData,
  getEpgFilteredData,
  getEpgFilterName,
  getEventDetails,
  getAlerts,
  getFavouriteAlerts,
  resetFavouriteAlerts,
  clearReminderState,
  getNoLockChannels,
  getClearEPGState,
  getPopUpState,
  setReminderPopupVisible,
  setFromHomeToLive
} = epgSlice.actions

export default epgSlice.reducer
