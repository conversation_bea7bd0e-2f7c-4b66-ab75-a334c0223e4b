import React, { useEffect, useRef, useState, useMemo } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useSelector, useDispatch } from 'react-redux'
import './ChooseImage.scss'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const ChooseProfileImg = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch();
  const { state } = useLocation()
  const { data, pageName } = state

  const profileRef = useRef([])
  const region = localStorage.getItem('region')

  const defaultImage = 'images/Profile_Icons/profile_image.png'
  const userImage = data?.user_image

  const profileAvatarData = useSelector(state => state?.profile?.profileAvatarData?.response?.avatars)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const loginapi = useSelector(state => state?.login?.loginSuccess?.response)
  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)

  const [avatarData, setAvatarData] = useState([])

  const handleImageClick = image => {
    navigate(pageName, {
      state: { selectedProf: image?.user_image, data: data,  username: data?.username },
      replace: true
    })
  }

  const handleBack=()=>{
    navigate(pageName, {
      state: { selectedProf: data?.user_image, data: data },
      replace: true
    })
  }
  useEffect(() => {
    if (profileAvatarData && profileAvatarData.length > 0) {
      setAvatarData(profileAvatarData);
    }
  }, [profileAvatarData]);

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461) {

      navigate(pageName, {
        state: { selectedProf: data?.user_image, data: data },
        replace: true
      })
    }
  }

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate(pageName, {
        state: { selectedProf: data?.user_image, data: data },
        replace: true
      })
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(()=>{
  pushScreenViewEvent({screenName:'choose_profile',screenData:loginapi,prevScreenName:pageName})
  }, [])

  //Below commented code for future use.

  // const rowData = [];
  // for (let i = 0; i < avatarData?.length; i++) {
  //   const row = [];
  //   for (let j = 0; j < (avatarData[i]?.collection?.length || 0); j++) {
  //     row.push(avatarData[i]?.collection?.[j] || null);
  //   }
  //   rowData.push(row);
  // }

  // const columnData = [];
  // for (let j = 0; j < Math.max(...avatarData?.map(avatar => avatar?.collection?.length || 0)); j++) {
  //   const column = [];
  //   for (let i = 0; i < avatarData?.length; i++) {
  //     column.push(avatarData[i]?.collection?.[j] || null);
  //   }
  //   columnData.push(column);
  // }


  return (
    <div className="choose-profile-image">
      <div className="image-profile-container">
        <div className="profile-app-logo">
          <img src={'images/claro-profile-logo.png'} className="logo-img" alt="logo" />
          <button className="back-button-profile focusable" id="profileApp"
           onClick={handleBack}>
            <img src={'images/Profile_Icons/ic_shortcut_amarillo.png'} className="yellow-dot" alt="img not found" />
            <img src={'images/Profile_Icons/ic_shortcut_back.png'} className="back-arrow" alt="img not found" />
            <span className="back-button-text">
              {translations?.language?.[region]?.atv_back_notification}
            </span>
          </button>
        </div>
        <div className="choose-profileimg-container">
          <p className="choose-profile-watch">
            {translations?.language?.[region]?.Perfiles_SeleccionarAvatar_Título_TextoTitulo ? translations?.language?.[region]?.Perfiles_SeleccionarAvatar_Título_TextoTitulo : 'Perfiles_SeleccionarAvatar_Título_TextoTitulo'}
          </p>
          <p className="user-name">{data?.username}</p>
          <img className="current-image" src={userImage ? userImage : defaultImage} />
        </div>
      </div>
      <div className="parent-imageselector-container">
        {avatarData &&
          avatarData?.map((item, rowIndex) => {

            if (item?.collection === null) {
              return null; 
            }

            const collection = item?.collection || []
            const collectionLength = collection.length

            // Scroll I want to do for number of times.
            const duplicationFactor = 15
            const extendedCollection = collectionLength >= 6 ? Array(duplicationFactor)?.fill(collection)?.reduce((acc, val) => acc.concat(val), []) : collection
            const extendedLength = extendedCollection.length

            return (
              <div className="parent-image-container" key={rowIndex}>
                {item.user_image && (
                  <div className="choose-profile-image-container">
                    <img className="image-category" src={item.user_image} alt={`Category ${rowIndex + 1}`} />
                  </div>
                )}
                <div className="choose-image-title">
                  {extendedCollection?.map((each, index) => {
                    const actualIndex = index % collectionLength
                    const isFirstElement = actualIndex === 0
                    const isLastElement = actualIndex === collectionLength - 1
                    const shouldRestrictFocus = collectionLength < 6
                    const prevIndex = (index - 1 + extendedLength) % extendedLength
                    const nextIndex = (index + 1) % extendedLength

                    const aboveRowIndex = rowIndex - 1
                    const belowRowIndex = rowIndex + 1
                    const aboveCollection = avatarData[aboveRowIndex]?.collection || []
                    const belowCollection = avatarData[belowRowIndex]?.collection || []
                    const aboveIndex = Math.min(actualIndex, aboveCollection.length - 1)
                    const belowIndex = Math.min(actualIndex, belowCollection.length - 1)

                    return (
                      <button
                        autoFocus={index === 0 && rowIndex === 0}
                        key={index}
                        ref={ref => { profileRef.current[rowIndex * extendedLength + index] = ref }}
                        className="img-button focusable"
                        onClick={() => handleImageClick(each)}
                        data-row-index={rowIndex}
                        data-collection-length={collectionLength}
                        data-sn-left={shouldRestrictFocus && isFirstElement ? null : `#profile-${rowIndex}-${prevIndex}`}
                        data-sn-right={shouldRestrictFocus && isLastElement ? null : `#profile-${rowIndex}-${nextIndex}`}
                        data-sn-up={aboveRowIndex >= 0 ? `#profile-${aboveRowIndex}-${aboveIndex}` : '#profileApp'}
                        data-sn-down={belowRowIndex < avatarData.length ? `#profile-${belowRowIndex}-${belowIndex}` : null}
                        id={`profile-${rowIndex}-${index}`}
                        onFocus={() => {
                          document
                            .getElementById(`profile-${rowIndex}-${index}`)?.scrollIntoView({
                              behavior: 'smooth',
                               block: 'center'
                            })
                        }}
                      >
                        <LazyLoadImage className="choose-profimgs" src={each?.user_image} alt={`Profile ${actualIndex + 1}`} />
                      </button>
                    )
                  })}
                </div>
              </div>
            )
          })}
      </div>
    </div>
  )
}

export default ChooseProfileImg
