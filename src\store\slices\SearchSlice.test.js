import searchReducer, {
  getSearchData,
  getSearchSuccess,
  getSearchError,
  getProgressbar<PERSON><PERSON>mark,
  getProgressbarBookmarkSuccess,
  getProgressbarBookmarkError,
  getPredictiveSearchData,
  getPredictiveSearchSuccess,
  getPredictiveSearchError,
  getMulticontentSearchData,
  getMultiContentSearchSuccess,
  getMultiContentSearchError,
  getTalentSearchData,
  getTalentSearchSuccess,
  getTalentSearchError,
  setShowTalentModule,
  setShowApiFailureModal,
  getTalentSearch,
  setReturnFocusById,
  setVodReturnFocusById,
  removeReturnFocusById,
  removeVodReturnFocusById
} from './SearchSlice'

describe('search slice reducers', () => {
  const initialState = {
    data: [],
    predictiveData: [],
    suggest: [],
    isPredictive: false,
    isLoading: false,
    error: null,
    popularSearch: '',
    isMulticontent: false,
    multiContentSearch: [],
    isProgressBarContent: false,
    isLoadingTalentSearch: false,
    progressbarContent: [],
    talentSearchData: null,
    showTalentModule: false,
    showApiFailureModal: false,
    talentSearch: {},
    setReturnFocus: null,
    setVodReturnFocus: null,
    isRecomErrorLoading: false,
    isRecomendationLoading: false,
  }

  it('should handle initial state', () => {
    expect(searchReducer(undefined, {})).toEqual(initialState)
  })

  it('should handle getSearchData', () => {
    expect(searchReducer(initialState, getSearchData())).toEqual({
      ...initialState,
      isRecomendationLoading: true
    })
  })

  it('should handle getSearchSuccess', () => {
    const payload = {
      response: {
        id: '704561'
      }
    }
    expect(searchReducer(initialState, getSearchSuccess(payload))).toEqual({
      ...initialState,
      isLoading: false,
      data: payload.respons
    })
  })

  it('should handle getSearchError', () => {
    const payload = 'Error message'
    expect(searchReducer(initialState, getSearchError(payload))).toStrictEqual({
      ...initialState,
      isLoading: false,
      error: payload
    })
  })

  it('should handle getProgressbarBookmark', () => {
    expect(searchReducer(initialState, getProgressbarBookmark())).toEqual({
      ...initialState,
      isProgressBarContent: true
    })
  })

  it('should handle getProgressbarBookmarkSuccess', () => {
    const payload = {
      response: {
        groups: [
          {
            id: '704561'
          }
        ]
      }
    }
    expect(
      searchReducer(initialState, getProgressbarBookmarkSuccess(payload))
    ).toEqual({
      ...initialState,
      isProgressBarContent: false,
      progressbarContent: payload.response.groups
    })
  })

  it('should handle getProgressbarBookmarkError', () => {
    const payload = 'Error message'
    expect(
      searchReducer(initialState, getProgressbarBookmarkError(payload))
    ).toEqual({
      ...initialState,
      isProgressBarContent: false,
      error: payload
    })
  })

  it('should handle getPredictiveSearchData', () => {
    expect(searchReducer(initialState, getPredictiveSearchData())).toEqual({
      ...initialState,
      isPredictive: true
    })
  })

  it('should handle getPredictiveSearchSuccess', () => {
    const payload = { response: { suggest: ['suggest1'], data: ['data1'] } }
    expect(
      searchReducer(initialState, getPredictiveSearchSuccess(payload))
    ).toEqual({
      ...initialState,
      isPredictive: false,
      predictiveData: { suggest: ['suggest1'], data: ['data1'] },
      suggest: ['suggest1']
    })
  })

  it('should handle getPredictiveSearchError', () => {
    const payload = 'Error message'
    expect(
      searchReducer(initialState, getPredictiveSearchError(payload))
    ).toEqual({
      ...initialState,
      isPredictive: false,
      error: payload
    })
  })

  it('should handle getMulticontentSearchData', () => {
    expect(searchReducer(initialState, getMulticontentSearchData())).toEqual({
      ...initialState,
      isMulticontent: true
    })
  })

  it('should handle getMultiContentSearchSuccess', () => {
    const payload = { data: ['content1', 'content2'] }
    expect(
      searchReducer(initialState, getMultiContentSearchSuccess(payload))
    ).toEqual({
      ...initialState,
      isMulticontent: false,
      multiContentSearch: ['content1', 'content2']
    })
  })

  it('should handle getMultiContentSearchError', () => {
    const payload = 'Error message'
    expect(
      searchReducer(initialState, getMultiContentSearchError(payload))
    ).toEqual({
      ...initialState,
      isMulticontent: false,
      error: payload
    })
  })

  it('should handle getTalentSearchData', () => {
    expect(searchReducer(initialState, getTalentSearchData())).toEqual({
      ...initialState,
      isLoadingTalentSearch: true
    })
  })

  it('should handle getTalentSearchSuccess', () => {
    const payload = { response: { groups: ['talent1', 'talent2'] } }
    expect(
      searchReducer(initialState, getTalentSearchSuccess(payload))
    ).toEqual({
      ...initialState,
      isLoadingTalentSearch: false,
      talentSearchData: ['talent1', 'talent2']
    })
  })

  it('should handle getTalentSearchError', () => {
    const payload = 'Error message'
    expect(searchReducer(initialState, getTalentSearchError(payload))).toEqual({
      ...initialState,
      isLoadingTalentSearch: false,
      error: payload
    })
  })

  it('should handle setShowTalentModule', () => {
    expect(searchReducer(initialState, setShowTalentModule(true))).toEqual({
      ...initialState,
      showTalentModule: true
    })
  })

  it('should handle setShowApiFailureModal', () => {
    expect(searchReducer(initialState, setShowApiFailureModal(true))).toEqual({
      ...initialState,
      showApiFailureModal: true
    })
  })

  it('should handle getTalentSearch', () => {
    const payload = { key: 'value' }
    expect(searchReducer(initialState, getTalentSearch(payload))).toEqual({
      ...initialState,
      talentSearch: { key: 'value' }
    })
  })

  it('should handle setReturnFocusById', () => {
    let payload = 'someId'
    expect(searchReducer(initialState, setReturnFocusById(payload))).toEqual({
      ...initialState,
      setReturnFocus: 'someId'
    })
  })

  it('should handle setVodReturnFocusById', () => {
    let payload = 'someId'
    expect(searchReducer(initialState, setVodReturnFocusById(payload))).toEqual(
      {
        ...initialState,
        setVodReturnFocus: 'someId'
      }
    )
  })

  it('should handle removeReturnFocusById', () => {
    expect(searchReducer(initialState, removeReturnFocusById())).toEqual({
      ...initialState,
      setReturnFocus: null
    })
  })

  it('should handle removeVodReturnFocusById', () => {
    expect(searchReducer(initialState, removeVodReturnFocusById())).toEqual({
      ...initialState,
      setVodReturnFocus: null
    })
  })
})
