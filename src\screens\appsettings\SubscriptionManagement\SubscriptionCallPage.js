import React, { useCallback, useEffect, useMemo } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './SubscriptionCall.scss'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import { useDispatch } from 'react-redux'
import { clearGetContractBackHandle } from '../../../store/slices/settingsSlice'

const SubscriptionCallPage = () => {
  const navigate = useNavigate()
  const state = useLocation()
  const subCallData = state
  const dispatch = useDispatch()
  const region = localStorage.getItem('region')
  const liveChannelId = localStorage.getItem('live-playing-channel-id') !=
  'undefined'
    ? localStorage.getItem('live-playing-channel-id')
    : ''

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []
  const loginResponse = useSelector(
    state => state?.login?.loginSuccess?.response
  )
  const isAnyBack = useSelector(state => state?.settingsReducer?.toBackPage)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  
  useEffect(() => {
    SpatialNavigation.focus()
  }, [])

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handleLivePlayer = () => {
    const isSubscribed = item => {
      const foundContract = paywayResponse?.filter(each =>
        each?.groups?.includes(item)
      )
      return foundContract?.length > 0 ? true : false
    }
    // only for reset channel data nonsubscribed user
    if (
      (loginResponse?.subscriptions &&
        loginResponse?.subscriptions?.length == 0) ||
      !isSubscribed(subCallData?.state?.id)
    ) {
      const groupId = liveChannelId || state?.playerData?.group_id
      dispatch(
        getChannelData({
          group_id: groupId,
          timeshift: '',
          switchChannel: 'yes',
          epgIndex: epgSevenDaysData[1]?.channelResponse?.findIndex(
            itrObj => itrObj.group_id == groupId
          )
        })
      )
    }
    if (isAnyBack?.backPage) {
      dispatch(clearGetContractBackHandle())
      navigate(isAnyBack?.backPage, {
        state: {
          backfocusid: isAnyBack?.focusedId
        }
      })
    } else {
      navigate('/livePlayer', {
        state: { showControls: 'live' },
        replace: true
      })
    }
  }
  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      if (subCallData?.state?.pageName == 'search') {
        localStorage.setItem('currNavIdx', -1)
        navigate('/search', {
          state: {
            inputValue: subCallData?.state?.inputValue,
            data: subCallData?.state?.data
          },
          replace: true
        })
      } else if (subCallData?.state?.pageName == 'settings/profile-settings') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'recordatorios' }
        })
      } else if (subCallData?.state?.pageName == 'livePlayer') {
        handleLivePlayer()
      }
    }
  }
  const handleLgkey = keycode => {
    if (
      keycode == 405 ||
      keycode === 461 ||
      keycode == 8 ||
      keycode == 'backClick'
    ) {
      if (subCallData?.state?.pageName == 'search') {
        localStorage.setItem('currNavIdx', -1)
        navigate('/search', {
          state: {
            inputValue: subCallData?.state?.inputValue,
            data: subCallData?.state?.data
          },
          replace: true
        })
      } else if (subCallData?.state?.pageName == 'settings/profile-settings') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'recordatorios' }
        })
      } else if (subCallData?.state?.pageName == 'livePlayer') {
        handleLivePlayer()
      }
    }
  }

  const handleBackNavigation = () => {
    typeof tizen == 'undefined'
      ? handleLgkey('backClick')
      : handlesamsungkey(10009)
  }

  const getCallPageTitle = useCallback(
    (familyCode, keyname) => {
      return apilanguage?.[familyCode + keyname]
    },
    [apilanguage]
  )

  const proveedorCode = useMemo(
    () =>
      subCallData?.state?.data?.proveedor_code ??
      subCallData?.state?.data?.channel_group?.common?.proveedor_code,
    [subCallData]
  )

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const titleData = useMemo(
    () => ({
      title: getCallPageTitle(
        proveedorCode,
        '_subscription_modal_title_OfferCallCenter_label'
      ),
      description: getCallPageTitle(
        proveedorCode,
        '_subscription_modal_description_OfferCallCenter_label'
      ),
      numberTitle: getCallPageTitle(
        proveedorCode,
        '_subscription_modal_titleNumber_OfferCallCenter_label'
      ),
      number: getCallPageTitle(
        proveedorCode,
        '_subscription_modal_number_OfferCallCenter_label'
      ),
      note: getCallPageTitle(
        proveedorCode,
        '_subscription_modal_note_OfferCallCenter_label'
      ),
      buttonText: getCallPageTitle(
        proveedorCode,
        '_subscription_modal_option_button_close'
      )
    }),
    [getCallPageTitle, proveedorCode]
  )

  return (
    <div className="background-call-page">
      <img src={'images/logo.png'} className="claro-symbol" alt="logo" />
      <button
        className="back-button-indicator focusable"
        autoFocus={true}
        id="backButton"
        onClick={() => handleBackNavigation()}
      >
        <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
        <img className="back-image" src={'images/back_button.png'} />
        <span className="back-text">
          {truncateText('BotonShortcut_TextoTitulo_Regresar')}
        </span>
      </button>
      <div className="call-wrapper">
        <span
          className="first-title-text"
          style={{ width: titleData.title ? '554px' : '' }}
        >
          {titleData.title ??
            truncateText('subscription_modal_title_OfferCallCenter_label')}
        </span>
        <span
          className="second-title-text"
          style={{ width: titleData.description ? '554px' : '' }}
        >
          {titleData.description?.replace('canales', 'canales\n') ||
            truncateText(
              'subscription_modal_description_OfferCallCenter_label'
            )}
        </span>
        <span
          className="third-title-text"  
        >
          {titleData.numberTitle ??
            truncateText(
              'subscription_modal_titleNumber_OfferCallCenter_label'
            )}
        </span>
        <span className="fourth-title-text">
          {titleData.number
            ? `{${titleData.number.replace(/-/g, ' ').trim()}}`
            : `{ ${truncateText(
                'subscription_modal_number_OfferCallCenter_label'
              )} }`}
        </span>
        <span
          className="fifth-title-text"
        >
          {titleData.note ||
            truncateText('subscription_modal_note_OfferCallCenter_label')}
        </span>
        <button
          autoFocus
          className="acceptar-button focusable"
          onClick={() => handleBackNavigation()}
        >
          {titleData.buttonText ??
            truncateText('subscription_modal_option_button_close')}
        </button>
      </div>
    </div>
  )
}

export default SubscriptionCallPage
