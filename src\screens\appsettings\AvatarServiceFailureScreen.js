import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation, useNavigate} from 'react-router-dom'
import './AvatarServiceFailureScreen.scss'
import {clearIsloggedInStatus} from '../../store/slices/login'

const AvatarServiceFailureScreen = () => {
  const { state } = useLocation()
  const navigate  = useNavigate()
  const dispatch = useDispatch()
  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const {
    atv_unexpected_error: unexpectedError,
    contents_withoutResults_talents_message_label: actionIncompleteText,
    btn_modal_ok: okButtonText
  } = apilanguage

  const userProfileData = useSelector(state => state?.profile?.userProfile)

  const keypresshandler = event => {
    event.preventDefault()
    const keycode = event.keyCode
    if (keycode === 10009 || keycode === 461 || keycode === 13) {
      handleOkClick()
    }
  }
  
  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handleOkClick = () => {
    state?.pageName == 'watchProfile' &&
      navigate('/watchprofile', {
        state: { data: '', page: 'home', img: userProfileData }
      })
    state?.pageName == 'settings' && navigate('/settings')
    dispatch(clearIsloggedInStatus())
  }

  return (
    <div className="error-page">
      <div className="claro-video-logo-container">
        <img
          className="claro-video-logo"
          src="images/Logos_Claro_Video.svg"
          alt="Claro Video Logo"
        />
      </div>
      <div className="error-screen-container">
        <img className="alert-img" src="images/ic_alert.png" alt="Alert Icon" />
        <h1 className="unexpected-error-text">{unexpectedError}</h1>
        <p className="action-incomplete-text">{actionIncompleteText}</p>
        <button className="accept-btn-container" onClick={handleOkClick}>
          <span className="accept-text">{okButtonText}</span>
        </button>
      </div>
    </div>
  )
}

export default AvatarServiceFailureScreen
