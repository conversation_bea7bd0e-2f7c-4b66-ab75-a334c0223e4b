import React from "react";
import { fireEvent, queryByAttribute, render, screen, act } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import CheckSecurityPin from "./CheckSecurityPin";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<MemoryRouter
			history={history}
			initialEntries={[{ state: { pageName: 'classificationToggle' } }]}
		>
			{children}
		</MemoryRouter>
	</Provider>
);
export const renderWithState = (ui, customState = {}) => {
	const store = mockStore({...initialState, ...customState});
	return render(ui, { wrapper: ({ children }) => Wrapper({ children, reduxStore: store }) });
};

const mockErrorResponse = {
	"status": "1"
}

const mockIsLoggedInSuccessResponse = {
	response: {
		session_stringvalue: "ZTEATV412001224226580292a69e33",
		user_id: "94520846",
		session_userhash: "abc123",
		user_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************"
	}
}

const mockStatusControlPinSuccessResponse = {
	response: {
		pin_purchase: {
			status: 0
		},
		pin_parental: {
			status: 1,
			info: {
				name: "<![CDATA[NC-17]]>",
				description: "<![CDATA[Apta para mayores de 18]]>",
				value: 50
			}
		},
		pin_channel: {
			status: 1,
			info: []
		},
		status: 0
	}
}

const checkControlPinSuccessResponse = {
	msg: 'OK'
}

const checkControlPinErrorResponse = {
	msg: 'ERROR',
	errors: "Invalid PIN"
}

const mockModifyControlPinSuccessResponse = {
	data: {
		update_accomplishment_status: {
			purchases_pin: 0,
			parental_pin: 1,
			channel_pin: 1
		}
	}
}

const mockRemindPinSuccessResponse = {
	response: {
		email_sent: true
	}
}

const mockAddSeriesRecordingSuccess = {
	response: {
		success: true
	}
}

const mockAddEpisodeRecordingSuccess = {
	response: {
		success: true
	}
}

const mockDelSeriesRecordingSuccess = {
	response: {
		success: true
	}
}

const mockDelEpisodeRecordingSuccess = {
	response: {
		success: true
	}
}

const mockLockedChannelAdd = {
	status: "success"
}

const mockRecordingErrors = {
	errors: [
		{
			code: 'PLY_REC_00014'
		}
	]
}

const mockAppMetaData = {
	translations: JSON.stringify({
		language: {
			en: {
				recording_alert_description_presentSeason_label: "Season is recording",
				recording_alert_description_pastSeason_label: "Season has been recorded",
				recording_alert_description_futureSeason_label: "Season will be recorded",
				recording_alert_description_presentEpisode_label: "Episode is recording",
				recording_alert_description_futureEpisode_label: "Episode will be recorded",
				cancelRecording_alert_title_futureEvent_label: "Recording canceled",
				cancelRecording_alert_description_futureSeason_label: "Season will not be recorded",
				cancelRecording_alert_description_episode_label: "Episode will not be recorded",
				deleteRecording_alert_description_pastEpisode_label: "Episode has been deleted",
				PLY_REC_00014_title: "Storage alert",
				PLY_REC_00014_message: "Not enough storage",
				lockChannel_alert_detail_label: "has been locked",
				TvEnVivo_Notificacion_TextoCuerpo_CanalBloqueado: "Channel blocked"
			}
		}
	})
}

describe('Parental Control Settings page test', () => {

	test('should render without api mock data', () => {
		initialState.login = {
			isLoggedIn: mockErrorResponse
		}
		initialState.settingsReducer = {
			statusControlPin: mockErrorResponse,
			modifyControlPin: mockErrorResponse,
			checkControlPin: mockErrorResponse,
			remindControlPin: mockErrorResponse
		}
		initialState.initialReducer = {
			appMetaData: mockAppMetaData
		}
		renderWithState(<CheckSecurityPin />)
	})

	test('should render with api mock data', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		}
		initialState.settingsReducer = {
			statusControlPin: mockStatusControlPinSuccessResponse,
			modifyControlPin: mockModifyControlPinSuccessResponse,
			checkControlPin: checkControlPinSuccessResponse,
			remindControlPin: mockRemindPinSuccessResponse
		}
		initialState.initialReducer = {
			appMetaData: mockAppMetaData
		}
		renderWithState(<CheckSecurityPin />)
	})

	test('navigate back', () => {
		const { container } = renderWithState(<CheckSecurityPin />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'backButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('pin visibility button click', () => {
		const { container } = renderWithState(<CheckSecurityPin />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'seePin')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('siguinete button click', () => {
		const { container } = renderWithState(<CheckSecurityPin />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'siguienteButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('forget pin button click', () => {
		const { container } = renderWithState(<CheckSecurityPin />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'forgetPin')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('should handle TV remote key press', () => {
		const { container } = renderWithState(<CheckSecurityPin />)
		
		// Simulating remote keypad input
		fireEvent.keyUp(container, { key: '1', keyCode: 49 })
		fireEvent.keyUp(container, { key: '2', keyCode: 50 })
		fireEvent.keyUp(container, { key: '3', keyCode: 51 })
		
		// Non-numeric key should be ignored
		fireEvent.keyUp(container, { key: 'a', keyCode: 65 })
	})
	
	test('should handle cancel button click', () => {
		const { container } = renderWithState(<CheckSecurityPin />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'cancelButton')
		fireEvent.focus(buttonClick)
		fireEvent.click(buttonClick)
	})
	
	test('should handle Samsung TV remote key events', () => {
		// Mock tizen API
		global.tizen = {
			tvinputdevice: {
				registerKeyBatch: jest.fn(),
				getKey: jest.fn().mockReturnValue({ code: 403 })
			}
		}
		
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { statusControlPin: mockStatusControlPinSuccessResponse },
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const { container } = renderWithState(<CheckSecurityPin />, mockState)
		
		// Simulate Samsung yellow button press
		fireEvent.keyUp(container, { keyCode: 403 })
		
		// Cleanup
		delete global.tizen
	})
	
	test('should handle content classification navigation', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse 
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ state: { pageName: 'contentClassification' } }]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle lockChannelModification navigation', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse 
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ state: { pageName: 'lockChannelModification' } }]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle lockChannelToggle with blockValue', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse,
				modifyControlPin: mockModifyControlPinSuccessResponse,
				lockedChannelAdd: mockLockedChannelAdd
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'lockChannelToggle',
							subType: 'lockChannelToggleOn',
							blockValue: 1,
							returnPage: true,
							blockChannelData: {
								name: 'Test Channel',
								number: '123',
								common: { id: '456' },
								group_id: '789'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle classificationToggle', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse,
				modifyControlPin: mockModifyControlPinSuccessResponse
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'classificationToggle',
							classificationValue: 1,
							parentalCode: 50
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle livePlayerRecord for Series', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse
			},
			epg: {
				addSeriesRecording: mockAddSeriesRecordingSuccess
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'livePlayerRecord',
							recordType: 'Series',
							eventStatus: 'Present Event',
							payload: {
								channelId: '123',
								programId: '456'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle livePlayerRecord for Episode', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse
			},
			epg: {
				addEpisodeRecording: mockAddEpisodeRecordingSuccess
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'livePlayerRecord',
							recordType: 'Episode',
							eventStatus: 'Future Event',
							payload: {
								channelId: '123',
								programId: '456'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle unlockEvent for Series', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse
			},
			epg: {
				deleteRecordingSeries: mockDelSeriesRecordingSuccess
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'livePlayerRecord',
							recordType: 'Series',
							status: 'unlockEvent',
							eventStatus: 'Present Event',
							payload: {
								channelId: '123',
								programId: '456'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle unlockEvent for Episode with Past event', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse
			},
			epg: {
				deleteRecordingEpisode: mockDelEpisodeRecordingSuccess
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'livePlayerRecord',
							recordType: 'Episode',
							status: 'unlockEvent',
							eventStatus: 'Past Event',
							payload: {
								channelId: '123',
								programId: '456'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	test('should handle recording errors', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse
			},
			epg: {
				recordingErrors: mockRecordingErrors
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'livePlayerRecord',
							recordType: 'Episode',
							payload: {
								channelId: '123',
								programId: '456'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle unlock series for Future event', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				checkControlPin: checkControlPinSuccessResponse
			},
			epg: {
				deleteRecordingSeries: mockDelSeriesRecordingSuccess
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const customWrapper = ({ children }) => (
			<Provider store={mockStore(mockState)}>
				<MemoryRouter
					history={history}
					initialEntries={[{ 
						state: { 
							pageName: 'livePlayerRecord',
							recordType: 'Series',
							status: 'unlockEvent',
							eventStatus: 'Future Event',
							payload: {
								channelId: '123',
								programId: '456'
							}
						} 
					}]}
				>
					{children}
				</MemoryRouter>
			</Provider>
		)
		
		const { container } = render(<CheckSecurityPin />, { wrapper: customWrapper })
		
		// Enter PIN and submit
		const getById = queryByAttribute.bind(null, 'id');
		fireEvent.click(getById(container, 'Key_1'));
		fireEvent.click(getById(container, 'Key_2'));
		fireEvent.click(getById(container, 'Key_3'));
		fireEvent.click(getById(container, 'Key_4'));
		fireEvent.click(getById(container, 'siguienteButton'));
	})
	
	test('should handle remind security PIN', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse,
				remindControlPin: mockRemindPinSuccessResponse
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		renderWithState(<CheckSecurityPin />, mockState)
	})
	
	test('should handle keyboard event handling for LG TV', () => {
		const mockState = {
			login: { isLoggedIn: mockIsLoggedInSuccessResponse },
			settingsReducer: { 
				statusControlPin: mockStatusControlPinSuccessResponse
			},
			initialReducer: { appMetaData: mockAppMetaData }
		}
		
		const { container } = renderWithState(<CheckSecurityPin />, mockState)
		
		// Simulate LG back button press (461)
		fireEvent.keyUp(container, { keyCode: 461 })
		
		// Simulate another LG remote button (405)
		fireEvent.keyUp(container, { keyCode: 405 })
	})
})
