.terms-and-condition{
    width: 1920px;
	height: 1080px;
	position: fixed;

.claro-logo {
		height: 95px;
		width: 245px;
		margin-top: 35px;
		margin-left: 90px;
	}

	.back-indicator {
		display: flex;
		height: 48px;
		width: 292px;
		border-radius: 6.6px;
		background-color: #2E303D;
		align-items: center;
		float: right;
		margin-top: 47px;
		margin-right: 64px;

		.yellow-indicator {
			height: 20px;
			width: 20px;
			margin-left: 24px;
			margin-right: 24px;
		}

		.back-image {
			height: 24px;
			width: 30px;
			margin-right: 24px;
		}

		.back-text {
			height: 30px;
			width: 146px;
			color: #FFFFFF;
			font-family: Roboto;
			font-size: 29.04px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 29.04px;
		}
	}
	.back-indicator:focus, .back-indicator:hover{
		background-color: #981C15;
	}

	.page-div{
		display: flex;
		flex-direction: row;
		font: normal normal normal 30px/40px Roboto;
		letter-spacing: 0px;
		color: #EEEEEE;
		opacity: 1;
		margin-top: 55px;
		justify-content: center;
	}

	.data-class{
		height: 76vh;
		overflow-y: scroll;
		width: 1310px;
	}

	.data-class::-webkit-scrollbar {
		width: 0.5em; /* Adjust the width as needed */
		background-color: transparent; /* Set the background color of the scrollbar track */
		display: flex;
	}

	.data-class::-webkit-scrollbar-thumb {
		background-color: #2E303D; /* Set the color of the scrollbar thumb */
		height: 180px;
	}

}