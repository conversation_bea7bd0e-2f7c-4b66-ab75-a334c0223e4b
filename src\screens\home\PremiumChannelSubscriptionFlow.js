import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './PremiumChannelSubscriptionFlow.scss'
import {
  clearGetContractBackHandle,
  getClearSubscriptionInfo,
  getViewSubscribeData
} from '../../store/slices/settingsSlice'
import { getChannelData } from '../../store/slices/PlayerSlice'
import { pushSubscriptionEvent } from '../../GoogleAnalytics'
import { SUBSCRIPTION_CAROUSEL_LIVE } from '../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const PremiumChannelSubscriptionFlow = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { state } = useLocation()
  const liveChannelId =
    localStorage.getItem('live-playing-channel-id') != 'undefined'
      ? localStorage.getItem('live-playing-channel-id')
      : ''

  const subscribeInfoData = state
  const [channelInfoHeight, setChannelInfoHeight] = useState(0)
  const channelInfoRef = useRef(null)

  const region = localStorage.getItem('region')
  const playerChannelData = useSelector(state => state?.player?.channelData)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
  const loginResponse = useSelector(
    state => state?.login?.loginSuccess?.response
  )
  const liveChannnelInfo = useSelector(state => state?.player?.playerinfo)
  const isAnyBack = useSelector(state => state?.settingsReducer?.toBackPage)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []

  const translations =
    apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const apilanguage = translations?.language?.[region]
  let channelIndex = epgSevenDaysData?.[1]?.channelResponse?.findIndex(
    itrObj => itrObj.group_id == liveChannelId
  )
  const viewData = useSelector(
    state => state?.settingsReducer?.viewSubscribeData
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)  
  const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys( userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbarTab,
        action_type : 'click boton'
      }

  //data to be passed here

  const settingLogoUrl = data => {
    return (
      apaAssetsImages?.[
        `Transaccionales_SuscripcionCheckoutA_ImagenDeContenido_${region}_${data}`
      ] ||
      apaAssetsImages?.[
        `Transaccionales_SuscripcionCheckoutA_ImagenDeContenido_${region}_${data?.toUpperCase()}`
      ] ||
      apaAssetsImages?.[
        `Transaccionales_SuscripcionCheckoutA_ImagenDeContenido_${data}`
      ] ||
      apaAssetsImages?.[
        `Transaccionales_SuscripcionCheckoutA_ImagenDeContenido_${data?.toUpperCase()}`
      ] ||
      'images/checkout_banner_placeholder.png'
    ) //checking the data is there in apa/assets
  }

  const getTaxLabel = data => {
    return truncateText(
      `${data}_subscriptionDescription_costTaxIncluded_label`,
      80
    )
  }

  const getSubscribeButton = data => {
    return truncateText('buy_' + data, 80)
  }

  const handleViewDetails = (e, monthData) => {
    if (state?.pageName == 'player') {
      pushSubscriptionEvent(userData,monthData,'',SUBSCRIPTION_CAROUSEL_LIVE)
      navigate('/my-settings/my-subscriptions/add-subscriptions', {
        state: {
          pageName: '/premiumSubscription',
          data: playerChannelData,
          inputValue: subscribeInfoData?.inputValue,
          returnPage: state?.returnPage,
          groupId: state?.groupId
        }
      })
    } else {
      const viewPlan = {
        verticalImage: settingLogoUrl(monthData?.banner),
        logo: channelLogoUrl(monthData?.family),
        workflowStart: monthData?.linkworkflowstart,
        family: monthData?.family,
        periodicity: monthData?.periodicity,
        price: monthData?.price,
        currency: monthData?.currency,
        styles: monthData?.style,
        scope: getScopeDescription(monthData?.producttype),
        taxLabel: getTaxLabel(monthData?.family),
        infoString: getFreeChargeString(monthData?.producttype),
        subscribeButton: getSubscribeButton(monthData?.family),
        viewButton: getViewDetailsButton(monthData?.producttype),
        producttype: monthData?.producttype,
        offertype: monthData?.oneoffertype,
        isPremiumChannel: true
      }
      dispatch(getViewSubscribeData(viewPlan))
      pushSubscriptionEvent(userData,viewPlan,'',SUBSCRIPTION_CAROUSEL_LIVE)
      navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
        state: {
          pageName: '/premiumSubscription',
          data: subscribeInfoData?.data,
          subscribeInfoData:
            subscribeInfoData?.priceDetails ?? state?.subscribeInfoData,
          returnPage: state?.returnPage,
          groupId: state?.groupId
        }
      })
    }
  }

  const handleSubscription = (e, monthData) => {
    const viewPlan = {
      verticalImage: settingLogoUrl(monthData?.banner),
      logo: channelLogoUrl(monthData?.family),
      workflowStart: monthData?.linkworkflowstart,
      family: monthData?.family,
      periodicity: monthData?.periodicity,
      price: monthData?.price,
      currency: monthData?.currency,
      styles: monthData?.style,
      scope: getScopeDescription(monthData?.producttype),
      taxLabel: getTaxLabel(monthData?.family),
      infoString: getFreeChargeString(monthData?.producttype),
      subscribeButton: getSubscribeButton(monthData?.family),
      viewButton: getViewDetailsButton(monthData?.producttype),
      producttype: monthData?.producttype,
      offertype: monthData?.oneoffertype,
      isPremiumChannel: true
    }
    dispatch(getViewSubscribeData(viewPlan))
    pushSubscriptionEvent(userData,viewPlan,'',SUBSCRIPTION_CAROUSEL_LIVE)
    navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new', {
      state: {
        pageName: '/premiumSubscription',
        data: subscribeInfoData?.data,
        subscribeInfoData:
          subscribeInfoData?.priceDetails ?? state?.subscribeInfoData,
        returnPage: state?.returnPage,
        groupId: state?.groupId
      }
    })
  }

  const channelLogoUrl = data => {
    return (
      apaAssetsImages?.[
        'Transaccionales_ContratarAddon_ImagenLogoCanal_' + data
      ] ||
      apaAssetsImages?.[
        'Transaccionales_ContratarAddon_ImagenLogoCanal_' + data?.toUpperCase()
      ] ||
      'images/CV_MENSUAL.png'
    ) //checking the data is there in apa/assets
  }

  const getScopeDescription = data => {
    return truncateText(
      `Transaccionales_ContratarAddon_TextoPromocion_${data}` ||
        `Transaccionales_ContratarAddon_TextoPromocion_${data?.toUpperCase()}`,
      40
    )
  }

  const getFreeChargeString = data => {
    return truncateText(
      `Transaccionales_ContratarAddon_TextoOferta_${data}` ||
        `Transaccionales_ContratarAddon_TextoOferta_${data?.toUpperCase()}`,
      18
    )
  }

  const getViewDetailsButton = data => {
    return truncateText(
      `Transaccionales_ContratarAddon_TextoQueIncluye_${data}`,
      25
    )
  }

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    dispatch(getClearSubscriptionInfo())
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const isSubscribed = item => {
    const foundContract = paywayResponse?.filter(each =>
      each?.groups?.includes(item)
    )
    return foundContract?.length > 0 ? true : false
  }

  const handleLivePlayer = () => {
    // only for reset channel data nonsubscribed user
    if (
      (loginResponse?.subscriptions &&
        loginResponse?.subscriptions?.length == 0) ||
      !isSubscribed(state?.data?.id)
    ) {
      const groupId =
        epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group_id ??
        paywayResponse?.[0]?.groups?.split(',').shift()

      dispatch(
        getChannelData({
          group_id: groupId,
          timeshift:
            epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group
              ?.common?.timeshift,
          switchChannel: 'yes',
          epgIndex: channelIndex
        })
      )
    }
    if (isAnyBack?.backPage) {
      dispatch(clearGetContractBackHandle())
      navigate(isAnyBack?.backPage, {
        state: {
          backfocusid: isAnyBack?.focusedId
        }
      })
    } else {
      navigate('/livePlayer', {
        state: { showControls: 'live', returnPage: 'livePlayer' },
        replace: true
      })
    }
  }

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      if (
        state?.returnPage === 'search' ||
        state?.pageName === 'search' ||
        state?.returnPage === 'searchToPlayer'
      ) {
        localStorage.setItem('currNavIdx', -1)
        navigate('/search', {
          state: {
            inputValue: subscribeInfoData?.inputValue,
            data: state?.data
          },
          replace: true
        })
      } else if (state?.returnPage == 'settings/profile-settings') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'recordatorios' }
        })
      } else if (
        state?.pageName == 'livePlayer' ||
        state?.returnPage === 'epgToPlayer'
      ) {
        handleLivePlayer()
      } else {
        if (state?.pageName === '/home') {
          localStorage.setItem('currNavIdx', -1)
        }
        navigate(state?.pageName)
      }
    }
  }

  const handleLgkey = keycode => {
    if (
      keycode == 405 ||
      keycode === 461 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
      keycode == 'backClick' ||
      keycode === 8
    ) {
      if (
        state?.returnPage === 'search' ||
        state?.pageName === 'search' ||
        state?.returnPage === 'searchToPlayer'
      ) {
        localStorage.setItem('currNavIdx', -1)
        navigate('/search', {
          state: {
            inputValue: subscribeInfoData?.inputValue,
            data: state?.data
          },
          replace: true
        })
      } else if (state?.returnPage == 'settings/profile-settings') {
        navigate('/settings/profile-settings', {
          state: { pageName: 'recordatorios' }
        })
      } else if (
        state?.pageName === 'livePlayer' ||
        state?.returnPage === 'epgToPlayer'
      ) {
        handleLivePlayer()
      } else {
        if (state?.pageName === '/home') {
          localStorage.setItem('currNavIdx', -1)
        }
        navigate(state?.pageName)
      }
    }
  }

  useEffect(() => {
    SpatialNavigation.focus()
  }, [])

  useEffect(() => {
    channelInfoRef.current &&
      setChannelInfoHeight(channelInfoRef.current.offsetHeight)
  }, [subscribeInfoData?.data?.proveedor_code])

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const getPremiumSubscriptionTitles = () => {
    return truncateText('Transaccionales_ContratarAddon_TextoTitulo', 100)
  }

  const getPremiumPeriodicity = data => {
    return apilanguage?.['subscription_' + data + '_month_label']
  }

  const handleBackNavigation = () => {
    typeof tizen == 'undefined'
      ? handleLgkey('backClick')
      : handlesamsungkey(10009)
  }

  return (
    <div className="north-pack-subscription">
      <button
        className="north-back-indicator focusable"
        onClick={() => handleBackNavigation()}
        id="northBackButtonId"
        data-sn-down="#viewDetailButton"
      >
        <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
        <img className="back-image" src={'images/back_button.png'} />
        <p className="back-text">
          {truncateText('BotonShortcut_TextoTitulo', 30)}
        </p>
      </button>
      <div className="subscription-container">
        <p className="subscription-title">
          {`${getPremiumSubscriptionTitles()}`}
        </p>
        <img
          src={channelLogoUrl(
            subscribeInfoData?.priceDetails?.family ??
              state?.subscribeInfoData?.family
          )}
          className={'channel-sub-logo'}
        />
        <span className="pricing">
          <p className="price-info">{`${
            subscribeInfoData?.priceDetails?.currency ??
            state?.subscribeInfoData?.currency
              ? subscribeInfoData?.priceDetails?.currency ??
                state?.subscribeInfoData?.currency
              : '$'
          }${
            subscribeInfoData?.priceDetails?.price ??
            state?.subscribeInfoData?.price
          } `}</p>
          <p className="tax-info">
            <span className="price-slash">
              {`${truncateText(
                'Transaccionales_ContratarAddon_TextoDiagonal',
                5
              )} `}
            </span>
            <span className="premium-price-label">
              {truncateText(
                subscribeInfoData?.priceDetails?.periodicity ??
                  state?.subscribeInfoData?.periodicity
              )}
            </span>
          </p>
        </span>
        <span className="free-charge-str">
          {viewData?.infoString ??
            getFreeChargeString(subscribeInfoData?.priceDetails?.producttype)}
        </span>
        <span className="channel-info" ref={channelInfoRef}>
          {viewData?.scope ??
            getScopeDescription(subscribeInfoData?.priceDetails?.producttype)}
        </span>
        <button
          className="view-details-button focusable"
          id="viewDetailButton"
          autoFocus={true}
          style={{
            marginTop: `calc(19px + ${channelInfoHeight}px)`,
            backgroundColor: truncateText('Color_ColorQueIncluye')
          }}
          onClick={e =>
            handleViewDetails(
              e,
              subscribeInfoData?.priceDetails ?? state?.subscribeInfoData
            )
          }
          data-sn-up="#northBackButtonId"
        >
          {viewData?.viewButton ??
            getViewDetailsButton(
              subscribeInfoData?.priceDetails?.producttype ??
                state?.data?.producttype
            )}
        </button>
        <button
          className="subscription-button focusable"
          onClick={e =>
            handleSubscription(
              e,
              subscribeInfoData?.priceDetails ?? state?.subscribeInfoData
            )
          }
          style={{ backgroundColor: truncateText('Color_ColorPrimario') }}
        >
          {truncateText(
            'Transaccionales_ContratarAddon_TextoBotonPrimario',
            25
          )}
        </button>
        <button
          className="cancel-button focusable"
          onClick={() => handleBackNavigation()}
          style={{ backgroundColor: truncateText('Color_ColorSecundario') }}
        >
          {truncateText(
            'Transaccionales_ContratarAddon_TextoBotonSecundario',
            25
          )}
        </button>
      </div>
    </div>
  )
}

export default PremiumChannelSubscriptionFlow
