import React, { useCallback, useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import '../../styles/NavSubTab.css'
import {
  filterDadta,
  getcurrentindex,
  getSubMenu,
  getsubmenuval,
  getTabvalue,
  subMenuRailCardsdata,
  subMenuRailIndex
} from '../../store/slices/subMenuDataSlice'
import { store } from '../../store/sagaStore'
import GenreRailsContent from './GenreRailsContent'
import { getPremiumNodeValue } from '../../store/slices/HomeSlice'
import { pushScreenViewEvent, pushSubMenuEvent } from '../../GoogleAnalytics'

const NavSubMenu = props => {
  const premiumgenre = useSelector(
    state => state?.homeReducer?.navbarNodeData?.response?.nodes
  )

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const nav = useSelector(state => state?.homeReducer?.navbarData)
  const navIndex = localStorage.getItem('currNavIdx')
  const navPage = nav?.[navIndex]?.page?.toLowerCase() || ''
  const startTimeRef = useRef(null)
  const submenu = useSelector(state => state?.SubMenuFilter?.submenudata)
  const indexvalue = useSelector(state => state?.SubMenuFilter?.index)
  const premiumTabValue = useSelector(
    state => state?.homeReducer?.premiumNodeValue
  )
  const tabvalue =
    premiumTabValue && premiumTabValue?.length ? 'playandgo' : props?.tabValue
  const filteredObj = premiumgenre?.find(obj => obj.code === tabvalue)
  const hidedata = useSelector(state => state?.SubMenuFilter?.filterclickdata)
  const submenuindex = useSelector(state => state?.SubMenuFilter?.cuerrntindex)
  const premiumsubmenutab = useSelector(state => state?.SubMenuFilter?.tabvalue)
  const activefocus = useSelector(
    state => state?.SubMenuFilter?.filterbackfocus
  )
  const navData = useSelector(state => state?.homeReducer?.navbarNodeData?.response?.nodes) ?? []
  const navbarTab = useSelector(state => state?.homeReducer?.NavTabValue)
  const contentSection = navData?.find(each => each?.code === navbarTab)
  const [filterIndex, setFilterIndex] = useState(indexvalue || 0)

  const app_behavour =
    filteredObj?.app_behaviour && JSON?.parse(filteredObj?.app_behaviour)
  const show_node = app_behavour?.node_config?.show_node
  const bannerIndex = (parseInt(localStorage.getItem('bannerIndex'), 10) || 0) + 1
  const handleKeyDown = useCallback(event => {
    const element = document.getElementById('navtabgenres')
    if (element) {
      const cards = document.querySelectorAll('.NavSubTab')
      const focusedElement = document.activeElement
      const focusedIndex = Array.from(cards).indexOf(focusedElement)

      if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
        const nextCard = cards[focusedIndex + 1]
        const containerRect = element.getBoundingClientRect()
        const nextCardRect = nextCard.getBoundingClientRect()

        if (nextCardRect.right > containerRect.right) {
          element.scrollLeft += nextCardRect.right - containerRect.right
        }
      } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
        const prevCard = cards[focusedIndex - 1]
        const containerRect = element.getBoundingClientRect()
        const prevCardRect = prevCard.getBoundingClientRect()

        if (prevCardRect.left < containerRect.left) {
          element.scrollLeft -= containerRect.left - prevCardRect.left
        }
      }
    }
  }, [])

  useEffect(() => {
    if (filterIndex) {
      const element = document.getElementById(`genrenav${filterIndex}`)
      if (element) {
        element?.focus()
      }
    }
    const navtabgenres = document.getElementById('navtabgenres')
    navtabgenres && navtabgenres.addEventListener('keydown', handleKeyDown)
    return () => {
      navtabgenres && navtabgenres.removeEventListener('keydown', handleKeyDown)
    }
  }, [premiumTabValue, handleKeyDown])

   useEffect(() => {
     startTimeRef.current = Date.now()
     return () => {
      startTimeRef.current = null
    }
   }, [])

  useEffect(() => {
    if (activefocus) {
      const element = document.getElementById(`genrenav${filterIndex}`)
      activefocus && element?.scrollIntoView({ block: 'end', behavior: 'smooth' })
    }
  }, [activefocus])

  var childs = []
  if (!filteredObj || !filteredObj?.childs) {
    return null
  } else {
    childs = filteredObj?.childs
  }

  const mappedTexts = childs?.find(obj => obj?.code === premiumsubmenutab)

  const handleButtonClick = (event, data, index) => {
    const engagement_time_msec = Date.now() - startTimeRef.current
    //GA for submenu event
    pushScreenViewEvent({screenName:'inicio', screenData: userDetails, prevScreenName: 'profiles', contentSection: contentSection?.text})
    const subMenuEventData = {
      userDetails: userDetails,
      subMenuName: data?.text?.toLowerCase(),
      menuName: navPage,
      time: engagement_time_msec
    }
    pushSubMenuEvent(subMenuEventData)
    startTimeRef.current = Date.now()
    const subMenu = localStorage.getItem('subMenu')
    localStorage.setItem('subMenu', parseInt(subMenu) + 1)
    localStorage.removeItem('setFilterOptionActive')

    store.dispatch(filterDadta(false))
    store.dispatch(getcurrentindex(index))
    let code = mappedTexts
      ? mappedTexts?.childs?.length && mappedTexts?.childs[index]?.code
      : childs?.length && childs[index]?.code
    let behaviour = mappedTexts
      ? mappedTexts?.childs?.length && mappedTexts?.childs[index]?.app_behaviour
      : childs?.length && childs[index]?.app_behaviour
    let app_behaviour = behaviour && JSON?.parse(behaviour)
    event.preventDefault()
    if (code) {
      if ((!data?.childs) && (app_behaviour?.layout === 'filterbygenre' || !app_behaviour)) {
        localStorage.setItem('submenucode', code)
        store.dispatch(getSubMenu(code))
        store.dispatch(subMenuRailCardsdata(true))
      } else {
        store.dispatch(getsubmenuval(true))
        store.dispatch(getTabvalue(code))
        store.dispatch(getPremiumNodeValue(code))
      }
    }
    store.dispatch(subMenuRailIndex(index))
    setFilterIndex(index)
  }

  return (
    <div className="nav-sub-tab-container">
      <div className={submenu === true ? 'genrecarddata' : ''}>
        <div className={hidedata === true ? 'hidedata' : ''}>
          <div className="Subnav-block" id="navtabgenres">
            {mappedTexts ? (
              <div className="childsubmenu">
                {mappedTexts?.childs?.map((text, index, array) => {
                  return (
                    <>
                      <button
                        className={`NavSubTab focusable ${
                          submenuindex === index && submenu === true
                            ? 'NavSubTab active'
                            : ''
                        }`}
                        style={{
                          border:
                            submenuindex === index && submenu === true
                              ? 'none'
                              : ''
                        }}
                        key={index}
                        data-sn-down={submenu ? '#filter' : document.getElementById(`index${bannerIndex}0`) ? `#index${bannerIndex}0` : undefined}
                        data-sn-right={
                          index === mappedTexts.childs.length - 1
                            ? `#genrenav${index}`
                            : `#genrenav${index + 1}`
                        }
                        id={'genrenav' + index}
                        onClick={event => {
                            handleButtonClick(event,text, index)
                        }}
                      >
                        {text?.text}
                      </button>
                    </>
                  )
                })}
              </div>
            ) : (
              show_node == true &&
              childs?.map((text, index, array) => {
                return (
                  <button
                    className={`NavSubTab focusable ${
                      submenuindex === index && submenu === true
                        ? 'NavSubTab active'
                        : ''
                    }`}
                    key={index}
                    data-sn-down={submenu ? '#filter' : document.getElementById(`index${bannerIndex}0`) ? `#index${bannerIndex}0` : undefined}
                    data-sn-right={
                      index === childs.length - 1
                        ? `#genrenav${index}`
                        : `#genrenav${index + 1}`
                    }
                    id={'genrenav' + index}
                    onClick={event => {
                        handleButtonClick(event,text, index)
                    }}
                  >
                    {text?.text}
                  </button>
                )
              })
            )}
          </div>
        </div>
      </div>
      <section className={`${submenu === true ? 'subrails' : ''}`}>
        <GenreRailsContent subMenuIndex={filterIndex} />
      </section>
    </div>
  )
}

export default NavSubMenu
