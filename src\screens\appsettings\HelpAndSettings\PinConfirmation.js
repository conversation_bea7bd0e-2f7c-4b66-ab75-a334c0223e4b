import React, { useState, useEffect } from 'react'
import './PinConfirmation.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { clearRemindPinSettingsState } from '../../../store/slices/settingsSlice'
import { INTERACTION_FORGOTTEN_PIN, interactionType, TV } from '../../../GoogleAnalyticsConstants'
import { pushNewInteractionContentEvent } from '../../../GoogleAnalytics'


function PinConfirmation(props) {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { state } = useLocation();

  const region = localStorage.getItem('region');

  const apaMetaData = useSelector((state) => state?.initialReducer?.appMetaData)
  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)

  const remindSecurityPin = useSelector(state => state?.settingsReducer?.remindControlPin?.response?.email_sent)
  const loginDetails = useSelector((state) => state?.login?.loginSuccess?.response)

  const apilanguage = translations?.language?.[region]


  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const successText = translations?.language?.[region]?.lockChannel_modal_description.replace("{@EMAIL}", "")

  const handlePinConfirmation = () => {
    pushNewInteractionContentEvent(
      {
        module_name: 'revisa la bandeja de entrada o tu correo no deseado',
        ...state?.gaContentData
      },
      INTERACTION_FORGOTTEN_PIN,
      TV,
      interactionType?.ENTENDIDO
    )
    switch (state?.pageName) {
      case 'parentalControl':
        navigate('/settings/profile-settings', {
          state: {
            pageName: state?.pageName,
            focusElement: 'remindPin',
            gaContentData: state?.gaContentData
          }
        })
        break
      case 'pinCheck':
        navigate('/settings/profile-settings/check-pin', {
          state: {
            pageName: state?.returnPage,
            gaContentData: state?.gaContentData
          }
        })
        break
      case 'deactivatePin':
        navigate('/settings/profile-settings/deactivate-pin', {
          state: { gaContentData: state?.gaContentData }
        })
        break
      case 'changePin':
        navigate('/settings/profile-settings/change-pin', {
          state: { gaContentData: state?.gaContentData }
        })
        break
      default:
        navigate('/my-settings/help-And-Settings/security-pin/configure', {
          state: {
            data: state?.data,
            item: state?.item,
            pageName: state?.pageName,
            gaContentData: state?.gaContentData
          }
        })
        break
    }
    dispatch(clearRemindPinSettingsState())
  }

  const keypresshandler = (event) => {
    const keycode = event.keyCode;
    (keycode === 10009 || keycode === 461) &&
      handlePinConfirmation()
  };

  useEffect(() => {
    document.addEventListener("keyup", keypresshandler);
    return () => {
      document.removeEventListener("keyup", keypresshandler);
    };
  }, [keypresshandler])


  return (
    <div className='pin-confirmation-container'>
      <div className='pin-confirmation-logo-container'>
        <img
          className="pin-confirmation-logo"
          src={'images/logo.png'}
        />
      </div>
      <div className="pin-confirmation-sub-container">
        <div className='pin-confirmation-message-heading-container'>
          {(remindSecurityPin && loginDetails?.validEmail) ?
            <p className='pin-confirmation-message-heading'>{truncateText('modal_pin_recovery_title', 150)}</p>
            :
            <p className='pin-confirmation-message-heading'>{translations?.language?.[region]?.updateMailCallCenter_modal_title_label}</p>
          }
        </div>
        <div className='pin-confirmation-message-container'>
          {(remindSecurityPin && loginDetails?.validEmail) ?
            <p className='pin-confirmation-message'>{truncateText('modal_pin_recovery_subtitle', 70)} {loginDetails?.email}</p>
            :
            <p className='pin-confirmation-message'>{translations?.language?.[region]?.updateMailCallCenter_modal_description_label}</p>
          }
        </div>
        <div className='pin-confirmation-button-container focusable'>
          <button
            autoFocus
            className='pin-confirmation-button focusable'
            onClick={handlePinConfirmation}
          >
            {truncateText('modal_pin_recovery_button', 30)}
          </button>
        </div>
      </div>
    </div>
  )
}

export default PinConfirmation