import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import LoginForgotPasswordMail from "./LoginForgotPasswordMail";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
);
export const renderWithState = (ui) => {
  return render(ui, { wrapper: Wrapper });
};

describe('LoginForgotPasswordMail page test', () => {
  test('input should change after onChange', () => {
    const result = renderWithState(<LoginForgotPasswordMail />)
    let input = result.container.querySelector('input[name="email"]')
    fireEvent.change(input, { target: { value: '<EMAIL>' } })
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(result.container, 'signinid');
    fireEvent.keyUp(scroll, { keyCode: '10009' })
    fireEvent(
      scroll,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
    renderWithState(<LoginForgotPasswordMail />)
  })
})