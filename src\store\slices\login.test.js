import initialSlice, {
    setSkeltonLoading,
    clearIsloggedInStatus,
    getLogin,
    getVcardData,
    getSeriesData,
    checkLoggedin,
    getImage,
    getImageSuccess,
    loginSuccess,
    getRegister,
    getUserInfo,
    getUserInfoSuccess,
    getLoginSuccess,
    loginError,
    getRegisterSuccess,
    getRegisterError,
    getPassword,
    recoverEmail,
    getIsLoggedinV1,
    getIsLoggedinV1Error,
    getClearAllLoginStates,
    clearLoginInfoState,
    clearLoginUserInfoState,
    getLoginNavigation,
    getRegisterNavigation,
    getWatchFree,
    getRegisterPopup,
    getBackNavigate,
    getPasswordSccess,
    getGuestUserPlayerData
} from './login'

describe('initialSlice reducer', () => {
    const initialState = {
        loginInfo: {},
        userInfo: {},
        loginError: {},
        loginSuccess: {},
        registerError: {},
        registerSuccess: {},
        isLoggedInStatus: '',
        isLoggedInError: {},
        accessToken: '',
        refreshToken: '',
        isLoading: false,
        recovermail: {},
        loginNavigation: false,
        registerNavigation: false,
        watchFreestate: false,
        registerPopup: false,
        vcardDetails: {},
        loading: false,
        RegBackNavigate: false,
        newpasswordCode: {},
        vcardSeriesDetails: {},
        anonymousUser:{}
    }
    it('should handle getLogin', () => {
		const action = { type: getLogin.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getLoginSuccess', () => {
		const payload = { login: 'Success' };
		const action = { type: getLoginSuccess.type, payload };
		const expectedState = { ...initialState, loginSuccess: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getRegister', () => {
		const action = { type: getRegister.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getRegisterSuccess', () => {
		const payload = { register: 'Success' };
		const action = { type: getRegisterSuccess.type, payload };
		const expectedState = { ...initialState, registerSuccess: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getUserInfo', () => {
		const action = { type: getUserInfo.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getUserInfoSuccess', () => {
		const payload = { userinfo: 'Success' };
		const action = { type: getUserInfoSuccess.type, payload };
		const expectedState = { ...initialState, userInfo: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPassword', () => {
		const action = { type: getPassword.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPasswordSccess', () => {
        const payload = { password: 'Success' };
		const action = { type: getPasswordSccess.type, payload };
		const expectedState = { ...initialState, newpasswordCode: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle loginSuccess', () => {
        const payload = { accessToken: 'test-access-token', refreshToken: 'test-refresh-token'};
		const action = { type: loginSuccess.type, payload };
		const expectedState = { ...initialState, accessToken: 'test-access-token', refreshToken: 'test-refresh-token'};
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  })

      it('should handle loginSuccess', () => {
        const payload = {  accessToken: 'test-access-token', refreshToken: 'test-refresh-token' };
		const action = { type: loginSuccess.type, payload };
		const expectedState = { ...initialState, accessToken: 'test-access-token', refreshToken: 'test-refresh-token' };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  })

      it('should handle recoverEmail', () => {
		const payload = { userinfo: 'Success' };
		const action = { type: recoverEmail.type, payload };
		const expectedState = { ...initialState, recovermail: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getIsLoggedinV1', () => {
		const action = { type: getIsLoggedinV1?.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getLoginNavigation', () => {
		const payload = { login: 'Navigation' };
		const action = { type: getLoginNavigation.type, payload };
		const expectedState = { ...initialState, loginNavigation: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getRegisterNavigation', () => {
		const payload = { register: 'Navigation' };
		const action = { type: getRegisterNavigation.type, payload };
		const expectedState = { ...initialState, registerNavigation: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getWatchFree', () => {
		const payload = { watch: 'free' };
		const action = { type: getWatchFree.type, payload };
		const expectedState = { ...initialState, watchFreestate: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

    it('should handle getRegisterPopup', () => {
		const payload = { register: 'popup' };
		const action = { type: getRegisterPopup.type, payload };
		const expectedState = { ...initialState, registerPopup: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getVcardData', () => {
		const payload = { Vcard: 'data' };
		const action = { type: getVcardData.type, payload };
		const expectedState = { ...initialState, vcardDetails: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getSeriesData', () => {
		const payload = { series: 'data' };
		const action = { type: getSeriesData.type, payload };
		const expectedState = { ...initialState, vcardSeriesDetails: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getClearAllLoginStates', () => {
		const payload = { clearalllogin: 'states' };
		const action = { type: getClearAllLoginStates.type, payload };
		const expectedState = { ...initialState, 
            loginInfo : {},
            userInfo : {},
            loginError : {},
            loginSuccess : {},
            registerError : {},
            registerSuccess : {},
            isLoggedInV1Error : {},
			isLoggedIn:{},
            accessToken : '',
            refreshToken : '',
            isLoading : false,
            recovermail : '',
			newpasswordCode: '',
        isLoggedInStatus: '',
        isLoggedInRefreshError: '',
        isLoggedInRefresh: '',
        settingClicked: false,
        termsAndConditionSuccess: {},
        termsAndConditionError: {}
         };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getBackNavigate', () => {
		const payload = { getback: 'navigate' };
		const action = { type: getBackNavigate.type, payload };
		const expectedState = { ...initialState, RegBackNavigate: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle clearIsloggedInStatus', () => {
		const action = { type: clearIsloggedInStatus.type};
		const expectedState = { ...initialState };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle clearLoginInfoState', () => {
		const action = { type: clearLoginInfoState.type};
		const expectedState = { ...initialState };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle clearLoginUserInfoState', () => {
		const action = { type: clearLoginUserInfoState.type};
		const expectedState = { ...initialState };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getGuestUserPlayerData', () => {
		const payload = { getback: 'navigate' };
		const action = { type: getGuestUserPlayerData.type, payload };
		const expectedState = { ...initialState, anonymousUser: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle setSkeltonLoading', () => {
		const payload = { getback: 'navigate' };
		const action = { type: setSkeltonLoading.type, payload };
		const expectedState = { ...initialState, loading: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      
      //////////////////////////////////////// ERROR CASES ////////////////////////////////////

      it('should handle loginError', () => {
		const payload = 'Error message';
		const action = { type: loginError.type, payload };
		const expectedState = { ...initialState, loginError: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getRegisterError', () => {
		const payload = 'Error message';
		const action = { type: getRegisterError.type, payload };
		const expectedState = { ...initialState, registerError: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle isLoggedInV1Error', () => {
		const payload = 'Error message';
		const action = { type: getIsLoggedinV1Error.type, payload };
		const expectedState = { ...initialState, isLoggedInV1Error: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      

});