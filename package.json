{"name": "te_upd_smarttv", "version": "2.0.210", "description": "Claro TV for AMX developed by Tata Elxsi", "main": "index.js", "scripts": {"start": "npm run dev", "dev": "webpack serve --env tv=samsung --env variant=dev --mode=development --config webpack.dev.js --open", "dev-prod": "webpack serve --env tv=samsung --env variant=prod --mode=development --config webpack.prod.js --open", "build": "webpack --config webpack.prod.js", "test": "jest --coverage", "build-tizen": "webpack  --env tv=samsung --env variant=dev--config webpack.prod.js && xcopy tv\\* build  /s /e /d /c && xcopy amx\\* build  /s /e /d /c &&  tizen.bat build-web -- build && tizen.bat package -t wgt -s AMX -- build\\.buildResult  && copy build\\.buildResult\\ClaroVideo.wgt .\\", "build-tizen-prod": "webpack  --env tv=samsung --env variant=prod --config webpack.prod.js && xcopy tv\\* build  /s /e /d /c && xcopy amx\\* build  /s /e /d /c && tizen.bat build-web -- build && tizen.bat package -t wgt -s AMX -- build\\.buildResult  && copy build\\.buildResult\\ClaroVideo.wgt .\\", "build-webos": "webpack --env tv=lg --env variant=dev --config webpack.prod.js && xcopy tv\\* build  /s /e /d /c && xcopy amx\\* build  /s /e /d /c && ares-package.cmd build -o .\\", "build-webos-prod": "webpack --env tv=lg --env variant=prod --config webpack.prod.js && xcopy tv\\* build  /s /e /d /c && xcopy amx\\* build  /s /e /d /c && ares-package.cmd build -o .\\", "build-tv": "webpack --config webpack.prod.js && xcopy tv\\* build  /s /e /d /c && tizen.bat build-web -- build && tizen.bat package -t wgt -s sreeni -- build\\.buildResult && copy build\\.buildResult\\ClaroVideo.wgt .\\ && ares-package.cmd build -o .\\ ", "deploy-tizen": "tizen.bat install -n ClaroVideo.wgt -- .\\", "webpack": "webpack"}, "author": "<PERSON><PERSON>", "license": "SEE LICENSE IN LICENSE.txt", "dependencies": {"@babel/core": "^7.18.6", "@babel/runtime": "^7.23.7", "@procot/webostv": "^1.2.5", "@react4tv/smart-tv-platform": "^1.0.0", "@reduxjs/toolkit": "^1.8.2", "axios": "^1.6.2", "bitmovin-player": "^8.192.0", "custom-env": "^2.0.1", "dompurify": "^3.1.5", "extract-css-chunks-webpack-plugin": "^4.10.0", "history": "^5.3.0", "html-react-parser": "^5.1.10", "immutable": "^4.1.0", "jest-fetch-mock": "^3.0.3", "jwt-decode": "3.1.2", "moment": "^2.29.4", "mux.js": "^6.1.0", "node-forge": "^1.3.1", "npaw-plugin-es5": "^7.2.43", "prop-type": "^0.0.1", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-lazy-load": "^4.0.1", "react-lazy-load-image-component": "^1.6.0", "react-lottie-player": "^1.5.5", "react-qr-code": "^2.0.7", "react-qr-image": "^1.1.0", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-slick": "^0.29.0", "react-timer-hook": "^3.0.7", "react-window": "^1.8.8", "react18-otp-input": "^1.0.3", "redux-mock-store": "^1.5.4", "redux-saga": "^1.2.3", "shaka-player": "^4.7.3", "slick-carousel": "^1.8.1", "svg-url-loader": "^7.1.1"}, "devDependencies": {"@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-optional-chaining": "^7.23.4", "@babel/plugin-transform-runtime": "^7.23.7", "@babel/preset-env": "^7.18.6", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.23.3", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "babel-jest": "^28.1.2", "babel-loader": "^8.2.5", "babel-plugin-styled-components": "^2.1.4", "css-loader": "^6.9.1", "dotenv": "^16.0.3", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^28.1.2", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^28.1.2", "node-sass": "^9.0.0", "react-test-renderer": "^18.2.0", "sass": "^1.70.0", "sass-loader": "^14.0.0", "style-loader": "^3.3.4", "webpack": "^5.73.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.2"}, "jest": {"testEnvironment": "jsdom", "moduleNameMapper": {"\\.(scss|sass|css)$": "identity-obj-proxy", "\\.(gif|ttf|eot|svg)$": "<rootDir>/test-utils/fileMock.js"}, "automock": false, "setupFiles": ["./src/setupTests.js", "jest-canvas-mock"]}}