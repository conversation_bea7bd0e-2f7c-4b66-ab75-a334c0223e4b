import React from 'react'
import './purchaseHours.scss'

export default function PurchaseHours() {
  return (
    <div className="purchaseHoursContainer">
      <div className="backContainer">
        <div className="backBtnhour focusable">
          <img
            className="yellowdothour"
            width={20}
            height={20}
            src="images/Home_icons/yellowdot.png"
          />
          <img
            className="backarrowhour"
            width={30}
            height={24}
            src="images/Home_icons/back.png"
          />
          <p className="backTexthour">REGRASAR</p>
        </div>
      </div>

      <div className="purchaseHoursMainBlock">
        <div className="purchaseHoursCenter">
          <p className="miniTitle">Paso 1 de 2</p>
          <p className="Title">Selecciona un plan para grabar más contenido</p>
          <div className="purchaseCardList">
            <div className="purchaseCardItem">
              <img
                className="card"
                src="images/Home_icons/MyContent/remoteshade.png"
              />
              <div className="purchaseContent">
                <div className="contentBlock">
                  <img
                    className="hoursImg"
                    src="images/Home_icons/MyContent/2hours.png"
                  />
                  <div className="rupeeBlock">
                    <div className="rupeeLine">
                      <div className="rupee">$XX/XX</div>
                      {/* <p className="next">/xx</p> */}
                    </div>
                    <p className="subtxt">IVA incluido</p>
                  </div>
                  <div className="description">
                    Disfruta 2 hrs más para grabar tus programas favoritos
                  </div>
                  <button className="hoursBtn">CONTRATAR 2 HORAS</button>
                </div>
              </div>
              <div className="purchaseCardItem"></div>
              <div className="purchaseCardItem"></div>
            </div>
            {/* second card */}
            <div className="purchaseCardItem">
              <img
                className="card"
                src="images/Home_icons/MyContent/tvwatchshade.png"
              />
              <div className="purchaseContent">
                <div className="contentBlock">
                  <img
                    className="hoursImg"
                    src="images/Home_icons/MyContent/5hours.png"
                  />
                  <div className="rupeeBlock">
                    <div className="rupeeLine">
                      <div className="rupee">$XX/XX</div>
                      {/* <p className="next">/xx</p> */}
                    </div>
                    <p className="subtxt">IVA incluido</p>
                  </div>
                  <div className="description">
                    Disfruta 2 hrs más para grabar tus programas favoritos
                  </div>
                  <button className="hoursBtn">CONTRATAR 5 HORAS</button>
                </div>
              </div>
            </div>

            {/* Third card */}
            <div className="purchaseCardItem">
              <img
                className="card"
                src="images/Home_icons/MyContent/handshade.png"
              />
              <div className="purchaseContent">
                <div className="contentBlock">
                  <img
                    className="hoursImg"
                    src="images/Home_icons/MyContent/10hours.png"
                  />
                  <div className="rupeeBlock">
                    <div className="rupeeLine">
                      <div className="rupee">$XX/XX</div>
                      {/* <p className="next">/xx</p> */}
                    </div>
                    <p className="subtxt">IVA incluido</p>
                  </div>
                  <div className="description">
                    Disfruta 2 hrs más para grabar tus programas favoritos
                  </div>
                  <button className="hoursBtn">CONTRATAR 10 HORAS</button>
                </div>
              </div>
            </div>
            <p></p>
          </div>
        </div>
      </div>
    </div>
  )
}
