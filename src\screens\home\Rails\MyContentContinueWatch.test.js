import React from "react";
import { fireEvent, getByTestId, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import MyContentContinueWatch from "./MyContentContinueWatch";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mocksuccessresponse = {
    "response": {
        "groups": [
            {
                "id": "23324",
                "number": "7",
                "name": "TV PERÚ",
                "hd": false,
                "image": "http://clarovideocdn2.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPER_t-290x163.png",
                "group_id": "784942",
                "liveref": "pe.tvperuhd",
                "epg_url": "TV",
                "source_uri": "",
                "provider_metadata_id": 2,
                "provider_metadata_name": "NAGRA",
                "default": false,
                "group": {
                    "common": {
                        "position": 0,
                        "id": "784942",
                        "title": "TV PERÚ",
                        "description": "TV PERÚ HD OTT/IPTV  PERÚ",
                        "large_description": "TV PERÚ HD",
                        "short_description": null,
                        "duration": null,
                        "image_large_alt": "TV PERÚ",
                        "image_medium_alt": "TV PERÚ",
                        "image_small_alt": "TV PERÚ",
                        "image_large": "http://clarovideocdn6.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPERWHORIZONTAL.jpg?size=529x297",
                        "image_medium": "http://clarovideocdn4.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPERWVERTICAL.jpg?size=200x300",
                        "image_small": "http://clarovideocdn6.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPERWHORIZONTAL.jpg?size=290x163",
                        "image_still": null,
                        "image_background": "http://clarovideocdn6.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/CLEAN/TVPER_e-1280x720.jpg",
                        "image_base_horizontal": "http://clarovideocdn3.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPERWHORIZONTAL.jpg",
                        "image_base_vertical": "http://clarovideocdn1.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPERWVERTICAL.jpg",
                        "image_base_square": "http://clarovideocdn1.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SS/TVPERWCUADRADO.jpg",
                        "image_clean_horizontal": "http://clarovideocdn6.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/CLEAN/TVPERWHORIZONTAL.jpg",
                        "image_clean_vertical": "http://clarovideocdn4.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/CLEAN/TVPERWVERTICAL.jpg",
                        "image_clean_square": "http://clarovideocdn4.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/CLEAN/TVPERWCUADRADO.jpg",
                        "image_sprites": "http://clarovideocdn7.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SPRITES/TVPER-SPRITEBAR.jpg",
                        "image_frames": "http://clarovideocdn3.clarovideo.net/CVPERU/PELICULAS/TVPER/EXPORTACION_WEB/SPRITES/TVPER-00h-00m-00s-00f.jpg",
                        "image_external": null,
                        "image_trickplay": "",
                        "date": "20190619130832",
                        "media_type": "2",
                        "title_uri": "TV-PERU",
                        "extendedcommon": {
                            "genres": {
                                "genre": [
                                    {
                                        "id": "53368",
                                        "name": "Nacionales",
                                        "desc": "Nacionales"
                                    },
                                    {
                                        "id": "53371",
                                        "name": "Todos",
                                        "desc": "Todos"
                                    },
                                    {
                                        "id": "51101",
                                        "name": "Variety",
                                        "desc": "Variedades"
                                    }
                                ]
                            },
                            "roles": {
                                "role": [
                                    {
                                        "id": "13617516",
                                        "name": "Actor",
                                        "desc": "Actor",
                                        "talents": {
                                            "talent": [
                                                {
                                                    "id": "32788257",
                                                    "name": "ND",
                                                    "surname": " ",
                                                    "fullname": ", ND"
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        "id": "13617517",
                                        "name": "Director",
                                        "desc": "Director",
                                        "talents": {
                                            "talent": [
                                                {
                                                    "id": "32788257",
                                                    "name": "ND",
                                                    "surname": " ",
                                                    "fullname": ", ND"
                                                }
                                            ]
                                        }
                                    }
                                ]
                            },
                            "format": {
                                "id": "91542711",
                                "name": "Type 7",
                                "types": "susc",
                                "sell_type": "susc",
                                "est": "N"
                            },
                            "media": {
                                "originaltitle": "TV PERÚ",
                                "description_extended": "TV PERÚ HD",
                                "publishyear": null,
                                "boxoffice": "0.0",
                                "rating": {
                                    "id": "10",
                                    "code": "Todos",
                                    "desc": "ATP (Apto para todo público)"
                                },
                                "duration": null,
                                "haspreview": "false",
                                "countryoforigin": {
                                    "code": "PE",
                                    "desc": "Peru"
                                },
                                "profile": {
                                    "audiotype": null,
                                    "videotype": null,
                                    "screenformat": null,
                                    "hd": {
                                        "enabled": "false"
                                    }
                                },
                                "islive": "1",
                                "livetype": "1",
                                "liveref": "pe.tvperuhd",
                                "timeshift": "4000",
                                "encoder_tecnology": {
                                    "id": "4",
                                    "desc": "ATEMEBP"
                                },
                                "recorder_technology": {
                                    "id": "5",
                                    "desc": "ATEMEBP"
                                },
                                "resource_name": null,
                                "rollingcreditstime": "-3",
                                "rollingcreditstimedb": null,
                                "proveedor": {
                                    "id": "12",
                                    "codigo": "cvperu",
                                    "nombre": "CLARO VIDEO PERU"
                                },
                                "rights": {
                                    "start_date": "2001-01-01",
                                    "end_date": "2100-01-01"
                                },
                                "channel_number": "7",
                                "language": {
                                    "original": {
                                        "id": "ESP",
                                        "desc": "Español"
                                    },
                                    "dubbed": "false",
                                    "subbed": "false",
                                    "options": {
                                        "option": [
                                            {
                                                "group_id": "784942",
                                                "content_id": "852635",
                                                "current_content": "false",
                                                "option_id": "O-ES",
                                                "audio": "ORIGINAL",
                                                "subtitle": null,
                                                "option_name": "original",
                                                "id": "ES",
                                                "desc": "Español",
                                                "label_short": "Id. Español",
                                                "label_large": "Idioma Original",
                                                "intro_start_time": null,
                                                "intro_finish_time": null,
                                                "credits_start_time": "-3",
                                                "audio_track": null,
                                                "subtitle_track": null,
                                                "resume_start_time": null,
                                                "resume_finish_time": null,
                                                "encodes": [
                                                    "hls",
                                                    "hls_kr",
                                                    "hlsprm",
                                                    "smooth_streaming",
                                                    "smooth_streaming_kr",
                                                    "dashwv",
                                                    "dashwv_kr",
                                                    "dvbc",
                                                    "ip_multicast_lms"
                                                ],
                                                "fast_play": {
                                                    "dvbc": "7",
                                                    "ip_multicast_lms": "https://livehlslms-1.media.claro.com.pe/bpk-tv/TVPERUHD/hls_fk/index.m3u8"
                                                }
                                            }
                                        ],
                                        "count": 1
                                    },
                                    "audio": {
                                        "options": [
                                            {
                                                "tag": "ORIGINAL",
                                                "track": "ENG"
                                            },
                                            {
                                                "tag": "ES",
                                                "track": "SPA"
                                            }
                                        ],
                                        "selected": "ES"
                                    },
                                    "subtitle": {
                                        "options": [
                                            {
                                                "tag": "ES",
                                                "track": "SPA"
                                            }
                                        ],
                                        "selected": null
                                    }
                                }
                            }
                        },
                        "ranking": {
                            "views_count": 0,
                            "votes_count": 0,
                            "average_votes": 4
                        }
                    }
                },
                "vistime": {
        "last": {
          "progress": 50  // Add this to make the item pass the filter
        }
      }
            },
            
        ],
        "total": 14
    }
}

describe('MyContentContinueWatch page test case', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        initialState.watchList = {
            continuewatchlist: mocksuccessresponse
        }
        const { container } = renderWithState(<MyContentContinueWatch />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})