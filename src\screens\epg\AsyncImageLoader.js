import React, { useState, useEffect } from 'react'

const AsyncImageLoader = props => {
  const [loadedSrc, setLoadedSrc] = useState(null)

  useEffect(() => {

    setLoadedSrc(null)
    
    const image = new Image()

    const handleLoad = () => {
      setLoadedSrc(props.src)
    }

    if (props.src) {
      image.addEventListener('load', handleLoad)
      image.src = props.src
    } else {
      image.addEventListener('load', handleLoad)
      image.src = props.src
    }
    return () => {
      image.removeEventListener('load', handleLoad)
    }
  }, [props.src])

  if (loadedSrc === props.src) {
    return (
      <img
        {...props}
        alt={props?.alt}
        loading={props?.loading}
        data-sn-down={props?.dataSnDown}
        data-sn-right={props?.dataSnRight}
        className={props?.className}
      />
    )
  }
  return null
}

export default AsyncImageLoader
