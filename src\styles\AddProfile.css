.AddProfile {
    top: 0px;
    left: 0px;
    width: 1920px;
    height: 1300px;
    position: relative;
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

.AddProfileCont{
    height: auto;
    /* height: 1040px; */
    /* overflow-y: scroll; */
}

.app-logo {
    width: 1920px;
    height: 1080px;
}


.backgroung-image {
    width: 1920px;
    height: 1080px;
}

.logo-size {
    margin-top: 157px;
    margin-left: 821px;
    width: 261px;
    height: 76px;

}

.Header {
    margin-top: -787px;
    margin-left: 416px;
    width: 1087px;
    height: 85px;
    text-align: center;
    letter-spacing: -0.84px;
    color: #EEEEEE;
    text-shadow: 0px 9px 23px #00000080;
    opacity: 1;
    font-size: 64px;
}

.Title {
    text-align: center;
    letter-spacing: -0.47px;
    font-family: 'Roboto Bold', sans-serif;
    color: #FFFFFF;
    opacity: 1;
    top: 413px;
    left: 534px;
    height: 48px;
    font-size: 36px;
    margin-top: 49px;
}

.logo-image {
    top: -1px;
    left: 0px;
    width: 1920px;
    height: 1px;
}

.App-logo {
    top: -1px;
    left: 0px;
    width: 1920px;
    height: 104px;
    background: #000000 0% 0% no-repeat padding-box;
}

.logo-img {
    margin-top: 51px;
    margin-left: 96px;
    width: 149px;
    height: 41px;
    opacity: 1;
}


.profile_radiobox {
    margin-top: 0.5rem;
}

.profile_radio {
    background: #000000 0% 0% no-repeat padding-box;
    border: 4px solid #34353B;
    border-radius: 12px;
    opacity: 1;
    width: 64px;
    height: 64px;
    box-shadow: inset 4px -6rem 1rem #000000;
}

.radio {
    top: 550px;
    width: 416px;
    height: 33px;
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.profilesave {
    margin: auto 0px auto 0px;
    width: 360px;
    height: 72px;
    background: #2E303D 0% 0% no-repeat padding-box;
    border-radius: 44px;
    text-align: center;
    font: normal normal normal 34px/40px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.profilesave:focus,
.profilesave:active {
    box-shadow: 0px 0px 24px #981C15;
    border: 3px solid #981C15;
    outline: unset;
    background: #981C15 0% 0% no-repeat padding-box;
}

.password-height {
    height: 146px
}

.ProfileNameContainer {
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}

.ProfileContainer {
    height: auto;
    width: 720px;
    display: flex;
    margin-left: auto;
    margin-right: auto;
    flex-direction: column;
}

.image {
    width: 41px;
    height: 29px;
    margin-top: 19px;
    margin-left: 32px;
}

.ProfButton {
    display: flex;
    margin-left: auto;
    margin-right: auto;
    height: 140px;
}

.ProfileImage {
    width: 128px;
    height: 128px;
}

.ProfButton:focus>.ProfileImage,
.ProfButton:active>.ProfileImage {
    z-index: 1;
    background: transparent 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 24px #981C15;
    border: 3px solid #981C15;
    outline: unset;
    border-radius: 160px;
    padding: 10px;
    opacity: 1;
}

.ChooseProfileText {
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
    text-align: center;
}

.addprofilecontainer {
    padding: 50px 50px;
}

.addWatchHeader {
    text-align: center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
    margin: 0;
}

.Profile_Name {
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    text-transform: capitalize;
    opacity: 1;
}

.ProfileNameCount {
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    text-transform: capitalize;
    opacity: 1;
}

.profile_heading {
    margin-bottom: 1rem;
    margin-top: 30px;
    left: 873px;
    height: 57px;
    font-weight: 'Regular';
    text-align: center;
    font-family: "Roboto";
    font-size: 48px;
    letter-spacing: 0px;
    color: #F1F2F3;
    opacity: 1;
}

.profile_title {
    margin-top: 10px;
    left: 787px;
    height: 35px;
    text-align: center;
    font-size: 30px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.profile-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4.5rem;
    background: transparent linear-gradient(90deg, #2B2C31F2 0%, #34353BF2 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 20px #00000029;
    opacity: 1;
    margin-top: 5rem;
    display: flex;
    justify-content: space-between;
}

.footer-text {
    width: 57px;
    height: 25px;
    text-align: left;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    font-family: "Roboto";
    font-size: 22px;
    margin-right: 57rem;
}

.profile_backbtn {
    margin-top: 1rem;
    width: 24px;
    height: 24px;
    margin-left: 57rem;
}




.Profile_Name_Input {
    width: 716px;
    height: 85px;
    background: #000000 0% 0% no-repeat padding-box;
    border-radius: 44px;
    border: 4px solid #34353B;
    opacity: 1;
    margin-top: 16px;
    padding-left: 25px;

    text-align: left;
    font: normal normal normal 30px/35px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.Profile_Name_Input:focus,
.Profile_Name_Input:active {
    z-index: 1;
    background: transparent 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 24px #981C15;
    border: 3px solid #981C15;
    outline: unset;
    border-radius: 44px;
}

.CheckboxContainer {
    display: flex;
    align-items: center;
    margin-top: 44px;
    outline: unset;
}

.CheckboxSubContainer {
    margin-top: -65px;
    display: block;
    position: relative;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Hide the browser's default checkbox */
.CheckboxSubContainer input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 64px;
    width: 64px;
    background-color: #000000;
    border: 4px #34353B solid;
    border-radius: 13px;
}

/* On mouse-over, add a grey background color */
.CheckboxContainer:focus input~.checkmark {
    z-index: 1;
    background: transparent 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 24px #981C15;
    border: 4px solid #981C15;
    outline: unset;
    border-radius: 13px;
}

/* When the checkbox is checked, add a blue background */
.CheckboxSubContainer input:checked~.checkmark {
    background-color: #000000;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.CheckboxSubContainer input:checked~.checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.CheckboxSubContainer .checkmark:after {
    left: 20px;
    top: 14px;
    width: 14px;
    height: 25px;
    font-weight: bold;
    border: solid white;
    border-width: 0px 7px 7px 0;
    -webkit-transform: rotate(35deg);
    -ms-transform: rotate(35deg);
    transform: rotate(35deg);
}

.CheckboxPopup {
    background: #EEEEEE 0% 0% no-repeat padding-box;
    border: 2px solid #EEEEEE;
    border-radius: 12px;
    opacity: 1;
    height: 433px;
    width: 426px;
    position: absolute;
    left: 130px;
    top: 375px;
}

.CheckboxPopup::before {
    content: '';
    position: absolute;
    width: 50px;
    height: 70px;
    z-index: -1;
    border-radius: 5px;
    right: -10px;
    transform: rotate(45deg);
    background-color: #EEEEEE;
    bottom: 35px;
}

.KidsDescTitle {
    font: normal normal bold 28px/33px Roboto;
    letter-spacing: -0.45px;
    color: #34353B;
    opacity: 1;
    text-align: left;
    margin-left: 36px;
}

.redDot {
    background: #DE1717 0% 0% no-repeat padding-box;
    opacity: 1;
    width: 30px;
    border: 0px;
    border-radius: 43px;
    height: 16px;
    margin: 8px 8px 0px 0px;
}

.kidsDescContainer {
    margin-left: 36px;
}

.KidsDesc {
    letter-spacing: 1.45px;
    margin-bottom: 15px;
    color: #34353B;
    opacity: 1;
    font: normal normal normal 28px/33px Roboto;
    display: flex;
    flex-direction: row;
}

.kids_profname {
    text-align: left;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
    font-size: 28px;
    font-family: 'Roboto';
    margin-left: 100px;

}

.PinTitle {
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.addprof_forgotpin {
    margin-right: 22rem;
    width: 234px;
    height: 69px;
    background: #282828 0% 0% no-repeat padding-box;
    text-align: center;
    font-size: 34px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
    /* margin-left: 74rem; */
    opacity: 1;
}

.addprof_hideicon {
    margin-top: 1rem;
    width: 33px;
    height: 29px;
    /* background: #EEEEEE 0% 0% no-repeat padding-box; */
    opacity: 1;
    /* margin-left: -50rem; */
}

.hideicon-_container {

    text-align: center;
    width: 104px;
    height: 69px;
    background: #282828 0% 0% no-repeat padding-box;
    border-radius: 5px;
    opacity: 1;
    margin: 10px 0px 0px 20px;
}

.pin {

    display: flex;
}

.PinContainer {
    display: flex;
    margin: 70px 350px 0px auto;
    flex-direction: column;
    width: 970px;
}

.sixdigitpin {
    font-size: 3.9rem;
    font-family: 'Roboto';
    color: white;
    width: 3rem;
    border: none;
    border-bottom: 4px solid grey;
    background-color: transparent;
    margin-right: 40px;
    outline: unset;
    text-align: center;
}

.sixdigitpin:focus,
.sixdigitpin:active {
    border-bottom: 4px solid #981C15;
}

.partitioned {
    border: none;
    width: 10.5ch;
    background:
        repeating-linear-gradient(90deg,
            dimgrey 0,
            dimgrey 1ch,
            transparent 0,
            transparent 1.5ch) 0 100%/100% 2px no-repeat;
    color: dimgrey;
    font: 5ch consolas, monospace;
    letter-spacing: .5ch;
}

.divInner {
    left: 0;
    position: sticky;
}

.divOuter {
    width: 190px;
    overflow: hidden;
    margin-left: auto;
    margin-right: auto;
    display: flex;
}

.kidsList {
    font-size: 32px;
    list-style: none;
}

ul.kidsList li::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin-top: 9px;
    border-radius: 50%;
    left: 36px;
    background-color: #DE1717;
}

ul.kidsList li {
    font-size: 32px;
}

.InputPopup {
	background: #EEEEEE 0% 0% no-repeat padding-box;
    border: 2px solid #EEEEEE;
    border-radius: 12px;
    opacity: 1;
    height: 140px;
    width: 515px;
    position: absolute;
    align-items: center;
    display: flex;
    left: 50px;
    margin-top: 24px;
}

.InputPopup::before {
	content: '';
    position: absolute;
    width: 45px;
    height: 45px;
    z-index: -1;
    border-radius: 3px;
    right: -15px;
    transform: rotate(45deg);
    background-color: #EEEEEE;
    bottom: 50px;
}

.CheckboxPinPopup {
    background: #EEEEEE 0% 0% no-repeat padding-box;
    border: 2px solid #EEEEEE;
    border-radius: 12px;
    opacity: 1;
    height: 545px;
    width: 426px;
    position: absolute;
    left: 115px;
    bottom: 80px;
}

.CheckboxPinPopup::before {
    content: '';
    position: absolute;
    width: 50px;
    height: 70px;
    z-index: -1;
    border-radius: 5px;
    right: -13px;
    transform: rotate(45deg);
    background-color: #EEEEEE;
    bottom: 5px;
}

.ProfileSaveButton {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    height: 250px;
}

.addprofback {
    position: fixed;
    bottom: 0px;
}