import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { getClearAllLoginStates } from '../../store/slices/login'
import { pushLoginEvent, pushScreenViewEvent} from '../../../src/GoogleAnalytics'
import '../loginregister/Loginpage.scss'

const LoginPassword = () => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const [type, setType] = useState('password')
  const [password, setPassword] = useState(state?.password)
  const dispatch = useDispatch()
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const navigateBack = useSelector(state => state?.login?.RegBackNavigate)
  const startTimeRef = useRef(null)

  const handleChange = value => {
    setPassword(value)
  }
  
  useEffect(() => {
    startTimeRef.current = Date.now()
    pushScreenViewEvent({screenName:'login_password_screen',prevScreenName: 'login_mail_srceen'})
    return () => {
      startTimeRef.current = null
    }
   }, [])

  const handlePasswordClick = e => {
    if (e.keyCode == 13 || e.keyCode == 65376 || e.keyCode == 40) {
      e.preventDefault()
      document.getElementById('signin').focus({ focusVisible: true })
      return
    } else if (e.keyCode == 38) {
      e.preventDefault()
      document.getElementById('password').focus({ focusVisible: true })
    }
  }

  const navigateLogin = e => {
    const engagement_time_msec = Date.now() - startTimeRef.current
    e.preventDefault()
    localStorage.setItem('pwd', password)
    dispatch(getClearAllLoginStates())
    // GA for login event
    pushLoginEvent(
      handleTranslationchange(
        'Onboarding_Inicio_sesion_ContrasenaRCU_Form_TextoBotonPrimario'
      )?.toLowerCase(),
      handleTranslationchange(
        'Onboarding_Inicio_sesion_ContrasenaRCU_Form_TextoTitulo'
      )?.toLowerCase(),
      engagement_time_msec
    )
    startTimeRef.current = Date.now()
    navigate('/signin', {
      state: { 
        focus: true, 
        value: state?.value, 
        password: password, 
        validEmail: state?.validEmail, 
        seriesEpisodeData: state?.seriesEpisodeData,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage
      }
    })
  }

  const handleTranslationchange = useCallback(keyname => {
    return apilanguage?.[keyname] ? apilanguage?.[keyname] : [keyname]
  }, [apilanguage])

  const keypresshandler = event => {
    if (event?.keyCode === 10009 || event?.keyCode === 461) {
      if (navigateBack) {
        navigate('/signin', {
          state: {
            focus: true,
            password: state?.password,
            password: password,
            value: state?.value,
            seriesEpisodeData: state?.seriesEpisodeData,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage
          }
        })
      } else
        navigate('/loginmail', {
          state: {
            password: password,
            password: state?.password,
            focus: true,
            value: state?.value,
            seriesEpisodeData: state?.seriesEpisodeData,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage
          }
        })
    }

    if (
      event?.keyCode == 13 ||
      event?.keyCode == 65376 ||
      event?.keyCode == 40
    ) {
      event.preventDefault()
      event.stopPropagation()
      if (event.target.id == 'hideId' && event?.keyCode == 13) {
        document.getElementById('hideId').focus({ focusVisible: true })
      } else {
        document.getElementById('signin').focus({ focusVisible: true })
      }
    } else if (event?.keyCode == 38) {
      event.preventDefault()
      event.stopPropagation()
      document.getElementById('password').focus({ focusVisible: true })
    } else if (
      event?.keyCode == 39 &&
      document.getElementById('password').selectionEnd ===
      document.getElementById('password').value?.length
    ) {
      document.getElementById('hideId').focus({ focusVisible: true })
    } else if (
      event?.keyCode == 37 &&
      !document.getElementById('hideId').focus({ focusVisible: true })
    ) {
      document.getElementById('password').focus({ focusVisible: true })
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className="App">
      <div className="App-logo">
        <img
          src={'images/claro-video-logo.png'}
          className="logo-img"
          alt="logo"
        />
      </div>
      <p className="password-Title">
        {handleTranslationchange('Onboarding_Inicio_sesion_ContrasenaRCU_Form_TextoTitulo')}
      </p>
      <div className="password-height">
        <input
          className="logininput-password"
          type={type}
          value={password}
          name="password"
          id="password"
          onChange={e => handleChange(e.target.value)}
          onKeyDown={e => handlePasswordClick(e)}
          autoFocus
        />
        {type === 'password' ? (
          <button
            className="hidesymbol focusable"
            onClick={() => setType('text')}
            data-sn-down={!password ? '#register' : '#signin'}
            id="hideId"
          >
            <img
              className="imagesignIcon"
              src={'images/Icono_Onboarding_Close.png'}
              alt="alert"
            />
          </button>
        ) : (
          <button
            className="hidesymbol focusable"
            onClick={() => setType('password')}
            data-sn-down={!password ? '#register' : '#signin'}
            id="hideId"
            >
            <img
              className="imagesignIcon"
              src={'images/Icono_Onboarding_Open.png'}
              alt="alert"
            />
          </button>
        )}
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          marginTop: '88px'
        }}>
        <button
          className={`${!password?'sign-In':'sign-In-enable'}`}
          id="signin"
          onClick={e => navigateLogin(e)}
          disabled={!password}>
          {handleTranslationchange('Onboarding_Inicio_sesion_ContrasenaRCU_Form_TextoBotonPrimario')}
        </button>
      </div>
    </div>
  )
}

export default LoginPassword
