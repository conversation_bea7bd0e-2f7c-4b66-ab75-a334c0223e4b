import React from 'react'
import { useSelector } from 'react-redux'
import './ShowRecordingHoursPopup.scss'

const ShowRecordingHoursPopup = props => {
  const region = localStorage.getItem('region')

  const paywayTokenResponse = useSelector(
    state => state?.epg?.paywayToken?.response
  )
  const recordingList = useSelector(
    state => state?.epg?.RecordingList?.response
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)

  const totalUsedHours = (recordingList?.time_used?.total / 3600)?.toFixed(1)
  const totalHours = paywayTokenResponse?.paqs?.paq?.reduce(
    (acc, item) => Number(item.npvrstorage) + acc,
    0
  )

  const ninetyPercentHours = (90 * totalHours) / 100
  let totalUsedHoursPerc = ((totalUsedHours / totalHours) * 100)?.toFixed(1) + '%'

  return (
    <>
      <div className="total-hours-layout">
        <div className="left-container-recording">
          <div className="total-hours-layout-sub-div">
            <span className="total-hours-layout-title">
              {
                translations?.language?.[region]
                  ?.myContent_recordingSpace_available_label
              }{' '}
              {totalUsedHours ? totalUsedHours : 0}h de{' '}
              {totalHours ? totalHours : 0}h
            </span>
            {totalUsedHours >= ninetyPercentHours && (
              <span className="warning-span">
                <img
                  src={'images/warning.png'}
                  className="warning-img"
                />
                <span class="total-hours-layout-title">
                  Libera o adquire mas espaciso
                </span>
              </span>
            )}
          </div>
          <div className="container-styles">
            <div
              className="filter-styles"
              style={{ width: totalUsedHoursPerc }}
            ></div>
          </div>
          <div className="d-flex-s-between">
            <label class="container1">
              <span className="checkbox-label">
                {
                  translations?.language?.[region]
                    ?.myContent_recordingSpace_used_label
                }
              </span>
              <input type="checkbox" checked="checked" />
              <span class="checkmark"></span>
            </label>
            <label class="container1">
              <span className="checkbox-label">
                {
                  translations?.language?.[region]
                    ?.myContent_recordingSpace_free_label
                }
              </span>
              <input type="checkbox" />
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
      </div>
    </>
  )
}

export default ShowRecordingHoursPopup
