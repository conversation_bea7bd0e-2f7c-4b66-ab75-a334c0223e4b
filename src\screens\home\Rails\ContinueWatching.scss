$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;
$text-ellipsis: ellipsis;

.railTitle {
  font-size: 32px;
  font-weight: normal;
  text-align: left;
  letter-spacing: 0px;
  color: $color-white;
  opacity: 1;
  margin-left: 70px;
  font-family: $font-family-roboto;
}

.rail_block-recordig:focus {
  .rail-image {
    z-index: 1;
    width: 420px;
    height: 230px;
    border: 4px solid #fff !important;
    padding: 4px;
    border-radius: 10px;
  }
}


.ContinueWatchingrailContainer {
  overflow-x: hidden;
  scroll-snap-type: x mandatory;
  height: 370px;
  margin-bottom: 40px;

  .ContinueWatchingsub {
    scroll-snap-type: x mandatory;
    margin-left: 30px;

    .continue-wrapper {
      position: relative;
      display: flex;
      overflow-x: scroll;
      scroll-snap-type: x mandatory;
      margin: 0.5rem;
      padding: 0.5rem;

      .rail_block {
        display: flex;
        margin-left: 20px;
        margin-right: 20px;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 412px;
        margin-bottom: 50px;
        opacity: 0.8;
        padding: 4px 4px 4px 4px;
      &:focus {
        scroll-snap-align: end;
        border: 4px solid #fff !important;
        border-radius: 10px;
        transform: scale(1.03);
        opacity: 1;
        // margin-right: 0px;
        // margin-left: 0px;
            .proveedorBlockRail_vero_hara {
                left: 13px;
                top: 13px;
              }
            
              .proveedorBlockRailAlq {
                left: 13px;
                top: 13px;
              }
            }

      .title-icon-block {
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 1;
        position: $position-absolute;
        width: 100%;
        top: 250px;

        .continuewatchingShow-title {
          .continuewatchingtitle {
            color: $color-white;
            font-size: 30px;
            font-weight: 500;
            font-family: Roboto;
            width: 200px;
            text-overflow: $text-ellipsis;
            white-space: nowrap;
            overflow: hidden;
            margin-left: 16px;
          }
        }
      }

      .progress-bar-continuewatch {
        width: 90%;
        position: $position-absolute;
        bottom: 30px;
        height: 0px;
      }
    }
  }

  .mycontent-railTitle {
    font-size: 32px;
    font-weight: normal;
    text-align: left;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin-left: 38px;
    font-family: $font-family-roboto;
  }

  .rail-image {
    display: flex;
    width: 412px;
    height: 232px;
  }
}
}

.recording-list-container {
  .img-grabando {
    width: 35px;
  }

  .rail-image {
    display: flex;
    width: 420px;
    height: 232px;
  }

  .mycontent-railTitle {
    font-size: 32px;
    font-weight: normal;
    text-align: left;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin-left: 38px;
    font-family: $font-family-roboto;
  }
}

.rail_block:focus {
  .deleteIcons {
    display: block;
  }
}
.rec-episode-list{
  position: relative;
  
  .rec-back-button {
    height: 48px;
    width: 292px;
    border-radius: 6.6px;
    background-color: #2e303d;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 49px;
    margin-left: 96px;
    margin-right: 53px;
    float: right;
    &:focus {
      background-color: #981c15;
    }
    .rec-yellow-dot {
      height: 20px;
      width: 20px;
    }
  
    .rec-back-arrow {
      height: 24px;
      width: 30px;
    }
  
    .rec-back-button-text {
      height: 30px;
      width: 146px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 29.04px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 29.04px;
    }
  }
  .rec-series-title {
    display: flex;
  }
  
  .rec-railTitle {
    font-size: 32px;
    font-weight: normal;
    text-align: left;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
    margin-left: 70px;
    font-family: 'Roboto';
  }
.episode-recording-list-container {
  overflow-x: hidden;
  width: 100%;

  .record-rail-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: 0.5rem;
    padding: 0.5rem;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;

    &.continue-wrapper {
      position: relative;
    }

    .rail-image {
      display: flex;
      width: 420px;
      height: 232px;
    }

    .episode-recording-block {
      height: 245px;
      width: 790px;
      position: relative;
      opacity: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      margin-top: 28px;
      margin-left: 35px;
    }
  }

  .img-grabando {
    width: 35px;
  }
}
}

.redDot {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  margin-right: 12px;
  margin-bottom: 8px;
}

.delete {
  width: 31.43px;
  height: 34.29px;
  margin-top: 10px;
}

.deleteIcons {
  display: none;
}

.defaultTitle {
  font-size: 28px;
  text-wrap: wrap;
  color: #fff;
  font-family: Roboto;
  font-weight: bold;
  padding-left: 0.5rem;
}

.progress-bars {
  background: #2c2c2c;
  position: absolute;
  bottom: 18px;
  /* left: 0; */
}

.rail_block:focus .defaultTitle {
  opacity: 1;
  border-radius: 10px;
  padding-left: 0.5rem;
}

.imageTitle {
  color: #fff;
  position: absolute;
  bottom: 40px;
  flex-wrap: wrap;
  line-height: 2.1rem;
}

.titleItem {
  font-size: 28px;
  text-wrap: wrap;
  position: absolute;
  bottom: 0px;
  left: 0px;
  color: #fff;
}

::-webkit-scrollbar {
  display: none;
}
.mycontentnodata{
  height: 340px;
  margin-bottom: 40px;
}
.nocontent-card-main {
  margin: 0.5rem;
  padding: 0.5rem;

  .nocontent-card {
    overflow-x: hidden;
    width: 1846px;
    background-color: black;
    display: flex;
    position: relative;
    height: 264px;
    left: 16px;

    .mycontent-rail-text {
      color: #767676;
      font-size: 32px;
      top: 62px;
      left: 20px;
      width: 607px;
      position: absolute;
      letter-spacing: 0;
      line-height: 34px;
      margin-left: 38px;
    }

    .myrecordcontent-railText {
      color: #767676;
      font-size: 32px;
      top: 62px;
      left: 20px;
      width: 568px;
      position: absolute;
    }

    .nocontent-sub-card {
      display: flex;

      .cardimage1 {
        width: 412px;
        height: 232px;
        position: absolute;
        top: 13px;
        right: 509px;
        display: flex;

        &:focus {
          border: 2px solid #fff;
        }
      }

      .cardimage2 {
        width: 412px;
        position: absolute;
        height: 232px;
        right: 65px;
        top: 13px;
        display: flex;
      }

      .cardimage3 {
        width: 31px;
        height: 232px;
        top: 12px;
        right: 0px;
        position: absolute;
        display: flex;
      }
    }
  }
}

.nocontent-card:focus {
  .cardimage1 {
    border: 4px solid #fff !important;
  }
}

.proveedorBlockRail {
  position: absolute;
  left: 13px;
  top: 14px;
  display: flex;
  height: 34px;
  width: auto;
}

.picardia-proveedor-block-rail-continue {
  position: absolute;
  top: 27px;
  display: flex;
  left:-11px;

  .picardia-tag {
    position: absolute;
    width: 100px;
    height: 30px;
  }
}

.picardia-image {
  width: auto;
  height: 24px;
}

.premium-icon{
  width: auto;
  height: 34px;
}

.proveedorBlockRail_vero_hara {
  height: 34px;
  width: auto;
  position: absolute;
  left: 13px;
  top: 16px;
  display: flex;
}

.verahora-tag {
  height: 32px;
  width: 122px;
  border-radius: 6px;
  background-color: #68b75c;
  color: #ffffff;
  font-family: Roboto;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
}

#icon1 {
  width: 111px;
  height: 38px;
}

.rail-image-series {
  display: flex;
  width: 412px;
  margin-left: 3px;
  /* Adjust the image width as needed */
  height: 232px;
  margin-right: 1.5rem;
}

.seriesCount {
  font-size: 3rem;
  color: white;
}

.series_block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.titleItem {
  color: white;
  opacity: 0;
  padding-left: 0.5rem;
}

.description {
  color: white;
}

.seriesMoviesTitlerail {
  position: absolute;
  bottom: 3px;
  left: 0rem;
}

.defaultTitlerail {
  font-size: 28px;
  text-wrap: wrap;
  color: #fff;
  padding-left: 0.5rem;
  font-family: 'Roboto';
  font-weight: bold;
  width: 25rem;
}

.description {
  font-size: 28px;
  text-wrap: wrap;
  color: #fff;
  padding-left: 0.5rem;
  margin-top: 0.2rem;
  font-family: Roboto;
}

.proveedorBlockRailAlq {
  position: absolute;
  left: 13px;
  top: 16px;
}

.tagAlq {
  width: 100px;
  height: 34px;
}

.tag {
  width: 161px;
  height: 38px;
}

progress {
  position: absolute;
  height: 16px;
  width: 390px;
  opacity: 1;
  border-radius: 8px;
  bottom: 43px;
  left: 12px;
}

progress::-webkit-progress-bar {
  background: #2c2c2c 0% 0% no-repeat padding-box;
  border-radius: 8px;
  opacity: 0.7;
}

progress::-webkit-progress-value {
  border-radius: 8px;
  background: #981c15 0% 0% no-repeat padding-box;
  opacity: 1;
}

.NotificationLayout {
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border: 2px solid #50595e;
  border-radius: 10px 0px 0px 10px;
  opacity: 1;
  width: 0;
  align-items: center;
  display: flex;
  position: absolute;
  white-space: nowrap;
  height: 140px;
  top: 60px;
  right: -4px;
  transition: 2s cubic-bezier(0, -0.31, 0.99, 1.24);
  overflow-x: hidden;
}

.NotificationLayout.show {
  width: 750px;
  opacity: 1 !important;
}

.NotificationTextLayout {
  margin-left: 42px;
}

.NotificationText {
  text-align: left;
  font: normal normal normal 28px/33px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  margin: 13px 0px;
  text-wrap: wrap;
  width: 500px !important;
}

.NotificationPath {
  text-align: left;
  font: normal normal normal 24px/28px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  margin: 13px 0px;
}