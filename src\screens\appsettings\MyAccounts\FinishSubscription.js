import React, { useEffect, useCallback, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  clearSubScribePlan,
  clearPromoCode,
  getVodSubscriptionInfo,
  getClearAllPaymentsData,
  getSubscriptionInfo,
  getClearSubscriptionInfo
} from '../../../store/slices/settingsSlice'
import './FinishSubscription.scss'
import { useDispatch } from 'react-redux'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { getChannelData, getMediaAPI } from '../../../store/slices/PlayerSlice'
import { getPremiumNodeValue } from '../../../store/slices/HomeSlice'
import Lottie from 'react-lottie-player'
import animationData from '../../../json/animationData.json'
import { getPayWayToken } from '../../../store/slices/EpgSlice'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'
import { CURRENT_PLATFORM } from '../../../utils/devicePlatform'

const FinishSubsription = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const region = localStorage.getItem('region')
  const { state } = useLocation()
  const viewData = useSelector(
    state => state?.settingsReducer?.viewSubscribeData
  )

  const [streamType, setStreamType] = useState('')
  const [grpId, setGrpId] = useState('')
  const [purchaseType, setPurchaseType] = useState('')
  const [episodeName, setEpisodeName] = useState('')
  const [episodeNumber, setEpisodeNumber] = useState(0)
  const [seasonNumber, setSeasonNumber] = useState(0)

  const pageNameCheck = state?.pageName ?? state?.previousPageName

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const subscribeInfoData = useSelector(
    state => state?.settingsReducer?.getVodSubsInfo?.response
  )
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const playerMappingData = useSelector(
    state => state?.settingsReducer?.afterSubscribePlayerData
  )
  const getMediaRes = useSelector(state => state?.player?.getMediaRes?.response)
  const vodId = useSelector(state => state?.settingsReducer?.vodID)
  const liveIndex = useSelector(state => state?.settingsReducer?.liveIndex)
  const getMediaError = useSelector(
    state => state?.player?.getMediaError?.errors?.[0]
  )
  const vodSeriesCastRedux = useSelector(
    state => state?.getVodSeries?.seriesCastData
  )
  const episodeData = useSelector(state => state?.getVodSeries?.episodeMoreInfo)

  const paymentProcessLoading = useSelector(
    state => state?.settingsReducer?.isLoading
  )
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles[0]?.parental
        ?.active
  )
  const statusControlPin = useSelector(
    state =>
      state?.settingsReducer?.statusControlPin?.response?.pin_parental?.info
        ?.value
  )
  const ageCode = parseInt(
    playerMappingData?.data?.extendedcommon?.media?.rating?.code
  )
  const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const translations =
    apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const apilanguage = translations?.language?.[region]
  const totalLabel =
    translations?.language?.[region]?.subscription_totalCost_description_label

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length > length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  const getScopeDescription = data => {
    return apilanguage?.[data + '_scope_description_label']
  }

  const supportedStream =
    apaMeta?.supported_stream && JSON.parse(apaMeta?.supported_stream)
  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keydown', keyPressFunc)
    return () => {
      document.removeEventListener('keydown', keyPressFunc)
    }
  }, [keyPressFunc])

  useEffect(() => {
    vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.number &&
      setEpisodeNumber(
        vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.number
      )
    vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.season &&
      setSeasonNumber(
        vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.season
      )
    vodSeriesCastRedux?.common?.title &&
      setEpisodeName(vodSeriesCastRedux?.common?.title)
  }, [vodSeriesCastRedux])

  //Removing this use effect as timeout is not for full screen.

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009 || keycode === 8) {
      let vodSubscrData =
        state?.pageName ==
        '/my-settings/my-subscriptions/add-subscriptions/viewDetails'
      if (state?.pageName === '/home') {
        localStorage.setItem('currNavIdx', 0)
        navigate(state?.pageName, {
          state: {
            data: state?.dataId,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            media: state?.media
          }
        })
      } else if (props?.pageName === 'vodPlayer') {
        props?.showBuySubs(false)
        return props?.showFin(true)
      } else if (vodSubscrData) {
        navigate(state?.pageName, {
          state: {
            dataId: state?.dataId,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            media: state?.media
          }
        })
      } else {
        navigate(state?.pageName, {
          state: {
            data: state?.dataId,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            media: state?.media
          }
        })
      }
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || ((CURRENT_PLATFORM == 'netrange') &&  keycode == 120)) {
      let vodSubscrData =
        state?.pageName ==
        '/my-settings/my-subscriptions/add-subscriptions/viewDetails'
      if (state?.pageName === '/home') {
        localStorage.setItem('currNavIdx', 0)
        navigate(state?.pageName, {
          state: {
            data: state?.dataId,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            media: state?.media
          }
        })
      } else if (props?.pageName === 'vodPlayer') {
        props?.showBuySubs(false)
        return props?.showFin(true)
      } else if (vodSubscrData) {
        navigate(state?.pageName, {
          state: {
            dataId: state?.dataId,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            media: state?.media
          }
        })
      } else {
        navigate(state?.pageName, {
          state: {
            data: state?.dataId,
            vodData: state?.vodData,
            pageName: state?.previousPage,
            media: state?.media
          }
        })
      }
    }
  }

  const defaultPaymentMethodReceipt = paymentState => {
    switch (paymentState?.paymentMethod) {
      case 'hubfacturafijagate':
        return `${truncateText(
          'ticket_subscription_hubfacturafijagate_label',
          30
        )} ${paymentState?.paymentAccount?.slice(-8)}`
      case 'hubgate':
        return `${truncateText(
          'ticket_subscription_hubgate_label',
          30
        )} ${paymentState?.paymentAccount?.slice(-8)}`
      case 'amcogate':
        return `${paymentState?.paymentGateway}`
      case 'claropagosgate':
        return `${truncateText(
          'ticket_subscription_claropagosgate_label',
          30
        )} ${paymentState?.paymentAccount?.slice(-8)}`
      default:
        return `${truncateText('ticket_subscription_promogate_label', 30)}`
    }
  }
  const handleReproducirClick = () => {
    dispatch(clearSubScribePlan())
    dispatch(getClearAllPaymentsData())
    const requiresPin =
      (securityPinCheck &&
        playerMappingData?.episodeData == null &&
        statusControlPin === 50 &&
        ageCode > 18) ||
      (statusControlPin === 40 && ageCode >= 16) ||
      (statusControlPin === 30 && ageCode >= 13) ||
      (statusControlPin === 20 && ageCode >= 7)

    if (requiresPin) {
      navigate('/my-settings/help-And-Settings/security-pin/configure', {
        state: {
          data: 'Create',
          pageName: state?.pageName || state?.previousPageName === '/movies'? 'movies':'series',
          moviedata:state?.vodData,
          resume:
            getMediaRes?.media?.initial_playback_in_seconds > 0 ? true : false
        }
      })
    } else {
      navigate('/vodPlayer', {
        state: {
          data: playerMappingData?.data,
          contentDataplayer: playerMappingData?.contentDataplayer,
          episodeData: playerMappingData?.episodeData,
          getMedia: getMediaRes,
          resume: false,
          inputValue: playerMappingData?.inputValue,
          returnPage: state?.pageName ?? state?.previousPageName
        }
      })
    }
  }

  const goToCertainPage = () => {
    dispatch(clearSubScribePlan())
    dispatch(clearPromoCode())
    state?.pageName == '/premiumSubscription' ||
    state?.previousPageName == '/premiumSubscription'
      ? navigate('/livePlayer', {
          state: { showControls: 'live', pinCancel: true },
          replace: true
        })
      : state?.pageName || state?.previousPageName === '/movies'
      ? navigate('/movies', {
          state: { vodData: state?.vodData }
        })
      : state?.pageName || state?.previousPageName === '/series'
      ? navigate('/series', {
          state: { data: state?.dataId }
        })
      : navigate('/home')
  }

  useEffect(() => {
    viewData?.family && dispatch(getPremiumNodeValue(viewData?.family))
  }, [viewData?.family])

  const handleHomeAcceptarClick = () => {
    navigate('/home')
  }

  useEffect(() => {
    if (
      viewData?.offertype == 'subscrition' ||
      viewData?.isPremiumChannel ||
      viewData?.isMultiPack
    ) {
      setPurchaseType('Suscripcion')
    } else if (
      viewData?.offertype &&
      (viewData?.offertype == 'buy' || viewData?.offertype == 'download_buy')
    ) {
      setPurchaseType('Compra')
    } else {
      setPurchaseType('Renta')
    }
  }, [viewData])

  useEffect(() => {
    if (
      grpId &&
      state?.pageName != '/premiumSubscription' &&
      state?.pageName != '/multiSubscription'
    ) {
      dispatch(
        getVodSubscriptionInfo({
          userId: userDetails?.parent_id,
          hks: userDetails?.session_stringvalue,
          is_kids: userDetails?.is_kids,
          url: `group_id=${grpId}`
        })
      )
    } else if (
      grpId &&
      (state?.pageName == '/premiumSubscription' ||
        state?.pageName == '/multiSubscription')
    ) {
      handleLivePlayback()
    }
  }, [grpId])

  const handleLivePlayback = () => {
    dispatch(getClearSubscriptionInfo())
    dispatch(
      getChannelData({
        group_id: state?.dataId?.id,
        timeshift: state?.dataId?.timeshift,
        switchChannel: 'yes',
        epgIndex: liveIndex
      })
    )
    navigate('/livePlayer', {
      state: { showControls: 'live' },
      replace: true
    })
  }

  useEffect(() => {
    setStreamType(
      !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
        ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
        : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
    )
    dispatch(
      getSubscriptionInfo({
        userId: userDetails?.parent_id,
        hks: userDetails?.session_stringvalue,
        url: `group_id=${state?.dataId?.id}`
      })
    )
    dispatch(
      getPayWayToken({
        hks: userDetails?.session_stringvalue,
        user_id: userDetails?.user_id
      })
    )
  }, [])

  useEffect(() => {
    if (getMediaRes?.media && Object.keys(getMediaRes)?.length > 0) {
      handleReproducirClick()
      setGrpId('')
    }
  }, [getMediaRes])

  useEffect(() => {
    const code = getMediaError?.code
    if (
      getMediaRes?.media &&
      streamType ==
         (supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0])
    ) {
      setStreamType(
         !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      setGrpId(vodId)
    }
    if (code == 'PLY_PLY_00009') {
      setStreamType(
        supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      setGrpId(vodId)
    }
  }, [getMediaError, getMediaRes])

  useEffect(() => {
    const payload = {
      id: grpId,
      payway_token: subscribeInfoData?.playButton?.payway_token,
      HKS: userDetails?.session_stringvalue,
      userId: userDetails?.user_id,
      user_token: loginInfo?.user_token,
      streamType
    }
    subscribeInfoData && dispatch(getMediaAPI(payload))
  }, [subscribeInfoData])

  useEffect(()=>{
  pushScreenViewEvent({screenName:'ticket_screen', screenData: userDetails, prevScreenName: state?.pageName})
},[])

  const displayFinishTitle = () => {
    if (purchaseType == 'Suscripcion') {
      return truncateText('Transaccionales_SuscripcionTicket_TextoTitulo', 17)
    } else if (purchaseType == 'Compra') {
      if (viewData?.isSeries) {
        if (viewData?.episodePurchase) {
          return truncateText(
            'Transaccionales_CompraEpisodioTicket_TextoTitulo',
            17
          )
        } else {
          return truncateText(
            'Transaccionales_CompraTemporadaTicket_TextoTitulo',
            17
          )
        }
      }
      return truncateText('Transaccionales_CompraTicket_TextoTitulo', 17)
    } else if (purchaseType == 'Renta') {
      return truncateText('Transaccionales_RentaTicket_TextoTitulo', 17)
    } else {
      return truncateText('Transaccionales_SuscripcionTicket_TextoTitulo', 17)
    }
  }

  const displayFinishCompra = () => {
    return (
      <div className="finish-image-and-text">
        <div className="finish-image-area">
          <LazyLoadImage
            className="finish-banner"
            src={
              viewData?.bannerUrl ||
              viewData?.verticalImage ||
              'images/checkout_banner_placeholder.png'
            }
          />
        </div>
        <div className="finish-text-area">
          <span className="asset-title">{viewData?.title}</span>
          {seasonNumber && viewData?.isSeries && region == 'mexico' && (
            <span className="asset-title">
              {episodeData?.episodeItem?.episode_number &&
              viewData?.episodePurchase
                ? `Temp. ${seasonNumber} | Ep. ${episodeNumber}`
                : `${truncateText(
                    `Transaccionales_CompraTemporadaTicket_TextoAbreParentesis`,
                    2
                  )} Temporada ${seasonNumber} ${truncateText(
                    `Transaccionales_CompraTemporadaTicket_TextoCierraParentesis`,
                    2
                  )}`}
            </span>
          )}
          <span className="finish-total-label-wrapper compra-total-wrapper">
            <span className="finish-total">
              {truncateText('Transaccionales_CompraTicket_TextoTotal', 7)}
            </span>
            <span className="finish-pricing-currency">
              {` ${viewData?.currency}${viewData?.price}`}
            </span>
          </span>
          <span className="finish-tax-label">
            {truncateText(
              `Transaccionales_CompraTicket_TextoImpuestos_${viewData?.producttype}`,
              15
            )}
          </span>
          <span className="subscription-paymentmethod-label">
            {truncateText('Transaccionales_CompraTicket_TextoMedioDePago', 15)}
          </span>
          <span className="finish-paid-with compra-paid-with">
            {defaultPaymentMethodReceipt(state)}
          </span>
        </div>
      </div>
    )
  }

  const displayFinishRenta = () => {
    return (
      <div className="finish-image-and-text">
        <div className="finish-image-area">
          <LazyLoadImage
            className="finish-banner"
            src={
              viewData?.bannerUrl ||
              viewData?.verticalImage ||
              'images/checkout_banner_placeholder.png'
            }
          />
        </div>
        <div className="finish-text-area">
          <span className="asset-title compra-title">{viewData?.title}</span>
          <span className="finish-total-label-wrapper ">
            <span className="finish-total">
              {truncateText('Transaccionales_CompraTicket_TextoTotal', 7)}
            </span>
            <span className="finish-pricing-currency">
              {` ${viewData?.currency}${viewData?.price}`}
            </span>
          </span>
          <span className="finish-tax-label">
            {truncateText(
              `Transaccionales_CompraTicket_TextoImpuestos_${viewData?.producttype}`,
              15
            )}
          </span>
          <span className="subscription-paymentmethod-label">
            {truncateText('Transaccionales_CompraTicket_TextoMedioDePago', 15)}
          </span>
          <span className="finish-paid-with">
            {defaultPaymentMethodReceipt(state)}
          </span>
          <span className="validity">
            <span className="finish-vigencia">
              {truncateText(
                `Transaccionales_SuscripcionTicket_TextoVigencia`,
                30
              )}
            </span>
            <span className="finish-page-duration">{`${
              viewData?.frequency
            } ${truncateText(viewData?.periodicity, 8)} ${truncateText(
              'Transaccionales_RentaTicket_TextoFecha1',
              12
            )}`}</span>
          </span>
          <span className="finish-page-duration renta-addon-text">{`${truncateText(
            'Transaccionales_RentaTicket_TextoFecha2',
            25
          )}`}</span>
        </div>
      </div>
    )
  }

  const displayFinishSuscripcion = () => {
    return (
      <div className="finish-image-and-text">
        <div className="finish-image-area">
          <LazyLoadImage
            className="finish-banner"
            src={
              viewData?.bannerUrl ||
              viewData?.verticalImage ||
              'images/checkout_banner_placeholder.png'
            }
          />
        </div>
        <div className="finish-text-area">
          <div className="finish-subscription-logos">
            <img
              className="finish-buy-sub-logo"
              src={viewData?.logo || 'images/CV_MENSUAL.png'}
            />
          </div>
          <span className="finish-free-charge-string">
            {truncateText(
              `Transaccionales_SuscripcionTicket_TextoOferta_${viewData?.producttype}`,
              18
            )}
          </span>
          <span className="finish-additional-info">
            {truncateText(
              `Transaccionales_SuscripcionTicket_TextoPromocion_${viewData?.producttype}`,
              38
            )}
          </span>
          <span className="finish-total-label-wrapper">
            <span className="finish-total">
              {truncateText('Transaccionales_SuscripcionTicket_TextoTotal', 7)}
            </span>
            <span className="finish-pricing-currency">
              {` ${viewData?.currency}${viewData?.price}`}
            </span>
            <span className="finish-periodicity">{`${truncateText(
              'Transaccionales_SuscripcionTicket_TextoDiagonal',
              2
            )} ${viewData?.periodicity}`}</span>
          </span>
          <span className="finish-tax-label">
            {truncateText(
              `Transaccionales_SuscripcionTicket_TextoImpuestos_${viewData?.producttype}`,
              15
            )}
          </span>
          <span className="subscription-paymentmethod-label">
            {truncateText(
              'Transaccionales_SuscripcionTicket_TextoMedioDePago',
              15
            )}
          </span>
          <span className="finish-paid-with">
            {defaultPaymentMethodReceipt(state)}
          </span>
          <span className="validity">
            <span className="finish-vigencia">
              {truncateText(
                `Transaccionales_SuscripcionTicket_TextoVigencia`,
                30
              )}
            </span>
            <span className="finish-page-duration">{`${state?.endDay} ${state?.endMonth}  ${state?.endYear}`}</span>
          </span>
        </div>
      </div>
    )
  }

  const displayReproducirPrimaryButton = () => {
    if (purchaseType == 'Suscripcion') {
      return truncateText(
        'Transaccionales_SuscripcionTicket_TextoBotonPrimarioReproducir',
        12
      )
    } else if (purchaseType == 'Compra') {
      if (viewData?.isSeries) {
        if (viewData?.episodePurchase) {
          return truncateText(
            'Transaccionales_CompraEpisodioTicket_TextoBotonPrimarioReproducir',
            12
          )
        } else {
          return truncateText(
            'Transaccionales_CompraTemporadaTicket_TextoBotonPrimarioReproducir',
            12
          )
        }
      } else {
        return truncateText(
          'Transaccionales_CompraTicket_TextoBotonPrimarioReproducir',
          12
        )
      }
    } else if (purchaseType == 'Renta') {
      return truncateText(
        'Transaccionales_RentaTicket_TextoBotonPrimarioReproducir',
        12
      )
    }
  }

  const displayAceptarPrimaryButton = () => {
    return truncateText(
      'Transaccionales_SuscripcionTicket_TextoBotonPrimarioAceptar',
      12
    )
  }

  const displayCerrarSecondaryButton = () => {
    if (purchaseType == 'Suscripcion') {
      return truncateText(
        'Transaccionales_SuscripcionTicket_TextoBotonSecundarioCerrar',
        7
      )
    } else if (purchaseType == 'Compra') {
      if (viewData?.isSeries) {
        if (viewData?.episodePurchase) {
          return truncateText(
            'Transaccionales_CompraEpisodioTicket_TextoBotonSecundarioCerrar',
            12
          )
        } else {
          return truncateText(
            'Transaccionales_CompraTemporadaTicket_TextoBotonSecundarioCerrar',
            12
          )
        }
      } else {
        return truncateText(
          'Transaccionales_CompraTicket_TextoBotonSecundarioCerrar',
          7
        )
      }
    } else if (purchaseType == 'Renta') {
      return truncateText(
        'Transaccionales_RentaTicket_TextoBotonSecundarioCerrar',
        12
      )
    }
  }

  return (
    <>
      <div
        className={`checkout-subsription-done ${
          paymentProcessLoading || grpId
            ? 'checkout-subscription-page-loader'
            : ''
        }`}
      >
        {(paymentProcessLoading || grpId) && (
          <div className="subscription-container-loader">
            <Lottie
              options={{
                rendererSettings: {
                  preserveAspectRatio: 'xMidYMid slice'
                }
              }}
              loop
              animationData={animationData}
              play
            />
          </div>
        )}
        <div className="finish-subscription-title">{displayFinishTitle()}</div>
        <div className="finish-details-wrapper">
          {purchaseType == 'Suscripcion' && displayFinishSuscripcion()}
          {purchaseType == 'Compra' && displayFinishCompra()}
          {purchaseType == 'Renta' && displayFinishRenta()}
          <img
            src={apaAssetsImages?.Icono_Aceptado ?? 'images/greenTick.png'}
            className="finish-green-tick-logo"
          />
        </div>
        <div className="finish-buttons-area">
          {pageNameCheck !== '/home' && (
            <button
              className="finish-subscription-page-buttons reproducir-button-margin focusable"
              id="reproducirButton"
              disabled={grpId}
              autoFocus
              onClick={() =>
                state?.pageName == '/premiumSubscription'
                  ? setGrpId(state?.dataId?.id)
                  : setGrpId(vodId)
              }
            >
              {displayReproducirPrimaryButton()}
            </button>
          )}
          {pageNameCheck == '/home' && (
            <button
              className="finish-subscription-page-buttons reproducir-button-margin focusable"
              id="reproducirButton"
              autoFocus
              onClick={handleHomeAcceptarClick}
            >
              {displayAceptarPrimaryButton()}
            </button>
          )}
          {pageNameCheck !== '/home' && (
            <button
              className="finish-subscription-page-buttons focusable"
              id="acceptarButton"
              onClick={goToCertainPage}
              disabled={grpId}
            >
              {displayCerrarSecondaryButton()}
            </button>
          )}
        </div>
      </div>
    </>
  )
}

export default FinishSubsription
