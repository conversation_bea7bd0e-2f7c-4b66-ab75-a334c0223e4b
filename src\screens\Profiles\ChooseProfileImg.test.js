import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router,  MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import ChooseProfileImg from "./ChooseProfileImg";
import '@testing-library/jest-dom';

 
const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
         <MemoryRouter
      history={history}
      initialEntries={[
        {
          state: {
            data: {
              gamification_id: '6579a68439b44705076d056b',
              username: '<PERSON><PERSON>lll',
              user_image:
                'lanegociadora01.png',
              rol: 'admin',
              admin: true,
              change_name: false,
              is_kids: 'false',
              partnerUserId: '92820606',
              user_hash:
                'OTI4MjA2MDZ8MTcxOTM5N=='
            }
          }
        }
      ]}
    >
      {children}
    </MemoryRouter>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
describe('Edit Watch Profile test', () => {
    test('should do button click', () => {
        const { container } = renderWithState(<ChooseProfileImg />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'profileApp');
        fireEvent.focus(scroll)
        fireEvent.keyUp(scroll,{keyCode: '405'})
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })

    
    test('handles Samsung TV green key', () => {
      const props = {
        onChange: jest.fn(),
        type: 'EditProfile',
        setCurrentButtonFocus: jest.fn()
      };
      global.tizen = {
        tvinputdevice: {
          registerKeyBatch: jest.fn(),
          getKey: jest.fn().mockReturnValue({ code: 10009 })
        }
      };
      const { container } = renderWithState(<ChooseProfileImg {...props} />);
      const getById = queryByAttribute.bind(null, 'id');
      const scroll = getById(container, 'profileApp');
      fireEvent.keyUp(scroll, { keyCode: 10009 });
      fireEvent(
        scroll,
          new MouseEvent('click', {
              bubbles: true,
              cancelable: true
          })
      )
      delete global.tizen;
    })
    test('handles image click and triggers navigate', () => {
      window.HTMLElement.prototype.scrollIntoView = function () { };
      const mockStoreWithAvatars = mockStore({
        profile: {
          profileAvatarData: {
            response: {
              avatars: [
                {
                  user_image: 'img.png',
                  collection: [
                    { user_image: 'avatar1.png' }
                  ]
                }
              ]
            }
          }
        },
        initialReducer: {
          appMetaData: {}
        }
      });
    
      const WrapperWithAvatars = ({ children }) => (
        <Provider store={mockStoreWithAvatars}>
          <MemoryRouter
            initialEntries={[
              {
                state: {
                  data: {
                    username: 'Tester',
                    user_image: 'profile.png'
                  },
                  pageName: '/'
                }
              }
            ]}
          >
            {children}
          </MemoryRouter>
        </Provider>
      );
    
      const { getByAltText } = render(<ChooseProfileImg />, { wrapper: WrapperWithAvatars });
      const avatarImg = getByAltText('Profile 1');
      fireEvent.click(avatarImg);
    });
    
})

