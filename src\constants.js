//Below data is a sample from please changed as per ur need
export const SPLASH_TIME_IN_MS = 3000
export const TRANSLATIONS_KEY = 'translations_strings'
export const CMSCONFIG_KEY = 'cmsconfig'

export const LOGIN_SCREEN = 'login'
export const ONBOARDING_SCREEN = 'onboarding'
export const DETAILS_SCREEN = 'details'
export const LOGIN_LIMIT = 'upgrade'
export const TERMS_AND_CONDITIONS = 'acceptandpurchase'
export const NETWORK_SCREEN = 'networkdisconnect'
export const RECORDING_SETTINGS = 'recordingsetting'
export const PARENTAL_SETTINGS = 'parentalsetting'
export const LOGIN_MESSAGE = 'Please type your username & password.'
export const FORGOT = 'FORGOT'
export const FORGOT_PASSWORD = 'FORGOT PASSWORD'
export const CONFIRM = 'Confirm'
export const LOGIN_CONFIRM = 'CONFIRM'
export const BACK = 'BACK'
export const RETRY = 'RETRY'
export const CANCEL = 'Cancel'
export const WATCH = 'Watch'
export const SEARCH = 'Search'
export const CONTINUE_AS_A_GUEST = 'CONTINUE AS A GUEST'
export const CONTINUE_ON_TV = 'CONTINUE ON TV'
export const TV_AUTH = 'tvAuth'
export const SETTINGS_MODULE = 'Setting Module'
export const RELATED_CONTENT = 'Related Content'
export const MY_CONTENT = 'My Content'
export const PLAYBACK_ERROR = 'playbackerror'
export const PLAYBACK_ERROR_TITLE = 'Playback Error'
export const PLAYER = 'player'
export const TV_CORE = 'TV Core'
export const EPG = 'Epg'
export const MY_TV = 'My TV'
export const MY_RECORDING = 'My Recording'
export const CHROMECAST = 'Chromecast'

export const DEFAULT_TRANSLATION_IN_FAILURE_CASE = {
  tvWelcome: {
    welcome: {
      loginMethodUsernamePassword: 'Login with username and password',
      defaultSubheadingWeb:
        'Watch Live TV or On Demand contents on your mobile, tablet and PC.',
      continueAsGuest: 'Continue as a Guest',
      defaultHeadingWeb: 'Welcome to Telekom TV GO',
      loginMethodOtp: 'Login with one time password',
      back: 'BACK',
      skip: 'Skip',
      loginMethodQrCode: 'Login with mobile',
      login: 'Login',
      loginPasswordError: 'Account does not exist',
      register: 'Register'
    },
    maintenancePage: {
      lblTitle: 'Maintenance',
      lblCancel: 'EXIT'
    },
    forceUpdate: {
      lblTitle: 'Update',
      lblCancel: 'CANCEL'
    }
  },
  footer: {
    footerText: {
      slogan: 'LIFE IS FOR SHARING.',
      copyrightText: '{0} {1} Magyar Telekom Nyrt.'
    },
    footerLinks: {
      termsOfUse: 'Terms of Use',
      contact: 'Contact',
      faq: 'FAQ',
      tnc: 'Terms & Conditions',
      about: 'About',
      dataPrivacy: 'Data Privacy',
      impressum: 'Impressum',
      cookies: 'Cookies'
    }
  },
  subscription: {
    qr: {
      channelPhoneDescription:
        'To subscribe {0} to be able to watch {1} {2} call {3}',
      buttonTitlePhone: 'Subscribe by Phone',
      emptySubscribeUrlDescription:
        'To be able to watch {0} subscribe to a package going to {1} ',
      scannerTitle: 'Subscribe %s',
      vodPhoneDescription:
        'To subscribe to this package for {0} to watch {1} {2} call {3}',
      buttonWeb: 'Subscribe by Web',
      descriptionWeb: 'To subscribe to this package open %s',
      descriptionPhone: 'To subscribe to this package call %s',
      multiSelectionDescription: 'Select one of the above to subscribe',
      emptyChannelPhoneDescription:
        'To be able to watch {0} subscribe to a package calling {1}',
      titleBack: 'Back',
      vodUrlDescription:
        'To subscribe to this package for {0} to watch {1} {2} go to {3}',
      multiSelectionTitle: 'Subscribe %s',
      channelSubscribeUrlDescription:
        'To subscribe {0} to be able to watch {1} {2} go to {3}',
      scanMessage: 'Use a QR Code reader to get to the page'
    },
    packageList: {
      title: 'Subscribe Package',
      description: 'Please select one'
    },
    channelList: {
      description: 'Please select one',
      title: 'Subscribe channel',
      itemTitle: 'Subscribe %s',
      itemSubtitle: 'Multiple Options'
    }
  },
  demo: {
    sample1: {
      singularMulti: 'var 1 {0} and var 2 {1}',
      onlySingular: 'Set to view {0}',
      onlyPlural:
        'set to use plural {word, plural, one {# word} other {# words}}',
      singularAndPlural:
        'first var for {singular} and second for {plural, plural, one {# one} other {# ones}}'
    },
    kt: {
      pendingBills2:
        'you have {no_of_bills, plural, one {# bill} other {# bills}} ',
      pendingBills: 'You {0} {date}',
      recordingScheduled: 'Recording scheduled for %s program',
      test: 'ygvgv',
      key: 'ParthProdTesting'
    },
    test2: {
      channelSubscribeUrlDescription:
        'To subscribe {PACKAGE_NAME, plural, other {PACKAGE_NAME} one {PACKAGE_NAME}} to be able to watch {0} {1} go to {2}'
    },
    delete: {
      test: 'Testing Plural for vText {test, plural, one {test} other {tests..}}',
      samplePlural:
        'This is a sample plural for {days, plural, one {#day} other {#days}}'
    },
    example: {
      testDelete: 'test_Delete_few',
      chiragTest2:
        '{chirag_test2, plural, other {chirag_test2} one {chirag_test2}}',
      test: 'testen',
      testingIos:
        'Testing IOS {variable, plural, one {# test} other {# tests}}',
      yes: 'No',
      noResults: 'TESTING_CHANGES_08DEC {0}',
      multiple: 'hello {1} hdahas {0}',
      chiragTest: '{test, plural, one {test1} other {test2}}',
      normalString: 'normal_string {0}',
      rentConfirmationMsgHours:
        'Do you wish to Rent {​​content_name}​​ {​​quality}​​ for  {duration}​​ {​​hour​​, plural, one {# hour} other {# hours}}​​ for {​​currency}​​ {​​price}​​'
    }
  },
  tvCore: {
    channelFilter: {
      filterChannelCategories: 'Channel Categories',
      filterChannelType: 'Channel Type',
      channelListFilterFavourites: 'Favourites',
      channelTypeFilterAll: 'All',
      noChannelAfterFilterApplied: 'No channel available for applied filters',
      channelListFilterSubscribed: 'Subscribed',
      channelTypeFilterTv: 'TV',
      filterChannelList: 'Channel List',
      mainTitle: 'Channel Filters',
      mainFilterClearAll: 'CLEAR ALL',
      channelListFilterAll: 'All',
      channelCategoryFilterClearAll: 'CLEAR ALL',
      channelTypeFilterRadio: 'Radio'
    },
    commonUi: {
      notificationMenuItem: 'Notification',
      exitApp: 'Exit',
      channelSubscriptionDialog: {
        subscribeViaWeb: 'Subscribe online',
        title: 'You can subscribe to this channel by phone or online',
        footer:
          'You can find more information about the subscriptions on the Telekom TV GO portal. If you need assistance, please call 1414.',
        packageListEmpty: 'No subscription package found',
        subscribeViaPhone: 'Subscribe by phone'
      },
      seriesRecordDialog: {
        titleSeries: 'Manage Series Recordings'
      }
    },
    errorNetwork: {
      backButtonTitle: 'Back',
      errorScreenMessage: 'Oops, something went wrong!',
      majorTitle: 'Attention',
      noNetworkInternetTitle: 'No internet connection',
      serviceTemporaryUnavailableTitle: 'Service temporary unavailable',
      retryText: 'RETRY',
      noNetworkConnectionDetail:
        'Please check if you are connected to a network',
      noNetworkInternetDetail:
        'Please check if you are connected to the internet',
      buttonLabelActive: 'Active',
      noNetworkConnectionTitle: 'No network connection',
      buttonNetworkSetting: 'Go to Network Settings',
      backButton: 'Back to login',
      serviceTemporaryUnavailableDetail: 'Please try again',
      majorDescription: 'Oops, something went wrong (md)',
      message2: 'Please try again when you made sure that you are connected.',
      showDetails: 'Show error details',
      message1: 'It looks like that you are not connected to the internet.',
      proxyError:
        'Please try again when you made sure that you are not connected through proxy.'
    },
    datePicker: {
      formatFallback: 'N/A'
    },
    network: {
      noInternetAvailable:
        'No network connection available. Please check if you are connected to a network'
    },
    castCrew: {
      showAll: 'Show all',
      widgetTitle: 'Cast and Crew'
    },
    securityBreach: {
      shelfLifeTitle: 'Shelf Life App',
      selfLifeDescription: 'You can not access the app.',
      hookedDevice: 'Hooked Device',
      hookDescription: 'You can not access the app.',
      debugTitle: 'Debug App',
      emulatorTitle: 'Emulator',
      temperedDevice: 'Tempered Device',
      temperedDescription: 'You can not access the app.',
      emulatorDecription: 'You can not access the app.',
      debugDesciption: 'You can not access the app.'
    },
    core: {
      tv: 'TV',
      dayToday: 'Today',
      titleWatchlist: 'Watchlist',
      notImplementedTitle: 'This function is not implemented yet!',
      genericErrorWeb: 'Error while loading content, please try again!',
      ios: 'iPhone',
      titleTransactions: 'My Rentals',
      years: 'Years',
      titleMyContent: 'MY CONTENTS',
      logout: 'LOGOUT',
      titleLowPowerStrip: 'Streaming affected by low power mode.',
      logo: 'TELEKOM TV GO',
      infoUseQr: 'Usa a QR Code reader to proceed.',
      reloadRailContent: 'Error while loading content, please try again!',
      titleRecording: 'My Recordings',
      atv: 'STB',
      edit: 'Edit',
      tryAgain: 'Try Again',
      titleMyAccount: 'My Account',
      titleEpg: 'EPG',
      titleSettings: 'Settings',
      lowPowerDialogDescription:
        'Your phone is running in battery saver mode that may lead to poor streaming experience. Turn off low power mode to ensure the best streaming quality.',
      emptyResponse: 'No content available',
      actionErrorMessage: 'Action could not be performed',
      cancelConfirmationText: 'Are you sure you want to cancel?',
      backHomeWeb: 'BACK HOME',
      dayYesterday: 'Yesterday',
      infoDisclaimerContentProviderRestriction:
        'This content cannot be recorded due to content provider restrictions.',
      pageNotFound: 'PAGE NOT FOUND.',
      relativeDayPlaceholder: 'day',
      android: 'Android Phone',
      assetTimeFormat: 'HH:mm',
      titleGeneral: 'GENERAL SETTINGS',
      dayTomorrow: 'Tomorrow',
      labelOk: 'OK',
      deleteConfirmationText: 'Are you sure you want to delete?',
      assetDateTimeFormat: 'dd.MM.YYYY | HH:mm',
      web: 'Web',
      titleSearch: 'Search',
      labelCancel: 'Cancel',
      deleteFailureWeb:
        'Action could not be performed correctly. Please retry in a few moments',
      and: 'and',
      durationInTimeFormat:
        '{time_in_minutes, plural, other {# min} one {# min}}',
      titleChannels: 'Channels',
      lowPowerDialogButtonLabel: 'Settings',
      titleMore: 'More',
      labelYes: 'Yes',
      scanBarcode: {
        description: 'Position the code on the screen between the brackets.',
        startButtonText: 'Scan',
        cancelButtonText: 'Cancel'
      },
      exitMessage: 'Are you sure you want to exit?',
      labelNo: 'No',
      confirm: 'Confirm',
      infoDisclaimerRecordStateNotAvailable:
        'This content is not available for recording.',
      comma: ',',
      lowPowerDialogTitle: 'Notification',
      titleHome: 'Start',
      under: 'Under',
      noProgramErrorMessage:
        'Program information could not be loaded for the selected item.'
    },
    downloadFeedback: {
      statusFailed: 'Download failed',
      statusRunning: 'Downloading',
      statusSuccessful: 'Download success',
      statusPaused: 'Download pause',
      statusPending: 'Download pending'
    },
    relatedContent: {
      errorMessage: 'Unable to load related content. Please try again',
      widgetTitle: 'Related Content'
    },
    rooted: {
      title: 'Rooted device',
      description: 'You cannot access app, since your device is rooted.'
    },
    appSelector: {
      title: 'Please select',
      dialerNotAvailable:
        'No application found on the device to complete the call. Please call {0} from another device.'
    },
    maintenance: {
      lblRefresh: 'Refresh',
      appUpdated: 'App restarted due to an update',
      lblMaintenance: 'Maintenance'
    },
    landscape: {
      rotateDevice: 'Please rotate your device.',
      notSupported: 'Landscape mode is not supported.'
    }
  },
  qamodule: {
    test: {
      sds: 'aa'
    },
    test2: {
      test: 'Value&#13;\n',
      pluralTest: 'Plural {var, plural, other {var} one {var}}'
    }
  },
  search: {
    filter: {
      all: 'All',
      tv: 'Series',
      showAll: 'Show All',
      channels: 'Channels',
      magazine: 'Magazine',
      ondemand: 'Movies',
      people: 'People'
    },
    searchResult: {
      totalContentDurationInTimeFormatSec:
        '{time_in_seconds, plural, one {# sec.} other {# secs.}}',
      totalContentDurationInTimeFormat:
        '{time_in_minutes, plural, one {# min.} other {# mins.}}'
    },
    noSearchResult: {
      description:
        'Find you content quickly&#13;\nTip: Use the virtual keyboard of your smartphone',
      noPreviousSearch: 'No previous searches ',
      title: 'No results found',
      errorLoadingContents: 'Error loading contents, please try again!',
      descriptionNoMagazine:
        'Find you content quickly&#13;\nTip: Use the virtual keyboard of your smartphone',
      titleWithQuery: 'No results for {0}'
    },
    recentSearch: {
      previousSearch: 'Search History',
      clearHistory: 'Clear History'
    },
    error: {
      errorWeb: 'Failed to search',
      reload: 'RELOAD',
      reloadSearchContentTitle: "Can't load complete search results",
      cancelReload: 'CANCEL',
      reloadSearchContentSubtitle: "Something went wrong. Let's try it again!"
    },
    searchView: {
      hintNoMagazine: 'Movies, TV shows, Series and Persons',
      program: 'Program',
      series: 'Series',
      pageTitle: 'Search',
      hint: 'Movies, TV Show, Person'
    }
  },
  myRental: {
    listing: {
      title: 'My Rentals',
      titleWeb: 'RENTALS'
    }
  },
  tvMenu: {
    myAccount: {
      logoutMessage: 'Are you sure want to log out ?',
      loggingOut: 'Logging Out',
      customerCenterHeading: 'CALL CENTER',
      frequentlyAskedQuestionsHeading: 'HAVE A QUESTION?',
      titleMyAccount: 'My Account',
      logoutButton: 'LOG OUT',
      accountManagementSubheading: 'Manage your Telekom fiók account',
      accountManagementHeading: 'ACCOUNT MANAGEMENT',
      customerCenterSubheading:
        'Before calling us, please browse our Frequently Asked Questions!',
      contactCustomerCareButton: 'Contact Call Center',
      openUserConsent: 'Manage User Consent',
      cancelButton: 'CANCEL',
      visitCustomerCenterButton: 'Visit telekom.hu',
      userConsentTitle: 'User Consent',
      openFaqButton: 'Frequently Asked Questions',
      clientManagementPortalButton: 'Manage Telekom fiók'
    },
    userConsent: {
      confirm: 'Confirm',
      pageSubtitle: 'Manage your consents and get more details about here.',
      personalisedConsentTitle: 'Personalised Consent',
      readFullConsent: 'Read the full consent',
      personalisedConsentDescription:
        'Personalised content utilises specific information about consumers to create a distinct, unique set of recommendations depending on content watched and browsed. Opting out from recommendation will show default recommendations.',
      pageTitle: 'User Consent'
    }
  },
  exceptionMessages: {
    myChannel: {
      1501: 'No Channel Selected'
    },
    genericDeck: {
      5001: 'Invalid Natco Key',
      5002: 'Invalid Language Code'
    },
    devices: {
      2101: 'No Device Found',
      2102: 'INVALID DEVICE ID'
    },
    ageSlots: {
      2201: 'INVALID DEVICE ID',
      2202: 'No Rating Found For Selection',
      2203: 'INVALID AGE'
    },
    hubsAndFeeds: {
      1301: 'Feed Id Not Found',
      1302: 'Empty Hubs',
      1303: 'Empty Feed Items'
    },
    recording: {
      2601: 'RECORDING NOT ALLOWED FOR ACCOUNT',
      2602: 'CREATE RECORDING FAILED',
      2603: 'CREATE UPDATE RECORDING FAILED',
      2604: 'NO RECORDINGS AVAILABLE',
      2605: 'INVALID CURRENT STATE',
      2606: 'Your recordings quota is full. Delete some recording to record new content.'
    },
    program: {
      1001: 'Program Not Found Test',
      1002: 'No Actions Found',
      1003: 'No Related Content'
    },
    generic: {
      0: 'Not Defined Error',
      11: 'No such resource',
      12: 'Upstream token invalid',
      13: 'Upstream down',
      14: 'API Contract Not Fulfilled',
      15: 'No Resource Found'
    },
    token: {
      2301: 'INVALID GRANT TYPE',
      2302: 'INVALID DETAILS'
    },
    search: {
      1801: 'INVALID SEARCH CATEGORY',
      1802: 'PERSON NOT FOUND FOR ID',
      1803: 'INVALID OFFSET'
    },
    component: {
      7001: 'Invalid Component Id'
    },
    pin: {
      1901: 'PIN NOT SET',
      1902: 'INVALID PASSCODE TYPE',
      1903: 'INVALID PIN',
      1904: 'NOT ABLE TO DECRYPT',
      1905: 'NEW PIN SAME AS CURRENT'
    },
    relatedContent: {
      2501: 'EMPTY PROGRAM ID'
    },
    series: {
      1101: 'Series Not Found',
      1102: 'No Actions Found',
      1103: 'No Season Found',
      1104: 'No Episodes Found'
    },
    myWatchlist: {
      1601: 'INVALID CONTENT ID',
      1602: 'INVALID CONTENT TYPE'
    },
    pinPreferences: {
      2001: 'PIN_PREFERENCES NOT SET',
      2002: 'INVALID PASSCODE TYPE',
      2003: 'INVALID PREFERENCES',
      2004: 'INVALID DEVICE ID'
    },
    rootCategory: {
      6001: 'Invalid Category Id'
    },
    epg: {
      1201: 'No Channel Details',
      1202: 'No Channels',
      1203: 'CHANNEL TIMESLOTS WITH INVALID DATE',
      1204: 'CHANNEL TIMESLOTS WITH INVALID HOUR OFFSET',
      1205: 'CHANNEL TIMESLOTS WITH INVALID HOUR RANGE'
    },
    transaction: {
      2701: 'OFFER ID ALREADY PURCHASED',
      2702: 'Credit Limit Exceeded'
    },
    player: {
      2401: 'CATCHUP NOT AVAILABLE',
      2402: 'INVALID STATION ID',
      2403: 'INVALID REQUEST FIELDS'
    }
  },
  tvAuth: {
    loginSelector: {
      cancel: 'Cancel',
      loginPref: 'How would you like to login?',
      phoneNumber: 'Enter a phone number',
      login: 'Login',
      userName: 'Username and password'
    },
    loginWithOneTimePassword: {
      emailPageSubtitle:
        'A one time password will be sent to your e-mail address',
      verifiyButtonTitle: 'Verify',
      resendOtp: 'Resend',
      phoneNumberEmpty: 'Please enter the phone number to continue with login.',
      pageTitle: 'Login with one time password',
      phoneNumberValidatorPatternFailed:
        'Please check the entered phone number',
      phoneNumberEnterOtpSubtitle:
        'Please enter the 4 digit one time password sent to {0}',
      emailEnterOtpTitle: 'Enter the one time password ',
      emailPageTitle: 'Enter your e-mail address',
      emailAddressEmpty: 'Please enter the email to continue with login.',
      emailValidatorPatternFailed: 'Please check the entered e-mail address',
      resendOtpRetrySecs:
        'You can regenerate OTP after {time_in_secs, plural, one {# sec} other {# secs}}',
      confirmButtonTitle: 'CONFIRM',
      optionPhoneNumber: 'Phone Number',
      emailTextfieldPlaceholder: 'E-mail address',
      emailEnterOtpSubtitle:
        'Please enter the 4 digit one time password sent to {0}',
      pageSubtitle: 'Please choose how you want to continue',
      phoneNumberPageTitle: 'Enter Your Phone number',
      otpEmpty: 'Please enter the OTP to continue with login.\t',
      phoneNumberPageSubtitle: 'A one time password will be sent to your phone',
      phoneNumberTextfieldPlaceholder: 'Phone number',
      resendOtpSuccessMessage: 'The one-time password has been resent.',
      optionEmailAddress: 'E-mail address',
      phoneNumberEnterOtpTitle: 'Enter the one time password '
    },
    loginWithQrCode: {
      qrCodeDescription:
        'Scan the QR code with your mobile camera app and continue the journey on your mobile.',
      verificationPageTitle: 'Connect to Device',
      verificationPageSubtitle:
        'Please type the 6 digit code as seen on your TV Screen',
      qrCodeTitle: 'Log in with QR code',
      accessCodeRefreshSuccessMessage:
        'The code expired so it was regenerated. Please make sure to enter the new one.',
      loginManuallyTitle: 'Log in manually',
      back: 'Back',
      accessCodeInputPlaceholderText: 'Enter the 6 digit code',
      loginStepLoginWithCredentials:
        'Log in with your credentials or sign-up for a new account',
      nextButtonTitle: 'Next',
      accessCodeVerificationSuccessMessage:
        'Navigálj könnyen és egyszerűen a felületen és válogass kényelmesen a különböző tartalmak között.',
      errorPageTitle: 'Something went wrong!',
      errorPageDescription:
        'You can’t log in with this method right now. Please go back and choose a different login method.',
      accessCodeExpiredErrorMessage:
        'Entered Code is not valid anymore. Please check the code on your TV screen',
      separatorText: 'OR',
      loginStepEnterCode: 'Enter the code: {0}',
      loginStepOpenWebsite: 'Open this website on your mobile: {0}',
      errorPageBackToOtherOptions: 'Back to other options\t'
    },
    login: {
      loginError: '.',
      loginWeb: 'Log In',
      continueAsGuest: 'Continue as Guest',
      retry: 'Retry',
      accountDisabledError:
        'Your access has been temporarily blocked for security reasons due to too many incorrect entries. Please reset your password.'
    },
    loginWithUserNamePassword: {
      passwordHint: 'Enter password',
      deviceLimitExceedErrorMessage:
        "You've reached your device limit. In order to start watching other devices need to be removed.&#13;\nThe system will remove the STB with the oldest login time. Press the confirm button to proceed.",
      back: 'BACK',
      usernameBelowHint:
        'Your username can be an e-mail address or a phone number',
      login: 'LOGIN',
      notRegisteredYet: 'Not registered yet?',
      password: 'Password',
      forgotPassword: 'Forgot password?',
      tvLoginDesc: 'Please log in to continue',
      passwordValidatorPatternFailed:
        'Password cannot be empty. Please enter your password.',
      userNamePasswordIncorrect: 'Username or password incorrect',
      loginPageTitle: 'Login',
      forgotPasswordMessage:
        'Forgot password? Please visit this link <u>%S</u> or use this QR code to reset your password.',
      loginPageSubtitle: 'Please type your username and password',
      registerNow: 'Register now',
      userName: 'Username',
      confirmButtonTitle: 'CONFIRM',
      usernamePasswordEmpty:
        'Please enter the username and password to continue with login.',
      stayLogin: 'Stay logged in',
      usernameHint: 'Enter username',
      usernameValidatorPatternFailed: 'Username is not valid!',
      loginAccountError: 'Account does not exist',
      forgotPasswordMessageUrlDisplay:
        'Forgot password? Please visit this link {0} or use this QR code to reset your password.',
      deviceLimit: 'Device Limit',
      serverSideError: 'An error occurred, please try again'
    },
    deviceLimitExceeded: {
      limitReachActionCancel: 'CANCEL',
      errorDescription: 'There is issue in login this time, please retry!',
      limitReachActionContinue: 'CONTINUE',
      errorTitle: 'Something went wrong!',
      errorActionRetry: 'RETRY',
      limitReachTitle: 'Manage list of devices',
      errorActionCancel: 'CANCEL',
      limitReachDescription:
        'You have reached your device limit. Your last used device would be automatically logged out.'
    },
    register: {
      qrGeneratingQr: 'Generating QR code..',
      qrBack: 'Back',
      qrDescription: 'Scan QR code to register',
      qrContinueOnTv: 'CONTINUE ON TV',
      qrErrorGeneratingQr: 'QR code generation failed, Please try again!',
      qrTitle: 'Register'
    }
  },
  epg: {
    filter: {
      labelSelectFilter: 'Channel Filters',
      lblFavouriteChannels: 'Favourite Channels',
      audioOnly: 'Radios',
      labelChannelCategory: 'Channel Category',
      labelFilterApplied: 'Filtered',
      labelFilter: 'Filter',
      labelAllChannels: 'All Channels',
      labelSubscribedChannels: 'Subscribed Channels'
    },
    channelGenre: {
      news: 'News',
      gyerekeknek: 'For kids',
      filmekSorozatok: 'Movies, series',
      regional: 'Regional',
      entertainment: 'Enertainment',
      kozossegi: 'Community',
      documentaryAndInfo: 'Documentary and Info',
      kozszolgalati: 'Public service',
      local: 'Local',
      religion: 'Religon',
      radio: 'Radio',
      lifestyle: 'Lifestyle',
      general: 'General',
      idegennyelvu: 'Foreign language',
      music: 'Music',
      culture: 'Culture',
      zene: 'Music',
      filmsAndSeries: 'Films and Series',
      international: 'International',
      sport: 'Sport',
      hirek: 'News',
      kids: 'Kids',
      shopping: 'Shopping'
    },
    daySelector: {
      labelSelectDay: 'Select Day',
      weekdayWeb: {
        sunday: 'Sunday',
        saturday: 'Saturday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        monday: 'Monday'
      },
      labelSelectorDayFormat: 'EEEE - dd.MM',
      labelSelectorDateFormat: 'dd.MM',
      labelTomorrow: 'Tomorrow',
      labelToday: 'Today',
      labelYesterday: 'Yesterday',
      labelSelectorWeekDayFormat: 'EEEE'
    },
    epgPage: {
      play: 'Play',
      buttonFilterLabel: 'Filter',
      labelDataNotAvailable: 'No information available',
      recordWarningLow:
        'You have less than {0} hrs of recording quota. Your unprotected recordings may be deleted, when your quota is overbooked.',
      channelErrorMessage: 'Failed to load channels',
      channelNotSubscribedDescription:
        'To be able to watch this channel, please open {0} in your browser or Smartbox device.',
      channelNotSubscribedOkButton: 'OK',
      noFavouritesChannelAvailable: 'No favorite channels yet',
      recordWarningMed:
        'You have less than {0} hrs of recording quota. Your unprotected recordings may be deleted, when your quota is overbooked.',
      channelNotSubscribedTitle: 'Requires subscription',
      startRecordingSeries: 'Start Series recording',
      recordWarningFull:
        'You have exceeded your recording quota. Your unprotected recordings may be deleted, when your quota is overbooked.',
      cancelRecording: 'Cancel Recording',
      channelSubscriptionDialogDefaultMessage:
        'To be able to watch {0} subscribe to package going to {1} or call PHONE {2}',
      optionInfo: 'Info',
      labelDescriptionNotAvailable: 'No description available',
      channelNotAvailable:
        'The channel you tried to watch is not available to you.',
      deleteRecording: 'Delete Recording',
      appName: 'Telekom TV GO',
      cancelRecordingSeries: 'Cancel Series recording',
      recordSeries: 'Series Recording',
      deleteRecordingSeries: 'Delete Series recording',
      scheduleErrorMessage: 'Failed to load TV Guide! Please try again',
      subscriptionErrorMsgWeb: 'Failed to load subscribed channels',
      labelNotAvailable: 'Not available',
      scheduleErrorMsgWeb: 'Failed to load schedules',
      lockedChannel: 'Locked content',
      labelNow: 'Now',
      startRecording: 'Start Recording',
      channelErrorMsgWeb: 'Failed to load channels',
      noSubscribedChannelAvailable: 'No subscribed channels yet'
    }
  },
  player: {
    error: {
      contentAvailableRegion:
        'Content playback stopped because this content is not available in your region. E:{0}',
      playbackAvailableCast: 'This content does not support casting.',
      maxSessionsReached:
        'The max number of simultaneous users watching was reached! Please try again once another user stops watching.',
      noNetworkConnection: 'No Network Connection. E:{0}',
      contentDownloadFailed: 'Content download failed. E:{0}',
      generalAudioMessage: 'Audio playback failed. Error Code:{0}',
      restrictedContent: 'Restricted Content',
      playbackNotAvailablePhone:
        'Content playback is not available for phones. E:{0}',
      deviceNotCompatiable:
        'Due to copyrights, the content is not avaliable on the device.',
      invalidAuthParameter:
        'Your authorization to view this video could not be confirmed. Please try login again',
      drmConcurrentStreamingExceeded:
        'License has expired because of too many concurrent usages',
      subscribeErrorMsgFormatted: 'To watch this content please subscribe {0}.',
      playbackAvailableMobileNetworks:
        'Content playback is not available on mobile networks. E:{0}',
      geoblockErrorMsg:
        'Due to copyright restrictions, this content can not be watched on your location.',
      playViaHdmi: 'Content playback failed through HDMI. E:{0}',
      deviceRestricted:
        'This channel cannot be watched on this device due to content provider restrictions.',
      secondScreenDetected:
        'A secondary display was detected which is not permitted in the current configuration. Remove the display and try again.',
      somethingWentWrong: 'Something went wrong. Please try later! E:{0}',
      licenseNotGranted:
        'There are no entitlements for your user and device that allow access to the release. Please try again',
      setupBoxError: 'Content playback is not allowed on Set-top-boxes. E:{0}',
      audioOnlyChannel: 'This channel has only Audio. E:{0}',
      deviceJailbroken:
        'Content playback failed because device is jailbroken. E:{0}',
      mediaNotYetAvailableProxy:
        'Content playback failed. Content not available via proxy. Error Code:{0}',
      playbackAvailableTablet:
        'Content playback is not available for tablets. E:{0}',
      accountNotVaild:
        'Content playback failed because account is invalid. E:{0}',
      playerErrorTimeout:
        'A timeout occurred due to no or bad internet connectivity.',
      adultContentNotAvailableTitle:
        'Adult contents are not available for this device',
      drmLicenseExpired:
        'An issue with the player licence occured. Please contact support if you see this error unexpectedly.',
      adultContentNotAvailableButtonTitle: 'Ok',
      noSpaceError:
        'Content playback failed due to no space available on device. E:{0}',
      ipAddressRestriction:
        'Content playback failed. The current IP address is not allowed. Error Code:{0}',
      deviceBlocked: 'Content playback failed because device is blocked. E:{0}',
      playbackNotAvailableWeb: 'Content is not allowed to play on web.. E:{0}',
      generalMessage: 'Content playback failed. E:{0}',
      lowBandwidthConnection:
        'Content playback failed due to low bandwidth connection. E:{0}',
      anonymousProxyRestriction:
        'Anonymous proxy detected. Please verify connection. Error Code:{0}',
      contentPlaybackOutsideHome:
        'Content is only available with Telekom TV registration. E:{0}',
      osVersionNotSupported:
        'Content playback failed because OS version is not supported. E:{0}',
      entitlementValidation:
        'Your entitlement to view this video could not be confirmed. Please try again.',
      playbackStoppedAgeRating:
        'Content playback stopped because of age rating. E:{0}',
      noInternetConnectionGeneralMessage:
        'No internet connection. Please try again later.',
      expiredRestriction: 'This content has expired',
      mediaNotYetAvailableDate:
        'Content playback failed. This content is not yet available. Error Code:{0}',
      drmRegionProhibited:
        'Due to copyright restrictions, this content can not be watched on your region.',
      screenReccordingDetected:
        'The current content is not allowed to be screen recorded.',
      initialisationFailed: 'Player initialisation failed',
      deviceRestrictedWithTochpointInfo:
        'This channel cannot be watched on this device due to content provider restrictions. You can watch the channel on {0}',
      contentBlackoutPlaybackError: 'Blackout contents can not be watched.',
      playbackAvailableWifi:
        'Content playback is not available on Wi-Fi. E:{0}',
      noLicense:
        'An issue with the player licence occurred. Please contact support if you see this error unexpectedly.',
      dowloadFailedFileTooLarge:
        'Content download failed because file is too large. E:{0}',
      fileNotAvailable: 'The video could not be found. Please try again.',
      playbleAvailableFormat:
        'Content is not available in a playable format for your current device. Please try again with another device.',
      noSignalWeakSignal: 'No Signal or Weak Signal! E:{0}',
      accountBlocked:
        'Content playback failed because account is blocked. E:{0}',
      missingAuth:
        'You need to be logged in to view this content. Please try again',
      licenseIssue:
        'An issue with the player licence occurred. Please contact support if you see this error unexpectedly.'
    },
    player: {
      play: 'Play',
      catchupConsentDialogTitle: 'Catchup',
      episodeNotPurchasedTitle: 'Requires purchase',
      none: 'None',
      nextEpisode: 'Next Episode',
      videoQuality: 'Video quality',
      languagePl: 'Polish',
      seekTimeFormat: 'HH:mm',
      optionInfo: 'Info',
      episodeNotPurchasedRentedDescription:
        'To be able to watch this episode, please open Telekom TV GO in your browser or Smartbox device.',
      disclaimerCatchupTimeshiftNotEnable:
        'TImeshift and catchup not available because catchup consent is not accepted',
      episodeNotRentedOkButton: 'OK',
      episodeNotRentedDescription:
        'To be able to watch this episode, please open Telekom TV GO in your browser or Smartbox device.',
      recordSeries: 'Record Series',
      recommendedContent: 'Recommended contents',
      bingeCancelButtonTitle: 'CANCEL',
      languageQaa: 'Original',
      forwardNotAllowed: 'Fast forward is not available due to copyrights.',
      episodeNotRentedTitle: 'Requires rental',
      languageQad: 'Original',
      languageMis: 'Uncoded Languages',
      subtitle: 'Subtitle',
      languageScr: 'Croatian',
      recordWarningLow:
        'You have less than {0} hrs of recording quota. Your unprotected recordings may be deleted if required.',
      '4kQuality': '4K',
      multicastLanguageClosedCaption: 'Closed caption',
      multicastLanguageNarrator: 'Narrator voice',
      enableCatchup: 'ENABLE CATCHUP',
      infoButtonSeeDetail: 'Details',
      timeshiftRestartNotAvailable:
        'Timeshift and restart are not available for this content due to content right restrictions.',
      languageNar: 'Narrated',
      cancelRecording: 'Cancel Recording',
      optionUnlock: 'Unlock',
      noDescriptionAvailable: 'No description available',
      optionSetting: 'Settings',
      tvGuide: 'TV GUIDE',
      lockedContent: 'Locked content',
      languageEn: 'English',
      audio: 'Audio',
      optionLanguage: 'Language',
      pinLockMsg: 'Please unlock content!',
      deleteRecording: 'Delete Recording',
      episodeNotPurchasedDescription:
        'To be able to watch this episode, please open Telekom TV GO in your browser or Smartbox device.',
      languageHun: 'Hungarian',
      relatedContentTimeFormat: 'HH:mm',
      catchupConsentDialogEnableBtn: 'Enable',
      pinLockAction: 'Unlock',
      headingInfo:
        '&#36;&#123;OUT_RUNTIME&#125; | &#36;&#123;genres&#125; |  &#36;&#123;AUDIO&#125; | &#36;&#123;OUT_RATING&#125;  | &#36;&#123;OUT_SCHEDULE&#125; |  &#36;&#123;out_release&#125;',
      cancelRecordingSeries: 'Cancel Series recording',
      multipleLanguage: 'Original language',
      noAudioAvailable: 'No audio available',
      languageDeu: 'German',
      episodeNotPurchasedRentedOkButton: 'OK',
      languageCro: 'Croatian',
      languageDe: 'German',
      optionRestart: 'Restart',
      language: 'Language',
      languageAng: 'English',
      languageEng: 'English',
      catchupConsentDialogCancelBtn: 'Cancel',
      episodeNotPurchasedRentedTitle: 'Requires purchase or rent',
      recordWarningFull:
        'You have exceeded your recording quota. Your unprotected recordings may be deleted if required.',
      geoblockErrorMsg:
        'Due to copyrights , this content can not be watched on your device.',
      multicastLanguageMulti: 'Multilanguage',
      contentDuration: '{time, plural, one {# min} other {# mins}}',
      channelSubscriptionDialogDefaultMessage:
        'To be able to watch {0} subscribe to package going to {1} or call PHONE {2}',
      languageUnd: 'Original',
      languageQnh: 'Narrator voice',
      subscribeErrorMsg: 'To watch this content please subscribe!',
      somethingWentWrong: 'Something went wrong',
      languageMul: 'Original',
      generalErrorMsg: 'Error loading content, please try again!',
      tryAgain: 'Try again',
      audioTrack: 'Audio Track',
      hdQuality: 'HD',
      languageHbs: 'Croatian',
      fhdQuality: 'Full HD',
      autoQuality: 'Auto',
      startRecording: 'Start Recording',
      restartNotAvailable:
        'Restart is not available for this content due to content rights restrictions.',
      nowOnTv: 'Now on TV',
      optionTeletext: 'Teletext',
      multicastLanguageOpen: 'Open subtitle',
      mul: 'Original language',
      noSubtitleAvailable: 'No subtitles available',
      letUsKnow: 'Let us know',
      catchupEnableMessage:
        'Activate this to watch the full catch up catalog and use time shift and restart.',
      back: 'Back',
      restartForwardNotAvailable:
        'Restart and fast forward are not available for this content due to content right restrictions.',
      recordWarningMed:
        'You have less than {0} hrs of recording quota. Your unprotected recordings may be deleted if required.',
      startRecordingSeries: 'Start Series recording',
      sdQuality: 'SD',
      audioTypeDolby: 'Dolby',
      optionGuide: 'TV Guide',
      optionReturnLive: 'Go to live',
      subtitleTypeSubtitle: 'Subtitle',
      languageHrv: 'Croatian',
      retry: 'Retry',
      subscribe: 'Subscribe',
      bingeWatchButtonTitle: 'WATCH',
      infoNoDescription: 'No content information',
      noContentFound: 'Channel not available! Please retry in a moment.',
      timeshiftNotAvailable:
        'Timeshift is not available for this content due to content right restrictions.',
      deleteRecordingSeries: 'Delete Series recording',
      episodeNotPurchasedOkButton: 'OK',
      subtitleTypeCaption: 'Caption',
      languageHr: 'Croatian',
      bingeWatchTitle: 'Watch Next S{0} E{1}',
      audioTypeStereo: 'Stereo',
      languageSubtitleTitle: 'Language Settings',
      languageHu: 'Hungarian',
      videoStartEndTimeSeparator: ':'
    }
  },
  settingsModule: {
    aboutSettings: {
      restartPromptTitle:
        'Application needs to be restarted to show/hide the debug overlay.',
      backButtonText: 'Back',
      showPlayerNameTitle: 'Show Player Name',
      clientId: 'Client Id',
      showPlayerStatsTitle: 'Show Player Statistics',
      privacy: 'Privacy Policies',
      versionInfo: 'Version Info',
      clientVersion: 'Client Version',
      operatingSystem: 'Operating System',
      debugPreferencesTitle: 'Debug Preferences',
      restartPromptConfirm: 'Restart',
      licenseDesc:
        'This application was developed using the 3rd party libraries below:',
      dataProtection: 'Data Protection',
      disclaimerTitle: 'Disclaimer',
      termsCondition: 'Terms and Conditions',
      moreInformation: 'More Information',
      toolbarTitle: 'About',
      drmInfo: 'DRM info',
      showMore: 'Show more',
      headingLicenses: 'Licenses',
      legal: 'Legal disclaimer',
      deviceModel: 'Device',
      umcVersion: 'UMC Version',
      showLess: 'Show less'
    },
    appSettings: {
      epgLandingTitle: 'Enable EPG as first page',
      epgLandingDescription: 'The app will start with EPG after the opening',
      pageTitle: 'First page'
    },
    playerSettings: {
      pipTitle: 'PICTURE-IN-PICTURE',
      tabletPipDisableDescription:
        'Feature is currently not available for tablet.',
      pipDescription: 'Allow mini player on top of other screens',
      pipPermissionDenied: 'PIP permission is not granted',
      pipNotSupportedByOs: 'PIP is not supported by your OS version',
      pageTitle: 'Player'
    },
    settingsHome: {
      settings: 'Settings',
      playerSettings: 'Player',
      devices: 'Devices',
      addText: 'Add',
      debugger: 'Debugger',
      about: 'About',
      recording: 'Recordings',
      language: 'Language',
      newPinText: 'New PIN',
      security: 'PIN Settings and Locks',
      logout: 'Logout',
      appSettings: 'First page',
      batterySettings: 'Low Power Mode',
      parental: 'Age Ratings',
      newPinAddMessage: 'Please enter your new PIN'
    },
    securitySettings: {
      pinDoesNotMatch: 'The entered PINs do not match',
      changePinError: 'You can not set the old PIN as a new PIN.',
      purchases: 'Purchase locks',
      typeYourNewPin: 'Please enter your new PIN',
      pleaseConfirmYourNewPin: 'Please confirm your new PIN',
      enableAdultContent: 'Enable adult contents',
      enteredPinIsIncorrect: 'The entered PIN is incorrect. Please try again.',
      ageRating: 'Rating system',
      typeYourOldPin: 'Please enter your old PIN',
      setPinDisclaimer: 'If you have forgotten your PIN, please call 1414.',
      typeYourPin: 'Please enter your 4-digit PIN',
      forgotYourPin: 'If you have forgotten your PIN, please call 1414.',
      setPin: 'SET PIN',
      adultContent: 'Adult locks',
      forgotPassword: 'Forgot your Password?',
      security: 'PIN Settings and Locks',
      pin: '4-digit PIN',
      forgotYourPinQrVisit: 'Please visit the website:',
      unratedContent: 'Unrated content locks',
      changePin: 'Change PIN',
      youHaveGotAPin: 'If you have forgotten your PIN, please call 1414.',
      enterPinDesc: 'If you have forgotten your PIN, please call 1414.',
      pinDescription: 'PIN is needed to use the Parental controls.'
    },
    parentalSettings: {
      ages: 'Ages',
      parental: 'Age Ratings',
      parentalFunDescription:
        'All ratings above your selected rating will also be locked. Enable/disable age ratings in PIN Settings and Locks menu.'
    },
    languageSettings: {
      languageChangeNowAllowed: 'Language change is not allowed',
      language: 'Language',
      nosubtitle: '(No subtitle)',
      selectLanguageText: 'Select Language',
      subtitle: 'Subtitle'
    },
    recording: {
      toggleTitle: 'Catchup ',
      recordSettings: 'RECORDING SETTINGS',
      catchupDescription: 'Activate to watch catchup content',
      title: 'Recordings',
      recordSettingsNotEnabledForUser: 'Recording requires subscription.',
      cloudStorage: 'STORAGE',
      quotaManagement: 'QUOTA MANAGEMENT',
      quotaRemaining:
        '{time, plural, one {# hour} other {# hours}} of recording remain',
      deleteOlderRecordings: 'Delete older recordings if quota is full',
      hourText: '%s hr',
      recordSettingsDesc:
        'If the direct recording is activated, you can initate recordings with one click.',
      manageRecording: 'MANAGE RECORDINGS',
      quotaUsed: '{time, plural, one {# hour} other {# hours}} already used',
      hoursShortText: 'hr',
      hoursRemaining: 'hours of recordings remain',
      hoursAlreadyUsed: 'hours already used',
      catchupTitle: 'Catchup',
      disclaimer:
        'Catchup is only a small part of the full feature set. Upgrade your subscription to enjoy the full personal recordings feature.'
    },
    batterySettings: {
      powerWarningTitle: 'Show warning on low power mode',
      powerWarningDescription:
        'Streaming quality can be affected in low power mode'
    },
    deviceSettings: {
      cancel: 'Cancel',
      deleteAccountDesc:
        'By removing this device you will be terminating your user session on the device.',
      listOfDevices: 'My Devices',
      sureQuestion: 'Are you sure?',
      thisDevice: 'This device',
      titleDevice: 'Devices',
      deviceGroupStb: 'STB',
      deviceGroupOthers: 'Others',
      device: {
        continue: 'Continue',
        myDevices: 'Devices',
        deviceDeletionInfo:
          'You have reached the limit of 5 active devices. Sign out a device to continue'
      },
      remove: 'Remove',
      loggedDeviceDesc: 'You are signed in on these devices:'
    }
  },
  myRecording: {
    recording: {
      lblCancel: 'CANCEL',
      lblEmptyRecordingDesc: 'There are no recordings added yet.',
      myRecording: 'My Recordings',
      upsaleMessage: 'You can enable recordings via account management!',
      lblEmptyRecodingTitle: 'No recordings',
      lblAll: 'All',
      scheduleShowmore: 'Show More',
      notEnabledForUser: 'Recordings is not supported',
      recordedRailTitle: 'Recordings',
      recordedShowmore: 'Show More',
      removePromptNo: 'No',
      lblSelectAtleaseOneItem: 'Please select at least one item from the list',
      lblRec: 'REC',
      scheduleRailTitle: 'Scheduled Recordings',
      removePromptYes: 'Yes',
      promptTitle: 'Confirmation',
      promptMessage: 'Are you sure you want to delete the selected recordings?',
      dateTimeFormate: 'dd.MM.YYYY | HH:mm',
      lblDelete: 'DELETE'
    }
  },
  cookie: {
    notification: {
      acceptMessage:
        'telekomtvgo.hu uses cookies for analysis. Please find more details here:',
      acceptTitle: 'Accept',
      viewTerms: 'View Terms'
    }
  },
  consent: {
    optin: {
      catchupEnabled: 'Enable',
      catchupSkip: 'Skip',
      catchupDescription:
        'Body 0, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed eiusmod tempor lorem ipsum adipiscing.',
      catchupTitle: 'Catchup Activation',
      catchupErrorApiFails: 'Oops! Something went wrong.'
    },
    consent: {
      personalisedConsentTitle: 'Personalised Consent',
      logoutDisclaimer:
        'Since, you have not accepted the essential Consent, Performing this action would log you out, Proceed?',
      save: 'Save',
      logoutDialogTitle: 'Logging out',
      consentBlockerDialogDisclaimer:
        "You haven't accepted some consent which are essential for performing this action. Would you like enable them from my account?",
      personalisedConsentDescription:
        'Personalised content utilises specific information about consumers to create a distinct, unique set of recommendations depending on content watched and browsed. Opting out from recommendation will show default recommendations.',
      personlisedConsent: 'Personalised Consent',
      clearDialogDisclaimer:
        'Performing this action would result in clearing of  your personal data which includes Watchlist, Bookmarks Recordings, Channels etc, are you sure you want to continue?',
      removeConsentDialogTitle: 'Remove Consent',
      clearHistoryDialogTitle: 'Clear History',
      logout: 'Logout',
      readFullConsent: 'Read Full Consent',
      clearHistory: 'Clear History ',
      userConsent: 'User Consent',
      consentBlockerDialogTitle: 'User Consent',
      manageYourAccount: 'Manage your account and check your billing history.'
    }
  },
  transactions: {
    myRentals: {
      titleRentalPurchase: 'My Rentals',
      titleShowMore: 'Show more'
    },
    myPurchases: {
      titleMyPurchase: 'My Purchases',
      titleShowMore: 'Show more'
    },
    transaction: {
      noTransactionsAvailable: 'No Rentals Found',
      titleRentalPurchase: 'My rentals and purchases',
      noRentalPurchaseFound: 'No rentals and purchase found.',
      noPurchasesFound: 'No purchased content found',
      title: 'My Rentals'
    }
  },
  myContent: {
    myChannels: {
      dialogMsgResetFavorites: 'Do you want to reset all favourite channels?',
      subscribedChannels: 'Subscribed Channels',
      favouriteChannels: 'Favourite Channels',
      description: 'Mark your favorites or set your channel order.',
      reorderDialogTitleSave: 'Apply Changes',
      reorderFilterAppliedMessage:
        'Channel reordering is not possible while filter is applied. ',
      noFavouritesChannelAvailable: 'No favourites channel available',
      title: 'MY CHANNELS',
      dialogActionNo: 'No',
      reorderCancel: 'CANCEL',
      allChannels: 'All Channels',
      dialogActionYes: 'Yes',
      options: 'Options',
      optionsFavoriteChannels: 'RESET FAVOURITE CHANNELS',
      dialogMsgResetOrder: 'Do you want to reset channel list?',
      dialogTitleResetList: 'Reset List',
      reorderDialogDescriptionSave: 'Would you like to apply the changes?',
      reorderFailureMessage: 'Something went wrong. Please try again!',
      reorderOptions: 'Options',
      filter: 'Filter',
      dvbReorderMessage: 'DVB channels can not be reordered',
      reorderConfirm: 'CONFIRM',
      optionsCancel: 'CANCEL',
      optionsResetOrder: 'RESET CHANNEL ORDER',
      noSubscribedChannelAvailable: 'No subscribed channel available'
    },
    channel: {
      subscriptionDialogErrorMessage:
        'To be able to watch {0} subscribe to package going to {1} or call PHONE {2}',
      subscriptionMultiPackageDialogFooter: 'Please select one',
      noSubscriptionPackageAvailable: 'No subscription package available',
      subscriptionDialogTitle: 'Subscribe {0}',
      subscriptionDialogFooter:
        'You can find more information about the subscriptions on the Telekom TV GO portal. If you need assistance, please call 1414.',
      channelSubscriptionDialogDefaultMessage:
        'To be able to watch {0} subscribe to package going to {1} or call PHONE {2}',
      subscriptionDialogMessageNew:
        'To subscribe {0} to be able to watch {1} {2} go to {3} and or call PHONE 1414',
      subscriptionDialogMessage:
        'To subscribe {0} to be able to watch {1} {2} go to {3} and or call PHONE {4}',
      subscriptionMultiPackageDialogTitle: 'Subscribe'
    }
  },
  resolutionNotSupported: {
    errorPage: {
      resolutionLimit:
        'The following modifications are needed to continue to the site:',
      resolutionNotSupported: 'Oops, your resolution is not supported!',
      resizeWindowText:
        'Please expand your browser window to full size, or zoom out with CTRL and - buttons.'
    }
  },
  home: {
    subpage: {
      filterTitle: 'Filter',
      noOfItemsShown: 'Showing {items, plural, other {items} one {items}}',
      defaultPageTitle: 'Subpage'
    },
    topMenu: {
      showMore: 'Show More',
      subtitleEpisode: 'Episode {0}',
      subtitleSeriesPrefixName: 'S{0}',
      subtitleEpisodePrefixName: 'E{0}',
      reloadRootCategoryContent: 'Oops, something went wrong!',
      showDetail: 'Show Detail'
    }
  },
  chromecast: {
    cast: {
      settings: 'Settings',
      stopCasting: 'Stop Casting',
      castTo: 'Cast to',
      noSubtitleAvailable: 'No subtitle available',
      sameNetworkMessage:
        'Make sure your phone and the cast device are on the same Wi-Fi network',
      languageSettings: 'Language Settings',
      back: 'Back',
      checkWiFiNetwork: 'Check your Wi-Fi network',
      noMediaSelected: 'No media selected',
      audioTracks: 'Audio tracks',
      noAudioAvailable: 'No Audio track available',
      phoneSettingsMessage:
        'Go to Settings on this phone and check that Local Network Access is turned on for this app ',
      subtitle: 'Subtitle',
      audio: 'Audio',
      localNetworkOn: 'Make sure Local Network Access is on'
    }
  },
  channels: {
    channel: {
      myWatchlist: 'MY WATCHLIST',
      myRecordings: 'MY RECORDINGS',
      nowOnTv: 'NOW ON TV',
      channelItemDayFormat: 'EEEE - dd.MM'
    }
  },
  appRating: {
    ratingScreen: {
      description:
        'What do you think, how many stars does this application deserves?',
      lowRatingCaption: 'There is still room for improvement, Keep working!',
      submitButtonLabel:
        'Rate {noOfStars, plural, one {# star} other {# stars}}',
      heading: 'Rate the application',
      pageTitle: 'Rating',
      highRatingCaption: 'I am quite satisfied with this application'
    },
    feedbackScreen: {
      feedbackPlaceholder: 'Describe your issue or share your ideas.',
      description:
        'You can suggest a feature, report an issue, or send other type of feedback. We will use your report to improve the application.',
      submitButtonLabel: 'Submit',
      heading: 'Tell us what you think',
      pageTitle: 'Feedback'
    }
  },
  detail: {
    detailPage: {
      dialogButtonSettings: 'Settings',
      transactionTypeRent: 'Rent',
      subscriptionNotPossibleOnDevice:
        'Subscription of this content is not possible on this device',
      enableAdultContentTitle: 'Adult content disabled',
      infoDisclaimerRecordContentExpireDaysText:
        'Content recording will expire in {days, plural, one {# day} other {# days}}.',
      transactionTypePurchase: 'Purchase',
      rentConfirmationMsgDay:
        'Selected content: {content_name}. Do you wish to rent in {quality} for {price} {currency}? The content can be watched unlimited times within {duration} {days, plural, one {# day} other {# days}}',
      episode: 'Episodes',
      infoDisclaimerSvodContentSubscribed:
        'This content is part of your subscription',
      disclaimerRecordingDisabled: 'Recording is disabled on this account.',
      rentDurationHourText:
        '{duration_in_hours, plural, one {# hr} other {# hrs}}',
      infoDisclaimerContentUnavailable: 'This content is not available now.',
      seriesErrorMessage: 'We were not able to load {0}. Please try again.',
      rentBtnSubTitle: '{0} | {1} | {2}',
      infoDisclaimerSubscriptionNotAvailable:
        'Subscription options not available.',
      extraSeason: 'Extra Season',
      season: 'Season',
      purchaseNotPossibleOnDevice:
        'Purchase of this content is not possible on this device',
      tvHeadingOne:
        '&#36;&#123;OUT_RUNTIME&#125; | &#36;&#123;GENRES&#125; |  &#36;&#123;SUBTITLE&#125; | &#36;&#123;OUT_RATING&#125;  | &#36;&#123;OUT_SCHEDULE&#125; |  &#36;&#123;out_release&#125;',
      totalContentDurationInTimeFormat:
        '{time_in_minutes, plural, one {# min} other {# mins}}',
      purchaseSubLabel: 'Buy in {0}',
      moreDates: 'More Dates',
      detailText: 'Details',
      infoDisclaimerRentExpireMinText:
        'Your rental will expire in {time_in_minutes, plural, one {# min} other {# mins}}.',
      moreDatesHeader: 'Available Schedules',
      infoDisclaimerRecordContentExpireTodayText:
        'Content recording will expire today at {0}.',
      watchDialogFooter: 'Please select',
      seasonWeb: 'Seasons',
      scheduleFormat: '{0} | {1}',
      quality: 'Quality',
      infoDisclaimerRentExpireHourText:
        'Your rental will expire in {time_in_hours, plural, one {# hr} other {# hrs}}.',
      adultContentNotAvailableTitle:
        'Adult contents are not available for this device',
      infoDisclaimerContentBlackout: 'Blackout contents can not be watched.',
      recordingRemovePromptNo: 'No',
      adultContentNotAvailableButtonTitle: 'OK',
      totalContentDurationInTimeFormatSec:
        '{time_in_seconds, plural, one {# sec} other {# secs}}',
      showMore: 'Show More',
      continueWatching: 'Continue watching',
      noInformation: 'No information available',
      infoDisclaimerContentUnavailableChannelUnsubscribed:
        'Subscription is needed to view this content.',
      rentMultilevelBtnLabel: '{0} | {1}',
      actionErrorMessage: 'We were not able to load actions. Please try again',
      infoDisclaimerRecordingFailed: 'This content has recordings issues.',
      episodeHideDate: 'Hide Dates',
      seriesprefixname: 'S{0}',
      rentConfirmationMsgWeb: 'Do you wish to rent {0} {1} for {2} for {3}?',
      recordingQuotaFull:
        'Your recording quota is full. Delete some recording to record new content.',
      scheduleNow: 'Now',
      acceptAndPurchase: 'ACCEPT AND PURCHASE',
      rentDurationDayText:
        '{duration_in_days, plural, one {# day} other {# days}}',
      confirmationTransactionTitle: 'Terms and Conditions',
      continueListening: 'Continue Listening',
      backTitle: 'Back',
      seriesInfo: 'Series Info',
      recordingRemovePromptYes: 'Yes',
      infoDisclaimerUnsubscibeSeparator: 'OR',
      dialogButtonOk: 'OK',
      castAndCrew: 'Cast And Crew',
      infoDisclaimerGeoBlocked: 'This content is available in the EU only.',
      rentDurationMinText:
        '{duration_in_minutes, plural, one {# min} other {# mins}}',
      rentSubLabel: 'Rent for {0} in {1}',
      dateRegex: 'EEEE - dd.MM',
      episodeprefixname: 'E{0}',
      genres: 'Genres',
      playerInfoButtonWeb: 'See Details',
      audio: 'Audio',
      infoDisclaimerRecordContentExpireMonthText:
        'Content recording will expire in {month, plural, one {# month} other {# months}}.',
      infoDisclaimerRecordContentExpireWeekText:
        'Content recording will expire in {week, plural, one {# week} other {# weeks}}.',
      rentConfirmationMsgHours:
        'Selected content: {content_name}. Do you wish to rent in {quality} for {price} {currency}? The content can be watched unlimited times within {duration} {hour, plural, one {# hour} other {# hours}}',
      infoDisclaimerSvodContentNotSubscribed:
        'This content is not part of your subscription',
      dialogButtonCancel: 'Cancel',
      disclaimerRecordingFeature: 'Recording is not available right now.',
      subtitles: 'Subtitles',
      infoDisclaimerIsCatchupNotAvailable:
        'This content is not available as catchup content.',
      infoDisclaimerUnsubscribePrefix:
        'To watch this content please subscribe.',
      castAndCrewShowsTitle: 'Shows',
      infoDisclaimerDeviceNotAllowed:
        'This content cannot be watched on this devices.',
      rentConfirmationMsg: 'Do you wish to rent {0} {1} for {2} for {3}?',
      rentalPurchaseNotPossibleOnDevice:
        'You can only rent or purchase this content on SmartBox device.',
      errorMessage: 'We were not able to load details. Please try again.',
      enableAdultContentMessage:
        'If you wish to enable adult contents, please enable adult contents in Settings',
      infoDisclaimerRecordingRestricted:
        'Recorded content cannot be watched on this device.',
      confirmationTransactionMessage:
        'Your purchase is not refundable and will be valid until you have an ongoing subscription to our service.',
      tvshowtitleepisodeprefix: 'Episode {0}',
      rentalNotPossibleOnDevice:
        'Rental of this content is not possible on this device.',
      relatedContent: 'Related Content',
      series: 'Series',
      recordingActionFailedTryAgain: 'Please try again later!',
      timeFormat: 'HH:mm',
      episodeShowDate: 'Show Dates',
      rentSubLabelDays:
        '{quality} | {duration} {days, plural, one {# day} other {# days}} | {currency} {price}',
      showLess: 'Show Less',
      purchaseBtnSubTitle: '{0} | {1}',
      infoDisclaimerRecordContentExpireYearText:
        'Content recording will expire in {year, plural, one {# year} other {# years}}.',
      infoDisclaimerRentExpireDayText:
        'Your rental will expire in {time_in_days, plural, other {time_in_days} one {time_in_days}}.'
    },
    action: {
      titleSubscribe: 'Subscribe',
      titleRecordChannel: 'Record on {0}',
      watchVod: 'VOD',
      rentalSubtitleMulti: 'Multiple Options',
      titleManageSeriesRecording: 'Manage Series Recording',
      titleRent: 'Rent',
      titleWatch: 'Watch',
      titleChannel: 'Channel {0}',
      titleSubscription: 'Subscription',
      channelSubscriptionBtnSubTitle: '{0} | {1}',
      titleUnpinned: 'Unpin',
      recordSubtitleMulti: 'Multiple Options',
      deleteRecordingPromptNo: 'No',
      failedRecording: 'Recording Failed',
      deleteRecordingPromptMessage:
        'Are you sure want to delete this recording?',
      titleSubscribeChannel: 'Subscribe Channel',
      recordWarningFull:
        'You have exceeded your recording quota. Your unprotected recordings may be deleted if required.',
      titleSchedule: 'Schedules',
      record: 'Record',
      channelSubscriptionDialogDefaultMessage:
        'To be able to watch {0} subscribe to package going to {1} or call PHONE {2}',
      titleScheduleWeb: 'Available schedules',
      deviceRestricted:
        'This content cannot be watched on this device due to content provider restrictions.',
      infoSuccessfullyPurchase: 'You successfully purchased {0}',
      titleRentOrPurchase: 'RENT OR PURCHASE',
      recordingDeleted: 'Recording deleted for %s program.',
      subscriptionDialogTitle: 'Subscribe {0}',
      guestLoginTitle: 'Login to watch',
      watchTv: 'TV',
      recordSeries: 'Record Series',
      continueWatchChannel: 'Continue watching on {0}',
      deleteRecordingPromptTitle: 'Confirmation',
      watchSubtitleMulti: 'Multiple Options',
      startRecording: 'Start Recording',
      manageRecording: 'Manage Recording',
      recordingScheduled: 'Recording scheduled for %s program',
      recordEpisode: 'Record Episode',
      deleteRecordingPromptYes: 'Yes',
      recordWarningLow:
        'You have less than {0} hrs of recording quota. Your unprotected recordings may be deleted if required.',
      continueListenChannel: 'Continue listening on {0}',
      titlePinned: 'Pin',
      channelSubscriptionDialogFooter:
        'You can find more information about the subscriptions on the Telekom TV GO portal. If you need assistance, please call 1414.',
      recording: 'REC',
      recordingDisclaimerNotSupported:
        'This content cannot be recorded due to content provider restrictions.',
      unprotectRecording: 'Unprotect Recording',
      enableCatchup: 'Enable Archive TV',
      watchRecording: 'RECORDING',
      recordWarningMed:
        'You have less than 1 hrs of recording quota. Your unprotected recordings may be deleted if required.',
      titleSubscribeSeries: 'Subscribe Series',
      startRecordingSeries: 'Start Series recording',
      titlePurchaseSeries: 'Purchase Series',
      titleWatchChannel: 'Watch on {0}',
      cancelRecording: 'Cancel Recording',
      subscriptionDialogMessageNew:
        'To subscribe {0} to be able to watch {1} {2} go to {3} and or call PHONE 1414',
      subscriptionDialogMessage:
        'To subscribe to this package for {0} to watch {1} {2} please call {3} or access {4}',
      guestLoginSubtitle: 'Login',
      schedulesNotAvailable: 'Schedules not available',
      titleTrailer: 'Trailer',
      deleteRecording: 'Delete Recording',
      titleUnlock: 'Unlock',
      titlePurchase: 'Purchase',
      deviceRestrictedWithTochpointInfo:
        'This content cannot be watched on this device due to content provider restrictions. You can watch the content on {0}',
      recordEpisodeCancel: 'Cancel Episode recording',
      subscriptionBtnSubTitle: '{0} | {1}',
      titleRentPurchaseBtnSubTitle: 'Starting from {0}',
      subscribeButtonSubtitleMultiple: 'Multiple options available',
      subscriptionDialogFooter:
        'You can find more information about the subscriptions on the Telekom TV GO portal. If you need assistance, please call 1414.',
      protectRecording: 'Protect Recording',
      cancelRecordingSeries: 'Cancel Series recording',
      recordingCanceled: 'Recording canceled for %s program',
      titleRentOptions: 'Rent Options',
      deleteRecordingSeries: 'Delete Series recording',
      channelSubscriptionDialogTitle: 'Subscribe {0}',
      recordSeriesCancel: 'Cancel Series recording',
      titlePurchaseOptions: 'Purchase Options',
      titleRentSeries: 'Rent Series',
      recordingTitle: 'My recordings',
      recordEpisodeDelete: 'Delete Episode recording',
      channelSubscriptionDialogMessage:
        'To subscribe {0} to be able to watch {1} {2} go to {3} or call {4}',
      titleListen: 'Listen',
      titleListenChannel: 'Listen on {0}'
    }
  },
  watchlist: {
    watchlistPage: {
      lblCancel: 'CANCEL',
      badgeVod: 'VOD',
      lblAll: 'All',
      lblEmptyWatchlistDesc:
        'You can pin programs to your favorites list for later viewing.',
      lblWatchlist: 'Favorites',
      seasonNumberPrefix: 'S{0}',
      onlyEpisodeNumberPrefix: 'Episode {0}',
      dateTimeFormat: 'YYYY.MM.dd | HH:mm',
      lblSelectAtleaseOneItem: 'Please select at least one item from the list',
      lblUnpinConfirmation: 'Are you sure you want to unpin',
      durationInTimeFormat:
        '{time_in_minutes, plural, one {# min} other {# mins}}',
      episodeNumberPrefix: 'E{0}',
      lblEmptyWatchlistTitle: 'No pinned contents yet',
      lblDelete: 'DELETE',
      badgeTv: 'TV'
    }
  },
  myTv: {
    myChannels: {
      filter: 'Filter',
      allChannels: 'All Channels',
      subscribedChannels: 'Subscribed Channels',
      favouriteChannels: 'Favourite Channels',
      channelsHeadingWeb:
        'Manage your favourite channels here in one place. Add or remove by clicking the heart icon.',
      title: 'MY CHANNELS',
      noFavouritesChannelAvailable: 'No favourites channel available',
      noSubscribedChannelAvailable: 'No subscribed channel available'
    },
    myWatchlist: {
      disclaimerTitle: 'No watchlist items',
      deleteWatchlist: 'Delete',
      removeFromFavorites: 'Remove from Favorites',
      selectAll: 'Select All',
      addToFavorites: 'Add to Favorites',
      deselectAll: 'Deselect All',
      back: 'Back',
      disclaimerMessage: 'No watchlist items avaliable',
      deleteSelected: 'Delete Selected',
      manage: 'Manage'
    },
    myAccount: {
      myChannels: 'MY CHANNELS',
      accountDescription:
        'Log into your Telekom fiók account and manage your subscription.',
      callDescription: 'You can call us',
      myRecordingTitle: 'Recordings',
      recordingDisclaimerNotSupported:
        'This content cannot be recorded due to content provider restrictions.',
      faqDescription: 'Visit us on our page for more information.',
      recordingSingleTitle: 'SINGLE RECORDINGS',
      recordingSeriesTitle: 'SERIES RECORDINGS',
      showAll: 'Show all',
      recordingEmptyDisclaimer: 'No recordings are available.',
      manageAccount: 'Manage Account',
      recordingDisabled: 'Recording is disable on this account.',
      faq: 'FAQ',
      userConsent: 'User Consent',
      myWatchlist: 'MY WATCHLIST',
      callSupport: '1414',
      myAccount: 'My Account',
      infoctaDescription: 'Use a QR Code reader to get to the page.',
      infoctaBack: 'Back'
    },
    myRentals: {
      title: 'My Rentals'
    },
    home: {
      titleTest: 'My Content',
      title: 'My TV'
    }
  },
  roleNames: {
    pageRoleName: {
      switcher: 'SWITCHER',
      v94: 'WRITER (TREATMENT)',
      'executive-producer': 'EXECUTIVE-PRODUCER',
      v96: 'Expert',
      v811: 'Lyricist',
      v491: 'Production manager',
      v490: 'Post-Production editor',
      'sound-supervisor': 'SOUND-SUPERVISOR',
      director: 'DIRECTOR',
      v497: 'Assistant Producer',
      costumer: 'COSTUMER',
      v496: 'Scenic Operative',
      v493: 'Programme Production Researcher',
      dancer: 'DANCER',
      'assistant-director': 'ASSISTANT-DIRECTOR',
      'costume-supervisor': 'COSTUME-SUPERVISOR',
      'technical-director': 'TECHNICAL-DIRECTOR',
      singer: 'SINGER',
      'lighting-supervisor': 'LIGHTING-SUPERVISOR',
      'sound-engineer': 'SOUND-ENGINEER',
      unknown: 'UNKNOWN',
      v716: 'Second Assistant Director',
      v717: 'Second Unit Director',
      v714: 'Fight Director',
      v715: 'Script Supervisor',
      'production-designer': 'PRODUCTION-DESIGNER',
      'cg-artist': 'CG-ARTIST',
      musician: 'MUSICIAN',
      v718: 'Sound Designer',
      v719: 'Music Arranger',
      performer: 'PERFORMER',
      'makeup-artist': 'MAKEUP-ARTIST',
      composer: 'COMPOSER',
      aggregator: 'AGGREGATOR',
      'continuity-person': 'CONTINUITY-PERSON',
      reporter: 'REPORTER',
      staff: 'STAFF',
      distributor: 'DISTRIBUTOR',
      'music-supervisor': 'MUSIC-SUPERVISOR',
      ad2: 'Advertising Production Company',
      'sfx-assistant': 'SFX-ASSISTANT',
      ad1: 'Advertising Agency',
      actor: 'Actor',
      ad4: 'Commissioning Channel',
      ad3: 'Advertiser',
      ad6: 'Presenter',
      ad5: 'Commissioning Brand',
      v43: 'Participant',
      ad8: 'Assistant Studio Manager',
      v42: 'Conductor',
      ad7: 'Studio Manager',
      v45: 'Photographer',
      anchor: 'ANCHOR',
      v44: 'Illustrator',
      ad9: 'Caption Author',
      'video-engineer': 'VIDEO-ENGINEER',
      v49: 'Sound Recordist',
      disseminator: 'DISSEMINATOR',
      v709: 'Key character',
      v2: 'WRITER (SCENARIO)',
      animator: 'ANIMATOR',
      'transportation-captain': 'TRANSPORTATION-CAPTAIN',
      broadcaster: 'BROADCASTER',
      'sound-effects-person\t': 'SOUND-EFFECTS-PERSON\t',
      v741: 'Key Grip',
      v103: 'Announcer',
      v742: 'Matte Artist',
      v738: 'Foley Editor',
      v739: 'Foley Mixer',
      v737: 'Foley Artist',
      'sfx-supervisor': 'SFX-SUPERVISOR',
      'art-director': 'ART-DIRECTOR',
      timekeeper: 'TIMEKEEPER',
      scriptwriter: 'SCRIPTWRITER',
      'set-designer': 'SET-DESIGNER',
      'visual-effects-supervisor': 'VISUAL-EFFECTS-SUPERVISOR',
      'camera-assistant': 'CAMERA-ASSISTANT',
      author: 'Author',
      ad13: 'Series Producer',
      ad12: 'Series Editor',
      'property-assistant': 'PROPERTY-ASSISTANT',
      ad11: 'Judge',
      ad10: 'Electrician',
      v727: 'Dialogue Coach',
      'production-assistant': 'PRODUCTION-ASSISTANT',
      producer: 'PRODUCER',
      publisher: 'PUBLISHER',
      v729: 'Hairdresser',
      'lighting-operator\t': 'LIGHTING-OPERATOR\t',
      webcaster: 'WEBCASTER',
      'set-maker': 'SET-MAKER',
      v489: 'Graphic Designer',
      v484: 'COSTUME DESIGNER',
      v485: 'Dresser',
      v483: 'Correspondent',
      v76: 'Adaptor',
      v77: 'Set Dresser',
      syndicator: 'SYNDICATOR',
      v79: 'Consultant',
      'makeup-supervisor': 'MAKEUP-SUPERVISOR',
      interviewer: 'INTERVIEWER',
      v751: 'Location Manager',
      'camera-operator': 'CAMERA-OPERATOR',
      v110: 'Casting',
      v80: 'Choreographer',
      v83: 'Director of photography',
      narrator: 'NARRATOR',
      v745: 'Sound Mixer',
      'property-master': 'PROPERTY-MASTER',
      v105: 'Special Effects'
    }
  }
}

export const NAV_BAR_TAB_VALUES = {
  GUIA: 'guia'
}

export const DEFAULT_THINGS = {
  defaultColor : '#8A4CA7'
}

export const NEW_RELIC_CONFIG = {
  licenseKey: '54c334f0c8',
  applicationID: '**********',
  accountID: '675452',
  trustKey: '675452',
  agentID: '**********'
}
