.lang-selection-container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    width: 1920px;
    height: 1080px;
    position: fixed;
    background: #121212;

    .lang-title-container {
        margin: 5px;
        box-sizing: border-box;
        height: 64px;
        width: 557px;
        border: 2px solid #fff;
        border-radius: 31px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        .lang-title {
            height: 35px;
            color: #fff;
            font-family: 'Roboto';
            font-size: 30px;
            letter-spacing: 0;
            line-height: 35px;
        }

        .check-icon {
            position: absolute;
            right: 20px;
            height: 24px;
            width: 24px;
        }
    }
   
    .lang-title-container-active {
        background-color: #ffffff;
        color: black;
        .lang-title {
            color: #121212;
        }
    }

    .lang-title-container:focus, 
    .lang-title-container-active:focus {
        border: 5px solid #fff;
    }
}