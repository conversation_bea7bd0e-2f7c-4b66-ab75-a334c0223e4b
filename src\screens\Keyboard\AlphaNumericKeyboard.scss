.KeyboardContainer {
  height: 647px;
  width: 880px;

  .clearButtonsDiv {
    margin-bottom: 84px;
    margin-left: 299px;
    display: flex;

    .clearButtons {
      height: 48px;
      width: 206px;
      margin-left: 20px;
      border-radius: 6.6px;
      background-color: #2e303d;
      border: 3px solid #121212;

      .shortcutImgStyles {
        margin-left: 24px;
        width: 20px;
      }

      .clearButtonsText {
        color: #ffffff;
        font-family: Roboto;
        font-size: 29.04px;
        letter-spacing: 0;
        line-height: 29.04px;
        margin-left: 24px;
        font-weight: bold;
      }
    }

    .clearButtons:focus {
      border-radius: 6px;
      border: 2px solid white;
    }
  }

  //Alpha Numeric Keyboard
  .keyBoardSelection {
    height: 48px;
    width: 136px;
    border-radius: 6.6px;
    background-color: #2e303d;
    border: 3px solid #121212;
    color: #ffffff;
    font-family: <PERSON><PERSON>;
    font-size: 35.2px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 36.3px;
    text-align: center;
    margin-right: 16px;
  }

  .keyBoardSelection:focus {
    border-radius: 6px;
    border: 3px solid white;
  }

  .alphaNumericKeyboardBtn {
    margin-bottom: 48px;
    display: inline-flex;
    justify-content: center;
    height: 48px;
    width: 49px;
    margin-right: 32px;
    color: #ffffff;
    font-family: Roboto;
    font-size: 39px;
    letter-spacing: 0;
    line-height: 48px;
    text-align: center;
    border-radius: 50%;
    border: 3px solid #121212;
  }

  .alphaNumericKeyboardBtn#shftId {
    justify-content: center;
    align-items: center;
    margin-right: 28px !important;
  
    &:focus {
      border: 3px solid white;
      color: white;
      border-radius: 100% !important;
    }
  }

  .spaceButton {
    width: 200px;
    height: 50px;
    justify-content: center;
    display: inline-grid;
    align-items: center;
  }

  .comIdButton {
    width: 85px;
    height: 50px;
    margin-left: 50px;
  }

  .arButton {
    width: 85px;
    height: 50px;
  }

  .gmailButton {
    width: 200px;
    height: 50px;
  }

  .alphaNumericKeyboardSpc {
    margin-bottom: 48px;
    margin-right: 32px;
    color: #ffffff;
    font-family: Roboto;
    font-size: 39px;
    letter-spacing: 0;
    line-height: 48px;
    text-align: center;
    border: 3px solid #121212;
  }

  .alphaNumericKeyboardBtn:focus {
    color: #ffffff;
    border-radius: 50%;
    border: 3px solid white;
  }

  .alphaNumericKeyboardSpc:focus {
    color: #ffffff;
    border-radius: 6px;
    border: 3px solid white;
  }

  .mailButtons {
    margin-right: 23px;
    margin-bottom: 35px;
    color: #ffffff;
    font-family: Roboto;
    font-size: 28px !important;
    letter-spacing: 0;
    line-height: 29.04px;
    text-align: center;
    border: 3px solid #121212;
  }

  .hotmail {
    width: 200px;
    height: 50px;
    margin-right: 0px;
  }

  .mailButtons:focus {
    color: #ffffff;
    border-radius: 50%;
    border: 3px solid white;
  }

  //Numeric Keyboard
  .KeyboardBtn {
    margin-right: 55px;
    color: #ffffff;
  }

  .KeyboardBtnFocus {
    color: #ffffff;
    margin-right: 55px;
    height: 61.6px;
    width: 61.6px;
    border-radius: 50%;
  }

  .KeyboardBtnFocus:focus {
    color: #ffffff;
    border-radius: 50px;
    border: 3px solid white;
  }

  .KeyboardContentTxt {
    text-align: center;
    font: normal normal 38px/45px Roboto;
    letter-spacing: 0px;
    opacity: 1;
    margin: 0;
  }
}