import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  data: [],
  castData: [],
  isLoading: false,
  error: null,

  watchList: [],
  addWatchList: {},
  delWatchList: {},
  isGetWatchlistLoading: false,
  isAddWatchlistLoading: false,
  isDelWatchlistLoading: false,
  getWatchlistError: {},
  addWatchlistError: {},
  delWatchlistError: {}
}

export const vodMoviesSlice = createSlice({
  name: 'movies',
  initialState,
  reducers: {
    getVodMoviesData: state => {
      state.isLoading = true
    },
    getVodMoviesSuccess: (state, { payload }) => {
      state.data = payload?.response?.groups ?? payload?.data?.groups 
      state.isLoading = false
    },
    getVodMoviesError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getVodMoviesCastData: state => {
      state.isLoading = true
    },

    getVodMoviesCastSuccess: (state, { payload }) => {
      state.castData = payload?.response?.group
      state.isLoading = false
    },
    getVodMoviesCastError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getClearVodState: (state,{})=>{
      state.castData = {}
      state.data = {}
    },

    //get movies watchlist page
    getVodMoviesWatchlist: state => {
      state.isGetWatchlistLoading = true
    },

    getVodMoviesWatchListSuccess: (state, action) => {
      state.watchList = action?.payload?.response?.groups
      state.isGetWatchlistLoading = false
      state.addWatchList = {}
      state.delWatchList = {}
    },

    getVodMoviesWatchlistError: (state, action) => {
      state.isGetWatchlistLoading = false
      state.getWatchlistError = action.payload
    },

    //add movie watch list
    addVodMoviesWatchList: state => {
      state.isAddWatchlistLoading = true
    },
    addVodMoviesWatchlistSuccess: (state, action) => {
      state.addWatchList = action?.payload
      state.isAddWatchlistLoading = false
    },
    addVodMoviesWatchListError: (state, action) => {
      state.isAddWatchlistLoading = false
      state.addWatchlistError = action.payload
    },

    //edit movie watch list

    deleteVodMoviesWatchList: state => {
      state.isDelWatchlistLoading = true
    },
    deleteVodMoviesWatchListSuccess: (state, action) => {
      state.delWatchList = action?.payload
      state.isDelWatchlistLoading = false
    },
    deleteVodMoviesWatchListError: (state, action) => {
      state.isDelWatchlistLoading = false
      state.delWatchlistError = action.payload
    }
  }
})

export const {
  getVodMoviesData,
  getVodMoviesSuccess,
  getVodMoviesError,
  getVodMoviesCastData,
  getVodMoviesCastSuccess,
  getVodMoviesCastError,
  getClearVodState,
  getVodMoviesWatchlist,
  getVodMoviesWatchListSuccess,
  getVodMoviesWatchlistError,
  addVodMoviesWatchList,
  addVodMoviesWatchlistSuccess,
  addVodMoviesWatchListError,
  deleteVodMoviesWatchList,
  deleteVodMoviesWatchListSuccess,
  deleteVodMoviesWatchListError
} = vodMoviesSlice.actions

export default vodMoviesSlice.reducer
