import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import NavSubMenu from "./NavSubMenu";
import { store as realStore } from "../../store/sagaStore";
import '@testing-library/jest-dom';

// Mock the store dispatch
jest.mock("../../store/sagaStore", () => ({
  store: {
    dispatch: jest.fn()
  }
}));

const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
);

const renderWithState = (ui, initialState) => {
  const store = mockStore(initialState);
  return {
    ...render(ui, {
      wrapper: (props) => <Wrapper {...props} reduxStore={store} />
    }),
    store
  };
};

describe("NavSubMenu component tests", () => {
  const mockGetItem = jest.fn().mockReturnValue("0");
  const mockSetItem = jest.fn();
  const mockAddEventListener = jest.fn();
  const mockRemoveEventListener = jest.fn();
  let mockEventListener;
  
  beforeEach(() => {
    mockGetItem.mockClear();
    mockSetItem.mockClear();
    mockAddEventListener.mockClear();
    mockRemoveEventListener.mockClear();
    realStore.dispatch.mockClear();
    mockEventListener = { keydownHandler: null };

    global.localStorage = {
      getItem: mockGetItem,
      setItem: mockSetItem
    };

    global.SpatialNavigation = {
      focus: jest.fn(),
      init: jest.fn(),
      add: jest.fn(),
      makeFocusable: jest.fn()
    };

    document.getElementById = jest.fn().mockImplementation((id) => {
      if (id === "navtabgenres") {
        return {
          addEventListener: mockAddEventListener.mockImplementation((event, handler) => {
            mockEventListener.keydownHandler = handler;
          }),
          removeEventListener: mockRemoveEventListener.mockImplementation((event, handler) => {
            if (mockEventListener.keydownHandler === handler) {
              mockEventListener.keydownHandler = null;
            }
          }),
          scrollLeft: 0,
          getBoundingClientRect: () => ({
            left: 0,
            right: 500
          })
        };
      }
      return {
        focus: jest.fn()
      };
    });
    Element.prototype.getBoundingClientRect = jest.fn().mockReturnValue({
      left: 100,
      right: 200
    });
  });

  test("should render with premium genre data", () => {
    const initialState = {
      login: {
        loginSuccess: { response: { user_id: "123" } },
        registerSuccess: { response: { user_id: "456" } }
      },
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { code: "genre1", text: "Genre 1" },
                  { code: "genre2", text: "Genre 2" }
                ]
              }
            ]
          }
        },
        premiumNodeValue: ["playandgo"]
      },
      SubMenuFilter: {
        submenudata: false,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0,
        tabvalue: null
      }
    };

    const { container } = renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    expect(container.querySelector(".nav-sub-tab-container")).toBeInTheDocument();
  });

  test("should handle button click for child without app_behaviour layout", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { 
                    code: "genre1", 
                    text: "Genre 1",
                    childs: [{ code: "subgenre1", text: "Sub Genre 1" }],
                    app_behaviour: JSON.stringify({
                      layout: "other"
                    })
                  }
                ]
              }
            ]
          }
        }
      },
      SubMenuFilter: {
        submenudata: true,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0
      }
    };

    const { container } = renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    
    const buttons = container.querySelectorAll(".NavSubTab");
    fireEvent.click(buttons[0]);
    
    expect(realStore.dispatch).toHaveBeenCalledWith(expect.objectContaining({
      type: expect.stringContaining("getsubmenuval")
    }));
  });

  test("should render with mapped text when premiumsubmenutab is available", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { 
                    code: "genre1", 
                    text: "Genre 1",
                    childs: [
                      { code: "subgenre1", text: "Sub Genre 1" }
                    ]
                  }
                ]
              }
            ]
          }
        }
      },
      SubMenuFilter: {
        submenudata: true,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0,
        tabvalue: "genre1"
      }
    };

    const { container } = renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    
    expect(container.querySelector(".childsubmenu")).toBeInTheDocument();
  });

  test("should handle keyboard navigation", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { code: "genre1", text: "Genre 1" },
                  { code: "genre2", text: "Genre 2" }
                ]
              }
            ]
          }
        }
      },
      SubMenuFilter: {
        submenudata: true,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0
      }
    };

    renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    const mockCards = [
      { focus: jest.fn(), getBoundingClientRect: () => ({ left: 50, right: 150 }) },
      { focus: jest.fn(), getBoundingClientRect: () => ({ left: 160, right: 260 }) },
      { focus: jest.fn(), getBoundingClientRect: () => ({ left: 270, right: 370 }) }
    ];
    
    document.querySelectorAll = jest.fn().mockReturnValue(mockCards);
    Object.defineProperty(document, 'activeElement', {
      get: jest.fn().mockReturnValue(mockCards[0]),
      configurable: true
    });

    const keydownHandler = mockEventListener.keydownHandler;
    expect(typeof keydownHandler).toBe('function');
    const rightKeyEvent = { key: 'ArrowRight', preventDefault: jest.fn() };
    keydownHandler(rightKeyEvent);

    const leftKeyEvent = { key: 'ArrowLeft', preventDefault: jest.fn() };
    Object.defineProperty(document, 'activeElement', {
      get: jest.fn().mockReturnValue(mockCards[1]),
      configurable: true
    });
    keydownHandler(leftKeyEvent);
  });

  test("should handle effect cleanup", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { code: "genre1", text: "Genre 1" }
                ]
              }
            ]
          }
        },
        premiumNodeValue: ["somevalue"]
      },
      SubMenuFilter: {
        submenudata: false,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0
      }
    };

    const { unmount } = renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    expect(mockAddEventListener).toHaveBeenCalled();
    unmount();
    expect(mockRemoveEventListener).toHaveBeenCalled();
  });

  test("should render null when filteredObj is undefined", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: []
          }
        }
      },
      SubMenuFilter: {
        submenudata: false,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0
      }
    };

    const { container } = renderWithState(<NavSubMenu tabValue="nonexistent" />, initialState);
    expect(container.firstChild).toBeNull();
  });

  test("should handle submenu true state", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { code: "genre1", text: "Genre 1" }
                ]
              }
            ]
          }
        }
      },
      SubMenuFilter: {
        submenudata: true,
        index: 0,
        filtercards: false,
        filterclickdata: false,
        cuerrntindex: 0
      }
    };

    const { container } = renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    
    expect(container.querySelector(".genrecarddata")).toBeInTheDocument();
    expect(container.querySelector(".subrails")).toBeInTheDocument();
  });

  test("should render correctly when hidedata is true", () => {
    const initialState = {
      login: {},
      homeReducer: {
        navbarNodeData: {
          response: {
            nodes: [
              {
                code: "playandgo",
                text: "Play & Go",
                app_behaviour: JSON.stringify({
                  node_config: { show_node: true }
                }),
                childs: [
                  { code: "genre1", text: "Genre 1" }
                ]
              }
            ]
          }
        }
      },
      SubMenuFilter: {
        submenudata: true,
        index: 0,
        filtercards: false,
        filterclickdata: true,
        cuerrntindex: 0
      }
    };

    const { container } = renderWithState(<NavSubMenu tabValue="playandgo" />, initialState);
    
    expect(container.querySelector(".hidedata")).toBeInTheDocument();
  });
});