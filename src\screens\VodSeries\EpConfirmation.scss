@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

$color-white: #ffffff;
$font-family-roboto: Roboto;
$position-absolute: absolute;
$text-wrap-nowrap: nowrap;
$font-weight-bold: bold;
$box-sizing-border: border-box;
$position-relative: relative;
$display-flex: flex;
$text-overflow-ellipsis: ellipsis;
$overflow-hidden: hidden;

.epconf-body {
    top: 0px;
    left: 0px;
    width: 1920px;
    position: relative;
    height: 1080px;
    background-color: black;

    .confirmation-logo-img {
        width: 244.04px;
        position: absolute;
        left: 91px;
        top: 34px;
        width: 244.04px;
    }

    .confirmation-main-title {
        height: 66px;
        width: 1087px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 56px;
        font-weight: bold;
        letter-spacing: -0.74px;
        line-height: 61px;
        text-align: center;
        position: absolute;
        top: 198px;
        left: 416px;
    }

    .play-confirmation-main-title {
        height: 66px;
        width: 1087px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 56px;
        font-weight: bold;
        letter-spacing: -0.74px;
        line-height: 61px;
        text-align: center;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
        top: 215px;
    }

    .play-confirmation-login-title {
        height: 42px;
        width: 760px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36px;
        letter-spacing: 1.47px;
        line-height: 39px;
        text-align: center;
        position: absolute;
        top: 333px;
        left: 50%;
        transform: translateX(-50%);
        margin: 0;
    }

    .confirmation-login-title {
        height: 42px;
        width: 1087px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36px;
        letter-spacing: -0.47px;
        line-height: 39px;
        text-align: center;
        position: absolute;
        top: 333px;
        left: 416px;
    }

    .main-buttons {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 473px;
        left: 670px;
        width: 590px;

        .confirm-register-name {
            height: 72px;
            width: 509px;
            position: relative;
            left: 36px;
            border-radius: 8.8px;
            background-color: #981C15;
            color: #ffffff;
            text-transform: uppercase;
            font-family: Roboto;
            font-size: 36px;
            font-weight: bold;
            letter-spacing: -0.58px;
            line-height: 42.18px;
            text-align: center;

            &:focus {
                height: 82.08px;
                left: 0px;
                width: 580.26px;
                border-radius: 10.03px;
            }
        }

        .confirm-sign-name {
            height: 72px;
            width: 509px;
            position: relative;
            text-transform: uppercase;
            left: 36px;
            margin-top: 32px;
            border-radius: 8.8px;
            background-color: #6C57A0;
            color: #ffffff;
            font-family: Roboto;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: -0.51px;
            line-height: 35px;
            text-align: center;

            &:focus {
                height: 82.08px;
                left: 0px;
                width: 580.26px;
                border-radius: 10.03px;
            }
        }

        .confirm-cancel-button {
            height: 72px;
            width: 509px;
            margin-top: 32px;
            text-transform: uppercase;
            position: relative;
            left: 36px;
            border-radius: 8.8px;
            background-color: #2e303d;
            color: #ffffff;
            font-family: Roboto;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: -0.51px;
            line-height: 35px;
            text-align: center;

            &:focus {
                height: 82.08px;
                left: 0px;
                width: 580.26px;
                border-radius: 10.03px;
            }
        }
    }
}