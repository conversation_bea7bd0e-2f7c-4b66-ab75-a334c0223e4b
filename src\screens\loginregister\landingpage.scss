$position-center: center;
$color-white: #eeeeee;
$font-family-roboto: '<PERSON><PERSON>';
$white-space-nowrap: nowrap;
$overflow-hidden: hidden;
$text-overflow-ellipsis: ellipsis;

.landingpage {
    position: fixed;
}

.app-logo {
    width: 1920px;
    height: 1080px;
}

.logo-size {
    margin-top: 107px;
    margin-left: 675px;
}

button {
    all: unset;
}

.landingpage-Header {
    position: relative;
    bottom: 769px;
    width: 1920px;
    height: 85px;
    text-align: $position-center;
    letter-spacing: -0.84px;
    color: $color-white;
    text-shadow: 0px 9px 23px #00000080;
    opacity: 1;
    font-size: 64px;
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
}

.landingpage-Title,
.landing-page-mexico-title {
    text-align: $position-center;
    letter-spacing: -0.47px;
    font-family: 'Roboto', sans-serif !important;
    color: $color-white;
    position: absolute;
    opacity: 1;
    font-weight: bold;
    top: 407px;
    left: 534px;
    height: 48px;
    font-size: 36px;
    margin-top: 18px !important;
}

.landing-page-mexico-title-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.main-buttons {
    font-family: $font-family-roboto;
    top: 517px;
    height: 316px;
    position: absolute;
}

.main-buttons-mexico {
    font-family: $font-family-roboto;
    margin-top: 0px;
    height: 316px;
}

.register-name {
    background: #981c15 0% 0% no-repeat padding-box;
    border-radius: 10px;
    opacity: 1;
    margin-top: 20px;
    margin-left: 713px;
    width: 504px;
    height: 80px;
    font-size: 32px;
    text-align: $position-center;
    color: $color-white;
    font-family: $font-family-roboto;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
    font-weight: bold;
}

.sign-name {
    background: #6c57a0 0% 0% no-repeat padding-box;
    border-radius: 10px;
    opacity: 1;
    margin-top: 31px;
    margin-left: 713px;
    width: 504px;
    height: 80px;
    font-size: 32px;
    text-align: $position-center;
    color: $color-white;
    font-family: $font-family-roboto;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
    font-weight: bold;
}

.Watch-name {
    background: #2e303d 0% 0% no-repeat padding-box;
    border-radius: 10px;
    opacity: 1;
    margin-top: 31px;
    margin-left: 713px;
    width: 504px;
    height: 80px;
    font-size: 32px;
    text-align: $position-center;
    color: $color-white;
    font-family: $font-family-roboto;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
    font-weight: bold;
}

.landing-page-telmex-login-button {
    background: #2e303d 0% 0% no-repeat padding-box;
    border-radius: 10px;
    opacity: 1;
    margin-top: 31px;
    width: 504px;
    height: 80px;
    font-size: 32px;
    text-align: $position-center;
    color: $color-white;
    font-family: $font-family-roboto;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
    font-weight: 460;

    &:focus {
        background: #2e303d 0% 0% no-repeat padding-box;
        width: 568px;
        height: 85px;
        font-size: 36px;
    }
}

.subscription {
    margin-top: 84px;
    left: 652px;
    height: 33px;
    text-align: $position-center;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    font-size: 30px;
    font-family: $font-family-roboto;
    width: 438px;
    margin-left: 505px;
}

.active-name {
    margin-top: -37px;
    margin-left: 969px;
    background: #2e303d 0% 0% no-repeat padding-box;
    border-radius: 10px;
    opacity: 1;
    text-align: $position-center;
    font-size: 25px;
    color: $color-white;
    width: 360px;
    height: 80px;
    font-family: $font-family-roboto;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.register-name:focus {
    background: #981c15 0% 0% no-repeat padding-box;
    width: 568px;
    margin-left: 678px;
    height: 85px;
    font-size: 36px;
}

.Watch-name:focus {
    background: #2e303d 0% 0% no-repeat padding-box;
    width: 568px;
    margin-left: 678px;
    height: 85px;
    font-size: 36px;
}

.sign-name:focus {
    width: 568px;
    margin-left: 678px;
    height: 85px;
    font-size: 36px;
}

.active-name:focus {
    background: #981c15 0% 0% no-repeat padding-box;
}

.activate-button {
    height: 80px;
}