.nav-sub-tab-container {
    overflow: scroll hidden;
    scroll-snap-type: x mandatory;
}

.NavSubTab {
    text-transform: uppercase;
    opacity: 1;
    margin: 23px 15px 14px 15px;
    width: 382px;
    height: 90px;
    font-size: 28px;
    text-align: center;
    background-color: #26272A;
    font-family: <PERSON><PERSON>;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    scroll-snap-type: x mandatory;
    border: 4px solid transparent !important;
    padding: 4px;
}

.NavSubTab.active{
    background-color: #981C15;
    opacity:1;
    
}

.NavSubTab:focus {
    margin-top: 23px;
    font-size: 30.79px;
    line-height: 35.18px;
    color: #ffffff;
   /* transform: scale(1.02); commented the dev-vijay wants it to be removed*/
    scroll-snap-align: end;
    border: 4px solid #fff !important;
  
}


.Subnav-block {
    display: -webkit-inline-box;
    margin-left: 55px;
    width: 100%;
    scroll-snap-type: x mandatory;
    margin-bottom: 36px;
}
.backBtn {
    display: flex;
    position: absolute;
    right:87px;
    top:130px;
    justify-content: space-between;
    align-items: center;
    width:292px;
    height:42px;
    background-color: #2E303D;
    padding:9px 24px;
    border-radius:6.6px;
}

.backBtn:focus {
border:2px solid #fff;
}
.backText {
    color:#fff;
    font-size: 29px;
    line-height: 29px;
}
.childsubmenu{
    display: flex;
}
.hidedata {
    display: none;
}

.Subnavhome_block {
    display: flex;
    width: 450%;
    margin-bottom:2rem;
    margin-top:2rem;
    scroll-snap-type: x mandatory;
}

