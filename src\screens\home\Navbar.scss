$primeColor: #9b0f0f;
$white-space-nowrap: nowrap;
$overflow-hidden: hidden;
$text-overflow-ellipsis: ellipsis;

.notification-title {
  color: #ffffff;
  font-size: 30px;
  margin-left: 170px;
  width: 1146px;
}

.ok-button {
  display: flex;
  position: absolute;
  right: 275px;
  top: 22px;
  justify-content: center; // Centering text inside the button
  align-items: center;
  width: 200px;
  border-radius: 7px;
  height: 54px;
  background-color: cornflowerblue;

  &:focus {
    border: 2px solid #fff !important;
    opacity: 1;
  }
}

.resgister-back-button {
  display: flex;
  position: absolute;
  right: 47px;
  top: 22px;
  justify-content: center; // Centering text inside the button
  align-items: center;
  border-radius: 7px;
  width: 200px;
  height: 54px;
  background-color: cornflowerblue;

  &:focus {
    border: 2px solid #fff !important;
    opacity: 1;
  }
}

.ok-greendot, .back-yellowdot {
  width: 25px;
  height: 25px;
}
.back-yellowdot {
  margin-left: 22px;
}
.okbutton-Text, .backbutton-Text {
  color: #ffffff;
  font-size: 29px; // Unified font size for consistency
  white-space: $white-space-nowrap;
  overflow: $overflow-hidden;
  text-overflow: $text-overflow-ellipsis;
  margin-left: 15px;
}

.register-popup {
  overflow-y: auto; // Allows smooth scrolling for long content
  width: 100%;
  height: 100px;
  background: #274df7;
}

.hidepop-up {
  display: none;
}

.blue-band-homemain-Container1,
.homemain-Container1 {
  overflow-y: auto; // Enable scrolling for the main container
  width: 100%;
  height: 100%; // Ensure it takes up the full viewport height
  box-sizing: border-box;
  position: relative;

  .nav-Container {
    position: sticky;
    top: 0;
    height: 113px;
    z-index: 9;
    background-color: #000; // Ensure background color on sticky
    padding-bottom: 19px;

    .navbar, .blue-band-navbar {
      display: flex;
      align-items: center;
      justify-content: center; // Ensures items are spread out
      width: 100%;
      opacity: 0.9;
      height: 12vh;
      position: fixed;
      z-index: 10;
      background-color: #000000; // Make sure it covers the entire navbar
    }

    .logo {
      color: white;
      font-size: 24px;
      font-weight: bold;
      position: absolute;
      left: 57px;
    }

    .nav-center-alg {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .nav-Items {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      background-color: #28292f;
      border-radius: 50px;
      height: 74px;

      .search-img,
      .search-imgfocused {
        margin-left: 5px;
        width: 36px;
        height: 36px;
        padding: 3px;
      }

      .search-Icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 60px;
        height: 60px;
        text-decoration: none;

        &:focus {
          border-radius: 50px;
          color: white;
          border: 2px solid #fff;
          background-color: unset;
        }
      }

      .search-Icon-focus {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 65px;
        height: 60px;
        background-color:#9b0f0f;
        border-radius: 50px;
        flex-shrink: 0;

        &:focus {
          border-radius: 50px;
          color: white;
          background-color: red;
          width: 65px;
          height: 60px;
        }
      }

      .search-Icon-Active {
        background-color: $primeColor;
        width: 57px;
        height: 42px;
       padding: 8px;
        border-radius: 37px;
        border: none;

        &.active {
          background-color: $primeColor;
        }
      }

      .seach-Icon-Selected {
        background-color: #de1717;
        width: 20px;
        height: 54px;
        border-radius: 50px;
      }

      .centered-items {
        display: flex;
        align-items: center;
        justify-content: flex-start; // Align items to the start for scroll
        overflow-x: auto; // Enable horizontal scrolling for long content
        margin-left: 20px;

        .searchParent {
          width: 30px;
          height: 50px;
          margin-right: 70px;
        }
      }

      .nav-iconnav-item {
        display: flex;
        align-items: center;
        justify-content: center; // Centering icon within the item
        width: 60px;
        height: 60px;

        &:focus {
          border: 4px solid red !important;
          border-radius: 100px;
        }

        &.nav-iconnav-active {
          border: 8px solid #de1717 !important;
          border-radius: 100px;
        }
      }
    }

    .navfocus, .navActive, .navSelected {
      height: 72px;
      text-align: center;
      font-family: 'Roboto', sans-serif;
      font-weight: bold;
      letter-spacing: 0.38px;
      line-height: 32px;
      text-transform: capitalize;
      padding: 0 20px;
      flex: 0 0 auto;
      margin-right: 20px;
      border-radius: 50px;

      &:focus {
        color: white;
        background-color: #de1717;
        outline: none;
      }
    }

    .navfocus {
      color: #7f8282;
      background-size: 145px 20px;
      font-size: 29px;
    }

    .navActive, .navSelected {
      color: white;
      font-size: 29px;
      background-color: $primeColor;
      border: none;

      &.active {
        background-color: $primeColor;
        outline: none;
      }
    }

    .navSelected {
      border: 2px solid yellow;
      background-color: #9b0f0f !important;
    }
  }
}

.profile-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
  position: relative;
  text-decoration: none;

  &:focus {
    border: 4px solid red !important;
    border-radius: 100%;
    margin-left: 3px;
  }

  .avatar-img {
    width: 56px;
    height: 56px;
    object-fit: cover;
  }
}

.right-aligned-items {
  display: flex;
  align-items: center;
}

button {
  all: unset;
}

.settingsimg {
  width: 60px;
  height: 60px;
  border-radius: 75px;
  opacity: 1;
}

.search-default {
  border: none;
  background-color: #28292f;
  border-radius: 50px;
}

.home-logo-img {
  width: 171px;
  height: 36px;
  object-fit: contain;
}
