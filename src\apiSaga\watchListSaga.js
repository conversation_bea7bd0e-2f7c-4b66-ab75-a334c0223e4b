import { call, takeEvery } from 'redux-saga/effects'
import { COMMON_URL, URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getWatchListError,
  getWatchListSuccess,
  addWatchListError,
  addWatchListSuccess,
  delWatchListError,
  delWatchListSuccess,
  delContinueWatchSuccess,
  delContinueWatchError,
  continueWatchlistSuccess,
  continueWatchlistError,
  lastSeenWatchListResponse,
  lastSeenWatchListResponseError
} from '../store/slices/getWatchListSlice'

export function* getWatchList(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.GET_WATCHLIST_URL +
        `user_id=${data?.payload?.userId}&HKS=${data?.payload?.HKS}&user_hash=${data?.payload?.user_hash}&lasttouch=${data?.payload?.id}&region=${region}&filterlist=${data?.payload?.filterlist}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getWatchListSuccess(response))
        },
        onError(error) {
          store.dispatch(getWatchListError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(getWatchListError(error))
  }
}

export function* addWatchList(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.ADD_WATCHLIST_URL}user_id=${data?.payload?.userId}&HKS=${data?.payload?.HKS}&user_hash=${data?.payload?.user_hash}&object_id=${data?.payload?.id}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(addWatchListSuccess(response))
        },
        onError(error) {
          store.dispatch(addWatchListError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(addWatchListError(error))
  }
}

export function* delWatchList(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.DEL_WATCHLIST_URL}user_id=${data.payload.userId}&HKS=${data?.payload?.HKS}&user_hash=${data?.payload?.user_hash}&object_id=${data?.payload?.id}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(delWatchListSuccess(response))
        },
        onError(error) {
          store.dispatch(delWatchListError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(delWatchListError(error))
  }
}

export function* continueWatchlist({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.GETCONTINUE_WATCH_LIST}user_id=${payload?.userId}&user_hash=${payload?.user_hash}&object_id=${payload?.id}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(continueWatchlistSuccess(response))
        },
        onError(error) {
          store.dispatch(continueWatchlistError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(continueWatchlistError(error))
  }
}

export function* delContinueWatch(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.GETCONTINUE_WATCH_DEL}&user_id=${data.payload.userId}&user_hash=${data?.payload?.user_hash}&object_id=${data?.payload?.id}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(delContinueWatchSuccess(response))
          localStorage.setItem(
            'continueWatchLasttouch',
            response?.lasttouch?.seen
          )
        },
        onError(error) {
          store.dispatch(delContinueWatchError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(delContinueWatchError(error))
  }
}

export function* lastSeenWatchApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.GETSEENLAST_VCARD}&filterlist=${payload?.filterlist}&group_id=${payload?.group_id}&lasttouch=${payload?.lasttouch}&region=${region}&user_hash=${payload?.user_hash}&user_id=${payload?.userId}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(lastSeenWatchListResponse(response))
        },
        onError(error) {
          store.dispatch(lastSeenWatchListResponseError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(lastSeenWatchListResponseError(error))
  }
}

export default function* watchListSaga() {
  yield takeEvery('watchList/getWatchList', getWatchList)
  yield takeEvery('watchList/addWatchList', addWatchList)
  yield takeEvery('watchList/delWatchList', delWatchList)
  yield takeEvery('watchList/continueWatchlist', continueWatchlist)
  yield takeEvery('watchList/delContinueWatch', delContinueWatch)
  yield takeEvery('watchList/lastSeenWatch', lastSeenWatchApi)
}
