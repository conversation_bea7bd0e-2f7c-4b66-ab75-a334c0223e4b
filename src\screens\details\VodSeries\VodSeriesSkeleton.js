import React from 'react'
import "./VodSeriesSkeleton.css"

export function VodSeriesSkeleton() {
  return (
    <div>
    <div className="description-page-shimmer">
    <div className="description-page-header skeleton"></div>
     <div className="description-page-subtitle skeleton"></div>
     <div className='page-description skeleton'></div>
     <div className='description-page-duration skeleton'></div>
    <div className="Vod-season-shimmer">
      {Array.from({ length: 12 }, (_, index) => (
          <div key={index}  className="Vod-season-button skeleton"></div>
      ))}
    </div>
    </div>


    <div className="Vod-episode-shimmer">
      {Array.from({ length: 12 }, (_, index) => (
        <div key={index} className="vod-episode-card">
          <div className="epg-cell-image skeleton"></div>
          <div className="vod-cell-content skeleton"></div>
        </div>
      ))}
    </div> 
     <div className="Vod-episode-shimmer">
      {Array.from({ length: 12 }, (_, index) => (
        <div key={index} className="vod-cast-card">
          <div className="vod-cell-content skeleton"></div>
        </div>
      ))}
    </div> 
     <div className="Vod-episode-shimmer">
      {Array.from({ length: 12 }, (_, index) => (
        <div key={index} className="vod-mlt-card">
          <div className="vod-cell-content skeleton"></div>
        </div>
      ))}
    </div>
  </div>
  )
}