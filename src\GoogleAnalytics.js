import * as firebaseAnalytics from './GoogleAnalyticsConstants'
import { COMMON_URL } from './utils/environment'

const measurementId = 'G-N7M15SPSVV'
const apiSecretKey = 'CfGNG9BnSnCqwQs5ws1VbQ'
// clientId is unique to identify an application
// logged-in userId
const Timestamp = Date.now() * 1000

function getSessionId() {
  const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  const now = Date.now();
  const lastActivity = Number(localStorage.getItem('lastActivity') || 0);
  let sessionId = localStorage.getItem('session_id');
  const clientId = localStorage.getItem('setDeviceID') ?? '23fe0340a788bd5cab7d82b5d37b2c6e'

  if (!sessionId || now - lastActivity > SESSION_TIMEOUT) {
    // Generate new session_id
    const timestamp = Math.floor(Date.now() / 1000);
    const random = Math.floor(1000 + Math.random() * 9000);
    sessionId = `${clientId}${timestamp}${random}`;
  }
  localStorage.setItem('session_id', sessionId);
  localStorage.setItem('lastActivity', now);
  return sessionId;
}
function getCountryCode(region) {
  return firebaseAnalytics.regionToCountryCode[region?.toLowerCase()] || ''
}
const URL = `https://www.google-analytics.com/mp/collect?api_secret=${apiSecretKey}&measurement_id=${measurementId}`

const logEvent = (event, payload) => {
  const LoginId = localStorage.getItem('user_id')
  const clientId =
    localStorage.getItem('setDeviceID') ?? '23fe0340a788bd5cab7d82b5d37b2c6e'
  const Timestamp = Date.now() * 1000

  const body = {
    client_id: clientId,
    timestamp_micros: Timestamp,
    user_properties: {
      sign_up_method: { value: firebaseAnalytics.EMAIL },
      user_type: {
        value: LoginId
          ? firebaseAnalytics.LOGGED_IN
          : firebaseAnalytics.GUEST_PERSON
      }
    },
    non_personalized_ads: false,
    events: [
      {
        name: event,
        params: payload
      }
    ]
  }

  if (LoginId) {
    body.user_id = LoginId
    body.user_properties.parent_id = { value: LoginId }
  }

  fetch(URL, {
    credentials: 'same-origin',
    method: 'POST',
    body: JSON.stringify(body)
  })
}

export const pushLandingEvent = (interaction,engagement_time_msec) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'landing'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = 'landing'
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'landing'
  payload[firebaseAnalytics.PAGE_TITLE] ='landing'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'loginActivity'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.LANDING, payload)
}

export const pushLoginEvent = (interaction,module,time) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'login'
  payload[firebaseAnalytics.PAGE_TITLE] ='login'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'loginActivity'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.INTERACTION_LOGIN, payload)
}

export const pushLoginErrorEvent = (error,module,time) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.ERROR_NAME] = error
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'login'
  payload[firebaseAnalytics.PAGE_TITLE] = 'login'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'loginActivity'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.ERROR_LOGIN, payload)
}

export const pushLoginSuccessEvent = (data,time) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'login'
  payload[firebaseAnalytics.PAGE_TITLE] = 'login'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'loginActivity'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.LOGIN, payload)
}

export const pushLoginPrivacyEvent = (interaction,module,engagement_time_msec) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module ?? "Nuestro aviso de privacidad ha cambiado, para continuar acepte los nuevos términos"
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'login-Terms-and-Conditons'
  payload[firebaseAnalytics.PAGE_TITLE] = 'Aviso de privacidad'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'login'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.PRIVACY_NOTICE, payload)
}

export const pushLogoutEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.userDetails?.subscriptions && Object.keys(data?.userDetails?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.userDetails?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.menuButton ?? 'inicio'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.userDetails?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = data?.interaction
  payload[firebaseAnalytics.MODULO_NAME] = data?.module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'settings'
  payload[firebaseAnalytics.PAGE_TITLE] = 'inicio' //value as per GA document.
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'home'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.time
  logEvent(firebaseAnalytics.LOGOUT, payload)
}

export const pushProfileEvent = (data) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.userDetails?.subscriptions && Object.keys(data?.userDetails?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.userDetails?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.userDetails?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = data?.interaction
  payload[firebaseAnalytics.MODULO_NAME] = data?.module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'mi perfil'
  payload[firebaseAnalytics.PAGE_TITLE] = data?.module
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'login'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.time
  logEvent(firebaseAnalytics.PROFILE_ADMIN, payload)
}

export const pushAddProfileEvent = (data,interaction,module,time) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'administrar perfiles'
  payload[firebaseAnalytics.PAGE_TITLE] = module
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'mi perfil'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.PROFILE_ADMIN, payload)
}

export const pushDeleteProfileEvent = (data,interaction,module,time) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'login'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'administrar perfiles'
  payload[firebaseAnalytics.PAGE_TITLE] = module
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'mi perfil'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.PROFILE_ADMIN, payload)
}

export const pushRegisterEvent = (interaction,module,engagement_time_msec) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'registro'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'registro'
  payload[firebaseAnalytics.PAGE_TITLE] ='registro'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'landing'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.INTERACTION_SIGN_UP, payload)
}

export const pushRegisterSuccessEvent = (data,engagement_time_msec) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'registro'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'registro'
  payload[firebaseAnalytics.PAGE_TITLE] ='registro'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'landing'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.SIGN_UP, payload)
}

export const pushRegisterCompleteEvent = (data,interaction,engagement_time_msec) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'inicio'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = "cintillo azul"
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'home'
  payload[firebaseAnalytics.PAGE_TITLE] ='inicio'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'registro'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.INTERACTION_SELECT_PLAN, payload)
}

export const pushForgotPasswordEvent = (interaction,module,engagement_time_msec) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = 'landing'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.PAGE_PATH] = 'olvidaste tu contraseña'
  payload[firebaseAnalytics.PAGE_TITLE] ='olvidaste tu contraseña'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'loginActivity'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.FORGOT_PASSWORD, payload)
}

export const pushForgotPasswordSuccessEvent = (interaction,module,engagement_time_msec) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SECTION] = 'landing'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.PAGE_PATH] = 'olvidaste tu contraseña'
  payload[firebaseAnalytics.PAGE_TITLE] ='olvidaste tu contraseña'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'loginActivity'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = engagement_time_msec
  logEvent(firebaseAnalytics.FORGOT_PASSWORD, payload)
}

export const pushMenuEvent = (data,menuName,time) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.CONTENT_SECTION] = menuName
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MENU_NAME] = menuName
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
  payload[firebaseAnalytics.PAGE_TITLE] =menuName
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'home'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.MENU, payload)
}

export const pushMenuProfileEvent = (data,menuName,contentSection,time) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.CONTENT_SECTION] = contentSection ?? 'inicio'
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MENU_NAME] = menuName
  payload[firebaseAnalytics.PAGE_PATH] = 'settings'
  payload[firebaseAnalytics.PAGE_TITLE] = contentSection ?? 'inicio'
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'home'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = time
  logEvent(firebaseAnalytics.MENU_PROFILE, payload)
}

export const pushSubMenuEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.userDetails?.subscriptions && Object.keys(data?.userDetails?.subscriptions)?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.userDetails?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.menuName
  payload[firebaseAnalytics.COUNTRY] = data?.userDetails?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MENU_NAME] = data?.menuName
  payload[firebaseAnalytics.SUBMENU_NAME] = data?.subMenuName
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
  payload[firebaseAnalytics.PAGE_TITLE] = data?.menuName
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = 'home'
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.time
  logEvent(firebaseAnalytics.SUB_MENU, payload)
}

export const pushSearchEvent = (event_name,searchData,contentData,index) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = searchData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_category
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.SEARCH
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.SEARCH_TERM] = searchData?.search_term
  payload[firebaseAnalytics.INTERACTION_TYPE] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_LIST] = 'resultados'
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.id ? contentData?.id : contentData?.group_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.title
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.is_series ? firebaseAnalytics.SERIES : firebaseAnalytics.MOVIE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.format_types
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_POSITION] = index
  payload[firebaseAnalytics.MODULO_NAME] = 'cards listado infinito resultados buscador'
  payload[firebaseAnalytics.CONTENT_LIST_ID] = contentData?.id ? contentData?.id : contentData?.group_id
  payload[firebaseAnalytics.PROVIDER] = contentData?.proveedor_code ? contentData?.proveedor_code: contentData?.provider?.code
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.contentAttributes?.episode ? `${firebaseAnalytics.EPISODIO} ${contentData?.contentAttributes?.episode} ${contentData?.contentAttributes?.titleEpisode}` : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.contentAttributes?.season ? `${firebaseAnalytics.TEMPORADA} ${contentData?.contentAttributes?.season}` : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = searchData?.previous_path?.toLowerCase() || 'home'
  payload[firebaseAnalytics.PAGE_PATH] = firebaseAnalytics.SEARCH
  payload[firebaseAnalytics.PAGE_TITLE] = firebaseAnalytics.SEARCH
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = searchData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE

  logEvent(event_name, payload)
}

export const pushContentSelectionEvent = (
  userData,
  contentData,
  index,
  contentSelectionType
) => {
  let payload = {} 
  payload[firebaseAnalytics.SUSCRIPTIONS] = userData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_category
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpt
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = userData?.content_section?.toLowerCase()
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.CONTENT_LIST] = userData?.content_list
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.id ? contentData?.id : contentData?.group_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.title
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.is_series ? firebaseAnalytics.SERIES : firebaseAnalytics.MOVIE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.format_types
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_POSITION] = index
  payload[firebaseAnalytics.MODULO_NAME] = userData?.modulo_name
  payload[firebaseAnalytics.CONTENT_LIST_ID] = userData?.content_list_id
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.contentAttributes?.episode ? `${firebaseAnalytics.EPISODIO} ${contentData?.contentAttributes?.episode} ${contentData?.contentAttributes?.titleEpisode}` : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.contentAttributes?.season ? `${firebaseAnalytics.TEMPORADA} ${contentData?.contentAttributes?.season}` : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PROVIDER] = contentData?.proveedor_code ? contentData?.proveedor_code: contentData?.provider?.code
  payload[firebaseAnalytics.PAGE_PATH] = userData?.page_path
  payload[firebaseAnalytics.PAGE_TITLE] = userData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = userData?.previous_path?.toLowerCase() || 'home'
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = userData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE

  logEvent(contentSelectionType, payload)
}

export const pushSubscriptionEvent = (userData,contentData,index,module) => {
  let payload = {}  
  payload[firebaseAnalytics.SUSCRIPTIONS] = userData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_category
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = userData?.content_section?.toLowerCase()
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = contentData?.viewButton?.toLowerCase()
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.PROVIDER] = contentData?.producttype?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_POSITION] = index
  payload[firebaseAnalytics.ACTION_TYPE] = userData?.action_type
  payload[firebaseAnalytics.PREVIOUS_PATH] = userData?.previous_path
  payload[firebaseAnalytics.PAGE_PATH] = userData?.page_path
  payload[firebaseAnalytics.PAGE_TITLE] = userData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = userData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE

  logEvent(firebaseAnalytics.SELECT_PLAN, payload)
}
export const pushInteractionContentEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions || {})?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
    ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = data?.screenName ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = ((data?.prevScreenName?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase()) ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MODULO_NAME]= firebaseAnalytics.VCARD ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.INTERACTION_TYPE] = data?.interactionType ?? firebaseAnalytics.NOT_APPLICABLE
 
  payload[firebaseAnalytics.CONTENT_ID] = data?.contentData?.common?.id ?? firebaseAnalytics.NOT_APPLICABLE
  
  const contentTitle = data?.contentType == 'series' ?
  data?.contentData?.common?.extendedcommon?.media?.serie?.title :
  data?.contentData?.common?.title

  payload[firebaseAnalytics.CONTENT_NAME] = contentTitle?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_TYPE] = data?.contentType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = data?.contentData?.external?.gracenote?.genres?.length > 0
   ? data.contentData.external.gracenote.genres.map(item => item?.toLowerCase()).join(', ')
   : data?.contentData?.common?.extendedcommon?.genres?.genre?.length > 0
      ? data.contentData.common.extendedcommon.genres.genre.map(item => item.name?.toLowerCase()).join(', ')
      : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = data?.subscriptionInfo ? firebaseAnalytics.BY_SUBSCRIPTION : firebaseAnalytics.NOT_APPLICABLE

  let episodeNumber = data?.contentData?.common?.extendedcommon?.media?.episode?.number
   
  payload[firebaseAnalytics.CONTENT_EPISODE] = episodeNumber ? `episodio ${episodeNumber} ${data?.contentData?.common?.title?.toLowerCase()} ` :
   firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_PRICE] = data?.subscriptionInfo?.price?.toString().trim() || '0.00'
  payload[firebaseAnalytics.CONTENT_SEASON] = data?.contentData?.common?.extendedcommon?.media?.episode?.season ?
  `temporada ${data?.contentData?.common?.extendedcommon?.media?.episode?.season}`
   : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.PROVIDER] = data?.contentData?.universal_id?.content_providers[0]?.provider_code ?? firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.INTERACTION_CONTENT, payload)
}
export const pushAddSubscriptionEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.subscriptions && Object.keys(data?.subscriptions || {})?.map(key => key?.toLowerCase())?.length > 0
    ? Object.keys(data?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ')
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
    ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = data?.screenName ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = ((data?.prevScreenName?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase()) ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MODULO_NAME]= firebaseAnalytics.VCARD
  payload[firebaseAnalytics.INTERACTION_TYPE] = data?.interactionType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_ID] = data?.contentData?.common?.id ?? firebaseAnalytics.NOT_APPLICABLE
  const contentTitle = data?.contentType == 'series' ?
  data?.contentData?.common?.extendedcommon?.media?.serie?.title :
  data?.contentData?.common?.title

  payload[firebaseAnalytics.CONTENT_NAME] = contentTitle?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_TYPE] = data?.contentType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = data?.contentData?.external?.gracenote?.genres?.length > 0
    ? data.contentData.external.gracenote.genres.map(item => item?.toLowerCase()).join(', ')
    : data?.contentData?.common?.extendedcommon?.genres?.genre?.length > 0
      ? data.contentData.common.extendedcommon.genres.genre.map(item => item?.name?.toLowerCase()).join(', ')
      : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = data?.subscriptionInfo ? firebaseAnalytics.BY_SUBSCRIPTION : firebaseAnalytics.NOT_APPLICABLE

  let episodeNumber = data?.contentData?.common?.extendedcommon?.media?.episode?.number
    
  payload[firebaseAnalytics.CONTENT_EPISODE] = episodeNumber ? `episodio ${episodeNumber} ${data?.contentData?.common?.title?.toLowerCase()} ` :
   firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_PRICE] = data?.subscriptionInfo?.price?.toString().trim() || '0.00'
  payload[firebaseAnalytics.CONTENT_SEASON] = data?.contentData?.common?.extendedcommon?.media?.episode?.season ?
  `temporada ${data?.contentData?.common?.extendedcommon?.media?.episode?.season}`
   : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.PROVIDER] = data?.contentData?.universal_id?.content_providers[0]?.provider_code
  logEvent(firebaseAnalytics.ADD_SUBSCRIPTION, payload)
}
export const pushContentDetailsEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.suscriptions || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_PATH] = data?.contentSection?.toLowerCase() //((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
    //?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = data?.screenName ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = ((data?.prevScreenName?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase()) ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase()
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MODULO_NAME]= firebaseAnalytics.VCARD
  payload[firebaseAnalytics.CONTENT_ID] =  data?.contentData?.common?.id ?? firebaseAnalytics.NOT_APPLICABLE
  
  const contentTitle = data?.contentType == 'series' ?
  data?.contentData?.common?.extendedcommon?.media?.serie?.title :
  data?.contentData?.common?.title

  payload[firebaseAnalytics.CONTENT_NAME] = contentTitle?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_TYPE] = data?.contentType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = data?.contentData?.external?.gracenote?.genres?.length > 0
    ? data.contentData.external.gracenote.genres.map(item => item?.toLowerCase()).join(', ')
    : data?.contentData?.common?.extendedcommon?.genres?.genre?.length > 0
      ? data.contentData.common.extendedcommon.genres.genre.map(item => item?.name?.toLowerCase()).join(', ')
      : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = data?.subscriptionInfo ? firebaseAnalytics.BY_SUBSCRIPTION : firebaseAnalytics.NOT_APPLICABLE

  let episodeNumber = data?.contentData?.common?.extendedcommon?.media?.episode?.number
    
  payload[firebaseAnalytics.CONTENT_EPISODE] = episodeNumber ? `episodio ${episodeNumber} ${data?.contentData?.common?.title?.toLowerCase()} ` :
   firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_PRICE] = data?.subscriptionInfo?.price?.toString().trim() || '0.00'
  payload[firebaseAnalytics.PROVIDER] = data?.contentData?.universal_id?.content_providers[0]?.provider_code
  payload[firebaseAnalytics.CONTENT_SEASON] = data?.contentData?.common?.extendedcommon?.media?.episode?.season ?
    `temporada ${data?.contentData?.common?.extendedcommon?.media?.episode?.season}`
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  logEvent(firebaseAnalytics.VIEW_VCARD, payload)
}

export const pushScreenViewEvent =(data)=> {
  let payload = {}
  payload[firebaseAnalytics.PAGE_TITLE] = data?.screenName ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_PATH] =((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;

  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.SUSCRIPTIONS] = data?.screenData?.subscriptions && Object.keys(data?.screenData?.subscriptions || {})?.length > 0
    ? Object.keys(data?.screenData?.subscriptions)?.map(key => key?.toLowerCase())?.join(', ')
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.PREVIOUS_PATH] = ((data?.prevScreenName?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase()) ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  logEvent(firebaseAnalytics.SCREEN_VIEW, payload)
}

export const pushContentPlayEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = firebaseAnalytics.NOT_APPLICABLE //contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.INTERACTION_TYPE] = contentData?.interactionType || firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.CONTENT_PLAY, payload)
}
export const pushContentPauseEvent = contentData => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.CONTENT_PAUSE, payload)
}

export const pushContentRewindEvent = contentData => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.CONTENT_REWIND, payload)
}

export const pushContentForwardEvent = contentData => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.CONTENT_FORWARD, payload)
}

export const pushContentProgressEvent = (contentData, percentage) => {
  let payload = {}
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.PROGRESS_PERCENTAGE] = percentage
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.CONTENT_PROGRESS, payload)
} 

export const pushContentDelayEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list
  logEvent(firebaseAnalytics.CONTENT_DELAY, payload)
}

export const pushPlayerInteractionEvent = (contentData, interactionType) => {
  let payload = {}

  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.INTERACTION_TYPE] = interactionType
  payload[firebaseAnalytics.CHANNEL_NAME] = contentData?.channel_name
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.INTERACTION_PLAYER, payload)
}

export const pushNewInteractionContentEvent = (
  contentData,
  eventType,
  contentType,
  interactionType
) => {
  let payload = {}
  payload[firebaseAnalytics.INTERACTION_TYPE] = interactionType
  payload[firebaseAnalytics.CONTENT_TYPE] = contentType
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = typeof contentData?.content_category === 'string' ? contentData?.content_category?.toLowerCase() :
  contentData?.content_category?.length > 0 ? contentData?.content_category?.map(item => item?.toLowerCase()).join(', ') : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list,
  payload[firebaseAnalytics.CHANNEL_NAME] = contentData?.channel_name,
  payload[firebaseAnalytics.ACTION_TYPE] = contentData?.action_type
  payload[firebaseAnalytics.CONTENT_LIST_ID] = contentData?.content_list_id
  payload[firebaseAnalytics.PAGE_PATH] = ((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.PAGE_TITLE] = contentData?.page_title
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = contentData?.engagement_time_msec
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_PATH] = contentData?.previous_screen || firebaseAnalytics.NOT_APPLICABLE
  logEvent(eventType, payload)
}

export const pushCheckoutPaymentEvent = (interactionType, contentData, data, module) => {
  let payload = {}
  payload[firebaseAnalytics.INTERACTION_TYPE] = interactionType,
  payload[firebaseAnalytics.MODULO_NAME] = module,
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id,
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name,
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type,
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category,
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = contentData?.content_availability,
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode,
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price,
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season,
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider,
  payload[firebaseAnalytics.PAYMENT_TYPE] = contentData?.payment_type,
  payload[firebaseAnalytics.PAGE_TITLE] = data?.screenName,
  payload[firebaseAnalytics.PREVIOUS_PATH] = data?.prevScreenName,
  payload[firebaseAnalytics.PAGE_PATH] = (window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.SUSCRIPTIONS] =  data?.suscriptions,
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.content_section?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))   
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn,
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.engagement_time_msec,
  payload[firebaseAnalytics.SESSION_ID] = getSessionId()
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE 
  logEvent(firebaseAnalytics.CHECKOUT_PAYMENT, payload)
}

export const pushSuccessfulSubscriptionEvent = (interactionType, contentData, data,module, eventType) => {
  let payload = {}
  payload[firebaseAnalytics.INTERACTION_TYPE] = interactionType,
  payload[firebaseAnalytics.MODULO_NAME] = module,
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id,
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name,
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type,
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category,
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = contentData?.content_availability,
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode,
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price,
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season,
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider,
  payload[firebaseAnalytics.PAYMENT_TYPE] = contentData?.payment_type,
  payload[firebaseAnalytics.PAGE_TITLE] = data?.screenName,
  payload[firebaseAnalytics.PREVIOUS_PATH] = data?.prevScreenName,
  payload[firebaseAnalytics.PAGE_PATH] = (window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.SUSCRIPTIONS] =  data?.suscriptions,
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.content_section?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE //value as per GA document.
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))   
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn,
  payload[firebaseAnalytics.ENGAGEMENT_TIME_MSEC] = data?.engagement_time_msec,
  payload[firebaseAnalytics.SESSION_ID] = getSessionId(),
  payload[firebaseAnalytics.OS_VERSION] = localStorage.getItem('platformVersion') || firebaseAnalytics.NOT_APPLICABLE 
  logEvent(eventType, payload)
}
