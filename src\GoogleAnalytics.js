import * as firebaseAnalytics from './GoogleAnalyticsConstants'
import { COMMON_URL } from './utils/environment'

const measurementId = 'G-N7M15SPSVV'
const apiSecretKey = 'CfGNG9BnSnCqwQs5ws1VbQ'
// clientId is unique to identify an application
const clientId = '23fe0340a788bd5cab7d82b5d37b2c6e'
// logged-in userId

function getCountryCode(region) {
  return firebaseAnalytics.regionToCountryCode[region?.toLowerCase()] || ''
}
let userId = 'AMX_Smart_TV_Guest_User'
const URL = `https://www.google-analytics.com/mp/collect?api_secret=${apiSecretKey}&measurement_id=${measurementId}`

const logEvent = (event, payload) => {
  fetch(URL, {
    credentials: 'same-origin',
    method: 'POST',
    body: JSON.stringify({
      client_id: clientId,
      user_id: userId,
      non_personalized_ads: false,
      events: [
        {
          name: event,
          params: payload
        }
      ]
    })
  })
}

export const pushLandingEvent = interaction => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = 'landing'
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.LANDING, payload)
}

export const pushLoginEvent = (interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.INTERACTION_LOGIN, payload)
}

export const pushLoginErrorEvent = (error,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.ERROR_NAME] = error
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.ERROR_LOGIN, payload)
}

export const pushLoginSuccessEvent = data => {
  userId = data?.user_id
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.LOGIN, payload)
}

export const pushLoginPrivacyEvent = (interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module ?? "Nuestro aviso de privacidad ha cambiado, para continuar acepte los nuevos términos"
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.PRIVACY_NOTICE, payload)
}

export const pushLogoutEvent = (data,interaction,MenuButton,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = MenuButton ?? 'inicio'
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.LOGOUT, payload)
}

export const pushProfileEvent = (data,interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE_OS] = ''
  payload[firebaseAnalytics.OS_VERSION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.PROFILE_ADMIN, payload)
}

export const pushAddProfileEvent = (data,interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.PROFILE_ADMIN, payload)
}

export const pushDeleteProfileEvent = (data,interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.PROFILE_ADMIN, payload)
}

export const pushRegisterEvent = (interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.INTERACTION_SIGN_UP, payload)
}

export const pushRegisterSuccessEvent = data => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.SIGN_UP, payload)
}

export const pushRegisterCompleteEvent = (data,interaction) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = "cintillo azul"
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.INTERACTION_SELECT_PLAN, payload)
}

export const pushForgotPasswordEvent = (interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  logEvent(firebaseAnalytics.FORGOT_PASSWORD, payload)
}

export const pushForgotPasswordSuccessEvent = (interaction,module) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.INTERACTION_TYPE] = interaction
  payload[firebaseAnalytics.MODULO_NAME] = module
  logEvent(firebaseAnalytics.FORGOT_PASSWORD, payload)
}

export const pushMenuEvent = (data,menuName) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = data?.user_id ? firebaseAnalytics.LOGGED_IN : firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = menuName
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MENU_NAME] = menuName
  logEvent(firebaseAnalytics.MENU, payload)
}

export const pushMenuProfileEvent = (data,menuName,contentSection) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = firebaseAnalytics.LOGGED_IN
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = contentSection ?? 'inicio'
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MENU_NAME] = menuName
  logEvent(firebaseAnalytics.MENU_PROFILE, payload)
}

export const pushSubMenuEvent = (data,subMenuName,menuName) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.user_id
  payload[firebaseAnalytics.PARENT_ID] = data?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
  payload[firebaseAnalytics.USER_TYPE] = data?.user_id ? firebaseAnalytics.LOGGED_IN : firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_type
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MENU_NAME] = menuName
  payload[firebaseAnalytics.SUBMENU_NAME] = subMenuName
  logEvent(firebaseAnalytics.SUB_MENU, payload)
}

export const pushSearchEvent = searchData => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = searchData?.user_id
  payload[firebaseAnalytics.PARENT_ID] = searchData?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = searchData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = searchData?.user_type
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_category
  payload[firebaseAnalytics.DEVICE_MODEL] = COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = firebaseAnalytics.SEARCH
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.SEARCH_TERM] = searchData?.search_term
  payload[firebaseAnalytics.INTERACTION_TYPE] = firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.SEARCH, payload)
}

export const pushContentSelectionEvent = (
  userData,
  contentData,
  index,
  contentSelectionType
) => {
  let payload = {} 
  payload[firebaseAnalytics.USER_ID] = userData?.user_id
  payload[firebaseAnalytics.PARENT_ID] = userData?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = userData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = userData?.user_type
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_category
  payload[firebaseAnalytics.DEVICE_MODEL] = COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpt
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = userData?.content_section?.toLowerCase()
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.CONTENT_LIST] = userData?.content_list
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.id ? contentData?.id : contentData?.group_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.title
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.is_series ? firebaseAnalytics.SERIES : firebaseAnalytics.MOVIE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.format_types
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_POSITION] = index
  payload[firebaseAnalytics.MODULO_NAME] = userData?.modulo_name
  payload[firebaseAnalytics.CONTENT_LIST_ID] = userData?.content_list_id
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.contentAttributes?.episode ? `${firebaseAnalytics.EPISODIO} ${contentData?.contentAttributes?.episode} ${contentData?.contentAttributes?.titleEpisode}` : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.contentAttributes?.season ? `${firebaseAnalytics.TEMPORADA} ${contentData?.contentAttributes?.season}` : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PROVIDER] = contentData?.proveedor_code ? contentData?.proveedor_code: contentData?.provider?.code
  logEvent(firebaseAnalytics.CONTENT_SELECTION_HOME, payload)
}

export const pushSubscriptionEvent = (userData,contentData,index,module) => {
  let payload = {}  
  payload[firebaseAnalytics.USER_ID] = userData?.user_id
  payload[firebaseAnalytics.PARENT_ID] = userData?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL
  payload[firebaseAnalytics.SUSCRIPTIONS] = userData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = userData?.user_type
  payload[firebaseAnalytics.DEVICE] = COMMON_URL?.device_category
  payload[firebaseAnalytics.DEVICE_MODEL] =  COMMON_URL?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL?.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = userData?.content_section?.toLowerCase()
  payload[firebaseAnalytics.COUNTRY] = getCountryCode(localStorage.getItem('region')) 
  payload[firebaseAnalytics.INTERACTION_TYPE] = contentData?.viewButton?.toLowerCase()
  payload[firebaseAnalytics.MODULO_NAME] = module
  payload[firebaseAnalytics.PROVIDER] = contentData?.producttype?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_POSITION] = index
  payload[firebaseAnalytics.ACTION_TYPE] = userData?.action_type

  logEvent(firebaseAnalytics.SELECT_PLAN, payload)
}
export const pushInteractionContentEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.screenData?.user_id ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = data?.screenData?.parent_id ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).length > 0
    ? Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ') 
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = data?.screenData?.user_id ? firebaseAnalytics.LOGGED_IN : firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE_MODEL] = COMMON_URL.device_model ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL.device_name ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MODULO_NAME]= firebaseAnalytics.VCARD ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.INTERACTION_TYPE] = data?.interactionType ?? firebaseAnalytics.NOT_APPLICABLE
 
  payload[firebaseAnalytics.CONTENT_ID] = data?.contentData?.common?.id ?? firebaseAnalytics.NOT_APPLICABLE
  
  const contentTitle = data?.contentType == 'series' ?
  data?.contentData?.common?.extendedcommon?.media?.serie?.title :
  data?.contentData?.common?.title

  payload[firebaseAnalytics.CONTENT_NAME] = contentTitle?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_TYPE] = data?.contentType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = data?.contentData?.external?.gracenote?.genres?.length > 0
   ? data.contentData.external.gracenote.genres.map(item => item?.toLowerCase()).join(', ')
   : data?.contentData?.common?.extendedcommon?.genres?.genre?.length > 0
      ? data.contentData.common.extendedcommon.genres.genre.map(item => item.name?.toLowerCase()).join(', ')
      : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = data?.subscriptionInfo ? firebaseAnalytics.BY_SUBSCRIPTION : firebaseAnalytics.NOT_APPLICABLE

  let episodeNumber = data?.contentData?.common?.extendedcommon?.media?.episode?.number
   
  payload[firebaseAnalytics.CONTENT_EPISODE] = episodeNumber ? `episodio ${episodeNumber} ${data?.contentData?.common?.title?.toLowerCase()} ` :
   firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_PRICE] = data?.subscriptionInfo?.price?.toString().trim() || '0.00'
  payload[firebaseAnalytics.CONTENT_SEASON] = data?.contentData?.common?.extendedcommon?.media?.episode?.season ?
  `temporada ${data?.contentData?.common?.extendedcommon?.media?.episode?.season}`
   : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PROVIDER] = data?.contentData?.universal_id?.content_providers[0]?.provider_code ?? firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.INTERACTION_CONTENT, payload)
}
export const pushAddSubscriptionEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.screenData?.user_id 
  payload[firebaseAnalytics.PARENT_ID] = data?.screenData?.parent_id
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL ?? ''
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).length > 0
    ? Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ')
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = data?.screenData?.user_id ? firebaseAnalytics.LOGGED_IN : firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category
  payload[firebaseAnalytics.DEVICE_MODEL] = COMMON_URL.device_model 
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MODULO_NAME]= firebaseAnalytics.VCARD
  payload[firebaseAnalytics.INTERACTION_TYPE] = data?.interactionType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_ID] = data?.contentData?.common?.id ?? firebaseAnalytics.NOT_APPLICABLE
  const contentTitle = data?.contentType == 'series' ?
  data?.contentData?.common?.extendedcommon?.media?.serie?.title :
  data?.contentData?.common?.title

  payload[firebaseAnalytics.CONTENT_NAME] = contentTitle?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_TYPE] = data?.contentType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = data?.contentData?.external?.gracenote?.genres?.length > 0
    ? data.contentData.external.gracenote.genres.map(item => item?.toLowerCase()).join(', ')
    : data?.contentData?.common?.extendedcommon?.genres?.genre?.length > 0
      ? data.contentData.common.extendedcommon.genres.genre.map(item => item?.name?.toLowerCase()).join(', ')
      : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = data?.subscriptionInfo ? firebaseAnalytics.BY_SUBSCRIPTION : firebaseAnalytics.NOT_APPLICABLE

  let episodeNumber = data?.contentData?.common?.extendedcommon?.media?.episode?.number
    
  payload[firebaseAnalytics.CONTENT_EPISODE] = episodeNumber ? `episodio ${episodeNumber} ${data?.contentData?.common?.title?.toLowerCase()} ` :
   firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_PRICE] = data?.subscriptionInfo?.price?.toString().trim() || '0.00'
  payload[firebaseAnalytics.CONTENT_SEASON] = data?.contentData?.common?.extendedcommon?.media?.episode?.season ?
  `temporada ${data?.contentData?.common?.extendedcommon?.media?.episode?.season}`
   : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PROVIDER] = data?.contentData?.universal_id?.content_providers[0]?.provider_code
  logEvent(firebaseAnalytics.ADD_SUBSCRIPTION, payload)
}
export const pushContentDetailsEvent = (data) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = data?.screenData?.user_id ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PARENT_ID] = data?.screenData?.parent_id ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).length > 0
    ? Object.keys(data?.subscriptions || {}).map(key => key?.toLowerCase()).join(', ') 
    :firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.USER_TYPE] = data?.screenData?.user_id ? firebaseAnalytics.LOGGED_IN : firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category
  payload[firebaseAnalytics.DEVICE_MODEL] = COMMON_URL.device_model 
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase()
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.MODULO_NAME]= firebaseAnalytics.VCARD
  payload[firebaseAnalytics.CONTENT_ID] =  data?.contentData?.common?.id ?? firebaseAnalytics.NOT_APPLICABLE
  
  const contentTitle = data?.contentType == 'series' ?
  data?.contentData?.common?.extendedcommon?.media?.serie?.title :
  data?.contentData?.common?.title

  payload[firebaseAnalytics.CONTENT_NAME] = contentTitle?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_TYPE] = data?.contentType ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_CATEGORY] = data?.contentData?.external?.gracenote?.genres?.length > 0
    ? data.contentData.external.gracenote.genres.map(item => item?.toLowerCase()).join(', ')
    : data?.contentData?.common?.extendedcommon?.genres?.genre?.length > 0
      ? data.contentData.common.extendedcommon.genres.genre.map(item => item?.name?.toLowerCase()).join(', ')
      : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] = data?.subscriptionInfo ? firebaseAnalytics.BY_SUBSCRIPTION : firebaseAnalytics.NOT_APPLICABLE

  let episodeNumber = data?.contentData?.common?.extendedcommon?.media?.episode?.number
    
  payload[firebaseAnalytics.CONTENT_EPISODE] = episodeNumber ? `episodio ${episodeNumber} ${data?.contentData?.common?.title?.toLowerCase()} ` :
   firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_PRICE] = data?.subscriptionInfo?.price?.toString().trim() || '0.00'
  payload[firebaseAnalytics.PROVIDER] = data?.contentData?.universal_id?.content_providers[0]?.provider_code
  payload[firebaseAnalytics.CONTENT_SEASON] = data?.contentData?.common?.extendedcommon?.media?.episode?.season ?
    `temporada ${data?.contentData?.common?.extendedcommon?.media?.episode?.season}`
    : firebaseAnalytics.NOT_APPLICABLE
  logEvent(firebaseAnalytics.VIEW_VCARD, payload)
}

export const pushScreenViewEvent =(data)=> {
  let payload = {}
  payload[firebaseAnalytics.SCREEN_NAME] = data?.screenName ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SCREEN_CLASS] =((window.location?.hash?.split('#')[1]?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase())
   ?? firebaseAnalytics.NOT_APPLICABLE;
  payload[firebaseAnalytics.USER_TYPE] = data?.screenData?.user_id ?
   firebaseAnalytics.LOGGED_IN : firebaseAnalytics.GUEST_PERSON
  payload[firebaseAnalytics.USER_ID] = data?.screenData?.user_id ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SECTION] = data?.contentSection?.toLowerCase() ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.COUNTRY] = data?.screenData?.country_code ?? getCountryCode(localStorage.getItem('region'))
  payload[firebaseAnalytics.PARENT_ID] = data?.screenData?.parent_id ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SIGN_UP_METHOD] = firebaseAnalytics.EMAIL ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.SUSCRIPTIONS] = Object.keys(data?.screenData?.subscriptions || {}).length > 0
    ? Object.keys(data.screenData.subscriptions).map(key => key?.toLowerCase()).join(', ')
    : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PAGE_URL] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.PREVIOUS_CLASS] = ((data?.prevScreenName?.split('/')?.filter(str => str !== '') || []).pop()?.toLowerCase()) ?? firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.DEVICE] = COMMON_URL.device_category
  payload[firebaseAnalytics.DEVICE_MODEL] = COMMON_URL.device_model 
  payload[firebaseAnalytics.DEVICE_NAME] = COMMON_URL.device_name
  payload[firebaseAnalytics.AUTHPN] = COMMON_URL.authpn
  logEvent(firebaseAnalytics.SCREEN_VIEW, payload)
}

export const pushContentPlayEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  logEvent(firebaseAnalytics.CONTENT_PLAY, payload)
}
export const pushContentPauseEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  logEvent(firebaseAnalytics.CONTENT_PAUSE, payload)
}

export const pushContentRewindEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list
  logEvent(firebaseAnalytics.CONTENT_REWIND, payload)
}

export const pushContentForwardEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list
  logEvent(firebaseAnalytics.CONTENT_FORWARD, payload)
}

export const pushContentProgressEvent = (contentData, percentage) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.PROGRESS_PERCENTAGE] = percentage
  logEvent(firebaseAnalytics.CONTENT_PROGRESS, payload)
} 

export const pushContentDelayEvent = contentData => {
  let payload = {}
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list
  logEvent(firebaseAnalytics.CONTENT_DELAY, payload)
}

export const pushPlayerInteractionEvent = (contentData, interactionType) => {
  let payload = {}
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = contentData?.content_category?.toLowerCase()
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.INTERACTION_TYPE] = interactionType
  payload[firebaseAnalytics.CHANNEL_NAME] = contentData?.channel_name
  logEvent(firebaseAnalytics.INTERACTION_PLAYER, payload)
}

export const pushNewInteractionContentEvent = (
  contentData,
  eventType,
  contentType,
  interactionType
) => {
  let payload = {}
  payload[firebaseAnalytics.INTERACTION_TYPE] = interactionType
  payload[firebaseAnalytics.CONTENT_TYPE] = contentType
  payload[firebaseAnalytics.USER_ID] = contentData?.user_id || userId
  payload[firebaseAnalytics.PARENT_ID] = contentData?.parent_id || userId
  payload[firebaseAnalytics.SIGN_UP_METHOD] = contentData?.sign_up_method
  payload[firebaseAnalytics.SUSCRIPTIONS] = contentData?.suscriptions
  payload[firebaseAnalytics.USER_TYPE] = contentData?.user_type
  payload[firebaseAnalytics.DEVICE] = contentData?.device
  payload[firebaseAnalytics.DEVICE_MODEL] =  contentData?.device_model
  payload[firebaseAnalytics.DEVICE_NAME] = contentData?.device_name
  payload[firebaseAnalytics.AUTHPN] = contentData?.authpn
  payload[firebaseAnalytics.CONTENT_SUBSECTION] = contentData?.content_subsection
  payload[firebaseAnalytics.CONTENT_SECTION] = contentData?.content_section
  payload[firebaseAnalytics.COUNTRY] = contentData?.country
  payload[firebaseAnalytics.CONTENT_ID] = contentData?.content_id
  payload[firebaseAnalytics.CONTENT_NAME] = contentData?.content_name
  payload[firebaseAnalytics.CONTENT_TYPE] = contentData?.content_type
  payload[firebaseAnalytics.CONTENT_CATEGORY] = typeof contentData?.content_category === 'string' ? contentData?.content_category?.toLowerCase() :
  contentData?.content_category?.length > 0 ? contentData?.content_category?.map(item => item?.toLowerCase()).join(', ') : firebaseAnalytics.NOT_APPLICABLE
  payload[firebaseAnalytics.CONTENT_AVAILABILITY] =
    contentData?.content_availability
  payload[firebaseAnalytics.CONTENT_EPISODE] = contentData?.content_episode
  payload[firebaseAnalytics.MODULO_NAME] = contentData?.modulo_name
  payload[firebaseAnalytics.CONTENT_PRICE] = contentData?.content_price
  payload[firebaseAnalytics.CONTENT_SEASON] = contentData?.content_season
  payload[firebaseAnalytics.PROVIDER] = contentData?.provider
  payload[firebaseAnalytics.CONTENT_LIST] = contentData?.content_list,
  payload[firebaseAnalytics.CHANNEL_NAME] = contentData?.channel_name,
  payload[firebaseAnalytics.ACTION_TYPE] = contentData?.action_type
  payload[firebaseAnalytics.CONTENT_LIST_ID] = contentData?.content_list_id
  logEvent(eventType, payload)
}

