import React, { useCallback, useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import './Banner.scss'
import { getBannerData, getPremiumNodeValue } from '../../store/slices/HomeSlice'
import { useNavigate } from 'react-router-dom'
import { getSubscriptionInfo, getContractBackHandle } from '../../store/slices/settingsSlice'
import { FixedSizeList as List } from 'react-window'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { pushContentSelectionEvent } from '../../GoogleAnalytics'
import { contentSelectionType } from '../../GoogleAnalyticsConstants'
import { getNavBarClicked } from '../../store/slices/login'
import { getChannelData } from '../../store/slices/PlayerSlice'
import { getTabvalue } from '../../store/slices/subMenuDataSlice'

const Banner = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const bannerData = useSelector(
    state => state?.homeReducer?.bannerHightlight?.response?.highlight
  )
  const premiumTabValue = useSelector(
    state => state?.homeReducer?.premiumCardFocus
  )
  const sliderData =
    props?.premium && bannerData ? bannerData : props.dataObject?.highlight
  const registerPopupdata = useSelector(state => state?.login?.registerPopup)
  const [bannerArr, setBannerArr] = useState([])
  const bannerRef = useRef()
  const activeElement = useRef(1)
  const isBannerFocused = useRef(false)

  const payWayToken = useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq)
  const navBarClicked = useSelector(state => state?.login?.navBarVariable)
  const getSettingsVariable = useSelector(state => state?.login?.settingClicked)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const premiumsubmenutab = useSelector(state => state?.SubMenuFilter?.tabvalue)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)  
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
  const region = localStorage.getItem('region')
  const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc
  const epgChannel = epgSevenDaysData && epgSevenDaysData[1]?.channelResponse
  const navIndex = localStorage.getItem('currNavIdx')
  localStorage.setItem('bannerIndex',props?.index)

  const handleKeyDown = event=> {
    if (event.key === 'ArrowDown' || event.keyCode == 40) {
      dispatch(getNavBarClicked(false))
    }
    if(event.key == 'ArrowUp' || event.keyCode == 38) {
      document.getElementById(`nav-${navIndex}`)?.focus()
    }
  }

  const getIsContractChanel = item => {
    const foundContract = payWayToken?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return true
    }
    return false
  }

  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
    const userData = {
      user_id: userDetails?.user_id,
      parent_id: userDetails?.parent_id,
      suscriptions: userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key => userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
      user_type: userDetails ? 'registrado' : 'anonimo',
      content_section : navbarTab,
      content_list : 'superdestacado',
      modulo_name : 'carrusel superdestacado',
      content_list_id : 'highlight'
    }
    pushContentSelectionEvent(userData, item, index, contentSelectionType.HOME_START)
    if (item?.is_series) {
      dispatch(getSubscriptionInfo({}))
      navigate('/series', { state: { data: item } })
    } else if (item?.live_enabled === '1') {
      if (!getIsContractChanel(item?.id)) {
        dispatch(getContractBackHandle({ backPage: '/home' }))
      }
      dispatch(
        getChannelData({
          group_id: item?.id ?? item?.group_id,
          timeshift: item?.timeshift,
          switchChannel: 'yes',
          epgIndex: epgChannel?.findIndex(
            itrObj => itrObj.group_id == (item?.id ?? item?.group_id)
          )
        })
      ),
        navigate('/livePlayer', {
          state: { showControls: 'live', data: item }, //Added showControls flag which will enable live player after navigation
          replace: true
        })
    } else if (item?.type === 'node') {
      dispatch(getPremiumNodeValue(item?.section))
      dispatch(getTabvalue(item?.section))
    }
     else {
      navigate('/movies', { state: { vodData: item } })
    }
  }

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon]
  }

  useEffect(() => {
    if (props?.premium) {
      dispatch(getBannerData(props?.dataObject))
    }
  }, [props?.premium, props?.dataObject])

  // Update bannerArr based on sliderData
  useEffect(() => {
    if (sliderData?.length > 0) {
      let sliderList = JSON.parse(JSON.stringify(sliderData))
      sliderList.unshift(sliderList[sliderList.length - 1]) // Add the last item to the beginning
      sliderList.push(sliderList[1]) // Add the first item to the end
      setBannerArr(sliderList)
    }
  }, [sliderData])

  const carouselInterval = useRef(null)

  const startCarousel = useCallback(() => {
    if (bannerArr && bannerArr.length > 0) {
      bannerRef?.current?.scrollToItem(1, 'center')
        premiumsubmenutab && document.getElementById(`carousel_id-1`)?.focus()
      if (activeElement.current < bannerArr?.length - 1) {
        carouselInterval.current = setInterval(() => {
          if (isBannerFocused.current == true) {
            bannerRef?.current?.scrollToItem(activeElement.current, 'center')
            document.getElementById(`carousel_id-${activeElement.current}`)?.focus()
          }
          else {
            bannerRef?.current?.scrollToItem(activeElement.current, 'center')
          }
          activeElement.current += 1
          if (activeElement.current == bannerArr?.length - 1) {
            activeElement.current = 1
          }
        }, 5000)
      }
    }
  }, [bannerArr])

  const handleScroll = ({ scrollOffset }) => {
    const validScrollOffset = scrollOffset != null ? scrollOffset : 0
    const index = Math.round(validScrollOffset / 1788)

    if (index != null) {
      activeElement.current = index
    }
  }

  useEffect(() => {
    startCarousel()
    return () => {
      if (carouselInterval.current) {
        clearInterval(carouselInterval.current)
      }
    }
  }, [bannerArr, startCarousel])


  useEffect(() => {
    if (bannerArr && bannerArr.length > 0) {
      bannerRef?.current?.scrollToItem(1, 'center')
      // Condition to handle both new profile and same profile cases
      if ((premiumsubmenutab) ||
        (premiumsubmenutab && document.getElementById('carousel_id-1'))) {
          document.getElementById('carousel_id-1')?.focus()
      }
    }
  }, [bannerArr, premiumsubmenutab])

  const listData = useCallback(
    ({ style, index }) => {
      const imageSource = Addproveedor(providerLabel?.[bannerArr?.[index]?.proveedor_code]?.susc)
      return (
        <button
          id={`carousel_id-${index}`}
          key={index}
          tabIndex={1}
          onClick={() => goToMoviesSeries(bannerArr?.[index],index)}
          onKeyDown={handleKeyDown}
          style={{ ...style, left: index * 1744 + index * 45 }}
          className={
            index == 0 || index == bannerArr?.length - 1
              ? 'bannerFocus'
              : 'bannerFocus focusable'
          }
          data-sn-left={
            index == 1 ? `#carousel_id-${bannerArr?.length - 2}` : undefined
          }
          data-sn-right={
            index == bannerArr?.length - 2 ? `#carousel_id-1` : undefined
          }
          onFocus={() => {
            registerPopupdata
              ? (document.getElementById('id-railsHome-regpopup').scrollTop = 0)
              : (document.getElementById('id-railsHome').scrollTop = 0)
            isBannerFocused.current = true
            bannerRef?.current?.scrollToItem(index, 'center')
          }}
          onBlur={() => (isBannerFocused.current = false)}
        >
          {bannerArr?.[index]?.image_highlight ? (
            <LazyLoadImage
              src={bannerArr?.[index]?.image_highlight}
              key={index}
              placeholderSrc="images/carousel_placeholder.png"
              height={bannerArr?.[index]?.highlight}
              className="img"
              loading="lazy"
              alt="image_highlight"
            />
          ) : (
            <LazyLoadImage
              src={'images/carousel_placeholder.png'}
              dataSnRight={true}
              style={{ height: '400px', width: '100%' }}
              dataSnDown="#railfocus0"
              alt="banner-placeholder"
            />
          )}

          {bannerArr?.[index]?.proveedor_code == 'amco' ? (
            bannerArr?.[index]?.format_types === 'ppe,download' ? (
              <div className="prov-block-rail-banner ">
                <img src={'images/Alquilar.svg'} className="alquilar-tag" />
              </div>
            ) : bannerArr?.[index]?.format_types === 'ppe' ? (
              <div className="prov-block-rail-banner-alquilar">
                <img src={'images/Alquilar.svg'} className="tag" />
              </div>
            )
              :
              bannerArr?.[index]?.format_types === 'ppe,est' ? (
                <>
                  <div className="comprar-tag-banner" style={bannerArr?.[index]?.is_series ? { backgroundColor: '#40336F' } : null}>COMPRAR</div>
                  <div className="proveedor-block-rail-alq-comp-banner">
                    <img
                      src={'images/Alquilar.svg'}
                      className="tagAlq"
                    />
                  </div>
                </>
              )
                :
                bannerArr?.[index]?.format_types === 'est' &&
                <div className="comprar-tag-banner" style={bannerArr?.[index]?.is_series ? { backgroundColor: '#40336F' } : null}>COMPRAR</div>
          ) : bannerArr?.[index]?.proveedor_code &&
            bannerArr?.[index]?.image_medium ? (
            <div className="prov-block-rail-banner-paramount">
              <div className="par-tag">
                <img
                  id="#icon1"
                  src={imageSource}
                  style={!imageSource ? { width: 0, height: 0 } : {}}
                  className={
                    bannerArr?.[index]?.proveedor_code === 'picardia2'
                      ? 'picardia-image-banner'
                      : 'paramount-image'
                  }
                />
              </div>
              {bannerArr?.[index]?.format_types === 'free' && userDetails?.subscriptions?.length == 0 ? (
                <div className="prov-block-rail-verohara1">
                  <div className="verahora-tag1">VER AHORA</div>
                </div>
              ) : null}
              {bannerArr?.[index]?.proveedor_code === 'picardia2' &&
                bannerArr?.[index]?.image_medium && (
                  <div className="prov-block-rail-banner-Adultus">
                    <img src={'images/Adultus.svg'} className="banner-adult-tag" />
                  </div>
                )}
            </div>
          ) : null}
        </button>
      )
    },
    [bannerArr, registerPopupdata]
  )

  return (
    <div className="banner-list-container">
      <List
        ref={bannerRef}
        height={500}
        width={1920}
        marginBottom={10}
        itemSize={1788}
        itemCount={bannerArr?.length}
        layout="horizontal"
        className="Caraousel_CSS"
        overscanCount={bannerArr?.length}
        onScroll={handleScroll}
      >
        {listData}
      </List>
    </div>
  )
}

export default React.memo(Banner)
