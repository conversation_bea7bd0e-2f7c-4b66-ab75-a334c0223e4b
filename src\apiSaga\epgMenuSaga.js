import { call, takeEvery } from 'redux-saga/effects'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getEpgMenuError,
  getEpgMenuSuccess
} from '../store/slices/EpgMenuSlice'

function* workGetEpgMenu() {
  try {
    yield call(
      request,
      URL.EPG_MENU_URL,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getEpgMenuSuccess(response))
        },
        onError(error) {
          store.dispatch(getEpgMenuError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(getEpgMenuError(error))
  }
}

export default function* epgMenuSaga() {
  yield takeEvery('epgMenu/getEpgMenu', workGetEpgMenu)
}
