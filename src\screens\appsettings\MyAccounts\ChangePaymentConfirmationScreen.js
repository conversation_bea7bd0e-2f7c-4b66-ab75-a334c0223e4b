import React, { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './AddTelephoneViaWeb.scss'

const ChangePaymentConfirmationScreen = () => {
  const navigate = useNavigate()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleAcceptarButtonClick = () => {
    navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new', {
      state: {
        pageName: state?.previousPageName,
        dataId: state?.dataId,
        vodData: state?.vodData,
        selectedPaymentIndex: state?.index
      }
    })
  }

  useEffect(() => {
    setTimeout(() => {
      handleAcceptarButtonClick()
    }, 3000)
  }, [])

  return (
    <div className="app-css-confirmation-screen">
      <div className="confirmation-screen-logo-container">
        <img className="confirmation-screen-logo" src={'images/logo.png'} />
      </div>

      <div className="confirmation-screen-main-div">
        <img
          className="green-tick-img"
          src={'images/greenTick.png'}
          alt="greenTickLogo"
        />
        <span className="confirmation-screen-text">
          {truncateText('defaultPaymentMethod_update_description_label', 30)}
        </span>
        <button
          className="confirmation-screen-acceptar-button"
          id="confirmationScreenAcceptButtonID"
          autoFocus
          onClick={handleAcceptarButtonClick}
        >
          {truncateText('defaultPaymentMethod_update_option_button_close', 30)}
        </button>
      </div>
    </div>
  )
}

export default React.memo(ChangePaymentConfirmationScreen)
