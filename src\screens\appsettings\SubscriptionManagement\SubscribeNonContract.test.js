import React from "react";
import { fireEvent, getByText, queryByAttribute,render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter, BrowserRouter as Router } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import SubscriptionContract from "./SubscribeNonContract";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
    <MemoryRouter
        history={history}
        initialEntries={[{ state: { subscribeInfoData: {data:{proveedor_code:'amco'}} } }]}
    >
        {children}
    </MemoryRouter>
</Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mockerrorresponse = {
    "status": "1"
}

const mocksuccessresponse = {
    bannerUrl:"https://clarovideocdn8.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/banner_hbo.png?**********",
    logo:"https://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/transactional_hbo_logo.png?**********",
    family:"hbo",
    periodicity:"month",
    price:"29.90",
    currency:"S/",
    styles:"style_hbo",
    workflowStart:"/services/payway/workflowstart?object_type=A&offer_id=14330299&suscription_id=2540749&device_category=STB&device_manufacturer=ZTE&device_model=androidTV&device_type=ptv&HKS=%28ZTEATV412001224226597abbede97c%29&region=peru&user_id=********",
    taxLabel:"Incl. IGV",
    subscribeButton:"SUSCRIBÍTE AHORA",
    viewButton:"¿Qué incluye HBO?",
    producttype:"HBO"
    
}

const mockIsLoggedinsuccessresponse = {
response:{
    accepted_terms: 1,
    admin: true,
    city: null,
    counterValidEmail: 0,
    country_code: "PE",
    email: "<EMAIL>",
    firstname: "Admin",
    gamification_id: "63ecf870de7e873ec21ca6c3",
    hasSavedPayway: 1,
    hasUserSusc: 1,
    is_kids: "false",
    is_user_logged_in: 1,
    lastname: "testing",
    lasttouch: {favorited: "657fe455ec4fd", profile: "657fe44c2a6be", purchased: "657fd244a1fab"},
    parent_id: "********",
    password_recovered: false,
    paymentMethods: {hubgate: true},
    region: "peru",
    session_parametername: "HKS",
    session_servername: "************",
    session_stringvalue: "ZTEATV412001224226580292a69e33",
    session_userhash: "************************************************************************************************",
    socialNetworks: [{id_usuario: "********", id_usuario_social: "63ecf870de7e873ec21ca6c3", redsocial: "IMUSICA"}],
    socialdata: null,
    subregion:null,
    subscriptions: {TV_EN_VIVO: true, MGM: true, AMCO: true},
    superhighlight: ["no_suscripto_nbatv", "no_suscripto_rtveplay", "susc", "no_suscripto_fox_sports", "no_suscripto_hbo"],
    user_id: "********",
    user_session: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I9OXS-jJomO5m_USqjIlt8XGd9Zzyypkdh2zFgZ9Jeg",
    user_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.85CTxu2wUyWObpGgpBiIEcGWwdGeGonriZotenfEtOk",
    username: "<EMAIL>",
    validEmail: true
},
status: "0"
}

const mockPaywayResponse = {
response:{
    list:[
        {
            gateway:"hubgate",
            gatewaytext:"Línea móvil Claro",
            buyLink:"/services/payway/paymentservice/buyconfirm/api_version/v7.2?payway=hubgate®ion=peru&device_category=STB&device_manufacturer=ZTE&device_model=androidTV&device_type=ptv&HKS=%28ZTEATV412001224226597abbede97c%29&user_id=********",
            hubCorpEnabled:true,
            product_id:"********",
            accessCode:false,
            msgAccessCode:"Recuerda que puedes proteger tus alquileres configurando un PIN desde MI CUENTA en web.",
            producttype:"HBO",
            buyToken:"Q1pNaE9jWG5LYzNtN0RWV2hQc1NhM1RlSitwZE0rK1NVSFZhR1NRMTRPSlRTbTVJOS9aZ1Q0Q0lSZjFMQVdsQzhMK21WbVlsQVNZZjlqRkRwQjd6N1ZLY0UxUTN1cHFOSnl1VUsrUWpUaDRRQzJOSkY1MU9kQkhEVk9MaURTT0NySWs5NE5hbHlVV1lEUGpHK3VQK3lNdEpBN2dhVi9POGt5K3dmMzFuWkE3RHVmVElzY3NRelF0RHB4LzZpY2diZWJZVlBjMnpkQlpaUVNacm5iQ29Od21aTzF5VnZURjlSYmVxUU14SUtlOWM1MjJvNTA5bUJ2cXlnbFpxYUt5MFhLTVFrRTF3M0xJUlBYQUlUWm1naTUzazAxZz0=",
            paymentMethodData:{
                account:null
            }
        },
        {
            gateway:"promogate",
            gatewaytext:"Código promocional",
            buyLink:"/services/payway/confirm?buylink=Q1pNaE9jWG5LYzNtN0RWV2hQc1NhbHVtQkhZQUpEU2RYME5VQVdzR2M3SU1Ud1ozSmV0UWxXSXo2d05hT2VWbUFjcUt6ejByTkZxRTRwVXhuVFJvaWhZTU1aVmZBVDBnOWxEL3pLYks1YkllY3l6aXk5MTVaTmRmWHBHRTdacy9OWjZqQzIwMmU0TmRjTnNwTnl6Tk96K0xwSEQ5a2hTRC9QcldtODZLSVhFOWgzdzk5NmVkZVBYTk94Nm1wM2syWU9kMEIwUnBVRU82NWk3c2V3RHBtb2k3bUxuMS9KdWJZNXUxMmRocnJ3Z0hkRTR6eUZLYU54M1grSmRqNW9kSVpwYzc0RnkzUDRxbTdNVWV6QT09&payway=promogate®ion=peru&device_category=STB&device_manufacturer=ZTE&device_model=androidTV&device_type=ptv&HKS=%28ZTEATV412001224226597abbede97c%29&user_id=********",
            hubCorpEnabled:false,
            product_id:null,
            accessCode:false,
            msgAccessCode:"Recuerda que puedes proteger tus alquileres configurando un PIN desde MI CUENTA en web.",
            producttype:"HBO",
            paymentMethodData:{
                account:null
            }
        }
    ],
    selectedPaymentMethod:'hubgate',
    hasSavedPayway:"0",
    hasUserSusc:"0",
    newWorkflow:"0"
}
}


describe('Landing page test', () => {

	test('should render data', () => {
		initialState.settingsReducer = {
            viewSubscribeData: mocksuccessresponse,
            paywayWorkFlow: mockPaywayResponse
}
        initialState.login = {
            isLoggedIn: mockIsLoggedinsuccessresponse
}
		
		renderWithState(<SubscriptionContract />)
	})
    test('cancel from contract screen', () => {
        initialState.login = {
            isLoggedIn: mocksuccessresponse
        }
        const { container } = renderWithState(<SubscriptionContract />)
        const getById = queryByAttribute.bind(null, 'id');
        const buttonClick = getById(container, 'cancel-button')
        fireEvent.keyUp(buttonClick,{keyCode: '405'})
        fireEvent.focus(buttonClick)
        fireEvent(
            buttonClick,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        
    })
    test('view details screen', () => {
        initialState.login = {
            isLoggedIn: mocksuccessresponse
        }
        const { container } = renderWithState(<SubscriptionContract />)
        const getById = queryByAttribute.bind(null, 'id');
        const buttonClick = getById(container, 'details-button')
        fireEvent.focus(buttonClick)
        fireEvent(
            buttonClick,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        
    })
})