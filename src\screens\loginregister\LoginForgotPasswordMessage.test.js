import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import LoginForgotPasswordMessage from "./LoginForgotPasswordMessage";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
);
export const renderWithState = (ui) => {
  return render(ui, { wrapper: Wrapper });
};

describe('LoginForgotPasswordMessage page test', () => {
  test('it should render the LoginForgotPasswordMessage Page onclick signin button', () => {
    const { container } = renderWithState(<LoginForgotPasswordMessage />)
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'signinid');
    fireEvent(
      scroll,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })
})