.shortcut-menu-bar {
  $item-width: 1387px;
  $item-height: 100px;
  height: $item-height;
  width: $item-width;
  opacity: 0.7;
  border-radius: 12px 12px 0 0;
  background-color: #000000;
  display: flex;
  flex-direction: row;
  justify-content: center;
  position: absolute;
  bottom: 0;
  z-index: 1;
  margin: 0 275px;
}

.shortcut-menu-bar-no-disponible {
  $item-width: 487px;
  $item-height: 100px;
  height: $item-height;
  width: $item-width;
  opacity: 0.7;
  border-radius: 12px 12px 0 0;
  background-color: #000000;
  display: flex;
  flex-direction: row;
  justify-content: center;
  position: absolute;
  bottom: 0;
  z-index: 1;
  margin: 0 717px;
}

.shortcut-bar-text {
  height: 28px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 26px;
  margin: 0 20px;
  opacity: 1;
}

.epg-shortcut-bar {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
}

.shortcut-bar-icon {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  align-self: center;
}

.player-shortcut-bar-text {
  height: 34px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 29px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 31px;
  align-self: center;
}

.shortcut-bar-icon-red {
  background-color: #f80000;
}

.shortcut-bar-icon-blue {
  background-color: #008eff;
  margin-right: 20px;
}

.shortcut-bar-icon-green {
  background-color: #00af1d;
}

.shortcut-bar-icon-yellow {
  background-color: #ffe100;
}

.player-shortcut-menu-bar {
  display: flex;
  justify-content: center;
  position: absolute;
  height: 48px;
  width: 1920px;
  bottom: 32px;

  .green {
    width: 173px;
  }
  
  .green:focus{
    height: 55px;
    width: 204px;
  }

  .blue {
    min-width: 308px;
    padding: 0 20px;
  }

  .yellow {
    width: 234px;
  }

  .red {
    width: 377px;
  }
}

.player-shortcut-bar {
  display: flex;
  justify-content: space-evenly;
  background: #2e303d;
  height: 48px;
  border-radius: 8px;
  margin: 0 16px;
  padding: 0 10px;
  &:focus {
    transform: translateY(-4px);
    outline: none;
    height: 55px;
  }
}
