import React, { useCallback, useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  getNavValue,
  getPremiumValueStateClear,
  getNavTabValue,
  getPremiumCardFocus,
  getClearStatePremiumCard,
  getContentMenu,
} from '../../store/slices/HomeSlice'
import './Navbar.scss'
import './Home.scss'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  clearSubmenuCard,
  getclickFilterrail,
  getTabvalue,
  subMenuRailCardsdata,
  subMenuRailIndex
} from '../../store/slices/subMenuDataSlice'
import { store } from '../../store/sagaStore'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import UserConfirmationModal from './UserConfirmationModal'
import {
  getNavBarClicked,
  getRegisterPopup,
  getSettingsClicked
} from '../../store/slices/login'
import { setShowTalentModule } from '../../store/slices/SearchSlice'
import { pushMenuEvent } from '../../GoogleAnalytics'

const Navbar = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { state } = useLocation()
  const disableFocus = props?.disableFocus 
  const userId = localStorage.getItem('userId')
  const startTimeRef = useRef(null)

  const [navFocus, setNavFocus] = useState(false)
  const [searchFocus, setSearchFocus] = useState(
    localStorage.getItem('currNavIdx') == -1 ? true : false
  )
  const [userConfirmationEnable, setUserConfirmationEnable] = useState(false)
  const [profileActive, setProfileActive] = useState(false)
  const [navFocusactive, setNavFocusactive] = useState(
    localStorage.getItem('currNavIdx') ?? 0
  )
  const [settingfocus, setSettingfocus] = useState(
    localStorage.getItem('currNavIdx') == -1 ? 'search' : ''
  )

  const submenu = useSelector(state => state?.SubMenuFilter?.submenudata)
  const hidedata = useSelector(state => state?.SubMenuFilter?.filterclickdata)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const userProfileData = useSelector(state => state?.profile?.userProfile)
  const premiumTabValue = useSelector(
    state => state?.homeReducer?.premiumNodeValue
  )
  const nav = useSelector(state => state?.homeReducer?.navbarData)
  const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const blueBand = useSelector(state => state?.login?.registerPopup)
  const showTalentModule = useSelector(state => state?.search?.showTalentModule)
  const storeFirstIndex = useSelector(state => state?.homeReducer?.storeFirstIndex)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)     
  const navData = userDetails?.is_kids === 'true' ? navKids : nav
  const navScrollRef = useRef()
  const showTalentModuleRef = useRef(showTalentModule)
  const searchIcon = 'images/search2.png'
  const [isMounted, setIsMounted] = useState(false)
  const firstButtonRef = useRef([]) // Ref for the first button
  const mycontentdata = localStorage.getItem('miscontenidos')
  const tabDetails = (code_text1, index, page_name) => {
    props?.page !== 'home' ? navigate('/home') : null
    store.dispatch(getNavTabValue(code_text1))
    store.dispatch(subMenuRailCardsdata(false))
    dispatch(getRegisterPopup(false))
    // store the state value
    store.dispatch(getTabvalue(''))
    store.dispatch(getNavValue(page_name))

    setNavFocusactive(index)
    if (code_text1 != 'guia') {
      localStorage.setItem('currNavIdx', index)
    } else if (localStorage.getItem('currNavIdx')) {
      localStorage.removeItem('currNavIdx')
    }
    if (localStorage.getItem('searchValue')) {
      localStorage.removeItem('searchValue')
    }
    setSearchFocus(false)
    code_text1 == 'miscontenidos'
      ? localStorage.setItem('miscontenidos', true)
      : code_text1 == 'micontenido_kids'
        ? localStorage.setItem('miscontenidos', true)
        : localStorage.removeItem('miscontenidos')
    dispatch(getPremiumValueStateClear())
    dispatch(clearSubmenuCard())
    if (code_text1 == 'homeuser' ) {
        document.getElementById('nav-0')?.focus()
      }
  }

  const selectedFocus = (code_text1, index, page_name) => {
    const engagement_time_msec = Date.now() - startTimeRef.current
    const navPage = nav?.[index]?.page?.toLowerCase() || ''
    store.dispatch(getNavBarClicked(true))
    dispatch(getPremiumCardFocus(null))
    dispatch(getContentMenu(code_text1))
    tabDetails(code_text1, index, page_name)
    //GA for menu event
    pushMenuEvent(userDetails,navPage,engagement_time_msec)
    startTimeRef.current = Date.now()
  }

  useEffect(() => {
    startTimeRef.current = Date.now()
    SpatialNavigation.focus()
    return () => {
      startTimeRef.current = null
    }
  }, [])

  useEffect(() => {
    setIsMounted(true)
  }, [])

  useEffect(() => {
    showTalentModuleRef.current = showTalentModule
  }, [showTalentModule])

  useEffect(() => {
    userDetails?.is_kids === 'true' && dispatch(getNavTabValue('homeuser_kids'))
  }, [userDetails])

  const keyPressFunc = useCallback(
    event => {
      if (
        event?.keyCode === 10009 ||
        event?.keyCode === 89 ||
        event?.keyCode === 461 ||
        event?.keyCode === 409 ||
        event?.keyCode === 8
      ) {
        dispatch(getClearStatePremiumCard({}))
        dispatch(subMenuRailIndex({}))
        const currNavIdx = localStorage.getItem('currNavIdx') || '0'
        const subMenu = localStorage.getItem('subMenu') || 0
        if (currNavIdx == 0) {
          if (subMenu > 0) {
            event.preventDefault()
            navScrollRef.current?.scrollIntoView({
              left: 0,
              top: 0,
              behavior: 'smooth'
            })
            localStorage.setItem('subMenu', subMenu - 1)
            if (subMenu == 1) {
              userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
            }
          } else {
            setUserConfirmationEnable(true)
          }
        } else if (currNavIdx == -1) {
          navigate('/home')
          dispatch(getNavTabValue(userDetails?.is_kids === 'true' ? 'homeuser_kids' : 'homeuser'))
          localStorage.setItem('currNavIdx', 0)
        } else if (currNavIdx == -1 && showTalentModuleRef.current) {
          dispatch(setShowTalentModule(false))
        } else if (premiumTabValue?.length && !hidedata && !submenu) {
          currNavIdx && navData
            ? tabDetails(navData[currNavIdx]?.code, currNavIdx)
            : currNavIdx != 0 &&
              !submenu &&
              !hidedata &&
              userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
        } else if (currNavIdx !== 0 && !submenu && !hidedata) {
          if (!state?.myContentData) {
            navScrollRef.current?.scrollIntoView({
              left: 0,
              top: 0,
              behavior: 'smooth'
            })
            userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
          } else {
            navScrollRef.current?.scrollIntoView({
              right: 0,
              top: 0,
              behavior: 'smooth'
            })
          }
        } else if (subMenu > 0) {
          event.preventDefault()
          navScrollRef.current?.scrollIntoView({
            left: 0,
            top: 0,
            behavior: 'smooth'
          })
          localStorage.setItem('subMenu', subMenu - 1)
        } else if (searchFocus || settingfocus == 'search') {
          navScrollRef.current?.scrollIntoView({
            left: 0,
            top: 0,
            behavior: 'smooth'
          })
          userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
        } else if (currNavIdx !== 0 && !submenu && !hidedata) {
          navScrollRef.current?.scrollIntoView({
            left: 0,
            top: 0,
            behavior: 'smooth'
          })
          userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
        } else if (profileActive && settingfocus == 'settings') {
          navScrollRef.current?.scrollIntoView({
            left: 0,
            top: 0,
            behavior: 'smooth'
          })
          userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
        } else if (settingfocus == 'settings') {
          navScrollRef.current?.scrollIntoView({
            left: 0,
            top: 0,
            behavior: 'smooth'
          })
          userDetails?.is_kids === 'true' ? tabDetails('homeuser_kids', 0) : tabDetails('homeuser', 0)
        }
      }
    },
    [premiumTabValue, submenu]
  )

  const goToSearch = () => {
    dispatch(getNavTabValue('search'))
    setNavFocusactive(-1)
    dispatch(getPremiumCardFocus(null))
    localStorage.setItem('currNavIdx', -1)
    setSearchFocus(true)
    setNavFocusactive()
    dispatch(getRegisterPopup(false))
    navigate('/search',{state: {gaPreviousPath: navbarTab}})
  }

  useEffect(() => {
    if (props?.getSettingsVariable) {
      const element = document.getElementById('userProfileImg')
      element && element?.focus()
    }
  }, [props?.getSettingsVariable])

  const handleFocusNav = (item, index) => {
    setNavFocus(true)
    setProfileActive(false)
    document
      .getElementById(`nav-${index}`)
      ?.scrollIntoView({ behavior: 'smooth', inline: 'end' })
  }

  const goToSettings = () => {
    dispatch(getRegisterPopup(false))
    dispatch(getSettingsClicked(true))
    navigate('/settings')
  }

  useEffect(() => {
    if (props?.page == 'settings') {
      setNavFocusactive()
      setProfileActive(true)
    }
  }, [props?.page])

  useEffect(() => {
    (props?.page == 'home' || props?.page == 'search') &&
      !props?.continueDel &&
      !props?.listDel &&
      document.body.addEventListener('keyup', keyPressFunc)
    return () => {
      (props?.page == 'home' || props?.page == 'search') &&
        !props?.continueDel &&
        !props?.listDel &&
        document.body.removeEventListener('keyup', keyPressFunc)
    }
  }, [premiumTabValue, submenu, props?.page])

  useEffect(() => {
    let myContents = document.getElementById(`nav-${navData?.length - 1}`)
    if (
      state?.myContentData ||
      localStorage.getItem('miscontenidos') ||
      (props?.myContentData && localStorage.getItem('miscontenidos'))
    ) {
      myContents?.scrollIntoView({ block: 'end', behavior: 'smooth' })
      props?.page != 'voddetail' && myContents?.focus()
    }
  }, [state?.myContentData])

  useEffect(() => {
    if (disableFocus) {
      const getNavIndx = localStorage.getItem('currNavIdx')
      firstButtonRef.current[getNavIndx]?.focus()
    }
  }, [disableFocus, firstButtonRef])

  const handleKeyDown = event => {
    const element = document.getElementById('centeredNavbar')
    if (element) {
      const cards = document.querySelectorAll('.navfocus')
      const focusedElement = document.activeElement
      const focusedIndex = Array.from(cards).indexOf(focusedElement)

      if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
        const nextCard = cards[focusedIndex + 1]
        const containerRect = element.getBoundingClientRect()
        const nextCardRect = nextCard.getBoundingClientRect()

        nextCardRect.right > containerRect.right &&
          (element.scrollLeft += nextCardRect.right - containerRect.right)
      } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
        const prevCard = cards[focusedIndex - 1]
        const containerRect = element.getBoundingClientRect()
        const prevCardRect = prevCard.getBoundingClientRect()

        prevCardRect.left < containerRect.left &&
          (element.scrollLeft -= containerRect.left - prevCardRect.left)
      }
    }
  }

  useEffect(() => {
    const centeredNavbar = document.getElementById('centeredNavbar')
    centeredNavbar && centeredNavbar.addEventListener('keydown', handleKeyDown)
    dispatch(getContentMenu('inicio'))
    return () => {
      centeredNavbar &&
        centeredNavbar.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  const profileKeyDown = e => {
    if (e.key == 'ArrowLeft') {
      dispatch(getSettingsClicked(false))
    }
  }

  const closeExitPopUp = () => {
    setUserConfirmationEnable(false)
    document.getElementById(`carousel_id-1`)?.focus()
  }
  return (
    <div>
      {userConfirmationEnable ? (
        <UserConfirmationModal hideConfirmpopup={closeExitPopUp}/>
      ) : null}
      <div
        className={
          blueBand == false && !hidedata
            ? 'homemain-Container1'
            : hidedata == true
              ? 'hidetata'
              : 'blue-band-homemain-Container1'
        }
      >
        <div className="nav-Container" style={mycontentdata && {backgroundColor : '#121212'}} id="nav-bar-focus">
          <nav className={hidedata == true ? 'hidetata' : 'navbar'} style={mycontentdata && {backgroundColor : '#121212'}}>
            <div className="logo">
              <img
                src={'images/logo.png'}
                alt="Logo"
                className="home-logo-img"
              />
            </div>
            <div className="nav-center-alg">
              <div
                className="nav-Items"
                id="navbarfocus"
                style={{
                  width: navData?.length > 6 ? '1159px' : ''
                }}
              >
                {/*  Clear focus state when button loses focus */}
                <button
                  onClick={() => goToSearch()}
                  onFocus={() => setSettingfocus('search')}
                  onBlur={() => setSettingfocus('')}
                  data-sn-up={
                    document.getElementById('bluebandGreenButton')
                      ? '#bluebandGreenButton'
                      : false
                  }
                  id="serachId"
                  className={isMounted && searchFocus ? 'search-Icon-focus focusable' : `search-Icon ${isMounted ? 'focusable' : ''} ${searchFocus || settingfocus == 'search'
                    ? 'search-Icon-Active'
                    : ''
                    } `}
                >
                  {settingfocus == 'search' ? (
                    <LazyLoadImage className="search-img" src={searchIcon} />
                  ) : (
                    <LazyLoadImage
                      className="search-imgfocused"
                      src={searchIcon}
                    />
                  )}
                </button>
                <div
                  className="centered-items"
                  ref={navScrollRef}
                  id="centeredNavbar"
                >
                  {navData?.length > 0 &&
                    navData?.map((item, index) => (
                      <React.Fragment key={item.code}>
                        {item.code == 'miscontenidos' && !userId ? null : (
                          // Assign ref to the first button
                          <button
                            ref={el => (firstButtonRef.current[index] = el)}
                            onClick={() => selectedFocus(item.code, index, item.page)}
                            id={`nav-${index}`}
                            style={{
                              border:
                                navFocusactive == index && navFocus
                                  ? 'none'
                                  : '',
                              height:
                                navFocusactive == index && navFocus
                                  ? '62px'
                                  : ''
                            }}
                            className={`navfocus focusable ${navFocusactive == index
                              ? 'navActive'
                              : `${searchFocus ? 'search-default' : ''}`
                              } ${navFocusactive == index && navFocus
                                ? 'navSelected'
                                : ''
                              }focusable`}
                            onFocus={() => handleFocusNav(index)}
                            onBlur={() => setNavFocus(false)}
                            restrict="self-only"
                            data-sn-up={
                              document.getElementById('bluebandGreenButton')
                                ? '#bluebandGreenButton'
                                : false
                            }
                            data-sn-down={
                              storeFirstIndex && localStorage.getItem('miscontenidos') && props?.page != 'voddetail'
                                ? `#index${storeFirstIndex}0`
                                : undefined
                            }
                            data-sn-left={
                              index == 0 ? '#serachId' : `#nav-${index - 1}`
                            }
                          >
                            <div className="Navnodes">{item?.page}</div>
                          </button>
                        )}
                      </React.Fragment>
                    ))}
                </div>
                {userDetails?.user_id ? (
                  <button
                    id="userProfileImg"
                    onFocus={() => {
                      setProfileActive(true)
                    }}
                    data-sn-up={
                      document.getElementById('bluebandGreenButton')
                        ? '#bluebandGreenButton'
                        : false
                    }
                    onClick={() => goToSettings()}
                    onBlur={() => {
                      props?.page == 'voddetail' ? setProfileActive(false) : ''
                    }}
                    className={`profile-item focusable ${profileActive ? 'profile-active' : ''
                      }`}
                    data-sn-left={`#nav-${navData?.length - 1}`}
                    onKeyDown={e => profileKeyDown(e)}
                  >
                    <img
                      className="avatar-img"
                      src={
                        userProfileData?.user_image ??
                        userProfileData?.img?.user_image ??
                        'images/avatar_img.png'
                      }
                    />
                  </button>
                ) : (
                  <button
                    id="nonUserProfileImg"
                    onClick={() => navigate('/landing')}
                    className="nav-iconnav-item focusable"
                  >
                    <img className="avatar-img" src={'images/avatar_img.png'} />
                  </button>
                )}
              </div>
            </div>
          </nav>
        </div>
      </div>
    </div>
  )
}

export default React.memo(Navbar)
