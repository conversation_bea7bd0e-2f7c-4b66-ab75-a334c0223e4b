import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { store } from '../../store/sagaStore'
import {
  getclickFilterrail,
  getFilterCurrentFocus,
  getNewRail,
  subMenuRailCards,
  subMenuRailCardsdata
} from '../../store/slices/subMenuDataSlice'
import AsyncImageLoader from '../epg/AsyncImageLoader'
import './GenreRail.scss'
import { useNavigate } from 'react-router-dom'
import GenreFilter from './GenreFilter'
import { pushContentSelectionEvent } from '../../GoogleAnalytics'
import { HOME, SELECT_CONTENT } from '../../GoogleAnalyticsConstants'
import { LazyLoadImage } from 'react-lazy-load-image-component'

const GenreRailsContent = props => {
  const navigate = useNavigate()
  const startTimeRef = useRef(null)
  const filtersubmenuUrl = []
  const contentlistUrl = useSelector(
    state =>
      state?.SubMenuFilter?.railListurl?.response?.modules?.module[0]
        ?.components?.component
  )
  const submenu = useSelector(state => state?.SubMenuFilter?.submenudata)
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const [navFocus, setNavFocus] = useState(false)
  const [activeIndex, setActiveIndex] = useState() // Initial active index
  const [focusContent, setFocusContent] = useState(false)

  contentlistUrl?.length > 0 &&
    contentlistUrl?.map(each => {
      each.type === 'Listadoinfinito' &&
        filtersubmenuUrl?.push({ urlData: each?.properties?.url })
    })

  const submenuUrl = filtersubmenuUrl[0]?.urlData
  const railcontentdata = useSelector(
    state => state?.SubMenuFilter?.raildata?.assets
  )
  const railcontentdatatotal = localStorage.getItem('submenucard')
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const hidedata = useSelector(state => state?.SubMenuFilter?.filterclickdata)
  const activefocus = useSelector(
    state => state?.SubMenuFilter?.filterbackfocus
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
   const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
    const nav = useSelector(state => state?.homeReducer?.navbarData)
    const navData = userDetails?.is_kids === 'true' ? navKids : nav
    const seriesIndex = navData?.findIndex(item => item?.code === 'seriesnv' && item?.page === 'Series' );
     const moviesIndex = navData?.findIndex(item => item?.code === 'peliculas' && item?.page === 'Películas' );
    const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)  
    const posterTitle =
    apaMetaData?.poster_title_configuration &&
    JSON?.parse(apaMetaData?.poster_title_configuration)
    const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
    useEffect(() => {
    store.dispatch(
      subMenuRailCards({
        url: submenuUrl,
        HKS: userDetails?.session_stringvalue,
        user_id: userDetails?.user_id
      })
    )
  }, [submenuUrl])

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const handleTranslationchange = useCallback(
    keyname => {
      return apilanguage?.[keyname] ? apilanguage?.[keyname] : [keyname]
    },
    [apilanguage]
  )

  useEffect(() => {
    setNavFocus(false)
    SpatialNavigation.focus()
    store.dispatch(getFilterCurrentFocus(false))
    activefocus && document.getElementById('filter').focus()
  }, [activefocus])

  const filterclick = e => {
    e?.preventDefault()
    store.dispatch(getclickFilterrail(true))
    localStorage.removeItem('backfilter')
    setNavFocus(true)
  }

  const handleFocus = e => {
    setFocusContent(true)
  }
  const handleBlur = e => {
    setFocusContent(false)
  }
  const handlesamsungkey = (key, keycode) => {
    if (submenu) {
      if (key.redcode == keycode) {
        const subMenu = localStorage.getItem('subMenu')
        localStorage.setItem('subMenu', parseInt(subMenu) + 1)
        store.dispatch(getclickFilterrail(true))
        localStorage.removeItem('backfilter')
        setNavFocus(true)
      }
    }
    if (keycode === 10009) {
      store.dispatch(subMenuRailCardsdata(false))
    }
  }
  const handleLgkey = keycode => {
    if (submenu) {
      if (keycode == 403) {
        const subMenu = localStorage.getItem('subMenu')
        localStorage.setItem('subMenu', parseInt(subMenu) + 1)
        store.dispatch(getclickFilterrail(true))
        localStorage.removeItem('backfilter')
        setNavFocus(true)
      }
    }
    if (keycode === 461) {
      store.dispatch(subMenuRailCardsdata(false))
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  let subMenuFinalurl
  let subMenuFromData
  const cardChange = (e, index) => {
    e.preventDefault()
    if (e.keyCode == 40) {
      if (
        index >= railcontentdata?.length - 18 &&
        railcontentdata?.length != railcontentdatatotal
      ) {
        let indexval = railcontentdata?.length
        let subMenuToData = 'from=0'
        if (railcontentdatatotal - railcontentdata?.length >= 40) {
          subMenuFromData = `from=${indexval}`
        } else {
          subMenuFromData = `from=${railcontentdata?.length}`
        }
        subMenuFinalurl = submenuUrl?.replace(subMenuToData, subMenuFromData)
        subMenuToData = subMenuFromData
        if (railcontentdata?.length < railcontentdatatotal) {
          store.dispatch(
            getNewRail({
              url: subMenuFinalurl,
              HKS: userDetails?.session_stringvalue,
              user_id: userDetails?.user_id
            })
          )
        }
      }
    }
  }

  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
    const engagementTime =  Date.now() - startTimeRef.current;    
    const userData = {
      suscriptions: userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key => userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
      content_section : navbarTab,
      modulo_name : 'cards listado infinito',
      page_path: HOME,
      page_title: navbarTab,
      engagement_time_msec: engagementTime
    }
    if (item?.is_series || item?.contentType === 'chapter') {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', seriesIndex)
      pushContentSelectionEvent(userData, item, index, SELECT_CONTENT)
      navigate('/series', { state: { data: item,pageName:'inicio'}, replace: true })
    } else {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', moviesIndex)
      pushContentSelectionEvent(userData, item, index, SELECT_CONTENT)
      navigate('/movies', { state: { vodData: item, pageName:'inicio' }, replace: true })
    }
    startTimeRef.current = Date.now()
  }
  useEffect(() => {
		startTimeRef.current = Date.now()
    return () => {
      startTimeRef.current = null
    }
  }, [])

  const handleKeyUp = () => {
     setActiveIndex(props?.subMenuIndex);
  };

  return (
    <div className="genrefilter-railcontent">
      {navFocus && !localStorage.getItem('backfilter') && (
        <div className={`${!hidedata ? 'hidetata' : 'genre-filter-page'}`}>
          <GenreFilter />
        </div>
      )}
      {submenu && (
        <div className={`${hidedata ? 'hidetata' : 'genrerailcarddata'}`}>
          <div className="FooterGenre ">
            <button
              className="FooterButtonGenre focusable"
              onClick={() => filterclick()}
              onKeyUp={() => handleKeyUp()}
              onFocus={() => handleFocus()}
              onBlur={() => handleBlur()}
              id="filter"
              data-sn-up={`#genrenav${activeIndex}`}
              data-sn-right
              data-sn-left
            >
              <img
                style={{ marginTop: '15px', marginBottom: 'auto' }}
                width={20}
                height={20}
                src="images/Home_icons/red.png"
              />
              <p className="FooterTextGenre">
                {handleTranslationchange('ribbons_data_option_button_filter')}
              </p>
            </button>
          </div>
          <div className={`${hidedata ? 'hidetata' : 'genrecarddataval'}`}>
            {railcontentdata?.map((item, index) => (
              <>
                <button
                  onClick={() => goToMoviesSeries(item,index)}
                  className="genrecard focusable"
                  id={`rail_${index}`}
                  onKeyUp={e => {
                    cardChange(e, index)
                  }}
                  key={`genre_${index}`}
                  data-sn-up={
                    Math.ceil((index + 1) / 4) === 1 ? `#filter` : undefined
                  }
                  data-sn-right={
                    ((((index + 1) % 4) !== 0 && (railcontentdata?.length) - 1 !== index)) && undefined  }
                    data-sn-left={
                      Math.ceil((index) % 4) === 0 ? '' : undefined 
                    }
                >
                  {item?.images?.small && item?.images?.small !== '' || item.image_small && item.image_small !== '' ? (
                    <>
                      <LazyLoadImage
                        src={item.image_small || item?.images?.small}
                        key={item.id}
                        className="submenurail-image"
                      />
                      {posterTitle?.default[item?.proveedor_code ?? item?.provider?.code]?.landscape ? (
                        <div
                          className="genre-content-title"
                          style={{
                            display: posterTitle?.default[item?.proveedor_code ?? item?.provider?.code]
                              ?.landscape
                              ? 'flex'
                              : 'none'
                          }}
                        >
                          {posterTitle?.default[item?.proveedor_code ?? item?.provider?.code]?.landscape
                            ? item?.title?.length >= 26
                              ? `${item?.title?.slice(0, 26)}...`
                              : item?.title
                            : ''}
                        </div>
                      ) : null}
                    </>
                  ) : (
                    <LazyLoadImage
                      src="images/vertical_card.png"
                      loading="lazy"
                      alt="PlaceHolder"
                      className="rail-image-vert"
                    />
                  )}
                  {/* tags */}

                  {item.images?.small && item?.provider?.code == 'amco' || item.image_small && item?.proveedor_code == 'amco' ? (
                     item?.format_types === 'ppe,download' || item?.contentAttributes?.marketingType === 'ppe,download' ? (
                      <div className="submenu-rail-alquilar">
                        <img
                          src={'images/Alquilar.svg'}
                          className="submenu-tagAlq-img"
                        />
                      </div>
                    ) :  item?.format_types === 'ppe' || item?.contentAttributes?.marketingType === 'ppe' ? (
                      <div className="submenu-rail-alquilar">
                        <img
                          src={'images/Alquilar.svg'}
                          className="submenu-tagAlq-img"
                        />
                      </div>
                    ) : null
                  ) : item?.images?.small && 
                      item?.provider?.code && 
                      item?.images?.medium ||
                      item?.image_medium && 
                      item.image_small &&
                      item?.proveedor_code
                     ? (
                    <div className="submenu-verohara">
                      {Addproveedor(providerLabel?.[item?.proveedor_code ?? item?.provider?.code]?.susc) && (
                        <img
                          className={
                            item?.proveedor_code === 'picardia2' || item?.provider?.code === 'picardia2'
                              ? 'picardia-image'
                              : 'submenu-verohara-img'
                          }
                          id="#icon1"
                          src={Addproveedor(providerLabel?.[item?.proveedor_code ?? item?.provider?.code]?.susc)}
                        />
                      )}
                      {item?.contentAttributes?.marketingType === 'free' || item?.format_types === 'free' && userDetails?.subscriptions?.length == 0 ? (
                        <div className="verahora-tag">VER AHORA</div>
                      ) : null}
                      {item?.images?.small &&
                        item?.provider?.code === 'picardia2' &&
                        item?.images?.medium || 
                        item.image_small &&
                        item?.proveedor_code === 'picardia2' &&
                        item?.image_medium && (
                          <div className="picardia-proveedorBlockRail-1">
                            <img
                              src={'images/Adultus.svg'}
                              className="picardia-tag-1"
                            />
                          </div>
                        )}
                    </div>
                  ) : null}
                </button>
              </>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default GenreRailsContent
