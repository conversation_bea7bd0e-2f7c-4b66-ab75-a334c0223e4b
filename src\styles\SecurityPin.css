.PinSettings {
	width: 1920px;
	height: 1080px;
	display: flex;
	flex-direction: column;
	background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

.SPTitle {
	font: normal normal normal 48px/63px Roboto;
	color: #F1F2F3;
	letter-spacing: 0px;
	opacity: 1;
	display: flex;
	margin: 3rem;
	justify-content: center;
}

.CPDesc {
	width: 740px;
	height: 280px;
	margin: 130px auto 0px auto;
}

.SPDescContainer {
	display: flex;
	flex-direction: column;
	margin-top: 185px;
}

.EPDescContainer {
	display: flex;
	flex-direction: column;
	margin-top: 26px;
}

.subTitles {
	text-align: center;
	font: normal normal normal 30px/42px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin: 0px auto 10px auto;
	width: 1164px;
}

.CreatePinContainer {
	display: flex;
	margin-top: 86px;
	justify-content: center;
	flex-direction: column;
}

.CreatePinButtons {
	width: 463px;
	height: 73px;
	background: #2E303D 0% 0% no-repeat padding-box;
	border-radius: 44px;
	font: normal normal normal 34px/40px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: auto;
	margin-right: auto;
}

.EditPinButtons {
	width: 560px;
	height: 100px;
	background: #2E303D 0% 0% no-repeat padding-box;
	border-radius: 44px;
	opacity: 1;
	font: normal normal normal 34px/40px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0px auto 20px auto;
}

.CreatePinButtons:focus,
.EditPinButtons:focus,
.ForgotPinButton:focus {
	background: #981C15 0% 0% no-repeat padding-box;
}

.subTextOtp {
	font: normal normal normal 28px/33px Roboto;
	letter-spacing: 0px;
	color: #A4A9AE;
	opacity: 1;
}

.otp-box {
	display: flex;
	margin-top: 35px;
}

.otp-field {
	margin-right: 35px;
	padding-bottom: 20px;
	width: 70px;
	height: 40px;
    font-size: 55px !important;
	text-align: center;
	color: #EEEEEE;
	border: none;
	border-bottom: 6px solid #2E303D;
	background-color: transparent;
	outline: unset;
	pointer-events: none;
}

.OtpFocused {
	margin-right: 35px;
	padding-bottom: 20px;
	width: 70px;
	height: 40px;
	font-size: 55px !important;
	text-align: center;
	color: #EEEEEE;
	border: none;
	border-bottom: 6px solid #981C15;
	background-color: transparent;
	outline: unset;
	pointer-events: none;
}

.otp-field:focus,
.otp-box:focus {
	border-bottom: 6px solid #981C15;
}

.seeOtpButton {
	min-width: 104px;
	height: 69px;
	background: #34353B 0% 0% no-repeat padding-box;
	border-radius: 5px;
	opacity: 1;
	align-items: center;
	justify-content: center;
	display: flex;
}

.seeOtpButton:focus {
	border: 3px solid #981C15;
}

.OtpIconVisibility {
	width: 33px;
	height: 29px;
	opacity: 1;
}

.PinError {
	text-align: left;
	font: normal normal normal 24px/28px Roboto;
	letter-spacing: 0px;
	color: #C60000;
	opacity: 1;
	margin-top: 10px;
}

.DeactivatePopup {
	background: #EEEEEE 0% 0% no-repeat padding-box;
	border: 2px solid #EEEEEE;
	border-radius: 12px;
	opacity: 1;
	height: 180px;
	width: 562px;
	position: absolute;
	left: 85px;
	bottom: 310px;
}

.RemindPopup {
	background: #EEEEEE 0% 0% no-repeat padding-box;
	border: 2px solid #EEEEEE;
	border-radius: 12px;
	opacity: 1;
	height: 138px;
	width: 562px;
	position: absolute;
	align-items: center;
	display: flex;
	left: 85px;
	bottom: 185px;
}

.DeactivatePopup::before,
.RemindPopup::before {
	content: '';
    position: absolute;
    width: 45px;
    height: 45px;
    z-index: -1;
    border-radius: 3px;
    right: -15px;
    transform: rotate(45deg);
    background-color: #EEEEEE;
    bottom: 50px;
}

.DeactivateList {
	font-size: 32px;
	list-style: none;
	margin: 18px 0px 0px -55px;
}

ul.DeactivateList li {
	font-size: 32px;
}

.SPSubTitle {
	text-align: center;
	font: normal normal normal 30px/42px Roboto;
	letter-spacing: 0px;
	color: #ADADAD;
	opacity: 1;
}

.DPDesc {
	width: 1303px;
	height: 150px;
	margin: 90px 96px 0px auto;
}

.ForgotPin {
	text-align: center;
	font: normal normal normal 34px/45px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin: 0;
}

.ForgotPinButton {
	min-width: 400px;
	height: 69px;
	background: #34353B 0% 0% no-repeat padding-box;
	border-radius: 5px;
	opacity: 1;
	margin-left: 22px;
}

.NotificationFMLayout {
	background: #1A1A1A 0% 0% no-repeat padding-box;
	border: 2px solid #50595E;
	border-radius: 10px 0px 0px 10px;
	opacity: 1;
	width: 0;
	align-items: center;
	display: flex;
	position: absolute;
	white-space: nowrap;
	height: 140px;
	top: 60px;
	right: -4px;
	transition: cubic-bezier(1, 0, 0, 1) ;
	/* transition: 2s cubic-bezier(0, -0.31, 0.99, 1.24); */
	overflow-x: hidden;
}

.NotificationFMLayout.show {
	width: 700px;
	opacity: 1 !important;
}

.NotificationFMText {
	text-align: left;
	font: normal normal normal 28px/37px Roboto;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 1;
	margin: 0;
	width: 500px !important;
}

.LCContainer {
	width: 1800px;
	display: flex;
	/* height: 700px; */
	margin: 0px auto 0px auto;
	flex-wrap: wrap;
}

.ChannelCard {
	padding: 15px;
}

.ChannelBtn:focus>.lazy-load-image-background {
	width: 412px;
	height: 232px;
	border: 3px solid #981C15;
	box-shadow: 0px 0px 24px #981C15;
	outline: 3px solid #981C15;
}

.ChannelImg {
	width: 412px;
	height: 232px;
}


.ChannelName {
	text-align: left;
	letter-spacing: -1.9px;
	color: #FFFFFF;
	opacity: 1;
	font: normal normal normal 28px/37px Roboto;
}

.ChannelContainer {
	display: flex;
	margin-left: auto;
	margin-right: auto;
	align-items: center;
	justify-content: center;
	width: 293px;
	height: 86px;
}

.ChannelNumber {
	text-align: left;
	font: normal normal normal 40px/45px Roboto;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 0.7;
	margin: 0 15px;
}

.ConDescContainer {
	width: 1261px;
	height: 245px;
	display: flex;
	margin: 0px auto;
	flex-direction: column;
}

.ConDescText {
	text-align: center;
	font: normal normal normal 30px/42px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	width: 1266px;
	margin-top: 8px;
}

.ContentCardContainer {
	width: 1360px;
	height: 290px;
	display: flex;
	justify-content: space-between;
	margin: 0px auto;
	overflow: hidden;
}

.BtnCardContainer {
	background: #333333 0% 0% no-repeat padding-box;
	position: relative;
	border-radius: 15px;
	opacity: 1;
	width: 240px;
	height: 280px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.BtnCardContainerDisable {
	background: #121212 0% 0% no-repeat padding-box;
	position: relative;
	border-radius: 15px;
	opacity: 1;
	width: 240px;
	height: 280px;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.BtnCardContainer::after,
.BtnCardContainerDisable::after {
	content: '';
	position: absolute;
	width: 1px;
	height: 40px;
	z-index: -1;
	right: -20px;
	transform: rotate(90deg);
	background-color: #EEEEEE;
}

.BtnCardContainer:focus,
.BtnCardContainerDisable:focus {
	box-shadow: 0px 0px 16px #981C15;
	border: 3px solid #981C15;
	opacity: 1;
}

.ConCardImg {
	display: flex;
	margin-left: auto;
	margin-right: auto;
	width: 100px;
	height: 100px;
}

.ConCardTitle {
	text-align: center;
	font: normal normal bolder 35px/46px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin: 44px 0px 0px 0px;
}

.ContentDesc {
	text-align: center;
	font: normal normal normal 26px/48px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
}

.SaveBtnContainer {
	display: flex;
	justify-content: center;
	margin-top: 45px;
}


.EmptyChannel {
	margin: 0px auto;
}

.EmptyChannelTxt {
	font: normal normal normal 26px/48px Roboto;
	color: #F1F2F3;
	letter-spacing: 0px;
	opacity: 1;
	display: flex;
	margin: 3rem;
	justify-content: center;
}

.ParentalControlMenuCon {
	margin: auto;
    display: block;
}

.ParentalControlCon{
	margin: 0px auto;
    display: flex;
    width: 85%;
}