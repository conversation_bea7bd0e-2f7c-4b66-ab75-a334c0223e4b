import epgVersionSlice, {
    getEpgVersion,
    getEpgVersionSuccess,
    getEpgVersionError
  } from './EpgVersionSlice';

  
  describe('epgVersionSlice', () => {
    const initialState = {
        epgVersion: '',
        soaVersion: '',
        isLoading: false,
        error: {}
    };
  
    it('should handle initial state', () => {
      expect(epgVersionSlice(undefined, { type: 'unknown' })).toEqual(initialState);
    });
  
    it('should handle getEpgVersion', () => {
      const nextState = epgVersionSlice(initialState, getEpgVersion());
      expect(nextState).toEqual({
        epgVersion: '',
        soaVersion: '',
        isLoading: true,
        error: {}
      });
    });
  
    it('should handle getEpgVersionSuccess', () => {
        const initialState = {
            epgVersion: '',
            soaVersion: '',
            isLoading: false,
        }

       const response = {
            epgVersion: '333529',
            soaVersion: '0.10'
        }

      const action = {
        payload: { response }
      };
      const expectedState = {
        epgVersion: response.epg_version,
        soaVersion: response.soa_version,
        isLoading: false
      };
      
      const nextState = epgVersionSlice(initialState, getEpgVersionSuccess(action));
      expect(nextState).toEqual(expectedState);
    });

    it('should handle getEpgVersionError', () => {
      const error = { message: 'Error' };
      const nextState = epgVersionSlice(initialState, getEpgVersionError(error));
      expect(nextState).toStrictEqual({
        epgVersion: '',
        soaVersion: '',
        isLoading: false,
        error
      });
    });
  });
