import React, { useCallback, useEffect } from 'react'
import './GeneralError.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
const alertImage = 'images/ic_alert.png'

const GeneralErrorScreen = () => {
    const { state } = useLocation();
    const navigate = useNavigate();
    const vodSeriesData = state?.vcardSeries
    const vodMoviesData = state?.vcardMovies

    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

    const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const region = localStorage.getItem('region')
    const apilanguage = translations?.language?.[region]

    useEffect(() => {SpatialNavigation.focus()},[])
    
      const keyPressFunc = (event) => {
        if(event.keyCode === 10009 || event.keyCode === 461 || event?.keyCode === 8) {
            if(state?.vcardType == 'series') {
                navigate('/series', { state: { data: vodSeriesData } })
            }else if(state?.vcardType == 'movies') {
              navigate('/movies', { state: { data: vodMoviesData } })
            }       
        }
      }

      useEffect(() => {
        document.body.addEventListener('keydown', keyPressFunc)
        return () => {
          document.body.removeEventListener('keydown', keyPressFunc)
        }
      }, [keyPressFunc])

    const handleTranslationchange = useCallback((keyname) => {
        if (!apilanguage?.[keyname]) {
          return (keyname?.slice(0,13) + '...')
        } else {
          return apilanguage?.[keyname]
        }
      }, [])

      const handleAccept = () => {
        if(state?.vcardType == 'series') {
            navigate('/series', { state: { data: vodSeriesData } })
        } else if(state?.vcardType == 'movies') {
          navigate('/movies', { state: { data: vodMoviesData } })
      }
      }

  return (
    <div className='background-Error'>
        <img
          src={'images/Logos_Claro_Video.svg'}
          className="claro-logo"
          alt="logo"
        />
        <div className='wrapper'>
            <img className='alert-icon' src={alertImage} />
            <p className='first-error-message'>{handleTranslationchange('api_error_title')}</p>
            <p className='second-error-message'>{handleTranslationchange('contents_withoutResults_talents_message_label')}</p>
            <button autoFocus={true} className='accept-button focusable' onClick={ () => handleAccept()}>{handleTranslationchange('PLY_DEV_00002_close')}</button>
        </div>
    </div>
  )
}

export default GeneralErrorScreen;