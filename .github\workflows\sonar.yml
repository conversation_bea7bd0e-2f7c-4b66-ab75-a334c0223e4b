# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
# 
#    CONFIDENTIAL
#    __________________
#
#    © 2023 - AMCO
#    All Rights Reserved.
#
#   NOTICE:  All information contained herein is, and remains
#   the property of AMCO and its suppliers,
#   if any.  The intellectual and technical concepts contained
#   herein are proprietary to AMCO
#   and its suppliers and may be covered by U.S. and Foreign Patents,
#   patents in process, and are protected by trade secret or copyright law.
#   Dissemination of this information or reproduction of this material
#   is strictly forbidden unless prior written permission is obtained
#   from AMCO.
#
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #

name: Sonar (analysis)

# Trigger enabled
# Code merge, pull request, push
on:
   workflow_dispatch:
     branches: [ "master" ]

#  push:
#    branches:
#    - develop
#    - main
#    - master
#    - releases

#  pull_request:
#    types: [opened, synchronize, reopened]
#    branches:
#    - bugfix
#    - bugfix/*
#    - develop
#    - feature/*
#    - hotfix
#    - hotfix/*
#    - main
#    - master
#    - release
#    - release/*
#    - releases

# Build Stage
# Runs-on: ubuntu-latest
jobs:

  # Code Analysis Stage
  # Description: This Stage analyze static code (CodeSmells, Bugs & Vulnerabilities)
  # Workflow: Use a predefined one (sonar-analysis) for SonarCloud analysis
  # Runs-on: ubuntu-latest
  analysis:
    name: Analysis
    uses: clarovideo-argentina/cv-auto-devops/.github/workflows/sonar-analysis.yml@main
    with:
      key: sct_nonandroid-repo-code
      name: SonarCloud
      organization: clarovideo
      project: sct_nonandroid-repo-code
      runs-on: ubuntu-latest
    secrets:
      token: ${{ secrets.SONAR_TOKEN }}
