export const unixToMinutes = unixTimestamp => {
  const date = new Date(unixTimestamp * 1000) // Convert Unix timestamp to milliseconds
  const minutes = date.getUTCMinutes() // Get minutes in UTC
  return minutes
}

export const convertToUnixBegin = dateString => {
  const date = new Date(dateString)
  date.setHours(0, 0, 0, 0) // Set to the beginning of the day
  return Math.floor(date.getTime() / 1000) // Convert to Unix timestamp
}

export const calculateDuration = (unixBegin, unixEnd) => {
  const durationSeconds = unixEnd - unixBegin
  const hours = Math.floor(durationSeconds / 3600)
  const minutes = Math.floor((durationSeconds % 3600) / 60)
  const seconds = durationSeconds % 60

  return `${hours > 10 ? hours : '0' + hours}:${
    minutes > 10 ? minutes : '0' + minutes
  }:${seconds > 10 ? seconds : '0' + seconds}`
}

export const calculateHoursDifference = (dateBegin, dateEnd) => {
  const beginDate = new Date(dateBegin)
  const endDate = new Date(dateEnd)

  const timeDifference = endDate - beginDate
  const hoursDifference = Math.floor(timeDifference / (1000 * 60 * 60))
  const minutesDifference = Math.floor(
    (timeDifference % (1000 * 60 * 60)) / (1000 * 60)
  )
  const secondsDifference = Math.floor((timeDifference % (1000 * 60)) / 1000)

  return `${hoursDifference > 10 ? hoursDifference : '0' + hoursDifference}:${
    minutesDifference > 10 ? minutesDifference : '0' + minutesDifference
  }:${secondsDifference > 10 ? secondsDifference : '0' + secondsDifference}`
}
