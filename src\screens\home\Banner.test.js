import React from "react";
import { fireEvent, getAllByText, getByAltText, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import Banner from "./Banner";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};


describe('Banner page test case', () => {
    test('should render the Banner', () => {
        const { container } = renderWithState(<Banner />)
    })

    test('should render api mock data', () => {
      const props = {
        premium:"premium"
      }
        renderWithState(<Banner {...props}/>)
    })
    test('should render api mock data', () => {
        initialState.homeSlice = {
            bannerHightlight:{
                response:{
                    highlight: {
                        "title": "Caminen con los ojos bien abiertos",
                        "title_uri": "Caminen-con-los-ojos-bien-abiertos",
                        "volant": null,
                        "crest": "Dos funcionarios del Vaticano investigan una sanación milagrosa atribuida a un cura local. A su llegada, su misteriosa desaparición detiene la investigación. ",
                        "url": "destacado_clickUrlGrupo(971784,'VIDEO');",
                        "image_highlight": "https://clarovideocdn6.clarovideo.net/pregeneracion//cms/images/202312/347725_Default_Los-Enviados-T2-Gen-2024_05165336.jpg",
                        "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/64200/EXPORTACION_WEB/SS/64200WHORIZONTAL.jpg?size=675x380",
                        "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/64200/EXPORTACION_WEB/SS/64200WVERTICAL.jpg?size=200x300",
                        "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/64200/EXPORTACION_WEB/SS/64200WHORIZONTAL.jpg?size=290x163",
                        "image_background": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/LOSENVIADOS-02-02-00/EXPORTACION_WEB/CLEAN/LOSENVIADOS-02-02-00_e-1280x720.jpg",
                        "group_id": "971784",
                        "special": null,
                        "section": null,
                        "format_types": "susc",
                        "live_enabled": "0",
                        "live_type": null,
                        "live_ref": null,
                        "rating_code": "R",
                        "proveedor_name": "PARAMOUNT",
                        "proveedor_code": "paramount",
                        "text_highlight": "Los Enviados (T2) Gen 2024",
                        "is_series": true,
                        "user_status": null,
                        "type": "group"
                    }
                }
            },
        }
        const props = {
          premium:"premium"
        }
          renderWithState(<Banner {...props}/>)
      })
})