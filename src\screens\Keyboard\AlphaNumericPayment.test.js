import React from 'react';
import { fireEvent, render, queryByAttribute, act } from '@testing-library/react';
import { Provider } from "react-redux";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from 'redux-mock-store';
import '@testing-library/jest-dom';
import { createHashHistory as createHistory } from 'history';
import AlphaNumeric from './AlphaNumericPayment';

const mockStore = configureStore([]);

const history = createHistory();

const initialState = {};

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}><Router history={history}>{children}</Router></Provider>
);

export const renderWithState = (ui) => {
  return render(ui, { wrapper: Wrapper });
};

describe('should render the home page test cases', () => {
  const defaultProps = {
    onChange: jest.fn(),
    onSubmit: jest.fn(),
    keyMode: "alphabetic",
    id: 'profileup',
    setCurrentButtonFocus: jest.fn()
  };

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test("should render the profileform for the smart tv part", () => {
    let props = {
      "onChange": "ƒ onChange() {}",
      "onSubmit": "ƒ onSubmit() {}",
      "type": "alphaNumeric",
      id: 'profileup',
      setCurrentButtonFocus: jest.fn()
    }
    const { rerender } = renderWithState(<AlphaNumeric {...defaultProps} />);
    rerender(<AlphaNumeric {...props} />)
  });

  test("handles LG TV key events", () => {
    delete global.tizen;
    renderWithState(<AlphaNumeric {...defaultProps} />);
    fireEvent.keyUp(document, { keyCode: 404 });
    expect(defaultProps.onChange).toHaveBeenCalledWith('');
  });

  test('renders numeric keypad layout', () => {
    const props = {
      onChange: jest.fn(),
      type: 'numeric',
      setCurrentButtonFocus: jest.fn()
    };
    const { getAllByText } = renderWithState(<AlphaNumeric {...props} />);
    expect(getAllByText('1')[0]).toBeInTheDocument();
    expect(getAllByText('0')[0]).toBeInTheDocument();
  });

  test('manages focus in numeric keypad', () => {
    const props = {
      onChange: jest.fn(),
      type: 'numeric',
      setCurrentButtonFocus: jest.fn(),
      onFocus: jest.fn(),
      onBlur: jest.fn()
    };
    const { getAllByRole } = renderWithState(<AlphaNumeric {...props} />);

    const buttons = getAllByRole('button');
    fireEvent.focus(buttons[0]);
    fireEvent.blur(buttons[0]);
  });

  test("handles autoFocus prop", () => {
    renderWithState(<AlphaNumeric {...defaultProps} autoFocus={true} />);
    act(() => { jest.advanceTimersByTime(1500); });
    expect(document.activeElement).toHaveTextContent('q');
  });

  test('respects length restriction', () => {
    const onChange = jest.fn();
    const { getByText } = renderWithState(
      <AlphaNumeric keyMode="alphabetic" onChange={onChange} setCurrentButtonFocus={jest.fn()} length={3} />
    );

    fireEvent.click(getByText('a'));
    fireEvent.click(getByText('b'));
    fireEvent.click(getByText('c'));
    fireEvent.click(getByText('d'));
  });

  test('numeric keypad functions correctly', () => {
    const onChange = jest.fn();
    const { getAllByRole } = renderWithState(
      <AlphaNumeric keyMode="numeric" onChange={onChange} setCurrentButtonFocus={jest.fn()} />
    );

    getAllByRole('button').forEach(button => {
      fireEvent.click(button);
    });
  });

  test('keyboard-clear-buttons ', () => {
    const props = {
      onChange: jest.fn(),
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn()
    };
    const { container } = renderWithState(<AlphaNumeric {...props} />)
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'btnclear')
    fireEvent.focus(buttonClick)
    fireEvent.blur(buttonClick)
    fireEvent(
      buttonClick,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })

  test('handles Samsung TV green key', () => {
    const props = {
      onChange: jest.fn(),
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn()
    };

    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 402 })
      },
    };

    const { container } = renderWithState(<AlphaNumeric {...props} />);
    const clearBtn = container.querySelector('#btnclear');
    fireEvent.keyUp(container, { keyCode: 402 });
    fireEvent(
      clearBtn,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
    delete global.tizen;
  });

})