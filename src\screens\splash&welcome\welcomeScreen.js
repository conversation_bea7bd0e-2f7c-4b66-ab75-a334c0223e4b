import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import './Splash.scss'
import Lottie from 'react-lottie-player'
import animationData from '../../json/animationData.json'
import { useSelector } from 'react-redux'
import { COMMON_URL } from '../../utils/environment'
import { useDispatch } from 'react-redux'
import {
  getLevel,
  getLevelKids,
  getNavTabValue
} from '../../store/slices/HomeSlice'

import { getEpgMenu, getEpgVersion } from '../../store/slices/EpgSlice'
import {
  getNavBarClicked,
  setSkeltonLoading,
  getIsLoggedinV1
} from '../../store/slices/login'

const WelcomeScreen = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
    const { state } = useLocation()
  const region = localStorage.getItem('region')

  const [kidsState, setKidsState] = useState(false)

  const railsDataObject = useSelector(state => state?.homeReducer?.urlsData)
  const railsDataKidsObject = useSelector(
    state => state?.homeReducer?.urlsDataKids
  )
  const levelData = useSelector(state => state?.homeReducer?.level)
  const levelKidsData = useSelector(state => state?.homeReducer?.levelKids)
  const vcardSeriesDetails = useSelector(
    state => state?.login?.vcardSeriesDetails
  )
  const vcardDetails = useSelector(state => state?.login?.vcardDetails)
  const loginSuccess = useSelector(state => state?.login?.loginSuccess)
  const anonymousUser = useSelector(state => state?.login?.anonymousUser)
  const watchFree = useSelector(state => state?.login?.watchFreestate)

  const loginUser = localStorage.getItem('login_user')
  const registerUser = localStorage.getItem('register_user')
  const hks = localStorage.getItem('hks')
  const appMetaDataVideo = useSelector(
    state => state?.initialReducer?.appMetaDataVideo
  )

  const videoTutorialData =
    appMetaDataVideo?.versionUpdate_tutorial_control_options &&
    JSON?.parse(appMetaDataVideo?.versionUpdate_tutorial_control_options)

  const railData = []
  const railDataKids = []

  useEffect(() => {
    dispatch(getEpgMenu())
    dispatch(getEpgVersion())
  }, [])

  // Normal profiles

  let count = 0
  var sortArray = []

  useEffect(() => {
    const userId = loginSuccess?.response?.user_id ?? ''
    const userStatus = loginSuccess?.response?.superhighlight ?? ''

    if (railsDataObject?.length) {
      const railsPromise = new Promise((resolve, reject) => {
        const railsDataPromise = []
        railsDataObject?.map(each =>
          each?.railsData?.map(data =>
            data?.map(every => {
              let index = (count += 1)
              if (every?.urls && every?.type != 'planselector') {
                const promise = fetch(
                  `${COMMON_URL.BASE_URL}/${every?.urls}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}&user_id=${userId}&user_status=${userStatus}`
                )
                  .then(async data => {
                    let value = await data.json()
                    railData.push({
                      page: each?.page,
                      code: each?.code,
                      dataObject: {
                        title: every?.title,
                        type: every?.type,
                        imageData: value?.response
                      },
                      index: index
                    })
                  })
                  .catch(error => {
                    railData.push({
                      page: each?.page,
                      code: each?.code,
                      dataObject: {
                        title: every?.title,
                        type: every?.type,
                        imageData: undefined
                      },
                      index: index
                    })
                  })
                railsDataPromise.push(promise)
              }
              else if (every?.type == 'planselector') {
                railData.push({
                  page: each?.page,
                  code: each?.code,
                  dataObject: {
                    title: every?.title,
                    type: every?.type,
                    imageData: every?.urls
                  },
                  index: index
                })
              }
              else {
                railData.push({
                  page: each?.page,
                  code: each?.code,
                  dataObject: {
                    title: every?.title,
                    type: every?.type,
                    imageData: undefined
                  },
                  index: index
                })
              }
            })

          )
        )
        Promise.all(railsDataPromise).then(() => {
          setTimeout(() => {
            resolve(railData)
          }, 500)
        })
      })
      railsPromise.then(data => {
        (sortArray = [...data].sort((a, b) => a.index - b.index)),
          dispatch(getLevel(sortArray))
      })
    }
  }, [railsDataObject, loginSuccess])

  //kids profiles

  let countKids = 0
  var sortArrayKids = []

  useEffect(() => {
    if (railsDataKidsObject?.length) {
      const railsPromiseKids = new Promise((resolve, reject) => {
        const railsDataPromiseKids = []
        railsDataKidsObject?.map(each =>
          each?.railsData?.map(data =>
            data?.map(every => {
              let index = (countKids += 1)
              if (every?.urls) {
                const promiseKids = fetch(
                  `${COMMON_URL.BASE_URL}/${every?.urls}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}`
                )
                  .then(async data => {
                    let value = await data.json()
                    railDataKids.push({
                      page: each?.page,
                      code: each?.code,
                      dataObject: {
                        title: every?.title,
                        type: every?.type,
                        imageData: value?.response
                      },
                      index: index
                    })
                  })
                  .catch(error => {
                    railDataKids.push({
                      page: each?.page,
                      code: each?.code,
                      dataObject: {
                        title: every?.title,
                        type: every?.type,
                        imageData: undefined
                      },
                      index: index
                    })
                  })
                railsDataPromiseKids.push(promiseKids)
              }
              else {
                railDataKids.push({
                  page: each?.page,
                  code: each?.code,
                  dataObject: {
                    title: every?.title,
                    type: every?.type,
                    imageData: undefined
                  },
                  index: index
                })
              }
            })
          )
        )
        Promise.all(railsDataPromiseKids).then(() => {
          setTimeout(() => {
            resolve(railDataKids)
          }, 500)
        })
      })
      railsPromiseKids.then(data => {
        (sortArrayKids = [...data].sort((a, b) => a.index - b.index)),
          dispatch(getLevelKids(sortArrayKids))
        setKidsState(true)
      })
    }
    else if (!railsDataKidsObject?.length) {
      setKidsState(true)
    }
  }, [railsDataKidsObject])

  useEffect(() => {
    if (levelData?.length && kidsState) {
     const isGuest = localStorage.getItem('isWatchFreeUser')
   
     
    if (loginUser) {
        const tutorialShown = localStorage.getItem('isLoginUserTutorialShown')
        // If not shown yet
        if (!tutorialShown) {
          localStorage.setItem('showTutorialAfterWatchProfile', 'true')
        }
        navigate('/watchprofile', { 
          state: { 
            data: '', 
            seriesEpisodeData: state?.seriesEpisodeData, 
            fromDetailsPage: state?.fromDetailsPage,
            pageName: state?.pageName
          }, replace: true })
      } else if( watchFree && !isGuest){
          localStorage.setItem('isWatchFreeUser', 'true')
          dispatch(getIsLoggedinV1({HKS:hks}))
          dispatch(setSkeltonLoading(false))
          videoTutorialData &&
          videoTutorialData?.[region] &&
          navigate('/videoTutorial', { 
            state: { 
              data: '', 
              seriesEpisodeData: state?.seriesEpisodeData, 
              fromDetailsPage: state?.fromDetailsPage,
              pageName: state?.pageName
            }
          })
      } else if (registerUser){
        dispatch(getIsLoggedinV1({HKS:hks}))
        dispatch(setSkeltonLoading(false))
        videoTutorialData &&
          videoTutorialData?.[region] &&
          navigate('/videoTutorial', { 
            state: { 
              data: '', 
              seriesEpisodeData: state?.seriesEpisodeData, 
              fromDetailsPage: state?.fromDetailsPage,
              pageName: state?.pageName
            }
          })
        if (!videoTutorialData) {
          switch (true) {
            case state?.fromDetailsPage: 
              navigate('/watchprofile', { 
                state: { 
                  data: '', 
                  seriesEpisodeData: state?.seriesEpisodeData, 
                  fromDetailsPage: state?.fromDetailsPage,
                  pageName: state?.pageName
                }, replace: true })
              break
            case anonymousUser?.page == 'livePlayer':
              dispatch(getIsLoggedinV1(false))
              dispatch(setSkeltonLoading(false))
              navigate('/livePlayer', {
                state: { showControls: 'live', grid: anonymousUser?.grid }
              })
              break
            case vcardSeriesDetails?.page == 'series':
              navigate('/series', {
                state: { data: vcardSeriesDetails?.vodSeries }
              })
              break
            case vcardDetails?.page == 'movies':
              navigate('/movies', {
                state: { vodData: vcardDetails?.vodMoviesData }
              })
              break
            case vcardSeriesDetails?.playerpage == 'playerrecord':
              navigate('/series', {
                state: { data: vcardSeriesDetails?.playerepisode }
              })
              break
            default:
              dispatch(getNavTabValue('homeuser'))
              dispatch(getNavBarClicked(true))
              navigate('/home')
              break
          }
        }
      } else {
        dispatch(getNavTabValue('homeuser'))
        dispatch(getNavBarClicked(true))
        navigate('/home')
      }
    }
    localStorage.removeItem('miscontenidos')
  }, [levelData, levelKidsData, kidsState])

  return (
    <div className="container-splash">
      <img className="splash" src="images/claro-video-logo.png" />
      <Lottie loop animationData={animationData} play />
    </div>
  )
}
export default React.memo(WelcomeScreen)
