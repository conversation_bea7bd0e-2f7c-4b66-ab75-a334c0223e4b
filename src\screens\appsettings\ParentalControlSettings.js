import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import './Settings.scss';
import { getRemindControlPin, getLockedChannelsList, getControlPinStatus, getControlPin, getClearModifyControlPin } from "../../store/slices/settingsSlice";
import LockedChannelList from "./HelpAndSettings/LockedChannelList";
import { NOT_APPLICABLE } from "../../GoogleAnalyticsConstants";
import { COMMON_URL } from "../../utils/environment";

const ParentalControlSettings = (props) => {

	const navigate = useNavigate()
	const dispatch = useDispatch()
	const { state } = useLocation()

	const region = localStorage.getItem('region')

	const [title, setTitle] = useState('')
	const [screenName, setScreenName] = useState('parentalControl')
	const contentDataRef = useRef(null)

	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
	const securityPinCheck = useSelector(state => state?.settingsReducer?.controlPin)
	const remindSecurityPin = useSelector(state => state?.settingsReducer?.remindControlPin?.response)
	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
	const controlPinStatus= useSelector(state => state?.settingsReducer?.controlPinStatus?.data)
	const modifyControlPin = useSelector(state => state?.settingsReducer?.modifyControlPin?.data)

	const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const apilanguage = translations?.language?.[region]

    const subscriptionDetails =
      userDetails?.paywayProfile?.subscriptions?.reduce(
        (resultant, value, index) =>
          index == 0 ? resultant + value?.key : resultant + ', ' + value?.key,
        ''
      )

	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	}

	const showSelectedAge = () => {
		const text = props?.selectedValue ?? controlPinStatus?.pin_parental?.info?.value;
		switch (text) {
			case 20:
			  return `${truncateText('profile_pin_clasification_label', 30)} ${truncateText('profile_pin_clasification_code_pg', 5)}`
			case 30:
			  return `${truncateText('profile_pin_clasification_label', 30)} ${truncateText('profile_pin_clasification_code_pg13', 10)}`
			case 40:
			  return `${truncateText('profile_pin_clasification_label', 30)} ${truncateText('profile_pin_clasification_code_r', 5)}`
			case 50:
			  return `${truncateText('profile_pin_clasification_label', 30)} ${truncateText('profile_pin_clasification_code_nc', 10)}`
			default:
			  return `${truncateText('profile_pin_clasification_label', 30)} ${truncateText('profile_pin_clasification_code_pg', 10)}`
		}
	}

	const handleChangePin = () => {
		navigate('/settings/profile-settings/change-pin', {
			state: {
				pageName: 'parentalControl',
				gaContentData: contentDataRef.current
			}
		})
	}

	const handleDeactivatePin = () => {
		navigate('/settings/profile-settings/deactivate-pin', {
			state: {
				pageName: 'parentalControl',
				gaContentData: contentDataRef.current
			}
		})
	}

	const handleBlockChannelModificationClick = () => {
		navigate('/settings/profile-settings/check-pin', {
			state: {
				pageName: 'lockChannelModification',
				gaContentData: contentDataRef.current
			}
		})
	}

	const handlePinCreate = () => {
		navigate('/my-settings/help-And-Settings/security-pin/configure', {
			state: {
				data: 'Create',
				purchase: 0,
				rating: state?.rating,
				pageName: 'parental-control',
				returnPage: state?.returnPageName,
				item: state?.item,
				gaContentData: contentDataRef.current
			},
			replace: true
		})
	}

	const handleBlockChannelToggle = (e, value, pageNameType) => {
		e.preventDefault()
		navigate('/settings/profile-settings/check-pin', {
			state: {
				pageName: 'lockChannelToggle',
				subType: pageNameType,
				blockValue: value,
				returnPage: state?.returnPageName,
				blockChannelData: state?.item,
				gaContentData: contentDataRef.current
			}
		})
	}

	const handleContentClassification = () => {
		navigate('/settings/profile-settings/check-pin', {
			state: {
				pageName: 'contentClassification',
				gaContentData: contentDataRef.current
			}
		})
	}

	const handleClassificationToggle = (e, value, parentalCode) => {
		e.preventDefault()
		navigate('/settings/profile-settings/check-pin', {
			state: {
				pageName: 'classificationToggle',
				classificationValue: value,
				parentalCode: parentalCode,
				gaContentData: contentDataRef.current
			}
		})
	}

	const handleForgotPin = () => {
		const payload = {
			hks: userDetails?.session_stringvalue,
			user_hash: userDetails?.session_userhash
		}
		dispatch(getRemindControlPin(payload))
	}

	useEffect(() => {
		dispatch(
			getLockedChannelsList({
				hks: userDetails?.session_stringvalue,
				user_hash: userDetails?.session_userhash,
				loader: true
			})
		)
		state?.subPage && setScreenName(state?.subPage)
		contentDataRef.current = {
			user_id:
				userDetails?.user_id,
			parent_id: userDetails?.parent_id,
			sign_up_method: 'correo electronico',
			suscriptions: subscriptionDetails?.toLowerCase(),
			user_type: 'registrado',
			device: COMMON_URL?.device_type,
			device_model: COMMON_URL?.device_model,
			device_name: COMMON_URL?.device_name,
			authpn: COMMON_URL?.authpn,
			content_subsection: NOT_APPLICABLE,
			content_section: 'player',
			country:
				userDetails?.country_code?.toLowerCase(),
			content_id: NOT_APPLICABLE,
			content_name: NOT_APPLICABLE,
			content_type: NOT_APPLICABLE,
			content_category: NOT_APPLICABLE,
			content_episode: NOT_APPLICABLE,
			modulo_name: 'elige un pin de seguridad de 6 numeros para continuar',
			provider: NOT_APPLICABLE
		}
		return () => {
			contentDataRef.current = null
		}
	}, [])

	useEffect(() => {
		const payload = {
			hks: userDetails?.session_stringvalue,
			user_id: userDetails?.user_id,
			user_token: userDetails?.user_token
		}
		dispatch(getControlPin(payload))
		dispatch(getControlPinStatus(payload))
		dispatch(getClearModifyControlPin())
	}, [modifyControlPin])

	useEffect(() => {
		securityPinCheck?.response?.hashed_code && setTitle('pinCreated')
	}, [securityPinCheck])

	useEffect(() => {
		if (remindSecurityPin) {
			navigate('/PinConfirmation', {
				state: {
					pageName: 'parentalControl',
					gaContentData: contentDataRef.current
				}
			})
		}
	}, [remindSecurityPin])

	useEffect(() => {
		title != 'pinCreated' && document.getElementById('createPin')?.focus()
		title == 'pinCreated' && document.getElementById('changePin')?.focus()
		state?.focusElement == 'classificationPG' && document.getElementById('contentClassification')?.focus()
		state?.focusElement == 'lockChannelModificar' && document.getElementById('blockChannelClick')?.focus()
		state?.focusElement == 'lockToggle' && document.getElementById('lockToggleButton')?.focus()
		state?.focusElement == 'classificationToggle' && document.getElementById('classificationToggleActive')?.focus()
		state?.focusElement == 'deactivateButton' && document.getElementById('deactivatePin')?.focus()
		state?.focusElement == 'remindPin' && document.getElementById('forgetPin')?.focus()
	}, [title, state])


	return (
		<div className="parental-control-settings">
			{screenName == 'parentalControl' ?
				<>
					<div className="parental-sub-div">
						<span className="parental-control-title">{truncateText('parental_control_tv_config', 30)}</span>
						{title == 'pinCreated' &&
							<div>
								<button className="parental-control-button focusable" id='changePin' onClick={handleChangePin}>
									<span className="parental-control-button-contents">{truncateText('profiles_settings_parentalControl_subtitle_edit', 20)}</span>
								</button>
								<button className="parental-control-button focusable" id='forgetPin' onClick={handleForgotPin}>
									<span className="parental-control-button-contents focusable">{truncateText('profiles_settings_parentalControl_subtitle_reminder', 20)}</span>
								</button>
								<button className="parental-control-button-deactivate focusable" id='deactivatePin' onClick={handleDeactivatePin}>
									<span className="parental-control-button-contents">{truncateText('profiles_settings_parentalControl_subtitle_disable', 20)}</span>
								</button>
							</div>}
					</div>
					<hr className="horizontal-line" />

					<div className="parental-control-div">
						<div className="parental-control-sub-div">
							<span className="parental-control-sub-title">{truncateText('channel_parental_control_settings_title', 30)}</span>
							<span className="parental-control-description">{truncateText('channel_parental_control_settings_description', 200)}</span>
						</div>
						<div className={title == 'pinCreated' && controlPinStatus?.pin_channel?.status == 1 ? "button-div" : 'no-pin-button-div'}>
							{title == 'pinCreated' && controlPinStatus?.pin_channel?.status == 1 &&
								<button className="sub-button focusable" id='blockChannelClick' onClick={handleBlockChannelModificationClick}>
									<span className="sub-button-contents">{truncateText('content_parental_control_settings_button_text', 30)}</span>
								</button>}
							{title == 'pinCreated' && controlPinStatus?.pin_channel?.status == 1 ?
								<button className="toggle-activated focusable" id='lockToggleButton' onClick={(e) => handleBlockChannelToggle(e, 0, 'lockChannelToggleOff')} >
									<span className="toggle-activated-circle"></span>
								</button>
								:
								<button className="toggle-button focusable" id='createPin' onClick={title == 'pinCreated' ? (e) => handleBlockChannelToggle(e, 1, 'lockChannelToggleOn') : handlePinCreate}>
									<span className="toggle-button-circle"></span>
								</button>
							}
						</div>
					</div>

					<span className="parental-control-title">{truncateText('parental_control_content_config', 30)}</span>
					<hr className="horizontal-line" />

					<div className="parental-control-div">
						<div className="parental-control-sub-div">
							<span className="parental-control-sub-title">{truncateText('content_parental_control_settings_title', 30)}</span>
							<span className="parental-control-description-classfication" >{truncateText('content_parental_control_settings_description', 150)}</span>
						</div>
						<div className={title == 'pinCreated' && controlPinStatus?.pin_parental?.status == 1 ? "button-div" : 'no-pin-button-div'}>
							{title == 'pinCreated' && controlPinStatus?.pin_parental?.status == 1 &&
								<button className="sub-button focusable" id='contentClassification' onClick={handleContentClassification}>
									<span className="sub-button-contents">{showSelectedAge()}</span>
									<span className="dropdown-sub-button">
										<img
											className="dropdown-sub-button-img"
											src="images/LiveTv/ic_chevron_down.png"
											alt="Dropdown chevron"
										/>
									</span>
								</button>}
							{title == 'pinCreated' && controlPinStatus?.pin_parental?.status == 1 ?
								<button className="toggle-activated focusable" id='classificationToggleActive' onClick={(e) => handleClassificationToggle(e, 0, 0)} >
									<span className="toggle-activated-circle"></span>
								</button>
								:
								<button className="toggle-button focusable" id='classificationToggle' onClick={title == 'pinCreated' ? (e) => handleClassificationToggle(e, 1, 30) : handlePinCreate}>
									<span className="toggle-button-circle"></span>
								</button>
							}
						</div>
					</div>
				</>
				:
				<LockedChannelList setScreenName={setScreenName} />
			}
		</div>
	)

}

export default React.memo(ParentalControlSettings)