import React, { useEffect, useCallback } from 'react'
import '../../styles/MosaicBottomBar.scss'
import { useNavigate } from 'react-router-dom'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'
function MosaicBottomBar() {
  const navigate = useNavigate()
  const keypresshandler = event => {
  const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode) {
      navigate('/livePlayer')
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 406 || keycode == 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 121)
) {
      navigate('/livePlayer')
    }
  }

  return (
    <div className="mosaic-bottom-bar-container">
      <div className="mosaic-bottom-bar-item-conatiner">
        <div className="mosaic-bottom-bar-item">
          {' '}
          <img
            src={'images/Mosaic_Icons/icon_complete_guide.png'}
            alt="complete guide"
          />
        </div>
        <div className="mosaic-bottom-bar-item">
          VER CANALES EN GUÍA COMPLETA
        </div>
      </div>
    </div>
  )
}

export default MosaicBottomBar
