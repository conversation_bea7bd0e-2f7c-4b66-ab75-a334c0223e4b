@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

.app {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px;

  .epiccard {
    width: 400px;
    height: 550px;
    background: #eee;
    opacity: 0.4;
    border-radius: 10px;
    box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);

    .epiccards__image {
      position: relative;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      height: 300px;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      background-image: url('../../tv/images/landscape_card.png');
    }

    .epiccards__content {
      padding: 20px;

      .L1 {
        height: 30px;
      }

      .L2 {
        height: 50px;
        width: 80%;
      }
    }

    .epiccards__image,
    .L1,
    .L2 {
      background: linear-gradient(110deg, #a09d9d 8%, #a5a0a0 18%, #989696 33%);
      border-radius: 8px;
      background-size: 200% 100%;
      animation: 0.8s shine linear infinite;
    }
  }
}

@keyframes shine {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}


.vod-description-page-shimmer {
  /* padding: 20px; */
  background-color: #242424;
  position: relative;
  background-repeat: no-repeat;
  background-image: url("../../tv/images/hero_banner.png");

  height: 378px;
  box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);

  vod-description-page-header {
    height: 20px;
    width: 50%;
    height: 15%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 50px;
    animation: vodloadingAnimation 0.8s infinite;
  }

  .vod-description-page-subtitle {
    width: 50%;
    height: 10%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 40px;
    animation: vodloadingAnimation 0.8s infinite;
    margin: 10px 0px 10px 0px;
  }

  .vod-description-page-duration {
    width: 25%;
    height: 5%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 40px;
    animation: vodloadingAnimation 0.8s infinite;
  }

  .vod-page-description {
    width: 50%;
    height: 30%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 40px;
    animation: vodloadingAnimation 0.8s infinite;
    margin: 10px 0px 10px 0px;
  }

  @media (min-width: 1920px) and (max-width: 2560px) {
    .vod-grid-shimmer {
      width: 1920px;
    }

    .vod-description-page-shimmer {
      height: 588px;
      /* width: 1920px; */
      padding: 50px 0px 0px 50px;
    }

    .vod-description-page-header {
      width: 50%;
      height: 15%;

    }

    .vod-description-page-subtitle {
      width: 50%;
      height: 5%;
      margin: 20px 0px 20px 0px;
    }

    .vod-description-page-duration {
      width: 25%;
      height: 5%;
    }

    .vod-page-description {
      width: 50%;
      height: 30%;
      margin: 20px 0px 20px 0px;
    }

    .vod-description-page-guide {
      width: 25%;
      height: 5%;
    }
  }
}