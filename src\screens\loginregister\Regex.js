export const email=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export const phone="(^((\\(\\d{3}\\))|\\d{3})[- .]?\\d{3}[- .]?\\d{4}$)";

export const emailPattern = RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i)
export const passwordPattern = RegExp(/^(?=.*\d)(?=.*[`~!@#$%^&*()\-_+={}\[\]<,.>?/"'|\\;:])(?=.*[a-zA-Z]).{8,}$/)
export const numberCheckPattern = RegExp(/[-/+/?/./`/~/,/{/}/[!/|/@/#/$/%/^/&/*/(/)/_/>/</:/;/'/"/=A-Za-z]/g)
export const changeNumberPattern = RegExp(/[-/?/./`/~/,/{/}/[!/|/@/#/$/%/^/&/*/(/)/_/>/</:/;/'/"/=A-Za-z]/g)
export const errorMailPattern = RegExp(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i)