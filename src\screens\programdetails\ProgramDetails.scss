@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

.program-details-container {
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  display: flex;
  font-family: <PERSON><PERSON>;
  position: fixed;
  top: 0px;
  z-index: 1;//Adding to fix issue of subtitles overlap 
}

// program description
.program-description {
  width: 1232px;
  height: 1080px;
  background: rgba(0, 0, 0, 0.7);
  padding: 56px 0px 176px 80px;

  & .program-details-back-indicator {
    display: flex;
    height: 48px;
    width: 290px;
    border-radius: 6.6px;
    background-color: #2e303d;
    align-items: center;
    float: left;

    &:focus {
      background-color: #eb0045;
    }

    & .program-details-yellow-indicator {
      height: 20px;
      width: 20px;
      margin-left: 24px;
      margin-right: 24px;
    }

    & .program-details-back-image {
      height: 24px;
      width: 30px;
      margin-right: 24px;
    }

    & .program-details-back-text {
      height: 30px;
      width: 146px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 29.04px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 29.04px;
    }
  }

  & .program-details-title {
    width: 1072px;
    height: 76px;
    font-family: 'Roboto Bold';
    font-size: 65px;
    line-height: 71px;
    color: #ffffff;
}

.program-title1{
  width:1072px;
  font-family: 'Roboto Bold';
  font-size: 65px;
  line-height: 71px;
  color: #ffffff;
}

  & .program-status-label {
    height: 32px;
    font: 24px 'Roboto Medium';
    line-height: 16px;
    color: #ffffff;
    border-radius: 6px;
    display: flex;
    }
    
.program-details-category{
  margin-top: 24px;
  height: 32px;
}
  & .program-category,
  & .program-broadcast-schedule,
  & .program-duration,
  & .program-age-alert {
    height: 32px;
    font: 28px 'Roboto';
    line-height: 32px;
    color: #ffffff;
    display: inline-grid;
    padding-left: 20px;
  }

  .program-duration{
    font-weight: bold;
  }

  .program-category{
    padding-left: 0px;
    padding-right: 20px;
    
    .no-content {
      margin: 0px;
      padding: 0px;
    }
  }

  .program-age-alert{
    border:1px solid #ffffff;
    padding: 5px;
  }


  & .program-year {
    width: 65px;
    height: 32px;
    font: 28px 'Roboto';
    line-height: 32px;
    color: #ffffff;
    display: inline-grid;
    padding-right: 20px;

    .no-content {
      margin: 0px;
      padding-left: 0px;
    }
  }

  & .program-synopsis {
    width: 1072px;
    max-height: 160px;
    font: 32px 'Roboto Bold';
    line-height: 40px;
    color: #ffffff;
    font-weight: bold;
    text-align: left;
    overflow: scroll;
  }

  & .program-description-details {
    font: 26px 'Roboto';
    color: #ffffff;
    line-height: 32px;
  }

  .program-description-container{
    position: relative;
    top: 90px;
  }

  .program-description-title{
    font-weight: bold;
  }
  
  .extra-details{
   margin-left: 16px;
  }
}

// program options
$roboto-font: Roboto;
$roboto-bold: bold;
$font-size-regular: 32px;
$font-size-medium: 40px;

.program-options {
  $width: 608px;
  $height: 1080px;
  width: $width;
  height: $height;
  background-color: #282828;
  opacity: 0.95;

  .program-channel-details {
    display: flex;
    align-items: center;
    padding: 46px 0 0 32px;

    .program-channel-number {
      width: 72px;
      height: 48px;
      font: $font-size-medium $roboto-font $roboto-bold;
      line-height: 48px;
      color: #ffffff;
      font-weight: $roboto-bold;
    }

    .program-channel-logo {
      width: 400px;
      height: 100px;
      text-align: left;
      color: #ffffff;
      display: flex;
      align-items: flex-start;
      font: $font-size-medium $roboto-font $roboto-bold;
      font-weight: $roboto-bold;
    }
    
    .program-channel-logo img {
      height: 100px;
    }
  }

  .program-options-name {
    height: 104px;
    font: $font-size-regular $roboto-font;
    color: #999999;
    padding-left: 32px;
    padding-right: 32px;
    display: flex;
    align-items: center;
    width: calc(100% - 64px);
    box-sizing: border-box;
    
    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }
  }


  .program-record {
    display: flex;
    height: 112px;
    align-items: center;
    padding-left: 38px;

    .record-program {
      margin-left: 25px;
      font: $font-size-regular $roboto-font;
      line-height: 48px;
      color: #ffffff;
    }

    .record-icon {
      width: 52px;
      height: 40px;
    }

    &:focus {
      background-color: #c60000;
      width: $width;
    }
  }

  .program-options-item:focus {
    background-color: #c60000;
  }

  .channel-options {
    height: 104px;
    display: flex;
    align-items: center;
    padding-left: 32px;

    .channel-options-name {
      font: $font-size-regular $roboto-font;
      color: #999999;
      line-height: 40px;
      text-transform: uppercase;
    }
  }

  .channel-favorites {
    height: 104px;
    padding-left: 38px;
    display: flex;
    align-items: center;

    .channel-favorites-icon img {
      width: 52px;
      height: 45.07px;
    }

    .channel-favorites-text {
      font: $font-size-regular $roboto-font;
      line-height: 48px;
      color: #ffffff;
      padding-left: 22px;
    }

    &:focus {
      background-color: #c60000;
      width: $width;
    }
  }

  .program-language {
    height: 104px;
    padding-left: 38px;
    display: flex;
    align-items: center;

    .language-program-text {
      font: $font-size-regular $roboto-font;
      line-height: 48px;
      color: #ffffff;
      padding-left: 22px;
    }

    &:focus {
      background-color: #c60000;
      width: $width;
    }
  }

  .block-channel {
    height: 104px;
    padding-left: 14px;
    display: flex;
    align-items: center;

    .block-channel-text {
      font: $font-size-regular $roboto-font;
      color: #ffffff;
      padding-left: 11px;
      text-transform: capitalize;
    }
    .block-channel-icon {
      padding: 0px 20px 0px 31px;
      }
    
    &:focus {
      background-color: #c60000;
      width: $width;
    }
  }
}

.audio-icon {
  height: 34.67px;
  width: 48px;
}

.subtitle-audio-text {
  margin: 0 10px;
}

.selected-audio-icon {
  width: 52px;
  height: 40px;
  left: 200px;
  position: relative;
}

.channel-options-label {
  height: 104px;
  width: 496px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 32px;
  letter-spacing: 0;
  line-height: 40px;
  margin: 0 0 0 32px;
  display: flex;
  align-items: center;
}

.back-indicator {
  display: flex;
  height: 48px;
  width: 292px;
  border-radius: 6.6px;
  background-color: #2e303d;
  align-items: center;
  float: left;
  
  &:focus {
    background-color: #eb0045;
  }
  
  .yellow-indicator {
    height: 20px;
    width: 20px;
    margin-left: 24px;
    margin-right: 24px;
  }

  .back-image {
    height: 24px;
    width: 30px;
    margin-right: 24px;
  }

  .back-text {
    height: 30px;
    width: 146px;
    color: #ffffff;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
  }
}

.lastchannel-alert-message-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  width: 1331px;
  height: 80px;
  background-color: #2e303d;
  color: #ffffff;
  transform: translateX(-50%);
  border-radius: 18px;
}
 
.lastchannel-alert-message {
  display: flex;
  align-items: center;
  height: 80px;
  color: #ffffff;
  font-size: 32px;
  padding-left: 36px;
  font-family: Roboto;
 
  img {
    width: 30px;
    height: 30px;
    padding-right: 29.35px;
  }
}

.back-indicator-button {
  display: inline-block;
  color: #ffffff;
  width: 290px;
  height: 48px;
  border-radius: 6.6px;
  background-color: #2e303d;
  vertical-align: middle;
  

  &:focus {
    background-color: #c60000;
  }

  .yellow-indicator-button {
    width: 20px; 
    height: 20px; 
    padding: 0px 24px 0px 24px;
    vertical-align: middle;
  }

  .back-indicator-image {
    width: 35px; 
    height: 28px; 
    padding: 0px 24px 0px 0px;
    vertical-align: middle;
  }

  span {
    display: inline-block;
    vertical-align: middle;
    font-family: Roboto;
    font-weight: bold;
    font-size: 29px;
    color: #ffffff;
    width: 146px;
    height: 34px;
  }
}

.lock-alert-msg{
  font-weight: bold;
}

