import React, { useEffect } from "react";
import { useDispatch, useSelector } from 'react-redux'
import './MyDevice.scss'
import { getDevicesList, getLogoutAllDevices } from "../../../store/slices/settingsSlice";
import { useNavigate } from "react-router-dom";
import { pushScreenViewEvent } from "../../../GoogleAnalytics";

const MyDevices = () => {

	const dispatch = useDispatch()
	const navigate = useNavigate()

	const region = localStorage.getItem('region')

	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData);
	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response);
	const deviceList = useSelector(state => state?.settingsReducer?.devicesList?.response?.devices)
	const logoutAllDevices = useSelector(state => state?.settingsReducer?.logoutAllDevices?.response)

	const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const apilanguage = translations?.language?.[region]


	const handleLogoutAllDevices = () => {
		dispatch(getLogoutAllDevices({ 'hks': userDetails?.session_stringvalue, 'userId': userDetails?.parent_id }))
	}

	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	};

	const keypresshandler = (event) => {
		const keycode = event.keyCode
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow']);
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
			}
			handlesamsungkey(codes, keycode)
		} else {
			handleLgkey(keycode)
		}
	};

	const handlesamsungkey = (key, keycode) => {
		if (key.yellowcode === keycode || keycode === 10009) {
			navigate('/settings', { state: { activeTab: 'my-devices' } })
		}
	}

	const handleLgkey = (keycode) => {
		if (keycode == 405 || keycode === 461 || keycode == 'backClick') {
			navigate('/settings', { state: { activeTab: 'my-devices' } })
		}
	}

	useEffect(() => {
		dispatch(getDevicesList(({ 'hks': userDetails?.session_stringvalue })))
		pushScreenViewEvent({screenName:'my_devices',screenData:userDetails,prevScreenName:'settings' })
	}, [])

	useEffect(() => {
		document.addEventListener("keyup", keypresshandler)
		return () => {
			document.removeEventListener("keyup", keypresshandler)
		}
	}, [keypresshandler])

	return (
		<div className="app-css-my-devices">
			<div>
				<img src={'images/Logos_Claro_Video.svg'} className="claro-logo" alt="logo" />
				<button className="back-indicator-button-pin focusable"
					id="back-button"
					onClick={(e) => handleLgkey('backClick')}
				>
					<img className='yellow-indicator-button' src={'images/yellow_shortcut.png'} />
					<img className='back-indicator-image' src={'images/back_button.png'} />
					<span>{truncateText('atv_back_notification', 30)}</span>
				</button>
			</div>
			<span className="title-my-devices">{truncateText('MenuAvatar_MisDispositivos_Titulo_TextoTitulo', 40)}</span>
			{deviceList && deviceList?.length > 0 ?
				<>
					<span className="sub-text">{truncateText('MenuAvatar_MisDispositivos_TextoSubTitulo', 50)}</span>
					{!logoutAllDevices ?
						<div className="my-devices-div">
							<button className="logout-my-devices-button focusable" autoFocus={!logoutAllDevices} id='myDevicesLogout' onClick={handleLogoutAllDevices}>
								<span className="button-contents">{truncateText('MenuAvatar_MisDispositivos_Texto_TextoBoton', 23)}</span>
							</button>
						</div>
						:
						<div className="my-devices-logout-success">
							<span className="signout_title">{truncateText('MenuAvatar_MisDispositivos_TextoDeConfirmacion', 40)}</span>
							<img src={'images/greenTick.png'} className="green-tick-img" />
						</div>}
					<div className="subtitle-div">
						<span className="sub-title-2" >
						{truncateText('MenuAvatar_MisDispositivos_TextoComplementario3', 30)}
						{truncateText('MenuAvatar_MisDispositivos_TextoComplementario4', 30)}
						</span>
					</div>
				</>
				:
				<div className="no-devices-div">
					<span className="no-devices">{truncateText('MenuAvatar_MisDispositivos_SubTitulo', 30)}</span>
				</div>
			}
		</div>
	)

}

export default MyDevices;