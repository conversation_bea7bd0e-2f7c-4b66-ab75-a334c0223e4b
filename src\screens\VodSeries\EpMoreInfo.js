import React, { useEffect, useCallback, useRef, useState } from 'react'
import './EpMoreInfo.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import moment from 'moment'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useDispatch, useSelector } from 'react-redux'
import {
  getTalentSearchData,
  setShowApiFailureModal,
  setShowTalentModule,
  setVodReturnFocusById,
  removeVodReturnFocusById
} from '../../store/slices/SearchSlice'
import ErrorEventModule from '../talentSearch/ErrorEventModule'
import TalentSearch from '../talentSearch/TalentSearch'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

export const EpMoreInfo = () => {
  const navigate = useNavigate()
  const region = localStorage.getItem('region')
  const { state } = useLocation()
  const dispatch = useDispatch()

  const data = state?.episodeItem
  const genreData = state?.genreData
  const castData = state?.castData
  const episodeIndex = state?.indexepisode
  const episodeMoreInfo = state?.episodeMoreInfo
  const seriesData = state?.moreseriesData
  const userDetails = state?.userDetails
  const filterlist = state?.filterlist
  let talentSearchData = useSelector(state => state?.search?.talentSearchData)
  const apaAssetData = state?.apaAssetData
  const vodSeriesData = state?.vodSeasonsData

  const vodDuration = moment(data?.duration, 'HH:mm:ss').format(
    data?.duration?.startsWith('00') ? `mm [min]` : `H [h] mm [min]`
  )
  const containerRef = useRef(null)

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const [visibleEpisodeInfo, setVisibleEpisodeInfo] = useState(false)
  const [actorsData, setActorsData] = useState(false)

  const [talentSearchfield, setTalentSearchfield] = useState('')

  useEffect(() => {
    pushScreenViewEvent({screenName:'epmore_info', screenData: userDetails, prevScreenName:state?.pageName})
    SpatialNavigation.focus()
    window.scrollTo(0, 2)
    return () => {
      dispatch(getTalentSearchData())
      dispatch(setVodReturnFocusById())
      dispatch(removeVodReturnFocusById())
    }
  }, [])

  const Addproveedor = code => {
    const addIcon = code.toLowerCase() + '_icon'
    return apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const handleTranslationchange = useCallback(keyname => {
    if (!apilanguage?.[keyname]) {
      return keyname?.slice(0, 13) + '...'
    } else {
      return apilanguage?.[keyname]
        ?.toLowerCase()
        ?.replace(/(^|\s)\S/g, char => char.toUpperCase())
    }
  }, [])

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (event.key === 'ArrowDown') {
      setVisibleEpisodeInfo(true)
      setActorsData(false)
    }
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleEpmoreLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  window.addEventListener('scroll', function () {
    // Check if the page is scrolled to the bottom
    if (window.scrollY === document.body.scrollHeight) {
      // Scroll to the top smoothly
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  })

  const handlesamsungkey = (key, keycode) => {
    if (document.getElementById('info-layout-id')) {
      if (key.yellowcode === keycode || keycode === 10009) {
        navigate('/series', {
          state: {
            data: seriesData,
            episodeIndex: episodeIndex,
            moreInfoVariable: episodeMoreInfo,
            vodSeriesSeasons: vodSeriesData
          }
        })
      }
    }
  }

  const handleEpmoreLgkey = keycode => {
    if (document.getElementById('info-layout-id')) {
      if (keycode == 405 || keycode === 461 || keycode === 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
        navigate('/series', {
          state: {
            data: seriesData,
            episodeIndex: episodeIndex,
            moreInfoVariable: episodeMoreInfo,
            vodSeriesSeasons: vodSeriesData
          }
        })
      }
    }
  }

  const handlegenrechar = genrechar => {
    let GenrechartTitle = genrechar || ''
    // const maxCharacters = 27 // Total character limit for two lines
    const parts = GenrechartTitle.split(',') // Split the string by commas
    const result = parts.map((part, index) =>
      index < parts.length - 1 ? part.trim() + ', ' : part.trim()
    ) // Add space after each part (except the last one)
    let resultchar = result.join('')
    // if (resultchar.length > maxCharacters) { commented for future development
    //   // Truncate content to fit within two lines
    //   const truncatedText = resultchar.substring(0, maxCharacters)
    //   // Update the truncated content
    //   resultchar = truncatedText + '...'
    // }
    return resultchar
  }

  const handleEpisodemaintitle = epistitle => {
    let epistitletTitle = epistitle || ''
    const maxCharacters = 40 // Total character limit for two lines
    if (epistitletTitle.length > maxCharacters) {
      const truncatedText = epistitletTitle.substring(0, maxCharacters)
      epistitletTitle = truncatedText + '...'
    }
    return epistitletTitle
  }

  const handleMainTitle = tle => {
    let largecontentTitle = tle || ''
    const maxCharacters = 58
    if (largecontentTitle.length > maxCharacters) {
      const truncatedText = largecontentTitle.substring(0, maxCharacters)
      const lastSpaceIndex = truncatedText.lastIndexOf(' ')
      largecontentTitle = truncatedText.substring(0, lastSpaceIndex) + '...'
    }
    return largecontentTitle
  }

  useEffect(() => {
    if (talentSearchData) {
      talentSearchData?.length > 0
        ? dispatch(setShowTalentModule(true))
        : dispatch(setShowApiFailureModal(true))
    }
  }, [talentSearchData])

  const handleCastClick = (cast, indx) => {
    dispatch(setVodReturnFocusById(indx))
    setTalentSearchfield(
      cast?.first_name === undefined && cast?.last_name === undefined
        ? cast?.surname + ' ' + cast?.name
        : cast?.first_name + ' ' + cast?.last_name
    )
    dispatch(
      getTalentSearchData({
        hks: userDetails?.session_stringvalue,
        value: cast?.id,
        typemusic: 'album',
        provider_id: '3',
        filterlist: filterlist,
        field: 'TALENT'
      })
    )
  }

  const showTalentModule = useSelector(state => state?.search?.showTalentModule)
  const showApiFailureModal = useSelector(
    state => state?.search?.showApiFailureModal
  )
  const setVodReturnFocus = useSelector(
    state => state?.search?.setVodReturnFocus
  )
  const [currentButtonFocus, setCurrentButtonFocus] = useState('')

  useEffect(() => {
    const element = document.getElementById(setVodReturnFocus)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
        block: 'center'
      })
      element.focus()
      setCurrentButtonFocus(setVodReturnFocus)
      setVisibleEpisodeInfo(true)
      setActorsData(false)
    }
  }, [showTalentModule, showApiFailureModal])

  return (
    <>
      {showTalentModule ? (
        <TalentSearch
          filterlist={filterlist}
          talentSearchData={talentSearchData}
          talentSearchfield={talentSearchfield}
          Addproveedor={Addproveedor}
          setCurrentButtonFocus={setCurrentButtonFocus}
          userDeatilResp={userDetails}
          currentButtonFocus={currentButtonFocus}
          keyParam={'epmoreInfo'}
        />
      ) : showApiFailureModal ? (
        <ErrorEventModule
          pageName={'epmoreInfo'}
          setCurrentButtonFocus={setCurrentButtonFocus}
        />
      ) : (
        <div className="info-layouts" id="info-layout-id">
          <button
            className="backIndicator focusable"
            onFocus={() => {
              setActorsData(true)
            }}
            autoFocus={true}
            id="backButton"
            data-sn-down="#castcardImage0"
            data-sn-right={true}
            data-sn-left={true}
            onClick={() => {
              navigate('/series', {
                state: {
                  data: seriesData,
                  episodeIndex: episodeIndex,
                  moreInfoVariable: episodeMoreInfo
                }
              })
            }}
          >
            <img
              className="yellowIndicator"
              src={'images/yellow_shortcut.png'}
            />
            <img className="backImage" src={'images/back_button.png'} />
            <p className="backText">
              {translations?.language?.[region]?.atv_back_notification}
            </p>
          </button>
          <>
            {actorsData && (
              <div className="main-wrapper">
                <div
                  className="mainInfo-title"
                  style={{
                    alignItems: data?.title ? 'self-end' : 'flex-start'
                  }}
                >
                  {handleMainTitle(data?.title)}
                </div>
                <span className="episode-count">
                  {handleTranslationchange(
                    'vcard_access_abbreviationSeason_label'
                  )}{' '}
                  {data?.season_number} |{' '}
                  {apilanguage?.content_data_episodes_label?.slice(0, 8)}{' '}
                  {data?.episode_number}:{' '}
                  <span className="episode-name">
                    {handleEpisodemaintitle(data?.title_episode)}
                  </span>
                </span>
                <div className="episode-vod-details">
                  <button
                    id="descriptive"
                    className="episode-descriptive focusable"
                    ref={containerRef}
                    //onKeyDown={(e) => handlekeyDown(e)}
                    tabIndex={1}
                  >
                    <span className="episode-description">
                      {data?.description}
                    </span>
                  </button>
                  <div className="episode-detail">
                    <div className="main-duration">
                      <div className="episode-duration">
                        {`${handleTranslationchange('DURATION')}:`}
                      </div>
                      <div className="episode-duration-copy">{vodDuration}</div>
                    </div>
                    <div className="episode-year-div">
                      <div className="episode-year">{'Año de estreno:'}</div>
                      <div className="episode-year-copy">{data?.year}</div>
                    </div>
                    <div className="episode-generos-div">
                      <div className="episode-generos">
                        {`${handleTranslationchange('GENRE')}:`}
                      </div>
                      <div className="episode-generos-copy">
                        {handlegenrechar(genreData?.toString())}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="actors-list">
              <span className="actors-title">Reparto</span>
              {(actorsData || visibleEpisodeInfo) && (
                <div className="actor-container actor-casts">
                  {castData?.length > 0 &&
                    castData?.map((cast, index) => {
                      return (
                        <button
                          data-testid="castTest"
                          id={`castcardImage${index}`}
                          autoFocus={
                            setVodReturnFocus == `castcardImage${index}`
                          }
                          onFocus={() =>
                            document
                              .getElementById(`castcardImage${index}`)
                              ?.scrollIntoView({
                                behavior: 'smooth',
                                inline: 'end'
                              })
                          }
                          className={'actors-block focusable'}
                          onClick={() =>
                            handleCastClick(cast, `castcardImage${index}`)
                          }
                          key={index}
                        >
                          <div className="episode-castcard-img">
                            <LazyLoadImage
                              className="episode-cast-img"
                              src={
                                cast?.image
                                  ? cast?.image
                                  : 'images/Vod_Movies_Icons/cast_thumbnail.png'
                              }
                              key={index}
                              placeholderSrc={
                                'images/Vod_Movies_Icons/cast_thumbnail.png'
                              }
                            />
                            <div className="episode-cast-details">
                              <div className="episode-cast-role">
                                {cast?.role}
                              </div>
                              <div className="episode-cast-name">
                                <p>
                                  {!cast?.first_name && !cast?.last_name
                                    ? cast?.surname + ' ' + cast?.name
                                    : cast?.first_name + ' ' + cast?.last_name}
                                </p>
                              </div>
                            </div>
                          </div>
                        </button>
                      )
                    })}
                </div>
              )}
            </div>
          </>
        </div>
      )}
    </>
  )
}
