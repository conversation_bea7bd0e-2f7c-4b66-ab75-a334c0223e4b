import React, { useCallback,useEffect, useRef} from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { pushForgotPasswordSuccessEvent, pushScreenViewEvent } from '../../GoogleAnalytics'
import "../loginregister/Loginpage.scss";

const LoginForgotPasswordMessage = () => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const apaMetaData = useSelector((state) => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

  const region = localStorage.getItem('region')
  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const startTimeRef = useRef(null)

  const handleTranslationchange = useCallback((keyname) => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])
  
  const moduleName = handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_TextoTitulo1') + ' ' + handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_TextoTitulo2')
  const clickSign = (e) => {
    e.preventDefault()
    //GA for forgot_password event
    const engagement_time_msec = Date.now() - startTimeRef.current
    pushForgotPasswordSuccessEvent(
      handleTranslationchange(
        'Onboarding_AlertaOlvidasteContrasena_TextoBotonPrimario'
      )?.toLowerCase(),
      moduleName?.toLowerCase(),
      engagement_time_msec
    )
    startTimeRef.current = Date.now()
    navigate('/landing')
  }

  useEffect(()=>{
    startTimeRef.current = Date.now()
    pushScreenViewEvent({screenName:'login_forgot_password_message', screenData: userDetails, prevScreenName: 'login_forgot_password'})
    return () => {
      startTimeRef.current = null
    }
  },[])

  return (
    <div className="App">
      <div className="App-logo">
        <img src={'images/claro-video-logo.png'} className="logo-img" alt="logo" />
      </div>
      <div className="mail-notification">
        <p className='message-Header'>{handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_TextoTitulo1')}</p>
        <p className='message-subHeader'>{handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_TextoTitulo2')}</p>
        <p className='meaasge-Title'>{`${handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_Texto1')} ${handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_Texto2')} ${state?.value}`}</p>
      </div>
      <div className="container-ent">
        <div className="center-ent">
          <button className="message-next focusable" id='signinid' autoFocus onClick={(e) => clickSign(e)}>{handleTranslationchange('Onboarding_AlertaOlvidasteContrasena_TextoBotonPrimario')}</button>
        </div>
      </div>
    </div>
  );
}

export default LoginForgotPasswordMessage;
