import React, { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  getClearAllSettingsState,
  getRemindControlPin,
  getCheckControlPin,
  getModifyControlPin
} from '../../store/slices/settingsSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import AlphaNumericKeyboard from '../Keyboard/AlphaNumericKeboard'
import { getLockedChannelAdd } from '../../store/slices/settingsSlice'
import {
  getAlerts,
  addLiveTvSeriesRecording,
  addLiveTvEpisodeRecording,
  getClearRecordingState,
  delLiveTvSeriesRecording,
  delLiveTvEpisodeRecording
} from '../../store/slices/EpgSlice'
import './Settings.scss'
import { pushNewInteractionContentEvent } from '../../GoogleAnalytics'
import { interactionType, PROTECTED_CONTENT, TV } from '../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const CheckSecurityPin = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const pinRef = useRef([])

  const invisible = 'images/Icono_Onboarding_Close.png'
  const visible = 'images/Icono_Onboarding_Open.png'

  const [pin, setPin] = useState(new Array(6).fill(''))
  const [visbilityIcon, setVisibilityIcon] = useState(invisible)
  const [buttonDisable, setButtonDisable] = useState(true)
  const [pinInvalid, setPinInvalid] = useState('')
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [focusedIdx, setFocusedIdx] = useState(0)

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const checkSecurityPin = useSelector(
    state => state?.settingsReducer?.checkControlPin
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const controlPinStatus= useSelector(
    state => state?.settingsReducer?.controlPinStatus?.data
  )
  const modifyControlPin = useSelector(
    state => state?.settingsReducer?.modifyControlPin?.data
  )
  const remindSecurityPin = useSelector(
    state => state?.settingsReducer?.remindControlPin?.response
  )
  const lockchannelAdded = useSelector(
    state => state?.settingsReducer?.lockedChannelAdd
  )
  const addSeriesRecording = useSelector(
    state => state?.epg?.addSeriesRecording?.response
  )
  const addEpisodeRecording = useSelector(
    state => state?.epg?.addEpisodeRecording?.response
  )

   const delSeriesRecoding = useSelector(
      state => state?.epg?.deleteRecordingSeries?.response
    )
    const delEpisodeRecoding = useSelector(
      state => state?.epg?.deleteRecordingEpisode?.response
    )

  const recordingErrors = useSelector(state => state?.epg?.recordingErrors)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handlePinVisbility = () => {
    visbilityIcon === invisible
      ? setVisibilityIcon(visible)
      : setVisibilityIcon(invisible)
  }

   const handleOTPChange = (element, index) => {
    const value = element.value ?? element

    if (value == 'cl') {
      setPin(pin.map((d, idx) => (idx === focusedIdx ? '' : d)))
      setFocusedIdx(index === 0 ? index : index - 1)
      return
    }
    if (value == 'clr') {
      setPin(new Array(pin.length).fill(''))
      setFocusedIdx(0)
      setPinInvalid('')
      return 
    }     
    if (/^\d$/.test(value)) {
      setPin(pin.map((d, idx) => (idx === index ? value : d)))
      setFocusedIdx(index === 5 ? index : index + 1)
      } else {
        setPin(pin.map((d, idx) => (idx === index ? '' : d)))
        setPinInvalid('')
      }
      setPinInvalid('')
   }
  const handleCheckPin = () => {
    pushNewInteractionContentEvent(
      state?.gaContentData, 
      PROTECTED_CONTENT, 
      TV, 
      interactionType?.SIGUIENTE
    )
    const payload = {
      controlPIN: pin.join(''),
      userId: userDetails?.user_id,
      hks: userDetails?.session_stringvalue,
      parental: 1
    }
    dispatch(getCheckControlPin(payload))
  }

  const handleForgotPin = () => {
    pushNewInteractionContentEvent(
      state?.gaContentData, 
      PROTECTED_CONTENT, 
      TV, 
      interactionType?.OLVIDASTE_TU_PIN_DE_SEGURIDAD
    )
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_hash: userDetails?.session_userhash
    }
    dispatch(getRemindControlPin(payload))
  }

  const handleBlockChannelToggle = () => {
    dispatch(
      getModifyControlPin({
        hks: userDetails?.session_stringvalue,
        userId: userDetails?.user_id,
        channelPin: state?.blockValue,
        parentalPin: controlPinStatus?.pin_parental?.status,
        purchasePin: controlPinStatus?.pin_purchase?.status,
        accessParentalCode: controlPinStatus?.pin_parental?.info?.value,
        userToken: userDetails?.user_token,
        userPin: pin.join('')
      })
    )
    if (state?.returnPage) {
      const payload = {
        hks: userDetails?.session_stringvalue,
        group_id:
          state?.blockChannelData?.group_id ??
          state?.blockChannelData?.common?.id,
        user_hash: userDetails?.session_userhash
      }
      dispatch(getLockedChannelAdd(payload))
      const alertDetailLabel =
        translations?.language?.[region]?.lockChannel_alert_detail_label
      const message = {
        message1: `Canal ${state?.blockChannelData?.name} ${state?.blockChannelData?.number} ${alertDetailLabel}`,
        message2: ` | ${truncateText(
          'TvEnVivo_Notificacion_TextoCuerpo_CanalBloqueado',
          70
        )}`
      }
      dispatch(
        getAlerts({
          image: 'images/lock_icon_liveTV.png',
          message: `${message.message1}${message.message2}`,
          status: 'lock'
        })
      )
      navigate('/livePlayer', {
        state: { showControls: 'live' },
        replace: true
      })
    }
  }

  const handleClassificationToggle = () => {
    dispatch(
      getModifyControlPin({
        hks: userDetails?.session_stringvalue,
        userId: userDetails?.user_id,
        channelPin: controlPinStatus?.pin_channel?.status,
        parentalPin: state?.classificationValue,
        purchasePin: controlPinStatus?.pin_purchase?.status,
        accessParentalCode: state?.parentalCode,
        userToken: userDetails?.user_token,
        userPin: pin.join('')
      })
    )
  }

  const handleSeriesRecording = e => {
    if(state?.status == 'unlockEvent'){
    dispatch(delLiveTvSeriesRecording(state?.payload))
    }else{
      dispatch(addLiveTvSeriesRecording(state?.payload))
    }
  }

  const handleEpisodeRecording = e => {
    if(state?.status == 'unlockEvent'){
     dispatch(delLiveTvEpisodeRecording(state?.payload))
      }else{
    dispatch(addLiveTvEpisodeRecording(state?.payload))
      }
  }

  useEffect(() => {
    if (addSeriesRecording?.success) {
      if (state?.eventStatus == 'Present Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabando', 30)} |
            ${apilanguage?.recording_alert_description_presentSeason_label}`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else if (state?.eventStatus == 'Past Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabado', 30)} |
            ${apilanguage?.recording_alert_description_pastSeason_label}`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_PorGrabar',
              30
            )} |
            ${apilanguage?.recording_alert_description_futureSeason_label}`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      }
      dispatch(getClearRecordingState())
      navigate('/livePlayer', {
        state: {
          showControls: 'live',
          screenName: 'livePlayerRecord'
        },
        replace: true
      })
    }else if (delSeriesRecoding?.success) {
    if (state?.eventStatus == 'Present Event') {
            dispatch(
              getAlerts({
                message: `${truncateText(
                  'TvEnVivo_Notificacion_TextoTitulo_GrabacionCancelada',
                  30
                )} |
                La serie se elimino de la sección Mis contenidos`,
                image: 'images/Record_Icon.png',
                status: 'delete'
              })
            )
          } else if (state?.eventStatus == 'Past Event') {
            dispatch(
              getAlerts({
                message: `${truncateText(
                  'TvEnVivo_Notificacion_TextoTitulo_GrabacionEliminada'
                )} |
                ${truncateText(
                  'TvEnVivo_Notificacion_TextoCuerpo_GrabacionCancelada_Evento',
                  70
                )}`,
                image: 'images/Record_Icon.png',
                status: 'delete'
              })
            )
          } else {
            dispatch(
              getAlerts({
                message: `${translations?.language?.[region]?.cancelRecording_alert_title_futureEvent_label} |
                ${translations?.language?.[region]?.cancelRecording_alert_description_futureSeason_label}`,
                image: 'images/Record_Icon.png',
                status: 'delete'
              })
            )
          }
        
      dispatch(getClearRecordingState())
      navigate('/livePlayer', {
        state: {
          showControls: 'live',
          screenName: 'livePlayerRecord'
        },
        replace: true
      })
    } else if (addEpisodeRecording?.success) {
      if (state?.eventStatus == 'Present Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabando', 30)} |
            ${apilanguage?.recording_alert_description_presentEpisode_label}`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else if (state?.eventStatus == 'Past Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabado', 30)} |
            ${apilanguage?.recording_alert_description_presentEpisode_label}`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_PorGrabar',
              30
            )} |
            ${apilanguage?.recording_alert_description_futureEpisode_label}`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      }
      dispatch(getClearRecordingState())
      navigate('/livePlayer', {
        state: {
          showControls: 'live',
          screenName: 'livePlayerRecord'
        },
        replace: true
      })
    }else if (delEpisodeRecoding?.success) {
     if (state?.eventStatus == 'Present Event') {
             dispatch(
               getAlerts({
                 message: `${truncateText(
                   'TvEnVivo_Notificacion_TextoTitulo_GrabacionCancelada',
                   30
                 )} |
                 ${truncateText(
                   'TvEnVivo_Notificacion_TextoCuerpo_GrabacionCancelada_Episodio',
                   70
                 )}`,
                 image: 'images/Record_Icon.png',
                 status: 'delete'
               })
             )
           } else if (state?.eventStatus == 'Past Event') {
             dispatch(
               getAlerts({
                 message: `${truncateText(
                   'TvEnVivo_Notificacion_TextoTitulo_GrabacionEliminada',
                   30
                 )} |
                   ${
                     translations?.language?.[region]
                       ?.deleteRecording_alert_description_pastEpisode_label
                   }`,
                 image: 'images/Record_Icon.png',
                 status: 'delete'
               })
             )
           } else {
             dispatch(
               getAlerts({
                 message: `${translations?.language?.[region]?.cancelRecording_alert_title_futureEvent_label} |
                 ${translations?.language?.[region]?.cancelRecording_alert_description_episode_label}`,
                 image: 'images/Record_Icon.png',
                 status: 'delete'
               })
             )
           }
      dispatch(getClearRecordingState())
      navigate('/livePlayer', {
        state: {
          showControls: 'live',
          screenName: 'livePlayerRecord'
        },
        replace: true
      })
    }else if (recordingErrors?.errors?.[0]?.code == 'PLY_REC_00014'){
      setPinInvalid(checkSecurityPin?.errors)
      setPin(new Array(6).fill(''))
      document.getElementById('Key_0')?.focus()
      setFocusedIdx(0)
      dispatch(getClearRecordingState())
       dispatch(
            getAlerts({
              message1:`${truncateText(
                'PLY_REC_00014_title',
                30
              )} | `,
                      message: `
                      ${truncateText(
                        'PLY_REC_00014_message',
                        50
                      )}`,
                      image: 'images/warning.png',
                      status: 'storagealert'
                    })
                  )
            navigate('/livePlayer', {
              state: { showControls: 'live' },
              screenName: 'livePlayerRecord',
              replace: true
            })
    }
  }, [addSeriesRecording, addEpisodeRecording, recordingErrors, delEpisodeRecoding, delEpisodeRecoding])

  useEffect(() => {
    remindSecurityPin &&
      navigate('/PinConfirmation', {
        state: {
          pageName: 'pinCheck',
          returnPage: state?.pageName,
          gaContentData: state?.gaContentData
        }
      })
  }, [remindSecurityPin])

  useEffect(() => {
    modifyControlPin &&
      !state?.returnPage &&
      navigate('/settings/profile-settings', {
        state: { pageName: 'parentalControl' }
      })
    modifyControlPin &&
      state?.returnPage &&
      lockchannelAdded &&
      navigate('/livePlayer', {
        state: { showControls: 'live' },
        replace: true
      })
  }, [modifyControlPin])

  useEffect(() => {
    if (pin && pin[0]?.length > 0 && pin[1]?.length > 0 && pin[2]?.length > 0) {
      setButtonDisable(false)
    } else {
      setButtonDisable(true)
    }
    pin[5]?.length > 0 && document.getElementById('siguienteButton')?.focus()
  }, [pin])

  useEffect(() => {
    setKeyboardFocus(true)
    // const blinkerText = () => {
    //   if (document.querySelector('.pin-focused')) {
    //     let p = document.querySelector('.pin-focused').value
    //     if (p?.substr(-1, 1) == '|') {
    //       let removePipe = p?.replace('|', '')
    //       document.querySelector('.pin-focused').value = removePipe
    //     } else if (p?.length == 0) {
    //       document.querySelector('.pin-focused').value = p + '|'
    //     }
    //   }
    // }

    // const blinkerInterval = setInterval(blinkerText, 1000)
    // return () => {
    //   clearInterval(blinkerInterval)
    // }
  }, [])

  useEffect(() => {
    if (checkSecurityPin?.msg === 'OK') {
      dispatch(getClearAllSettingsState())
      state?.pageName == 'contentClassification' &&
        navigate('/settings/profile-settings/content-classification', {
          state: { pin: pin.join('') }
        })
      state?.pageName == 'lockChannelModification' &&
        navigate('/settings/profile-settings', {
          state: { pageName: 'parentalControl', subPage: 'lock' }
        })
      state?.pageName == 'lockChannelToggle' && handleBlockChannelToggle()
      state?.pageName == 'classificationToggle' && handleClassificationToggle()
      state?.pageName == 'livePlayerRecord' &&
        state?.recordType == 'Series' &&
        handleSeriesRecording()
      state?.pageName == 'livePlayerRecord' &&
        state?.recordType == 'Episode' &&
        handleEpisodeRecording()
    } else if (checkSecurityPin?.msg === 'ERROR') {
      setPinInvalid(checkSecurityPin?.errors)
      dispatch(getClearAllSettingsState())
      setPin(new Array(6).fill(''))
      setVisibilityIcon(invisible)
      document.getElementById('Key_0')?.focus()
      setButtonDisable(true)
      setFocusedIdx(0)
    }
  }, [checkSecurityPin])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      pushNewInteractionContentEvent(
        state?.gaContentData, 
        PROTECTED_CONTENT, 
        TV, 
        interactionType?.CANCELAR
      )
      state?.pageName == 'contentClassification' &&
        navigate('/settings/profile-settings', {
          state: {
            pageName: 'parentalControl',
            focusElement: 'classificationPG'
          }
        })
      state?.pageName == 'lockChannelModification' &&
        navigate('/settings/profile-settings', {
          state: {
            pageName: 'parentalControl',
            focusElement: 'lockChannelModificar'
          }
        })
      state?.pageName == 'lockChannelToggle' &&
        navigate('/settings/profile-settings', {
          state: { pageName: 'parentalControl', focusElement: 'lockToggle' }
        })
      state?.pageName == 'classificationToggle' &&
        navigate('/settings/profile-settings', {
          state: {
            pageName: 'parentalControl',
            focusElement: 'classificationToggle'
          }
        })
      state?.pageName == 'livePlayerRecord' &&
        navigate('/livePlayer', {
          state: {
            showControls: 'live',
            featureTag: 'future',
            screenName:'livePlayerRecord'
          },
          replace: true
        })
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode == 'backClick' || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      pushNewInteractionContentEvent(
        state?.gaContentData,
        PROTECTED_CONTENT,
        TV,
        interactionType?.CANCELAR
      )
      state?.pageName == 'contentClassification' &&
        navigate('/settings/profile-settings', {
          state: {
            pageName: 'parentalControl',
            focusElement: 'classificationPG'
          }
        })
      state?.pageName == 'lockChannelModification' &&
        navigate('/settings/profile-settings', {
          state: {
            pageName: 'parentalControl',
            focusElement: 'lockChannelModificar'
          }
        })
      state?.pageName == 'lockChannelToggle' &&
        navigate('/settings/profile-settings', {
          state: { pageName: 'parentalControl', focusElement: 'lockToggle' }
        })
      state?.pageName == 'classificationToggle' &&
        navigate('/settings/profile-settings', {
          state: {
            pageName: 'parentalControl',
            focusElement: 'classificationToggle'
          }
        })
      state?.pageName == 'livePlayerRecord' &&
        navigate('/livePlayer', {
          state: {
            showControls: 'live',
            featureTag: 'future',
            screenName:'livePlayerRecord'
          },
          replace: true
        })
    }
  }

  const handleTVRemoteKey = (event) => {    
    const key = event.key;
    if (/^\d$/.test(key)) {
      const currentIndex = pin.findIndex((digit) => digit === '');      
      if (currentIndex !== -1) {
        const newOtp = [...pin];
        newOtp[currentIndex] = key;
        key?.length == 1 && setPin(newOtp);
        if (currentIndex < 5) {
          setFocusedIdx(currentIndex + 1)
        }
      }
      setPinInvalid('')
    }    
  };

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
    handleTVRemoteKey(event);
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className="app-css-change-pin">
      <div className="check-pin-logo-container">
        <img className="check-pin-logo" src={'images/logo.png'} />
        <button
          className="back-indicator-button-pin focusable"
          id="backButton"
          onClick={e => handleLgkey('backClick')}
        >
          <img
            className="yellow-indicator-button"
            src={'images/yellow_shortcut.png'}
          />
          <img
            className="back-indicator-image"
            src={'images/back_button.png'}
          />
          <span>{truncateText('top_head_option_button_back', 30)}</span>
        </button>
      </div>
      <div className="left-container-div">
        <AlphaNumericKeyboard
          type="alphaNumeric"
          onChange={e => handleOTPChange(e, focusedIdx)}
          autoFocus={keyboardFocus}
          name={'security-pin'}
        />
      </div>
      <div className="right-container-div">
        <span className="pin-screen-title">
       { state?.pageName == 'livePlayerRecord' ? truncateText(
                  'lockChannel_alert_detail_label',
                  30
                ) : truncateText('setupPin_modal_title_label', 30)}  
         
        </span>
        <span className="pin-screen-sub-title pin-screen-sub-title-width">
          {state?.pageName == 'livePlayerRecord' ? (state?.status == 'unlockEvent' ? 'Ingresá tu PIN de seguridad de 4 a 6 caracteres para cancelar esta grabación' : 'Ingresá tu PIN de seguridad de 4 a 6 caracteres para grabar este programa.' 
               ) : state?.pageName == 'lockChannelModification' ?
            truncateText('modal_pin_change_channels_subtitle', 100)
            :
            state?.pageName == 'lockChannelToggle' && state?.subType === 'lockChannelToggleOn' ?
              truncateText('modal_pin_activate_button_subtitle', 100)
              :
              state?.pageName == 'lockChannelToggle' && state?.subType === 'lockChannelToggleOff' ?
                truncateText('modal_pin_deactivate_button_subtitle', 100)
                : 
                truncateText('modal_now_pin_subtitle', 100)}

        </span>

        <div className="right-container-extra-div">
          <>
            <div className="pin-box-div">
              {pin?.map((item, index) => {
                return (
                  <div key={index} className="pin-wrapper">
                  <input
                    style={
                      visbilityIcon === invisible
                        ? { fontSize: '128px' }
                        : { fontSize: '32px' }
                    }
                    className={
                      pinInvalid
                        ? 'pin-field-invalid'
                        : focusedIdx == index
                          ? 'pin-focused'
                          : 'pin-field'
                    }
                    type={'text'}
                    name="pin"
                    onKeyUp={e => !/[0-9]/.test(e.key) && e.preventDefault()}
                    id={`pin${index}`}
                    ref={ref => (pinRef.current[index] = ref)}
                    maxLength={1}
                    key={index}
                    value={
                      visbilityIcon === invisible && item?.length > 0
                        ? '*'
                        : item
                    }
                    onChange={e => handleOTPChange(e.target, index)}
                    inputMode="none"
                    readOnly
                    data-testid={`pin${index}`}
                  />
                   {/* Show blinking pipe cursor if input is focused and empty */}
                   {focusedIdx === index && item === '' && (
                      <span className="pin-cursor">|</span>
                    )}
                  </div>
                )
              })}
              <button
                onClick={handlePinVisbility}
                className="see-pin-button focusable"
                data-testid={`pinVisible`}
                id="seePin"
              >
                <img src={visbilityIcon} />
              </button>
            </div>

            {pinInvalid ? (
              <p className="pin-error">
                <span className="pin-error-contents">
                  {truncateText(
                    'lockChannel_tooltip_valid_label_validation',
                    50
                  )}
                </span>
              </p>
            ) : null}

            <button
              className="pin-screen-button focusable pin-screen-button-top"
              id="siguienteButton"
              disabled={buttonDisable}
              onClick={handleCheckPin}
            >
              {truncateText('bt_suscripcion_siguiente', 30)}
            </button>

            <button
              className="pin-screen-button-cancel focusable"
              id="cancelButton"
              onClick={e => handleLgkey('backClick')}
            >
              {truncateText('modal_pin_cancel_button', 30)}
            </button>

            <button
              onClick={handleForgotPin}
              className="forgot-pin-button focusable"
              id={'forgetPin'}
            >
              <p className="forgot-pin-content">
                {truncateText('modal_pin_forgot_pin', 35)}
              </p>
            </button>
          </>
        </div>
      </div>
    </div>
  )
}

export default React.memo(CheckSecurityPin)
