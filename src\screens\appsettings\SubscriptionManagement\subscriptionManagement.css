.App-Css{
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: #121212
}

.claro-logo{
	height: 34.36px;
  width: 169.64px;
	margin-top: 67px;
	margin-left: 90px;
}
/* 
.sub-desc{
    font: normal normal normal 30px/42px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
		display: flex;
		width: 1080px;
		text-align: center;
		margin-left: 21%;		
}

.active-cards{
	display: flex;
	margin-left: 5%;
	margin-top: 5%;
	width: 1828px;
	overflow-x: scroll;
}

.sub-div-active{
	display: flex;
	flex-direction: column;
	margin-right: 5%;
}
.active-focus-card{
	width: 448px;
	height: 300px;
	background: #212121 0% 0% no-repeat padding-box;
	border-radius: 8px;
	opacity: 1;	
}

.active-focus-card:focus{
	border: 3px solid #981C15;
	opacity: 1;
} */

/* .cards-sub-div{
	display: flex;
	justify-content: center;
	align-items: center;
} */

/* .logo-css{
	margin-top: 32px;
	margin-bottom: 18px;
	margin-left: 12%;
	width: 110px;
	height: 45px;
	opacity: 1;
} */
/* 
.price-css{
	width: 79px;
	margin-left: 7%;
	text-align: left;
	letter-spacing: -1.12px;
	color: #EEEEEE;
	opacity: 1;
	font: normal normal normal 34px Roboto;

}

.message{
	width: 136px;
	text-align: left;
	margin-left: 7%;
	letter-spacing: -0.81px;
	color: #EEEEEE;
	opacity: 1;
	font: normal normal normal 24px Roboto;

}

.card-desc-subs{
	letter-spacing: -0.65px;
	color: #ADADAD;
	opacity: 1;
	margin-block-start: 10px;
	text-align: center;
	font: normal normal normal 24px Roboto;

}

.card-desc-date{
	height: 37px;
	text-align: center;
	font: normal normal normal 22px/0px Roboto;
	letter-spacing: -0.76px;
	color: #FFFFFF;
	opacity: 1;
	margin-block-start: 2em;
} */
/* 
.card-desc-payment{
	letter-spacing: -0.76px;
	color: #EEEEEE;
	opacity: 1;
	height: 73px;
	font: normal normal normal 22px/0px Roboto;
	text-align: center;
	margin-block-start: 1em;
} */

/* .cancelButton{
	width: 448px;
	height: 72px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 7%;
	background: #2E303D 0% 0% no-repeat padding-box;
	border-radius: 44px;
	opacity: 1;
	font: normal normal normal 32px/45px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;

}

.cancelButton:focus {
	background: #981C15 0% 0% no-repeat padding-box;
} */

.cancel-title{
	text-align: center;
	font: normal normal normal 36px/30px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
}

.sub-div-cancel{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 5%;
}
.cancel-sub-info{
	width: 1134px;
	text-align: center;
	font: normal normal normal 30px/42px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin-left: 21%;
}

.cancel-cards{
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 
.add-subscriptions-div{
	display: flex;
	margin-left: 5%;
	width: 1783px;
	overflow-x: scroll;
}

.add-cards{
	margin-right: 5%;
	width: 253px;
}

.banner{
	width: 296px;
	position: relative;
}

.subs-meta-data{
	position: relative;
	bottom: 33%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-left: 18%;
}

.add-sub-logo{
	display: flex;
	height: 39px;
}

.pricing-info{
	text-align: center;
	letter-spacing: -0.98px;
	color: #FFFFFF;
	opacity: 1;
	font: normal normal normal 20px Roboto;
	display: block;
	margin-top: 25px;
}

.price-css{
	width: 68px;
	height: 41px;
	text-align: center;
	letter-spacing: -0.98px;
	color: #FFFFFF;
	opacity: 1;
}

.taxLabel{
	letter-spacing: -0.68px;
	color: #FFFFFF;
	opacity: 1;
	margin-top: 2%;
	font: normal normal normal 22px Roboto;
	text-align: center;
}

.freechargeStr{
	width: 127px;
	height: 54px;
	text-align: center;
	font: normal normal normal 22px Roboto;
	letter-spacing: -0.68px;
	color: #FFFFFF;
	opacity: 1;
	display: flex;
	margin-top: 15px;

}

.scope_description{
	height: 33px;
	text-align: left;
	letter-spacing: -0.87px;
	color: #EEEEEE;
	font: normal normal normal 28px/48px Roboto;
	opacity: 1;
}

.sub-buttons-div{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 20px;
}

.subs-button{
	height: 40px;
  width: 200px;
	opacity: 1;
	margin-bottom: 5%;
	border-radius: 4px;
	text-transform: uppercase;
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 16.1px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 19px;
  text-align: center;	
}

.subs-button:focus{
	height: 46.03px;
  width: 229.84px;
} */

.main-container-view{
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 5%;
}
.left-image-view{
	width: 413px;
	height: 619px;
}
.right-container-view-subscription{
	display: flex;
	flex-direction: column;
	justify-content: center;
	gap:6px;
	margin-left: 5%;
	
}

.buy-sub-logo{
	width: 188px;
}

.pricing-info-view-subs{
	height: 57px;
	text-align: left;
	font: normal normal normal 38px/48px Roboto;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 1;
}

.freechargeStr-view{
	text-align: left;
	letter-spacing: 0px;
	color: #FFF000;
	font: normal normal normal 28px/48px Roboto;
	text-shadow: 0px 2px 4px #00000033;
	opacity: 1;
}

.subs-button-view-screen{
	width: 501px;
	height: 69px;
	background: #981C15 0% 0% no-repeat padding-box;
	border-radius: 35px;
	opacity: 1;
	text-align: center;
	font: normal normal bold 28px/48px Roboto;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 1;
	margin-top: 45px;
}

.subs-button-view-screen:focus{
	background: #981C15 0% 0% no-repeat padding-box;
}

.text-payment{
	text-align: left;
	letter-spacing: 0px;
	color: #EEEEEE;
	font: normal normal normal 20px/21px Roboto;
	opacity: 0.7;
	margin-left: 10%;
}

.subscription-info{
	width: 838px;
	height: 65px;
	text-align: left;
	letter-spacing: 0px;
	font: normal normal normal 28px/48px Roboto;
	color: #EEEEEE;
	opacity: 0.7;
}

.back-indicator {
	display: flex;
	height: 48px;
	width: 292px;
	border-radius: 6.6px;
	background-color: #2E303D;
	align-items: center;
	float: right;
	margin-top: 7px;
	margin-right: 64px;
}
.yellow-indicator {
	height: 20px;
	width: 20px;
	margin-left: 24px;
	margin-right: 24px;
}

.back-image {
	height: 24px;
	width: 30px;
	margin-right: 24px;
}

.back-text {
	height: 30px;
	width: 146px;
	color: #FFFFFF;
	font-family: Roboto;
	font-size: 29.04px;
	font-weight: bold;
	letter-spacing: 0;
	line-height: 29.04px;
}