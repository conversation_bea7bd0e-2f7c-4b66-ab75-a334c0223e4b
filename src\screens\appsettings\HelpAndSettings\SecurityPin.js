import React, { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import './HelpAndSettings.scss'
import { useDispatch } from 'react-redux'
import {
  clearRemindPinSettingsState,
  getClearAllSettingsState
} from '../../../store/slices/settingsSlice'
import { useSelector } from 'react-redux'
import { CURRENT_PLATFORM } from '../../../utils/devicePlatform'

const SecurityPin = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()
  const region = localStorage.getItem('region')

  const securityPinCheck = useSelector(state => state?.settingsReducer?.controlPin)
  const disableSecurityPin = useSelector(state => state?.settingsReducer?.disableControlPin?.response?.profiles?.[0]?.parental?.active)
  const remindSecurityPin = useSelector(state => state?.settingsReducer?.remindControlPin?.response?.email_sent)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const [title, setTitle] = useState('')
  const [actPurchase, setActPurchase] = useState(0)
  const [showNotification, setShowNotification] = useState(false)
  const [notificationText, setNotificationText] = useState('')

  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  useEffect(() => {
    if (disableSecurityPin === false) {
      setNotificationText('disable')
      dispatch(getClearAllSettingsState())
      setShowNotification(true)
      setTimeout(() => {
        setShowNotification(false)
      }, 4000)
    }
  }, [])

  useEffect(() => {
    if (
      state?.pageName == 'Transaction' &&
      securityPinCheck?.response?.profiles?.[0]?.purchase?.active
    ) {
      setTitle('TransactionDeAct')
    } else if (
      state?.pageName == 'Transaction' &&
      securityPinCheck?.response?.profiles?.[0]?.purchase?.active === false
    ) {
      setTitle('TransactionAct')
    } else if (securityPinCheck?.response?.profiles?.[0]?.parental?.active) {
      setTitle('Edit')
    } else if (securityPinCheck?.response?.profiles?.[0]?.parental?.active === false) {
      setTitle('Create')
    }
  }, [securityPinCheck])

  useEffect(() => {
    if (remindSecurityPin) {
      setNotificationText('remind')
      dispatch(clearRemindPinSettingsState())
      setShowNotification(true)
      setTimeout(() => {
        setShowNotification(false)
      }, 4000)
    }
  }, [remindSecurityPin])

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handleConfigurePin = item => {
    navigate('/my-settings/help-And-Settings/security-pin/configure', {
      state: {
        data: item,
        purchase: actPurchase,
        rating: state?.rating,
        pageName: '/my-settings/help-And-Settings/security-pin',
        mainPage: state?.pageName
      },
      replace: true
    })
  }

  const handleTranslationchange = (keyname) => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      state?.pageName == '/livePlayer' ?
        navigate('/livePlayer',
          {
            state: { showControls: 'live' },
            replace: true
          })
        :
        navigate('/my-settings/help-And-Settings')
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode == 'backClick' || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      state?.pageName == '/livePlayer' ?
        navigate('/livePlayer',
          {
            state: { showControls: 'live' },
            replace: true
          })
        :
        navigate('/my-settings/help-And-Settings')
    }
  }

  return (
    <div className="app-css">
      <div>
        <img src={'images/Logos_Claro_Video.svg'} className="claro-logo" alt="logo" />
        <div className="back-indicator" onClick={(e) => handleLgkey('backClick')}>
          <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
          <img className="back-image" src={'images/back_button.png'} />
          <p className="back-text">{handleTranslationchange("atv_back_notification")}</p>
        </div>
      </div>
      {title == 'Create' ? (
        <>
          <span className="create-pin-title">
            {handleTranslationchange("setupPin_confirmation_title_label")}
          </span>

          <div className="create-pin-container">
            <p className="create-pin-sub-titles">
              {handleTranslationchange("setupPin_confirmation_detail_label")}
            </p>

            <div className="create-pin-button-container">
              <button
                autoFocus
                className="create-pin-button focusable"
                id="configure-button"
                onClick={() => handleConfigurePin('Create')}
                data-testid="CreatePin"
                style={{ marginTop: '80px' }}
              >
                <p style={{ margin: 0 }}>{handleTranslationchange("setupPin_confirmation_option_button_continue​")}</p>
              </button>
              <button
                className="create-pin-button focusable"
                id="cancel-button"
                onClick={(e) => handleLgkey('backClick')}
                data-testid="create-pin"
              >
                <p style={{ margin: 0 }}>{handleTranslationchange("userProfile_password_option_button_cancel")}</p>
              </button>
            </div>
          </div>
        </>
      )
        :
        null
      }
    </div>
  )
}

export default SecurityPin