import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  imageresponse: ''
}

export const ImageSlice = createSlice({
  name: 'images',
  initialState,
  reducers: {
    getImage: (state, { payload }) => {},
    getImageSuccess: (state, { payload }) => {
      state.imageresponse = payload
    }
  }
})

export const { getImage, getImageSuccess } = ImageSlice.actions
export default ImageSlice.reducer
