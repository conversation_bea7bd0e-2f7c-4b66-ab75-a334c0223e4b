/* Card styles */
.cards {
  background: transparent
    linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
    no-repeat padding-box;
  top: 718px;
  left: 635px;
  width: 538px;
  height: 285px;
  /* UI Properties */
  opacity: 1;
  overflow: hidden;
  margin: 15px;
  /* border-radius: 10px; */
  box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);
}

.series-card {
  background: transparent
    linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
    no-repeat padding-box;
  top: 718px;
  left: 635px;
  height: 104px;
  width: 152px;
  /* UI Properties */
  opacity: 1;
  overflow: hidden;
  margin: 20px;
  /* border-radius: 10px; */
  box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);
}

/* Card image loading */
.cards__images img {
  width: 100%;
  height: 500px;
}

.cards__images.loadings {
  height: 100%;
  width: 100%;
}

.series_card img{
  height: 100%;
  width: 100% 
}

.series_card.loadings {
  height: 100%;
  width: 100%;
}

/* The loading Class */
.loadings {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* background-color: silver; */
  background-image: url('../../tv/images/landscape_card.png');
}

/* The moving element */
.loadings::after {
  display: block;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  transform: translateX(-100%);
  background: -webkit-gradient(
    linear,
    left,
    right,
    from(transparent),
    color-stop(rgba(211, 206, 206, 0.2)),
    to(transparent)
  );

  background: linear-gradient(
    90deg,
    transparent,
    rgba(194, 191, 191, 0.2),
    transparent
  );

  /* Adding animation */
  animation: loadings 0.8s infinite;
}

/* Loading Animation */
@keyframes loadings {
  100% {
    transform: translateX(100%);
  }
}

/* Vertical Card styles */
.vcards {
  top: 647px;
  left: 1185px;
  width: 426px;
  height: 530px;
  /* UI Properties */
  background-color: black;
  opacity: 1;
  background: transparent
  linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
  no-repeat padding-box;
  overflow: hidden;
  margin: 18px;
  /* border-radius: 10px; */
  box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);
}

/* Card image loading */
.vcards__images img {
  width: 100%;
  height: 100%;
}

.vcards__images.vloadings {
  height: 100%;
  width: 100%;
}

/* The loading Class */
.vloadings {
  position: relative;
  /* background-color: silver; */
  background-size: cover;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('../../tv/images/vertical_card.png');
}

/* The moving element */
.vloadings::after {
  display: block;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  transform: translateX(-100%);
  background: -webkit-gradient(
    linear,
    left,
    right,
    from(transparent),
    color-stop(rgba(211, 206, 206, 0.2)),
    to(transparent)
  );

  background: linear-gradient(
    90deg,
    transparent,
    rgba(194, 191, 191, 0.2),
    transparent
  );

  /* Adding animation */
  animation: vloadings 0.8s infinite;
}

/* Loading Animation */
@keyframes vloadings {
  100% {
    transform: translateX(100%);
  }
}


/* Title loading */

 /* Card styles */ 
 .tcard{ 
  /* background-color: #fff; */
  top: 518px;
  left: 435px;
  width: 55%;
  height: 50px;
  /* UI Properties */
  opacity: 1;
  background: transparent
  linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
  no-repeat padding-box;
  overflow: hidden;
  margin: 15px;
  border-radius: 10px;
  box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);
} 

/* Card title */ 
.tcard__title { 
  padding: 8px; 
  font-size: 22px; 
  font-weight: 700; 
} 

.tcard__title.tloading { 
  height: 15px; 
  width: 45%; 
  margin: 0.8rem; 
  border-radius: 30px; 
} 


/* The loading Class */ 
.tloading { 
  position: relative;
background-size: cover;
background-repeat: no-repeat;
background-color: #494848a1
} 

/* The moving element */ 
.tloading::after { 
  display: block;
  content: '';
  position: absolute;
  width: 55%;
  height: 50px;
  transform: translateX(-100%);
  background: -webkit-gradient(
    linear,
    left,
    right,
    from(transparent),
    color-stop(rgba(211, 206, 206, 0.2)),
    to(transparent)
  );

  background: linear-gradient(
    90deg,
    transparent,
    rgba(194, 191, 191, 0.2),
    transparent
  );
  /* Adding animation */ 
  animation: tloading 0.8s infinite; 
} 

/* Loading Animation */ 
@keyframes tloading { 
  100% { 
      transform: translateX(100%); 
  } 
} 


/* //cast cards */
 
.castcards {
  top: 631px;
  left: 1185px;
  width: 248px;
  height: 250px;
  /* UI Properties */
  background: transparent
  linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
  no-repeat padding-box;
  opacity: 1;
 
  overflow: hidden;
  margin: 14px;
  /* border-radius: 10px; */
  box-shadow: 9px 17px 45px -29px rgba(0, 0, 0, 0.44);
}
 
/* Card image loading */
.castcards__images img {
  width: 100%;
  height: 100%;
}
 
.castcards__images.castloadings {
  height: 100%;
  width: 100%;
}
 
/* The loading Class */
.castloadings {
  position: relative;
  /* background-color: silver; */
  background-size: cover;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('../../tv/images/Vod_Movies_Icons/cast_thumbnail.png');
}
 
/* The moving element */
.castloadings::after {
  display: block;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  transform: translateX(-100%);
  background: -webkit-gradient(
    linear,
    left,
    right,
    from(transparent),
    color-stop(rgba(211, 206, 206, 0.2)),
    to(transparent)
  );
 
  background: linear-gradient(
    90deg,
    transparent,
    rgba(194, 191, 191, 0.2),
    transparent
  );
 
  /* Adding animation */
  animation: castloadings 0.8s infinite;
}
 
/* Loading Animation */
@keyframes castloadings {
  100% {
    transform: translateX(100%);
  }
}