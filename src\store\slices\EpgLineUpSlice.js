import { createSlice } from '@reduxjs/toolkit'

export const epgLineupSlice = createSlice({
  name: 'epgLineup',
  initialState: {
    epgLineup: [],
    isLoading: false,
    error: {},
  },
  reducers: {
    getEpgLineup: state => {
      state.isLoading = true
    },
    getEpgLineupSuccess: (state, action) => {
      state.epgLineup = action?.payload?.response?.channels
      state.isLoading = false
    },
    getEpgLineupError: (state, action) => {
      state.isLoading = false
      state.error = action.payload
    },
  },
})

export const { getEpgLineup, getEpgLineupSuccess, getEpgLineupError } =
  epgLineupSlice.actions

export default epgLineupSlice.reducer
