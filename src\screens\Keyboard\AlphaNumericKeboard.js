import React, { useState, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import {
  alphaNumericButtons,
  numberPadButtons,
  specialCharecters,
  numericButtons
} from './buttonConstant'
import './AlphaNumericKeyboard.scss'
import './../../styles/tvstyles.scss'
import { hoveringMouse } from '../../utils/constant'

const AlphaNumericKeyboard = props => {
  const boardBtnRef = useRef([])
  const numericBtnRef = useRef([])

  const region = localStorage.getItem('region')

  const [keyVal, setValue] = useState('')
  const [isSpecial, setSpecial] = useState(false)
  const [caps, setCaps] = useState(false)
  const [numericSubmitDisable, setNumericSubDisable] = useState(true)
  const [selectedIdx, setSelectedIdx] = useState(0)

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)

  useEffect(() => {
    setValue(props?.value || '')
  }, [props])

  useEffect(() => {
    setNumericSubDisable(props?.submitDisable)
  }, [props?.submitDisable])

  useEffect(() => {
    props?.autoFocus &&
      setTimeout(() => {
        numericBtnRef?.current[0]?.focus()
      }, 1400)
  }, [props?.autoFocus])

  useEffect(() => {
    if (props?.fromMic) {
      setTimeout(() => {
        boardBtnRef?.current[0]?.focus()
      }, 200)
    }
  }, [props?.fromMic])

  useEffect(() => {
    document.getElementById('Key_0') && document.getElementById('Key_0').focus()
  }, [isSpecial])
  const onKeyChange = e => {
    let val = keyVal
    switch (e) {
      case 'cl':
        val = val.slice(0, -1)
        break
      case 'space':
        val = val.concat(' ')
        break
      case 'clr':
        val = ''
        break
      default:
        val = val.concat(e)
        break
    }
    if (props?.length) {
      if (val?.length <= props?.length) {
        setValue(val)
        props?.onChange(val)
      }
    } else {
      setValue(val)
      props?.onChange(val)
    }
    document.getElementById(props?.id) &&
      document.getElementById(props?.id).scrollBy(30, 0)
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF1Green', 'ColorF3Blue'])
      const codes = {
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code,
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.greencode === keycode) {
      document.getElementById('clearBtn').click()
    } else if (key.bluecode === keycode) {
      props.type == 'alphaNumeric' && props.name != 'security-pin'
        ? onKeyChange('clr')
        : onNumericKeyChange('clr')
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 404 || keycode == 67 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 119)) {
      document.getElementById('clearBtn').click()
    } else if (keycode == 406 || keycode == 66 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 121)) {
      props.type == 'alphaNumeric' && props.name != 'security-pin'
        ? onKeyChange('clr')
        : onNumericKeyChange('clr')
    }
  }

  const onNumericKeyChange = item => {
    props?.onChange(item)
  }

  const profileFocus = () => {
    if (props.id == 'profileup') {
      document.getElementById('tv_input') &&
        document.getElementById('tv_input').classList.add('focus-btn')
    }
    document.getElementById('searchInput') &&
      document.getElementById('searchInput').classList.add('search_border')
  }
  const profileBlur = () => {
    if (props.id == 'profileup') {
      document.getElementById('tv_input') &&
        document.getElementById('tv_input').classList.remove('focus-btn')
    }
    document.getElementById('searchInput') &&
      document.getElementById('searchInput').classList.remove('search_border')
  }
  let splCharrow = specialCharecters && Math.ceil(specialCharecters.length / 8)
  return props.type == 'alphaNumeric' ? (
    <>
      {isSpecial ? (
        <div className={'KeyboardContainer'}>
          <div className="clearButtonsDiv" style={{ marginLeft: '0px' }}>
            <button
              className="keyBoardSelection focusable"
              style={{
                fontWeight: isSpecial ? 'normal' : 'bold',
                color: isSpecial ? '#7f8282' : '#ffffff'
              }}
              onClick={() => setSpecial(false)}
            >
              abc
            </button>
            <button
              className="keyBoardSelection focusable"
              onClick={() => setSpecial(true)}
            >
              {
                translations?.language?.[region]
                  ?.keyboard_access_option_button_numeric
              }
            </button>
            <button
              className={'clearButtons focusable'}
              onFocus={() => {
                props?.onFocus?.()
              }}
              id="clearBtn"
              onBlur={props?.onBlur}
              onClick={
                props?.name == 'security-pin'
                  ? () => onNumericKeyChange('cl')
                  : () => onKeyChange('cl')
              }
            >
              <img
                src={'images/green_shortcut.png'}
                className={'shortcutImgStyles'}
              />
              <span className="clearButtonsText">
                {
                  translations?.language?.[region]
                    ?.keyboard_access_option_button_delete
                }
              </span>
            </button>
            <button
              className={'clearButtons focusable'}
              onFocus={() => {
                props?.onFocus?.()
              }}
              onBlur={props?.onBlur}
              onClick={
                props?.name == 'security-pin'
                  ? () => onNumericKeyChange('clr')
                  : () => onKeyChange('clr')
              }
            >
              <img
                src={'images/blue_shortcut.png'}
                className={'shortcutImgStyles'}
              />
              <span className="clearButtonsText">
                {
                  translations?.language?.[region]
                    ?.keyboard_access_option_button_empty
                }
              </span>
            </button>
          </div>
          {specialCharecters.map((e, i) => (
            e == 'space' ? (
              <button
                id="spcId"
                type="button"
                data-sn-down={props.dsndown}
                onFocus={() => profileFocus()}
                onBlur={() => profileBlur()}
                onClick={() => onKeyChange('space')}
                className="alphaNumericKeyboardBtn spaceButton focusable"
                onMouseOver={() => {
                  hoveringMouse('spcId')
                }}
              >
                <img src="images/spacebar.png" alt="spacebar" />
              </button>
             ) :(
              <button
                style={{ marginLeft: e == '-' ? '95px' : null }}
                data-sn-down={
                  props.name === 'profile' &&
                    Math.ceil((i + 1) / 8) !== splCharrow
                    ? undefined
                    : document.getElementById('option0') && '#option0'
                }
                onFocus={() => profileFocus()}
                onBlur={() => profileBlur()}
                data-sn-right={
                  props.id == 'profileup' && (e == 'abc' || (i + 1) % 8 == 0)
                    ? ''
                    : undefined
                }
                name={props.name}
                data-testid={'profile'}
                type="button"
                id={'Key_' + i}
                onClick={() =>
                  e == 'abc'
                    ? setSpecial(false)
                    : onKeyChange(caps ? e.toUpperCase() : e.toLowerCase())
                }
                className="alphaNumericKeyboardBtn focusable"
                onMouseOver={() => {
                  hoveringMouse('Key_' + i)
                }}
              >
                <span className="">
                  {caps ? e.toUpperCase() : e.toLowerCase()}
                </span>
              </button>
          )))}
        </div>
      ) : (
        <div className={`KeyboardContainer`}>
          <div
            className="clearButtonsDiv"
            id="clear-buttons-alphaNumeric"
            style={{ marginLeft: '0px' }}
          >
            <button
              className="keyBoardSelection focusable"
              onClick={() => setSpecial(false)}
            >
              abc
            </button>
            <button
              className="keyBoardSelection focusable"
              style={{
                fontWeight: !isSpecial ? 'normal' : 'bold',
                color: !isSpecial ? '#7f8282' : '#ffffff'
              }}
              onClick={() => setSpecial(true)}
            >
              {
                translations?.language?.[region]
                  ?.keyboard_access_option_button_numeric
              }
            </button>

            <button
              className={'clearButtons focusable'}
              onFocus={() => {
                props?.onFocus?.()
              }}
              id="clearBtn"
              onBlur={props?.onBlur}
              onClick={
                props?.name == 'security-pin'
                  ? () => onNumericKeyChange('cl')
                  : () => onKeyChange('cl')
              }
            >
              <img
                src={'images/green_shortcut.png'}
                className={'shortcutImgStyles'}
              />
              <span className="clearButtonsText">
                {
                  translations?.language?.[region]
                    ?.keyboard_access_option_button_delete
                }
              </span>
            </button>

            <button
              className={'clearButtons focusable'}
              onFocus={() => {
                props?.onFocus?.()
              }}
              onBlur={props?.onBlur}
              onClick={
                props?.name == 'security-pin'
                  ? () => onNumericKeyChange('clr')
                  : () => onKeyChange('clr')
              }
            >
              <img
                src={'images/blue_shortcut.png'}
                className={'shortcutImgStyles'}
              />
              <span className="clearButtonsText">
                {
                  translations?.language?.[region]
                    ?.keyboard_access_option_button_empty
                }
              </span>
            </button>
          </div>
          <div>
            {alphaNumericButtons.map((e, i) => (
              <>
                {e == 'shift' ||
                  e == 'backSpace' ||
                  e == 'clearAll' ||
                  e == 'space' ||
                  e == '.com' ||
                  e == '.ar' ||
                  e == '@gmail.com' ||
                  e == '@hotmail.com' ? null : (
                  <button
                    ref={ref => (boardBtnRef.current[i] = ref)}
                    style={{
                      marginLeft:
                        e == '@'
                          ? '38px'
                          : e == '-'
                            ? '0px'
                            : e == '_'
                              ? '100px'
                              : null
                    }}
                    type="button"
                    data-testid="profile"
                    data-sn-right={
                      props.id == 'profileup' &&
                        (e == 'abc' || e == '&?!' || (i + 1) % 10 == 0)
                        ? ''
                        : undefined
                    }
                    data-sn-left={
                      props.id == 'searchleft' &&
                        (e == 'abc' || e == '&?!' || i % 10 == 0)
                        ? '#Searchinput-data0'
                        : undefined
                    }
                    id={'Key_' + i}
                    data-sn-up={i >= 30 && i <= 37 ? `#Key_${i - 9}` : i === 38 ? '#Key_28' : i === 28 ? '#Key_18' : i >= 21 && i <= 27 ? `#Key_${i - 10}` : null}
                    data-sn-down={i === 19 ? '#Key_28' : i === 28 ? '#Key_38' : i === 27 ? '#Key_36' : i === 26 ? '#Key_35' : i === 25 ? '#Key_34' : i === 24 ? '#Key_33' : i === 23 ? '#Key_32' : i === 22 ? '#Key_31' : i === 21 ? '#Key_30' : i >= 11 && i <= 18 ? `#Key_${i + 10}` : null}
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() =>
                      e == '&?!'
                        ? setSpecial(true)
                        : onKeyChange(caps ? e.toUpperCase() : e.toLowerCase())
                    }
                    className={`alphaNumericKeyboardBtn focusable`}
                    onMouseOver={() => {
                      hoveringMouse('Key_' + i)
                    }}
                  >
                    <span className="">
                      {caps ? e.toUpperCase() : e.toLowerCase()}
                    </span>
                  </button>
                )}
                {e == 'shift' ? (
                  <button
                    id="shftId"
                    type="button"
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() => setCaps(!caps)}
                    style={{ marginRight: '64px' }}
                    className="alphaNumericKeyboardBtn focusable"
                    onMouseOver={() => {
                      hoveringMouse('shftId')
                    }}
                  >
                    <img
                      src="images/shift.png"
                      style={{ width: '35px', height: '35px' }}
                      alt="shift"
                    />
                  </button>
                ) : e == 'space' ? (
                  <button
                    id="spcId"
                    type="button"
                    data-sn-down={props.dsndown}
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() => onKeyChange('space')}
                    className="alphaNumericKeyboardBtn spaceButton focusable"
                    onMouseOver={() => {
                      hoveringMouse('spcId')
                    }}
                  >
                    <img src="images/spacebar.png" alt="spacebar" />
                  </button>
                ) : e == '.com' &&
                  props.name != 'search' &&
                  props.name != 'register' &&
                  props.name != 'coupon' ? (
                  <button
                    id=".comId"
                    type="button"
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() => onKeyChange('.com')}
                    className="mailButtons comIdButton focusable"
                    onMouseOver={() => {
                      hoveringMouse('.comId')
                    }}
                  >
                    .com
                  </button>
                ) : e == '.ar' &&
                  props.name != 'search' &&
                  props.name != 'register' &&
                  props.name != 'coupon' ? (
                  <button
                    id=".arId"
                    type="button"
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() => onKeyChange('.ar')}
                    className="mailButtons arButton focusable"
                    onMouseOver={() => {
                      hoveringMouse('.comId')
                    }}
                  >
                    .ar
                  </button>
                ) : e == '@gmail.com' &&
                  props.name != 'search' &&
                  props.name != 'register' &&
                  props.name != 'coupon' ? (
                  <button
                    id="gmlId"
                    type="button"
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() => onKeyChange('@gmail.com')}
                    className="mailButtons gmailButton focusable"
                    onMouseOver={() => {
                      hoveringMouse('gmlId')
                    }}
                  >
                    @gmail.com
                  </button>
                ) : e == '@hotmail.com' &&
                  props.name != 'search' &&
                  props.name != 'register' &&
                  props.name != 'coupon' ? (
                  <button
                    id="yhoId"
                    type="button"
                    onFocus={() => profileFocus()}
                    onBlur={() => profileBlur()}
                    onClick={() => onKeyChange('@hotmail.com')}
                    className="mailButtons hotmail focusable"
                    onMouseOver={() => {
                      hoveringMouse('yhoId')
                    }}
                  >
                    @hotmail.com
                  </button>
                ) : null}
              </>
            ))}
          </div>
        </div>
      )}
    </>
  ) : (
    <div className="KeyboardContainer">
      <div className="clearButtonsDiv" id="clear-buttons">
        <button
          className={'clearButtons focusable'}
          onFocus={() => {
            props?.onFocus?.()
          }}
          id="clearBtn"
          onBlur={props?.onBlur}
          onClick={() => onNumericKeyChange('cl')}
        >
          <img
            src={'images/green_shortcut.png'}
            className={'shortcutImgStyles'}
          />
          <span className="clearButtonsText">
            {
              translations?.language?.[region]
                ?.keyboard_access_option_button_delete
            }
          </span>
        </button>
        <button
          className={'clearButtons focusable'}
          onFocus={() => {
            props?.onFocus?.()
          }}
          onBlur={props?.onBlur}
          onClick={() => onNumericKeyChange('clr')}
        >
          <img
            src={'images/blue_shortcut.png'}
            className={'shortcutImgStyles'}
          />
          <span className="clearButtonsText">
            {
              translations?.language?.[region]
                ?.keyboard_access_option_button_empty
            }
          </span>
        </button>
      </div>
      <div>
        {numericButtons.map((item, index) => (
          <button
            key={index}
            className={'alphaNumericKeyboardBtn focusable'}
            ref={ref => (numericBtnRef.current[index] = ref)}
            onFocus={() => {
              setSelectedIdx(index)
              props?.onFocus?.()
            }}
            onBlur={props?.onBlur}
            onClick={e => onNumericKeyChange(item)}
            id={'Key_' + index}
            autoFocus={index == 0}
          >
            <p className="KeyboardContentTxt">{item}</p>
          </button>
        ))}
      </div>
    </div>
  )
}

export default AlphaNumericKeyboard
