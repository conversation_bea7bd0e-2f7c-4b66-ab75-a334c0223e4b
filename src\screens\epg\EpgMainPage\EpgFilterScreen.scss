.epg-filter-layout {
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  font-family: Roboto;
  position: fixed;
  top: 0px;
  z-index: 4;
  background-color: black;

  .green-button,
  .yellow-button {
    display: flex;
    align-items: center;
    border-radius: 8.8px;
    background-color: #2e303d;
    height: 48px;
    position: absolute;
    top: 55px;

    .back-image {
      margin: 0 10px 0 20px;
    }

    &:focus {
      background-color: #981c15;
    }

    .green-button-image,
    .yellow-button-image {
      margin-left: 20px;
    }

    .yellow-button-text,
    .green-button-text {
      height: 21.65px;
      color: #ffffff;
      font-family: <PERSON>o;
      font-size: 29.04px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 29.04px;
      margin: 13px 25px 20px 16px;
      text-transform: uppercase;
    }
  }

  .green-button {
    width: 168px;
    right: 434px;

    .green-button-text {
      width: 120px;
      text-align: center;
    }
  }

  .yellow-button {
    width: 292px;
    right: 80px;

    .yellow-button-text {
      width: 146px;
    }
  }

  .epg-filter-list {
    position: relative;
    width: 580px;
    height: 244px;
    top: 409px;
    left: 670px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .page-title {
      color: #ffffff;
      font-family: Roboto;
      font-size: 36.04px;
      font-weight: bold;
      letter-spacing: 0;
      display: flex;
      justify-content: center;
      margin: 0px !important;
    }

    .title-channels,
    .title-categories {
      height: 72px;
      width: 509px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 29.04px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 29.04px;
      margin-left: 15px;
      border-radius: 8.8px;
      display: flex;
      justify-content: center;
      margin-top: 20px;
      background-color: #2e303d;

      &:focus {
        height: 82.08px;
        width: 580.26px;
        background-color: #981c15;
      }
    }

    .title-channels-text,
    .title-categories-text {
      height: 38px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 40px;
      font-weight: bold;
      letter-spacing: -0.51px;
      line-height: 0px;
      text-align: center;
    }
  }
}