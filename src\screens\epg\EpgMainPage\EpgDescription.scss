@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

$position-left: left;
$color-white: #ffffff;
$color-pale-white: #cccccc;
$font-roboto: Roboto;

.description-container {
  text-align: $position-left;
  color: $color-white;
  opacity: 1;
  height: 296px;
  width: 1490px;
  margin: 40px 0px 21px 80px;
}

.live-tv-detail-container {
  text-align: $position-left;
  opacity: 1;
  height: 240px;
}

//Commenting and keeping for fure reference
// .description-subtitle {
//   text-align: $position-left;
//   text-wrap: nowrap;
//   font: normal normal normal 16px $font-roboto;
//   font-size: 16px;
//   letter-spacing: 0.29px;
//   color: $font-roboto;
//   opacity: 1;
//   top: 154px;
//   left: 96px;
//   width: 1490px;
//   height: 33px;
// }


.no-content {
  padding: 0px;
  margin: 0px;
}

.time {
  text-align: $position-left;
  font: normal normal normal 16px $font-roboto;
  letter-spacing: 0px;
  color: $font-roboto;
  opacity: 1;
}

.description-details {
  text-align: $position-left;
  height: 150px;
  width: 1490px;
  color: $color-white;
  font-family: $font-roboto;
  font-size: 28px;
  font-weight: 500;
  letter-spacing: -0.91px;
  line-height: 40px;
  margin: 0;
}

.talent-details {
  height: 32px;
  width: 136px;
  color: $color-white;
  font-family: $font-roboto;
  font-size: 26px;
  letter-spacing: -0.84px;
  line-height: 32px;
}

.talent-title {
  font-weight: bold;
}

.epg-extra-details {
  margin-left: 16px;
}

//Commenting and keeping for fure reference
// .age-alert {
//   height: 32px;
//   // border: solid;
//   display: inline-block;
//   padding: 5px;
//   border-radius: 4px;
//   border: 3px solid #FFF;
//   flex-shrink: 0;
// }

.content-duration {
  height: 32px;
  width: 126px;
  color: $color-white;
  font-family: $font-roboto;
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 32px;
}

.complete-guide {
  text-align: $position-left;
  font: normal normal medium 20px $font-roboto;
  font-size: 20px;
  letter-spacing: 0px;
  color: #dddddd;
  opacity: 1;
}

.description-container {
  text-align: $position-left;
  opacity: 1;
  height: 296px;
  width: 1490px;
  margin: 40px 0px 21px 80px;
  color: $color-white;
  top: 0px;
  z-index: 1;
}

.live-tv-detail-container {
  text-align: $position-left;
  opacity: 1;
  width: 1850px;
  height: 1020px;
  background: transparent linear-gradient(90deg, #121212 0%, #121212f2 27%, #03151f00 100%) 0% 0% no-repeat padding-box;
  padding: 10px 10px 10px 60px;
  position: absolute;
  top: 0;
  z-index: 1;
}

.description-title {
  width: 1920px;
  padding: 0px;
  height: 66px;
  text-align: $position-left;
  font: normal normal normal 56px/36px $font-roboto;
  letter-spacing: 0px;
  color: #dddddd;
  opacity: 1;
  text-wrap: nowrap;
  display: flex;
  align-items: center;
  margin: 0px;
}

.ButtonImageHide {
  display: none;
}

.description-subtitle {
  text-align: $position-left;
  text-wrap: nowrap;
  font: normal normal normal 28px/32px $font-roboto;
  font-size: 28px;
  letter-spacing: 0.29px;
  color: $font-roboto;
  opacity: 1;
  top: 154px;
  left: 96px;
  width: 1490px;
  height: 32px;
  margin-bottom: 0px;
}

.time {
  text-align: $position-left;
  font: normal normal normal 28px/32px $font-roboto;
  letter-spacing: 0px;
  color: $font-roboto;
  opacity: 1;
  height: 33px;
}

.description-details {
  text-align: $position-left;
  width: 1490px;
  height: 80px;
  color: $color-white;
  font-family: $font-roboto;
  font-size: 28px;
  font-weight: 500;
  letter-spacing: -0.91px;
  line-height: 40px;
  overflow-y: scroll;
  margin: 0;
  padding-bottom: 24px;
  margin-bottom: 24px;
}

.description-category,
.description-year {
  padding-left: 0px;
  margin-right: 20px;
}

.description-broadcast-schedule,
.content-duration {
  margin-left: 20px;
}

.talent-details {
  height: 32px;
  width: 136px;
  color: $color-white;
  font-family: $font-roboto;
  font-size: 26px;
  letter-spacing: -0.84px;
  line-height: 32px;
}

.age-alert {
  height: 32px;
  border-radius: 3px;
  border: 2px solid #FFF;
  flex-shrink: 0;
  display: inline-block;
}

.content-duration {
  height: 32px;
  width: 126px;
  color: $color-white;
  font-family: $font-roboto;
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 32px;
}

.complete-guide {
  text-align: $position-left;
  font: normal normal 28px/40px $font-roboto;
  font-size: 28px;
  letter-spacing: 0px;
  color: #dddddd;
  opacity: 1;
  font-weight: 800;
  width: 400px;
  height: 33px;
  margin: 0;
  text-wrap: nowrap;
  display: flex;
  align-items: center;
}


.shortcut-bar-epg {
  position: fixed;
  height: 99px;
  width: 1840px;
  opacity: 0.6;
  border-radius: 12px 12px 0 0;
  background-color: #000000;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  margin-right: 40px;
  margin-left: 40px;
}

.shortcut-bar-epg2 {
  position: fixed;
  height: 99px;
  width: 1840px;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  margin-right: 40px;
  margin-left: 40px;
}

.shortcut-bar-text {
  height: 28px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 26px;
  margin: 0 20px;
  opacity: 0.7;
}

.epg-bar-heading {
  color: #ffffff;
  font-family: Roboto;
  font-size: 48px;
  font-weight: bold;
  letter-spacing: -0.77px;
  line-height: 48px;
  padding-left: 40px;
}

.epg-shorcut-container {
  display: flex;
}