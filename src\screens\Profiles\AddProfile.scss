.add-profile {
    height: 1080px;
    width: 1920px;
    background-color: #121212;
    position: fixed;
     .profile-app-logo{     
        width: 1920px;
        background-color: #121212;
        display: flex;
        justify-content: space-between;
        .logo-img {
        margin-top: 67px;
        margin-left: 90px;
        width: 171px;
        height: 36px;
      }
        
    .back-button{
        height: 48px;
        width: 292px;
        border-radius: 6.6px;
        background-color: #2E303D;
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 49px;
        margin-left: 96px;
        margin-right: 53px;
        .yellow-dot{
            height: 20px;
            width: 20px;
        }
        .back-arrow{
            height: 24px;
            width: 30px;
        }
        .back-button-text{    height: 30px;
            width: 146px;
            color: #FFFFFF;
            font-family: Roboto;
            font-size: 29.04px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 29.04px;
        }
    }

     }




.add-watch-header {
    display: flex;
    color: white;
    margin: 0px;
    align-items: center;
    justify-content: center;
    height: 57px;
    width: 1920px;
    color: #FFFFFF;
    font-family: <PERSON><PERSON>;
    font-size: 48px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 57px;
    text-align: center;
    margin-bottom: 73px;
}
.add-edit-container{
    display:flex;

    .left-container-virtual-keyboard{
        margin-left: 87px;
    }
          .edit-key-board{  
        height: 629px;
        width: 940px;
        margin-left: 18px;
        }

          .right-container-add-profile-cont{
            height: 626px;
            width: 828px;
           margin-left: 80px;
           .edit-image-container{
            display:flex;
                .profile-image {
                    height: 150px;
                    width: 150px;
                    margin-left: 54px;
                    margin-right: 48.8px;
                }       
    .choose-profile-text-container
    {
        height: 72px;
        width: 401px;
        border-radius: 3.99px;
        background-color: #2E303D;
        margin-top: 33.5px;
        display: flex;
        justify-content: center;
        
        .choose-profile-text{
        color: #FFFFFF;
        font-family: Roboto;
        font-size: 32px;
        font-weight: bold;
        letter-spacing: -0.51px;
        line-height: 76px;
        display:block;
        }
        
    }
    .choose-profile-text-container:focus{
        transform: scale(1.1);
    }
        }
        .profile-container {
   
            .profile-name-container{
                margin-top: 32px;
                margin-bottom: 16px;
                    .profile-name{
                    height: 33px;
                  width: 677px;
                  color: #FFFFFF;
                  font-family: Roboto;
                  font-size: 28px;
                  font-weight: bold;
                  letter-spacing: -0.2px;
                  line-height: 33px;
                  margin-left: 54px;
                }
              }
              .profile-name-input-container{
                height: 72px;
                width: 720px;
                border-radius: 6px;
                background-color: #212224;
                margin-left: 54px;
                .profile-name-input  {
                    height: 36px;
                    width: 585px;
                    color: white;
                    font-family: Roboto;
                    font-size: 32px;
                    letter-spacing: -0.51px;
                    line-height: 32px;
                    background-color: #212224;
                    border: unset;
                    margin-top: 12px;
                    margin-left: 30px;
                    margin-bottom: 20px;
                  }

                  .cursor-point-addprofile{
                    left: 61px;
                    top: 15px;
                    color: #ffffff;
                    font-family: 'Roboto';
                    font-size: 36px;
                    font-weight: bold;
                    letter-spacing: 0;
                    line-height: 40px;
                    animation:blink 1s step-end infinite;
                    margin-left: 10px;
                  }
                // .cursor-point-addprofile {
                //     position: absolute;
                //     left: 67px;
                //     top: 15px;
                //     color: #ffffff;
                //     font-family: 'Roboto';
                //     font-size: 36px;
                //     font-weight: bold;
                //     letter-spacing: 0;
                //     line-height: 40px;
                //     animation: blink 2s step-end infinite;
                //   }
                  @keyframes blink {
                    50% {
                      opacity: 0;
                    }
                  }
            } 
        }
        }
        
}
.profile_radiobox {
    margin-top: 0.5rem;
}
.profile_radio {
    background: #000000 0% 0% no-repeat padding-box;
    border: 4px solid #34353B;
    border-radius: 12px;
    opacity: 1;
    width: 64px;
    height: 64px;
    box-shadow: inset 4px -6rem 1rem #000000;
}
.radio {
    top: 550px;
    width: 416px;
    height: 33px;
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.profile-save:focus,
.profile-save:active {
transform: scale(1.1);
border-radius: 6.9px;
}
.password-height {
    height: 146px
}

.image {
    width: 41px;
    height: 29px;
    margin-top: 19px;
    margin-left: 32px;
}

.add-cancel-btn:focus{
transform: scale(1.1);
border-radius: 6.9px;
}

.prof-button:focus>.profile-image,
.prof-button:active>.profile-image {
    z-index: 1;
}
.profile_heading {
    margin-bottom: 1rem;
    margin-top: 30px;
    left: 873px;
    height: 57px;
    font-weight: 'Regular';
    text-align: center;
    font-family: "Roboto";
    font-size: 48px;
    letter-spacing: 0px;
    color: #F1F2F3;
    opacity: 1;
}

.profile_title {
    margin-top: 10px;
    left: 787px;
    height: 35px;
    text-align: center;
    font-size: 30px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.profile_backbtn {
    margin-top: 1rem;
    width: 24px;
    height: 24px;
    margin-left: 57rem;
}

.profile-name-input-container:focus{
    box-sizing: border-box;
    height: 72px;
    width: 720px;
    border: 4px solid #4C6F94;
    border-radius: 6px;
    background-color: #212224;


}
.profile-name-input:focus,
.profile-name-input:active {
    z-index: 1;
    outline: unset;
    border-radius: 44px;
}

.checkbox-container {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 24px;
    outline: unset;
    margin-bottom: 54px;
}

.checkbox-sub-container{
    margin-top: -39px;
    display: block;
    position: relative;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Hide the browser's default checkbox */
.checkbox-sub-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.check-mark {
    position: absolute;
    height: 72px;
    width: 72px;
    border-radius: 6px;
    background-color: #212224;
    margin-left:54px;
    margin-right: 16px;
    margin-top: -24px;
}

/* On mouse-over, add a grey background color */
.checkbox-container:focus input~.check-mark {
    z-index: 1;
    outline: unset;
    border-radius: 13px;
    border: 4px solid #4C6F94;
}

/* When the checkbox is checked, add a blue background */
.checkbox-sub-container input:checked~.check-mark {
    background-color: #212224;
}

/* Create the checkmark/indicator (hidden when not checked) */
.check-mark:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.checkbox-sub-container input:checked~.check-mark:after {
    display: block;
}

/* Style the checkmark/indicator */
.checkbox-sub-container .check-mark:after {
    left: 20px;
    top: 14px;
    width: 14px;
    height: 25px;
    font-weight: bold;
    border: solid white;
    border-width: 0px 7px 7px 0;
    -webkit-transform: rotate(35deg);
    -ms-transform: rotate(35deg);
    transform: rotate(35deg);
}

.checkbox-popup {
    z-index: 1;
    border-radius: 6px;
    background-color: #FFFFFF;
    height: 243px;
    width: 566px;
    position: absolute;
    right: 114px;
    top: 549px;
 
.kids-desc-title{

    height: 43px;
    width: 428px;
    color: #1A1A1A;
    font-family: Roboto;
    font-size: 32px;
    font-weight: bold;
    letter-spacing: -0.51px;
    line-height: 38px;
    text-align: left;
    margin-left: 22px;
    margin-bottom: 0px;
    margin-top: 16px;
}

.checkbo-popup:after,
.checkbox-popup:before {
	left: 24px;
	top: 4%;
	border: solid transparent;
	content: " ";
	width: 0;
	position: absolute;
	pointer-events: none;
}

.checkbox-popup:after {
	border-width: 10px 10px 10px 20px;
	border-left: 27px solid #FFFFFF;
}

}
.checkbox-popup::before {
	content: "";
	position: absolute;
	width: 51px;
	height: 38px;
	z-index: -1;
	border-radius: 3px;
	right: 528px;
	transform: rotate(50deg);
	background-color: #FFFFFF;
	bottom:185px;
}

.redDot {
    background: #DE1717 0% 0% no-repeat padding-box;
    opacity: 1;
    width: 30px;
    border: 0px;
    border-radius: 43px;
    height: 16px;
    margin: 8px 8px 0px 0px;
}

.kids-desc-container {
    margin-left: 22px;
    margin-top: 16px;
    .kids-list{    
        list-style: none;  
        .kids-desc {
            height: 72px;
            width: 520px;
            color: #1A1A1A;
            font-family: Roboto;
            font-size: 32px;
            letter-spacing: -0.51px;
            line-height: 36px;
            opacity: 1;
            display: flex;
            flex-direction: row;
            margin-bottom: 8px;
            margin-left:-18px;
        }
    }
    ul.kids-list li::before {
        content: '';
        position: absolute;
        width: 10px;
        height: 10px;
        margin-top: 9px;
        border-radius: 50%;
        left: 22px;
        background-color: #000000;            
    }
    ul.kids-list li {
        font-size: 32px;
    }
}



.kids-profname {
    height: 32px;
    width: 629px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 32px;
    letter-spacing: -0.51px;
    line-height: 32px;
    margin-left: 147px;
    margin-top: 0px;
}


.addprof_hideicon {
    margin-top: 1rem;
    width: 33px;
    height: 29px;
    opacity: 1;
}

.hideicon-_container {

    text-align: center;
    width: 104px;
    height: 69px;
    background: #282828 0% 0% no-repeat padding-box;
    border-radius: 5px;
    opacity: 1;
    margin: 10px 0px 0px 20px;
}

.pin {

    display: flex;
}

.PinContainer {
    display: flex;
    margin: 70px 350px 0px auto;
    flex-direction: column;
    width: 970px;
}

.sixdigitpin {
    font-size: 3.9rem;
    font-family: 'Roboto';
    color: white;
    width: 3rem;
    border: none;
    border-bottom: 4px solid grey;
    background-color: transparent;
    margin-right: 40px;
    outline: unset;
    text-align: center;
}

.sixdigitpin:focus,
.sixdigitpin:active {
    border-bottom: 4px solid #981C15;
}

.partitioned {
    border: none;
    width: 10.5ch;
    background:
        repeating-linear-gradient(90deg,
            dimgrey 0,
            dimgrey 1ch,
            transparent 0,
            transparent 1.5ch) 0 100%/100% 2px no-repeat;
    color: dimgrey;
    font: 5ch consolas, monospace;
    letter-spacing: .5ch;
}

.divInner {
    left: 0;
    position: sticky;
}

.divOuter {
    width: 190px;
    overflow: hidden;
    margin-left: auto;
    margin-right: auto;
    display: flex;
}

.kids-list {
    font-size: 32px;
    list-style: none;
}

ul.kids-list li::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin-top: 9px;
    border-radius: 50%;
    left: 36px;
    background-color: #DE1717;
}

ul.kids-list li {
    font-size: 32px;
}

.InputPopup {
	background: #EEEEEE 0% 0% no-repeat padding-box;
    border: 2px solid #EEEEEE;
    border-radius: 12px;
    opacity: 1;
    height: 140px;
    width: 515px;
    position: absolute;
    align-items: center;
    display: flex;
    left: 50px;
    margin-top: 24px;
}

.InputPopup::before {
	content: '';
    position: absolute;
    width: 45px;
    height: 45px;
    z-index: -1;
    border-radius: 3px;
    right: -15px;
    transform: rotate(45deg);
    background-color: #EEEEEE;
    bottom: 50px;
}

.CheckboxPinPopup {
    background: #EEEEEE 0% 0% no-repeat padding-box;
    border: 2px solid #EEEEEE;
    border-radius: 12px;
    opacity: 1;
    height: 545px;
    width: 426px;
    position: absolute;
    left: 115px;
    bottom: 80px;
}

.CheckboxPinPopup::before {
    content: '';
    position: absolute;
    width: 50px;
    height: 70px;
    z-index: -1;
    border-radius: 5px;
    right: -13px;
    transform: rotate(45deg);
    background-color: #EEEEEE;
    bottom: 5px;
}

.profile-save-button{
    .add-cancel-btn{
        height: 72px;
    width: 720px;
    border-radius: 4px;
    background-color: #2E303D;
    margin-left: 54px;
    display: flex;
    justify-content: center;
    align-items: center;
    }
}

.addprofback {
    position: fixed;
    bottom: 0px;
}
    .add-cancel-btn{
        height: 72px;
        width: 720px;
        border-radius: 4px;
        background-color: #2E303D;
        margin-left: 54px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .add-cancel-text{
        height: 38px;
        width: 605px;
        color: #FFFFFF;
        font-family: Roboto;
        font-size: 32px;
        font-weight: bold;
        letter-spacing: -0.51px;
        line-height: 38px;
        text-align: center;
        display: flex;
    justify-content: center;
    align-items: center;
    }
    .add-save-btn{
        height: 38px;
        width: 142px;
        color: #FFFFFF;
        font-family: Roboto;
        font-size: 32px;
        font-weight: bold;
        letter-spacing: -0.51px;
        line-height: 38px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .deleteprofile_btn:focus {
        transform: scale(1.1);
        border-radius: 4.6px;

    }
    .choose-profile-text{
        transform:scale(1.1);
       
    }
    .profile-save{
        height: 72px;
        width: 720px;
        border-radius: 6px;
        background-color: #981C15;
        margin-top: 37px;
        margin-bottom: 35px;
        margin-left: 54px;
        display: flex;
        justify-content: center;
        align-items: center;

    }
    .error-popup {
        background-color: #fff;
        color: #C1272D;
        border: 1px solid #f5c6cb;
        padding: 10px;
        margin-top: 10px;
        font-size: 32px;
        height: 67px;
        line-height: 38px;
        text-align: center;
        border-radius: 4px;
        margin-left: 60px;
        width: 690px;
       }
       .profile-save-button{
        margin-top: 50px;
       }
}