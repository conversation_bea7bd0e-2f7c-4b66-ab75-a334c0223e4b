import React from 'react'
import { render, fireEvent, queryByAttribute, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import { BrowserRouter as Router } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import { fromJS } from 'immutable'
import EpConfirmation from './EpConfirmation'

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: jest.fn(),
  useNavigate: jest.fn(),
}))

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}))

jest.mock('../../store/sagaStore', () => ({
  store: {
    dispatch: jest.fn()
  }
}))

// Setup
const initialState = fromJS({})
const mockStore = configureStore([])
const history = createHistory()
const useLocationMock = require('react-router-dom').useLocation
const useNavigateMock = require('react-router-dom').useNavigate
const useSelectorMock = require('react-redux').useSelector
const useDispatchMock = require('react-redux').useDispatch
const sagaStoreMock = require('../../store/sagaStore').store

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router>{children}</Router>
  </Provider>
)

const renderWithState = (ui) => {
  return render(ui, { wrapper: Wrapper })
}

describe('EpConfirmation component', () => {
  const navigateMock = jest.fn()
  const dispatchMock = jest.fn()
  
  beforeEach(() => {
    useNavigateMock.mockReturnValue(navigateMock)
    useDispatchMock.mockReturnValue(dispatchMock)
    sagaStoreMock.dispatch.mockClear()
    jest.spyOn(window.localStorage.__proto__, 'getItem').mockImplementation((key) => {
      if (key === 'region') return 'US'
      return null
    })
    jest.spyOn(window.localStorage.__proto__, 'setItem').mockImplementation(() => {})
    useSelectorMock.mockImplementation(callback => {
      const state = {
        initialReducer: {
          appMetaData: {
            translations: JSON.stringify({
              language: {
                US: {
                  keepWatching_modal_option_button_continueToSee: 'Continue to see',
                  keepWatching_modal_description_label: 'To play this content, sign in or register',
                  keepWatching_option_button_register: 'Register',
                  keepWatching_option_button_login: 'Login',
                  keepWatching_option_button_cancel: 'Cancel'
                }
              }
            })
          }
        },
        epg: {
          epgChannel: [null, { channelResponse: [{ group_id: '123' }] }],
          paywayToken: {
            response: {
              paqs: {
                paq: [{ groups: '123,456' }]
              }
            }
          }
        }
      }
      return callback(state)
    })
  })
  afterEach(() => {
    jest.clearAllMocks()
  })

  test('should handle back button on details page', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'details',
        seriesEpisodeData: { id: '123' },
        vodMoviesData: { id: '456' }
      }
    })
    
    renderWithState(<EpConfirmation />)
    fireEvent.keyUp(document, {
      keyCode: 8
    })
    expect(navigateMock).toHaveBeenCalledWith(
      '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
      expect.objectContaining({
        state: expect.objectContaining({
          fromDetailsPage: true
        })
      })
    )
  })

  test('should handle back button on livePlayer page', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'livePlayer',
        data: { id: '123' },
        grid: true
      }
    })
    
    renderWithState(<EpConfirmation />)
    fireEvent.keyUp(document, {
      keyCode: 10009
    })
    expect(navigateMock).toHaveBeenCalledWith(
      '/livePlayer',
      expect.objectContaining({
        state: expect.objectContaining({
          showControls: 'live'
        })
      })
    )
  })

  test('should handle back button on search page', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'search',
        inputValue: 'test search'
      }
    })
    
    renderWithState(<EpConfirmation />)
    fireEvent.keyUp(document, {
      keyCode: 461
    })
    
    expect(navigateMock).toHaveBeenCalledWith(
      '/search',
      expect.objectContaining({
        state: expect.objectContaining({
          inputValue: 'test search'
        })
      })
    )
    expect(localStorage.setItem).toHaveBeenCalledWith('currNavIdx', -1)
  })

  test('should handle back button on series page', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'series',
        seriesEpisodeData: { id: '123' }
      }
    })
    
    renderWithState(<EpConfirmation />)
    fireEvent.keyUp(document, {
      keyCode: 8
    })
    expect(navigateMock).toHaveBeenCalledWith(
      '/series',
      expect.objectContaining({
        state: expect.objectContaining({
          data: expect.objectContaining({ id: '123' })
        })
      })
    )
  })

  test('should handle back button on playerrecord page', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'playerrecord'
      }
    })
    const playMock = jest.fn()
    renderWithState(<EpConfirmation page="playerrecord" playplayer={{ play: playMock }} />)
    fireEvent.keyUp(document, {
      keyCode: 8
    })
    expect(playMock).toHaveBeenCalled()
    expect(sagaStoreMock.dispatch).toHaveBeenCalled()
  })

  test('should handle back button for default case (movies)', () => {
    useLocationMock.mockReturnValue({
      state: {
        vodMoviesData: { id: '789' }
      }
    })
    renderWithState(<EpConfirmation />)
    fireEvent.keyUp(document, {
      keyCode: 8
    })
    expect(navigateMock).toHaveBeenCalledWith(
      '/movies',
      expect.objectContaining({
        state: expect.objectContaining({
          vodData: expect.objectContaining({ id: '789' })
        })
      })
    )
  })

  test('should handle cancel button for livePlayer', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'livePlayer',
        data: { id: '123' },
        grid: true
      }
    })
    const { getByText } = renderWithState(<EpConfirmation />)
    fireEvent.click(getByText('Cancel'))
    expect(localStorage.setItem).toHaveBeenCalledWith('subMenu', 1)
    expect(navigateMock).toHaveBeenCalledWith(
      '/livePlayer',
      expect.objectContaining({
        state: expect.objectContaining({
          showControls: 'live'
        })
      })
    )
  })

  test('should handle cancel button for search page', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'search',
        inputValue: 'test search'
      }
    })
    const { getByText } = renderWithState(<EpConfirmation />)
    fireEvent.click(getByText('Cancel'))
    expect(localStorage.setItem).toHaveBeenCalledWith('subMenu', 1)
    expect(navigateMock).toHaveBeenCalledWith(
      '/search',
      expect.objectContaining({
        state: expect.objectContaining({
          inputValue: 'test search'
        })
      })
    )
  })

  test('should handle register button click', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'livePlayer',
        data: { id: '123' },
        seriesEpisodeData: { id: '456' },
        vodMoviesData: { id: '789' }
      }
    })
    const { getByText } = renderWithState(<EpConfirmation />)
    fireEvent.click(getByText('Register'))
    expect(sagaStoreMock.dispatch).toHaveBeenCalled()
    expect(navigateMock).toHaveBeenCalledWith(
      '/register',
      expect.objectContaining({
        state: expect.anything()
      })
    )
  })

  test('should handle sign in button click', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'series',
        seriesEpisodeData: { id: '456' },
        vodMoviesData: { id: '789' }
      }
    })
    const { getByText } = renderWithState(<EpConfirmation />)
    fireEvent.click(getByText('Login'))
    expect(sagaStoreMock.dispatch).toHaveBeenCalled()
    expect(navigateMock).toHaveBeenCalledWith(
      '/signin',
      expect.objectContaining({
        state: expect.anything()
      })
    )
  })

  test('should clean up event listener on unmount', () => {
    useLocationMock.mockReturnValue({
      state: {
        page: 'series'
      }
    })
    
    const { unmount } = renderWithState(<EpConfirmation />)
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener')
    unmount()
    expect(removeEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function))
  })
})