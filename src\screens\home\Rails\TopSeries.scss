$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;

.toprailContainer {
  overflow-x: hidden;
  height: 320px;
  margin-bottom: 25px;

  .railTitleTop {
    font-size: 32px;
    font-weight: normal;
    text-align: left;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    font-family: $font-family-roboto;
    margin-left: 70px;
  }

  .rail-wrapper {
    display: flex;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
    margin-left: 30px;

    .count_block {
      display: flex;
      flex-direction: row;
      margin-left: -58px;

      .no_img:not(:last-child) {
        margin-left: 0px;
      }

      .no_img:last-of-type {
        margin-left: 25px;
      }
    }

    .series_block {
      display: flex;
      justify-content: center;
      align-items: center;
      right: 50px;
      border: 3px solid transparent !important;
      border-radius: 2.91px;
      opacity: 1;
      padding: 4px;
    }

    .series_block:focus {
      border-radius: 10px;
      transform: scale(1.02);
      opacity: 1;
      scroll-snap-align: end;
      border: 4px solid #fff !important;
    }

    .top-image-series {
      display: flex;
      width: 412px;
      height: 232px;
    }

    .seriesMoviesTitle {
      position: absolute;
      bottom: 3px;
      left: 0px;
      opacity: 0;
    }

    .series_block:focus .seriesMoviesTitle {
      opacity: 1;
      padding-left: 16px;
    }

    .defaultTitleTop {
      font-size: 28px;
      text-wrap: wrap;
      color: #fff;

      padding-left: 16px;
      font-family: 'Roboto';
      width: 20rem;
    }

    .proveedor-block-rail-alq-comp {
      position: absolute;
      top: 48px;
      left: 11px;
    }

    .comprar-tag-movies {
      position: absolute;
      top: 10px;
      left: 10px;
      height: 34px;
      width: 108px;
      border-radius: 6px;
      background-color: #477f9B;
      color: #ffffff;
      font-family: Roboto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 20px;
    }
  }
}