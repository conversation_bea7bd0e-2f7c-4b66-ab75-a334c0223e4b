$position-relative: relative;
$position-absolute: absolute;
$color-white: #ffffff;

.tooltip {
  position: $position-relative;
  display: inline-block;
  top: -25px;
  z-index: 2;
}

.tooltip {
  .tooltiptext {
    visibility: visible;
    /* height: 33px; */
    /* width: 82px; */
    /* background-color: #eb0145; */
    /* font: normal normal normal 24px/28px Roboto; */
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    text-align: center;
    border-radius: 6px;
    // padding: 5px 0;
    position: $position-relative;
    z-index: 1;
    bottom: 150%;
    top: 38px;
    left: -20px;
    margin-left: -60px;
    height: 40px;
    width: 126px;
    background-color: #eb0045;
    font-family: <PERSON><PERSON>;
    font-size: 26px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 26px;
    // align-content: center;
    .date-text{
      position: relative;
      top: 7px;
    }
  }

  .tooltiptext::after {
    content: '';
    position: $position-absolute;
    top: 111%;
    left: 51px;
    // margin-left: -40px;
    border-width: 13px;
    border-style: solid;
    border-color: $color-white transparent transparent transparent;
    z-index: 0;
  }
}

.arrow-point {
  border: 2px solid $color-white;
  position: $position-absolute;
  color: $color-white;
  top: 90px;
  height: 550px;
  width: 0px;
  left: -18px;
  z-index: 0;
}