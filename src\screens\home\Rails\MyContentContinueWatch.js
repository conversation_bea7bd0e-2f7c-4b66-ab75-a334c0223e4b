import React, { useState, useEffect, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './ContinueWatching.scss'
import ProgressBar from '../../Progressbar/Progressbar'
import { continueWatchlist } from '../../../store/slices/getWatchListSlice'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { contentSelectionType } from '../../../GoogleAnalyticsConstants'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const MyContentContinueWatch = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()
  const [content, setContent] = useState([])
  const [focusContent, setFocusContent] = useState(false)
  const [focusedId, setFocusedId] = useState(null)
  const smallprogress = true
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const apilanguage = translations?.language?.[region]
  const mycontentdata = localStorage.getItem('miscontenidos')
  const mycontentplaceholder = mycontentdata
    ? 'images/mycontent_placeholder.png'
    : 'images/landscape_card.png'
  const userId = localStorage.getItem('userId')
  const lastTouch = localStorage.getItem('lasttouch')
  const railImage = useSelector(
    state => state?.watchList?.continuewatchlist?.response?.groups
  )
  const filteredData = railImage?.filter(each => 
    each?.vistime?.last?.progress > 0 && each?.vistime?.last?.progress !== 100
  ) || [];
  const getdeletecontinuewatch = useSelector(
    state => state?.watchList?.deletecontinuewatchlist
  )
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const navIndex = localStorage.getItem('currNavIdx')
 
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
    const backelement = state?.fromPage == 'vodplayer' ? document.getElementById('myContentContinueWatch0') : document.getElementById(props?.backfocusid)

    useEffect(() => {
      if (backelement) {
        setTimeout(() => {
          setContent(filteredData?.[0])
          backelement.focus();
          backelement.scrollIntoView({
            block: 'center',
            inline: 'center',
            behavior: 'smooth',
          });
        }, 100);
      }else if(focusContent && filteredData?.length > 0){
        document.getElementById(`index${props?.index}${filteredData?.length-1}`)?.focus()
      }
    }, [backelement,railImage]);
    

  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
    const userData = {
      user_id: userDetails?.user_id,
      parent_id: userDetails?.parent_id,
      suscriptions: userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key => userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
      user_type: userDetails ? 'registrado' : 'anonimo',
      content_section : navbarTab,
      content_list : props?.title,
      modulo_name : 'carrusel',
      content_list_id : 'carrusel horizontal'
    }
    if (item?.is_series || item?.episode_number) {
      // GA : Content Selection Event
      pushContentSelectionEvent(userData, item, index, contentSelectionType.HOME_SERIES)
      navigate('/series', {
        state: { data: item, backfocusid: `index${props?.index}${index}` }
      })
    } else {
      // GA : Content Selection Event
      pushContentSelectionEvent(userData, item, index, contentSelectionType.HOME_MOVIES)
      navigate('/movies', {
        state: { vodData: item, backfocusid: `index${props?.index}${index}` }
      })
    }
  }

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const handleTranslationchange = useCallback(keyname => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  const handleFocus = data => {
    setContent(data)
    setFocusContent(true)
  }
  const handleBlur = data => {
    setFocusContent(false)
  }

  useEffect(() => {
    dispatch(
      continueWatchlist({
        id:
          getdeletecontinuewatch?.msg === 'OK'
            ? getdeletecontinuewatch?.lasttouch?.seen
            : lastTouch,
        userId,
        HKS: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
  }, [getdeletecontinuewatch, userDetails, lastTouch])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (key.redcode == keycode) {
        navigate('/deletecard', {
          state: {
            deteleData: content,
            delContinuewatch: true,
            page: 'continuewatch',
            backfocusid: focusedId
          }
        })
      }
    }
  }

  const handleLgkey = keycode => {
    if (focusContent) {
      if (keycode == 403) {
        navigate('/deletecard', {
          state: {
            deteleData: content,
            delContinuewatch: true,
            page: 'continuewatch',
            backfocusid: focusedId
          }
        })
      }
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    SpatialNavigation.focus()
  }, [])

  const handleKeyDown = event => {
    if(event.key == 'ArrowUp' || event.keyCode == 38) {
       document.getElementById(`nav-${navIndex}`)?.focus()
    }
  }

  return (
    <div className="ContinueWatchingrailContainer" 
    style={{height : filteredData.length > 0 ? 370 : 340, marginBottom: filteredData.length > 0 ? 40 : 10}}
    id={'myContentRail'}>
      {filteredData.length > 0 ? (
        <div className="railTitle">
          <SafeHTML html={props?.title || ''} />
        </div>
      ) : (
        ''
      )}
      {
        <div className='ContinueWatchingsub'>
          {filteredData.length > 0  ?(
            <div className="continue-wrapper">
              {filteredData?.map((each, index) => (
                <>
                      <button
                        className="rail_block focusable"
                        key={index}
                        id={`index${props?.index}${index}`}
                        onClick={() => goToMoviesSeries(each, index)}
                        onFocus={() => {
                          handleFocus(each)
                          setFocusedId(`index${props?.index}${index}`)
                        }}
                        onBlur={() => handleBlur(each)}
                        onKeyDown={handleKeyDown}
                        data-testid={`rail_card_click${index}`}
                        data-sn-right={index != filteredData.length - 1 && undefined}
                        data-sn-left={index == 0 ? '' : undefined}
                        data-sn-down={document?.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                      >
                        {each.image_small && each.image_small !== '' ? (
                          <>
                            <LazyLoadImage
                              src={each.image_small}
                              placeholderSrc="images/landscape_card.png"
                              key={index}
                              className="rail-image"
                              id={`railfocus${index}`}
                            />

                            <div className="progress-bar-continuewatch">
                              <ProgressBar
                                percent={each?.vistime?.last?.progress}
                                size={'small'}
                                sliderWidth={0}
                                smallprogress={smallprogress}
                              />
                            </div>

                            <div className="title-icon-block">
                              <div className="continuewatchingShow-title">
                                <div className="continuewatchingtitle">
                                  {each?.title}
                                </div>
                              </div>
                              <div className="deleteIcons">
                                <img
                                  src={'images/Home_icons/red.png'}
                                  className="redDot"
                                />
                                <img
                                  src={'images/Home_icons/delete.png'}
                                  className="delete"
                                />
                              </div>
                            </div>
                          </>
                        ) : (
                          <LazyLoadImage
                            src="images/landscape_card.png"
                            loading="lazy"
                            alt="PlaceHolder"
                            className="rail-image"
                          />
                        )}

                        {/* tags */}

                        {each.image_small && each?.proveedor_code == 'amco' ? (
                          each?.format_types === 'ppe,download' ? (
                            <div className="proveedorBlockRailAlq">
                              <img
                                src={'images/Alquilar.svg'}
                                className="tagAlq"
                              />
                            </div>
                          ) : each?.format_types === 'ppe' ? (
                            <div className="proveedorBlockRailAlq">
                              <img
                                src={'images/Alquilar.svg'}
                                className="tagAlq"
                              />
                            </div>
                          ) : // each?.format_types === 'free' ?
                          // <div className="proveedorBlockRail">
                          //   <img src={'images/verahora.png'} className="tag" />
                          // </div>
                          null
                        ) : each.image_small &&
                          each?.proveedor_code &&
                          each?.image_medium ? (
                          <div className="proveedorBlockRail_vero_hara">
                            {Addproveedor(providerLabel?.[each?.proveedor_code]?.susc) && (
                              <img
                                id="#icon1"
                                className={
                                  each?.proveedor_code === 'picardia2' &&
                                  'picardia-image'
                                }
                                src={Addproveedor(providerLabel?.[each?.proveedor_code]?.susc)}
                              />
                            )}
                            {each?.format_types === 'free' && userDetails?.subscriptions?.length == 0 ? (
                              <div className="verahora-tag">VER AHORA</div>
                            ) : null}
                            {each.image_small &&
                              each?.proveedor_code === 'picardia2' &&
                              each?.image_medium && (
                                <div className="picardia-proveedorBlockRail">
                                  <img
                                    src={'images/Adultus.svg'}
                                    className="picardia-tag"
                                  />
                                </div>
                              )}
                          </div>
                        ) : null}
                      </button>
                </>
              ))}
            </div>
          ) : (
            mycontentdata && (
              <div>
                <div className="mycontent-railTitle">
                  <SafeHTML html={props?.title || ''} />
                </div>
                <div className="nocontent-card-main">
                  <button
                    className="nocontent-card focusable"
                    data-sn-right
                    data-sn-left
                    id={`index${props?.index}0`}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                  >
                    <p className="mycontent-rail-text">
                      {handleTranslationchange(
                        'myContent_noSaved_lastSeen_label'
                      )}
                    </p>
                    <div className="nocontent-sub-card">
                      <img className="cardimage1" src={mycontentplaceholder} />
                      <img className="cardimage2" src={mycontentplaceholder} />
                      <img className="cardimage3" src={mycontentplaceholder} />
                    </div>
                  </button>
                </div>
              </div>
            )
          )}
        </div>
      }
    </div>
  )
}

export default MyContentContinueWatch
