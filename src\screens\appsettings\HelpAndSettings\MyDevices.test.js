import React from "react";
import { fireEvent, queryByAttribute, render, waitFor } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import MyDevices from "./MyDevices";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<Router history={history}>{children}</Router>
	</Provider>
);

export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper });
};

const mockErrorResponse = {
	"status": "1"
}

const mockIsLoggedInSuccessResponse = {
	response: {
		parent_id: "78516744",
		session_stringvalue: "ZTEATV412001224226580292a69e33"
	}
}

const mockDeviceListSuccessResponse = {
	response: {
		devices: [
			{
				user_device_id: "138182193",
				device_id: "138182193",
				real_device_id: "cd44bb94-2cf5-4e19-bdad-3a712dc203c7",
				device_category: "tv",
				device_manufacturer: "androidtv",
				device_model: "androidtv",
				device_type: "tv",
				device_name: "androidtv",
				device_so: "Android+12",
				date_since: "2024-06-29T00:00:00-0300",
				date_modified: "2024-06-29T00:00:00-0300"
			},
			{
				user_device_id: "138182193",
				device_id: "138182193",
				real_device_id: "cd44bb94-2cf5-4e19-bdad-3a712dc203c7",
				device_category: "tv",
				device_manufacturer: "androidtv",
				device_model: "androidtv",
				device_type: "tv",
				device_name: "androidtv",
				device_so: "Android+12",
				date_since: "2024-06-29T00:00:00-0300",
				date_modified: "2024-06-29T00:00:00-0300"
			}
		]
	}
}


describe('My Devices Landing page test', () => {

	test('should render without api mock data', () => {
		initialState.login = {
			isLoggedIn: mockErrorResponse
		}
		initialState.settingsReducer = {
			devicesList: mockErrorResponse
		}
		initialState.settingsReducer = {
			logoutAllDevices: {
				response: false
			}
		}
		renderWithState(<MyDevices />)
	})

	test('should render with api mock data', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		}
		initialState.settingsReducer = {
			devicesList: mockDeviceListSuccessResponse
		}
		initialState.settingsReducer = {
			logoutAllDevices: {
				response: true
			}
		}
		renderWithState(<MyDevices />)
	})

	test('my devices back button click', () => {
		const { container } = renderWithState(<MyDevices />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'back-button')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		fireEvent.keyUp(buttonClick, { keyCode: '10009' })
		fireEvent.keyUp(buttonClick, { keyCode: '405' })
	})

	test('my devices logout all button click', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		};
		initialState.settingsReducer = {
			devicesList: mockDeviceListSuccessResponse,
			logoutAllDevices: {
				response: false
			}
		};
		const { container } = renderWithState(<MyDevices />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'myDevicesLogout')
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})
})
