import React, { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import './Settings.scss'
import moment from 'moment'
import { pushScreenViewEvent } from '../../GoogleAnalytics'


const ReminderViewSettings = props => {

	const navigate = useNavigate()

	const region = localStorage.getItem('region')
	let reminderIndex = 0

	const reminderList = useSelector(state => state?.epg?.ReminderLive?.response)
	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
 	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

	const translations =
		apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const apilanguage = translations?.language?.[region]

	const today = new Date()
	const now =
		today.getFullYear() +
		'/' +
		(today.getMonth() + 1).toString().padStart(2, '0') +
		'/' +
		today.getDate().toString().padStart(2, '0') +
		' ' +
		today.toLocaleTimeString('en-US', { hour12: false })

	const todayDate = today.getFullYear() +
		'/' +
		(today.getMonth() + 1).toString().padStart(2, '0') +
		'/' +
		today.getDate().toString().padStart(2, '0')


	let reminderAvailable
	if (reminderList && typeof reminderList != 'string') {
		reminderAvailable = reminderList.find(each => now < each?.data?.begintime)
	}

	useEffect(() => {
		reminderAvailable && document.getElementById('reminder-id-1')?.focus()
		!reminderAvailable && document.getElementById('recordatoriosButton')?.focus()
	}, [reminderAvailable])

	useEffect(()=>{
 		pushScreenViewEvent({screenName:'reminder',screenData:userDetails,prevScreenName:'settings' })
	},[])

	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	}

	const isReminderFutureEvent = programData => {
		if (now < programData?.data?.endtime) {
			reminderIndex += 1
			return true
		} else {
			return false
		}
	}

	const isToday = programDate => {
		const eventDateConversion = programDate.split(' ')
		if (todayDate == eventDateConversion[0]) {
			return true
		} else {
			return false
		}
	}

	const handleReminderClick = (e, programData) => {
		e.preventDefault();
		navigate('/settings/actionScreenSettings', {
			state: {
				data: programData,
				pageName: 'recordatorios',
				screenActive: props?.screenName,
				defaultFocus: props?.defaultFocus
			}
		})
	}

	const handleKeyUp = (e, programData) => {
		e.preventDefault()

		if (e?.keyCode == 405) {
			navigate('/settings/actionScreenSettings', {
				state: {
					data: programData,
					pageName: 'recordatorios',
					screenActive: props?.screenName,
					defaultFocus: props?.defaultFocus
				}
			})
		}

		const keycode = e?.keyCode
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
			}
			if (codes.yellowcode == keycode) {
				navigate('/settings/actionScreenSettings', {
					state: {
						data: programData,
						pageName: 'recordatorios',
						screenActive: props?.screenName,
						defaultFocus: props?.defaultFocus
					}
				})
			}
		}
	}

	return (
		<div className="reminder-settings">
			<span className="reminder-title">
				{truncateText('profiles_settings_reminders_title', 30)}
			</span>
			<hr className="horizontal-line" />
			{reminderAvailable ? (
				<>
					<span className="reminder-sub-title">
						{truncateText('reminder_settings', 100)}
					</span>
					<div className="reminder-events-div focusable">
						{reminderList &&
							typeof reminderList != 'string' &&
							reminderList?.map(
								(each, index) =>
									isReminderFutureEvent(each) && (
										<button
											className="reminder-button focusable"
											onClick={(e) => handleReminderClick(e, each)}
											onKeyUp={(e) => handleKeyUp(e, each)}
											key={index}
											id={`reminder-id-${reminderIndex}`}
										>
											<img
												className="reminder-channel-image"
												src={each?.data?.channel_image}
											/>
											<span className="reminder-event-name">
												{each?.data?.name}
											</span>
											<span className="reminder-channel-name">
												{each?.data?.channel_name}
											</span>
											<span className="reminder-begin-time">
												{isToday(each?.data?.endtime) ? `${truncateText('recording_today_format', 5)} ${moment(each?.data?.endtime).format('HH:mm [HS]')}` : moment(each?.data?.endtime).format('DD/MM/YYYY HH:mm [HS]')}
											</span>
										</button>
									)
							)}
					</div>
					<span className="more-info-shortcut">
						{truncateText('show-option-message-1', 20)}
						<img src={'images/yellow_shortcut.png'} />
						{truncateText('show-option-message-2', 40)}
					</span>
				</>
			) : (
				<div className="reminder-not-available">
					<span className="reminder-not-available-title">
						{truncateText('reminder-empty-list', 40)}
					</span>
					<span className="reminder-not-available-sub-title">
						{truncateText('how-to-add-reminder-1', 80)}
						<img className='reminder-clock-icon' src={'images/clock_icon.png'} />.
						{truncateText('how-to-add-reminder-2', 120)}<br />
						{truncateText('how-to-add-reminder-3', 80)}
					</span>
				</div>
			)}
		</div>
	)
}

export default React.memo(ReminderViewSettings)
