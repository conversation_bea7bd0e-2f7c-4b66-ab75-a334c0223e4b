export const COMMON_URL = {
  BASE_URL:
    typeof tizen == 'undefined'
      ? 'http://mfwktv2lg-api.clarovideo.net'
      : 'https://mfwktv2samsung-api.clarovideo.net',
  authpn: 'tataelxsi',
  authpt: 'vofee7ohhecai',
  prueba: 'test2',
  api_version: 'v5.94',
  device_category: 'tv',
  device_id: localStorage.getItem('setDeviceID') ?? '23fe0340a788bd5cab7d82b5d37b2c6e',
  device_manufacturer: typeof tizen == 'undefined' ? 'lg' : 'samsung',
  device_name: typeof tizen == 'undefined' ? 'lg' : 'samsung',
  device_model: typeof tizen == 'undefined' ? 'web0s' : 'tizen',
  device_so: typeof tizen == 'undefined' ? 'webos' : 'tizen',
  device_type: 'tv',
  sessionKey: '3c5b773dc773765539920a1ac3d944fa-guatemala',
  user_hash:
    'NzQ0MDIyMDJ8MTY3MjA1NjY3N3xiNDhkMjRiYTdiMGZjYWJjMmRjOTNhZjMzNjdlMDdjYjIxM2FhNmZlYzFkMzk2NDcyZg==',
  player: 'bitmovin',
  npawAdapterString:
    'https://adapters.plugin.npaw.com/adapters-json/video/bitmovin8/7.0.9-bitmovin8-js.json'
    
}

//common url for lg

// export const COMMON_URL = {
//   BASE_URL: 'http://mfwktv2lg-api.clarovideo.net',
//   authpn: 'tataelxsi',
//   authpt: 'vofee7ohhecai',
//   prueba: 'test2',
//   api_version: 'v5.94',
//   device_category: 'tv',
//   device_id: '23fe0340a788bd5cab7d82b5d37b2c6e',
//   device_manufacturer: 'lg',
//   device_model: 'web0s',
//   device_type: 'tv',
//   device_name : 'lg',
//   sessionKey: '3c5b773dc773765539920a1ac3d944fa-peru',
//   user_hash:
//     'NzQ0MDIyMDJ8MTY3MjA1NjY3N3xiNDhkMjRiYTdiMGZjYWJjMmRjOTNhZjMzNjdlMDdjYjIxM2FhNmZlYzFkMzk2NDcyZg==',
// }

export const URL = {
  //sms urls
  START_HEADER_INFO_URL: `${COMMON_URL.BASE_URL}/services/user/startheaderinfo?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
  GET_USERINFO_URL: `${COMMON_URL.BASE_URL}/services/user/getuserinfo?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&prueba=${COMMON_URL.prueba}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}`,
  LOGIN_URL: `${COMMON_URL.BASE_URL}/services/user/login?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&includpaywayprofile=1%20HTTP/1.1`,
  //LOGIN_URL: `${COMMON_URL.BASE_URL}/services/user/login?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&includpaywayprofile=1%20HTTP/1.1`,
  REGISTER_URL: `${COMMON_URL.BASE_URL}/services/user/register?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&prueba=${COMMON_URL.prueba}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&accepterms=1`,
  PASSWORD_URL: `${COMMON_URL.BASE_URL}/services/user/sendtemporalcode?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&terminos=0&is_kids=0`,
  SET_TERMS_AND_CONDITION: `${COMMON_URL.BASE_URL}/services/user/settermsandconditions?terminos=1&api_version=${COMMON_URL.api_version}&format=json&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}`,
  IMAGE_URL: `${COMMON_URL.BASE_URL}/services/apa/asset?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}`,
  USER_AUTHDEVICE_URL: `${COMMON_URL.BASE_URL}/services/user/authdevice?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&serial_id=ZTEATV41200799983&format=json&device_name=${COMMON_URL.device_name}&includpaywayprofile=1`,
  PUSH_SESSION_URL: `${COMMON_URL.BASE_URL}/services/user/push_session?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&device_name=${COMMON_URL.device_name}`,
  //PUSH_SESSION_URL: `${COMMON_URL.BASE_URL}/services/user/push_session?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&device_name=${COMMON_URL.device_name}`,

  //v1 urls
  NAV_V1_URL: `${COMMON_URL.BASE_URL}/services/nav/v1/experience?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json`,
  LOGIN_V1_URL: `${COMMON_URL.BASE_URL}/services/user/v1/ott/login?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_type=${COMMON_URL.device_type}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_id=${COMMON_URL.device_id}&format=json&includpaywayprofile=1%20HTTP/1.1`,
  USER_LOGOUT_V1_URL: `${COMMON_URL.BASE_URL}/services/user/v1/ott/logout?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_category=${COMMON_URL.device_category}`,
  //cms urls
  NAV_URL: `${COMMON_URL.BASE_URL}/services/nav/data?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}&format=json`,
  BANNER_URL: `&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
  PREMIUM_URL: `${COMMON_URL.BASE_URL}`,

  EPG_MENU_URL: `${COMMON_URL.BASE_URL}/services/epg/menu?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}`,
  EPG_VERSION_URL: `${COMMON_URL.BASE_URL}/services/epg/version?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}`,
  EPG_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/epg/channel?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}`,
  EPG_LINEUP_URL: `${COMMON_URL.BASE_URL}/services/epg/lineup?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}`,
  CONTENT_PARAM_URL: `&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json`,
  FAVORITE_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/favorited?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_so=${COMMON_URL.device_so}&format=json&live=true`,
  CMS_LEVEL_V1_URL: `${COMMON_URL.BASE_URL}/services/cms/v1/level?device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_id=${COMMON_URL.device_id}&format=json`,
  CMS_LEVEL_USER_V1_URL: `${COMMON_URL.BASE_URL}/services/cms/v1/leveluser?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json`,
  //Live tv
  //REMINDER
  // REMINDER_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/reminder/list?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&device_type=${COMMON_URL.device_type}&format=json`,
  // ADDREMINDER_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/reminder/create?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  // DELETE_REMINDER_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/reminder/delete?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,

  //dub
  REMINDER_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/reminder/list?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json`,
  ADDREMINDER_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/reminder/create?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  DELETE_REMINDER_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/reminder/delete?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,

  //payway
  PAYWAY_TOKEN_URL: `${COMMON_URL.BASE_URL}/services/payway/linealchannels?authpn=${COMMON_URL.authpn}&&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json`,
  PayWay_Purchase_Button_Info: `${COMMON_URL.BASE_URL}/services/payway/purchasebuttoninfo?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&object_type=A`,

  ADDFAVOURITE_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/favoriteadd?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&live=true&object_type=5`,
  DELETE_FAVOURITE_CHANNEL_URL: `${COMMON_URL.BASE_URL}/services/user/favoritedel?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&live=true&object_type=5`,

  LIVETV_RECORD_LIST_URL: `${COMMON_URL.BASE_URL}/services/recordings/v1/list?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json`,
  ADD_LIVETV_SERIES_RECORD_URL: `${COMMON_URL.BASE_URL}/services/recordings/series/add?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  ADD_LIVETV_EPISODE_RECORD_URL: ` ${COMMON_URL.BASE_URL}/services/recordings/add?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  DELETE_LIVETV_SERIES_RECORD_URL: `${COMMON_URL.BASE_URL}/services/recordings/series/delete?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}`,
  DELETE_LIVETV_EPISODE_RECORD_URL: `${COMMON_URL.BASE_URL}/services/recordings/delete?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}`,
  //VOD_MOVIES
  VOD_MOVIES_CONTENT_CAST_URL: `${COMMON_URL.BASE_URL}/services/content/data?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  // mlt
  VOD_MOVIES_RECOMENDATION_URL: `${COMMON_URL.BASE_URL}/services/content/recommendations?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json&`,
  ADD_VOD_MOVIES_WATCHLIST_URL: `${COMMON_URL.BASE_URL}/services/user/favoriteadd?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&`,
  DEL_VOD_MOVIES_WATCHLIST_URL: `${COMMON_URL.BASE_URL}/services/user/favoritedel?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&object_type=1`,

  //VOD_SERIES
  GET_WATCHLIST_URL: `${COMMON_URL.BASE_URL}/services/user/favorited?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&`,
  ADD_WATCHLIST_URL: `${COMMON_URL.BASE_URL}/services/user/favoriteadd?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&object_type=1&`,
  DEL_WATCHLIST_URL: `${COMMON_URL.BASE_URL}/services/user/favoritedel?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&object_type=1&`,
  VOD_SERIES_URL: `${COMMON_URL.BASE_URL}/services/content/serie?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&`,
  VOD_SERIES_CAST_URL: `${COMMON_URL.BASE_URL}/services/content/data?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&`,
  VOD_SERIES_MLT_URL: `${COMMON_URL.BASE_URL}/services/content/v1/recommendations?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json&`,

  //Search
  Search_RECOMENDATION_URL: `${COMMON_URL.BASE_URL}/services/ranking/list?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=v5.93&format=json&from=0&quantity=10`,
  SEARCHPREDICTIVELINEAR: `${COMMON_URL.BASE_URL}/services/search/linealpredictive?api_version=${COMMON_URL.api_version}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&tenant_code=clarovideo`,
  MULTICONTENT_SEARCH_URL: `${COMMON_URL.BASE_URL}/services/payway/paymentservice/multicontentsearch?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}`,
  MULTICONTENT_PAYMENT_INFO: `${COMMON_URL.BASE_URL}/services/payway/paymentservice/v1/multipurchasebuttoninfo?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_so=${COMMON_URL.device_so}&device_id=${COMMON_URL.device_id}&device_name=${COMMON_URL.device_name}&format=json`,
  SEARCH_PROGRESSBAR_BOOKMARK_URL: `${COMMON_URL.BASE_URL}/services/user/getbookmark?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json`,
  Talent_Search_RECOMENDATION_URL: `${COMMON_URL.BASE_URL}/services/search/vertical?device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&format=json&quantity=50`,
  //Settings
  IS_LOGGED_IN_V1_OTT_URL: `${COMMON_URL.BASE_URL}/services/user/v1/ott/isloggedin?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&format=json`,
  IS_LOGGED_IN_V1_URL: `${COMMON_URL.BASE_URL}/services/user/v1/isloggedin?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}`,
  DEVICES_LIST_URL: `${COMMON_URL.BASE_URL}/services/device/list?api_version=${COMMON_URL.api_version}&format=json&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_id=${COMMON_URL.device_id}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
  PASSWORD_CHANGE_URL: `${COMMON_URL.BASE_URL}/services/user/password?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  MODIFY_CONTROL_PIN_URL: `${COMMON_URL.BASE_URL}/services/user/v1/modifycontrolpin?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&format=json`,
  TRANSACTION_HISTORY_URL: `${COMMON_URL.BASE_URL}/services/payway/renthistory?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  USER_LOGOUT_URL: `${COMMON_URL.BASE_URL}/services/user/logout?api_version=${COMMON_URL.api_version}&format=json&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_category=${COMMON_URL.device_category}`,
  USER_LOGOUT_ALL_DEVICES_URL: `${COMMON_URL.BASE_URL}/services/user/logout?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_id=${COMMON_URL.device_id}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&all_sessions=true`,
  STATUS_CONTROL_PIN_URL: `${COMMON_URL.BASE_URL}/services/user/statuscontrolpin?device_id=${COMMON_URL.device_id}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}`,
  GET_CONTROL_PIN_URL: `${COMMON_URL.BASE_URL}/services/user/controlpin/get?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&`,
  CONTROL_PIN_STATUS_URL: `${COMMON_URL.BASE_URL}/services/user/v1/controlpin/status?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&format=json&`,
  CHANGE_CONTROL_PIN_URL: `${COMMON_URL.BASE_URL}/services/user/controlpin/changecode?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&`,
  SET_CONTROL_PIN_URL: `${COMMON_URL.BASE_URL}/services/user/controlpin/set?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&`,
  LOCKED_CHANNELS_LIST_URL: `${COMMON_URL.BASE_URL}/services/user/controlpin/channels/list?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&`,
  CHECK_CONTROL_PIN_URL: `${COMMON_URL.BASE_URL}/services/user/checkcontrolpin?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&`,
  APP_META_DATA_URL: `${COMMON_URL.BASE_URL}/services/apa/metadata?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&userId=76325562`,
  LOCKED_CHANNEL_ADD_URL: `${COMMON_URL.BASE_URL}/services/user/controlpin/channels/add?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&`,
  LOCKED_CHANNEL_DELETE_URL: `${COMMON_URL.BASE_URL}/services/user/controlpin/channels/delete?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&`,
  CHANGE_CONTROL_PIN: `${COMMON_URL.BASE_URL}/services/user/changecontrolpin?device_id=${COMMON_URL.device_id}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&region=${COMMON_URL.region}&HKS=web6138f5cd41851&user_id=42407119&current_pin=123456&new_pin=654321&repeat_pin=654321`,
  REMIND_CONTROL_PIN: `${COMMON_URL.BASE_URL}/services/user/remindcontrolpin?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&`,
  DISABLE_CONTROL_PIN: `${COMMON_URL.BASE_URL}/services/user/controlpin/disable?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&format=json&`,
  ACTIVE_SUBSCRIPTIONS_URL: `${COMMON_URL.BASE_URL}/services/payway/data?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  CANCEL_SUBSCRIPTIONS_URL: `${COMMON_URL.BASE_URL}/services/payway/cancelsuscription?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json`,
  GET_SUBSCRIPTIONS_INFO_URL: `${COMMON_URL.BASE_URL}/services/payway/v1/purchasebuttoninfo?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json&`,
  ADD_PAYMENTS_URL: `${COMMON_URL.BASE_URL}/services/payway/buyconfirm/api_version/v7.1?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&region=${COMMON_URL.region}&payway=amcogate`,
  CONFIRM_PAYMENTS_URL: `${COMMON_URL.BASE_URL}/services/payway/paymentservice/buyconfirm/api_version/v7.2?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}`,
  PROMO_CODE_URL: `${COMMON_URL.BASE_URL}/services/payway/confirm?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&region=${COMMON_URL.region}&format=json&buylink=Q1pNaE9jWG5LYzNtN0RWV2hQc1NhbHVtQkhZQUpEU2RYME5VQVdzR2M3SU1Ud1ozSmV0UWxXSXo0Z3BiT3Zvd0JVYldxbk5XY3Zva04wRTYyWCtPZjBCNUkya2htQ3BzWDN4OUhld25mOGxSYWZCOS9wZWtCT0FSemxMTTcxUkNLVjZIQ2cyN1VsQ0NTKzMreUZ1MFRwWVFnNWpKUFFRdHJXeWd0ZkJzZDE2cFFOYk5KWDBmWkpic2ZSYnZobzJJc0FYV05EWFNDMGs1REpKeU4zdWZxMXlRblNkWUIzek5lOHhzVnNjaHo2RmszYzY4anRadlVUM2hETmQybzdmdEg2R3BOZ29tempRRDR4Y0FRYVcvOFVsbzVkUFIxZUFVRFlUMQ%3D%3D&payway=promogate&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4QFkkc8RsDJVJnm5bQvr2gsUzwadlt_YP4zPp5wOzAA`,
  PAYWAY_WORKFLOW_URL: `${COMMON_URL.BASE_URL}/services/payway/workflowstart?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&region=${COMMON_URL.region}&format=json&group_id=1088786&object_type=G&offer_id=********&suscription_id=null`,
  APA_META_DATA_HELP_URL: `${COMMON_URL.BASE_URL}/services/apa/metadata?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&user_id=0`,
  PROMO_CODE_URL: `${COMMON_URL.BASE_URL}/services/payway/confirm?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&region=${COMMON_URL.region}&format=json&buylink=Q1pNaE9jWG5LYzNtN0RWV2hQc1NhbHVtQkhZQUpEU2RYME5VQVdzR2M3SU1Ud1ozSmV0UWxXSXo0Z3BiT3Zvd0JVYldxbk5XY3Zva04wRTYyWCtPZjBCNUkya2htQ3BzWDN4OUhld25mOGxSYWZCOS9wZWtCT0FSemxMTTcxUkNLVjZIQ2cyN1VsQ0NTKzMreUZ1MFRwWVFnNWpKUFFRdHJXeWd0ZkJzZDE2cFFOYk5KWDBmWkpic2ZSYnZobzJJc0FYV05EWFNDMGs1REpKeU4zdWZxMXlRblNkWUIzek5lOHhzVnNjaHo2RmszYzY4anRadlVUM2hETmQybzdmdEg2R3BOZ29tempRRDR4Y0FRYVcvOFVsbzVkUFIxZUFVRFlUMQ%3D%3D&payway=promogate&pincode=111111111111111111111&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4QFkkc8RsDJVJnm5bQvr2gsUzwadlt_YP4zPp5wOzAA`,

  //User Profiles
  PROFILE_URL: `${COMMON_URL.BASE_URL}/services/user/profile/create?device_id=${COMMON_URL.device_id}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&format=json`,
  PROFILE_READ_URL: `${COMMON_URL.BASE_URL}/services/user/profile/read?device_id=${COMMON_URL.device_id}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&format=json`,
  PROFILE_DELETE_URL: `${COMMON_URL.BASE_URL}/services/user/profile/delete?device_id=${COMMON_URL.device_id}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}`,
  // PROFILE_AVATAR_URL: `${COMMON_URL.BASE_URL}/services/user/profile/avatars?device_id=${COMMON_URL.device_id}}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&format=json`,
  PROFILE_UPDATE_URL: `${COMMON_URL.BASE_URL}/services/user/profile/update?device_id=${COMMON_URL.device_id}}&device_category=${COMMON_URL.device_category}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&device_manufacturer=${COMMON_URL.device_manufacturer}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}`,
  PROFILE_AVATAR_URL: `${COMMON_URL.BASE_URL}/services/user/profile/avatars/collection?format=json&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_category=${COMMON_URL.device_category}&api_version=${COMMON_URL.api_version}`,
  //getmediaLive
  GETMEDIA_LIVE: `${COMMON_URL.BASE_URL}/services/player/getmedia?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&preview=0`,
  GETLINEAL_PAYWAY: `${COMMON_URL.BASE_URL}/services/payway/linealchannels?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&device_so=${COMMON_URL.device_so}&format=json&preview=0&ts=1&npvr=1`,
  GETMEDIA_VOD: `${COMMON_URL.BASE_URL}/services/player/getmedia?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json`,
  //continuewatch delete
  GETCONTINUE_WATCH_DEL: `${COMMON_URL.BASE_URL}/services/user/seendel?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}`,
  GETCONTINUE_WATCH_LIST: `${COMMON_URL.BASE_URL}/services/user/seen?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&api_version=${COMMON_URL.api_version}&device_id=${COMMON_URL.device_id}&format=json&`,
  GETSEENLAST_VCARD: `${COMMON_URL.BASE_URL}/services/user/seenlast?api_version=${COMMON_URL.api_version}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json`
}
