.btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.idioma-button {
  height: 64px;
  width: 226px;
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 42px;
  letter-spacing: 0;
  line-height: 49px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 39px;
}

.idioma-button-contents {
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 42px;
  letter-spacing: 0;
  line-height: 49px;
}

.idioma-button:focus,
.idioma-button-active {
  box-sizing: border-box;
  height: 64px;
  width: 226px;
  border: 4px solid #FFFFFF;
  border-radius: 30px;
  color: #FFFFFF;
  font-family: <PERSON><PERSON>;
  font-size: 44px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 39px;
}

.btn-text {
  height: 64px;
  width: 368px;
  color: #FFFFFF;
  font-family: <PERSON><PERSON>;
  font-size: 42px;
  letter-spacing: 0;
  line-height: 49px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text:focus,
.recordatorios-active {
  box-sizing: border-box;
  height: 64px;
  width: 368px;
  border: 4px solid #FFFFFF;
  border-radius: 30px;
  font-size: 44px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 52px;
  font-family: Roboto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-parental-btn {
  height: 64px;
  width: 409px;
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 42px;
  letter-spacing: 0;
  line-height: 49px;
  margin-left: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-parental-btn:focus,
.parentalControl-active {
  box-sizing: border-box;
  height: 64px;
  width: 409px;
  border: 4px solid #FFFFFF;
  border-radius: 30px;
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 44px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}


.title-text {
  height: 47px;
  width: 209px;
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 40px;
  letter-spacing: 0;
  line-height: 43px;
}

.horizontal-line {
  display: block;
  height: 1px;
  border: 0;
  box-sizing: border-box;
  border: 0.67px solid #475865;
  margin: 1em 0;
  padding: 0;
}

.text-detail-container {
  display: flex;
  justify-content: space-around;
}

.text-detail-title {
  height: 19px;
  color: #FFFFFF;
  font-family: 'Roboto';
  font-size: 16px;
  letter-spacing: 0;
  line-height: 17px;
}

.dropdown-container {
  float: left;
  overflow: hidden;
}

.dropdown-btn {
  height: 24px;
  width: 133px;
  color: #FFFFFF;
  font-family: 'Roboto';
  font-size: 20px;
  letter-spacing: 0;
  line-height: 22px;
  box-sizing: border-box;
  border: 2px solid #FFFFFF;
  border-radius: 20px;
}