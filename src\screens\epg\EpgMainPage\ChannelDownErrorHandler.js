import React from 'react'
import { useSelector } from 'react-redux'
import './ChannelDownErrorHandler.scss'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'

const region = localStorage.getItem('region')
const alertImage = 'images/ic_alert.png'

const  ChannelDownErrorHandler = (props) => {

    const Channel_Name = 'HBO';
    const Channel_Number = '199';

    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
    const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const apilanguage = translations?.language?.[region]
    pushScreenViewEvent({screenName:'channelError',screenData:userDetails, prevScreenName:'livePlayer'})

  return (
    <div className="channel-err-screen">
      <img className="alert-icon" src={alertImage} />
      <p className="unavailable-channel-msg">
        {apilanguage?.fallenChannel_access_titleChannelNotAvailable_label || `El canal ${Channel_Name} ${Channel_Number} no está disponible por ahora`}
      </p>
      <p className="channel-instruction-msg">
        {apilanguage?.fallenChannel_alert_titleNotAvailable1_label || 'Continúa viendo tu programación favorita probando con otro canal.'}
      </p>
    </div>
  )
}

export default ChannelDownErrorHandler
