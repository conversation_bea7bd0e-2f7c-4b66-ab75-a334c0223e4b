import React from 'react'
import '../../styles/MosaicSkeletonLoading.css'
function MosaicSkeletonLoading() {
  return (
    <div>
      <div className="mosaic-topgrid-container-shimmer">
        <div className="mosaic-topgrid-content-shimmer">
          {Array.from({ length: 6 }, (_, index) => (
            <button className="mosaic-topgrid-item-shimmer">
              <div className="mosaic-topgrid-image"></div>
              <p className="mosaic-topgrid-title"></p>
            </button>
          ))}
        </div>
      </div>
      <div className="mosaic-grid-container-shimmer">
        {Array.from({ length: 12 }, (_, index) => (
          <div key={index} className="mosaic-grid-item-shimmer">
            <div className="mosaic-channel-number-shimmer"> </div>
            <div className="mosaic-channel-image-shimmer"></div>
            <div className="mosaic-channel-title-shimmer"></div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default MosaicSkeletonLoading
