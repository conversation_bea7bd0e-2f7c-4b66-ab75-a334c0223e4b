.deleteprofile_btn {
    /* margin-right: 36rem; */
    /* margin-top: 67px; */
    /* margin-left: 781px; */
    width: 330px;
    font-size: 30px;
    height: 72px;
    color: #EEEEEE;
    background: #2E303D 0% 0% no-repeat padding-box;
    border-radius: 44px;
    opacity: 1;
    text-align: center;
}

.editprofile_container {
    display: flex;
    justify-content: space-evenly;
    width: 900px;
    margin: 120px auto 0px auto;
}

.editbackbutton{
    margin-top: 50px;
    position: fixed;
    bottom: 5px;
}
.editprofilesave {
    width: 330px;
    font-size: 30px;
    height: 72px;
    color: #EEEEEE;
    background: #2E303D 0% 0% no-repeat padding-box;
    border-radius: 44px;
    opacity: 1;
    text-align: center;
}

.editprofilesave:focus,
.editprofilesave:active,
.deleteprofile_btn:focus,
.deleteprofile_btn:active {
    box-shadow: 0px 0px 5px #981C15;
    border: 3px solid #981C15;
    outline: 1px solid #981C15;
    background: #981C15 0% 0% no-repeat padding-box;
}

.EditProfile {
    top: 0px;
    left: 0px;
    width: 1920px;
    height: 1080px;
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

/* .EditProfile {
    top: 0px;
    left: 0px;
    width: var(--maxWidth);
    height: var(--maxHeight);
   
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
} */

.app-logo {
    width: 1920px;
    height: 1080px;
}

.logo-img {
    margin-top: 51px;
    margin-left: 96px;
    width: 149px;
    height: 41px;
    opacity: 1;
}

.edit_heading {
    margin-top: 74px;
    left: 873px;
    height: 57px;
    font-weight: 'Regular';
    text-align: center;
    font-family: "Roboto";
    font-size: 48px;
    letter-spacing: 0px;
    color: #F1F2F3;
    opacity: 1;

}

.editprof_image {
    padding-left: 55rem;
    width: 128px;
    height: 128px;
}

.edit_title {
    margin-top: 40px;
    left: 787px;
    height: 35px;
    text-align: center;
    font-size: 30px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.edit_container {
    height: 135px
}

.edit_name {
    margin-left: 38rem;
    width: 109px;
    height: 33px;
    text-align: left;
    font-size: 28px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.edit_chara {
    margin-top: 4rem;
    margin-left: 29rem;
    width: 59px;
    height: 33px;
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.edit_radio {
    top: 531px;
    margin-left: 612px;
    width: 42px;
    height: 47px;
    border: 3px solid #34353BE8;
    border-radius: 12px;
    opacity: 1;
    margin-top: 17px;
}

.edit_radiobox {
    margin-top: 57px;
}


.editkidsprofile {
    margin-left: 573px;
    margin-top: -47px;
}

.editkids_profname {
    text-align: left;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
    font-size: 28px;
    font-family: 'Roboto';
    margin-left: 6rem;

}

.profile-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4.5rem;
    background: transparent linear-gradient(90deg, #2B2C31F2 0%, #34353BF2 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 20px #00000029;
    opacity: 1;
    margin-top: 5rem;
    display: flex;
    justify-content: space-between;
}

.footer-text {
    width: 57px;
    height: 25px;
    text-align: left;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    font-family: "Roboto";
    font-size: 22px;
    margin-right: 57rem;
    margin-top: 1.3rem;
}

.profile_backbtn {
    margin-top: 0.9rem;
    width: 24px;
    height: 24px;
    margin-left: 57rem;
}