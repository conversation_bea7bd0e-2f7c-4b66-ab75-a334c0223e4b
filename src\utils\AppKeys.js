import { CURRENT_PLATFORM } from './devicePlatform'

export const getAppKey = (tv) => {
  const platformKeys = {
    // New platform names from CURRENT_PLATFORM
    'samsung': '6afec3cba19383484321f79d226a2c5d',
    'lg': '8a4cbe65411f6c00e161c110c10a9a33',
    'netrange': '0986cfcd5908d72a7885e85ba19b1793',
    'hisense': '18a1166ff94a2fbe743eee3c22e8726f',
    'zeasn': '6e60e54e4a6ed8c4e26458a22dab98dd',
  }

  // If tv argument is provided, use it directly
  if (tv) {
    return platformKeys[tv] || platformKeys[CURRENT_PLATFORM] || platformKeys['lg']
  }

  // Otherwise use current platform
  return platformKeys[CURRENT_PLATFORM] || platformKeys['lg']
}
