import React from "react";
import { fireEvent, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import AddPaymentsMobile from "./AddPaymentMobile";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mockerrorresponse = {
  "status": "1"
}
const mocksuccessresponse = {
buyToken: "Q1pNaE9jWG5LYzNtN0RWV2hQc1NhM1RlSitwZE0rK1NVSFZhR1NRMTRPSlRTbTVJOS9aZ1Q0Q0lSZjFMQVdsQzhMK21WbVlsQVNZZjlqRkRwQjd6N1ZLY0UxUTN1cHFOSnl1VUsrUWpUaDRRQzJOSkY1MU9kQkhEVk9MaURTT0NySWs5NE5hbHlVV1lEUGpHK3VQK3lNdEpBN2dhVi9POGt5K3dmMzFuWkE3RHVmVElzY3NRelF0RHB4LzZpY2diZWJZVlBjMnpkQlpaUVNacm5iQ29Od21aTzF5VnZURjlSYmVxUU14SUtlOWM1MjJvNTA5bUJ2cXlnbFpxYUt5MFhLTVFrRTF3M0xJUlBYQUlUWm1naTUzazAxZz0=",
extra_params: "{\"account\":\"********\"}",
token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3MDI4OTM4MjgsImV4cCI6MTcwODA3ODEyOCwidXNyIjp7InVzZXJfaWQiOiI4ODg3MjY1NSIsInVzZXJfdHlwZSI6IkNQRU5PVklQIiwidXNlcm5hbWUiOiJwZXJ1MDFAZ21haWwuY29tIiwiZW1haWwiOiJwZXJ1MDFAZ21haWwuY29tIiwiZmlyc3RuYW1lIjoiQWRtaW5pc3RyYWRvciIsImxhc3RuYW1lIjoiQXBlbGxpZG8iLCJjb3VudHJ5X2NvZGUiOiJQRSIsInJlZ2lvbiI6InBlcnUiLCJhY2NlcHRlZF90ZXJtcyI6MSwiZ2FtaWZpY2F0aW9uX2lkIjoiNWMwNDZhYTZmZTAzZmY3NWMwN"
}


const mockdata= {
    translations:{
      language:{
        peru:{
          "atv_logout_confirmation":'¿Estás seguro de cerrar sesión?',
        },
      }
    }
  }
  describe('Landing page test', () => {
test('should render api mock data', () => {
    initialState.settingsReducer = {
        addPayments: mockerrorresponse,
    }
    renderWithState(<AddPaymentsMobile />)
})


test('should render api mock data', () => {
    initialState.settingsReducer = {
        addPayments: {
            response: {
                status: 200,
            }
        },
    }
    renderWithState(<AddPaymentsMobile />)
})



  test('input should change after onChange', () => {
    const { container } = renderWithState(<AddPaymentsMobile/>)
     //   const getById = queryByAttribute.bind(null, 'id');
       // const buttonClick = getById(container, 'next')
    let input = container.querySelector('input[name="input-number"]')
    fireEvent.change(input, { target: { value: '9884546123' } })
    renderWithState(<AddPaymentsMobile />)
})
test('handle lg', () => {
  initialState.settingsReducer = {
    addPayments: mockerrorresponse,
}
  const { container } = renderWithState(<AddPaymentsMobile />)
  const getById = queryByAttribute.bind(null, 'id');
  const buttonClick = getById(container, 'paymentmobile-page-back')
  fireEvent.keyDown(buttonClick,{keyCode: '405'})
  fireEvent(
    buttonClick,
    new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
    })
  )
  
})

test('handle cancel trnasction', () => {
  initialState.settingsReducer = {
    addPayments: mockerrorresponse,
}
  const { container } = renderWithState(<AddPaymentsMobile />)
  const getById = queryByAttribute.bind(null, 'id');
  const buttonClick = getById(container, 'trnas-cancel-button')
  fireEvent(
    buttonClick,
    new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
    })
  )
  
})
test('element focuses on mouse enter', () => {
  const element = document.createElement('div');
  const mockFocus = jest.fn();
  element.focus = mockFocus;

  // Create the event handler function
  const onMouseEnter = (e) => {
    e.target.focus();
  };

  // Attach the event handler to the element
  element.onmouseenter = onMouseEnter;

  // Simulate the mouseenter event
  const mouseEnterEvent = new MouseEvent('mouseenter');
  element.dispatchEvent(mouseEnterEvent);

  expect(mockFocus).toHaveBeenCalledTimes(1);
});
  })