import React, { useState, useEffect, useRef } from 'react'
import './HelpAndSettings.scss'
import { useDispatch, useSelector } from 'react-redux'
import {
  getCheckControlPin,
  getClearAllSettingsState,
  getLockedChannelAdd,
  getLockedChannelDelete,
  getLockedChannelsList,
  getRemindControlPin,
  clearCheckControlPin
} from '../../../store/slices/settingsSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import AlphaNumericKeyboard from '../../Keyboard/AlphaNumericKeboard'
import { getAlerts } from '../../../store/slices/EpgSlice'
import {
  getChannelData,
  getRecordPlayer
} from '../../../store/slices/PlayerSlice'
import { getNavTabValue } from '../../../store/slices/HomeSlice'
import { interactionType, PROTECTED_CONTENT, TV } from '../../../GoogleAnalyticsConstants'
import { pushNewInteractionContentEvent } from '../../../GoogleAnalytics'

export const UnlockChannel = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { state } = useLocation()
  const region = localStorage.getItem('region')

  const pinRef = useRef([])

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const remindSecurityPin = useSelector(
    state => state?.settingsReducer?.remindControlPin?.response
  )
  const checkSecurityPin = useSelector(
    state => state?.settingsReducer?.checkControlPin
  )
  const unlockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelDelete
  )
  const lockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelAdd
  )
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)

  const checkFromHomeToLive = useSelector(state => state?.epg?.fromHomeToLive)

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  let sameChannelPlay =
    props?.item?.group_id == localStorage.getItem('live-channel-id')

    const liveChannelId =
    localStorage.getItem('live-playing-channel-id') != 'undefined'
      ? localStorage.getItem('live-playing-channel-id')
      : ''

      let channelIndexForRegressor = epgSevenDaysData?.[1]?.channelResponse?.findIndex(
        itrObj => itrObj.group_id == liveChannelId
      )

  let channelIndex = epgSevenDaysData[1]?.channelResponse.findIndex(
    itrObj => itrObj.group_id == props?.item?.group_id
  )
  const navData = useSelector(state => state?.homeReducer?.navbarData)

  const invisible = 'images/Icono_Onboarding_Close.png'
  const visible = 'images/Icono_Onboarding_Open.png'

  const [pin, setPin] = useState(new Array(6).fill(''))
  const [visbilityIcon, setVisibilityIcon] = useState(invisible)
  const [buttonDisable, setButtonDisable] = useState(true)
  const [unlockFlag, setUnlockFlag] = useState(false)

  const [pinInvalid, setPinInvalid] = useState('')
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [focusedIdx, setFocusedIdx] = useState(0)

  useEffect(() => {
    setKeyboardFocus(true)
    // const blinkerText = () => {
    //   if (document.querySelector('.pin-focused')) {
    //     let p = document.querySelector('.pin-focused').value
    //     if (p?.substr(-1, 1) == '|') {
    //       let removePipe = p?.replace('|', '')
    //       document.querySelector('.pin-focused').value = removePipe
    //     } else if (p?.length == 0) {
    //       document.querySelector('.pin-focused').value = p + '|'
    //     }
    //   }
    // }

    // const blinkerInterval = setInterval(blinkerText, 1000)
    // return () => {
    //   clearInterval(blinkerInterval)
    // }
  }, [])

  useEffect(() => {
    if (checkSecurityPin?.msg === 'OK') {
      let channelIndex = props?.item?.group_id
        ? epgSevenDaysData[1]?.channelResponse.findIndex(
            itrObj => itrObj.group_id == props?.item?.group_id
          )
        : epgSevenDaysData[1]?.channelResponse.findIndex(
            itrObj => itrObj.group_id == props?.item?.id
          )
      if (props?.contentName == 'record' && unlockFlag && !buttonDisable) {
        dispatch(
          getRecordPlayer(props?.selectedContent?.actions?.play?.dashwv)
        ),
          navigate('/vodPlayer', {
            state: {
              page: 'record',
              backfocusid: props?.backfocusid,
              returnPage: props?.returnPage,
              inputValue: props?.inputValue,
              recordTitle: props?.selectedContent?.channel?.event?.name,
              selectedContent: props?.selectedContent
            }
          })
        setUnlockFlag(false)
      }
      const payload = {
        hks: userDetails?.session_stringvalue,
        group_id:
          props?.pageName == '/livePlayer' || props?.pageName == '/vodPlayer'
            ? props?.item?.group_id ?? props?.item?.id
            : props?.item?.id ?? props?.item?.channel_group_id,
        user_hash: userDetails?.session_userhash
      }
      props?.title == 'epg'
        ? // dispatch(
          //   getChannelData({
          //     group_id: props?.item?.group_id ?? props?.item?.id,
          //     timeshift: props?.item?.group?.common?.timeshift,
          //     switchChannel: 'yes',
          //     epgIndex: channelIndex
          //   })
          // ),
          (dispatch(clearCheckControlPin()),
          navigate('/livePlayer', {
            state: {
              showControls: 'live',
              fromUnlockPinPage: true,
              returnPage: props?.returnPage,
              featureTag: props?.featureTag,
              pastProgramData: props?.pastProgramData,
              fromZap: props?.fromZap,
              group_id: props?.item?.group_id ?? props?.item?.id,
              timeshift: props?.item?.group?.common?.timeshift,
              checkPastContent: props?.checkPastContent,
              fromEpg: props?.fromEpg
            },
            replace: true
          }))
        : props?.contentName != 'record' &&
          dispatch(
            props.title == 'Lock'
              ? getLockedChannelAdd(payload)
              : getLockedChannelDelete(payload)
          )
    } else if (checkSecurityPin?.msg === 'ERROR') {
      setPinInvalid(checkSecurityPin?.errors)
      dispatch(getClearAllSettingsState())
      setPin(new Array(6).fill(''))
      document.getElementById('Key_0')?.focus()
      setFocusedIdx(0)
    }
  }, [checkSecurityPin])

  useEffect(() => {
    if (unlockchannel?.msg === 'OK') {
      props?.pageName == '/livePlayer'
        ? dispatch(
            getAlerts({
              image: 'images/Unblock_icon.png',
              message: `Canal ${
                props?.item?.name ?? props?.item?.actual_event?.name
              } ${
                props?.item?.number ?? props?.item?.actual_event?.channel_number
              } debloqueado`,
              status: 'lock'
            })
          )
        : null
      props?.pageName != '/livePlayer' &&
        navigate(props?.pageName, {
          state: { liveTvDetail: true }
        })
      props?.pageName == '/livePlayer' &&
        navigate('/livePlayer', {
          state: { showControls: 'live', fromUnlockPinPage: true },
          replace: true
        })
      const payload = {
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      }
      dispatch(getLockedChannelsList(payload))
    }
  }, [unlockchannel])

  useEffect(() => {
    if (lockchannel?.msg === 'OK') {
      props?.pageName == '/livePlayer'
        ? dispatch(
            getAlerts({
              image: 'images/Mosaic_Icons/icon_locked_channel.png',
              message: `Canal ${props?.item?.name} ${props?.item?.number} Canal bloqueado | Para ver este canal necesitaras tu PIN de seguarided`,
              status: 'lock'
            })
          )
        : null
      props?.pageName != '/livePlayer' &&
        navigate(props?.pageName, {
          state: { liveTvDetail: true }
        })
      props?.pageName == '/livePlayer' &&
        navigate('/livePlayer', {
          state: { showControls: 'live', returnPage: props?.returnPage },
          replace: true
        })
      const payload = {
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      }
      dispatch(getLockedChannelsList(payload))
    }
  }, [lockchannel])

  useEffect(() => {
    if (remindSecurityPin) {
      navigate('/PinConfirmation', {
        state: {
          data: props?.title,
          item: props?.item,
          pageName: props?.pageName
        }
      })
    }
  }, [remindSecurityPin])

  useEffect(() => {
    if (
      pin &&
      pin[0]?.length > 0 &&
      pin[1]?.length > 0 &&
      pin[2]?.length > 0 &&
      pin[3]?.length > 0
    ) {
      setButtonDisable(false)
    } else {
      setButtonDisable(true)
    }
    pin[5]?.length > 0 && document.getElementById('siguiente-button')?.focus()
  }, [pin])

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
    handleTVRemoteKey(event)
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (
      key.yellowcode === keycode ||
      keycode === 10009 ||
      keycode == 405 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
      keycode === 461
    ) {
      pushNewInteractionContentEvent(
        state?.gaContentData,
        PROTECTED_CONTENT,
        TV,
        interactionType?.CANCELAR
      )
      if (
        props?.returnPage === 'search' &&
        (props?.pageName == '/livePlayer' ||
          props?.pageName == 'movies' ||
          props?.pageName == 'series')
      ) {
        navigate('/search', {
          state: { inputValue: props?.inputValue },
          replace: true
        })
      } else if (props?.returnPage == 'myContent') {
        navigate('/home', {
          state: { myContentData: true, backfocusid: props?.backfocusid },
          replace: true
        })
        localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      } else if (props?.returnPage == 'episodescreen') {
        navigate('/episodescreen', {
          state: {
            myContentData: true,
            backfocusid: props?.backfocusid,
            sericetitle: props?.selectedContent?.channel?.event?.name,
            seasonid: props?.selectedContent?.channel?.event?.ext_season_id
          },
          replace: true
        })
        localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      } else {
        props?.pageName == '/livePlayer'
          ? (handleLivePlayerPayload(),
            navigate('/livePlayer', {
              state: { showControls: 'live', pinCancel: true },
              replace: true
            }))
          : props?.returnPage == 'search'
          ? navigate('/search', {
              state: { inputValue: props?.inputValue },
              replace: true
            })
          : navigate(props?.pageName)
      }
    }
  }

  const handleLivePlayerPayload = () => {
    dispatch(
      getChannelData({
        group_id: epgSevenDaysData[1]?.channelResponse[channelIndexForRegressor]?.group_id ?? liveChannelId,
        timeshift:
          epgSevenDaysData[1]?.channelResponse[channelIndexForRegressor]?.group?.common
            ?.timeshift,
        switchChannel: 'yes',
        epgIndex: channelIndexForRegressor
      })
    )
  }

  const handleLgkey = keycode => {
    if (
      keycode == 405 ||
      keycode === 461 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
      keycode == 'backClick' ||
      keycode === 10009
    ) {
      pushNewInteractionContentEvent(
        state?.gaContentData,
        PROTECTED_CONTENT,
        TV,
        interactionType?.CANCELAR
      )
      if (
        props?.returnPage === 'search' &&
        (props?.pageName == '/livePlayer' ||
          props?.pageName == 'movies' ||
          props?.pageName == 'series')
      ) {
        navigate('/search', {
          state: { inputValue: props?.inputValue },
          replace: true
        })
      } else if (props?.returnPage === 'search') {
        navigate('/search', {
          state: { inputValue: props?.inputValue },
          replace: true
        })
      } else if (props?.returnPage == 'myContent') {
        navigate('/home', {
          state: { myContentData: true, backfocusid: props?.backfocusid },
          replace: true
        })
        localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      } else if (props?.returnPage == 'episodescreen') {
        navigate('/episodescreen', {
          state: {
            myContentData: true,
            backfocusid: props?.backfocusid,
            sericetitle: props?.selectedContent?.channel?.event?.name,
            seasonid: props?.selectedContent?.channel?.event?.ext_season_id
          },
          replace: true
        })
        localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      } else {
        props?.pageName == '/livePlayer'
          ? (handleLivePlayerPayload(),
            navigate('/livePlayer', {
              state: { showControls: 'live', pinCancel: true },
              replace: true
            }))
          : navigate(props?.pageName)
      }
    }
  }

  const handleTVRemoteKey = event => {
    const key = event.key
    if (/^\d$/.test(key)) {
      const currentIndex = pin.findIndex(digit => digit === '')
      if (currentIndex !== -1) {
        const newOtp = [...pin]
        newOtp[currentIndex] = key
        key?.length == 1 && setPin(newOtp)
        if (currentIndex < 5) {
          setFocusedIdx(currentIndex + 1)
        }
      }
      setPinInvalid('')
    }
  }

  const handleOTPChange = (element, index) => {
    const value = element.value ?? element

    if (value == 'cl') {
      setPin(pin.map((d, idx) => (idx === focusedIdx ? '' : d)))
      setFocusedIdx(index === 0 ? index : index - 1)
      return
    }
    if (value == 'clr') {
      setPin(new Array(pin.length).fill(''))
      setFocusedIdx(0)
      setPinInvalid('')
      return 
    }     
    if (/^\d$/.test(value)) {
      setPin(pin.map((d, idx) => (idx === index ? value : d)))
      setFocusedIdx(index === 5 ? index : index + 1)
      } else {
        setPin(pin.map((d, idx) => (idx === index ? '' : d)))
        setPinInvalid('')
      }
      setPinInvalid('')
   }

  const handleUnlock = () => {
    !checkFromHomeToLive && pushNewInteractionContentEvent(
      state?.gaContentData, 
      PROTECTED_CONTENT, 
      TV, 
      interactionType?.SIGUIENTE
    )
    setButtonDisable(true)
    props?.contentName == 'record' && setUnlockFlag(true)
    if (
      pin[0]?.length > 0 &&
      pin[1]?.length > 0 &&
      pin[2]?.length > 0 &&
      pin[3]?.length > 0 
    ) {
      setButtonDisable(false)

      const payload = {
        controlPIN: pin.join(''),
        userId: userDetails?.user_id,
        hks: userDetails?.session_stringvalue,
        parental: 1
      }
      dispatch(getCheckControlPin(payload))
    }
  }

  const handleForgotPin = () => {
    pushNewInteractionContentEvent(
      state?.gaContentData, 
      PROTECTED_CONTENT, 
      TV, 
      interactionType?.OLVIDASTE_TU_PIN_DE_SEGURIDAD
    )
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_hash: userDetails?.session_userhash
    }
    dispatch(getRemindControlPin(payload))
  }

  const handlePinVisbility = () => {
    visbilityIcon === invisible
      ? setVisibilityIcon(visible)
      : setVisibilityIcon(invisible)
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  return (
    <div className="app-css-block-channel">
      <div className="back-indicator-regrassar">
        <img
          src={'images/Logos_Claro_Video.svg'}
          className="claro-logo"
          alt="logo"
        />
        <button
          className="back-indicator-button-pin focusable"
          id="back-button"
          onClick={e => handleLgkey('backClick')}
        >
          <img
            className="yellow-indicator-button"
            src={'images/yellow_shortcut.png'}
          />
          <img
            className="back-indicator-image"
            src={'images/back_button.png'}
          />
          <span>{truncateText('top_head_option_button_back', 30)}</span>
        </button>
      </div>
      <div className="left-container-div">
        <AlphaNumericKeyboard
          type="alphaNumeric"
          onChange={e => handleOTPChange(e, focusedIdx)}
          autoFocus={keyboardFocus}
          name="security-pin"
        />
      </div>

      <div className="right-container-div">
        <span className="blocked-title">
          {truncateText(
            'TvEnVivo_PanelOpciones_MenuOpciones_Opcion5_TextoDesbloquear',
            30
          )}{' '}
        </span>
        <span className="sub-title-unlock">
        {truncateText(
            'MenuAvatar_CanalBloqueado_TextoDescriptivo_Texto1',
            75
          )}{' '}
          {truncateText(
            'MenuAvatar_CanalBloqueado_TextoDescriptivo_Texto2',
            75
          )}
        </span>
        <div>
          <>
            <div className="pin-box-div">
              {pin?.map((item, index) => {
                return (
                  <div key={index} className="pin-wrapper">
                  <input
                    style={
                      visbilityIcon === invisible
                        ? { fontSize: '128px' }
                        : { fontSize: '32px' }
                    }
                    className={
                      pinInvalid
                        ? 'pin-field-invalid'
                        : focusedIdx == index
                        ? 'pin-focused'
                        : 'pin-field'
                    }
                    type={'text'}
                    name="pin"
                    onKeyUp={e => !/[0-9]/.test(e.key) && e.preventDefault()}
                    id={`pin${index}`}
                    ref={ref => (pinRef.current[index] = ref)}
                    maxLength={1}
                    key={index}
                    value={
                      visbilityIcon === invisible && item?.length > 0
                        ? '*'
                        : item
                    }
                    onChange={e => handleOTPChange(e.target, index)}
                    inputMode="none"
                    readOnly
                    data-testid={`pin${index}`}
                  />
                    {/* Show blinking pipe cursor if input is focused and empty */}
                    {focusedIdx === index && item === '' && (
                      <span className="pin-cursor">|</span>
                    )}
                  </div>
                )
              })}
              <button
                onClick={handlePinVisbility}
                className="see-pin-button focusable"
                data-testid={`pinVisible`}
                id="see-pin"
              >
                <img src={visbilityIcon} />
              </button>
            </div>
            {pinInvalid ? (
              <p className="pin-error">
                <span className="pin-error-contents">
                  {truncateText(
                    'lockChannel_tooltip_valid_label_validation',
                    50
                  )}
                </span>
              </p>
            ) : null}

            <button
              className="block-channel-button block-channel-button-margin focusable"
              id="siguiente-button"
              disabled={buttonDisable}
              onClick={handleUnlock}
            >
              {truncateText('bt_suscripcion_siguiente', 30)}
            </button>

            <button
              className="block-channel-button-cancel focusable"
              onClick={e => handleLgkey('backClick')}
            >
              {truncateText('modal_pin_cancel_button', 30)}
            </button>

            <button
              onClick={handleForgotPin}
              className="forgot-pin-button focusable"
              data-testid={`forgotPin`}
            >
              <p className="forgot-pin-content">
                {truncateText('modal_pin_forgot_pin', 35)}
              </p>
            </button>
          </>
        </div>
      </div>
    </div>
  )
}
