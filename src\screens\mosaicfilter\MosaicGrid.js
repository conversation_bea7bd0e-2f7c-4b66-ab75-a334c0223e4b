import React, { useEffect, useRef } from 'react'
import '../../styles/MosaicGrid.scss'
import { useDispatch, useSelector } from 'react-redux'
import {
  getEpgMenu,
  getEpgVersion,
  getEpgLineup,
  getFavouriteLive
} from '../../store/slices/EpgSlice'
import { getLockedChannelsList } from '../../store/slices/settingsSlice'
import GridLayout from './GridLayout'

function MosaicGrid(props) {
  const dispatch = useDispatch()
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const favoriteChannelList = useSelector(
    state => state?.epg?.favouriteLive?.response?.groups
  )

  useEffect(() => {
    dispatch(
      getLockedChannelsList({
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(
      getFavouriteLive({
        epg_version: epgVersion,
        hks: userDetails?.session_stringvalue,
        user_id: userDetails?.user_id,
        user_token: userDetails?.user_token,
        lasttouch: userDetails?.lasttouch?.favorited,
        user_hash: userDetails?.session_userhash,
      })
    )
    dispatch(getEpgLineup({ nodeid: props.node }))
  }, [props?.node])

  const { epgMenu, epgVersion, epgLineup, isChannelLoading, epgLineuplength } =
    useSelector(state => ({
      epgMenu: state?.epg?.epgMenu,
      epgVersion: state?.epg?.epgVersion,
      epgLineup: state?.epg?.epgLineup?.response?.channels,
      epgLineuplength: state?.epg?.epgLineup?.response?.channels?.length,
      isChannelLoading: state?.epg?.isChannelLoading
    }))
  return (
    <div>
      {props.node === 'lockScreen' && lockedChannelsList ? (
        <GridLayout img={lockedChannelsList} type={'lock'} />
      ) : props.node === 'FavoriteScreen' && favoriteChannelList ? (
        <GridLayout img={favoriteChannelList} type={'favourite'} />
      ) : epgLineup ? (
        <GridLayout img={epgLineup} type={'epg'} />
      ) : null}
    </div>
  )
}

export default MosaicGrid
