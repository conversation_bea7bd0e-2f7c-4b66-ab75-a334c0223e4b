.epg-grid-shimmer{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 10px;
    background-color: #000000;
  }
  
  .epg-cell {
    border-radius: 5px;
    padding: 10px;
    background-color: #242424;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: loadingAnimation 1s infinite;
    display: flex;
    justify-content: center;
    grid-gap: 10px;
    align-items: center;
    height: 35px;
  }
  
  .epg-cell-image {
    height: 20px;
    width: 25%;
    height: 50%;
    background-color: #2F2F2F;
    border-radius: 2px;
  }
  
  .epg-cell-content {
    height: 10px;
    width: 40%;
    height: 50%;
    background-color: #2F2F2F;
    border-radius: 2px;
  } 
  
  .epg-shimmer {
    animation: loadingAnimation 1s infinite;
    background: linear-gradient(90deg, transparent,
        rgba(194, 191, 191, 0.2), transparent);
  }
  
  @keyframes loadingAnimation {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }
  
  .description-page-shimmer {
    /* padding: 20px; */
    /* background-color: #242424; */
    height: 278px;  
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
   
  }
  .description-page-header{
    height: 20px;
    width: 50%;
    height: 15%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
  }
  .description-page-subtitle{
    width: 50%;
    height: 10%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
    margin: 10px 0px 10px 0px;
  }
  .description-page-duration{
    width: 25%;
    height: 5%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
  }
  .page-description{
    width: 50%;
    height: 30%;
    /* background-color: #2F2F2F; */
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
    margin:10px 0px 10px 0px;
  }
  .description-page-guide{
    width: 25%;
    height: 5%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
  }

  .mini-epg-skeleton {
    width: 100%;
    height: 100%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
  }

    .epg-grid-shimmer{
      width: 1920px;
    }
    .description-page-shimmer{
      height: 498px;
      width: 1920px;
      padding: 50px 0px 0px 50px;
    }
    .epg-cell{
      width:604px;
      height: 100px;
    }
    .description-page-header{
      width: 50%;
      height: 15%;
    
    }
    .description-page-subtitle{
      width: 50%;
      height: 10%;
      margin: 20px 0px 20px 0px;
    }
    .description-page-duration{
      width: 25%;
      height: 5%;
    }
    .page-description{
      width: 50%;
      height: 30%;
      margin: 20px 0px 20px 0px;
    }
    .description-page-guide{
      width: 25%;
      height: 5%;
    }
  