import React from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { getNavTabValue } from '../../../store/slices/HomeSlice'
import '../../../styles/LiveDetailPage.scss'
import './EpgMenuBar.scss'

const MenuBar = props => {
  const { channelErr, lockedScreen } = props
  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const { inLivePlayer, isSelectedChannelTS } = props
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  const handleNavigation = (event, value) => {
    if (event?.code == 'Enter' || event?.keyCode == 13) {
      switch (value) {
        case 'green':
          props?.handleBackHome(true)
          break
        case 'blue':
          props?.handleShowOptions(event)
          break
        case 'yellow':
          props?.handleMiniEpgShortcut()
          break
        case 'red':
          if (isSelectedChannelTS) {
            props?.handleStartOver()
          }
          break
        default:
          break
      }
    }
    event?.preventDefault()
  }

  return (
    <div
      className={
        inLivePlayer
          ? 'player-shortcut-menu-bar'
          : !props?.eventNotAvailable
          ? 'shortcut-menu-bar'
          : 'shortcut-menu-bar-no-disponible'
      }
    >
      <div
        id="live-shortcut-green"
        className={
          inLivePlayer
            ? 'player-shortcut-bar green focusable'
            : 'epg-shortcut-bar'
        }
        onKeyUp={event => handleNavigation(event, 'green')}
        onClick={event => handleNavigation(event, 'green')}
        tabIndex={1}
        data-sn-up={isSelectedChannelTS ? '#livePlayButton' : '#guia'}
      >
        <span className="shortcut-bar-icon shortcut-bar-icon-green"></span>
        <span
          className={
            inLivePlayer ? 'player-shortcut-bar-text' : 'shortcut-bar-text'
          }
        >
          {inLivePlayer
            ? truncateText('BotonShortcut_TextoTitulo_Salir', 20)?.toUpperCase()
            : truncateText(
                'TvEnVivo_MiniGuia_PanelShortcuts_TextoSalir',
                20
              )?.toUpperCase()}
        </span>
      </div>
      {!props?.eventNotAvailable && (
        <>
          {!channelErr && (
            <div
              className={
                inLivePlayer
                  ? 'player-shortcut-bar blue focusable'
                  : 'epg-shortcut-bar'
              }
              onKeyUp={event => handleNavigation(event, 'blue')}
              onClick={event => handleNavigation(event, 'blue')}
              tabIndex={1}
              data-sn-up={isSelectedChannelTS ? '#livePlayButton' : '#guia'}
            >
              <span className="shortcut-bar-icon shortcut-bar-icon-blue"></span>
              <span
                className={
                  inLivePlayer
                    ? 'player-shortcut-bar-text'
                    : 'shortcut-bar-text'
                }
              >
                {inLivePlayer
                  ? truncateText('BotonShortcut_TextoTitulo_MasOpciones', 20)
                  : truncateText(
                      'TvEnVivo_MiniGuia_PanelShortcuts_TextoOpciones',
                      20
                    )}
              </span>
            </div>
          )}
          <div
            className={
              inLivePlayer
                ? 'player-shortcut-bar yellow focusable'
                : 'epg-shortcut-bar'
            }
            onKeyUp={event => handleNavigation(event, 'yellow')}
            onClick={event => handleNavigation(event, 'yellow')}
            tabIndex={1}
            data-sn-up={isSelectedChannelTS ? '#livePlayButton' : '#guia'}
          >
            <span className="shortcut-bar-icon shortcut-bar-icon-yellow"></span>
            <span
              className={
                inLivePlayer ? 'player-shortcut-bar-text' : 'shortcut-bar-text'
              }
            >
              {inLivePlayer
                ? truncateText('BotonShortcut_TextoTitulo_MiniGuia', 20)
                : truncateText(
                    'TvEnVivo_MiniGuia_PanelShortcuts_TextoGuia',
                    20
                  )}
            </span>
          </div>
        {!channelErr && !lockedScreen &&
            ((inLivePlayer === true && isSelectedChannelTS) || inLivePlayer !== true ? (
              <div
                className={
                  inLivePlayer
                    ? 'player-shortcut-bar red focusable'
                    : 'epg-shortcut-bar'
                }
                onKeyUp={event => handleNavigation(event, 'red')}
                onClick={event => handleNavigation(event, 'red')}
                tabIndex={1}
                data-sn-up={'#livePlayButton'}
              >
                <span className="shortcut-bar-icon shortcut-bar-icon-red"></span>
                <span
                  className={
                    inLivePlayer
                      ? 'player-shortcut-bar-text'
                      : 'shortcut-bar-text'
                  }
                >
                  {inLivePlayer
                    ? truncateText(
                        'BotonShortcut_TextoTitulo_VerDesdeElInicio',
                        20
                      )
                    : truncateText(
                        'TvEnVivo_MiniGuia_PanelShortcuts_TextoReproductor',
                        20
                      )}
                </span>
              </div>
            ) : null)}
        </>
      )}
    </div>
  )
}

export default MenuBar
