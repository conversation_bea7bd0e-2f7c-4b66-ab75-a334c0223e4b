import React from 'react'
import { fireEvent, render, getByText } from '@testing-library/react'
import { Provider, useDispatch, useSelector } from 'react-redux'
import { <PERSON>rowserRouter as Router } from 'react-router-dom'
import * as redux from 'react-redux';
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import { fromJS } from 'immutable'
import Search from './Search'
import SearchDataSaga, {
  fetchSearchSaga,
  fetchPredictiveSearchSaga
} from '../../apiSaga/searchSaga'
import mycontentestReducer, {
  getSearchData,
  getSearchSuccess,
  getSearchError,
  getPredictiveSearchData,
  getPredictiveSearchSuccess,
  getPredictiveSearchError
} from '../../store/slices/SearchSlice'
import { takeEvery } from 'redux-saga/effects'
import SearchInputMock from './searchRailsCompMock.json'

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn()
}))

const mockStore = configureStore([])

const history = createHistory()

const initialState = fromJS({
  ContentDetailsReducer: {},
  languagesReducer: {}
})
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
)
export const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}

describe('search', () => {
  global.SpatialNavigation = {
    focus: jest.fn(),
    init: jest.fn(),
    add: jest.fn(),
    makeFocusable: jest.fn()
  };
  const state = {
    data: [],
    predictiveData: [],
    isPredictive: false,
    isLoading: true,
    error: null,
    popularSearch: '',
    isRecomendationLoading: true
  }
  const getmycontentFailure = {
    response: null,
    status: '1',
    msg: 'ERROR',
    errors: {
      error: ['Invalid Argument on field: error_params_group_id'],
      code: 'error_params'
    }
  }

  let spyOnUseSelector
  beforeEach(() => {
    const mockUseDispatch = jest.fn()
    const mockDispatch = jest.fn()
    const mockUseSelector = jest.fn()
    const mockSelector = jest.fn()

    jest.mock('react-redux', () => ({
      useDispatch: () => mockUseDispatch.mockReturnValue(mockDispatch),
      useSelector: () => mockUseSelector.mockReturnValue(mockSelector)
    }))

    // Mock useSelector hook
    spyOnUseSelector = jest.spyOn(redux, 'useSelector')
  })

  afterEach(() => {
    useSelector.mockClear()
  })

  let SpeechRecognitionMock

  beforeEach(() => {
    // Mock the webkitSpeechRecognition instance using jest.fn()
    SpeechRecognitionMock = jest.fn().mockImplementation(() => {
      return {
        start: jest.fn(),
        stop: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        fillRect: jest.fn()
      }
    })

    // Override the window object's SpeechRecognition property with the mocked webkitSpeechRecognition instance
    window.webkitSpeechRecognition = SpeechRecognitionMock
  })

  it('it should start speech recognition', () => {
    window.webkitSpeechRecognition().start()

    // Assert that the start method of the webkitSpeechRecognition instance was called
    expect(window.webkitSpeechRecognition().start).toBeDefined()
  })

  it('it should stop speech recognition', () => {
    window.webkitSpeechRecognition().stop()

    // Assert that the stop method of the webkitSpeechRecognition instance was called
    expect(window.webkitSpeechRecognition().stop).toBeDefined()
  })

  it('Expects to run onChange function when input is changed in the DOM', () => {
    useDispatch.mockReturnValue(jest.fn())
    window.HTMLElement.prototype.scrollBy = function () {}
    jest.useFakeTimers()

    const result = renderWithState(<Search />)

    let input = result.container.querySelector('input[name="search"]')
    fireEvent.mouseOver(input)
    fireEvent.keyUp(input, { target: { value: 'danc' } })
    fireEvent.submit(input, jest.fn())
    jest.advanceTimersByTime(1500)
  })

  it('renders V-card metadata while landing on search screen', async () => {
    useDispatch.mockReturnValue(jest.fn())
    const genObject = SearchDataSaga()

    expect(genObject.next().value).toEqual(
      takeEvery('search/getSearchData', fetchSearchSaga)
    )
    expect(genObject.next().value).toEqual(
      takeEvery('search/getPredictiveSearchData', fetchPredictiveSearchSaga)
    )
  })

  it('Test in prod env', () => {
    const filterlist = jest.fn()
    filterlist('peru')
    expect(filterlist).toHaveBeenCalled()
  })

  it('should handle initial state', () => {
    const initialState = state
    const action = { type: 'unknown' }
    const expectedState = initialState
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSearchData', () => {
    const initialState = { ...state, data: { id: '1234' } }
    const action = getSearchData({ id: '1234' })
    const expectedState = { ...state, data: { id: '1234' } }
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSearchSuccess', () => {
    const payload = {
      data: SearchInputMock
    }
    const initialState = { ...state, data: payload }
    const action = mycontentestReducer(
      initialState,
      getSearchSuccess(payload, { SearchInputMock })
    )
    const expectedState = { ...state, data: payload }
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSearchError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = mycontentestReducer(
      initialState,
      getSearchError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPredictiveSearchData', () => {
    const payload = {
      data: SearchInputMock
    }
    const initialState = { ...state, data: payload }
    const action = mycontentestReducer(
      initialState,
      getPredictiveSearchData(payload, { SearchInputMock })
    )
    const expectedState = { ...state, data: payload }
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPredictiveSearchSuccess', () => {
    const payload = {
      data: SearchInputMock
    }
    const initialState = { ...state, data: payload }
    const action = mycontentestReducer(
      initialState,
      getPredictiveSearchSuccess(payload, { SearchInputMock })
    )
    const expectedState = { ...state, data: payload }
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPredictiveSearchError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = mycontentestReducer(
      initialState,
      getPredictiveSearchError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(mycontentestReducer(initialState, action)).toEqual(expectedState)
  })

  it('renders popular search metadata while landing on search screen', async () => {
    useDispatch.mockReturnValue(jest.fn())
    const genObject = SearchDataSaga()
    expect(genObject.next().value).toEqual(
      takeEvery('search/getSearchData', fetchSearchSaga)
    )
  })

  it('renders predective data while landing on VOD movies screen', async () => {
    const genObject = SearchDataSaga()
    genObject.next()
    expect(genObject.next().value).toEqual(
      takeEvery('search/getPredictiveSearchData', fetchPredictiveSearchSaga)
    )
  })

  test('Expects to run onChange function when input is changed in the DOM', () => {
    jest.useFakeTimers()
    window.HTMLElement.prototype.scrollBy = function () {}
    let props = {
      handleInputChange: jest.fn(),
      onChange: jest.fn(),
    }
    const result = renderWithState(<Search {...props} />)
    let input = result.container.querySelector('input[name="search"]')
    fireEvent.change(input, { target: { value: 'xfggggg' } })
    fireEvent.mouseOver(input)
    fireEvent.submit(input, jest.fn())
    fireEvent(
      getByText(result.container, '1'),
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
    jest.advanceTimersByTime(500)
  })
})
