import React from 'react'
import "./../../styles/SkeletonScreenLoading.css"
import './../../styles/RailsSkeletonLoading.css'
import '../../styles/ToonKidsCardSkeltonLoading.css'

// banner skeleton
export const SkeletonScreenloading=()=> {
  return (
    <div>
     <div className="card-banner">
        <div className="card__image loading"></div>
    </div> 
    </div>    
  )
}

export const RailsSkeletonLoading = ({listsToRender, flag})=> {
  return (
      <>
    {Array(listsToRender).fill(1)
      .map((i,index) => (
        // vertical or horizontal
    <div className={ flag === 'Vertical' ? "vcards": flag === "seriesButton" ? "series-card" : "cards"} key={index} >
       <div className={ flag === 'Vertical' ? "vcards__images vloadings": flag === "seriesButton" ? "series_card loadings" : "cards__images loadings" }></div>
   </div> 
    ))}
    </>
  )
}

export const CastSkeletonLoading = ({listsToRender})=> {
  return (
      <>
    {Array(listsToRender).fill(1)
      .map((i,index) => (
        // vertical or horizontal
    <div className="castcards" key={index} >
       <div className="castcards__images castloadings"></div>
   </div>
    ))}
    </>
  )
}

export const TitleSkeletonLoading=()=> {
  return (
    <div className="tcard" > 
    <div className="tcard__title tloading"></div> 
   </div> 
  )
}

export const ToonKidsSkeletonLoading = ({listsToRender}) => {
  return (
     <>
     {Array(listsToRender)
       .fill(1)
       .map((i,index) => (
        <div className="skeleton" key={index}>
        <div className="skeleton-left flex1">
        <div className="square circle"></div>
      </div>
</div>
     ))}
     </>
  )
}
