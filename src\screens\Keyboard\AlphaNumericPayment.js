import React, { useState, useEffect } from 'react'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './../../styles/AlphaNumericPayment.scss'
import './../../styles/tvstyles.scss'

const AlphaNumeric = props => {
  const numberOption = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
    ['/', ':', ';', '(', ')', '$', '&', '@', '"', ','],
    ['¿', '?', '¡', '!', '`', '[', ']', '{', '}', '#'],
    ['%', '^', '*', '=', '\\', '|', '~', '<', '>', '>'],
    ['-', '_', 'space', '.', '+']
  ]

  const alphabet = [
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'ñ'],
    ['caps', 'z', 'x', 'c', 'v', 'b', 'n', 'm', '@'],
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']
    // ['.com', '.ar', '@gmail.com', '@hotmail.com', '_', '-', 'space', '.', '+']
  ]

  const [keyMode, setKeyMode] = useState(
    props.keyMode ? props.keyMode : 'numeric'
  )
  const [disableNumber, setDisableNumber] = useState(
    props.disableNumber ? props.disableNumber : false
  )
  const [disableAplphabet, setDisableAlphabet] = useState(
    props.disableAplphabet ? props.disableAplphabet : false
  )
  // const [prevVal,setPrevVal] = useState (props.val ? props.val:null);
  const [inputValue, setInputValue] = useState(props.val ? props.val : '')
  const [isCapsOn, setIsCapsOn] = useState(false)

  useEffect(() => {
    // Add any logic you want to run on mount
    if (keyMode == 'numeric') {
      document.getElementById('01').focus()
    } else if (keyMode == 'alphabetic') {
      document.getElementById('0q').focus()
    }

    return () => {}
  }, [keyMode])

  useEffect(() => {
    props.onChange(inputValue)
  }, [inputValue])

  const buttonRow = (row, rowIndex) => {
    const btns = row.map((val, index) => (
      <button
        className="payment-keyboard-button focusable"
        id={rowIndex + val}
        key={index}
        style={{ color: 'white' }}
        onClick={() => handleButtonClick(val)}
      >
        {val === 'caps' ? (
          val.toUpperCase()
        ) : val === 'space' ? (
          <img
            src="images/spacebar.png"
            style={{ width: '111px', height: 'auto' }}
            alt="spacebar"
          ></img>
        ) : isCapsOn ? (
          val.toUpperCase()
        ) : (
          val
        )}
      </button>
    ))
    return (
      <div style={{ display: 'flex' }} key={rowIndex}>
        {btns}
      </div>
    )
  }

  // const buttonRow = (row, rowIndex) => {
  //   const btns = row.map((val, index) => (
  //     <button
  //       className='payment-keyboard-button focusable'
  //       key={index}
  //       style={{ color: 'white' }}
  //       onClick={() => handleButtonClick(val)}
  //     >
  //       {
  //         val === 'caps'
  //           ? val.toUpperCase()
  //           : val === 'space'
  //             ? <img src="" alt="blank" style={{ width: '20px', height: '20px' }} />
  //             : val === 'enter'
  //               ? <img src="enter.png" alt="enter" style={{ width: '30px', height: '30px' }} />
  //               : isCapsOn
  //                 ? val.toUpperCase()
  //                 : val
  //       }
  //     </button>
  //   ));
  //   return <div style={{ display: 'flex' }} key={rowIndex}>{btns}</div>;
  // };

  const handleButtonClick = value => {
    // Handle button click logic here
    if (value === 'caps') {
      setIsCapsOn(prev => !prev) // Toggle caps mode
    } else if (value === 'space') {
      setInputValue(prevValue => prevValue + ' ') // Add space to input
    } else {
      setInputValue(
        prevValue => prevValue + (isCapsOn ? value.toUpperCase() : value)
      )
    }
  }

  const switchMode = mode => {
    setKeyMode(mode)
  }

  const handleClearInput = () => {
    setInputValue('')
  }

  const handleBackspace = () => {
    setInputValue(prevValue => prevValue.slice(0, -1))
  }
  return (
    <div className="keyboardBox KeyboardPromoCode">
      <div className="top-buttons-key">
        <button
          id="alphabet"
          disabled={disableAplphabet}
          className=" focusable  top-buttom-image"
          style={{ color: 'white' }}
          onClick={() => switchMode('alphabetic')}
        >
          {disableAplphabet ? (
            <img
              class="top-buttom-image"
              alt="abc"
              src="images/Search_Icons/sc_teclado_abc_inactive.png"
            />
          ) : (
            <img
              class="top-buttom-image"
              alt="abc"
              src="images/Search_Icons/sc_teclado_abc_active.png"
            />
          )}
        </button>
        <button
          id="numeric"
          disabled={disableNumber}
          className=" focusable  top-buttom-image"
          style={{ color: 'white' }}
          onClick={() => switchMode('numeric')}
        >
          {disableNumber ? (
            <img
              class="top-buttom-image"
              alt="Search.."
              src="images/Search_Icons/sc_teclado_123_inactive.png"
            />
          ) : (
            <img
              class="top-buttom-image"
              alt="Search.."
              src="images/Search_Icons/sc_teclado_123_active.png"
            />
          )}
        </button>
        <button
          id="btnclear"
          className=" focusable  top-buttom-image"
          style={{ color: 'white' }}
          onClick={handleClearInput}
        >
          {' '}
          <img
            class="top-buttom-image"
            alt="Clear"
            src="images/Search_Icons/sc_teclado_borrar.png"
          />{' '}
        </button>
        <button
          id="btnbackspace"
          className=" focusable  top-buttom-image"
          style={{ color: 'white' }}
          onClick={handleBackspace}
        >
          {' '}
          <img
            class="top-buttom-image"
            alt="BackSpace"
            src="images/Search_Icons/sc_teclado_vaciar.png"
          />{' '}
        </button>
      </div>
      <div>
        {keyMode === 'numeric' &&
          numberOption.map((row, index) => buttonRow(row, index))}
        {keyMode === 'alphabetic' &&
          alphabet.map((row, index) => buttonRow(row, index))}
      </div>
    </div>
  )
}

export default AlphaNumeric
