import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import * as reactRedux from 'react-redux';
import configureStore from 'redux-mock-store';
import AvatarServiceFailureScreen from './AvatarServiceFailureScreen';
import { BrowserRouter as Router } from 'react-router-dom';
import * as reactRouter from 'react-router-dom';
import { clearIsloggedInStatus } from '../../store/slices/login';

// Mock the react-redux hooks
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: jest.fn(),
  useNavigate: jest.fn(),
}));

// Mock the action creator
jest.mock('../../store/slices/login', () => ({
  clearIsloggedInStatus: jest.fn(),
}));

const localStorageMock = {
  getItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('AvatarServiceFailureScreen', () => {
  const mockStore = configureStore([]);
  const initialState = {
    initialReducer: {
      appMetaData: {
        translations: JSON.stringify({
          language: {
            US: {
              atv_unexpected_error: 'Unexpected Error',
              contents_withoutResults_talents_message_label: 'Action Incomplete',
              btn_modal_ok: 'OK',
            },
          },
        }),
      },
    },
    profile: {
      userProfile: { username: 'testUser' },
    },
  };
  let store;
  let useSelectorMock;
  let useDispatchMock;
  let mockDispatch;
  let useLocationMock;
  let useNavigateMock;
  let mockNavigate;

  beforeEach(() => {
    store = mockStore(initialState);
    localStorageMock.getItem.mockReturnValue('US');
    
    useSelectorMock = jest.spyOn(reactRedux, 'useSelector');
    useSelectorMock.mockImplementation((selector) => selector(store.getState()));
    
    mockDispatch = jest.fn();
    useDispatchMock = jest.spyOn(reactRedux, 'useDispatch');
    useDispatchMock.mockReturnValue(mockDispatch);
    useLocationMock = jest.spyOn(reactRouter, 'useLocation');
    useLocationMock.mockReturnValue({ state: null });

    mockNavigate = jest.fn();
    useNavigateMock = jest.spyOn(reactRouter, 'useNavigate');
    useNavigateMock.mockReturnValue(mockNavigate);

    clearIsloggedInStatus.mockReset();
    clearIsloggedInStatus.mockReturnValue({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });


  it('handles Enter key press to trigger OK button action', () => {
    render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );
    
    fireEvent.keyUp(document, { keyCode: 13 });
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });

  it('handles Back key press (keyCode 10009) to trigger OK button action', () => {
    render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );
    fireEvent.keyUp(document, { keyCode: 10009 });
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });

  it('handles Exit key press (keyCode 461) to trigger OK button action', () => {
    render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );
    fireEvent.keyUp(document, { keyCode: 461 });
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });

  it('removes event listener on component unmount', () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
    
    const { unmount } = render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );
    
    unmount();
    
    expect(removeEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function));
  });

  it('navigates to watchprofile when state.pageName is watchProfile', () => {
    useLocationMock.mockReturnValue({ state: { pageName: 'watchProfile' } });
    
    render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );
    
    fireEvent.click(screen.getByText('OK'));
    expect(mockNavigate).toHaveBeenCalledWith('/watchprofile', {
      state: { data: '', page: 'home', img: { username: 'testUser' } },
    });
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });

  it('navigates to settings when state.pageName is settings', () => {
    useLocationMock.mockReturnValue({ state: { pageName: 'settings' } });
    
    render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );
    fireEvent.click(screen.getByText('OK'));
    expect(mockNavigate).toHaveBeenCalledWith('/settings');
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });

  it('only dispatches clearIsloggedInStatus when state.pageName is not defined', () => {
    // Set up useLocation to return no state
    useLocationMock.mockReturnValue({ state: null });
    
    render(
      <Provider store={store}>
        <Router>
          <AvatarServiceFailureScreen />
        </Router>
      </Provider>
    );

    fireEvent.click(screen.getByText('OK'));
    expect(mockNavigate).not.toHaveBeenCalled();
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'CLEAR_LOGGED_IN_STATUS' });
  });
});