import { call, takeEvery } from 'redux-saga/effects'
import {
  getStartHeaderInfoSuccess,
  getStartHeaderInfoError,
  getAppMetaDataSuccess,
  getAppMetaDataError,
  getMetaDataHelpSuccess,
  getMetaDataHelpError,
  getIpDetailsSuccess,
  getIpDetailsError,
  getAppMetaDataVideoSuccess,
  getAppMetaDataVideoError
} from '../store/slices/initialSlices'
import { store } from '../store/sagaStore'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { getAppMetaData, getMetaDataHelp } from '../store/slices/initialSlices'
import { getImage } from '../store/slices/Images'
import { getNavData, getNavDataKids } from '../store/slices/HomeSlice'
import { getAppKey } from '../utils/AppKeys'
import { CURRENT_PLATFORM } from '../utils/devicePlatform'

function* getStartHeaderInfoApi({ payload }) {
  try {
    yield call(
      request,
      URL.START_HEADER_INFO_URL,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          localStorage.setItem('region', response?.response?.region)
          store.dispatch(getAppMetaData({ region: response?.response?.region }))
          store.dispatch(
            getAppMetaData({
              type: 'videoTutorial',
              sessionKey: '1ea92828d25840002e3bbf56de34026a',
              region: response?.response?.region
            })
          )
          store.dispatch(getMetaDataHelp(response?.response?.region))
          store.dispatch(getImage(response?.response?.region))
          store.dispatch(getNavData())
          store.dispatch(getStartHeaderInfoSuccess(response))
          store.dispatch(getNavDataKids())
        },
        onError(error) {
          store.dispatch(getStartHeaderInfoError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getAppMetaDataApi({ payload }) {
  const tvType = CURRENT_PLATFORM
  try {
    yield call(
      request,
      `${URL.APP_META_DATA_URL}&region=${payload?.region}&sessionKey=${
        payload?.type == 'videoTutorial'
          ? payload?.sessionKey
          : getAppKey(tvType)
      }-${payload?.region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          payload?.type
            ? store.dispatch(getAppMetaDataVideoSuccess(response))
            : store.dispatch(getAppMetaDataSuccess(response))
        },
        onError(error) {
          payload?.type
            ? store.dispatch(getAppMetaDataVideoError(error))
            : store.dispatch(getAppMetaDataError(error))
        }
      }
    )
  } catch (error) {
    console.error('error --> ', error)
  }
}

function* getMetaDataHelpApi({ payload }) {
   const tvType = CURRENT_PLATFORM
  try {
    yield call(
      request,
      `${URL.APA_META_DATA_HELP_URL}&region=${payload}&sessionKey=d48c48c956cda082e2e03b717c81c220-${payload}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getMetaDataHelpSuccess(response))
        },
        onError(error) {
          store.dispatch(getMetaDataHelpError(error))
        }
      }
    )
  } catch (error) {
    console.error('error --> ', error)
  }
}

function* getIpDetailsApi() {
  try {
    yield call(
      request,
      'https://ipinfo.io/json',
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getIpDetailsSuccess(response))
        },
        onError(error) {
          store.dispatch(getIpDetailsError(error))
        }
      }
    )
  } catch (error) {
    console.error('Unable to fetch the IP details --> ', error)
  }
}

export default function* initialSaga() {
  yield takeEvery('initialSlices/getStartHeaderInfo', getStartHeaderInfoApi)
  yield takeEvery('initialSlices/getAppMetaData', getAppMetaDataApi)
  yield takeEvery('initialSlices/getMetaDataHelp', getMetaDataHelpApi)
  yield takeEvery('initialSlices/getIpDetails', getIpDetailsApi)
}
