import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import Settings from "./Settings";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<MemoryRouter
			history={history}
			initialEntries={[{ state: { activeTab: 'profile-settings' } }]}
		>
			{children}
		</MemoryRouter>
	</Provider>
);
export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper })
};
const mockErrorResponse = {
	"status": "1"
}
const mockIsLoggedInSuccessResponse = {
	response: {
		admin: true,
		session_stringvalue: "ZTEATV412001224226580292a69e33"
	}
}

const mockLogoutSuccessResponse = {
	msg: 'OK',
	response: true,
	status: '0'
}

const mockProfileResponse = {
	response: {
		data: {
			members: [
				{
					gamification_id: "6620f0166da82d665174b92c",
					username: "Administrador",
					user_image: "https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar01.png?**********",
					rol: "admin",
					admin: true,
					change_name: true,
					is_kids: false,
					partnerUserId: "97738348",
					user_hash: "************************************************************************************************"
				}
			]
		}
	}
}

describe('Settings Landing page test', () => {
	global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn(),
		clear: jest.fn(),
        uninit: jest.fn() 
      };

	test('should render without api mock data', () => {
		initialState.login = {
			isLoggedIn: mockErrorResponse
		}
		initialState.login = {
			isLoggedInStatus: ''
		}
		initialState.profile = {
			profileData: mockErrorResponse
		}
		renderWithState(<Settings />)
	})
	test('should render with api mock data', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		}
		initialState.login = {
			isLoggedInStatus: 'Success'
		}
		initialState.profile = {
			profileData: mockProfileResponse
		}
		renderWithState(<Settings />)
	})

	test('navigation to administrator profiles page', () => {
		initialState.login = {
			isLoggedIn: {
				response: {
					admin: true
				}
			}
		}
		const { container } = renderWithState(<Settings />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'administratorProfiles')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		fireEvent.keyUp(buttonClick, { keyCode: '461' })
		history.push('/watchprofile')
	})

	test('navigation to profile settings control page', () => {
		initialState.login = {
			isLoggedIn: {
				response: {
					admin: true
				}
			}
		}
		const { container } = renderWithState(<Settings />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'adjustingProfiles')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		history.push('/settings/profile-settings')
	})

	test('navigation to my devices logout confirmation page', () => {
		initialState.login = {
			isLoggedIn: {
				response: {
					admin: true
				}
			}
		}
		const { container } = renderWithState(<Settings />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'myDevices')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		history.push('/my-settings/help-And-Settings/my-devices')
	})

	test('on profiles click', () => {
		const { container } = renderWithState(<Settings />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'image0')
		localStorage.getItem('currNavIdx', 0)
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('on log out click', () => {
		const { container } = renderWithState(<Settings />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'logout')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		history.push('/')
	})

	test('navigation to login page after logging out', () => {
		initialState.settingsReducer = {
			userLogout: mockLogoutSuccessResponse
		}
		const { container } = renderWithState(<Settings />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'logout')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		history.push('/')
	})
})
