import React from 'react'
import "../../styles/VodSeriesSkeleton.scss"
import { CastSkeletonLoading, RailsSkeletonLoading, TitleSkeletonLoading } from '../SkeletonScreenLoading/SkeletonScreenloading'
 

const VodEpisodeSkeleton = ({ listsToRender }) => {
  return (
      <>
          {Array(listsToRender).fill(1)
              .map((i,index) => (
                  <div className="app" key={index}>
                      <div className="epiccards" >
                          <div className="epiccards__image"></div>
                          <div className="epiccards__content">
                              <h2 className="L1"></h2>
                              <h2 className="L1"></h2>
                              <p className="L2"></p>
                          </div>
                      </div>
                  </div>
              ))}
      </>
  )
}
 
const VodSeriesSkeleton = () => {
  return (
    <>
      <div className="vod-description-page-shimmer">
        <div className="vod-description-page-header shimmer"></div>
        <div className="vod-description-page-subtitle shimmer"></div>
        <div className='vod-description-page-duration shimmer'></div>
        <div className='vod-page-description shimmer'></div>
      </div>
      <TitleSkeletonLoading />
      <div style={{ display: 'flex',overflowX: 'scroll' }}>
        <VodEpisodeSkeleton listsToRender={5} />
      </div>
      <TitleSkeletonLoading />
      <div style={{ display: 'flex',overflowX: 'scroll' }}>
        <CastSkeletonLoading listsToRender={8 } />
      </div>
      <TitleSkeletonLoading />
      <div style={{ display: 'flex' }}>
        <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
      </div>
    </>
  )
}
 
export default VodSeriesSkeleton