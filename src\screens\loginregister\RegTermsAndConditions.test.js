import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import RegTermsAndConditions from "./RegTermsAndConditions";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

describe('RegTermsAndConditions page test', () => {
    test('onclick of down arrow', () => {
        const { container } = renderWithState(<RegTermsAndConditions />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'regtermsandcondition');
        fireEvent.keyUp(scroll, { keyCode: '405' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('onclick of down arrow', () => {
        const { container } = renderWithState(<RegTermsAndConditions />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'regtermsandcondition');
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('onclick of down arrow', () => {
        const { container } = renderWithState(<RegTermsAndConditions />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'regtermsandcondition');
        fireEvent.keyUp(scroll, { keyCode: '38' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('onclick of down arrow', () => {
        const { container } = renderWithState(<RegTermsAndConditions />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'regtermsandcondition');
        fireEvent.keyUp(scroll, { keyCode: '40' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})