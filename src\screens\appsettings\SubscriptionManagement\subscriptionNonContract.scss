.app-css {
    width: 1920px;
    height: 1080px;
    position: fixed;
    background: #121212;

    .back-indicator {
        display: flex;
        height: 48px;
        width: 292px;
        border-radius: 6.6px;
        background-color: #2E303D;
        align-items: center;
        float: right;
        margin-top: 47px;
        margin-right: 64px;

        .yellow-indicator {
            height: 20px;
            width: 20px;
            margin-left: 24px;
            margin-right: 24px;
        }

        .back-image {
            height: 24px;
            width: 30px;
            margin-right: 24px;
        }

        .back-text {
            height: 30px;
            width: 146px;
            color: #FFFFFF;
            font-family: Roboto;
            font-size: 29.04px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 29.04px;
        }
    }

    .subscription-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 129px;

        .subscription-title {
            width: 385px;
            ;
            color: #cacaca;
            font-family: Roboto;
            font-size: 40px;
            letter-spacing: 0;
            line-height: 43px;
            text-align: center;
            margin: 0;
        }

        .logo-container {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            width: 250px;

            .channelSubLogo {
                width: 80px;
                height: 80px;
            }
        }

        .pricing {
            display: flex;

            .priceInfo {
                margin-top: 16px;
                width: 86.93px;
                color: #EEEEEE;
                font-family: Roboto;
                font-size: 36.54px;
                font-weight: 900;
                letter-spacing: -1.14px;
                line-height: 39px;
            }

            .taxInfo {
                height: 31px;
                width: 75.07px;
                color: #EEEEEE;
                font-family: Roboto;
                font-size: 25.58px;
                font-weight: 900;
                letter-spacing: -0.8px;
                line-height: 27px;
            }
        }

        .freeChargeStr {
            height: 15px;
            width: 184px;
            color: #00A9FF;
            font-family: Roboto;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: -0.87px;
            line-height: 15px;
            text-align: center;
        }

        .details-container {
            margin-top: 150px;
            display: flex;
            gap: 19px;
            flex-direction: column;

            .channel-name {
                height: 44px;
                width: auto;
                color: #EEEEEE;
                font-family: Roboto;
                font-size: 48px;
                letter-spacing: -0.87px;
                line-height: 30px;
                text-align: center;
                margin: 0;
            }

            .channel-info {
                margin-top: 19px;
                height: 44px;
                width: auto;
                color: #EEEEEE;
                font-family: Roboto;
                font-size: 28px;
                letter-spacing: -0.87px;
                line-height: 30px;
                text-align: center;
                margin: 0;
            }

            .channel-option {
                margin-top: 19px;
                height: 44px;
                width: auto;
                color: #EEEEEE;
                font-family: Roboto;
                font-size: 28px;
                letter-spacing: -0.87px;
                line-height: 30px;
                text-align: center;
                margin: 0;
            }
        }

        .btn-container {
            margin: 10px;
            width: 550px;
            display: flex;
            flex-direction: column;
            

            .view-details-button {
                height: 72px;
                width: 509px;
                border-radius: 10.03px;
                background-color: #ECAF2A;
                color: #000000;
                font-family: Roboto;
                font-size: 36.5px;
                font-weight: bold;
                letter-spacing: -0.58px;
                line-height: 42.18px;
                text-align: center;
            }

            .cancel-button {
                margin-top: 20px;
                height: 72px;
                width: 509px;
                border-radius: 8.8px;
                background-color: #2E303D;
                color: #FFFFFF;
                font-family: Roboto;
                font-size: 32px;
                font-weight: bold;
                letter-spacing: -0.51px;
                line-height: 35px;
                text-align: center;
            }

            .view-details-button:focus,
            .cancel-button:focus {
                transition: all 0.1s ease-in-out;
                transform: scale(1.2);
            }
        }

    }
}