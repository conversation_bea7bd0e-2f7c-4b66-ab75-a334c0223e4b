import React, { createContext, useContext, useState, useEffect } from 'react'

// Create a context for managing focus state
const FocusContext = createContext({
  focusedElement: null,
  handleFocus: () => {},
  handleBlur: () => {}
})

// Custom hook to use the focus context
export const useFocus = () => {
  const contextValue = useContext(FocusContext)
  return contextValue
}

// Provider component to wrap components that need access to the focus state
export const FocusProvider = ({ children }) => {
  const [focusedElement, setFocusedElement] = useState(document.body)

  const handleFocus = event => {
    const target = event.target
    if (target !== focusedElement) {
      setFocusedElement(target)
    }
  }

  const handleBlur = event => {
    const relatedTarget = event.relatedTarget
    if (!relatedTarget) {
      setFocusedElement(document.body)
    }
  }

  useEffect(() => {
    document.body.addEventListener('focusin', handleFocus, true)
    document.body.addEventListener('focusout', handleBlur, true)

    return () => {
      document.body.removeEventListener('focusin', handleFocus, true)
      document.body.removeEventListener('focusout', handleBlur, true)
    }
  }, [])

  return (
    <FocusContext.Provider value={{ focusedElement, handleFocus, handleBlur }}>
      {children}
    </FocusContext.Provider>
  )
}

// FocusAnimation component
export const FocusAnimation = () => {
  const { focusedElement } = useFocus()

  const updateRingStyle = () => {
    if (!focusedElement) return {}

    const box = focusedElement.getBoundingClientRect()
    const id = focusedElement.id || ''
    let opacity = focusedElement === document.body ? 0 : 1

    const updatedStyles = {
      position: 'fixed',
      pointerEvents: 'none',
      border: '4px solid #fff',
      borderRadius: '8px',
      zIndex: 99,
      transition: 'all 0.2s ease-in-out',
      top: id.includes('episode-image-button')
        ? `${box.top - 6}px`
        : id.includes('recommendimage')
        ? `${box.top - 4}px`
        : `${box.top - 8}px`,
      bottom: `${box.bottom}px`,
      right: `${box.right}px`,
      left:
        id.includes('episode-image-button') || id.includes('recommendimage')
          ? `${box.left - 7}px`
          : `${box.left - 6}px`,
      width:
        id.includes('episode-image-button') || id.includes('recommendimage')
          ? `${box.width}px`
          : `${box.width + 5}px`,
      height:
        id.includes('episode-image-button') || id.includes('recommendimage')
          ? `${box.height}px`
          : `${box.height + 8}px`,
      opacity,
      transform:
        id.includes('episode-image-button') || id.includes('recommendimage')
          ? 'scale(1.0)'
          : 'scale(1.02)'
    }
    if (
      id.includes('nav-') ||
      id.includes('castId') ||
      id.includes('serachId') ||
      id.includes('profileimg')
    ) {
      return
    }

    return updatedStyles
  }

  return <div id="focus-ring" style={updateRingStyle()} />
}
