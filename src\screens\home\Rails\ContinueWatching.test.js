import React from "react";
import { fireEvent, getByTestId, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ContinueWatching from "./ContinueWatching";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const baseItem = {
    vistime: { last: { progress: 50 } }, // Added required progress data
    image_small: "https://example.com/image.jpg",
    proveedor_code: "paramount",
    format_types: "susc"
};

const mockdata = {
    "groups": [
        {
            ...baseItem,
            "id": "1114102",
            "title": "Manual de Supervivencia Escolar de Ned",
            "is_series": true
        },
        {
            ...baseItem,
            "id": "1110876",
            "title": "George & Tammy",
            "is_series": true
        },
        {
            ...baseItem,
            "id": "1150923",
            "title": "Clínica X",
            "is_series": true
        },
        {
            ...baseItem,
            "id": "925552",
            "title": "Tyler Perry's Young Dylan",
            "is_series": true
        }
    ],
    "total": 4
};

const liverailcardmockdata = {
    "groups": [
        {
            ...baseItem,
            "id": "1114102",
            "live_enabled": "1"
        },
        {
            ...baseItem,
            "id": "1110876",
            "title": "George & Tammy",
            "live_enabled": "1"
        }
    ],
    "total": 2
};

const moviesrailcardmockdata = {
    "groups": [
        {
            ...baseItem,
            "id": "1114102",
            "is_series": false
        },
        {
            ...baseItem,
            "id": "1110876",
            "title": "George & Tammy",
            "is_series": false
        }
    ],
    "total": 2
};

describe('Railcard page test case', () => {
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        const props = {
            dataObject: mockdata
        }
        const { container } = renderWithState(<ContinueWatching {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })

    test('should render onclick railcard liverailcardmockdata', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        const props = {
            dataObject: liverailcardmockdata
        }
        const { container } = renderWithState(<ContinueWatching {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })

    test('should render onclick railcard moviesrailcardmockdata', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        const props = {
            dataObject: moviesrailcardmockdata
        }
        const { container } = renderWithState(<ContinueWatching {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })

    test('should render with byUser prop', () => {
        const props = {
            byUser: true
        }
        renderWithState(<ContinueWatching {...props} />)
    })
})