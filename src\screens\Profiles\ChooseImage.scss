.choose-profile-image {
    width: 1920px;
    height: 1080px;
    background-color: #121212;

    .image-profile-container {
      position: fixed;
      z-index: 9999;
      top: 0;
      width: 100%;
      background-color: #121212;
    }
  
    .profile-app-logo {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
  
      .logo-img {
        width: 171px;
        height: 36px;
        margin-left: 75px;
      }
  
      .back-button-profile {
        margin-right: 53px;
        height: 48px;
        width: 292px;
        border-radius: 6.6px;
        background-color: #2e303d;
        display: flex;
        justify-content: space-around;
        align-items: center;
  
        img {
          &.yellow-dot {
            height: 20px;
            width: 20px;
          }
          &.back-arrow {
            height: 24px;
            width: 30px;
          }
        }
        &:focus {
        background-color: #981c15;
        }
        .back-button-text {
          color: #fff;
          font: bold 29.04px/29.04px 'Roboto';
        }
      }
    }
  
    
  
    .choose-profileimg-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-top: 72px;
  
      .choose-profile-watch {
        color: #f4f4f4;
        font-family: 'Roboto';
        font-size: 48px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 50px;
        margin-left: 77px;
      }
  
      .user-name {
        color: #f4f4f4;
        font: bold 32px/42px Roboto;
        margin-right: 27px;
        width: 850px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        align-content: center;
      }
  
      .current-image {
        margin-right: 53px;
      }
    }
  
    .parent-imageselector-container {
      padding-top: 280px;
      background-color: #121212;
    }
  
    .parent-image-container {
      display: flex;
      margin-top: 110px;
      overflow-y: hidden;

  
      .choose-profile-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        overflow-x: auto;
        min-width: 430px;
        margin-left: 53px;
      }
  
      .choose-image-title {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: scroll;
        scroll-snap-type: x mandatory;
      }
  
      .img-button {
        border: 3px solid transparent;
         padding: 10px ;
        &:focus > .choose-profimgs {
          border-radius: 50%;
          border: 3px solid #fff;
          transform: scale(1.01);
          opacity: 1;
          scroll-snap-align: end;
        }
      }
  
      .choose-profimgs {
        width: 184px;
        height: 184px;
        margin: 0 20px;
        opacity: 0.8;
      }
    }
  }