import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { getStartHeaderInfo, getIpDetails } from '../../store/slices/initialSlices'
import './Splash.scss'
import Lottie from 'react-lottie-player'
import animationData from '../../json/animationData.json'
import { useDispatch, useSelector } from 'react-redux'
import { getLogin } from '../../store/slices/login'
import { pushScreenViewEvent } from '../../GoogleAnalytics'
const SplashScreen = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const token = localStorage.getItem('token')
  const navData = useSelector(state => state?.homeReducer?.navbarData)
  const navaDataKids = useSelector(state => state?.homeReducer?.navbarKidsData)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const startheaderinfo = useSelector(state => state?.initialReducer?.startHeaderInfo?.response)
  let tvDeviceInfo = {
    id: '',
    model: '',
    sdkVersion: ''

  }

  function updateTvDeviceInfo(newInfo) {
    tvDeviceInfo = { ...tvDeviceInfo, ...newInfo }
  }

  function detectPlatform() {
    if (window.tizen !== undefined) {
      return 'Tizen'
    } else if (window.webOS !== undefined) {
      return 'webOS'
    } else {
      return 'Unknown'
    }
  }

  async function sha256hash(text) {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');
    return hashHex;
  }
  
  function getTVDeviceInfo() {
    const platform = detectPlatform()
    switch (platform) {
      case 'Tizen':
        const tizenId = tizen.systeminfo.getCapability(
              'http://tizen.org/system/tizenid'
            )
        sha256hash(tizenId).then(hash =>  localStorage.setItem('setDeviceID',hash));
        // try {
        //   const tizenId = tizen.systeminfo.getCapability(
        //     'http://tizen.org/system/tizenid'
        //   )
        //   const hashedId = sha256(tizenId).toString();
        //   //const newId = uuidv4();
        //   const tizenModel = tizen.systeminfo.getCapability(
        //     'http://tizen.org/system/model_name'
        //   )
        //   const tizenSdkVersion = tizen.systeminfo.getCapability(
        //     'http://tizen.org/feature/platform.version'
        //   )

        //   updateTvDeviceInfo({
        //     id: hashedId,
        //     model: tizenModel,
        //     sdkVersion: tizenSdkVersion
        //   })
        //   deviceId.value = hashedId
        //   localStorage.setItem('setDeviceID',hashedId)
        //   //setDeviceID(tizenId)
        // } catch (error) {
        //   updateTvDeviceInfo({
        //     id: '',
        //     model: 'Unknown Tizen',
        //     sdkVersion: 'Unknown'
        //   })
        // }
        break

      case 'webOS':
        var request = webOS.service.request('luna://com.webos.service.sm', {
          method: 'deviceid/getIDs',
          parameters: {
            idType: ['LGUDID'],
          },
          onSuccess: function (inResponse) {
            if (inResponse?.idList?.[0]?.idValue) {
              //setDeviceID(inResponse?.idList?.[0]?.idValue)
              localStorage.setItem('setDeviceID',inResponse?.idList?.[0]?.idValue)
            } else {
              console.log('Device ID not found in the response')
            }
          },
          onFailure: function (inError) {
            console.log('Failed to get system ID information')
          }
        })
        break
      default:
        updateTvDeviceInfo({
          id: '',
          model: 'Unknown',
          sdkVersion: 'Unknown'
        })
    }
  }
  useEffect(() => {
    // GA : ScreenView Event
    pushScreenViewEvent({screenName:'splash'})
    dispatch(getStartHeaderInfo())
    dispatch(getIpDetails())
    appFocus()
    getTVDeviceInfo()
  }, [])

  useEffect(() => {
    if (apaMetaData && Object.keys(apaMetaData).length > 0 && startheaderinfo?.region) {
      token
        ? (dispatch(
          getLogin({
            userHash: localStorage.getItem('user_hash'),
            type: 'splashLogin',
            hks: localStorage.getItem('hks')
          })
        ),
          navigate('/prewelcome'))
        : navigate('/landing')
    }
  }, [apaMetaData, startheaderinfo])
  return (
    <div className="container-splash">
      <img className="splash" src="images/claro-video-logo.png" />
      <Lottie loop animationData={animationData} play />
    </div>
  )
  // spatial start
  function appFocus() {
    //landing page
    SpatialNavigation.add('Landing-Page', {
      selector: ' .landingpage .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //register and signin page
    SpatialNavigation.add('Register-Blueband', {
      selector: '.register-popup .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,

      leaveFor: {
        left: '',
        right: '',
        up: ''
      }
    })

    SpatialNavigation.add('Register-Page', {
      selector: '.App .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: ''
      }
    })
    SpatialNavigation.add('sign-in-btn-container', {
      selector: 'login-email-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('LoginTermsandCondition', {
      selector: '.login-terms-and-condition .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,

      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //profile screen
    SpatialNavigation.add('Watch-Profile', {
      selector: '.add-profile .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Edit-Watch-Profile', {
      selector: '.watchingProfile .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#backButtonId',
        down: '#ListoEditButton_id'
      }
    })
    SpatialNavigation.add('Add-Profile', {
      selector: '.add-edit-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: '#add-profile-name-input-container'
      }
    })
    SpatialNavigation.add('Delete-Profile', {
      selector: '.delete-profile .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Choose-Image', {
      selector: '.choose-profile-image .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Edit-Profile', {
      selector: '.edit-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //SN for Home Page Nav
    SpatialNavigation.add('Nav-Bar', {
      selector: '.nav-center-alg .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: '#CarouselId'
      }
    })
    //Navigation for Carousel
    SpatialNavigation.add('CarouselId', {
      selector: '.Caraousel_CSS .focusable',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: '#Home-Data'
      }
    })
    //Vodmovies for Home Page Nav
    SpatialNavigation.add('vod-mov', {
      selector: '.vod-layout .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //Vodmovies for model Page Nav
    SpatialNavigation.add('vod-mov-player-model', {
      selector: '.vod-player-warning-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('vod-mov-player-model-back', {
      selector: '.player-warning-backScreen .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('vod-mov-player-model-button', {
      selector: '.player-warning-button-container-div .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Home-Data', {
      selector: '.railsHome .focusable',
      straightOnly: false,
      straightOverlapThreshold: 0.1,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#CarouselId',
        down: ''
      }
    })
    SpatialNavigation.add('Home-Data-regpopup', {
      selector: '.railsHome-regpopup .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('Toonkids', {
      selector: '.Toonical_block .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('premium-card', {
      selector: '.premium-carousel-wrapper .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('payment-method-mobile', {
      selector: '.payment-method-mobile .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '#btn-cancel',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('promo-code-page', {
      selector: '.promo-code-page .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#add-promo-code-back',
        down: ''
      }
    })

    SpatialNavigation.add('payment-method-setting', {
      selector: '.payment-method-setting .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#paymentmethod-page-back',
        down: '#payment-method-bottom-cancel-button'
      }
    })
    SpatialNavigation.add('land-line-add-number-via-web', {
      selector: '.land-line-add-number-via-web .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#land-line-redirect-web-back',
        down: '#landlineFinishSubscriptionOkBtn'
      }
    })
    SpatialNavigation.add('KeyboardPromoCode', {
      selector: '.KeyboardPromoCode .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#btnbackspace',
        down: ''
      }
    })
    //deletemy content
    SpatialNavigation.add('delete-mycontent', {
      selector: ' .deletecard-mainclass .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('filter-screen', {
      selector: '.submenufiltercontainer .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //Settings
    SpatialNavigation.add('New-Settings', {
      selector: '.main-container-settings .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Language-Selection', {
      selector: '.lang-selection-container .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Profile-Settings', {
      selector: '.profile-settings-control-div .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-bar',
        down: '#Reminder-Profile-Settings'
      }
    })
    SpatialNavigation.add('Reminder-Profile-Settings', {
      selector: '.reminder-events-div .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-bar',
        down: ''
      }
    })
    SpatialNavigation.add('Reminder-action-screen-settings', {
      selector: '.action-screen-settings .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Parental-control-screen-settings', {
      selector: '.parental-control-settings .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Profile-Settings',
        down: ''
      }
    })

    //restoring change from Amit
    SpatialNavigation.add('Choose-language-Settings', {
      selector: '.lang-setting-main-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Profile-Settings',
        down: ''
      }
    })

    SpatialNavigation.add('Pin-Change-New-Settings', {
      selector: '.app-css-change-pin .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-bar',
        down: ''
      }
    })
    SpatialNavigation.add('Locked-Channels-List', {
      selector: '.app-css-locked-channel-list .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-bar',
        down: ''
      }
    })
    SpatialNavigation.add('My-Devices', {
      selector: '.app-css-my-devices .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Content-classification', {
      selector: '.content-classification .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Add-Subscription', {
      selector: '.plan-selector-app-css .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('checkout-subsription-page', {
      selector: '.checkout-subsription-page .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#subscription-page-back',
        down: ''
      }
    })
    SpatialNavigation.add('Subscription Details', {
      selector: '.subscriptionDetails .focusable',
      enterTo: 'default-element',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#subscriptionDetailPageBack',
        down: '#subscriptionDetailPage-subs-btn'
      }
    })
    SpatialNavigation.add('Active-Subscription', {
      selector: '.active-cards .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Cancel-Subscription', {
      selector: '.cancelSubDiv .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Help-And-Settings', {
      selector: '.right-container .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Security-Pin', {
      selector: '.create-pin-button-container .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Create-Security-Back', {
      selector: '.back-indicator-regrassar .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '#Key_0',
        right: '',
        up: '',
        down: '#see-pin'
      }
    })
    SpatialNavigation.add('Create-Security-Pin', {
      selector: '.left-container-div .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '#see-pin',
        up: '#back-indicator-regrassar',
        down: ''
      }
    })
    SpatialNavigation.add('Create-Security', {
      selector: '.right-container-div .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '#Key_0',
        right: '',
        up: '#Create-Security-Back',
        down: ''
      }
    })
    SpatialNavigation.add('pin-confirmation-container', {
      selector: '.pin-confirmation-button-container .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //EPG Main Guide SN
    SpatialNavigation.add('MainGuideId', {
      selector: '.epg-channel-program .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //EPG Mini Guide SN
    SpatialNavigation.add('MiniGuideId', {
      selector: ' .mini-epg-nav .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //SN for VOD SERIES
    SpatialNavigation.add('vod-series', {
      selector: '.series-Main-Layout .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('vodseriesplayerwarning', {
      selector: '.series-player-warning-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: false,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('MoreInfoSeason', {
      selector: '.moreinfoseason .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: false,
      leaveFor: {
        left: '',
        right: '',
        up: () => {
          return false
        },
        down: ''
      }
    })
    SpatialNavigation.add('cast-layout', {
      selector: '.cast-layout .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: false,
      leaveFor: {
        left: '',
        right: '',
        up: () => {
          return false
        },
        down: ''
      }
    })
    SpatialNavigation.add('search-layout', {
      selector: '.search-container-layout .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('keyboard-layouts', {
      selector: '.keyboard-layout .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('talent-search-layout', {
      selector: '.talent-search-layout .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: false,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('massinfo-layout', {
      selector: '.tab-tray-wrapper .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: false,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('search-input-layouts', {
      selector: '.search-input-layout .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: false,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('search-screen', {
      selector: '.search-screen-layout .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('keyboardCover', {
      selector: '.keyboardCover .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('top-buttons-key', {
      selector: '.top-buttons-key .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('keyboard-special-button', {
      selector: '.keyboard-special-buttons .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('keyboard-clear-button', {
      selector: '.keyboard-clear-buttons .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })
    SpatialNavigation.add('EpConfirmation', {
      selector: '.epconf-body .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Program-Options', {
      selector: '.program-details-container .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Bitmovin-Vod', {
      selector: '.bitmovin .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Bitmovin-Vod-Subtitles', {
      selector: '.language-container .focusable',
      enterTo: 'last-focused',
      rememberSource: true,
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Bitmovin-Fin-Player', {
      selector: '.fin-player-main-container .focusable',
      enterTo: 'last-focused',
      rememberSource: true,
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    // vodseries carousel of player
    SpatialNavigation.add('seasonbtn', {
      selector: '.Season-button-header .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: '#episodefocus'
      }
    })
    SpatialNavigation.add('episodefocus', {
      selector: '.episodecontainer .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#seasonbtn',
        down: '#back'
      }
    })
    //Navigation changes for live player
    SpatialNavigation.add('liveplayer', {
      selector: '.bottom-controls-live .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('userconfirm', {
      selector: '.user-confirm-button-block .focusable',
      enterTo: '',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('player-error', {
      selector: '#player-error .focusable',
      enterTo: '',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('EpMoreInfo', {
      selector: '.info-layouts .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add({
      selector: '.actors-block .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#backButton',
        down: ''
      }
    })
    //contract offers screens
    SpatialNavigation.add('contract-screen', {
      selector: ' .bottom-container .focusable',
      enterTo: 'default-element',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('contract-live', {
      selector: '.subscription-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //recordingepisode
    SpatialNavigation.add('Recording-episode', {
      selector: '.rec-episode-list .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //delete recordingepisode
    SpatialNavigation.add('Delete-Recording', {
      selector: '.deletecard-mainclass .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    //subscriptionContainer
    SpatialNavigation.add('Rec-offer', {
      selector: '.subscriptionContainer .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('Submenu-Filter', {
      selector: '.genrefilter-railcontent .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('background-Error', {
      selector: '.accept-button .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('ShowReminder', {
      selector: '.reminder-alert .focusable',
      enterTo: 'last-focused',
      straightOnly: true,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })
    SpatialNavigation.add('CheckoutLayout', {
      selector: '.checkout-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('CheckoutKeyboard', {
      selector: '.checkout-keyboard-layout .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '#searchInput',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('CheckoutDetails', {
      selector: '.checkout-details-container .focusable',
      enterTo: 'last-focused',
      straightOnly: false,
      straightOverlapThreshold: 0.4,
      rememberSource: true,
      leaveFor: {
        left: '.checkout-keyboard-layout',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('CheckoutTopButtons', {
      selector: '.top-buttons-key .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })

    SpatialNavigation.add('CheckoutSpecialButtons', {
      selector: '.keyboard-special-buttons .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '#Nav-Bar',
        down: ''
      }
    })

    SpatialNavigation.add('CallCenterPage', {
      selector: '.background-call-page .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('NetworkErrorPage', {
      selector: '.network-service-error-div .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('MultiSubscription', {
      selector: '.app-subscription-css .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('PremiumSubscription', {
      selector: '.north-pack-subscription .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('FinishSubscription', {
      selector: '.checkout-subsription-done .focusable',
      enterTo: '',
      straightOnly: true,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('FibraLinePopup', {
      selector: '.popup-overlay .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.add('CreditCardPopup', {
      selector: '.credit-card-popup-data-div .focusable',
      enterTo: '',
      straightOnly: false,
      straightOverlapThreshold: 0.5,
      rememberSource: true,
      leaveFor: {
        left: '',
        right: '',
        up: '',
        down: ''
      }
    })

    SpatialNavigation.makeFocusable()
  }
}
export default React.memo(SplashScreen)
