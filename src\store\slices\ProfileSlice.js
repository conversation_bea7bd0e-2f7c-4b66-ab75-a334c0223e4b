import { createSlice } from '@reduxjs/toolkit'

export const ProfileSlice = createSlice({
  name: 'profile',
  initialState: {
    profileData: {},
    profileCreateData: {},
    profileEditData: {},
    profileDeleteData: {},
    profileAvatarData: {},
    userAuthDevice: {},
    pushSession: {},
    pushSessionError: {},
    userProfile: {},
    isLoading: false,
    error: null,
    isHomeScreen: false
  },
  reducers: {
    //update
    getProfileUpdateData: (state, { payload }) => {
      state.isLoading = true
    },
    getProfileUpdateSuccess: (state, { payload }) => {
      state.profileEditData = payload
      state.isLoading = false
    },
    getProfileUpdateError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    // deletedata
    getProfileDeleteData: (state, { payload }) => {
      state.isLoading = true
    },
    getProfileDeleteSuccess: (state, { payload }) => {
      state.profileDeleteData = payload
      state.isLoading = false
    },
    getProfileDeleteError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    // avatardata
    getProfileAvatarData: (state, { payload }) => {
      state.isLoading = true
    },
    getProfileAvatarSuccess: (state, { payload }) => {
      state.profileAvatarData = payload
      state.isLoading = false
    },
    getProfileAvatarError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    // profileread
    getProfileReadData: (state, { payload }) => {
      state.isLoading = true
      state.profileEditData = {}
      state.profileDeleteData = {}
      state.profileCreateData = {}
      state.profileData = {}
      state.profileAvatarData = {}
      state.userAuthDevice = {}
      state.pushSession = {}
    },
    getProfileReadSuccess: (state, { payload }) => {
      state.profileData = payload
      state.isLoading = false
    },
    getProfileReadError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    // profilecreatedata
    getProfileData: (state, { payload }) => {
      state.isLoading = true
    },
    getProfileSuccess: (state, { payload }) => {
      state.profileCreateData = payload
      state.isLoading = false
    },
    getProfileError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getUserAuthDevice: (state) => {
      state.isLoading = true;
    },
    getUserAuthDeviceSuccess: (state, { payload }) => {
      state.userAuthDevice = payload;
      state.isLoading = false;
    },
    getUserAuthDeviceError: (state, { payload }) => {
      state.isLoading = false;
      state.error = payload;
    },

    getPushSession: (state) => {
      state.isLoading = true;
    },
    getPushSessionSuccess: (state, { payload }) => {
      state.pushSession = payload;
      state.isLoading = false;
    },
    getPushSessionError: (state, { payload }) => {
      state.isLoading = false;
      state.pushSessionError = payload;
    },

    getUserProfile: (state, { payload }) => {
      state.userProfile = payload;
    },

    getClearProfileState: (state) => {
      state.profileData = {};
      state.profileCreateData = {};
      state.profileEditData = {};
      state.profileDeleteData = {};
      state.profileAvatarData = {};
      state.userAuthDevice = {};
      state.pushSession = {};
      state.pushSessionError = {};
      state.userProfile = {};
      state.isLoading = false;
      state.error = null;
      state.isHomeScreen = false;
    },

    getBackProfile: (state, { payload }) => {
      state.isHomeScreen = payload;
    }
  },
})

export const {
  getProfileDeleteData,
  getProfileDeleteSuccess,
  getProfileDeleteError,
  getProfileAvatarData,
  getProfileAvatarSuccess,
  getProfileAvatarError,
  getProfileReadData,
  getProfileReadError,
  getProfileReadSuccess,
  getProfileData,
  getProfileSuccess,
  getProfileError,
  getProfileUpdateData,
  getProfileUpdateSuccess,
  getProfileUpdateError,
  getUserAuthDevice,
  getUserAuthDeviceSuccess,
  getUserAuthDeviceError,
  getPushSession,
  getPushSessionSuccess,
  getPushSessionError,
  getUserProfile,
  getClearProfileState,
  getBackProfile
} = ProfileSlice.actions

export default ProfileSlice.reducer
