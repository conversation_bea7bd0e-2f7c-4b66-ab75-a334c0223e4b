import React from "react";
import { fireEvent, getByTestId, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import Navbar from "./Navbar";


const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const mockerrorresponse = [
    {
        "page": "Inicio",
        "code": "homeuser"
    },
    {
        "page": "Películas",
        "code": "peliculas",
        "menu": [
            {
                "id": "334791",
                "id_parent": "172076",
                "code": "accionaventura",
                "text": "Acción y Aventura",
            },
            {
                "id": "334792",
                "id_parent": "172076",
                "code": "animeyvideojuego",
                "text": "Anime",
            },
        ]
    }
]
describe('Navbar page test', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('it should render the Navbar Page', () => {
        window.HTMLElement.prototype.scrollIntoView = function () { };
        initialState.homeReducer = {
            navbarData: mockerrorresponse
        }
        const { container } = renderWithState(<Navbar />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'nav-0');
        fireEvent.blur(scroll)
        fireEvent.focus(scroll)
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        const { container } = renderWithState(<Navbar />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'serachId');
        fireEvent.focus(scroll)
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        initialState.login = {
            isLoggedIn: {
                response: {
                    user_id: '55555'
                }
            }
        }
        const { container } = renderWithState(<Navbar />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'userProfileImg');
        fireEvent.focus(scroll)
        fireEvent.blur(scroll)
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollTo = function () { };
        const props = {
            page: 'home'
        }
        const { container } = renderWithState(<Navbar {...props} />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'userProfileImg');
        localStorage.setItem('currNavIdx', '0')
        localStorage.setItem('subMenu', '1')
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollTo = function () { };
        const props = {
            page: 'home'
        }
        const { container } = renderWithState(<Navbar {...props} />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'userProfileImg');
        localStorage.setItem('currNavIdx', '0')
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollTo = function () { };
        const props = {
            page: 'home'
        }
        const { container } = renderWithState(<Navbar {...props} />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'userProfileImg');
        localStorage.setItem('subMenu', '-1')
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollTo = function () { };
        const props = {
            page: 'home'
        }
        const { container } = renderWithState(<Navbar {...props} />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'userProfileImg');
        localStorage.setItem('subMenu', '1')
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard', () => {
        window.HTMLElement.prototype.scrollTo = function () { };
        const props = {
            page: 'home'
        }
        const { container } = renderWithState(<Navbar {...props} />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'userProfileImg');
        localStorage.setItem('currNavIdx', '1')
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
})
