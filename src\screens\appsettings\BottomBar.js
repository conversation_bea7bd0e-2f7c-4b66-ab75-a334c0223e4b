import { useState, useEffect } from 'react';
import React from "react";
import '../../styles/BottomBar.css';


const BottomBar = (props) => {
	const [isSamsungKeyboardOpen, setSamsungKeyboardOpen] = useState(false);

	const checkSamsungKeyboard = () => {
		if(typeof tizen !== 'undefined') {
			const isKeyboardOpen = window.innerHeight < window.screen.height;
			setSamsungKeyboardOpen(isKeyboardOpen);
		}
	  };
	
	  useEffect(() => {
		window.addEventListener('resize', checkSamsungKeyboard);
		return () => {
		window.removeEventListener('resize', checkSamsungKeyboard);
		};
	  }, [checkSamsungKeyboard]);

	return (
		<>
		{ !isSamsungKeyboardOpen && (
			<div className="bottom-bars">
			<div className={props.isfooter == true ? "regibottom-icon" : "bottom-bar-icon"}>
				<img className="close-icon" src={props.image} />
				<p className={props.isfooter == true ? "loginfooter-text" : "loginfooter-text"}>{props.title}</p>
			</div>
			{props?.secondImage &&
			<div style={{marginLeft:80}} className={props.isfooter == true ? "regibottom-icon" : "bottom-bar-icon"}>
				<img className="close-icon" src={props?.secondImage} />
				<p className={props.isfooter == true ? "loginfooter-text" : null}>{props?.secondTitle}</p>
			</div>}
			{props.filter === 'true' ?
			<div className="bottom-bar-icon" style={{marginLeft:'3%'}}>
				<img className="close-icon" src={'images/filter_icon.svg'} />
				<p >{props.filterTitle}</p>
			</div> : null}
			</div>
		)}
		</>		
	)
}

export default BottomBar