import React, { useCallback, useEffect } from 'react'
import { useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { store } from '../../store/sagaStore'
import {
  getBackNavigate,
  getClearAllLoginStates,
  getLogin,
  getUserInfo,
  getGuestUserPlayerData
} from '../../store/slices/login'
import { getPayWayToken } from '../../store/slices/EpgSlice'
import { getProfileReadData } from '../../store/slices/ProfileSlice'
import { pushScreenViewEvent, pushLoginErrorEvent } from '../../GoogleAnalytics'
import '../loginregister/Loginpage.scss'
import { errorMailPattern } from './Regex'

const Loginpage = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [invalidEmail, setIvalidEmail] = useState('')
  const [notRegisterEmail, setNotRegisterEmail] = useState('')
  const [notRegisterEmailMsg, setNotRegisterEmailMsg] = useState('')
  const [loginApiErrMsg, setLoginApiErrMsg] = useState('')
  const [isLoginClicked, setIsLoginClicked] = useState(false)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const startheaderinfoData = useSelector(
    state => state?.initialReducer?.startHeaderInfo?.response
  )
  const apilanguage = translations?.language?.[region]
  const userInfo = useSelector(state => state?.login?.userInfo)
  const loginapi = useSelector(state => state?.login)
  const gamificationid = loginapi?.loginSuccess?.response?.gamification_id
  const hks = loginapi?.loginSuccess?.response?.session_stringvalue
  const userid = loginapi?.loginSuccess?.response?.parent_id
  const user_hash = loginapi?.loginSuccess?.response?.session_userhash
  const token = localStorage.getItem('token')
  const lasttouch = loginapi?.loginSuccess?.response?.lasttouch.profile
  const seenLastTouch = loginapi?.loginSuccess?.response?.lasttouch?.seen
  const vcardSeriesDetails = useSelector(
    state => state?.login?.vcardSeriesDetails
  )
  const vodDetails = useSelector(state => state?.login?.vcardSeriesDetails)
  const vcardDetails = useSelector(state => state?.login?.vcardDetails)
  const anonymousUser = useSelector(state => state?.login?.anonymousUser)
  const { state } = useLocation()
  const validemail = errorMailPattern.test(state?.value)
  const passwordError = loginapi?.loginError?.response?.error ? true : false
  useEffect(() => {
    // GA : ScreenView Event
    pushScreenViewEvent({screenName:'signin',prevScreenName:'landing',screenData:loginapi?.loginSuccess?.response})
    state?.focus == true ? document.getElementById('signid')?.focus() : ''
    switch (true) {
      case userInfo?.status == '1' && !validemail:
        document.getElementById('email')?.focus()
        setIvalidEmail(
          handleTranslationchange('Onboarding_IniciaSesionErrorRCU_Form_TextoTooltip1')
        )
        break
      case userInfo?.status == '1' && validemail:
        document.getElementById('email')?.focus()
        setNotRegisterEmail(
          handleTranslationchange('Onboarding_IniciaSesionCorreoInvalidoRCU_Form_TextoTooltip1')
        )
        setNotRegisterEmailMsg(
          handleTranslationchange('Onboarding_IniciaSesionCorreoInvalidoRCU_Form_TextoTooltip2')
        )
        break
      case loginapi?.loginError?.status == '1' && passwordError:
        document.getElementById('password')?.focus()
        setLoginApiErrMsg(
          handleTranslationchange('Onboarding_IniciaContrasenaRCU_Form_TextoTooltip1')
        )
        break
        case loginapi?.loginError?.response?.accepted_terms == '0':
          navigate('/login-Terms-and-Conditons',{state:{password:state?.password, confirmscreen:state?.confirmscreen, username:state?.value}})
          break
      default:
        break
    }
    if (
      userInfo?.status == '0' &&
      loginapi?.loginSuccess?.status == '0' &&
      gamificationid
    ) {
      const payload = {
        hks: hks,
        userid: userid,
        token: token,
        gamificationid: gamificationid,
        lasttouch: lasttouch
      }
      store.dispatch(getProfileReadData(payload))
      localStorage.setItem('gamificationid', gamificationid)
      localStorage.setItem('loginId', userid)
      localStorage.setItem('lasttouch', lasttouch)
      localStorage.setItem('seenLastTouch', seenLastTouch)
      localStorage.setItem('user_hash', user_hash)
      localStorage.setItem('hks', hks)

      if (anonymousUser?.page == 'livePlayer') {
        store.dispatch(
          getPayWayToken({
            hks: hks,
            user_id: userid
          })
        )
      }
      navigate('/prewelcome', { 
        state: { 
          confirmscreen: state?.confirmscreen, 
          seriesEpisodeData: state?.seriesEpisodeData, 
          page: state?.page,
          pageName: state?.pageName,
          fromDetailsPage: state?.fromDetailsPage
        } 
      })
    }
  }, [userInfo, loginapi, gamificationid,passwordError])

  useEffect(() => {
    if (!isLoginClicked) return
    const errorMessages = [invalidEmail, notRegisterEmail, loginApiErrMsg]
    const hasError = errorMessages.some(msg => !!msg)
    if (hasError) {
      errorMessages.forEach(msg => {
        if (msg) {
          pushLoginErrorEvent(
            msg.toLowerCase(),
            handleTranslationchange(
              'Onboarding_IniciaSesionRCU_Texto1'
            )?.toLowerCase()
          )
        }
      })
      setIsLoginClicked(false)
    }
  }, [invalidEmail, notRegisterEmail, loginApiErrMsg, isLoginClicked])

  const clickSignin = e => {
    e.preventDefault()
    setIsLoginClicked(true)
    dispatch(getUserInfo({ data: state?.value }))
    dispatch(
      getLogin({
        password: state?.password,
        hks: startheaderinfoData?.session_stringvalue,
        navigate,
        confirmscreen: state?.confirmscreen
      })
    )
  }

  const forgotPassword = e => {
    navigate('/loginforgotpassword', {
      state: { 
        value: state?.value, 
        password: state?.password,
        seriesEpisodeData: state?.seriesEpisodeData, 
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage
      }
    })
  }
  const navigateLoginPassword = e => {
    e.preventDefault()
    if (e.key === 'Enter') {
      dispatch(getBackNavigate(true))
      dispatch(getClearAllLoginStates())
      navigate('/loginpassword', {
        state: {
          page: 'loginpage',
          value: state?.value,
          password: state?.password,
          seriesEpisodeData: state?.seriesEpisodeData, 
          pageName: state?.pageName,
          fromDetailsPage: state?.fromDetailsPage
        }
      })
    }
  }

  const LoginPassword = e => {
    e.preventDefault()
    dispatch(getClearAllLoginStates())
    dispatch(getBackNavigate(true))
    navigate('/loginpassword', {
      state: {
        page: 'loginpage',
        value: state?.value,
        password: state?.password,
        seriesEpisodeData: state?.seriesEpisodeData,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage
      }
    })
  }

  const navigateLoginEmail = e => {
    e.preventDefault()
    if (e.key === 'Enter') {
      navigate('/loginmail', {
        state: {
          page: 'loginpage',
          value: state?.value,
          password: state?.password,
          seriesEpisodeData: state?.seriesEpisodeData,
          pageName: state?.pageName,
          fromDetailsPage: state?.fromDetailsPage
        }
      })
    }
  }

  const clickclear = e => {
    e.preventDefault()
    switch (true) {
      case anonymousUser?.page == 'livePlayer':
        dispatch(getGuestUserPlayerData({}))
        navigate('/livePlayer', {
          state: { showControls: 'live', grid: anonymousUser?.grid }
        })
        break
      case state?.fromDetailsPage:
        navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
          state: { 
            data: state?.seriesEpisodeData,
            seriesEpisodeData: state?.seriesEpisodeData,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage
          }
        })
        break
      case vcardSeriesDetails?.page == 'series':
        navigate('/series', { state: { data: vcardSeriesDetails?.vodSeries } })
        break
      case vcardDetails?.page == 'movies':
        navigate('/movies', { state: { vodData: vcardDetails?.vodMoviesData } })
        break
      case vcardSeriesDetails?.playerpage == 'playerrecord':
        navigate('/series', {
          state: { data: vcardSeriesDetails?.playerepisode }
        })
        break
      case state?.page == 'search':
        navigate('/EPconfirmation', {
          state: { inputValue: state?.inputValue,page:state?.page }
        })
        break  
      default:
        navigate('/landing', {
          state: { backfocusid: state?.backfocusid }
        })
        break
    }
  }

  const keypresshandler = event => {
    if (
      event?.keyCode === 10009 ||
      event?.keyCode === 461 ||
      event?.keyCode === 89 ||
      event?.keyCode === 8
    ) {
      dispatch(getClearAllLoginStates())
      if (vodDetails?.confirmscreen || anonymousUser?.confirmscreen) {
        navigate('/EPconfirmation', {
          state: {
            page: anonymousUser?.page,
            seriesEpisodeData: state?.seriesEpisodeData,
            vodMoviesData: state?.vodMoviesData,
            page: state?.page,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage
          }
        })
      } else {
        navigate('/landing', {
          state: { backfocusid: state?.backfocusid }
        })
      }
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handleTranslationchange = useCallback(
    keyname => {
      return apilanguage?.[keyname] ? apilanguage?.[keyname] : [keyname]
    },
    [apilanguage]
  )

  return (
    <div className="App">
      <div className="App-logo">
        <img
          src={'images/claro-video-logo.png'}
          className="logo-img"
          alt="logo"
        />
      </div>
      {/* <div className='loginpage-height'> */}
      <p className="login-Header">
        {handleTranslationchange('Onboarding_IniciaSesion_TextoTitulo')}
      </p>
      {/* <div className='choose-login-option'>
          <div>
            <button className="login-mobile">CON TU TELÉFONO MÓVIL</button>
            <button className="login-Remote active">CON TU CONTROL REMOTO</button>
          </div>
        </div> */}
      <div className="login-title">
        <label className="login-page-email">
          {handleTranslationchange('Onboarding_IniciaSesionRCU_Texto1')}
        </label>
      </div>
      <div className="loginbutton-height">
        <button
          className={`${
            invalidEmail || notRegisterEmail && notRegisterEmailMsg
              ? 'email-redborder focusable'
              : state?.focus == true
              ? 'login-namereg focusable'
              : 'loginmail-placeholder focusable'
          }`}
          id="email"
          onClick={e =>
            navigate('/loginmail', {
              state: { 
                value: state?.value, 
                password: state?.password, 
                seriesEpisodeData: state?.seriesEpisodeData,
                fromDetailsPage: state?.fromDetailsPage,
                pageName: state?.pageName
              }
            })
          }
          onKeyPress={e => navigateLoginEmail(e)}
          autoFocus
        >
          {state?.focus && state?.value
            ? state?.value?.length > 44
              ? `${state?.value.slice(0, 44)}...`
              : state?.value
            : handleTranslationchange('Onboarding_IniciaSesionRCU_TextoPlaceholder1')}
        </button>
        <div className="login-error-box">
          <div
            id={`exampleInputEmailMsg`}
            className={`err-text ${
              invalidEmail
                ? 'login-invalid-email'
                : notRegisterEmail || notRegisterEmailMsg
                ? 'login-invalid-text'
                : 'invisible'
            }`}
          >
            <p className="login-err-msg">
              {[invalidEmail || notRegisterEmail,`${" "}`,notRegisterEmailMsg]}
            </p>
          </div>
        </div>
        <button
          className={`${
            loginApiErrMsg
              ? 'password-redborder focusable'
              : state?.focus == true
              ? 'login-password focusable'
              : 'loginpassword-placeholder focusable'
          }`}
          id="password"
          onClick={e => LoginPassword(e)}
          onKeyPress={e => navigateLoginPassword(e)}
        >
          {state?.focus && state?.password
            ? state?.password?.length > 26
              ? '*'.repeat(26)
              : '*'.repeat(state?.password?.length || 0)
            : handleTranslationchange('Onboarding_IniciaSesionRCU_TextoPlaceholder2')}
        </button>
        <div className="error-box">
          <div
            id={`exampleInputEmailMsg`}
            className={`err-text ${
              loginApiErrMsg ? 'invalid-text-pass' : 'invisible'
            }`}
          >
            {loginApiErrMsg}
          </div>
          <span className="err_msg"></span>
        </div>
      </div>
      {/* </div> */}
      <div>
        <button
          className={`${!state?.value || !state?.password?'loginpage-next':'loginpage-next-enable'} focusable`}
          id="signid"
          disabled={!state?.value || !state?.password}
          onClick={e => clickSignin(e)}
        >
          {[handleTranslationchange('Onboarding_IniciaSesionRCU_TextoBotonPrimario')]}
        </button>
      </div>
      <div>
        <button
          className="loginpage-clear focusable"
          id="registernext"
          onClick={e => clickclear(e)}
        >
          {[handleTranslationchange('Onboarding_IniciaSesionRCU_TextoBotonSecundario')]}{' '}
        </button>
      </div>
      <div className="sign-registerreg">
        <button
          className="sign-register focusable"
          id="rigisterbtn"
          onClick={e => forgotPassword(e)}
        >
          {handleTranslationchange(
            'Onboarding_IniciaSesionRCU_Texto2'
          )} {handleTranslationchange(
            'Onboarding_IniciaSesionRCU_Texto3'
          )}
        </button>
      </div>
    </div>
  )
}

export default Loginpage
