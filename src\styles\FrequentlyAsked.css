.page-title{
	font: normal normal normal 48px/57px Roboto;
	letter-spacing: 0px;
	color: #F1F2F3;
	opacity: 1;
	display: flex;
	justify-content: center;
	margin-top: 3%;
}

.filter-button{
	font: normal normal normal 36px/42px Roboto;
	letter-spacing: 0px;
	color: #ADADAD;
	opacity: 1;
	display: flex;
	justify-content: center;
	margin-top: 3%;
}

.filter-button:focus {
	color: #981C15

}

.page-div
{
	text-align: left;
	display: flex;
	flex-direction: row;
	font: normal normal normal 30px/40px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin-left: 4%;
	margin-top: 2%;
}

.dataClass{
	height: 63vh;
  overflow-y: scroll;
}

.dataClass::-webkit-scrollbar {
		width: 0.3em; /* Adjust the width as needed */
		background-color: transparent; /* Set the background color of the scrollbar track */
	  display: flex;
  }
  
  .dataClass::-webkit-scrollbar-thumb {
		background-color: #515151; /* Set the color of the scrollbar thumb */
	  height: 60px;
		border-radius: 2px;
  }

.contentClass{
	width: 82%;
}

.buttonDiv{
	margin-top: 12%;
	display: flex;
	flex-direction: column;
	margin-left: 5%;
	margin-right: 5%;
}

.buttonFocus :focus {
	background: #981C15 0% 0% no-repeat padding-box;
}
