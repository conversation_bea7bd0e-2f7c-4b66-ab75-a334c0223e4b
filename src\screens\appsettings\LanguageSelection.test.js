import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, useNavigate, useLocation } from 'react-router-dom';
import LanguageSelection from './LanguageSelection';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
 
// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn(),
  useLocation: jest.fn(),
}));
 
// Create mock store
const mockStore = configureStore([]);
 
describe('LanguageSelection', () => {
  const mockNavigate = jest.fn();
  let store;
 
  beforeEach(() => {
    // Setup mock localStorage
    const localStorageMock = {
      getItem: jest.fn((key) => {
        if (key === 'region') return 'US';
        return null;
      }),
      setItem: jest.fn((key, value) => {
        // Mock implementation to store values for testing
        localStorage[key] = value;
      }),
      clear: jest.fn(),
    };
    // Assign to global object
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
 
    // Initialize Redux mock store
    store = mockStore({
      initialReducer: {
        appMetaData: {
          translations: JSON.stringify({
            language: {
              US: {
                subtitled_in_spanish: 'Subtitulada al español',
                subtitled_in_portuguese: 'Subtitulada al portugués',
                original_language: 'Idioma Original',
                portuguese_language: 'Doblada al portugués',
                spanish_language: 'Doblada al español'
              }
            }
          })
        }
      }
    });
 
    useNavigate.mockReturnValue(mockNavigate);
    useLocation.mockReturnValue({ state: { type: 'onDemand', currentLanguage: 'Idioma Original' } });
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  // Helper function to render component with all required providers
  const renderComponent = () => {
    return render(
<Provider store={store}>
<MemoryRouter>
<LanguageSelection />
</MemoryRouter>
</Provider>
    );
  };
 
  it('renders all language options', () => {
    renderComponent();
 
    const languages = [
      "Subtitulada al español",
      "Subtitulada al portugués",
      "Idioma Original",
      "Doblada al portugués",
      "Doblada al español"
    ];
 
    languages.forEach(language => {
      expect(screen.getByText(language)).toBeInTheDocument();
    });
  });
 
  it('calls handleLanguageSelect when a language is clicked', () => {
    renderComponent();
 
    const languageButton = screen.getByText('Subtitulada al español');
    fireEvent.click(languageButton);
 
    expect(mockNavigate).toHaveBeenCalledWith('/settings/profile-settings', {
      state: { selectedLanguage: 'Subtitulada al español', type: 'onDemand', backFocus: undefined },
      replace: true
    });
    // Check localStorage
    expect(localStorage.setItem).toHaveBeenCalledWith('vodContentLanguage', 'S-ES');
  });
 
  it('sets localStorage for onDemand content', () => {
    renderComponent();
 
    const languageButton = screen.getByText('Doblada al portugués');
    fireEvent.click(languageButton);
 
    expect(localStorage.setItem).toHaveBeenCalledWith('vodContentLanguage', 'D-PT');
  });
 
  it('sets localStorage for live content', () => {
    useLocation.mockReturnValue({ state: { type: 'live', currentLanguage: 'Idioma Original' } });
 
    renderComponent();
 
    const languageButton = screen.getByText('Subtitulada al portugués');
    fireEvent.click(languageButton);
 
    expect(localStorage.setItem).toHaveBeenCalledWith('liveContentLanguage', 'S-PT');
  });
});