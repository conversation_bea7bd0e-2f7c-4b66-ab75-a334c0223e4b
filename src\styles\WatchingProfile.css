.PrfWatchHeaderContainer {
    padding: 50px 50px;
}

.PrfWatchHeader {
    text-align: center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
    margin: 0;
}

.UserProfileContainer {
    display: flex;
    width: 85%;
    height: 400px;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 50px;
}

.UserImage {
    width: 224px;
    height: 224px;
   
}

.UserProfName {
    text-align: center;
    font: normal normal normal 36px/42px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
    text-transform: uppercase;
    /* word-wrap: break-word;
    width: 100%; */
    margin-left: -30px;
    margin-right: -50px
}

.addprofile_name {
    font-size: 28px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
    position: relative;
    top: 14rem;
    right: 16rem;
}

.UserProfileImage {
    text-align: center;
    margin-right: 100px;
    margin-top: 26px;
    /* width: 224px; */
    height: 300px;
}

.UserProfileImage:focus,
.UserProfileImage:active {
    margin-right: 120px;
    /* width: 272px !important; */
    height: 272px !important;
    margin-top: 0px !important;
}

.UserProfileImage:focus>.UserImage,
.UserProfileImage:active>.UserImage,
.UserProfileImage:focus>.lazy-load-image-background,
.UserProfileImage:active>.lazy-load-image-background {
    z-index: 1;
    background: transparent 0% 0% no-repeat;
    border: 3px solid #981C15;
    outline: unset;
    border-radius: 50%;
    padding: 10px;
    width: 272px !important;
    height: 272px !important;
    opacity: 1;
    transform: scale(1);
}

.UserProfileImage:focus>.UserProfName,
.UserProfileImage:active>.UserProfName {
    text-align: center;
    font: normal normal normal 43px/50px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}


.EditWatchProfile {
    display: flex;
    justify-content: center;
    margin-top: 100px;
}

.EditWatchProfBtn {
    width: 500px;
    height: 88px;
    background: #2E303D 0% 0% no-repeat padding-box;
    font: normal normal normal 34px/40px Roboto;
    color: #EEEEEE;
    border-radius: 44px;
    opacity: 1;
    text-align: center;
}

.EditWatchProfBtn:focus,
.EditWatchProfBtn:focus {
    /* box-shadow: 0px 0px 24px #981C15; */
    /* border: 3px solid #981C15; */
    outline: unset;
    background: #981C15 0% 0% no-repeat padding-box;
    opacity:1;
}

.NotificationLayout {
    background: #1A1A1A 0% 0% no-repeat padding-box;
    border: 2px solid #50595E;
    border-radius: 10px 0px 0px 10px;
    opacity: 1;
    width: 0;
    align-items: center;
    display: flex;
    position: absolute;
    white-space: nowrap;
    height: 140px;
    top: 60px;
    right: -4px;
    transition: cubic-bezier(1, 0, 0, 1) ;
    /* transition: 2s cubic-bezier(0, -0.31, 0.99, 1.24); */
    overflow-x: hidden;
}

.NotificationLayout.show {
    width: 750px;
    opacity: 1 !important;
}

.NotificationTextLayout {
    margin-left: 42px;
}

.NotificationText {
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    margin: 13px 0px;
    word-wrap: break-word;
    width: 400px !important;
    text-wrap: wrap;
}
.profile-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4.5rem;
    background: transparent linear-gradient(90deg, #2B2C31F2 0%, #34353BF2 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 20px #00000029;
    opacity: 1;
    margin-top: 5rem;
    display: flex;
    justify-content: space-between;
}

.footer-text {
    width: 57px;
    height: 25px;
    text-align: left;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    font-family: "Roboto";
    font-size: 22px;
    margin-right: 57rem;
}

.profile_backbtn {
    margin-top: 1rem;
    width: 24px;
    height: 24px;
    margin-left: 57rem;
}

