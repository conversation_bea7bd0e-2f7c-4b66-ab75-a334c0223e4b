import { createSlice } from '@reduxjs/toolkit'

export const getVodSeriesSlice = createSlice({
  name: 'vodSeries',
  initialState: {
    seriesData: [],
    isLoading: false,
    error: {},
    seriesCastData: [],
    isSeriesCastLoading: false,
    seriesCastError: {},
    seriesMLTData: [],
    isSeriesMLTLoading: false,
    seriesMLTError: {},
    episodeDetail: '',
    episodeMoreInfo: {}
  },
  reducers: {
    vodSeries: state => {
      state.isLoading = true
    },
    vodSeriesDataSuccess: (state, action) => {
      state.seriesData = action?.payload?.response
      state.isLoading = false
    },
    vodSeriesDataError: (state, action) => {
      state.isLoading = false
      state.error = action.payload?.data?.response
    },

    vodSeriesCast: state => {
      state.isSeriesCastLoading = true
    },
    vodSeriesCastSuccess: (state, action) => {
      state.seriesCastData = action?.payload?.response?.group
      state.isSeriesCastLoading = false
    },
    vodSeriesCastError: (state, action) => {
      state.isSeriesCastLoading = false
      state.seriesCastError = action.payload
    },

    vodSeriesMLT: state => {
      state.isSeriesMLTLoading = true
    },
    vodSeriesMLTSuccess: (state, action) => {
      state.seriesMLTData =
        action?.payload?.data?.groups ?? action?.payload?.response?.groups
      state.isSeriesMLTLoading = false
    },
    vodSeriesMLTError: (state, action) => {
      state.isSeriesMLTLoading = false
      state.seriesMLTError = action.payload
    },
    clearVodSeries: state => {
      state.seriesData = {}
    },
    clearVodSeriesCast: state => {
      state.seriesCastData = {}
    },
    clearVodSeriesMlt: state => {
      state.seriesMLTData = {}
    },
    getEpisodeVariable: (state, { payload }) => {
      state.episodeDetail = payload
    },
    getEpisodeMoreInfo: (state, { payload }) => {
      state.episodeMoreInfo = payload
    }
  }
})

export const {
  vodSeries,
  vodSeriesDataSuccess,
  vodSeriesDataError,
  vodSeriesCast,
  vodSeriesCastSuccess,
  vodSeriesCastError,
  vodSeriesMLT,
  vodSeriesMLTSuccess,
  vodSeriesMLTError,
  clearVodSeries,
  clearVodSeriesCast,
  clearVodSeriesMlt,
  getEpisodeVariable,
  getEpisodeMoreInfo
} = getVodSeriesSlice.actions

export default getVodSeriesSlice.reducer
