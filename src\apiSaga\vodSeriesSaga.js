import { call, takeEvery } from 'redux-saga/effects'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  vodSeriesCastError,
  vodSeriesCastSuccess,
  vodSeriesDataError,
  vodSeriesDataSuccess,
  vodSeriesMLTError,
  vodSeriesMLTSuccess
} from '../store/slices/vodSeriesSlice'
import { URL } from '../utils/environment'

export function* vodSeries(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.VOD_SERIES_URL +
        `user_id=${data?.payload?.userId}&group_id=${
          data?.payload?.id
        }&region=${region}&HKS=${data?.payload?.hks}&is_kids=${
          data?.payload.is_kids == 'false' ? 0 : 1
        }&terminos=0`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(vodSeriesDataSuccess(response))
        },
        onError(error) {
          store.dispatch(vodSeriesDataError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(vodSeriesDataError(error))
  }
}

export function* vodSeriesCast(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.VOD_SERIES_CAST_URL +
        `user_id=${data?.payload?.userId}&group_id=${
          data?.payload?.id
        }&region=${region}&HKS=${data?.payload?.hks}&is_kids=${
          data?.payload.is_kids == 'false' ? 0 : 1
        }&terminos=0`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(vodSeriesCastSuccess(response))
        },
        onError(error) {
          store.dispatch(vodSeriesCastError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(vodSeriesCastError(error))
  }
}

export function* vodSeriesMLT(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.VOD_SERIES_MLT_URL}user_id=${data?.payload?.userId}&group_id=${
        data?.payload?.id
      }&display_only=${data?.payload?.display_only}&region=${region}&HKS=${
        data?.payload?.hks
      }&is_kids=${
        data?.payload?.is_kids == 'false' ? 0 : 1
      }&terminos=0&user_token=${
        data?.payload?.user_token
      }&is_only_provider=true&quantity=50&filterlist=${
        data?.payload?.filterlist
      }`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(vodSeriesMLTSuccess(response))
        },
        onError(error) {
          store.dispatch(vodSeriesMLTError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(vodSeriesMLTError(error))
  }
}

export default function* getVodSeries() {
  yield takeEvery('vodSeries/vodSeries', vodSeries)
  yield takeEvery('vodSeries/vodSeriesCast', vodSeriesCast)
  yield takeEvery('vodSeries/vodSeriesMLT', vodSeriesMLT)
}
