import React from "react";
import { fireEvent, getByText, queryByAttribute, render, screen } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import SubscriptionViewDetails from "./SubscriptionViewDetails";

const mockNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({
    state: mockLocationState
  })
}));

let mockLocationState = {
  data: { id: '12345' },
  pageName: '/my-settings/my-subscriptions',
  returnPage: '/home'
};

const mockLocalStorage = (() => {
  let store = { region: 'peru' };
  return {
    getItem: jest.fn(key => store[key]),
    setItem: jest.fn((key, value) => { store[key] = value; }),
    clear: jest.fn(() => { store = {}; })
  };
})();
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({
      response: {
        highlight: [{ image_highlight: 'test-image.jpg' }],
        groups: [
          { image_base_vertical: 'vertical-image.jpg' },
          { image_base_horizontal: 'horizontal-image1.jpg' },
          { image_base_horizontal: 'horizontal-image2.jpg' },
          { image_base_horizontal: 'horizontal-image3.jpg' }
        ]
      }
    })
  })
);

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
);

export const renderWithState = (ui, store = mockStore(initialState)) => {
  return render(
    <Provider store={store}>
      <Router>{ui}</Router>
    </Provider>
  );
};

const mockerrorresponse = {
  "status": "1"
};

const mocksuccessresponse = {
  bannerUrl: "https://clarovideocdn8.clarovideo.net/pregeneracion/cms//banner_hbo.png?**********",
  logo: "https://clarovideocdn0.clarovideo.net/pregeneracion/cms/hbo_logo.png?**********",
  family: "hbo",
  periodicity: "month",
  price: "29.90",
  currency: "S/",
  styles: "style_hbo",
  workflowStart: "/services/path",
  taxLabel: "Incl. IGV",
  subscribeButton: "SUSCRIBÍTE AHORA",
  viewButton: "¿Qué incluye HBO?",
  producttype: "HBO",
  offertype: "subscrition"
};

const mockIsLoggedinsuccessresponse = {
  response: {
    accepted_terms: 1,
    admin: true,
    city: null,
    counterValidEmail: 0,
    country_code: "PE",
    email: "<EMAIL>",
    firstname: "Admin",
    gamification_id: "63ecf870de7e873ec21ca6c3",
    hasSavedPayway: 1,
    hasUserSusc: 1,
    is_kids: "false",
    is_user_logged_in: 1,
    lastname: "testing",
    lasttouch: { favorited: "657fe455ec4fd", profile: "657fe44c2a6be", purchased: "657fd244a1fab" },
    parent_id: "78516744",
    password_recovered: false,
    paymentMethods: { hubgate: true },
    region: "peru",
    session_parametername: "HKS",
    session_servername: "************",
    session_stringvalue: "ZTEATV412001224226580292a69e33",
    session_userhash: "NzgjE4ZDlhMw==",
    socialNetworks: [{ id_usuario: "78516744", id_usuario_social: "63ecf870de7e873ec21ca6c3", redsocial: "IMUSICA" }],
    socialdata: null,
    subregion: null,
    subscriptions: { TV_EN_VIVO: true, MGM: true, AMCO: true },
    superhighlight: ["no_suscripto_nbatv", "no_suscripto_rtveplay", "susc", "no_suscripto_fox_sports", "no_suscripto_hbo"],
    user_id: "78516744",
    user_session: "kdh2zFgZ9Jeg",
    user_token: "eyJ0eXAiOOk",
    username: "<EMAIL>",
    validEmail: true
  },
  status: "0"
};

const mockVodSeriesCastRedux = {
  seriesCastData: {
    common: {
      extendedcommon: {
        format: {
          types: "subscription"
        }
      }
    }
  }
};

const mockAppMetaData = {
  translations: JSON.stringify({
    language: {
      peru: {
        'BotonShortcut_TextoTitulo': 'Atrás',
        'month': 'mes',
        'Transaccionales_ProbarAddon_TextoDiagonal': '/',
        'checkout_access_option_button_subscription': 'SUSCRÍBETE',
        'Transaccionales_Paquetes_TextoPromocion1_HBO': 'Promoción HBO 1',
        'Transaccionales_Paquetes_TextoPromocion2_HBO': 'Promoción HBO 2',
        'Transaccionales_Paquetes_TextoPromocion3_HBO': 'Promoción HBO 3',
        'Transaccionales_Paquetes_TextoPromocion4_HBO': 'Promoción HBO 4',
        'Transaccionales_Paquetes_TextoPromocion5_HBO': 'Promoción HBO 5',
        'Transaccionales_ProbarAddon_TextoPromocion1_HBO': 'Prueba HBO 1',
        'Transaccionales_ProbarAddon_TextoPromocion2_HBO': 'Prueba HBO 2',
        'Transaccionales_ProbarAddon_TextoPromocion3_HBO': 'Prueba HBO 3',
        'Transaccionales_ProbarAddon_TextoOferta1_HBO': 'Disfruta',
        'Transaccionales_ProbarAddon_TextoOferta2_HBO': ' la mejor programación',
        'Transaccionales_Paquetes_TextoComplementario1_HBO': 'Texto complementario 1',
        'Transaccionales_Paquetes_TextoComplementario2_HBO': 'Texto complementario 2',
        'Transaccionales_ProbarAddon_TextoComplementario1_HBO': 'Addon complementario 1',
        'Transaccionales_ProbarAddon_TextoComplementario2_HBO': 'Addon complementario 2',
        'Transaccionales_ProbarAddon_TextoComplementario3_HBO': 'Addon complementario 3'
      }
    }
  })
};

const mockCMSViewSub = {
  response: {
    modules: {
      module: [
        {
          components: {
            component: [
              {
                type: 'Highlight',
                properties: {
                  url: '/api/highlight/123'
                }
              },
              {
                type: 'Carrouselvertical',
                properties: {
                  url: '/api/carousel/123'
                }
              }
            ]
          }
        }
      ]
    }
  }
};

const mockImagesResponse = {
  'Transaccionales_ProbarAddon_ImagenLogoCanal_hbo': 'hbo_logo.png'
};

describe('SubscriptionViewDetails Component Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
    global.tizen = undefined;
  });

  test('should render with basic data', () => {
    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
  });

  test('should handle subscribe button click and navigate to subscribe page', () => {
    mockLocationState = {
      data: { id: '12345' },
      pageName: '/my-settings/my-subscriptions',
      returnPage: '/home'
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const getById = queryByAttribute.bind(null, 'id');
    const subscribeButton = getById(container, 'subscriptionDetailPage-subs-btn');
    fireEvent.click(subscribeButton);
    expect(mockNavigate).toHaveBeenCalled();
  });

  test('should handle subscribe with watchFree true and vodSeriesCastRedux', () => {
    mockLocationState = {
      dataId: { id: '12345' },
      pageName: '/series',
      seriesEpisodeData: { id: '67890' }
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: true
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: mockVodSeriesCastRedux
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const getById = queryByAttribute.bind(null, 'id');
    const subscribeButton = getById(container, 'subscriptionDetailPage-subs-btn');
    
    fireEvent.click(subscribeButton);
    expect(mockNavigate).toHaveBeenCalledWith('/EPconfirmation', expect.any(Object));
  });

  test('should handle subscribe with multiSubscription pageName', () => {
    mockLocationState = {
      data: { id: '12345' },
      pageName: '/multiSubscription',
      sendIndex: 2,
      returnPage: '/home',
      inputValue: 'search-term',
      subscribeInfoData: { id: 'info-123' }
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const getById = queryByAttribute.bind(null, 'id');
    const subscribeButton = getById(container, 'subscriptionDetailPage-subs-btn');
    
    fireEvent.click(subscribeButton);
    expect(mockNavigate).toHaveBeenCalledWith(
      '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
      expect.any(Object)
    );
  });

  test('should handle subscribe with premiumSubscription pageName', () => {
    mockLocationState = {
      data: { id: '12345' },
      pageName: '/premiumSubscription',
      returnPage: '/home',
      inputValue: 'search-term',
      subscribeInfoData: { id: 'info-123' },
      groupId: 'group-123'
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const getById = queryByAttribute.bind(null, 'id');
    const subscribeButton = getById(container, 'subscriptionDetailPage-subs-btn');
    
    fireEvent.click(subscribeButton);
    expect(mockNavigate).toHaveBeenCalledWith(
      '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
      expect.any(Object)
    );
  });

  test('should handle Samsung key press (yellow button)', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 403 })
      }
    };

    mockLocationState = {
      data: { id: '12345' },
      pageName: '/premiumSubscription',
      returnPage: '/home',
      subscribeInfoData: { id: 'info-123' },
      groupId: 'group-123'
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    fireEvent.keyUp(document, { keyCode: 403 });
    
    expect(global.tizen.tvinputdevice.registerKeyBatch).toHaveBeenCalledWith(['ColorF2Yellow']);
    expect(mockNavigate).toHaveBeenCalledWith('/premiumSubscription', expect.any(Object));
  });

  test('should handle Samsung key press (back button)', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 10009 })
      }
    };

    mockLocationState = {
      fromDetailsPage: true,
      pageName: '/series',
      seriesEpisodeData: { id: '67890' }
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: { common: { id: 'series-123' } }
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    fireEvent.keyUp(document, { keyCode: 10009 });
    expect(mockNavigate).toHaveBeenCalledWith('/series', expect.any(Object));
  });

  test('should handle Samsung key press with multiSubscription', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 403 })
      }
    };

    mockLocationState = {
      data: { id: '12345' },
      pageName: '/multiSubscription',
      sendIndex: 2,
      returnPage: '/home'
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    fireEvent.keyUp(document, { keyCode: 403 });
    expect(mockNavigate).toHaveBeenCalledWith('/multiSubscription', expect.any(Object));
  });

  test('should handle LG key press (back button)', () => {
    mockLocationState = {
      vodData: { id: '12345' },
      pageName: '/movies',
      vodMoviesData: { id: '67890' },
      page: 'details'
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const backButton = queryByAttribute.bind(null, 'id')(container, 'subscriptionDetailPageBack');
    fireEvent.click(backButton);
    expect(mockNavigate).toHaveBeenCalledWith('/movies', expect.any(Object));
  });

  test('should handle LG key press with keyboard event', () => {
    mockLocationState = {
      pageName: '/home',
      focusIndex: 3,
      data: { id: '12345' }
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    fireEvent.keyUp(document, { keyCode: 461 });
    
    expect(mockNavigate).toHaveBeenCalledWith('/home', expect.any(Object));
  });

  test('should handle LG key press event for returnPage of search', () => {
    mockLocationState = {
      pageName: '/multiSubscription',
      returnPage: 'search',
      data: { id: '12345' }
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    fireEvent.keyUp(document, { keyCode: 461 });
    expect(mockNavigate).toHaveBeenCalledWith('/multiSubscription', expect.any(Object));
  });

  test('should test fetch API calls', async () => {
    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    await new Promise(resolve => setTimeout(resolve, 0));
    expect(global.fetch).toHaveBeenCalledTimes(2);
    expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('/api/highlight/123'));
    expect(global.fetch).toHaveBeenCalledWith(expect.stringContaining('/api/carousel/123'));
  });

  test('should handle long text in translation', () => {
    const longTextAppMetaData = {
      translations: JSON.stringify({
        language: {
          peru: {
            'BotonShortcut_TextoTitulo': 'This is a very long text that should be truncated because it exceeds the length limit that was set',
            'month': 'mes',
            'Transaccionales_ProbarAddon_TextoDiagonal': '/',
            'checkout_access_option_button_subscription': 'SUSCRÍBETE',
            'Transaccionales_Paquetes_TextoPromocion1_HBO': 'Promoción HBO 1',
            'Transaccionales_Paquetes_TextoPromocion2_HBO': 'Promoción HBO 2',
            'Transaccionales_Paquetes_TextoPromocion3_HBO': 'Promoción HBO 3',
            'Transaccionales_Paquetes_TextoPromocion4_HBO': 'Promoción HBO 4',
            'Transaccionales_Paquetes_TextoPromocion5_HBO': 'Promoción HBO 5'
          }
        }
      })
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: longTextAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const backText = container.querySelector('.back-text');
    expect(backText).not.toBeNull();
    expect(backText.textContent).toBe('This is a ...');
  });

  test('should handle watch free with free format types', () => {
    mockLocationState = {
      dataId: { id: '12345' },
      pageName: '/series',
      seriesEpisodeData: { id: '67890' }
    };

    const freeFormatVodSeriesCastRedux = {
      seriesCastData: {
        common: {
          extendedcommon: {
            format: {
              types: 'free,download'
            }
          }
        }
      }
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: true
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: freeFormatVodSeriesCastRedux
    });

    const { container } = renderWithState(<SubscriptionViewDetails />, store);
    const getById = queryByAttribute.bind(null, 'id');
    const subscribeButton = getById(container, 'subscriptionDetailPage-subs-btn');
    
    fireEvent.click(subscribeButton);
    
    expect(mockNavigate).toHaveBeenCalledWith(
      '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
      expect.any(Object)
    );
  });

  test('should handle default case in navigation for back button', () => {
    mockLocationState = {
      data: { id: '12345' },
      pageName: '/other-page',
      groupId: 'group-123'
    };

    const store = mockStore({
      settingsReducer: {
        viewSubscribeData: mocksuccessresponse,
        cmsViewSub: mockCMSViewSub
      },
      login: {
        isLoggedIn: mockIsLoggedinsuccessresponse,
        watchFreestate: false
      },
      initialReducer: {
        appMetaData: mockAppMetaData
      },
      Images: {
        imageresponse: mockImagesResponse
      },
      getVodSeries: {
        seriesCastData: null
      }
    });

    renderWithState(<SubscriptionViewDetails />, store);
    
    fireEvent.keyUp(document, { keyCode: 8 });
    
    expect(mockNavigate).toHaveBeenCalledWith('/other-page', expect.any(Object));
  });
});