/* Imported Font */
@import url('http://fonts.googleapis.com/css?family=Roboto:700,400,500,300');

/* Reminder Alert Container */
.show-container {
  position: absolute;
  flex-direction: row;
  display: flex;
  background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.71) 42.99%, rgba(0,0,0,0.96) 100%);
  opacity: 0.7;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0;
  bottom: 0;
  width: 1920px;
  height: 1080px;
}

.reminder-alert {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 80px;
    right: 0;
    z-index: 9999;
}

/* Reminder Second Alert Container */
.reminder-second-alert {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #2e303d;
    border-radius: 18px;
    padding: 28px;
}

/* Alert Text Container */
.alert-txt {
    display: flex;
    align-items: center;
}

/* Clock Icon */
.clock-image {
    width: 36px;
    height: 36px;
}

/* First Text - Program Name and Channel Number */
.first-text {
    color: #ffffff;
    font-family: Roboto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .channel-title {
        font-size: 36px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 36px;
        margin-left: 22px;
    }

    .channel-number {
        font-size: 32px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 32px;
        margin-left: 16px;
    }

    .line-mark {
        height: 36px;
        width: 10px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 36px;
        margin-left: 16px;
    }
}

/* Channel Logo */
.channel-logo {
    margin-left: 32px;
    width: 62px;
    height: 26.76px;
}

/* Second Text - Program Start and End Time */
.second-text {
    color: #ffffff;
    font-family: Roboto;
    font-size: 32px;
    font-weight: 500;
    letter-spacing: 0.5px;
    line-height: 32px;
    margin-left: 32px;
}

/* Sintonizar and Cerrar Buttons */
.alert-txt1 {
    margin-left: 16px;
    background-color: #2e303d;
    width: 265px;
    border-radius: 8px;
    height: 41px;
    cursor: pointer;
    justify-content: center;
    display: flex;
    align-items: center;

    &:focus {
        transform: scale(1.2);
        margin-left: 39px;
        margin-right: 22px;
        width: 265px !important;
        height: 48px !important;
    }

    .schedule-reminder-red-text {
        text-align: center;
        color: #ffffff;
        font-family: Roboto;
        font-size: 29px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 31px;
        padding-left: 22px;
    }
}

.alert-txt2 {
    margin-left: 16px;
    background-color: #2e303d;
    width: 265px;
    border-radius: 8px;
    height: 41px;
    cursor: pointer;
    justify-content: center;
    display: flex;
    align-items: center;

    &:focus {
        transform: scale(1.2);
        width: 265px !important;
        height: 48px !important;
        margin-left: 46px;
    }

    .schedule-reminder-green-text {
        text-align: center;
        color: #ffffff;
        font-family: Roboto;
        font-size: 29px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 31px;
        padding-left: 22px;
    }
}



/* Red and Green Bullets */
.bullet-red,
.bullet-green {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 5px;
}

.bullet-red {
    background-color: red;
}

.bullet-green {
    background-color: rgb(14, 248, 14);
}