import vodSeriesReducer, {
  vodSeries,
  vodSeriesDataSuccess,
  vodSeriesDataError,
  vodSeriesCast,
  vodSeriesCastSuccess,
  vodSeriesCastError,
  vodSeriesMLT,
  vodSeriesMLTSuccess,
  vodSeriesMLTError,
  clearVodSeries,
  clearVodSeriesCast,
  clearVodSeriesMlt,
  getEpisodeVariable
} from './vodSeriesSlice'

const initialState = {
  seriesData: [],
  isLoading: false,
  error: {},
  seriesCastData: [],
  isSeriesCastLoading: false,
  seriesCastError: {},
  seriesMLTData: [],
  isSeriesMLTLoading: false,
  seriesMLTError: {},
  episodeDetail: '',
  episodeMoreInfo: {}
}
const seriesmockdata = [
  {
    id: '919562'
  }
]
describe('vodSeriesSlice reducer', () => {
  it('should return the initial state', () => {
    expect(vodSeriesReducer(undefined, {})).toEqual(initialState)
  })

  it('should handle vodSeries action', () => {
    expect(vodSeriesReducer(initialState, vodSeries())).toEqual({
      ...initialState,
      isLoading: true
    })
  })

  it('should handle vodSeriesDataSuccess action', () => {
    const action = {
      type: vodSeriesDataSuccess.type,
      payload: {
        response: [
          {
            id: '919562'
          }
        ]
      }
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      seriesData: action.payload.response,
      isLoading: false
    })
  })

  it('should handle vodSeriesDataError action', () => {
    const action = {
      type: vodSeriesDataError.type,
      payload: { data: { response: 'error' } }
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      isLoading: false,
      error: action.payload.data.response
    })
  })

  it('should handle vodSeriesCast action', () => {
    expect(vodSeriesReducer(initialState, vodSeriesCast())).toEqual({
      ...initialState,
      isSeriesCastLoading: true
    })
  })

  it('should handle vodSeriesCastSuccess action', () => {
    const action = {
      type: vodSeriesCastSuccess.type,
      payload: {
        response: {
          group: [
            {
              id: '919562'
            }
          ]
        }
      }
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      seriesCastData: action.payload.response.group,
      isSeriesCastLoading: false
    })
  })

  it('should handle vodSeriesCastError action', () => {
    const action = {
      type: vodSeriesCastError.type,
      payload: 'castError'
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      isSeriesCastLoading: false,
      seriesCastError: action.payload
    })
  })

  it('should handle vodSeriesMLT action', () => {
    expect(vodSeriesReducer(initialState, vodSeriesMLT())).toEqual({
      ...initialState,
      isSeriesMLTLoading: true
    })
  })

  it('should handle vodSeriesMLTSuccess action', () => {
    const action = {
      type: vodSeriesMLTSuccess.type,
      payload: {
        response: {
          groups: [
            {
              id: '919562'
            }
          ]
        }
      }
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      seriesMLTData: action.payload.response.groups,
      isSeriesMLTLoading: false
    })
  })

  it('should handle vodSeriesMLTError action', () => {
    const action = {
      type: vodSeriesMLTError.type,
      payload: 'mltError'
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      isSeriesMLTLoading: false,
      seriesMLTError: action.payload
    })
  })

  it('should handle clearVodSeries action', () => {
    expect(
      vodSeriesReducer(
        {
          ...initialState,
          seriesData: seriesmockdata
        },
        clearVodSeries()
      )
    ).toEqual({
      ...initialState,
      seriesData: {}
    })
  })

  it('should handle clearVodSeriesCast action', () => {
    expect(
      vodSeriesReducer(
        {
          ...initialState,
          seriesCastData: seriesmockdata
        },
        clearVodSeriesCast()
      )
    ).toEqual({
      ...initialState,
      seriesCastData: {}
    })
  })

  it('should handle clearVodSeriesMlt action', () => {
    expect(vodSeriesReducer({}, clearVodSeriesMlt())).toEqual({
      seriesMLTData: {}
    })
  })

  it('should handle getEpisodeVariable action', () => {
    const action = {
      type: getEpisodeVariable.type,
      payload: '1'
    }
    expect(vodSeriesReducer(initialState, action)).toEqual({
      ...initialState,
      episodeDetail: action.payload
    })
  })
})
