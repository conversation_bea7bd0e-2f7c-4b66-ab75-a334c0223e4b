$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;



.premium-carousel-Container {
  height: 300px;
  margin-bottom: 40px;

  .premium-carousel-Title {
    font-size: 32px;
    font-weight: normal;
    text-align: left;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin-left: 72px;
    font-family: $font-family-roboto;
  }

  .premium-text{
    margin: 0px;
  }

  .premium-carousel {
    margin-left: 30px;
    .premium-carousel-wrapper {
      display: flex;
      flex-direction: row;
      overflow: scroll;
      scroll-snap-type: x mandatory;
      margin: 0.5rem;
      padding: 0.5rem;
      .toonical-block {
        display: flex;
        flex-direction: column;
        border-radius: 50%;
        margin: 0rem 1.5rem;
        position: relative;
        width: 299px;
        height: 298px;
        padding:6px;
        border: 4px solid transparent !important;
        
        .rail-image-Toon {
          display: flex;
          width: 298px;
        }
      }
      .toonical-block:focus {
        transform: scale(1.02);
          transition: transform 0.2s ease-in-out;
        scroll-snap-align: end;
        border: 4px solid #fff !important;
      }
      
      .premium-carousel-image {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 20px;
        margin-right: 20px;
        opacity: 0.8;
        scroll-snap-type: x mandatory;
        padding: 4px 4px 4px 4px;
      }


      .premium-carousel-image:focus {
        opacity: 1;
        border-radius: 10px;
        transform: scale(1.03);
        scroll-snap-align: end;
        border: 4px solid #fff !important;
      }

      .premiumImage {
        width: 412px;
        height: 232px;
        display: flex;
      }
    }
  }
}



::-webkit-scrollbar {
  display: none;
}

::-webkit-scrollbar {
  display: none;
}

//Needed for future reference
/* .premium-channel-logo { 
  display: flex;
  justify-content: center;
  width: 1920px;
  height: 52px;
  margin-top: 5px;
  margin-bottom: 13px;
}
 */

 .standardChannelLogo{
  display: block;
  width:100%;
  margin-top:16px
 }


.premium-channel-logo {
  display: flex;
  justify-content: center;
  background-size: cover;
  background-position: center;
  margin-bottom: 32px;
  width: 1920px;
  height: 70px;
  align-items: center;
}

.premium-channel-logo2 {
  height: 65px;
  display: flex;
  justify-content: center;
  width: 1920px;
  align-items: center;
  margin-bottom: 15px;
}

.premium-text {
  text-transform: lowercase;
}

.premium-text::first-letter {
  text-transform: capitalize;
}