import React, { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { store } from '../../store/sagaStore'
import { getTermsAndCondition } from '../../store/slices/login'
import { getProfileReadData } from '../../store/slices/ProfileSlice'
import '../loginregister/LoginTermsAndCondition.scss'
import { pushLoginPrivacyEvent } from '../../GoogleAnalytics'

const LoginTermsAndCondition = () => {
    const { state } = useLocation()
    const navigate = useNavigate()
    const [checkboxerrMsg, setCheckboxerrMsg] = useState('')
    const [isChecked, setIsChecked] = useState(state?.checkbox)
    const region = localStorage.getItem('region')
    const token = localStorage.getItem('token')
    const startheaderinfoData = useSelector(state => state?.initialReducer?.startHeaderInfo?.response)
    const logintermsandcondition = useSelector(state => state?.login?.termsAndConditionSuccess)
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const apilanguage = translations?.language?.[region]
    const gamificationid = logintermsandcondition?.response?.userData?.gamification_id
    const hks = logintermsandcondition?.response?.userData?.session_stringvalue
    const userid = logintermsandcondition?.response?.userData?.parent_id
    const user_hash = logintermsandcondition?.response?.userData?.session_userhash
    const lasttouch = logintermsandcondition?.response?.userData?.lasttouch?.profile

    useEffect(() => {
        SpatialNavigation.focus()
        state?.backfocus == 'privacypolicy' && document?.getElementById('privacyPolicy')?.focus()
        if (logintermsandcondition?.status == '0' && gamificationid) {
            const payload = {
                hks: hks,
                userid: userid,
                token: token,
                gamificationid: gamificationid,
                lasttouch: lasttouch
            }
            store.dispatch(getProfileReadData(payload))
            localStorage.setItem('gamificationid', gamificationid)
            localStorage.setItem('loginId', userid)
            localStorage.setItem('lasttouch', lasttouch)
            localStorage.setItem('user_hash', user_hash)
            localStorage.setItem('hks', hks)
            navigate('/prewelcome', { 
                state: { 
                    confirmscreen: state?.confirmscreen, 
                    seriesEpisodeData: state?.seriesEpisodeData, 
                    pageName: state?.pageName,
                    fromDetailsPage: state?.fromDetailsPage 
                } 
            })
        }
    }, [logintermsandcondition])

    const handleTranslationchange = useCallback(keyname => {
        return apilanguage?.[keyname] ? apilanguage?.[keyname] : [keyname]
    }, [apilanguage])

    const focusFunction = () => {
        !isChecked && setCheckboxerrMsg(handleTranslationchange('password_tooltip_checkTermsConditions_label_validation'))
    }
    const blurFunction = () => {
        setCheckboxerrMsg('')
    }
    const handleCheckboxChange = e => {
        e.preventDefault()
        setCheckboxerrMsg('')
        setIsChecked(!isChecked)
    }
    const termsAndConditionNavigate = e => {
      e.preventDefault()
      //GA for login event
      pushLoginPrivacyEvent(
        'conocer términos y condiciones',
        handleTranslationchange(
          'noticePrivacy_popupDescription_label'
        )?.toLowerCase()
      )
      navigate('/Terms-and-Conditons', {
        state: {
          page: 'logintermsandcondition',
          checkbox: isChecked,
          seriesEpisodeData: state?.seriesEpisodeData,
          pageName: state?.pageName,
          fromDetailsPage: state?.fromDetailsPage
        }
      })
    }
    const privacyPolicyNavigate = e => {
      e.preventDefault()
      //GA for login event
      pushLoginPrivacyEvent(
        'conocer políticas de privacidad',
        handleTranslationchange(
          'noticePrivacy_popupDescription_label'
        )?.toLowerCase()
      )
      navigate('/privacy-and-policy', {
        state: {
          page: 'privacypolicy',
          checkbox: isChecked,
          seriesEpisodeData: state?.seriesEpisodeData,
          pageName: state?.pageName,
          fromDetailsPage: state?.fromDetailsPage
        }
      })
    }
    const profilePageNavigate = e => {
      //GA for login
      pushLoginPrivacyEvent(
        'continuar',
        handleTranslationchange(
          'noticePrivacy_popupDescription_label'
        )?.toLowerCase()
      )
      store.dispatch(
        getTermsAndCondition({
          password: state?.password,
          username: state?.username,
          HKS: startheaderinfoData?.session_stringvalue,
          confirmscreen: state?.confirmscreen
        })
      )
    }

    const keypresshandler = event => {
        event.preventDefault()
        if (event?.keyCode === 10009 || event?.keyCode === 461 || event?.keyCode === 16) {

            navigate('/signin',{
                state:{
                    password:state?.password,
                    username:state?.username, 
                    pageName: state?.pageName,
                    seriesEpisodeData: state?.seriesEpisodeData
                }
            })
        }
    }

    useEffect(() => {
        document.addEventListener('keyup', keypresshandler)
        return () => {
            document.removeEventListener('keyup', keypresshandler)
        }
    }, [keypresshandler])

    return (
        <div className="login-terms-and-condition">
            <img
                src={'images/claro-video-logo.png'}
                className="terms-and-condition-logo"
                alt="logo"
            />
            <p className="terms-and-condition-title">
                {handleTranslationchange('noticePrivacy_popupTitle_label')}
            </p>
            <p className="login-terms-and-condition-title">
                {handleTranslationchange('noticePrivacy_popupDescription_label')}

            </p>
            <div className="radio-box">
                <button
                    id="checkbox"
                    className="checkbox-containerreg focusable"
                    onClick={handleCheckboxChange}
                    onFocus={() => focusFunction()}
                    onBlur={() => blurFunction()}
                >
                    <label className="checkbox-subcontainereg">
                        <input
                            className="checkbox-focus"
                            type="checkbox"
                            checked={isChecked}
                        />
                        <span className="checkmarkreg"></span>
                    </label>
                    <div className="error-box">
                        <div
                            id={`exampleInputEmailMsg`}
                            className={`${checkboxerrMsg ? 'checkbox-error-msg' : 'invisible'}`}>
                            <span>{[checkboxerrMsg]}</span>
                        </div>
                    </div>
                    <p className="checkbox_title">
                        {handleTranslationchange('noticePrivacy_popupValidate_label')}
                    </p>
                </button>
            </div>
            <div className="main-buttons">
                <button
                    autoFocus
                    onClick={(e) => termsAndConditionNavigate(e)}
                    id="termsAndCondition"
                    className="terms-and-condition-button focusable"
                >
                    {handleTranslationchange('noticePrivacy_popupTermsConditions_option_button')}

                </button>
                <button
                    onClick={(e) => privacyPolicyNavigate(e)}
                    id="privacyPolicy"
                    className="privacy-policy-button focusable"
                >
                    {handleTranslationchange('noticePrivacy_popupPrivacyPolicy_option_button')}
                </button>
                <button
                    id="confirm"
                    className="login-confirm-button focusable"
                    onClick={() => profilePageNavigate()}
                    disabled={!isChecked}
                >
                    {handleTranslationchange('noticePrivacy_popupContinue_option_button')}
                </button>
            </div>
        </div>
    )
}

export default LoginTermsAndCondition