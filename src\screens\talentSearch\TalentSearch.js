import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import {
  setShowTalentModule,
  getProgressbarBookmark,
  removeReturnFocusById
} from '../../store/slices/SearchSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './../VodMovies/VodMovies.scss'
import './../talentSearch/TalentSearch.scss'
import { RailsSkeletonLoading } from '../SkeletonScreenLoading/SkeletonScreenloading'
import ProgressBar from '../Progressbar/Progressbar'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const TalentSearch = props => {
  const [currentButtonFocus, setCurrentButtonFocus] = useState(`mlts-0`)
  const showTalentModule = useSelector(state => state?.search?.showTalentModule)
  const showReturnFocus = useSelector(state => state?.search?.setReturnFocus)
  const setVodReturnFocus = useSelector(
    state => state?.search?.setVodReturnFocus
  )

  const dispatch = useDispatch()
  const navigate = useNavigate()
  const state = useLocation()
  const smallprogress = true

  const userDeatilResp = useSelector(
    state => state?.login?.isLoggedIn?.response
  )
  const talentData = useSelector(state => state?.search?.talentSearch)

  let talentMapData = props?.talentSearchData ?? talentData?.data

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      dispatch(setShowTalentModule(false))
      if (props?.keyParam == 'search') {
        dispatch(setShowTalentModule(false))
        props?.setSearchValue(props?.searchValue)
        props?.setCurrentButtonFocus(showReturnFocus)
      } else if (
        props?.keyParam == 'vodMovies' ||
        props?.keyParam == 'vodSeries'
      ) {
        props?.setCurrentButtonFocus(setVodReturnFocus)
        dispatch(setShowTalentModule(false))
        talentMapData = null
      } else if (props?.keyParam == 'epmoreInfo') {
        props?.setCurrentButtonFocus(setVodReturnFocus)
        dispatch(setShowTalentModule(false))
      }
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
) {
      dispatch(setShowTalentModule(false))
      if (props?.keyParam == 'search') {
        dispatch(setShowTalentModule(false))
        props?.setSearchValue(props?.searchValue)
        props?.setCurrentButtonFocus(showReturnFocus)
      } else if (
        props?.keyParam == 'vodMovies' ||
        props?.keyParam == 'vodSeries'
      ) {
        props?.setCurrentButtonFocus(setVodReturnFocus)
        dispatch(setShowTalentModule(false))
        talentMapData = null
      } else if (props?.keyParam == 'epmoreInfo') {
        props?.setCurrentButtonFocus(setVodReturnFocus)
        dispatch(setShowTalentModule(false))
      }
    }
  }

  useEffect(() => {
    let navtag = document.getElementById('nav-bar-focus')
    navtag && navtag.setAttribute('style', 'display:none')
    return () => {
      props?.keyParam == 'search' &&
        navtag &&
        navtag.setAttribute('style', 'display:block')
    }
  }, [showTalentModule])

  useEffect(() => {
    let group_id = {}
    let searchvalueId = []
    talentMapData?.length > 0 &&
      talentMapData?.map(item => {
        if (item?.type == 'S' || item?.type == 'P') {
          group_id[item?.id] = { format_types: item.format_types }
        }
        searchvalueId?.push(item.id)
      })
    if (talentMapData?.length != 0)
      dispatch(
        getProgressbarBookmark({
          group_id: searchvalueId,
          user_hash: userDeatilResp?.session_userhash,
          filterlist: props?.filterlist
        })
      )
  }, [talentMapData])

  const progressbarContentData = useSelector(
    state => state?.search?.progressbarContent
  )

  const handleBack = () => {
    dispatch(setShowTalentModule(false))
    props?.keyParam == 'search' && props?.setSearchValue(props?.searchValue)
    if (props?.keyParam == 'vodMovies' || props?.keyParam == 'vodSeries') {
      props?.setCurrentButtonFocus(setVodReturnFocus)
      dispatch(setShowTalentModule(false))
    } else if (props?.keyParam == 'epmoreInfo') {
      props?.setCurrentButtonFocus(setVodReturnFocus)
      dispatch(setShowTalentModule(false))
    } else if (props?.keyParam == 'search') {
      props?.setCurrentButtonFocus(showReturnFocus)
      dispatch(setShowTalentModule(false))
    }
  }

  const handleTalentPlayback = selectedEvent => {
    if (selectedEvent?.is_series || selectedEvent?.episode_number) {
      state?.pathname == '/search'
        ? navigate('/series', {
            state: {
              data: selectedEvent,
              showControls: 'vod',
              flag: true,
              talentData: props?.talentSearchData,
              talentName: props?.talentSearchfield,
              returnPage: 'search'
            },
            replace: true
          })
        : navigate('/series', {
            state: { data: selectedEvent, showControls: 'vod', flag: true },
            replace: true
          })
    } else if (
      selectedEvent?.is_series == false ||
      !selectedEvent?.season_number
    ) {
      state?.pathname == '/search'
        ? navigate('/movies', {
            state: {
              vodData: selectedEvent,
              showControls: 'vod',
              flag: true,
              talentData: props?.talentSearchData,
              talentName: props?.talentSearchfield,
              returnPage: 'search'
            }
          })
        : navigate('/movies', {
            state: { vodData: selectedEvent, showControls: 'vod', flag: true }
          })
    }
  }

  const goToMoviesSeries = item => {
    dispatch(removeReturnFocusById())
    if (
      progressbarContentData?.map(eventData => eventData?.id == item?.id)?.[0]
    ) {
      progressbarContentData?.map(
        eventData =>
          eventData?.id == item?.id && handleTalentPlayback(eventData)
      )
    } else {
      handleTalentPlayback(item)
    }
  }

  return (
    <div className="talent-search-layout">
      <div className="regresser-box">
        <button
          data-testid="playbutton"
          id="button-back-id"
          className="talent-warning-backscreen focusable"
          onFocus={() => setCurrentButtonFocus('regresser')}
          onClick={handleBack}
          data-sn-down="#vodMoviesRecommend-0"
        >
          <LazyLoadImage
            className="talent-back-img-icon"
            src={'images/Vcard_Icons/yellowcircle_small.png'}
            placeholderSrc={'images/Vcard_Icons/yellowcircle_small.png'}
          />
          <LazyLoadImage
            className="talent-back-img-icon"
            src={'images/Vcard_Icons/icon_backpage.png'}
            placeholderSrc={'images/Vcard_Icons/icon_backpage.png'}
          />
          <span className="talent-back-button-regresar-title">REGRESAR</span>
        </button>
      </div>
      <span className="talent-search-title">
        {props?.talentSearchfield || talentData?.name}
      </span>
      <div className="talent-search-container">
        {talentMapData && talentMapData ? (
          <div className="talent-search-wrapper">
            {talentMapData?.map((item, index) => (
              <button
                data-testid="talentTest"
                className="talent-search-block focusable"
                key={index}
                id={`vodMoviesRecommend-${index}`}
                onClick={() => goToMoviesSeries(item)}
                onFocus={() => {
                  setCurrentButtonFocus(`mlts-${index}`)
                  document
                    .getElementById(`vodMoviesRecommend-${index}`)
                    .scrollIntoView({
                      behavior: 'smooth',
                      inline: 'center',
                      block: 'center'
                    })
                }}
                autoFocus={index === 0}
              >
                {item?.image_small !== '' || item?.image_small !== undefined ? (
                  <LazyLoadImage
                    src={
                      item?.image_small
                        ? item?.image_small
                        : 'images/landscape_card.png'
                    }
                    loading="lazy"
                    alt="PlaceHolder"
                    className="talent-search-image"
                    key={index}
                    id={`talentfocus${index}`}
                    placeholderSrc={'images/landscape_card.png'}
                  />
                ) : (
                  <LazyLoadImage
                    src="images/landscape_card.png"
                    loading="lazy"
                    alt="PlaceHolder"
                    className="talent-search-image"
                    placeholderSrc={'images/landscape_card.png'}
                  />
                )}
                <div className="inline-progressbar-layouts">
                  {progressbarContentData?.map(eventData =>
                    eventData?.id == item?.id ? (
                      <div style={{ marginTop: '112px' }}>
                        <ProgressBar
                          style={{
                            visibility:
                              eventData?.vistime?.last?.hasOwnProperty(
                                'progress'
                              )
                                ? ''
                                : 'hidden'
                          }}
                          isLoading={false}
                          percent={eventData?.vistime?.last?.progress}
                          size={'small'}
                          showInfo={true}
                          sliderWidth={378}
                          smallprogress={smallprogress}
                        />
                      </div>
                    ) : null
                  )}
                </div>
                {item?.image_small && item?.proveedor_code == 'amco' ? (
                  item?.format_types === 'ppe,download' ? (
                    <div className="proveedor-block-rail-alq ">
                      <img src={'images/Alquilar.svg'} className="tag-alq" />
                    </div>
                  ) : item?.format_types === 'ppe' ? (
                    <div className="proveedor-block-rail-alq ">
                      <img src={'images/Alquilar.svg'} className="tag-alq" />
                    </div>
                  ) : null
                ) : item.image_small &&
                  item?.proveedor_code &&
                  item?.image_medium ? (
                  <div className="proveedors-block-rail">
                    <img
                      id="#icon1"
                      src={props?.Addproveedor(item?.proveedor_code)}
                    />
                    {item?.format_types === 'free' ? (
                      <div className="proveedors-block-rail">
                        <img src={'images/verahora.png'} className="tag" />
                      </div>
                    ) : null}
                  </div>
                ) : item.image_small &&
                  item?.proveedor_code === 'picardia2' &&
                  item?.image_medium ? (
                  ((
                    <div className="proveedors-block-rail">
                      <img
                        id="#icon1"
                        src={props?.Addproveedor(item?.proveedor_code)}
                        className="tag"
                      />
                    </div>
                  ),
                  (
                    <div
                      className="proveedors-block-rail"
                      style={{ left: '20px' }}
                    >
                      <img src={'images/Adultus.svg'} className="tag" />
                    </div>
                  ))
                ) : null}
              </button>
            ))}
          </div>
        ) : (
          <div style={{ display: 'flex' }}>
            <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
          </div>
        )}
      </div>
    </div>
  )
}

export default TalentSearch
