.search-container-layout {
  width: 1905px;
  height: 930px;
  position: fixed;
  opacity: 1;
  display: flex;
  background-color: black;

  .keyboard-layout {
    background-color: black;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    margin-top: 55px;
    margin-left: 75px;
  }

  .keyboard-special-buttons {
    height: 51.25px;
    width: 148.63px;
    border-radius: 6.6px;
    background-color: #2e303d;
    

    &:focus,
    &:active {
      outline: 3px solid white;
    }
  }

  .keyboard-clear-buttons {
    height: 46.66px;
    width: 201.69px;
    border-radius: 6.6px;
    background-color: #2e303d;
  }

  .keyboard-special-buttons:focus,
  .keyboard-clear-buttons:focus,
  .keyboard-special-buttons:active,
  .keyboard-clear-buttons:active {
    outline: 3px solid white;
  }

  .search-screen-layout {
    flex: 0 0 855px;
    flex-direction: column;
    background-color: black;
    box-sizing: border-box;
    /* make sure 50% is still 50% after you add padding */
    margin-top: 55px;
    margin-right: 50px;

    .search-input-layout {
      box-sizing: border-box;
      height: 72px;
      width: 720px;
      border-radius: 6px;
      background-color: #28292f;
      position: relative;
      display: flex;
      flex-direction: row;
      margin-bottom: 20px;
      outline: none !important;
      border: #28292f !important;
      box-shadow: none !important;
      border-bottom: none !important;
      margin-left: 33px;
      cursor: pointer;

      &:focus,
      &:active {
        border: 4px solid #4c6f94 !important;
      }

      .search-icon {
        height: 36px;
        width: 36px;
        margin-top: 10px;
        margin-left: 16px;
        margin-right: 13px;
        margin-top: 18px;
        margin-bottom: 57px;
      }

      .cursor-point {
        position: absolute;
        left: 67px;
        top: 15px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 40px;
        animation: blink 2s step-end infinite;
      }
      @keyframes blink {
        50% {
          opacity: 0;
        }
      }

      .search-input {
        outline: none;
        height: 72px;
        width: 740px;
        background: 0% 0% no-repeat padding-box;
        opacity: 1;
        cursor: none !important;
      }

      .search-input::-webkit-input-placeholder{
        padding-left: 10px;
      }

      .input-text {
        color: #ffffff;
        font-family: Roboto;
        font-size: 33px;
        letter-spacing: -0.35px;
        line-height: 22.33px;
        position: relative;
        text-align: left;
        opacity: 1;
        outline: none;
        caret-color: white;
        outline: none !important;
        border: #28292f !important;
        cursor: pointer;
      }
    }

    .resultados {
      height: 40px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 40px;
      margin-top: 45px;
      margin-bottom: 40px;
    }

    .busquedas-populares {
      height: 40px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 100px;
      margin-left: 33px;
    }

    .popular-error-layout {
      flex-direction: column;
      height: 40px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 40px;
      letter-spacing: 0;
      line-height: 48px;
      text-align: center;
      margin-top: 30px;
      display: flex;
    }

    .talent-message-width {
      width: 901px;
    }

    .result-layout {
      display: grid;
      flex-direction: column;
      max-height: 705px;
      overflow-x: hidden;
      overflow-y: auto;
      scroll-snap-type: y mandatory;
    }
    
    .result-layout-error{
      display: grid;
      flex-direction: column;
      max-height: 360px;
      overflow-x: hidden;
      overflow-y: auto;
      scroll-snap-type: y mandatory;
    }
  
    .popular-result-layout {
      display: flex;
      flex-direction: column;
      display: flex;
      flex-direction: column;
      height: 350px;
      overflow-x: hidden;
    }

    .result-container {
      height: 210px;
      width: 721px;
      position: 'relative';
      opacity: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      background-color: black;
      margin-top: 42px;
      margin-left: 33px;
    }

    .result-container:last-child{
      margin-bottom: 220px;
    }

    .search-player-loader {
      width: '50%';
      height: 1080px;
      position: absolute;
      top: 0;
      left: 50%;
      display: flex;
      margin: 0px auto;
      right: 0;
    }

    .resultoverlay {
      background-image: linear-gradient(
        to bottom,
        rgba(245, 246, 252, 0.52),
        rgba(117, 19, 93, 0.73)
      );
      height: 245px;
      width: 765px;
      position: 'relative';
      opacity: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      background-color: black;
      margin-top: 28px;
    }

    .popular-search-img-layout{
      height: 190px;
      width: 328px;
      position: relative;
      
      &:focus,
      &:active {
        scroll-snap-align: center;
        transform: scale(1.1);
        outline: 5px solid white;
        margin: 1px;
      }
    }
    .image-layout {
      height: 190px;
      width: 328px;
      position: relative;

      &:focus,
      &:active {
        scroll-snap-align: center;
        transform: scale(1.1);
        outline: 5px solid white;
        margin: 1px;
      }
    }

    .search_mlt-image {
      display: flex;
      height: 186px;
      width: 326px;
    }

    .image-layout:focus > .search_mlt-image,
    .image-layout:active > .search_mlt-image {
      height: 250px;
      width: 350px;
    }

    .image-live {
      height: 190px;
      width: 328px;
      position: relative;

      &:focus,
      &:active {
        transform: scale(1.05);
        outline: 5px solid white;
        margin: 13px;
      }
    }

    .search_live-image {
      display: flex;
      height: 186px;
      width: 326px;
    }

    .image-live:focus > .search_live-image,
    .image-live:active > .search_live-image {
      height: 250px;
      width: 350px;
    }

    .image-layout-lock {
      height: 190px;
      width: 328px;
      position: relative;
      opacity: 0.2;

      &:focus,
      &:active {
        transform: scale(1.05);
        outline: 5px solid white;
        margin: 13px;
      }
    }

    .image-layout-lock:focus > .search_mlt-image,
    .image-layout-lock:active > .search_mlt-image {
      height: 250px;
      width: 350px;
    }

    .LiveTvDescDetai {
      opacity: 1;
      height: 230px;
      width: 348px;
      background: transparent
        linear-gradient(90deg, #121212 0%, #121212f2 27%, #03151f00 100%) 0% 0%
        no-repeat padding-box;
      position: absolute;
      top: 0;
      z-index: 1;
    }

    .result-detail-layout:focus > .search_mlt-image,
    .result-detail-layout:active > .search_mlt-image {
      height: 250px;
      width: 350px;
    }

    .inline-play-button {
      display: flex;
      justify-content: flex-end;
      width: 80px;
      height: 80px;
      position: absolute;
      right: 5px;
      bottom: 20px;
    }

    .result-detail-layout {
      margin-left: 50.57px;
      height: 217px;
      width: 403px;
      margin-top: 10px;
    }

    .popular-detail-layout {
      margin-left: 37px;
      height: 217px;
      width: 367px;
    }

    .result-content-title {
      height: 40px;
      width: 360px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 40px;
      margin-bottom: 8px;
    }

    .result-content-year {
      height: 40px;
      width: 336px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 30px;
      letter-spacing: 0;
      line-height: 40px;
    }

    .result-content-moreinfo {
      height: 72px;
      width: 336px;
      margin-top: 24px;
      border-radius: 4px;
      background-color: #2e303d;

      &:hover {
        display: inline;
        cursor: pointer;
      }
      &:focus,
      &:active {
        scroll-snap-align: center;
        transform: scale(1);
        outline: 3px solid white;
        border-radius: 4px;
      }
    }

    .mas-info {
      height: 40px;
      width: 304px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 28px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 40px;
      text-align: center;
      margin-left: 112px;
      margin-right: 16px;
      margin-top: 16px;
      margin-bottom: 16px;
    }

    .result-channel-detail {
      height: 40px;
      width: 336px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 30px;
      letter-spacing: 0;
      line-height: 40px;
      margin-bottom: 8px;
    }

    .live-img {
      height: 32px;
      width: 32px;
    }

    .deatil-status-tag {
      height: 32px;
      border-radius: 6px;
      margin-left: 8px;
    }

    .icon-recordatorio {
      height: 30.4px;
      width: 30.4px;
      margin-left: 15px;
    }

    .reminder-icon {
      height: 32px;
      width: 32px;
      margin-left: 15px;
    }

    .data-grabado {
      height: 32px;
      width: 224px;
      color: #ffffff;
      font-family: '.SF NS Display';
      font-size: 30px;
      letter-spacing: 0;
      line-height: 32px;
      margin-left: 17px;
    }

    .content-time {
      height: 40px;
      width: 350px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 30px;
      letter-spacing: 0;
      line-height: 40px;
      margin-top: 8px;
    }

    .detail-progressbar {
      position: absolute;
      margin-top: -43px;
      margin-left: 399px;
    }

    .default-titlerail-vod {
      height: 35px;
      width: 408px;
      color: white;
      font-family: roboto;
      font-size: 30px;
      letter-spacing: 0;
      line-height: 33px;
      margin-top: 40px;
    }

    .search-progressbar-position {
      width: 250px;
      margin-top: 160px;
      margin-right: 387px;
    }

    .play-icon-img {
      height: 80px;
      width: 80px;
      margin-left: 8px;
      margin-top: 3px;
    }

    .play-icon {
      height: 80px;
      width: 80px;
      margin-left: 235px;
      margin-top: 3px;
    }

    .inline-img-button-layout {
      position: absolute;
      right: 6px;
      display: flex;
      flex-direction: row;
      width: 314px;
    }

    .progress-button {
      position: relative;
      bottom: 90px;
      display: flex;
      height: 1px;
      width: 265px;
      justify-content: flex-start;
      opacity: 0.8;

      .progress-margin {
        margin-top: 56px;
      }
    }

    .play-button {
      display: flex;
      justify-content: flex-end;
      width: 80px;
      height: 80px;
      position: absolute;
      right: 5px;
      bottom: 20px;
    }

    .proveedors-block-rail {
      position: absolute;
      left: 7px;
      top: 5px;
      display: flex;
      width: auto;
      height: 32px;
      
      .premium-icon{
        width: auto;
        height: 34px;
        opacity:4
      }

      .picardia-image {
        width: auto;
        height: 24px;
        opacity:4
      }

      .adults-tag{
        position: relative;
        right: 134px;
        top: 25px;
        display: flex;
      }
      
      .picardia-tag {
        position: absolute;
        width: 101px;
        height: 30px;
      }
    }

    .premium-icon{
      width: auto;
      height: 34px;
    }
    .picardia-image {
      width: auto;
      height: 24px;
    }

    .verahora-tag {
      height: 34px;
      width: auto;
      border-radius: 6px;
      background-color: #68b75c;
      color: #ffffff;
      font-family: Roboto;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 20px;
    }

    .tags {
      width: 111px;
      height: 38px;
    }

    .proveedor-block-rail-alq {
      position: absolute;
      left: 6px;
      top: 8px;
      display: flex;
      height: 34px;
      width: 100px;

      .tag-alq{
        width: 100px;
        height: 34px; 
      }
    }

    .locked-channel-icon {
      position: absolute;
      right: 24px;
      top: 20.5px;
    }

    .icon-tag-coloumn {
      display: flex;
      flex-direction: column;
    }
    .record-icon-row {
      display: flex;
      flex-direction: row;
    }

    .tagAlq {
      width: 111px;
      height: 39px;
    }

    .talent-img-layout {
      height: 184px;
      width: 328px;
      background-color: rgba(216, 216, 216, 0);
      display: flex;
      justify-content: space-evenly;

      &:focus,
      &:active {
        outline: 2px solid white;
        //this change may be needed for search
        // box-shadow: 1px 1px 10px 4px white;
        margin: 2rem;
        opacity: 1;
        transform: scale(1.1);
      }
    }

    .cast-result-container {
      height: 245px;
      width: 765px;
      position: 'relative';
      opacity: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      background-color: black;

      .talent-wrapper {
        height: 225px;
        width: 765px;
        position: 'relative';
        opacity: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-evenly;
        background-color: black;
        margin-top: 28px;
        margin-left: 31px;

        .cast-button-layout {
          height: 190px;
          width: 190px;

          &:focus,
          &:active {
            background: transparent 0% 0% no-repeat;
            transform: scale(1.05);
            outline: unset;
            padding: 10px;
            opacity: 1;
            height: 190px;
            width: 190px;
            border: 7px solid white;
            border-color: white;
            border-radius: 50%;
          }

          .talent-oval {
            height: 190px;
            width: 190px;
            border-radius: 50%;
          }
        }
        .talent-result-detail-layout {
          margin-left: 56px;
          height: 217px;
          width: 403px;
          margin-top: 10px;

          .talent-name {
            height: 40px;
            width: 365px;
            color: #ffffff;
            font-family: Roboto;
            font-size: 36px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 40px;
          }

          .actor-director {
            height: 40px;
            width: 336px;
            color: #ffffff;
            font-family: Roboto;
            font-size: 30px;
            letter-spacing: 0;
            line-height: 40px;
            margin-top: 8px;
          }
        }
      }
    }

    .utiliza-otras-palabr {
      height: 160px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 33px;
      letter-spacing: 0;
      line-height: 40px;
      text-align: center;
      margin-bottom: 15px;
      margin-left: 30px;
    }

    .search-error {
      height: 48px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 39px;
      margin-bottom: 33px;
      text-align: center;
      white-space: pre-wrap;
    }

    .search-network-error {
      height: 40px;
      width: 720px;
      color: #981c15;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 39px;
      text-align: center;
      margin-top: 16px;
      white-space: pre-wrap;
    }

    .no-encontramos-coinc {
      height: 48px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 40px;
      text-align: center;
    }

    .error-layout {
      margin-top: 72px;
      display: flex;
      flex-direction: column;
    }

    .intenta-mas-tarde {
      height: 40px;
      width: 720px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 33px;
      letter-spacing: 0;
      line-height: 40px;
      text-align: center;
      margin-top: 8px;
    }

    .proveedor-block-rail {
      position: absolute;
      left: 9px;
      top: 9px;
      display: flex;
    }

    .tagAlq {
      width: 111px;
      height: 39px;
    }

    .tag {
      width: 161px;
      height: 38px;
    }

    .rail_block-recordig {
      height: 232px;
      width: 408px;
      position: relative;
    }

    .recording-block {
      height: 245px;
      width: 790px;
      position: 'relative';
      opacity: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      margin-top: 28px;
      margin-left: 13px;
      scroll-snap-type: x mandatory;
    }

    .duration-time {
      height: 40px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 30px;
      letter-spacing: 0;
      line-height: 40px;
    }

    .record-status {
      height: 40px;
      width: 336px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 30px;
      letter-spacing: 0;
      line-height: 40px;
      margin-left: 25px;
    }

    .recording-image {
      margin-top: 5px;
    }

    .record-metadata-block {
      margin-left: 50px;
      height: 217px;
      width: 367px;
    }

    .record-play-icon {
      position: absolute;
      left: 315px;
      top: 140px;
    }

    .deleteIcons-record {
      display: flex;
      position: absolute;
      top: 209px;
      margin-left: 689px;
      width: max-content;
    }

    .deleteIcons-record {
      display: none;
    }

    .content-title-rec {
      width: 360px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 40px;
      margin-bottom: 8px;
    }

    .rec-backBtn {
      display: flex;
      position: absolute;
      right: 87px;
      top: 47px;
      justify-content: space-between;
      align-items: center;
      width: 292px;
      height: 28px;
      padding: 13px;
      background-color: #2e303d;
      border-radius: 9px;
    }

    .rec-backBtn:focus {
      background: #981c15 0% 0% no-repeat padding-box;
    }

    .rail_block-recordig:focus {
      scroll-snap-align: center;
      .rail-image {
        border: 4px solid #fff !important;
        padding: 4px;
        z-index: 1;
        width: 430px;
        height: 230px;
        border-radius: 10px;
        transform: scale(1.02);
        transition: transform 0.2s ease-in-out;
      }

      .deleteIcons-record {
        display: block !important;
      }
    }
    .search-container-loader {
      position: absolute;
      top: 201px;
      right: 336px;
    }
  }
}
