.add-profile{
    background-color: #121212;
    position:fixed;

.editbackbutton{
    margin-top: 50px;
    position: fixed;
    bottom: 5px;
}

.edit-profile-save:focus,
.edit-profile-save:active
 {
    margin-left: 27px;
    transform: scale(1.2);
    border-radius: 6.9px;
    background-color: #981C15;
}
.cancelbtn-editprof:focus{
    transform: scale(1.2);
    border-radius: 6.9px;
    margin-right: 40px;
}
.EditProfile {
    top: 0px;
    left: 0px;
    width: 1920px;
    height: 1080px;
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

.profile-app-logo {
    width: 1920px;
    background-color: #121212;
    height:96px;
    display: flex;
    justify-content: space-between;

    .logo-img {
        margin-top: 67px;
        margin-left: 90px;
        width: 171px;
        height: 36px;
     }
    
    .back-button{
        height: 48px;
        width: 292px;
        border-radius: 6.6px;
        background-color: #2E303D;
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 49px;
        margin-left: 96px;
        margin-right: 53px;
        .yellow-dot{
            height: 20px;
            width: 20px;
        }
        .back-arrow{
            height: 24px;
            width: 30px;
        }
        .back-button-text
        {
             height: 30px;
            width: 146px;
            color: #FFFFFF;
            font-family: Roboto;
            font-size: 29.04px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 29.04px;
        }
        &:focus{
            background-color: #981c15;
        }
      }
    }

.edit_heading {
    margin-top: 74px;
    left: 873px;
    height: 57px;
    font-weight: 'Regular';
    text-align: center;
    font-family: "Roboto";
    font-size: 48px;
    letter-spacing: 0px;
    color: #F1F2F3;
    opacity: 1;
}

.editprof_image {
    padding-left: 55rem;
    width: 128px;
    height: 128px;
}

.edit_title {
    margin-top: 40px;
    left: 787px;
    height: 35px;
    text-align: center;
    font-size: 30px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.edit_container {
    height: 135px
}

.edit_name {
    margin-left: 38rem;
    width: 109px;
    height: 33px;
    text-align: left;
    font-size: 28px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.edit_chara {
    margin-top: 4rem;
    margin-left: 29rem;
    width: 59px;
    height: 33px;
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.edit_radio {
    top: 531px;
    margin-left: 612px;
    width: 42px;
    height: 47px;
    border: 3px solid #34353BE8;
    border-radius: 12px;
    opacity: 1;
    margin-top: 17px;
}

.edit_radiobox {
    margin-top: 57px;
}

.editkidsprofile {
    margin-left: 573px;
    margin-top: -47px;
}

.editkids_profname {
    text-align: left;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
    font-size: 28px;
    font-family: 'Roboto';
    margin-left: 6rem;
}

.edit-container{
    display:flex;
    justify-content: space-around;
    }
    .edit-key-board{
        height: 629px;
        width: 787px;
    }
    .profile-name-container{
       margin-bottom: 16px;
      }
      .profile-name-input-container{
        height: 72px;
        width: 720px;
        border-radius: 6px;
        background-color: #212224;
        margin-top: 0px;
        .profile-name-input  {
            height: 52px;
            width: 585px;
            color: white;
            font-family: Roboto;
            font-size: 32px;
            letter-spacing: -0.51px;
            line-height: 32px;
            background-color: #212224;
            border: unset;
            outline: none;
            margin-left: 30px;
          }
      }
    .edit-profile-cont{
         height: 634px;
         width: 799px;
        
        .edit-content{
            .edit-image-container{
                display: flex;   
                margin-bottom: 34px;  
                .profile-image{
                    height: 150px;
                    width: 150px;
                  }
                
        .choose-profile-text-container{
            height: 72px;
            width: 401px;
            border-radius: 4.59px;
            background-color: #2E303D;
            margin-left: 38px;
            margin-top: 35.5px;
            display: flex;
            justify-content: center;
            align-items: center;

            .choose-profile-text{
                color: #FFFFFF;
                font-family: Roboto;
                font-size: 36.8px;
                font-weight: bold;
                letter-spacing: -0.59px;
                line-height:82.8px;
                text-align: center;
                display: block;
            }
            
        }
}
    }
}
  
.prf-watch-header{
    display: flex;
    color: white;
    margin: 0px;
    align-items: center;
    justify-content: center;
    height: 57px;
    width: 1920px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 48px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 57px;
    text-align: center;
    margin-bottom: 73px;
}
.edit-checkbox-container{
    display: flex;
    align-items: center;
    margin-top: 24px;
    outline: unset;
    margin-bottom: 54px;

              .checkbox-sub-container {
            display: block;
            position: relative;
            cursor: pointer;
            font-size: 22px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .checkbox-focus{
            height: 72px;
            width: 72px;
            border-radius: 6px;
            background-color: #212224!important;
            margin-right: 16px;   
        }
          /* Create a custom checkbox */
          .check-mark {
            position: absolute;
            top: 0;
            left: 0;
            height: 72px;
            width: 72px;
            border-radius: 6px;
            background-color: #212224;
            margin-left: 2px;
            margin-right: 16px;
            margin-top: -9px;
          
        }
        .kids-prof-name{
            height: 32px;
            width: 629px;
            color: #FFFFFF;
            font-family: Roboto;
            font-size: 32px;
            letter-spacing: -0.51px;
            line-height: 32px;
            margin-left: 29px;
            margin-top: 2px;
        }
}

.edit-profile-save{
    height: 72px;
    width: 720px;
    border-radius: 4px;
    background-color: #2E303D;
}
/* Hide the browser's default checkbox */
.checkbox-sub-container input {
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}
/* On mouse-over, add a grey background color */
.edit-checkbox-container:focus input~.check-mark {
    z-index: 1;
     outline: unset;
    border-radius: 13px;
    border: 4px solid #4C6F94;
}
/* When the checkbox is checked, add a blue background */
.checkbox-sub-container input:checked~.check-mark {
    background-color: #212224;
}
/* Create the checkmark/indicator (hidden when not checked) */
.check-mark:after {
    content: "";
    position: absolute;
    display: none;
}
/* Show the checkmark when checked */
.checkbox-sub-container input:checked~.check-mark:after {
    display: block;
}

/* Style the checkmark/indicator */
.checkbox-sub-container.check-mark:after {
    left: 25px;
    top: 14px;
    width: 14px;
    height: 25px;
    font-weight: bold;
    border: solid white;
    border-width: 0px 7px 7px 0;
    -webkit-transform: rotate(35deg);
    -ms-transform: rotate(35deg);
    transform: rotate(35deg);
}
.redDot {
    background: #DE1717 0% 0% no-repeat padding-box;
    opacity: 1;
    width: 30px;
    border: 0px;
    border-radius: 43px;
    height: 16px;
    margin: 8px 8px 0px 0px;
}


.ul.kidsList li::before {  
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  margin-top: 9px;
  border-radius: 50%;
  left: 36px;
  background-color: #DE1717;
  }

  ul.kidsList li {
    font-size: 32px;
}

.inline-edit-btns{
    display: flex;
 } 

    .edit-profile-save {
        height: 72px;
        width: 360px;
        opacity: 0.57;
        border-radius: 6px;
        background-color:#981C15;
        display: flex;
        justify-content: center;
        align-items: center;

            .edit-profile-save-text{
                height: 38px;
                width: 293px;
                color: #FFFFFF;
                font-family: Roboto;
                font-size: 32px;
                font-weight: bold;
                letter-spacing: -0.51px;
                line-height: 38px;
                text-align: center;
            }
    }
    .cancelbtn-editprof{
        height: 72px;
        width: 344px;
        border-radius: 4px;
        background-color: #2E303D;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 15px;

        .cancelbtntxtEditprof{
            height: 38px;
            width: 293px;
            color: #FFFFFF;
            font-family: Roboto;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: -0.51px;
            line-height: 38px;
            text-align: center;
        }
    }
    .delete-profilebtn{
        height: 72px;
        width: 720px;
        border-radius: 4px;
        background-color: #2E303D;
        margin-top: 19px;
        display: flex;
        justify-content: center;
        align-items: center;
        .delete-profilebtn-text{
           height: 38px;
           width: 629.71px;
           color: #FFFFFF;
           font-family: Roboto;
           font-size: 32px;
           font-weight: bold;
           letter-spacing: -0.51px;
           line-height: 38px;
           text-align: center;
                 }
    }
    .checkbox-popup{
        z-index: 1;
        border-radius: 6px;
         background-color: #FFFFFF;
         height: 243px;
         width: 566px;
         position: absolute;
         right: 35px;
         top: 469px;
         .checkbox-popup:after,
         .checkbox-popup:before {
             left: 24px;
             top: 4%;
             border: solid transparent;
             content: " ";
             width: 0;
             position: absolute;
             pointer-events: none;
         }
         .checkbox-popup:after {
             border-width: 10px 10px 10px 20px;
             border-left: 27px solid #FFFFFF;
         }
        
         .kids-desc-title {
            height: 43px;
            width: 428px;
            color: #1A1A1A;
            font-family: Roboto;
            font-size: 32px;
            font-weight: bold;
            letter-spacing: -0.51px;
            line-height: 38px;
            text-align: left;
            margin-left: 22px;
            margin-bottom: 0px;
            margin-top: 16px;
        }

        .kids-desc-container {
            margin-left: 22px;
            margin-top: 16px;
        .kids-list{    
        list-style: none;  
            .kids-desc {
                height: 72px;
                width: 520px;
                color: #1A1A1A;
                font-family: Roboto;
                font-size: 32px;
                letter-spacing: -0.51px;
                line-height: 36px;
                opacity: 1;
                display: flex;
                flex-direction: row;
                margin-bottom: 8px;
                margin-left:-18px;
            
            }
        }
           ul.kidsList li::before {
                content: '';
                position: absolute;
                width: 10px;
                height: 10px;
                margin-top: 9px;
                border-radius: 50%;
                left: 22px;
                background-color: #000000;            
            }
            
            ul.kidsList li {
                font-size: 32px;
            }
        
        }
     }

    .checkbox-popup::before {
        content: "";
        position: absolute;
        width: 51px;
        height: 38px;
        z-index: -1;
        border-radius: 3px;
        right: 528px;
        transform: rotate(50deg);
        background-color: #FFFFFF;
        bottom:185px;
    }
    
     .delete-profilebtn:focus
    {
    transform: scale(1.2);
    border-radius: 6.9px;
     }
   .choose-profile-text-container:focus{
    transform: scale(1.1);
    border-radius:3px;
   }

   .profile-name-input-container:focus{
    border: 2.67px solid #4C6F94;
    border-radius: 4px;

   }
.profile-name{
  height: 33px;
  width: 677px;
  color: #FFFFFF;
  font-family: Roboto;
  font-size: 28px;
  font-weight: bold;
  letter-spacing: -0.2px;
  line-height: 33px;
}
.error-popup {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 10px;
    margin-top: 10px;
    border-radius: 4px;
   }
 .edit-profile-container{
  margin-top:128px;
   }
   .cursor-point-addprofile{
    left: 61px;
    top: 15px;
    color: #ffffff;
    font-family: Roboto;
    font-size: 36px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 40px;
    animation:blink 5s step-end infinite;
    margin-left: 10px;
  }
}