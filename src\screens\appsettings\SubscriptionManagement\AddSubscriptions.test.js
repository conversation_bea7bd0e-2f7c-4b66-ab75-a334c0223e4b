import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import AddSubscriptions from './AddSubscriptions';

// Mock the required modules
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn()
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: jest.fn(),
  useNavigate: jest.fn()
}));

jest.mock('react-lazy-load-image-component', () => ({
  LazyLoadImage: ({ src, className }) => <img src={src} className={className} alt="lazy loaded" />
}));

const mockStore = configureStore([]);

describe('AddSubscriptions Component', () => {
  let store;
  let mockDispatch;
  let mockNavigate;

  beforeEach(() => {
    // Reset mocks
    mockDispatch = jest.fn();
    mockNavigate = jest.fn();

    store = mockStore({
      settingsReducer: {
        getPlanSelector:   
        {
          "offers": [
              {
                  "producttype": "CV_MENSUAL",
                  "price": {
                      "currency": "$",
                      "amount": 115,
                      "regular_amount": 115,
                      "price_before_tax": null
                  },
                  "assets": {
                      "background": {
                          "default": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo.png?1691008683",
                          "124x295": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_124x295.png?1693003057",
                          "135x288": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_135x288.png?1693003078",
                          "165x394": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_165x394.png?**********",
                          "230x491": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_230x491.png?1693003015",
                          "248x590": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_248x590.png?1693002994",
                          "aaf": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_aaf.png?1691008711"
                      },
                      "logo": {
                          "124x32": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_124x32.png?1693000470",
                          "135x40": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_135x40.png?1693000303",
                          "165x40": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_165x40.png?1692999965",
                          "230x64": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_230x64.png?1693000117",
                          "248x64": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_248x64.png?1692999775",
                          "aaf": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_aaf.png?1691008735",
                          "adr": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_adr.png?1691008791",
                          "ios": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_ios.png?1691008813",
                          "web": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_web.png?1693246979",
                          "win": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_win.png?1693247268",
                          "xbox": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_xbox.png?1693247485"
                      }
                  },
                  "translation": {
                      "labelTemporality": "/ mes",
                      "labelTaxes": "IVA Incluido",
                      "labelBeforeTaxes": null,
                      "txtPromo": {
                          "texts": [
                              {
                                  "text": "Disfruta de un"
                              },
                              {
                                  "text": "precio especial"
                              },
                              {
                                  "text": "ahora"
                              }
                          ]
                      },
                      "btnFeatures": {
                          "fgColor": "#ecaf2a",
                          "texts": [
                              {
                                  "text": "¿Qué incluye?"
                              },
                              {
                                  "text": "¿QUÉ INCLUYE?"
                              }
                          ]
                      },
                      "btnSuscription": {
                          "fgColor": "#981C15",
                          "texts": [
                              {
                                  "text": "SUSCRÍBETE",
                                  "color": "#FFFFFF"
                              }
                          ]
                      }
                  },
                  "_links": {
                      "checkout": {
                          "href": "/services/payway/workflowstart?object_type=A&offer_id=14327289&suscription_id=980&device_category=tv&device_manufacturer=lg&device_model=web0s&device_type=tv&region=mexico"
                      }
                  }
              },
              {
                  "producttype": "FOX",
                  "price": {
                      "currency": "$",
                      "amount": 1149,
                      "regular_amount": 1149,
                      "price_before_tax": null
                  },
                  "assets": {
                      "background": {
                          "default": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo.png?1698877547",
                          "124x295": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_124x295.png?1698877607",
                          "135x288": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_135x288.png?1698877669",
                          "165x394": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_165x394.png?1698877722",
                          "230x491": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_230x491.png?1698877778",
                          "248x590": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_248x590.png?1698877834",
                          "aaf": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_aaf.png?1698877877"
                      },
                      "logo": {
                          "124x32": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_124x32.png?1698877920",
                          "135x40": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_135x40.png?1698877962",
                          "165x40": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_165x40.png?1698878089",
                          "230x64": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_230x64.png?1698878124",
                          "248x64": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_248x64.png?1698878283",
                          "aaf": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_aaf.png?1698878314",
                          "adr": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_adr.png?1698878349",
                          "ios": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_ios.png?1698878435",
                          "web": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_web.png?1698878463",
                          "win": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_win.png?1698878487",
                          "xbox": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_xbox.png?1698878519"
                      }
                  },
                  "translation": {
                      "labelTemporality": "/ año",
                      "labelTaxes": "IVA incluido",
                      "labelBeforeTaxes": null,
                      "txtPromo": {
                          "texts": [
                              {
                                  "text": "Disfruta este"
                              },
                              {
                                  "text": "canal premium"
                              }
                          ]
                      },
                      "btnFeatures": {
                          "fgColor": "#ECAF2A",
                          "texts": [
                              {
                                  "text": "¿Qué incluye?"
                              },
                              {
                                  "text": "¿QUÉ INCLUYE?"
                              }
                          ]
                      },
                      "btnSuscription": {
                          "fgColor": "#002885",
                          "texts": [
                              {
                                  "text": "SUSCRÍBETE",
                                  "color": "#FFFFFF"
                              }
                          ]
                      }
                  },
                  "_links": {
                      "checkout": {
                          "href": "/services/payway/workflowstart?object_type=A&offer_id=********&suscription_id=3200&device_category=tv&device_manufacturer=lg&device_model=web0s&device_type=tv&region=mexico"
                      }
                  }
              },
              {
                  "producttype": "EDYE",
                  "price": {
                      "currency": "$",
                      "amount": 57,
                      "regular_amount": 57,
                      "price_before_tax": null
                  },
                  "assets": {
                      "background": {
                          "default": "http://clarovideocdn4.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo.png?1713215152",
                          "124x295": "http://clarovideocdn2.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo_124x295.png?1714158092",
                          "135x288": "http://clarovideocdn2.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo_135x288.png?1714158134",
                          "165x394": "http://clarovideocdn2.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo_165x394.png?1714157837",
                          "230x491": "http://clarovideocdn2.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo_230x491.png?1714150040",
                          "248x590": "http://clarovideocdn2.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo_248x590.png?1714150025",
                          "aaf": "http://clarovideocdn8.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenFondo_aaf.png?1713215172"
                      },
                      "logo": {
                          "124x32": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_124x32.png?1714154116",
                          "135x40": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_135x40.png?1714153979",
                          "165x40": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_165x40.png?1714151211",
                          "230x64": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_230x64.png?1714154073",
                          "248x64": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_248x64.png?1714154178",
                          "aaf": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_aaf.png?1713215192",
                          "adr": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_adr.png?1713215208",
                          "ios": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_ios.png?1713215226",
                          "web": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_web.png?1713215243",
                          "win": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_win.png?1713215259",
                          "xbox": "http://clarovideocdn8.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_EDYE/ImagenLogo_xbox.png?1713215338"
                      }
                  },
                  "translation": {
                      "labelTemporality": "/ mes",
                      "labelTaxes": "IVA Incluido",
                      "labelBeforeTaxes": null,
                      "txtPromo": {
                          "texts": [
                              {
                                  "text": "Disfruta este"
                              },
                              {
                                  "text": "canal premium"
                              }
                          ]
                      },
                      "btnFeatures": {
                          "fgColor": "#ecaf2a",
                          "texts": [
                              {
                                  "text": "¿Qué incluye?"
                              },
                              {
                                  "text": "¿QUÉ INCLUYE?"
                              }
                          ]
                      },
                      "btnSuscription": {
                          "fgColor": "#002885",
                          "texts": [
                              {
                                  "text": "SUSCRÍBETE",
                                  "color": "#FFFFFF"
                              }
                          ]
                      }
                  },
                  "_links": {
                      "checkout": {
                          "href": "/services/payway/workflowstart?object_type=A&offer_id=14368385&suscription_id=3400&device_category=tv&device_manufacturer=lg&device_model=web0s&device_type=tv&region=mexico"
                      }
                  }
              }
          ]
      }
      },
      Images: {
        imageresponse: {
          'test_banner_en': "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_165x394.png?**********",
          'en_transactional_test_family_logo': 'test_logo_url'
        }
      },
      login: {
        isLoggedIn: {
          response: {
            parent_id: '123',
            session_stringvalue: 'test_session'
          }
        }
      },
      initialReducer: {
        appMetaData: {
          myaccount_configuration: JSON.stringify({
            default: {
              suscription: [{ provider: 'test_product' }]
            }
          }),
          translations: JSON.stringify({
            language: {
              en: {
                subscription_premium_monthly_label: 'Monthly',
                premium_subscriptionDescription_costTaxIncluded_label: 'Tax included',
                transactional_promotion_text_subscription_plan_tryInvite_description: 'Try for free',
                subscribe_now: 'Subscribe Now',
                view_details: 'View Details',
                price_subscriptionDescription_slash_label: '/',
                style_hbo: '#FF0000',
                style_hbo_text: '#FFFFFF'
              }
            }
          })
        }
      }
    });

    require('react-redux').useSelector.mockImplementation(callback => {
      return callback(store.getState());
    });

    require('react-redux').useDispatch.mockReturnValue(mockDispatch);

    require('react-router-dom').useLocation.mockReturnValue({ state: {} });

    require('react-router-dom').useNavigate.mockReturnValue(mockNavigate);

    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'en'),
        setItem: jest.fn()
      },
      writable: true
    });

    // Mock element scrollIntoView
    Element.prototype.scrollIntoView = jest.fn();

    // Mock document.getElementById to prevent issues with focus
    document.getElementById = jest.fn().mockImplementation(() => ({
      focus: jest.fn(),
      scrollIntoView: jest.fn()
    }));
  });

  afterEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
  });

  test('renders AddSubscriptions component with subscription cards', () => {
    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions apiUrl="test-url" />
        </BrowserRouter>
      </Provider>
    );
    expect(container.querySelector('.add-subscription-container')).toBeInTheDocument();
    expect(container.querySelector('.add-subscription-card')).toBeInTheDocument();
    expect(mockDispatch).toHaveBeenCalledWith(expect.objectContaining({
      type: expect.any(String),
      payload: expect.objectContaining({
        userId: '123',
        hks: 'test_session',
        url: 'test-url'
      })
    }));
  });

  test('handles subscription button click', () => {
    mockDispatch.mockClear();

    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    mockDispatch.mockClear();
    mockNavigate.mockClear();

    const subscribeButton = container.querySelector('.add-subscription-card');
    fireEvent.click(subscribeButton);
    expect(mockDispatch).toHaveBeenCalledTimes(2);
    expect(mockNavigate).toHaveBeenCalledWith('/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
      expect.objectContaining({
        state: expect.objectContaining({
          pageName: '/home',
        })
      })
    );
  });

  test('handles plan focus and blur', async () => {
    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    const subscriptionCard = container.querySelector('.add-subscription-card');
    fireEvent.focus(subscriptionCard);
    await waitFor(() => {
      expect(container.querySelector('.sub-view-plan-button')).toBeInTheDocument();
    });
    fireEvent.blur(subscriptionCard);
    expect(container.querySelector('.sub-view-plan-button')).toBeInTheDocument();
  });

  test('handles view details button click', async () => {
    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    mockDispatch.mockClear();
    mockNavigate.mockClear();

    const subscriptionCard = container.querySelector('.add-subscription-card');
    fireEvent.focus(subscriptionCard);

    await waitFor(() => {
      const viewDetailsButton = container.querySelector('.sub-view-plan-button');
      expect(viewDetailsButton).toBeInTheDocument();
      fireEvent.click(viewDetailsButton);
      expect(mockNavigate).toHaveBeenCalledWith('/my-settings/my-subscriptions/add-subscriptions/viewDetails',
        expect.objectContaining({
          state: expect.objectContaining({
            pageName: '/home',
          })
        })
      );
    });
  });

  test('handles empty or missing banner URLs', () => {
    const modifiedStore = mockStore({
      ...store.getState(),
      settingsReducer: {
        getPlanSelector: {
          response: {
            listButtons: {
              button: []
            }
          }
        }
      }
    });

    require('react-redux').useSelector.mockImplementation(callback => {
      return callback(modifiedStore.getState());
    });

    const { container } = render(
      <Provider store={modifiedStore}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );
    expect(container.querySelector('.add-subscription-card')).not.toBeInTheDocument();
  });

  test('handles missing or invalid image URLs', () => {
    const modifiedStore = mockStore({
      ...store.getState(),
      Images: {
        imageresponse: {
          'test_banner_en': "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_165x394.png?**********",
          'en_transactional_test_family_logo': 'test_logo_url'
        }
      }
    });

    require('react-redux').useSelector.mockImplementation(callback => {
      return callback(modifiedStore.getState());
    });

    const { container } = render(
      <Provider store={modifiedStore}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    const bannerImg = container.querySelector('.addsubscription-banner');
    expect(bannerImg).toBeInTheDocument();
    expect(bannerImg.getAttribute('src')).toBe("http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_165x394.png?**********");
  });

  test('handles text truncation correctly', () => {
    const longTextStore = mockStore({
      ...store.getState(),
      settingsReducer: {
        getPlanSelector: {
          "offers": [
              {
                  "producttype": "CV_MENSUAL",
                  "price": {
                      "currency": "$",
                      "amount": 115,
                      "regular_amount": 115,
                      "price_before_tax": null
                  },
                  "assets": {
                      "background": {
                          "default": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo.png?1691008683",
                          "124x295": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_124x295.png?1693003057",
                          "135x288": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_135x288.png?1693003078",
                          "165x394": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_165x394.png?**********",
                          "230x491": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_230x491.png?1693003015",
                          "248x590": "http://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_248x590.png?1693002994",
                          "aaf": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenFondo_aaf.png?1691008711"
                      },
                      "logo": {
                          "124x32": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_124x32.png?1693000470",
                          "135x40": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_135x40.png?1693000303",
                          "165x40": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_165x40.png?1692999965",
                          "230x64": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_230x64.png?1693000117",
                          "248x64": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_248x64.png?1692999775",
                          "aaf": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_aaf.png?1691008735",
                          "adr": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_adr.png?1691008791",
                          "ios": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_ios.png?1691008813",
                          "web": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_web.png?1693246979",
                          "win": "http://clarovideocdn0.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_win.png?1693247268",
                          "xbox": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_aup/SelectorDePlan_Tarjeta_CV_MENSUAL/ImagenLogo_xbox.png?1693247485"
                      }
                  },
                  "translation": {
                      "labelTemporality": "/ mes",
                      "labelTaxes": "IVA Incluido",
                      "labelBeforeTaxes": null,
                      "txtPromo": {
                          "texts": [
                              {
                                  "text": "Disfruta de un"
                              },
                              {
                                  "text": "precio especial"
                              },
                              {
                                  "text": "ahora"
                              }
                          ]
                      },
                      "btnFeatures": {
                          "fgColor": "#ecaf2a",
                          "texts": [
                              {
                                  "text": "¿Qué incluye?"
                              },
                              {
                                  "text": "¿QUÉ INCLUYE?"
                              }
                          ]
                      },
                      "btnSuscription": {
                          "fgColor": "#981C15",
                          "texts": [
                              {
                                  "text": "SUSCRÍBETE",
                                  "color": "#FFFFFF"
                              }
                          ]
                      }
                  },
                  "_links": {
                      "checkout": {
                          "href": "/services/payway/workflowstart?object_type=A&offer_id=14327289&suscription_id=980&device_category=tv&device_manufacturer=lg&device_model=web0s&device_type=tv&region=mexico"
                      }
                  }
              },
              {
                  "producttype": "FOX",
                  "price": {
                      "currency": "$",
                      "amount": 1149,
                      "regular_amount": 1149,
                      "price_before_tax": null
                  },
                  "assets": {
                      "background": {
                          "default": "http://clarovideocdn3.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo.png?1698877547",
                          "124x295": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_124x295.png?1698877607",
                          "135x288": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_135x288.png?1698877669",
                          "165x394": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_165x394.png?1698877722",
                          "230x491": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_230x491.png?1698877778",
                          "248x590": "http://clarovideocdn1.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_248x590.png?1698877834",
                          "aaf": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenFondo_aaf.png?1698877877"
                      },
                      "logo": {
                          "124x32": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_124x32.png?1698877920",
                          "135x40": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_135x40.png?1698877962",
                          "165x40": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_165x40.png?1698878089",
                          "230x64": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_230x64.png?1698878124",
                          "248x64": "http://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_248x64.png?1698878283",
                          "aaf": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_aaf.png?1698878314",
                          "adr": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_adr.png?1698878349",
                          "ios": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_ios.png?1698878435",
                          "web": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_web.png?1698878463",
                          "win": "http://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_win.png?1698878487",
                          "xbox": "http://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_mexico/SelectorDePlan_Tarjeta_FOX/ImagenLogo_xbox.png?1698878519"
                      }
                  },
                  "translation": {
                      "labelTemporality": "/ año",
                      "labelTaxes": "IVA incluido",
                      "labelBeforeTaxes": null,
                      "txtPromo": {
                          "texts": [
                              {
                                  "text": "Disfruta este"
                              },
                              {
                                  "text": "canal premium"
                              }
                          ]
                      },
                      "btnFeatures": {
                          "fgColor": "#ECAF2A",
                          "texts": [
                              {
                                  "text": "¿Qué incluye?"
                              },
                              {
                                  "text": "¿QUÉ INCLUYE?"
                              }
                          ]
                      },
                      "btnSuscription": {
                          "fgColor": "#002885",
                          "texts": [
                              {
                                  "text": "SUSCRÍBETE",
                                  "color": "#FFFFFF"
                              }
                          ]
                      }
                  },
                  "_links": {
                      "checkout": {
                          "href": "/services/payway/workflowstart?object_type=A&offer_id=********&suscription_id=3200&device_category=tv&device_manufacturer=lg&device_model=web0s&device_type=tv&region=mexico"
                      }
                  }
              } 
          ]
      }
      },
      initialReducer: {
        appMetaData: {
          myaccount_configuration: JSON.stringify({
            default: {
              suscription: [{ provider: 'test_product' }]
            }
          }),
          translations: JSON.stringify({
            language: {
              en: {
                planSelector_access_title_label: 'Test Title',
                subscription_premium_monthly_label: 'Monthly',
                premium_subscriptionDescription_costTaxIncluded_label: 'Tax included',
                transactional_promotion_text_subscription_plan_tryInvite_description: 'Try for free',
                subscribe_now: 'Subscribe Now',
                'This is a very long text that should be truncated in the UI': 'This is a very long text that should be truncated in the UI',
                price_subscriptionDescription_slash_label: '/',
                style_hbo: '#FF0000',
                style_hbo_text: '#FFFFFF'
              }
            }
          })
        }
      }
    });

    require('react-redux').useSelector.mockImplementation(callback => {
      return callback(longTextStore.getState());
    });

    const { container } = render(
      <Provider store={longTextStore}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    const subscriptionCard = container.querySelector('.add-subscription-card');
    fireEvent.focus(subscriptionCard);
    const viewButtonContent = container.querySelector('.view-button-sub-div span');
    expect(viewButtonContent.textContent).toBe('¿QUÉ INCLUYE?');
  });

  test('handles Samsung TV back key navigation', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockImplementation(key => {
          if (key === 'ColorF2Yellow') return { code: 403 };
          if (key === 'ColorF3Blue') return { code: 406 };
          return { code: 0 };
        })
      }
    };
    require('react-router-dom').useLocation.mockReturnValue({
      state: {
        pageName: 'home'
      }
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    fireEvent.keyDown(document, { keyCode: 10009 });
    expect(mockNavigate).toHaveBeenCalledWith('/home');
    delete global.tizen;
  });

  test('handles Samsung TV navigation from non-home page', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockImplementation(key => {
          if (key === 'ColorF2Yellow') return { code: 403 };
          if (key === 'ColorF3Blue') return { code: 406 };
          return { code: 0 };
        })
      }
    };

    require('react-router-dom').useLocation.mockReturnValue({
      state: {
        pageName: '/details',
        data: { testData: 'test' },
        vodData: { testVod: 'vodTest' },
        previousPage: '/dashboard'
      }
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    fireEvent.keyDown(document, { keyCode: 403 });
    expect(mockNavigate).toHaveBeenCalledWith('/details', {
      state: {
        data: { testData: 'test' },
        vodData: { testVod: 'vodTest' },
        pageName: '/dashboard'
      }
    });
    delete global.tizen;
  });

  test('handles Samsung TV blue key (view details)', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockImplementation(key => {
          if (key === 'ColorF2Yellow') return { code: 403 };
          if (key === 'ColorF3Blue') return { code: 406 };
          return { code: 0 };
        })
      }
    };

    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    const subscriptionCard = container.querySelector('.add-subscription-card');
    fireEvent.focus(subscriptionCard);
    mockDispatch.mockClear();
    mockNavigate.mockClear();
    fireEvent.keyDown(document, { keyCode: 406 });

    expect(mockNavigate).toHaveBeenCalledWith('/my-settings/my-subscriptions/add-subscriptions/viewDetails',
      expect.objectContaining({
        state: expect.objectContaining({
          pageName: '/home'
        })
      })
    );
    delete global.tizen;
  });

  test('handles LG TV back key navigation', () => {
    require('react-router-dom').useLocation.mockReturnValue({
      state: {
        pageName: 'home'
      }
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    fireEvent.keyDown(document, { keyCode: 461 });
    expect(mockNavigate).toHaveBeenCalledWith('/home');

    mockNavigate.mockClear();
    fireEvent.keyDown(document, { keyCode: 405 });

    expect(mockNavigate).toHaveBeenCalledWith('/home');
  });

  test('handles LG TV navigation from non-home page', () => {
    require('react-router-dom').useLocation.mockReturnValue({
      state: {
        pageName: '/movie-details',
        data: { movieId: '12345' },
        vodData: { streamUrl: 'test-stream' },
        previousPage: '/movies'
      }
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    fireEvent.keyDown(document, { keyCode: 461 });
    expect(mockNavigate).toHaveBeenCalledWith('/movie-details', {
      state: {
        data: { movieId: '12345' },
        vodData: { streamUrl: 'test-stream' },
        pageName: '/movies'
      }
    });
  });

  test('handles LG TV blue key (view details)', () => {
    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );

    const subscriptionCard = container.querySelector('.add-subscription-card');
    fireEvent.focus(subscriptionCard);
    mockDispatch.mockClear();
    mockNavigate.mockClear();
    fireEvent.keyDown(document, { keyCode: 406 });
    expect(mockNavigate).toHaveBeenCalledWith('/my-settings/my-subscriptions/add-subscriptions/viewDetails',
      expect.objectContaining({
        state: expect.objectContaining({
          pageName: '/home'
        })
      })
    );
  });

  test('handles planFocus properly', () => {
    const { container } = render(
      <Provider store={store}>
        <BrowserRouter>
          <AddSubscriptions />
        </BrowserRouter>
      </Provider>
    );
    const card = container.querySelector('.add-subscription-card');
    const mockEvent = { preventDefault: jest.fn() };
    const component = container.firstChild;
    fireEvent.focus(card);
    expect(container.querySelector('.sub-view-plan-button')).toBeInTheDocument();
    const event = { preventDefault: jest.fn() };
    fireEvent.blur(card);
    expect(container.querySelector('.sub-view-plan-button')).toBeInTheDocument();
  });
});