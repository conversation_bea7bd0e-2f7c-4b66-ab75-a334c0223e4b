import React from 'react'
import { fireEvent, queryByAttribute, render } from '@testing-library/react/'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import { BrowserRouter as Router } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import { fromJS } from 'immutable'
import LoginTermsAndCondition from './LoginTermsAndCondition'

const initialState = fromJS({})
const mockStore = configureStore([])
const history = createHistory()
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
)
export const renderWithState = ui => {
    return render(ui, { wrapper: Wrapper })
}
const loginsuccessmock = {
    "status": "0"
}
describe('LoginTermsAndCondition page test', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('it should render the LoginTermsAndCondition Page', () => {
        initialState.login = {
            loginSuccess: loginsuccessmock
        }
        initialState.login = {
            loginSuccess: {
                response:{
                    gamification_id:'00987'
                }
            }
        }
        renderWithState(<LoginTermsAndCondition />)
    })
    test('onclick of checkbox', () => {
        const { container } = renderWithState(<LoginTermsAndCondition />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'checkbox');
        fireEvent.focus(scroll)
        fireEvent.blur(scroll)
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('it should render onclick termsandcondition', () => {
        const { container } = renderWithState(<LoginTermsAndCondition />)
        const getById = queryByAttribute.bind(null, 'id')
        const scroll = getById(container, 'termsAndCondition')
        fireEvent.keyUp(scroll, { key: "461" })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('it should render onclick privacypolicy', () => {
        const { container } = renderWithState(<LoginTermsAndCondition />)
        const getById = queryByAttribute.bind(null, 'id')
        const scroll = getById(container, 'privacyPolicy')
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    test('it should render onclick confirm button', () => {
        const { container } = renderWithState(<LoginTermsAndCondition />)
        const getById = queryByAttribute.bind(null, 'id')
        const scroll = getById(container, 'confirm')
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})