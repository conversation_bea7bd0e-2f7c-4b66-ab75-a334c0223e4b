import subMenuDataSlice, {
    getSubMenu, getSubMenuSuccess, getSubMenuFailure, subMenuRailCardsFailure, subMenuNextRailCards,
    subMenuRailCardsdata, subMenuRailIndex, subMenuRailCards, railsRes,
    filterDadta, getNewRail, getFilterrail, getclickFilterrail, getcurrentindex, getTabvalue,
    getsubmenuval, getFilterCurrentFocus
} from './subMenuDataSlice';

const initialState = {
    genre: {},
    railListurl: {},
    raildata: {},
    loading: false,
    error: {},
    submenudata: false,
    index: 0,
    totalrailcard: 0,
    filtercards: false,
    filterdata: {},
    filterclickdata: false,
    cuerrntindex: '',
    tabvalue: '',
    submenu: false,
    filterbackfocus: false
}

describe('subMenuDataSlice reducer', () => {
    it('should return initial state', () => {
        expect(subMenuDataSlice(undefined, {})).toEqual(initialState)
     });

    it('should handle getSubMenu action', () => {
        expect(subMenuDataSlice(initialState, getSubMenu())).toEqual({
            ...initialState
        })
    });

    it('should handle getSubMenuSuccess action', () => {
        const action = {
            type: getSubMenuSuccess.type,
            payload:  ''
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            railListurl: action.payload 
        })
    });

    it('should handle getSubMenuFailure action', () => {
        const action = {
            type: getSubMenuFailure.type,
            payload: ''
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            error: action.payload
        })
    });

    it('should handle subMenuRailCardsFailure action', () => {
        const action = {
            type: subMenuRailCardsFailure.type,
            payload: ''
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            error: action.payload
        })
    });
    

    it('should handle subMenuNextRailCards action', () => {
        const action = {
            type: subMenuNextRailCards.type,
            payload: 'subMenuNextRailCards'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState
        })
    });


    it('should handle subMenuRailCardsdata action', () => {
        const action = {
            type: subMenuRailCardsdata.type,
            payload: 'subMenuRailCardsdata'
        }
        expect(subMenuDataSlice(initialState,action)).toEqual({
           ...initialState,
           submenudata: action.payload 
        })
    });

    it('should handle subMenuRailIndex action', () => {
        const action = {
            type: subMenuRailIndex.type,
            payload: ''
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            index: action.payload
        })
    });

    it('should handle railsRes action', () => {
        const action = {
            type: railsRes.type,
            payload: ''
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            // index: action.payload
            totalrailcard: '',
            raildata: { assets: undefined }
        })
    });

    it('should handle subMenuRailCards action', () => {
        const action = {
            type: subMenuRailCards.type,
            payload: ''
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState 
        })
    });

    it('should handle filterDadta action', () => {
        const action = {    
            type: filterDadta.type,
            payload: 'filterDadta'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            filtercards: action.payload
        }) 
    });

    it('should handle getNewRail action', () => {
        const action = {
            type: getNewRail.type,
            payload: 'getNewRail'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
        })
    });

    it('should handle getFilterrail action', () => {
        const action = {
            type: getFilterrail.type,
            payload: 'getFilterrail'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            filterdata: action.payload 
        })
    });

    it('should handle getclickFilterrail action', () => {
        const action = {
            type: getclickFilterrail.type,
            payload: 'getclickFilterrail'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            filterclickdata: action.payload
        })
    });

    it('should handle getcurrentindex action', () => {
        const action = {
            type: getcurrentindex.type,
            payload: 'getcurrentindex'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            cuerrntindex: action.payload
        })
    });

    it('should handle getTabvalue action', () => {
        const action = {
            type: getTabvalue.type,
            payload: 'getTabvalue' 
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            tabvalue: action.payload    
        })
    });

    it('should handle getsubmenuval action', () => {
        const action = {
            type: getsubmenuval.type,
            payload: 'getsubmenuval'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            submenu: action.payload 
        })
    });

    it('should handle getFilterCurrentFocus action', () => {
        const action = {
            type: getFilterCurrentFocus.type,
            payload: 'getFilterCurrentFocus'
        }
        expect(subMenuDataSlice(initialState, action)).toEqual({
            ...initialState,
            filterbackfocus: action.payload  
        })
    });

})