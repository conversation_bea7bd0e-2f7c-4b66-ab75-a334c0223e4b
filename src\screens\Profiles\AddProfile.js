import React, { useState, useEffect, useRef, useCallback } from 'react'
import './AddProfile.scss'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  getProfileAvatarData,
  getProfileData,
  getProfileReadData
} from '../../store/slices/ProfileSlice'
import { useDispatch, useSelector } from 'react-redux'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import AlphaNumeric from '../Keyboard/AlphaNumeric'
import AlphaNumericKeyboard from '../Keyboard/AlphaNumericKeboard'
import { pushAddProfileEvent, pushScreenViewEvent } from '../../GoogleAnalytics'
import {
  clearFPNotification,
  getCheckControlPin,
  getClearAllSettingsState,
  setControlPin,
  setLastTouch,
  setlastTypedText
} from '../../store/slices/settingsSlice'

const AddProfile = () => {
  const { state } = useLocation()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const data  = state
  const saveBtnRef = useRef(null)
  const numRef = useRef(null)
  const abcRef = useRef(null)
  const borrarRef = useRef(null)
  const vaciarRef = useRef(null)

  const userProfileData = useSelector(state => state?.profile?.userProfile)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )
  const profileDataRedux = useSelector(
    state => state?.profile?.profileCreateData
  )
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles?.[0]?.parental
        ?.active
  )
  const securityPin = useSelector(
    state => state?.settingsReducer?.setControlPinData
  )
  const verifySecurityPin = useSelector(
    state => state?.settingsReducer?.checkControlPin
  )
  const remindSecurityPin = useSelector(
    state => state?.settingsReducer?.remindControlPin?.response?.email_sent
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')
  const [inputValue, setInputValue] = useState('')
  const [isChecked, setIsChecked] = useState(false)
  const [showKidsPopup, setShowKidsPopup] = useState(false)
  const [showInputPopup, setShowInputPopup] = useState(false)
  const [pinCreated, setPinCreated] = useState(false)
  const [selectedImage, setSelectedImage] = useState('')
  const [eyeFocused, setEyeFocused] = useState(false)
  const [buttonDisable, setButtonDisable] = useState(true)
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [showSaveBtn, setShowSaveBtn] = useState(false)
  const [focusedIdx, setFocusedIdx] = useState(0)
  const [pin, setPin] = useState(new Array(6).fill(''))
  const [pinInvalid, setPinInvalid] = useState('')
  const [showNotification, setShowNotification] = useState(false)
  const inputRef = useRef(null)
  const blinkerText = useRef()
  const chooseProfileButtonRef = useRef(null)

  const [currentButtonFocus, setCurrentButtonFocus] = useState('profilename')
  const [showError, setShowError] = useState(false)
  const [isFocused, setIsFocused] = useState(false)

  const moduloName = translations?.language?.[region]
    ?.addProfile_access_title_label
    ? translations?.language?.[region]?.addProfile_access_title_label
    : 'addProfile_access_title_label'

  const addButton = translations?.language?.[region]
    ?.addProfile_access_option_button_save
    ? translations?.language?.[region]?.addProfile_access_option_button_save
    : 'addProfile_access_option_button_save'

  const deleteButton = translations?.language?.[region]
    ?.userProfile_password_option_button_cancel
    ? translations?.language?.[region]
        ?.userProfile_password_option_button_cancel
    : 'userProfile_password_option_button_cancel'

  useEffect(() => {
    // Set focus on the choose profile button when component mounts for initial focus
    if (chooseProfileButtonRef.current) {
      chooseProfileButtonRef.current.focus()
    }
    setInputValue(state?.username || data?.username)
    setSelectedImage(
      state?.selectedProf?.length > 1
        ? state?.selectedProf
        : data?.user_image || 'images/Profile_Icons/profile_image.png'
    )
    setShowSaveBtn(true)
    pushScreenViewEvent({screenName:'add_profile',screenData:loginInfo ?? registerInfo, prevScreenName:'watching_profile'})
  }, []) // Empty dependency array means this runs once on mount


  useEffect(() => {
    setPinCreated(securityPinCheck)
  }, [securityPinCheck])

  useEffect(() => {
    if (verifySecurityPin?.msg === 'OK') {
      createProfile()
    } else if (verifySecurityPin?.msg === 'ERROR') {
      setButtonDisable(true)
      setFocusedIdx(0)
      setPinInvalid(verifySecurityPin?.errors)
      dispatch(getClearAllSettingsState())
      setPin(new Array(6).fill(''))
      setKeyboardFocus(true)
      setButtonDisable(true)
      setShowSaveBtn(false)
    }
  }, [verifySecurityPin])

  useEffect(() => {
    if (remindSecurityPin) {
      dispatch(clearFPNotification())
      setShowNotification(true)
      setTimeout(() => {
        setShowNotification(false)
      }, 4000)
    }
  }, [remindSecurityPin])

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate('/watchprofile')
    }
  }

  const handleLgkey = keycode => {
    if (keycode === 405 || keycode === 461 || keycode === 'back') {
      navigate('/watchprofile')
    }
  }

  const handleInputChange = event => {
    setCurrentButtonFocus('keyBoard')
    let newValue = event
    newValue = newValue.replace(/^\s+-/, '').replace(/[^\w\s-.@_+ñ]/gi, '')

     // Check for disallowed special characters or substrings
    const hasForbiddenChars = /[@_\-.]|(\.ar|\.hotmail\.com|\.gmail\.com)/.test(newValue)

    if (hasForbiddenChars) {
      setShowError(true)
    } else {
      setShowError(false)
    }


    if (newValue.length <= 25  && !hasForbiddenChars) {
      const cleanValue = newValue.replace(/\|$/, '');
      setInputValue(cleanValue);
       dispatch(setlastTypedText(cleanValue))
    }
  }

  useEffect(() => {
    let isBlinking = true;
    
    const handleBlinker = () => {
      const prfNameCheck = document.querySelector('.profile-name-input');
      if (prfNameCheck) {
        const baseValue = inputValue || '';
        prfNameCheck.value = isBlinking ? baseValue + '|' : baseValue;
        isBlinking = !isBlinking;
      }
    };
  
    blinkerText.current = setInterval(handleBlinker, 700);
  
  
    return () => {
      if (blinkerText.current) {
        clearInterval(blinkerText.current);
      }
    };
  }, [inputValue]); 

  useEffect(() => {
    if (isChecked) {
      setShowSaveBtn(false)
    }

    if (!isChecked) {
      setPin(new Array(6).fill(''))
      setFocusedIdx(0)
      setShowSaveBtn(true)
    }
  }, [isChecked])

  useEffect(() => {
    if (
      securityPin?.profiles?.[0]?.parental.active &&
      securityPin?.profiles?.[0]?.channel.active
    ) {
      createProfile()
    }
  }, [securityPin])

  useEffect(() => {
    if (
      pin &&
      pin[0].length > 0 &&
      pin[1].length > 0 &&
      pin[2].length > 0 &&
      pin[4].length > 0 &&
      pin[5].length > 0
    ) {
      setButtonDisable(false)
    } else {
      setButtonDisable(true)
    }
  }, [pin])

  const inputFocused = () => {
    pinRef?.current[focusedIdx]?.focus()
  }

  const createProfile = () => {
    const payload = {
      hks: loginInfo
        ? loginInfo?.session_stringvalue
        : registerInfo?.session_stringvalue,
      userid: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
      firstname: inputValue,
      user_token: loginInfo ? loginInfo?.user_token : registerInfo?.user_token,
      user_image: selectedImage,
      is_kids: isChecked
    }
    dispatch(getProfileData(payload))
    //GA for profile event
     pushAddProfileEvent(loginInfo,addButton?.toLowerCase(),moduloName?.toLowerCase())
  }

  const handle_cancel = () => {
    dispatch(setlastTypedText(''))
    navigate('/watchprofile', {
      state: { data: '', page: 'home', img: userProfileData }
    })
    // GA for profile event
    pushAddProfileEvent(loginInfo,deleteButton?.toLowerCase(),moduloName?.toLowerCase())
  }
  const handle_profimage = () => {
    navigate('/chooseprofile', {
      state: { 
        pageName: '/addprofile',
        data: { ...data, user_image: selectedImage, username: inputValue }
      },
      replace: true
    })
    dispatch(
      getProfileAvatarData({
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue
      })
    )
  }

  useEffect(() => {
    if (profileDataRedux?.msg === 'OK') {
      const payload = {
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue,
        userid: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
        gamificationid: loginInfo
          ? loginInfo?.gamification_id
          : registerInfo?.gamification_id,
        token: loginInfo ? loginInfo?.user_token : registerInfo?.user_token,
        lasttouch: profileDataRedux?.response?.lasttouch?.profile
      }
      dispatch(setLastTouch(profileDataRedux?.response?.lasttouch?.profile))
      dispatch(getProfileReadData(payload))
      navigate('/watchprofile', {
        state: {
          data: 'Profile Created',
          userName: profileDataRedux?.response?.firstname
        },
        replace: true
      })
    }
  }, [profileDataRedux])

  const handleSave = () => {
    if (pinCreated) {
      const payload = {
        controlPIN: pin.join(''),
        userId: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue,
        parental: 1
      }
      dispatch(getCheckControlPin(payload))
    } else {
      const payload = {
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue,
        userId: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
        user_token: loginInfo
          ? loginInfo?.user_token
          : registerInfo?.user_token,
        parental: 1,
        purchase: 0,
        channel: 1,
        rating: 30,
        code: pin.join('')
      }
      dispatch(setControlPin(payload))
    }
  }
  useEffect(() => {
    const inputElement = inputRef.current
    const preventKeyboard = event => {
      event.preventDefault()
    }
    if (inputElement) {
      inputElement.addEventListener('focus', preventKeyboard)
    }
    return () => {
      if (inputElement) {
        inputElement.removeEventListener('focus', preventKeyboard)
      }
    }
  }, [])

  return (
    <div
      className="add-profile"
      style={{
        height:
          isChecked && !showSaveBtn ? '1500px' : isChecked ? '1300px' : '1080px'
      }}
    >
      <div className="profile-app-logo">
        <img
          src={'images/claro-profile-logo.png'}
          className="logo-img"
          alt="logo"
        />
        <button className="back-button focusable"
         id="addprofileback" 
         data-sn-down='#ChooseProfileText_Container_id'
         onClick={() => handleLgkey('back')}
         >
          <img
            src={'images/Profile_Icons/ic_shortcut_amarillo.png'}
            className="yellow-dot"
            alt="img not found"
          />
          <img
            src={'images/Profile_Icons/ic_shortcut_back.png'}
            className="back-arrow"
            alt="img not found"
          />
          <span className="back-button-text">
            {' '}
            {translations?.language?.[region]?.atv_back_notification}{' '}
          </span>
        </button>
      </div>
      <div>
        <div className="addprofile-container">
          <h5 className="add-watch-header">
            {moduloName}
          </h5>
        </div>
        {/* Here add profile code is present 1. virtual keyboard 2. change image and add name */}
        <div className="add-edit-container">
          <div className="left-container-virtual-keyboard">
            <AlphaNumericKeyboard
              type="alphaNumeric"
              onChange={handleInputChange}
              value={inputValue}
              autoFocus={keyboardFocus}
              name="add-profile"
            />
          </div>
          <div className="right-container-add-profile-cont">
            <div className="edit-image-container">
              <button
                className="prof-button"
              >
                <LazyLoadImage className="profile-image" src={selectedImage} />
              </button>
              <button
                id="ChooseProfileText_Container_id"
                ref={chooseProfileButtonRef}
                data-sn-down="#add-profile-name-input-container"
                className="choose-profile-text-container focusable"
                onClick={handle_profimage}
              >
                <span className="choose-profile-text" id="ChooseProfileText_Id">
                  {translations?.language?.[region]
                    ?.addProfile_access_option_button_avatarSelector
                    ? translations?.language?.[region]
                        ?.addProfile_access_option_button_avatarSelector
                    : 'addProfile_access_option_button_avatarSelector'}
                </span>
              </button>
            </div>

            <div className="profile-container">
              <div className="profile-name-container">
                <label className="profile-name">
                  {translations?.language?.[region]
                    ?.addProfile_access_nameProfile_label
                    ? translations?.language?.[region]
                        ?.addProfile_access_nameProfile_label
                    : 'addProfile_access_nameProfile_label'}
                </label>
              </div>
              <button
                className="profile-name-input-container focusable"
                id="add-profile-name-input-container"
                data-sn-up="#ChooseProfileText_Container_id"
                onFocus={() => {
                  setIsFocused(true)
                }}
                onBlur={() => {
                  setIsFocused(false)
                }}
              >
                <input
                  className="profile-name-input"
                  type="text"
                  name="profilename"
                  id="profilename"
                  ref={inputRef}
                  maxLength={25}
                  onChange={e => {
                    handleInputChange(e?.target?.value)
                  }}
                  value={inputValue}
                  onBlur={() => {
                    setShowInputPopup(false)
                    setEyeFocused(false)
                  }}
                  inputMode="none"
                  autoComplete="off"
                  onFocus={() => {
                    setShowInputPopup(true)
                    setEyeFocused(true)
                  }}
                  disabled
                />
              </button>
              {showError && (
                <div className="error-popup">
                  {/* No se permiten caracteres especiales. Ingresa sólo letras y números. */}
                  {translations?.language?.[region]?.USR_USR_00015
                    ? translations?.language?.[region]?.USR_USR_00015
                    : 'No se permiten caracteres especiales. Ingresa sólo letras y números.'}
                </div>
              )}
              {showKidsPopup ? (
                <span className="checkbox-popup">
                  <h5 className="kids-desc-title">
                    {translations?.language?.[region]?.kids_tooltip_title_label
                      ? translations?.language?.[region]
                          ?.kids_tooltip_title_label
                      : 'kids_tooltip_title_label'}
                  </h5>

                  <div className="kids-desc-container">
                    <ul className="kids-list">
                      <li className="kids-desc">
                        {translations?.language?.[region]
                          ?.kids_tooltip_parentalRestriction_label_validation
                          ? translations?.language?.[region]
                              ?.kids_tooltip_parentalRestriction_label_validation
                          : 'kids_tooltip_parentalRestriction_label_validation'}
                      </li>
                      <li className="kids-desc">
                        {translations?.language?.[region]
                          ?.kids_tooltip_additionalRestrictions_label_validation
                          ? translations?.language?.[region]
                              ?.kids_tooltip_additionalRestrictions_label_validation
                          : 'kids_tooltip_additionalRestrictions_label_validation'}
                      </li>
                    </ul>
                  </div>
                </span>
              ) : null}
            </div>

            <div className="profile-save-button">
              <button
                autoFocus
                ref={saveBtnRef}
                disabled={inputValue && inputValue.length > 0 ? false : true}
                onClick={isChecked ? handleSave : createProfile}
                className="profile-save focusable"
                data-sn-down="#bu"
                style={{
                  opacity: inputValue && inputValue.length > 0 ? '1' : '0.5'
                }}
              >
                <span className="add-save-btn">
                  {addButton}
                </span>
              </button>
              <button
                id="bu"
                className="add-cancel-btn focusable"
                onClick={handle_cancel}
                data-sn-down={true}
              >
                <span className="add-cancel-text">
                  {deleteButton}
                </span>
              </button>
            </div>

            {isChecked ? (
              <div
                style={{ margin: '30px 0px' }}
                onFocus={() => setShowSaveBtn(false)}
              ></div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AddProfile
