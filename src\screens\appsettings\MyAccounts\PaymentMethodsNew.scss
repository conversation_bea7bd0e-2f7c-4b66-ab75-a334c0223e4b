body {
  margin: 0px;
}

.payment-method-setting {
  width: var(--maxWidth);
  height: var(--maxHeight);
  display: flex;
  flex-direction: column;
  .upper-back-button-area {
    float: right;
    margin-right: 40px;
    margin-top: 30px;
    .upper-button-back {
      display: flex;
      flex-direction: row;
      align-items: center;
      // justify-content: space-evenly;
      border-radius: 6.6px;
      background-color: #2e303d;
      color: #ffffff;
      font-family: Roboto;
      font-size: 29.04px;
      font-weight: bold;
      float: right;
      width: 292px;
      line-height: 0px;
      &:focus {
        background-color: #c60000;
      }
      .yellow-indicator-back {
        height: 20px;
        width: 20px;
        margin-left: 20px;
      }

      .image-back {
        height: 24px;
        width: 30px;
        margin-left: 20px;
      }
      .text-back {
        margin-left: 20px;
      }
    }
  }
  .payment-method-title {
    width: 100%;
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 48px;
    text-align: center;
    margin-top: 55px;
  }

  .payment-method-title-2 {
    color: #FFFFFF;
    font-family: Robot<PERSON>;
    font-size: 29.04px;
    letter-spacing: 0;
    line-height: 29.04px;
  }

  .credit-card-title-2 {
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 29.04px;
    letter-spacing: 0;
    line-height: 29.04px;
    margin-top: 90px;
    text-align: center;
  }

  .payment-option-div-credit-card {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 35px;
    margin-bottom: 120px;
    .credit-card-button {
      height: 264px;
      width: 264px;
      border-radius: 3px;
      margin: 0px 34px;
      align-items: center;
      display: flex;
      transform: scale(1.07);
    }

    .credit-card-button > img {
      width: 264px;
      height: 264px;
    }

    .credit-card-button:focus {
      border: 4px solid #ffffff;
      border-radius: 3px;
      transform: scale(1.07);
    }
  }

  .payment-option-div {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin-top: 179px;
    margin-bottom: 200px;
    .payment-buttons {
      height: 264px;
      width: 264px;
      border-radius: 3px;
      margin: 0px 34px;
      justify-content: center;
      align-items: center;
      display: flex;
    }
    .payment-buttons:focus {
      border: 4px solid #ffffff;
      border-radius: 3px;
      transform: scale(1.07);
    }
  }
  .bottom-cancel-button-area {
    width: 100%;
    text-align: center;
  }

  #cardMaskedNumber {
    color: #ffffff;
    font-family: Roboto;
    font-size: 29.04px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 29.04px;
    position: relative;
    right: 190px;
    top: 90px;
  }

  #creditCardButtons {
    height: 225px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
  }

  #seleccionar {
    height: 72px;
    width: 719px;
    border-radius: 8.8px;
    background-color: #981c15;
    padding: 10px;
    &:focus {
      background-color: #981c15;
      height: 83px;
      width: 828px;
      color: #ffffff;
      font-size: 32px;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }
    .credit-card-seleccionar-text {
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }
  }

  #reemplazar {
    height: 72px;
    width: 719px;
    border-radius: 8.8px;
    background-color: #2e303d;
    padding: 10px;
    &:focus {
      background-color: #2e303d;
      height: 83px;
      width: 828px;
      color: #ffffff;
      font-size: 32px;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }
    .credit-card-reemplazar-text {
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }
  }

  #paymentMethodCancelButton {
    height: 72px;
    width: 504px;
    border-radius: 8.8px;
    background-color: #2e303d;
    padding: 10px;
    &:focus {
      background-color: #2e303d;
      height: 82.08px;
      width: 574.56px;
      color: #ffffff;
      font-size: 32px;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }
  }
  .payment-cancel-text {
    max-width: 160px;
  }
  .payment-button-image {
    height: 264px;
    width: 264px;
  }
}
