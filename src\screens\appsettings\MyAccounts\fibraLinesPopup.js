import React from 'react'
import './fibraLinesPopup.scss'

const FibraLinesPopup = ({
  onClose,
  fibraLineResponse,
  updateFibraLineupValue
}) => {
  return (
    <div className="popup-overlay">
      <div className="popup-body">
        {fibraLineResponse?.length > 0 &&
          fibraLineResponse?.map((option, index) => (
            <button
              key={index}
              autoFocus={index == 0}
              onClick={() => {
                updateFibraLineupValue(option)
                onClose()
              }}
              className="popup-option-button focusable"
            >
              <span>{option.description}</span>
              <span>{option.service_id}</span>
            </button>
          ))}
      </div>
    </div>
  )
}

export default FibraLinesPopup
