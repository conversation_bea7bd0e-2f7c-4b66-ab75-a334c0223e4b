@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

$color-white: #ffffff;
$font-family-roboto: Roboto;
$position-absolute: absolute;
$text-wrap-nowrap: nowrap;
$font-weight-bold: bold;
$box-sizing-border: border-box;
$position-relative: relative;
$display-flex: flex;
$text-overflow-ellipsis: ellipsis;
$overflow-hidden: hidden;

.player-loader {
  width: 50%;
  height: 1080px;
  position: absolute;
  top: 0px;
  left: 0px;
  display: flex;
  margin: 0px auto;
  right: 0px;
  opacity: 1;
  z-index: 1;
}

.series-Main-Layout {
  width: 1920px;
  height: 945px;
  position: fixed;
  opacity: 1;

  .back-ground-images {
    background-size: 1432.67px 770px;
    height: 770px;
    width: 1920px;
    background-blend-mode: overlay;
    background-repeat: no-repeat;
    background-position: right;

    .series-layout {
      display: flex;
      flex-direction: column;
      position: relative;
      left: 88px;

      .content-seriestitle {
        max-width: 998px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 64px;
        letter-spacing: 0;
        position: relative;
        line-height: 72px;
      }

      .vod-season-seriesdetails {
        height: 33px;
        width: 1085px;
        position: absolute;

        .seriescontent-name {
          height: 28px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 24px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 26px;
          margin-right: 24px;
        }

        .pipe {
          height: 28px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 24px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 26px;
          margin-right: 24px;
        }

        .seriesvod-genero {
          height: 28px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 24px;
          letter-spacing: 0;
          line-height: 26px;
          margin-right: 24px;
        }

        .seriesvod-duration-year {
          height: 28px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 24px;
          letter-spacing: 0;
          line-height: 26px;
          margin-right: 24px;
        }

        .seriesstroke {
          box-sizing: $box-sizing-border;
          border: 3px solid $color-white;
          border-radius: 3px;
        }

        .seriesvod-duration-ano {
          height: 32px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 24px;
          letter-spacing: 0.25px;
          line-height: 32px;
          margin-right: 24px;
          padding-left: 8px;
          padding-right: 8px;
        }

        .seriesduration-ano-copy {
          height: 28px;
          width: 114px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 24px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 26px;
        }
      }

      .largevod-season-seriesdetails {
        height: 85px;
        width: 1054px;
        display: flex;
        flex-direction: column;
        text-align: justify;
        justify-content: space-around;

        .largeseriescontent-name {
          white-space: nowrap;
          height: 28px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 24px;
          max-width: 1054px;
          font-weight: bold;
          letter-spacing: 0;
          line-height: 26px;
          text-overflow: $text-overflow-ellipsis;
        }

        .largedetails {
          display: flex;
          flex-direction: row;
          align-items: center;

          .largeseriesvod-genero {
            height: 28px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 24px;
            letter-spacing: 0;
            line-height: 26px;
          }

          .largeseriesvod-duration-year {
            height: 28px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 24px;
            letter-spacing: 0;
            line-height: 26px;
            margin-left: 24px;
          }

          .largeseriesstroke {
            height: 32px;
            border: solid #ffffff;
            border-radius: 3px;
            margin-left: 24px;

            .largeseriesvod-duration-ano {
              width: 110px;
              max-height: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
              color: $color-white;
              font-family: $font-family-roboto;
              font-size: 24px;
              letter-spacing: 0.25px;
              line-height: 32px;
            }
          }

          .largeseriesduration-ano-copy {
            height: 28px;
            width: 114px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 24px;
            font-weight: $font-weight-bold;
            letter-spacing: 0;
            line-height: 26px;
            margin-left: 24px;
          }
        }
      }
    }

    .seriesvod-description-layouts {
      max-width: 1085px;
      display: flex;
      flex-direction: column;
      gap: 20px;
      top: 0px;
      position: relative;
      left: 88px;

      .EpisodeDetails {
        max-height: 30px;
        max-width: 1054px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 26px;
        text-align: justify;
        position: absolute;
        letter-spacing: 0;
        line-height: 29px;
        overflow: $overflow-hidden;
        text-overflow: $text-overflow-ellipsis;
        margin-top: 38px;
      }

      .episode-title {
        color: #ffffff;
        font-family: Roboto;
        font-size: 26px;
        font-weight: bold;
      }

      .seriesvod-description {
        max-height: 120px;
        max-width: 1054px;
        color: #ffffff;
        position: absolute;
        font-family: Roboto;
        text-align: left;
        top: 86px;
        font-size: 26px;
        letter-spacing: 0;
        line-height: 40px;
        overflow: $overflow-hidden;
        text-overflow: $text-overflow-ellipsis;
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-line-clamp: 3;
        /* Fallback for WebKit-based browsers */
        -webkit-box-orient: vertical;
        /* Fallback for WebKit-based browsers */
        -moz-box-orient: vertical;
        /* Fallback for Firefox */
        -ms-flex-direction: column;
        /* Fallback for Internet Explorer */
        flex-direction: column;
        /* Standard */
        white-space: normal;
        /* Fallback for browsers supporting flexbox */
      }
    }

    .seriesltv-progressbar-position {
      width: 1080px;
      display: flex;
      position: relative;
      height: 26px;
      left: 88px
    }

    .promo-card {
      height: 24px;
      position: relative;
      right: 380px;
      top: 72px;
      text-align: justify;
      left: 88px;

      .promo-first-title {
        color: $color-white;
        font-family: Roboto;
        font-size: 32px;
        font-weight: bold;
        letter-spacing: -0.7px;
        line-height: 24px;
      }

      .promo-second-title {
        color: #00aaff;
        font-family: $font-family-roboto;
        font-size: 32px;
        font-weight: $font-weight-bold;
        letter-spacing: -0.7px;
        line-height: 24px;
      }
    }

    .button-layout {
      display: flex;
      justify-content: flex-start;
      position: relative;
      flex-direction: row;
      align-items: center;
      height: 130px;
      left: 88px;

      .total-wrapper {
        display: flex;
        align-items: center;
      }
.seriesicon-container{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      border-radius: 7px;
      .seriesicons-vcards {
        height: 64px;
        width: 64px;
        position: relative;
        display: flex;

        &.play-focused {
          top: 19px;
          left: 40px;
        }

        &.play-unfocused {
          top: 22px;
          left: 43px;
        }
      }
    }
      .button-text {
        height: 60px;
        width: 125px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 23px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 30px;
        text-align: center;
        margin-top: 8px;
        margin-left: 10px;

        &:hover {
          display: block;
        }
      }

      .button-textfocus-out {
        height: 60px;
        width: 125px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 23px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 30px;
        text-align: center;
        margin-top: 8px;
        margin-left: 10px;
      }

      .button-reproducer-copy1 {
        height: 104px;
        width: 152px;
        border-radius: 7px;
        background-color: $color-white;
        border-color: #1a1a1a;
        box-shadow: 0 0 0 2px $color-white;
        border-radius: 5px;
      }

      .button-Focus {
        height: 104px;
        width: 152px;
        border-radius: 7px;
        box-shadow: 0 0 0 3px $color-white;
        border: 5px solid white;
        border-color: #1a1a1a;
      }

      .button-icons {
        height: 96px;
        width: 144px;
        border-radius: 7px;
        margin-right: 24px;
        border: 4px solid transparent;
        background-color: #282828;
        &:focus {
          box-shadow: 0 0 0 3px $color-white;
          border-color:#121212;
        }

        &.active {
          background-color: $color-white;
          // box-shadow: 0 0 0 3px $color-white;
          border-color: #1a1a1a;
        }
      }

      .large-subscibre {
        height: 96px;
        border-radius: 8px;
        margin-right: 24px;
        background-color: #ecaf2a;
        border: 4px solid transparent;

        &:focus {
          box-sizing: $box-sizing-border;
          box-shadow: 0 0 0 3px $color-white;
          height: 118px;
          border-color: #1a1a1a;
        }

        .whole-larger-text {
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 0px 10px;

          .large-subscribe-details-out {
            color: #1e1e1e;
            font-family: Roboto;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 32px;
          }

          .large-second-subscribe-details-out {
            height: 32px;
            color: #1e1e1e;
            font-family: Roboto;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 32px;
          }
        }
      }

      .button-large-subscribe {
        box-sizing: $box-sizing-border;
        height: 118px;
        width: 444px;
        border: 3px solid $color-white;
        border-radius: 12px;
        background-color: #ecaf2a;
        margin-right: 24px;
        border-color: #1a1a1a;
        box-shadow: 0 0 0 3px $color-white;
      }

      .button-large-subscribe-month-icon {
        height: 104px;
        width: 430px;
        border-radius: 8px;
        display: flex;
        margin-right: 24px;
        background-color: #ecaf2a;
      }

      .subscibr-month {
        height: 104px;
        width: 152px;
        border-radius: 7px;
        margin-right: 24px;
        border: 3px solid transparent !important;
        padding: 4px;

        &:focus {
          border: 3px solid $color-white !important;
        }
        &:focus > .whole-text {
          border-radius: 4px;
        }
      }

      .button-subscribe-month {
        box-sizing: $box-sizing-border;
        height: 104px;
        width: 152px;
        border: 3px solid $color-white;
        border-radius: 7px;
        margin-right: 24px;
        box-shadow: 0 0 0 3px $color-white;
      }

      .button-subscribe-month-icon {
        height: 104px;
        width: 152px;
        border-radius: 7px;
        margin-right: 24px;
        position: relative;
      }

      .whole-text {
        display: flex;
        flex-direction: column;
        text-align: center;
        align-items: center;
        justify-content: center;
        text-align: center;
        border-radius: 7px;
        height: 100%;

        .price-details {
          height: 32px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 32px;
          text-align: center;
        }

        .price-details-out {
          height: 32px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 32px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 32px;
          text-align: center;
        }

        .includio-text {
          height: 33px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 20px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 24px;
          text-align: center;
        }

        .includio-text-out {
          height: 33px;
          color: #ffffff;
          font-family: Roboto;
          font-size: 20px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 24px;
          text-align: center;
        }
      }

      .button-subscribe-text {
        height: 60px;
        width: 152px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 23px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 30px;
        text-align: center;
        position: relative;
        margin-top: 8px;
        text-transform: capitalize;
      }

      .button-subscribe-textfocus-out {
        height: 60px;
        width: 152px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 23px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 30px;
        text-align: center;
        position: relative;
        margin-top: 8px;
        text-transform: capitalize;
      }
    }
    .disclaimer-text-vcardseries{
      width: 1048px;
      height:32px;
      margin-top: 186px;
      left: 88px;
      position: relative;
      color: #FFF;
      font-family: Roboto;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      opacity: 0.5;
    }
  }

  .tab-tray-serieswrapper {
    display: flex;
    flex-direction: row;
    top: 40px;
    justify-content: space-between;
    left: 88px;
    text-align: center;
    position: relative;
    width: 1100px;

    .firstseriesrecommended-tab-focus {
      height: 80px;
      width: 340px;
      border-radius: 7px;
      background-color: #981c15;
      font-weight: $font-weight-bold;
      border: 4px solid transparent !important;

      &:focus {
        border: 4px solid $color-white !important;
      }
    }

    .firstseason-title,
    .firstrecommend-title,
    .firstmoreinfo-title {
      height: 33px;
      width: 308px;
      color: $color-white;
      font-family: $font-family-roboto;
      font-size: 28px;
      letter-spacing: 0;
      line-height: 30px;
    }

    .firstmore-information-tab {
      height: 80px;
      width: 340px;
      border-radius: 7px;
      background-color: #1e1e1e;
      border: 3px solid transparent !important;

      &:focus {
        opacity: 1;
        border: 3px solid #fff !important;
      }

      &:active {
        background-color: #981c15;
      }
    }

    .firstrecommended-tab {
      height: 80px;
      width: 340px;
      border-radius: 7px;
      background-color: #1e1e1e;
      border: 3px solid $color-white;
      border-radius: 5px;
    }
  }

  .seriestab-tray-serieswrapper {
    width: 1900px;
    height: 100%;
    position: fixed;
    background: transparent
      linear-gradient(
        90deg,
        #121212 0%,
        rgba(18, 18, 18, 0.9490196078) 50%,
        rgba(18, 18, 18, 0) 100%
      )
      0% 0% no-repeat padding-box;

    .buttonslayoutthree {
      margin-top: 40px;
      width: 1080px;
      display: flex;
      margin-left: 88px;
      justify-content: space-between;
      text-align: center;
      flex-direction: row;

      .secondrecommended-tab {
        height: 80px;
        width: 340px;
        border-radius: 7px;
        background-color: #1e1e1e;
        border: 2px solid $color-white;
        border-radius: 5px;
      }

      .seriesrecommended-tab-focus {
        width: 340px;
        border-radius: 7px;
        background-color: #981c15;
        border: 3px solid transparent !important;
        font-weight: bold;

        &:focus {
          border: 4px solid black !important;
          box-shadow: 0 0 0 3px $color-white !important;
        }
      }

      .Secondseasontitle,
      .secondRecommendtitle,
      .secondMoreinfotitle {
        height: 33px;
        width: 308px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 28px;
        letter-spacing: 0;
        line-height: 30px;
      }

      .secondmore-information-tab {
        height: 80px;
        width: 340px;
        border-radius: 7px;
        background-color: #1e1e1e;
        border: 3px solid transparent !important;

        &:focus {
          opacity: 1;
          border: 3px solid $color-white !important;
        }

        &:active {
          background-color: #981c15;
        }
      }
    }

    .TotalContainer {
      display: flex;
      flex-direction: row;
      width: 1680px;
      text-align: center;
      margin-left: 88px;
      // margin-top: 50px;
      position: fixed;
      bottom: 0;

      .seasoncontainer {
        width: 385px;
        height: 760px;
        display: flex;
        flex-direction: column;
        overflow-y: scroll;
        overflow-x: hidden;
        scroll-snap-type: y mandatory;

        .SeasonButtonseriesFocused {
          border-radius: 12px;
          opacity: 1;
          height: 94px !important;
          display: flex;
          border: 3px solid transparent !important;

          &:focus,
          &:active {
            opacity: 1;
            scroll-snap-align: end;
            border: 3px solid #ffffff !important;
          }

          .SeasonData {
            letter-spacing: 0px;
            font-size: 24px;
            line-height: 32px;
            font-family: $font-family-roboto;
            color: #ffffff;
            text-transform: capitalize;
            opacity: 1;
            margin-left: 47px;
          }

          .episode-count {
            letter-spacing: 0px;
            font-size: 21px;
            margin-top: 29px;
            margin-left: 10px;
            font-family: $font-family-roboto;
            line-height: 24px;
            color: #ffffff;
            text-transform: capitalize;
            opacity: 1;
          }
        }
      }

      @media screen and (max-width: 3840px) and (max-height: 2160px) {
        .totalepisodescontainer {
          display: grid;
          flex-direction: column;
          width: 1272px;
          margin-left: 15px;
          margin-top: 14px;
          max-height: 741px;
          overflow-y: scroll;
          scroll-snap-type: y mandatory;

          .episodecontainer {
            scroll-margin-top: 20px;
            margin-left: 10px;
            flex-direction: row;
            text-align: justify;
            justify-content: center;
            width: 1275px;
            height: 215px;
            margin-bottom: 40px;

            .initial-episode {
              width: 377px;
              height: 214px;
              padding: 4px;
              justify-content: center;
              position: relative;
              display: flex;

              &:focus {
                border: 3px solid #fff !important;
                scroll-snap-align: start;
              }

              .initial-episode:last-child {
                margin-bottom: 0px;
              }

              .episode-card-imgin-series {
                width: 377px;
                height: 214px;
                display: flex;
              }

              .episode-progress-bar {
                width: 85%;
                position: absolute;
                bottom: 30px;
                height: 0px;
                left: 27px;
              }
            }

            .episode-card-img-series {
              width: 377px;
              height: 214px;
              padding: 4px;
              justify-content: center;
              position: relative;
              display: flex;
              &:focus {
                border: 3px solid #fff !important;
                scroll-snap-align: center;
              }

              .episode-card-img-series:last-child {
                margin-bottom: 0px;
              }

              .episode-card-imgin-series {
                width: 377px;
                height: 214px;
                display: flex;
              }

              .episode-progress-bar {
                width: 85%;
                position: absolute;
                bottom: 30px;
                height: 0px;
                left: 27px;
              }
            }

            .episodeText {
              position: relative;
              display: flex;
              flex-direction: column;
              width: 863px;
              height: 214px;
              left: 40px;

              .EpisodeCardTitle {
                justify-content: space-between;
                height: 40px;
                color: #ffffff;
                margin: 0px;
                text-align: left;
                font-family: Roboto;
                font-size: 32px;
                font-weight: bold;
                letter-spacing: 0;
                line-height: 32px;
                overflow: $overflow-hidden;
                text-overflow: $text-overflow-ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
              }

              .EpisodeDesLayout {
                .EpisodeDescription {
                  height: 64px;
                  margin-right: 55px;
                  text-align: left;
                  color: #ffffff;
                  font-family: Roboto;
                  font-size: 24px;
                  letter-spacing: 0;
                  line-height: 32px;
                  overflow: $overflow-hidden;
                  text-overflow: $text-overflow-ellipsis;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                }
              }

              .moreinfoseason {
                height: 72px;
                width: 220px;
                border-radius: 7px;
                background-color: #1e1e1e;
                position: relative;
                border: 3px solid transparent !important;

                &:focus {
                  border: 3px solid #ffffff !important;
                }

                .moreinfotitleseriesseason {
                  height: 32px;
                  width: 188px;
                  margin: 0px auto;
                  color: #ffffff;
                  font-family: Roboto;
                  font-size: 24px;
                  letter-spacing: 0;
                  line-height: 32px;
                  text-align: center;
                }
              }
            }
          }

          .episodecontainer:last-child {
            margin-bottom: 0px;
          }
        }
      }
    }

    .RailLayout {
      display: flex;
      flex-direction: column;
      text-align: justify;
      margin-left: 88px;

      .SeasonHeader {
        height: 32px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 32px;
        letter-spacing: 0;
        line-height: 32px;
        margin-top: 55px;
        margin-bottom: 20px;
      }

      .MLTContainer {
        display: flex;
        overflow-x: scroll;
        align-items: center;
        scroll-snap-type: x mandatory;

        .MLTCard {
          opacity: 0.8;
          margin-right: 40px;
          position: relative;
          border: 4px solid transparent !important;

          &:focus {
            border-radius: 4px;
            opacity: 1;
            scroll-snap-align: end;
            border: 4px solid #fff !important;
          }

          .MLTCard:last-child :focus {
            margin-right: 0px;
          }

          .MLTCardImage {
            display: flex;
            height: 232px;
            width: 412px;
          }

          .MLTDetails {
            position: absolute;
            bottom: 22px;
            margin-left: 25px;
            width: 250px;
            display: none; // if required need to change entire div in future

            .MLTTitle {
              height: 35px;
              overflow: $overflow-hidden;
              text-overflow: $text-overflow-ellipsis;
              white-space: nowrap;
              color: #ffffff;
              font-family: Roboto;
              font-size: 30px;
              letter-spacing: 0;
              line-height: 33px;
            }
          }
        }
      }
    }

    .more-info-layouts {
      position: relative;
      display: flex;
      flex-direction: column;
      margin-left: 108px;
      bottom: 19px;

      .moreInfo-title {
        width: 1054px;
        color: $color-white;
        font-family: Roboto;
        font-size: 60px;
        letter-spacing: 0;
        line-height: 88px;
        text-overflow: ellipsis;
        margin-bottom: 72px;
        margin-top: 23px;
      }

      .moreInfo-description-layout {
        display: $display-flex;
        flex-direction: row;
        width: 1750px;

        .moreInfo-detail-layout {
          width: 1125px;
          height: auto;
          flex-direction: column;
          height: 489px;
          display: flex;
          justify-content: space-between;
          margin-right: 120px;
          overflow: hidden;

        

          .moreInfo-description-detail { 
            width: 1125px;
            height: 400px;
            color: #ffffff;
            font-family: Roboto;
            font-size: 32px;
            letter-spacing: 0;
            line-height: 48px;
            background-blend-mode: overlay;
            background-repeat: no-repeat;
            overflow-y: scroll  !important;
            text-align: left;
            padding-bottom: 80px;

            &:focus {
              background-color: #28292f;
            }

            &.faded::after {
              content: '';
              position: fixed;
              left: 110px;
              top: 760px;
              width: 1130px;
              height: 180px;
              background: linear-gradient(
                180deg,
                rgba(18, 18, 18, 0) 0%,
                #121212 100%
              );
              pointer-events: none;
            }
          }
          .moreInfo-description-detail::-webkit-scrollbar {
            width: 8px; 
            background-color: transparent; 
            display: flex;
          }
        
          .moreInfo-description-detail::-webkit-scrollbar-thumb {
            background-color: #2e303d; 
            height: 180px;
          }
        }
        
      }

      .casts-layout {
        display: flex;
        flex-direction: column;

        .casts-title {
          height: 32px;
          width: 113px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 32px;
          letter-spacing: 0;
          margin-bottom: 18px;
          margin-top: 48px;
        }

        .series-Casts {
          display: flex;
          flex-direction: row;
          overflow-x: scroll;
          overflow-y: hidden;
          justify-content: left;
          position: relative;
          scroll-snap-type: x mandatory;

          .mlts-block {
            margin-right: 30px;
           
            &:focus,
            &:active {
              border-radius: 5px;
              border: 5.6px solid $color-white;
              scroll-snap-align: end;
              opacity: 1;
              transform: scale(1);
              height: 491px;
            }

            .castcard-img {
              border-radius: 3px;
              background-size: 100% 100%;
              background-blend-mode: overlay;

              .cast-img {
                background: linear-gradient(
                  180deg,
                  rgba(0, 0, 0, 0) 0%,
                  #000000 100%
                );
                opacity: 1;
                height: 490px;
                width: 326px;
              }

              .cast-details {
                height: 107px;
                display: flex;
                flex-direction: column;
                position: absolute;
                top: 387px;
                width: 326px;
                background: linear-gradient(180deg, rgb(113 79 79 / 0%) 0%, #000000c7 100%);

                .cast-role {
                  height: 32px;
                  width: 325px;
                  color: #ffffff;
                  font-family: Roboto;
                  font-size: 28px;
                  letter-spacing: 0;
                  line-height: 48px;
                  text-align: center;
                }

                .cast-name {
                  height: 32px;
                  color: $color-white;
                  font-family: $font-family-roboto;
                  font-size: 28px;
                  font-weight: $font-weight-bold;
                  letter-spacing: 0;
                  line-height: 0;
                  text-align: center;
                }
              }
            }
          }

          .series-Casts .mlts-block:last-child {
            margin-right: 20px;
          }
        }
      }
    }

    .moreInfo-vod-detail {
      width: 460px;
      display: flex;
      flex-direction: column;

      .duration-div {
        width: 459px;
        margin-bottom: 80px;

        .moreInfo-duration {
          width: 459px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 36px;
          letter-spacing: 0;
          line-height: 40px;
        }

        .moreInfo-duration-copy {
          width: 459px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 36px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 39px;
        }
      }

      .year-div {
        width: 306px;
        margin-bottom: 80px;

        .moreInfo-year {
          width: 459px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 36px;
          letter-spacing: 0;
          line-height: 40px;
        }

        .moreInfo-year-copy {
          width: 459px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 36px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 39px;
        }
      }

      .generos-div {
        width: 306px;

        .generos {
          width: 459px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 36px;
          letter-spacing: 0;
          line-height: 40px;
        }

        .generos-copy {
          width: 460px;
          height: auto;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 36px;
          font-weight: $font-weight-bold;
          letter-spacing: 0;
          line-height: 39px;
        }
      }
    }
  }
}

.series-player-warning-container {
  text-align: left;
  opacity: 1;
  width: 1920px;
  height: 1080px;
  background: transparent
    linear-gradient(90deg, #121212 0%, #121212f2 27%, #03151f00 100%) 0% 0%
    no-repeat padding-box;
  z-index: 1;
  position: fixed;

  .series-player-warning-backScreen {
    box-sizing: $box-sizing-border;
    height: 62px;
    width: 306px;
    margin-top: 41px;
    margin-right: 30px;
    float: right;
    background-color: #2e303d;
    border: 4px solid transparent !important;

    &:focus {
      background: #981c15 0% 0% no-repeat padding-box;
      border: 4px solid #fff !important;
    }

    .series-back-img-icon {
      left: 17px;
    }

    .series-back-button-regresar-title {
      height: 33px;
      width: 208px;
      color: $color-white;
      font-family: $font-family-roboto;
      font-size: 28px;
      font-weight: $font-weight-bold;
      letter-spacing: 0;
      line-height: 30px;
      text-align: center;
    }
  }

  .series-player-warning-button-container-div {
    opacity: 1;
    z-index: 1;
    width: 1640px;
    display: $display-flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .series-player-warning-title {
      height: 50px;
      width: 523px;
      color: $color-white;
      font-family: $font-family-roboto;
      font-size: 50px;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
    }

    .series-player-warning-button-div {
      flex-direction: column;
      display: $display-flex;
      gap: 27px;
      margin-top: 50px;

      .series-player-warning-title-button {
        justify-content: center;
        background: #2e303d 0% 0% no-repeat padding-box;
        opacity: 1;
        height: 55.2px;
        width: 384px;
        border-radius: 4.6px;
        height: 102.41px;
        width: 496.75px;
        border: 4px solid transparent !important;

        &:focus {
          background: #981c15 0% 0% no-repeat padding-box;
          border: 4px solid #fff !important;
        }

        .series-player-warning-title-button-Contents {
          height: 29px;
          width: 356.17px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 25px;
          font-weight: $font-weight-bold;
          letter-spacing: -0.4px;
          line-height: 29px;
          text-align: center;
        }
      }
    }
  }
}

.vod-series-loader {
  position: absolute;
  top: 23%;
  right: 40%;
}
