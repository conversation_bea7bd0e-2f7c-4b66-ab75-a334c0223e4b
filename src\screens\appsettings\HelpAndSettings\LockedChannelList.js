import React, { useEffect } from 'react'
import './LockedChannels.scss'
import { useNavigate, useLocation } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { LazyLoadImage } from 'react-lazy-load-image-component'

const LockedChannelList = (props) => {

  const navigate = useNavigate()
  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const lockedChannelsList = useSelector(state => state?.settingsReducer?.lockedChannelsList?.response?.groups)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const channelNumber = [];
  var sortChannelsArray = [];

  lockedChannelsList?.length > 0 && lockedChannelsList?.map((each) => channelNumber.push({ 'channelNumber': each?.channel_number ? each?.channel_number : 0, 'data': each }))

  if (channelNumber?.length > 0) {
    sortChannelsArray = [...channelNumber].sort((a, b) => a.channelNumber - b.channelNumber)
  }

  useEffect(() => {
    sortChannelsArray?.length > 0 ?
      document.getElementById('lock0')?.focus()
      :
      document.getElementById('terminarClick')?.focus()
  }, [sortChannelsArray])

  const keypresshandler = (event) => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF1Green']);
      const codes = {
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener("keyup", keypresshandler)
    return () => {
      document.removeEventListener("keyup", keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.greencode === keycode || keycode === 10009) {
      props?.setScreenName('parentalControl')
      navigate('/settings/profile-settings', { state: { pageName: 'parentalControl', focusElement: 'lockChannelModificar' } })
    }
  }

  const handleLgkey = (keycode) => {
    if (keycode == 404 || keycode === 461 || keycode == 'backClick') {
      props?.setScreenName('parentalControl')
      navigate('/settings/profile-settings', { state: { pageName: 'parentalControl', focusElement: 'lockChannelModificar' } })
    }
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  };

  const handleUnlockChannel = (e, data, index) => {
    e.preventDefault();
    navigate('/settings/actionScreenSettings', { state: { data: data, pageName: 'parentalControl', subPage: 'lock', focusButtonId: `lock${index}` } })
  }

    useEffect(() => {
    if (state?.focusButtonId) {
        const button = document.getElementById(state?.focusButtonId);
        if (button) {
          button.focus();
        }
    }
   }, [state?.focusButtonId,sortChannelsArray]);

  return (
    <div className="app-css-locked-channel-list">
      <span className="locked-channel-title">Control Parental
        {truncateText('profiles_settings_parentalControl_slash_label', 2)}
        {truncateText('profiles_settings_parentalControl_blockedchannels_title', 30)}
      </span>
      <button className="locked-channel-button focusable" id='terminarClick' onClick={(e) => handleLgkey('backClick')}>
        <img src='images/green_shortcut.png' className='icon-green-lock-channel' />
        <span className="locked-channel-button-contents">{truncateText('profiles_settings_parentalControl_shortcut_finish', 30)}</span>
      </button>

      <hr className="horizontal-line" />
      <span className='locked-channel-subtitle'>{truncateText('blocked_channel_parental_control_settings_title', 30)}</span>

      <div className='locked-channels-container' >
        {sortChannelsArray?.length > 0 ? (
          sortChannelsArray?.map((item, index) => (
            <button className='locked-channels-card focusable' key={index} id={`lock${index}`} onClick={(e) => handleUnlockChannel(e, item, index)} >
              <span className='channel-number'>{item?.channelNumber}</span>
              <LazyLoadImage src={item?.data?.image_small} className="Channel-img" />
              <div className='unlock-channel-div'>
                <img className='lock-icon' src={'images/lock_icon_miniEPG.png'} />
                <span className='unlock-title'>{truncateText('profiles_settings_blockedchannels_unlock', 20)}</span>
              </div>
            </button>
          ))
        ) :
          lockedChannelsList?.length == 0 ? (
            <div className="empty-channel">
              <p className="empty-channel-txt">
                {truncateText('blocked_channel_parental_control_settings_subtitle', 70)}
              </p>
            </div>
          ) : null
        }
      </div>
    </div>
  )
}

export default React.memo(LockedChannelList)