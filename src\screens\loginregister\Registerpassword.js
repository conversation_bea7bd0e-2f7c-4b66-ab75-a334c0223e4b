import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { pushRegisterEvent,pushScreenViewEvent } from '../../GoogleAnalytics'
import '../loginregister/Registerpage.scss'

const RegisterPassword = () => {
    const navigate = useNavigate()
    const { state } = useLocation()
    const [type, setType] = useState('password')
    const [password, setPassword] = useState(state?.regpassword)
    const [lowerCase, setLowerCase] = useState(false)
    const [upperCase, setUpperCase] = useState(false)
    const [number, setNumber] = useState(false)
    const [mincharacter, setMincharacter] = useState(false)
    const [nospecial, setNospecial] = useState(true)
    const [showerrMsg, setShowerrMsg] = useState(false)
    const [errMsg, setErrMsg] = useState('')
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const region = localStorage.getItem('region')
    const translations =
        apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const apilanguage = translations?.language?.[region]
    const navigateBack = useSelector(state => state?.login?.RegBackNavigate)
    const startTimeRef = useRef(null)

    const focusFunction = () => {
        setShowerrMsg(true)
        setErrMsg('')
    }
    const blurFunction = () => {
        setShowerrMsg(false)
    }

    useEffect(() => {
      startTimeRef.current = Date.now()
      return () => {
      startTimeRef.current = null
     }
    }, [])

    const handleChange = value => {
        setPassword(value)
        const lower = new RegExp('(?=.*[a-z])')
        const upper = new RegExp('(?=.*[A-Z])')
        const number = new RegExp('(?=.*[0-9])')
        const length = new RegExp('(?=.{8,})')
        const specialchr = /^[a-zA-Z0-9]*$/;
        if (lower.test(value)) {
            setLowerCase(true)
        } else {
            setLowerCase(false)
        }
        if (upper.test(value)) {
            setUpperCase(true)
        } else {
            setUpperCase(false)
        }
        if (number.test(value)) {
            setNumber(true)
        } else {
            setNumber(false)
        }
        if (length.test(value)) {
            setMincharacter(true)
        } else {
            setMincharacter(false)
        }
        if (specialchr.test(value)) {
            setNospecial(true)
        } else {
            setNospecial(false)
        }
    }

    const navHome = e => {
      e.preventDefault()
      localStorage.setItem('pwd', password)
      const engagement_time_msec = Date.now() - startTimeRef.current
      //GA for sign_up event
      pushRegisterEvent(
        handleTranslationchange(
          'Onboarding_RegistroContrasenaRCU_Form_TextoBotonPrimario'
        )?.toLowerCase(),
        handleTranslationchange(
          'Onboarding_RegistroContrasenaRCU_Form_TextoTitulo'
        )?.toLowerCase(),
        engagement_time_msec
      )
      navigate('/register', {
        state: {
          focus: true,
          regpassword: password,
          regmail: state?.regmail,
          seriesEpisodeData: state?.seriesEpisodeData,
          fromDetailsPage: state?.fromDetailsPage,
          pageName: state?.pageName,
          page: 'registermail'
        }
      })
     startTimeRef.current = Date.now()
    }

    const keypresshandler = event => {
        if (event?.keyCode === 10009 || event?.keyCode === 461) {
            if (navigateBack) {
                navigate('/register', {
                    state: {
                        focus: true, 
                        regpassword: password, 
                        regmail: state?.regmail, 
                        seriesEpisodeData: state?.seriesEpisodeData,
                        fromDetailsPage: state?.fromDetailsPage,
                        pageName: state?.pageName,
                        page: 'registermail'
                    }
                })
            } else
                navigate('/regemail', {
                    state: { 
                        focus: true, 
                        regpassword: password, 
                        regmail: state?.regmail, 
                        seriesEpisodeData: state?.seriesEpisodeData,
                        fromDetailsPage: state?.fromDetailsPage,
                        pageName: state?.pageName,
                    }
                })
        }
        if (
            event?.keyCode == 13 ||
            event?.keyCode == 65376 ||
            event?.keyCode == 40
        ) {
            event.preventDefault()
            event.stopPropagation()
            if (event.target.id == 'hideId' && event?.keyCode == 13) {
                document.getElementById('hideId').focus({ focusVisible: true })
            } else {
                document.getElementById('reg-psd-btn').focus({ focusVisible: true })
            }
        } else if (event?.keyCode == 38) {
            event.preventDefault()
            event.stopPropagation()
            document.getElementById('password').focus({ focusVisible: true })
        } else if (
            event?.keyCode == 39 &&
            document.getElementById('password').selectionEnd ===
            document.getElementById('password').value?.length
        ) {
            document.getElementById('hideId').focus({ focusVisible: true })
        } else if (
            event?.keyCode == 37 &&
            !document.getElementById('hideId').focus({ focusVisible: true })
        ) {
            document.getElementById('password').focus({ focusVisible: true })
        }
    }
    useEffect(() => {
        document.addEventListener('keyup', keypresshandler)
        return () => {
            document.removeEventListener('keyup', keypresshandler)
        }
    }, [keypresshandler])

    const handleTranslationchange = useCallback(keyname => {
        if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
            return [keyname]
        } else {
            return apilanguage?.[keyname]
        }
    }, [])
    
    useEffect(() => {
        password?.length > 0 && handleChange(password)
      }, [password])

    useEffect(()=>{
    pushScreenViewEvent({screenName:'register_password_screen',  prevScreenName:state?.pageName ?? state?.gaPageName})
    },[])

    return (
        <div className="App">
            <div className="App-logo">
                <img
                    src={'images/claro-video-logo.png'}
                    className="logo-img"
                    alt="logo"
                />
            </div>
            <p className="password-Titlereg">{handleTranslationchange('Onboarding_RegistroContrasenaRCU_Form_TextoTitulo')}</p>
            <div className="reg-password-height">
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <input
                        className="register-namepass"
                        type={type}
                        name="password"
                        id="password"
                        value={password}
                        data-sn-right="#hideId"
                        onChange={e => handleChange(e.target.value)}
                        onFocus={() => focusFunction()}
                        onBlur={() => blurFunction()}
                        autoFocus
                    />
                    {type === 'password' ? (
                        <button
                            className="hidesymbolreg focusable"
                            onClick={() => setType('text')}
                            id="hideId"
                            data-sn-left="#password">
                            <img
                                className="imageicon"
                                src={'images/Icono_Onboarding_Close.png'}
                                alt="alert"
                            />
                        </button>
                    ) : (
                        <button
                            className="hidesymbolreg focusable"
                            onClick={() => setType('password')}
                            id="hideId"
                            data-sn-left="#password">
                            <img
                                className="imageicon"
                                src={'images/Icono_Onboarding_Open.png'}
                                alt="alert"
                            />
                        </button>
                    )}
                    <div className="error-box">
                        <div
                            id={`exampleInputEmailMsg`}
                            className={`err-text ${errMsg ? 'Reg-invalid-text' : 'invisible'}`}>
                            {[errMsg]}
                        </div>
                        <span className="err_msg"></span>
                    </div>
                    <div className={`${(number && lowerCase && upperCase && mincharacter && nospecial) ? "passwordValidCheck" : "password-msg"}`}>
                        <main className={`tracker-box`}>
                            <div className="Password-req">{handleTranslationchange('Onboarding_RegistroRCU_Form_TítuloTooltip1_{Texto}')}</div>
                            <div className="Password-req-case">
                                {lowerCase && upperCase ? (
                                    <span className="correct-icon">
                                        <img
                                            className="crt-img"
                                            src={`${password ? 'images/Icono_Onboarding_Password_Correcto.png' : ''}`}
                                        />
                                    </span>
                                ) : (
                                    <span className="wrong-icon">
                                        <img
                                            className="incrt-img"
                                            src={`${password ? 'images/Icono_Onboarding_Password_Incorrecto.png' : ''}`}
                                        />
                                    </span>
                                )}
                                <p
                                    className={
                                        !password
                                            ? 'password-error-text'
                                            : lowerCase && upperCase
                                                ? 'password-req'
                                                : 'redtext'
                                    }>
                                    {handleTranslationchange(
                                        'Onboarding_RegistroRCU_Form_TextoTooltip1'
                                    )}
                                </p>
                            </div>
                            <div className="Error-case-name">
                                <div className="Password-req-name">
                                    {number ? (
                                        <span className="correct-icon">
                                            <img
                                                className="crt-img"
                                                src={`${password ? 'images/Icono_Onboarding_Password_Correcto.png' : ''}`}
                                            />
                                        </span>
                                    ) : (
                                        <span className="wrong-icon">
                                            <img
                                                className="incrt-img"
                                                src={`${password ? 'images/Icono_Onboarding_Password_Incorrecto.png' : ''}`}
                                            />
                                        </span>
                                    )}
                                    <p
                                        className={
                                            !password
                                                ? 'password-error-text'
                                                : number
                                                    ? 'password-req'
                                                    : 'redtext'
                                        }>
                                        {' '}
                                        {handleTranslationchange(
                                            'Onboarding_RegistroRCU_Form_TextoTooltip2'
                                        )}{' '}
                                    </p>
                                </div>
                                <div className="Password-req-name">
                                    {mincharacter ? (
                                        <span className="correct-icon">
                                            <img
                                                className="crt-img"
                                                src={`${password ? 'images/Icono_Onboarding_Password_Correcto.png' : ''}`}
                                            />
                                        </span>
                                    ) : (
                                        <span className="wrong-icon">
                                            <img
                                                className="incrt-img"
                                                src={`${password ? 'images/Icono_Onboarding_Password_Incorrecto.png' : ''}`}
                                            />
                                        </span>
                                    )}
                                    <p
                                        className={
                                            !password
                                                ? 'password-error-text'
                                                : mincharacter
                                                    ? 'password-req'
                                                    : 'redtext'
                                        }>
                                        {handleTranslationchange(
                                            'Onboarding_RegistroRCU_Form_TextoTooltip3'
                                        )}
                                    </p>
                                </div>
                                <div className="Password-req-name">
                                    {nospecial ? (
                                        <span className="correct-icon">
                                            <img
                                                className="crt-img"
                                                src={`${password ? 'images/Icono_Onboarding_Password_Correcto.png' : ''}`}
                                            />
                                        </span>
                                    ) : (
                                        <span className="wrong-icon">
                                            <img
                                                className="incrt-img"
                                                src={`${password ? 'images/Icono_Onboarding_Password_Incorrecto.png' : ''}`}
                                            />
                                        </span>
                                    )}
                                    <p
                                        className={
                                            !password
                                                ? 'password-error-text'
                                                : nospecial
                                                    ? 'password-req'
                                                    : 'redtext'
                                        }>
                                        {handleTranslationchange(
                                            'Onboarding_RegistroRCU_Form_TextoTooltip4'
                                        )}
                                    </p>
                                </div>
                            </div>
                        </main>
                    </div>
                </div>
            </div>
            <div className="password-next">
                <button
                    className={`${ !lowerCase || !upperCase || !number || !mincharacter || !nospecial?'register-next':'register-next-enable'}`}
                    id="reg-psd-btn"
                    onClick={e => navHome(e)}
                    disabled={
                        !lowerCase || !upperCase || !number || !mincharacter || !nospecial
                    }>
                    {handleTranslationchange('Onboarding_RegistroContrasenaRCU_Form_TextoBotonPrimario')}
                </button>
            </div>
        </div>
    )
}
export default RegisterPassword