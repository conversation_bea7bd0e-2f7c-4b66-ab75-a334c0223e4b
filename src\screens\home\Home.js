import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import './Navbar.scss'
import './Home.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import HomeData from './HomeData'
import {
  clearIsloggedInStatus,
  getRegisterPopup,
  setSkeltonLoading
} from '../../store/slices/login'
import NavSubMenu from './NavSubMenu'
import { subMenuRailCardsdata } from '../../store/slices/subMenuDataSlice'
import { store } from '../../store/sagaStore'
import {
  getFavouriteLive,
  getLiveTvRecording,
  getLiveReminder,
  getLiveTvCompleteRecording,
  getLiveTvProgressRecording
} from '../../store/slices/EpgSlice'
import { getLockedChannelsList } from '../../store/slices/settingsSlice'
import { getLinealPayway } from '../../store/slices/PlayerSlice'
import { pushScreenViewEvent } from '../../GoogleAnalytics'
import Navbar from './Navbar'
import RegisterBlueband from './RegisterBlueband'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const Home = () => {
  const { state } = useLocation()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const tempRef = useRef()
  const [lastFocusedIndex, setLastFocusedIndex] = useState(state?.id)
  const [nodeLayout, setNodeLayout] = useState('')
  const submenu = useSelector(state => state?.SubMenuFilter?.submenudata)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const isLoggedInStatus = useSelector(state => state?.login?.isLoggedInStatus)
  const registerPopupdata = useSelector(state => state?.login?.registerPopup)
  const navbarTab = useSelector(state => state?.homeReducer?.NavTabValue)
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const getSettingsVariable = useSelector(state => state?.login?.settingClicked)
  const navData =
    useSelector(state => state?.homeReducer?.navbarNodeData?.response?.nodes) ??
    []

  const contentSection = navData?.find(each => each?.code === navbarTab)
  
  useEffect(() => {
    navData?.length > 0 &&
      navData?.map(val => {
        val?.code == navbarTab &&
          setNodeLayout(JSON.parse(val?.app_behaviour)?.layout)
      })
  }, [navbarTab])

  useEffect(() => {
    pushScreenViewEvent({screenName:'inicio',screenData:userDetails,prevScreenName:'profiles',contentSection:contentSection?.text })
    setTimeout(() => {
      lastFocusedIndex && document.getElementById(lastFocusedIndex)?.focus()
    }, [100])
  }, [lastFocusedIndex,contentSection])

  useEffect(() => {
    dispatch(getLiveTvRecording({ user_token: userDetails?.user_token }))
    dispatch(
      getLiveTvCompleteRecording({ user_token: userDetails?.user_token })
    )
    dispatch(
      getLiveTvProgressRecording({ user_token: userDetails?.user_token })
    )
    dispatch(
      getLiveReminder({
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(
      getLockedChannelsList({
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(
      getLinealPayway({
        HKS: userDetails?.session_stringvalue,
        user_id: watchFree ? 0 : userDetails?.user_id
      })
    )
    dispatch(getFavouriteLive())
  }, [userDetails])

  useEffect(() => {
    if (isLoggedInStatus && isLoggedInStatus == 'Success') {
      dispatch(setSkeltonLoading(false))
      dispatch(clearIsloggedInStatus())
    }
    submenu == true ? store.dispatch(subMenuRailCardsdata(false)) : null
  }, [isLoggedInStatus])

  const keypressfun = useCallback(event => {
    const keycode = event.keyCode
    if (keycode >= 37 && keycode <= 40 && state?.id) {
      navigate('.', { replace: true })
    }
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF1Green', 'ColorF2Yellow'])
      const codes = {
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code,
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode, event)
    } else {
      handleLgkey(keycode, event)
    }
  }, [])

  useEffect(() => {
    !document.getElementById('reminder-alert') &&
      registerPopupdata &&
      document.addEventListener('keyup', keypressfun)

    return () => {
      !document.getElementById('reminder-alert') &&
        registerPopupdata &&
        document.removeEventListener('keyup', keypressfun)
    }
  }, [keypressfun])

  const handlesamsungkey = (key, keycode, event) => {
    if (registerPopupdata) {
      if (key.yellowcode === keycode) {
        dispatch(getRegisterPopup(false))
      } else if (key.greencode === keycode) {
        if (document.getElementById('search-container')) {
          event.preventDefault()
        } else {
          dispatch(getRegisterPopup(false))
          navigate('/my-settings/my-subscriptions/add-subscriptions', {
            state: { pageName: 'home' }
          })
        }
      }
    }
  }

  const handleLgkey = (keycode, event) => {
    if (registerPopupdata) {
      if (keycode == 405 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
        dispatch(getRegisterPopup(false))
      } else if (keycode == 404 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 119)) {
        if (document.getElementById('search-container')) {
          event.preventDefault()
        } else {
          dispatch(getRegisterPopup(false))
          navigate('/my-settings/my-subscriptions/add-subscriptions', {
            state: { pageName: 'home' }
          })
        }
      }
    }
  }

  return (
    <div>
      <div className="home-page" id="home_component">
        <div>{registerPopupdata ? <RegisterBlueband /> : null}</div>
        <div className="homemain-Container" id="keyboard">
          {navbarTab !== 'search' && (
            <Navbar
              page={'home'}
              continueDel={state?.page}
              listDel={state?.page}
              getSettingsVariable={getSettingsVariable}
            />
          )}
          <div
            style={{ backgroundColor: '#000000', }}
            ref={tempRef}
          />
          <div
            id={registerPopupdata ? 'id-railsHome-regpopup' : 'id-railsHome'}
            className={registerPopupdata ? 'railsHome-regpopup' : 'railsHome'}
          >
            {submenu == true && navbarTab !== 'search' ? (
              <NavSubMenu tabValue={navbarTab} />
            ) : (
              <HomeData
                navbarTabValue={navbarTab}
                cardId={state?.id}
                backfocusid={state?.backfocusid}
                nodeLayout={nodeLayout}
                fromPage={state?.fromPage}
                delContinuewatch={state?.delContinuewatch}
                deleteHomeMycontent={state?.deleteHomeMycontent}
                railIndex={state?.railIndex}
              />
            )}
            <div
              style={{ position: 'relative', height: '40px', Bottom: '20px' }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default React.memo(Home)
