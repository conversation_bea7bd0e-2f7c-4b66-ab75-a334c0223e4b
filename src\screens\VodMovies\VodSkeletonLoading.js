import React from 'react'
import "./../../styles/SkeletonScreenLoading.css"
import { RailsSkeletonLoading,TitleSkeletonLoading,  CastSkeletonLoading,ToonKidsSkeletonLoading } from '../SkeletonScreenLoading/SkeletonScreenloading'



function VodSkeletonLoading() {
  return (
    <div className="vod-description-page-shimmer">
    <div className="vod-description-page-header shimmer"></div>
     <div className="vod-description-page-subtitle shimmer"></div>
     <div className='vod-description-page-duration shimmer'></div>
     <div className='vod-page-description shimmer'></div>
     <div style={{ display: 'flex' }}>
     <div className="vod-circle shimmer"></div>
     <div className="vod-circle shimmer"></div>
     <div className="vod-circle shimmer"></div>
     </div>
    <TitleSkeletonLoading />
       <div style={{ display: 'flex' }}>
              <CastSkeletonLoading listsToRender={7} />
            </div>
            <TitleSkeletonLoading />
            <div style={{ display: 'flex' }}>
                <RailsSkeletonLoading listsToRender={4} flag={'Horizontal'} />
              </div>
  </div>
  )
}

export default VodSkeletonLoading