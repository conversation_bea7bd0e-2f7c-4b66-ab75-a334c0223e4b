/* Card styles */
.card-banner{
    background: transparent
    linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
    no-repeat padding-box;
    height: 650px;
    width: 100%;
    overflow: hidden;
    margin: 18px;
    border-radius: 5px;
    box-shadow: 9px 17px 45px -29px
                rgba(0, 0, 0, 0.44);
}

/* Card image loading */
.card__image img {
    width: 100%;
    height: 500px;
}
  
.card__image.loading {
    height: 100%;
    width: 100%;
}

/* The loading Class */
.loading {
    position: relative;
    background-repeat: no-repeat;
    
      /* background-size: 100% 100%;  */
    /* background-color: silver; */
    background-image: url("../../tv/images/hero_banner.png");
}

/* The moving element */
.loading::after {
    display: block;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    transform: translateX(-100%);
    background: -webkit-gradient(linear, left top,
                right top, from(transparent), 
                color-stop(rgba(211, 206, 206, 0.2)),
                to(transparent));
                  
    background: linear-gradient(90deg, transparent,
        rgba(194, 191, 191, 0.2), transparent);

    /* Adding animation */
    animation: loading 0.8s infinite;
}

/* Loading Animation */
@keyframes loading {
    100% {
        transform: translateX(100%);
    }
}



/* vid detailpage css  */

.vod-grid-shimmer{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 10px;
    background: transparent
    linear-gradient(90deg, #121212 0%, #121212f2 50%, #12121200 100%) 0% 0%
    no-repeat padding-box;
  }
  
  .vod-cell {
    border-radius: 5px;
    padding: 10px;
    background-color: rgba(194, 191, 191, 0.2);
    box-shadow: 9px 17px 45px -29px
    rgba(0, 0, 0, 0.44);
    animation: loadingAnimation 0.8s infinite;
    display: flex;
    justify-content: center;
    grid-gap: 10px;
    align-items: center;
    height: 35px;
  }
  
  .vod-cell-image {
    height: 20px;
    width: 25%;
    height: 50%;
    /* background-color: #2F2F2F; */
    border-radius: 2px;
  }
  
  .vod-cell-content {
    height: 10px;
    width: 40%;
    height: 50%;
    background-color: rgba(194, 191, 191, 0.2);
    border-radius: 2px;
  } 
  
  .shimmer {
    display: block;
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    transform: translateX(-100%);
    background: -webkit-gradient(linear, left top,
                right top, from(transparent), 
                color-stop(rgba(211, 206, 206, 0.2)),
                to(transparent));
                  
    background: linear-gradient(90deg, transparent,
        rgba(194, 191, 191, 0.2), transparent);

    /* Adding animation */
    animation: vodloadingAnimation 0.8s infinite;
  }
  
  @keyframes vodloadingAnimation {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }


  
  .vod-description-page-shimmer {
    /* padding: 20px; */
    background-color: #242424;
    position: relative;
    background-repeat: no-repeat;
    background-image: url("../../tv/images/hero_banner.png");

    height: 378px;  
    box-shadow: 9px 17px 45px -29px
    rgba(0, 0, 0, 0.44);
   
  }
  .vod-description-page-header{
    height: 20px;
    width: 50%;
    height: 15%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 50px;
    animation: vodloadingAnimation 0.8s infinite;
  }
  .vod-description-page-subtitle{
    width: 50%;
    height: 10%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 40px;
    animation: vodloadingAnimation 0.8s infinite;
    margin: 10px 0px 10px 0px;
  }
  .vod-description-page-duration{
    width: 25%;
    height: 5%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 40px;
    animation: vodloadingAnimation 0.8s infinite;
  }
  .vod-page-description{
    width: 50%;
    height: 30%;
    background-color: rgba(194, 191, 191, 0.2);

    border-radius: 40px;
    animation: vodloadingAnimation 0.8s infinite;
    margin:10px 0px 10px 0px;
  }
  .vod-circle{
    /* width: 5%;
    height: 5%; */
    margin-top: 1%;
    margin-left: 1%;
    margin-right: 1%;
    border-radius: 50% !important;
    height: 100px !important;
    width: 100px;
    background-color: rgba(194, 191, 191, 0.2);
    /* border-radius: 80px; */
    animation: vodloadingAnimation 0.8s infinite;
  }
  @media (min-width: 1920px) and (max-width: 2560px) {
    .vod-grid-shimmer{
      width: 1920px;
    }
    .vod-description-page-shimmer{
      height: 588px;
      width: 1920px;
      padding: 50px 0px 0px 50px;
    }
    .vod-cell{
      width:604px;
      height: 100px;
    }
    .vod-description-page-header{
      width: 50%;
      height: 15%;
    
    }
    .vod-description-page-subtitle{
      width: 50%;
      height: 5%;
      margin: 20px 0px 20px 0px;
    }
    .vod-description-page-duration{
      width: 25%;
      height: 5%;
    }
    .vod-page-description{
      width: 50%;
      height: 30%;
      margin: 20px 0px 20px 0px;
    }
    .vod-description-page-guide{
      width: 25%;
      height: 5%;
    }
  }