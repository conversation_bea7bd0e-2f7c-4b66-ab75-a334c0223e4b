@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

$color-white: #ffffff;
$font-family-roboto: Roboto;
$text-wrap-nowrap: nowrap;
$font-weight-bold: bold;
$box-sizing-border: border-box;
$display-flex: flex;
$text-overflow-ellipsis: ellipsis;
$overflow-hidden: hidden;

.info-layouts {
  position: fixed;
  display: flex;
  flex-direction: column;
  margin-top: 29px;
  // width: 100vw;

  .backIndicator {
    display: flex;
    align-items: center;
    height: 62px;
    width: 306px;
    border-radius: 6.6px;
    background-color: #2e303d;
    margin-left: auto;
    // margin-right: 64px;
    padding: 0 24px;
    box-sizing: border-box;
    border: none;
    outline: none;

    &:focus {
      border: 3px solid #ffffff;
      border-radius: 14px;
      outline: none;
    }

    .yellowIndicator {
      height: 20px;
      width: 20px;
      margin-right: 24px;
      flex-shrink: 0;
    }

    .backImage {
      height: 24px;
      width: 30px;
      margin-right: 24px;
      flex-shrink: 0;
    }

    .backText {
      color: #ffffff;
      font-family: <PERSON>o, sans-serif;
      font-size: 29.04px;
      font-weight: bold;
      line-height: 30px;
      flex-shrink: 0;
      margin: 0;
    }
  }

  .main-wrapper {
    display: flex;
    flex-direction: column;
    margin-left: 88px;
    margin-top: 49px;

    .mainInfo-title {
      width: 1054px;
      color: $color-white;
      font-family: Roboto;
      font-size: 60px;
      display: flex;
      letter-spacing: 0;
      line-height: 70px;
    }

    .episode-count {
      text-transform: capitalize;
      max-height: 32px;
      max-width: 1054px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 28px;
      text-align: left;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 20px;
      margin-bottom: 36px;
      text-overflow: $text-overflow-ellipsis;
    }

    .episode-name {
      max-height: 32px;
      max-width: 1054px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 28px;
      font-weight: bold;
      text-align: left;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 20px;
      text-overflow: $text-overflow-ellipsis;
    }

    .episode-vod-details {
      display: flex;
      flex-direction: row;
      width: 1705px;
      justify-content: space-between;
      align-items: flex-start;

      .episode-descriptive {
        width: 1125px;
        text-align: left;
        display: flex;
        overflow-y: hidden;
        outline: none;

        .episode-description {
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 32px;
          letter-spacing: 0;
          line-height: 46px;
          overflow-wrap: break-word;
        }

        &:focus {
          overflow-y: auto;
        }
      }

      .episode-detail {
        height: 464px;
        width: 460px;
        flex-direction: column;
        display: flex;
        justify-content: space-between;

        .main-duration {
          height: 40px;
          width: 459px;

          .episode-duration {
            height: 40px;
            width: 459px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 36px;
            letter-spacing: 0;
            line-height: 40px;
          }

          .episode-duration-copy {
            height: 42px;
            width: 459px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 36px;
            font-weight: $font-weight-bold;
            letter-spacing: 0;
            line-height: 39px;
          }
        }

        .episode-year-div {
          height: 55px;
          width: 306px;

          .episode-year {
            height: 40px;
            width: 459px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 36px;
            letter-spacing: 0;
            line-height: 40px;
          }

          .episode-year-copy {
            height: 42px;
            width: 459px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 36px;
            font-weight: $font-weight-bold;
            letter-spacing: 0;
            line-height: 39px;
          }
        }

        .episode-generos-div {
          height: 127px;
          width: 306px;

          .episode-generos {
            height: 40px;
            width: 459px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 36px;
            letter-spacing: 0;
            line-height: 40px;
          }

          .episode-generos-copy {
            height: auto;
            width: 460px;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 36px;
            font-weight: $font-weight-bold;
            letter-spacing: 0;
            line-height: 39px;
          }
        }
      }
    }

    .episode-container::-webkit-scrollbar {
      height: 8px;
    }

    .episode-container::-webkit-scrollbar-thumb {
      background-color: gray;
      border-radius: 10px;
    }

    .episode-container::-webkit-scrollbar-track {
      background-color: $color-white;
      border-radius: 10px;
    }
  }

  .actors-list {
    display: flex;
    margin-left: 88px;
    flex-direction: column;
    width: 1760px;

    .actors-title {
      height: 32px;
      width: 113px;
      color: $color-white;
      font-family: $font-family-roboto;
      font-size: 32px;
      letter-spacing: 0;
      margin: 24px 0px;
      font-weight: normal;
    }

    .actor-container {
      display: $display-flex;
      overflow-x: scroll;
      scroll-snap-type: x mandatory;
    }

    .actor-casts {
      display: flex;
      flex-direction: row;
      height: 500px;
      overflow-x: scroll;
      justify-content: left;
      position: relative;
      scroll-snap-type: x mandatory;

      .actors-block {
        margin-right: 30px;

        &:focus,
        &:active {
          border-radius: 6px;
          border: 5.6px solid $color-white;
          scroll-snap-align: end;
          opacity: 1;
          transform: scale(1);
        }

        .episode-castcard-img {
          height: 490px;
          width: 326px;
          border-radius: 3px;
          background-size: 100% 100%;
          background-blend-mode: overlay;

          .episode-cast-img {
            background: linear-gradient(
              180deg,
              rgba(0, 0, 0, 0) 0%,
              #000000 100%
            );
            border-radius: 3px;
            opacity: 1;
            height: 490px;
            width: 326px;
          }

          .episode-cast-details {
            height: 107px;
            display: flex;
            flex-direction: column;
            position: absolute;
            bottom: 2px;
            width: 326px;

            .episode-cast-role {
              height: 32px;
              width: 325px;
              color: #ffffff;
              font-family: Roboto;
              font-size: 28px;
              letter-spacing: 0;
              line-height: 32px;
              text-align: center;
            }

            .episode-cast-name {
              height: 32px;
              color: $color-white;
              font-family: $font-family-roboto;
              font-size: 28px;
              font-weight: $font-weight-bold;
              letter-spacing: 0;
              line-height: 32px;
              text-align: center;
            }
          }
        }
      }
    }
  }
}
