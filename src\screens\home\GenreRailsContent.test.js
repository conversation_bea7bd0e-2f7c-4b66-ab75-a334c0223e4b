import React from "react";
import { fireEvent, getAllByText, getByAltText, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import GenreRailsContent from "./GenreRailsContent";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const GenreRailsmock = {
        "assets": [
            {
                "id": "1317391",
                "title": "Celtics City",
                "title_episode": "Capítulo I: Los Padres Fundadores",
                "title_uri": "Celtics-City",
                "title_original": "Celtics City",
                "description": "Liderados por Bob Cousy y Bill Russell, los Celtics se convierten en una dinastía de la NBA, pero las tensiones raciales auguran una era turbulenta.",
                "description_large": "Mientras los Boston Celtics baten récords al celebrar su 18vo título de la NBA, nos remontamos a los orígenes de la franquicia con la llegada de Red Auerbach en 1950 y su inquebrantable visión del éxito. Auerbach construye un equipo poderoso liderado por Bob Cousy y Bill Russell, pero las tensiones raciales en Boston auguran una era tumultuosa por delante.",
                "short_description": null,
                "image_large": "http://clarovideocdn4.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/SS/2283798WHORIZONTAL.jpg?size=529x297",
                "image_medium": "http://clarovideocdn2.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/SS/2283798WVERTICAL.jpg?size=200x300",
                "image_small": "http://clarovideocdn4.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/SS/2283798WHORIZONTAL.jpg?size=290x163",
                "image_still": "http://clarovideocdn8.clarovideo.net/HBO/PELICULAS/2283799-1-01-01-01/EXPORTACION_WEB/STILLS/2283799-1-01-01-01-STILL-01.jpg",
                "image_background": "http://clarovideocdn0.clarovideo.net/HBO/SERIES/CELTICSCITY-01-01-00/EXPORTACION_WEB/CLEAN/CELTICSCITY-01-01-00_e-1280x720.jpg",
                "url_imagen_t1": "http://clarovideocdn5.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/CLEAN/2283798WVERTICAL.jpg?size=200x300",
                "url_imagen_t2": "http://clarovideocdn7.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/CLEAN/2283798WHORIZONTAL.jpg?size=290x163",
                "image_base_horizontal": "http://clarovideocdn1.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/SS/2283798WHORIZONTAL.jpg",
                "image_base_vertical": "http://clarovideocdn9.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/SS/2283798WVERTICAL.jpg",
                "image_base_square": "",
                "image_clean_horizontal": "http://clarovideocdn4.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/CLEAN/2283798WHORIZONTAL.jpg",
                "image_clean_vertical": "http://clarovideocdn2.clarovideo.net/HBO/SERIES/2283798/EXPORTACION_WEB/CLEAN/2283798WVERTICAL.jpg",
                "image_clean_square": "",
                "image_sprites": "",
                "image_frames": "",
                "image_trickplay": "",
                "image_external": null,
                "duration": "00:59:33",
                "date": "20250314152854",
                "year": "2025",
                "preview": "false",
                "season_number": "1",
                "episode_number": "1",
                "format_types": "susc",
                "live_enabled": "0",
                "live_type": null,
                "live_ref": null,
                "source_uri": "",
                "timeshift": null,
                "votes_average": 4,
                "rating_code": "PG",
                "proveedor_name": "HBO",
                "proveedor_code": "hbo",
                "encoder_tecnology": {
                    "id": null,
                    "desc": null
                },
                "recorder_technology": {
                    "id": null,
                    "desc": null
                },
                "resource_name": null,
                "rollingcreditstime": null,
                "rollingcreditstimedb": null,
                "is_series": true,
                "channel_number": null
            },
            {
                "id": "1307301",
                "title": "Una Actualización sobre Nuestra Familia",
                "title_episode": "Bienvenidos a la Familia",
                "title_uri": "Una-Actualizacion-sobre-Nuestra-Familia",
                "title_original": "An Update On Our Family",
                "description": "A medida que su canal de YouTube crece y se vuelve más lucrativo, los populares vloggers Myka y James Stauffer, padres de tres hijos, adelantan a su audiencia una \"gran sorpresa\": planean adoptar un niño de China.",
                "description_large": "Al adentrarse en el lucrativo mundo de los vlogs de YouTube, Myka Stauffer se muestra muy sociable y dispuesta a compartir todo frente a las cámaras, lo que le permite conseguir una gran audiencia y patrocinio de distintas marcas a su canal. Myka y su marido James, que ya son padres de tres hijos, adelantan a su audiencia una \"gran sorpresa\": planean adoptar un niño de China.",
                "short_description": null,
                "image_large": "http://clarovideocdn4.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/SS/2279354WHORIZONTAL.jpg?size=529x297",
                "image_medium": "http://clarovideocdn2.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/SS/2279354WVERTICAL.jpg?size=200x300",
                "image_small": "http://clarovideocdn4.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/SS/2279354WHORIZONTAL.jpg?size=290x163",
                "image_still": "http://clarovideocdn8.clarovideo.net/HBO/PELICULAS/2279355-1-01-01-01/EXPORTACION_WEB/STILLS/2279355-1-01-01-01-STILL-01.jpg",
                "image_background": "http://clarovideocdn6.clarovideo.net/HBO/SERIES/UNAACTUALIZACINSOBRENUESTRAFAMILIA-01-01-00/EXPORTACION_WEB/CLEAN/UNAACTUALIZACINSOBRENUESTRAFAMILIA-01-01-00_e-1280x720.jpg",
                "url_imagen_t1": "http://clarovideocdn5.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/CLEAN/2279354WVERTICAL.jpg?size=200x300",
                "url_imagen_t2": "http://clarovideocdn7.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/CLEAN/2279354WHORIZONTAL.jpg?size=290x163",
                "image_base_horizontal": "http://clarovideocdn1.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/SS/2279354WHORIZONTAL.jpg",
                "image_base_vertical": "http://clarovideocdn9.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/SS/2279354WVERTICAL.jpg",
                "image_base_square": "",
                "image_clean_horizontal": "http://clarovideocdn4.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/CLEAN/2279354WHORIZONTAL.jpg",
                "image_clean_vertical": "http://clarovideocdn2.clarovideo.net/HBO/SERIES/2279354/EXPORTACION_WEB/CLEAN/2279354WVERTICAL.jpg",
                "image_clean_square": "",
                "image_sprites": "",
                "image_frames": "",
                "image_trickplay": "",
                "image_external": null,
                "duration": "00:49:24",
                "date": "20250130133835",
                "year": "2025",
                "preview": "false",
                "season_number": "1",
                "episode_number": "1",
                "format_types": "susc",
                "live_enabled": "0",
                "live_type": null,
                "live_ref": null,
                "source_uri": "",
                "timeshift": null,
                "votes_average": 4,
                "rating_code": "PG",
                "proveedor_name": "HBO",
                "proveedor_code": "hbo",
                "encoder_tecnology": {
                    "id": null,
                    "desc": null
                },
                "recorder_technology": {
                    "id": null,
                    "desc": null
                },
                "resource_name": null,
                "rollingcreditstime": null,
                "rollingcreditstimedb": null,
                "is_series": true,
                "channel_number": null
            }
        ]
    }

const submenufilterdata = {
    "response": {
        "modules": {
            "module": [
                {
                    "name": "nivel-3-default",
                    "type": "listadoinfinito",
                    "components": {
                        "component": [
                            {
                                "name": "background",
                                "type": "Background",
                                "properties": {
                                    "id": "comp0",
                                    "color": null,
                                    "imglarge": "",
                                    "imgmedium": "",
                                    "imgsmall": "",
                                    "imgextrasmall": "",
                                    "imgdefault": "",
                                    "byuser": "false"
                                }
                            },
                            {
                                "name": "header",
                                "type": "SectionHeader",
                                "properties": {
                                    "id": "comp1",
                                    "large": null,
                                    "medium": null,
                                    "small": null,
                                    "extrasmall": null,
                                    "imglarge": "",
                                    "imgmedium": "",
                                    "imgsmall": "",
                                    "imgextrasmall": "",
                                    "htmltag": "h3",
                                    "imgdefault": "",
                                    "link_type": "none",
                                    "link_group": null,
                                    "link_special": null,
                                    "link_node": null,
                                    "link_app_behaviour": null,
                                    "byuser": "false"
                                }
                            }
                        ]
                    }
                }
            ]
        },
        "version": "4187"
    },
    "status": "0",
    "msg": "OK"
}

describe('GenreRailsContent page test', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('navigation to filter page', () => {
        initialState.SubMenuFilter = {
            submenudata: true
        }
        const { container } = renderWithState(<GenreRailsContent />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'filter');
        fireEvent.focus(scroll)
        fireEvent.blur(scroll)
        fireEvent.keyUp(scroll, { key: 'ArrowUp' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })

    // test('should render onclick railcard', () => {
    //     initialState.SubMenuFilter = {
    //         submenudata: true
    //     }
    //     initialState.SubMenuFilter = {
    //         raildata: GenreRailsmock
    //     }
    //     initialState.SubMenuFilter = {
    //         filterclickdata: false
    //     }
    //     const { container } = renderWithState(<GenreRailsContent />)
    //     const getById = queryByAttribute.bind(null, 'id');
    //     const scroll = getById(container, 'rail_0');
    //     fireEvent(
    //         scroll,
    //         new MouseEvent('click', {
    //             bubbles: true,
    //             cancelable: true
    //         })
    //     )
    // })

    test('render GenreRailsContent mockdata', () => {
        initialState.SubMenuFilter = {
            railListurl: submenufilterdata
        }
        renderWithState(<GenreRailsContent />)
    })
})