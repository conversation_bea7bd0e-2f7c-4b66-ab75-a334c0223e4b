import React,{useEffect} from 'react'
import './Settings.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { getAlerts } from '../../store/slices/EpgSlice'
import { getClearSetControlPin } from '../../store/slices/settingsSlice'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

function PinCreationSuccess() {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const { state } = useLocation()

  const region = localStorage.getItem('region')

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleAcceptClick = () => {
    if (state?.pageName == 'livePlayer') {
      const alertDetailLabel =
        translations?.language?.[region]
          ?.TvEnVivo_Notificacion_TextoTitulo_CanalBloqueado
      const replacementText = `${state?.data?.name} ${state?.data?.number}`
      const message1 = alertDetailLabel?.replace('@canal', replacementText)
      const message2 = ` | ${truncateText(
        'TvEnVivo_Notificacion_TextoCuerpo_CanalBloqueado',
        30
      )}`

      dispatch(
        getAlerts({
          image: 'images/lock_icon_liveTV.png',
          message: `${message1}`,
          message2: `${message2}`,
          status: 'lock',
          subStatus: 'add-lock'
        })
      )
      navigate('/livePlayer', {
        state: { showControls: 'live', fromPage : "pinCreation" },
        replace: true
      })
    } else {
      dispatch(getClearSetControlPin())
      navigate('/settings/profile-settings', {
        state: { pageName: 'parentalControl' }
      })
    }
  }

  useEffect(()=>{
    pushScreenViewEvent({screenName:'pin_creation_success', screenData: userDetails, prevScreenName: 'create_security_pin'})
  },[])

  return (
    <div className="pin-creation-success">
      <img src={'images/checkPinTick.png'} className="green-tick" />
      <span className="pin-creation-success-text1">{truncateText('modal_pin_create_title', 40)}</span>
      <span className="pin-creation-success-text2">
        {truncateText('modal_pin_create_msg', 100)}
      </span>
      <button
        className="pin-creation-success-button"
        autoFocus
        id="acceptButton"
        onClick={handleAcceptClick}
      >
        <span className="pin-creation-success-button-content">
          {truncateText('btn_modal_ok', 30)}
        </span>
      </button>
    </div>
  )
}

export default React.memo(PinCreationSuccess)
