import React, { useCallback, useEffect, useRef } from 'react'
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import './MycontentDelete.scss'
import { deleteMyContentRecording } from '../../../store/slices/EpgSlice';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { pushContentSelectionEvent } from '../../../GoogleAnalytics';
import { DELETE_RECORDING } from '../../../GoogleAnalyticsConstants';


const RecordingDelete = props => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const startTimeRef = useRef(null)
  const recordingData = state?.recordingData
  const dispatch = useDispatch()
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const apaMetaData = useSelector((state) => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode;
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {

        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode);
    } else {
      handleLgkey(keycode);
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate('/home',{state:{backfocusid: state?.backfocusid}});
    }
  }


  const handleLgkey = (keycode) => {
    if (keycode == 405 || keycode === 461) {
      navigate('/home',{state:{backfocusid: state?.backfocusid}});

    }
  }
  const handleTranslationchange = useCallback((keyname) => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  const handleWatchListDel = (e) => {
    e.preventDefault();
    const engagementTime =  Date.now() - startTimeRef.current;  
    const userData = {
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        content_section : navbarTab,
        content_list : props?.title,
        modulo_name : 'carrusel',
        page_path: 'player grabaciones',
        page_title: navbarTab,
        engagement_time_msec: engagementTime
      }
    //GA event delete recording
    pushContentSelectionEvent(userData,recordingData,'',DELETE_RECORDING)
    dispatch(deleteMyContentRecording(state?.url))
    navigate('/home',{state:{backfocusid: state?.backfocusid}})
    startTimeRef.current = Date.now();
  }

  const clickcancel = (e) => {
    e.preventDefault();
    navigate('/home',{state:{backfocusid: state?.backfocusid}})
  }

  const handleBack = (e) => {
    e.preventDefault();
    navigate('/home',{state:{backfocusid: state?.backfocusid}})
  }

  useEffect(() => {
    startTimeRef.current = Date.now()
    SpatialNavigation.focus();
    document.getElementById('confirmbt')?.focus()
    return () => {
      startTimeRef.current = null
    }
  }, [])

  return (
    <div className="deletecard-mainclass">
      <div className="delete-regresser-box">
        <button
          className="filter-backScreen focusable"
          onClick={e => handleBack(e)}
          id='backbutton'
        >
          <LazyLoadImage
            className="filter-back-img-icon"
            src={'images/Vcard_Icons/yellowcircle_small.png'}
            placeholderSrc={'images/Vcard_Icons/yellowcircle_small.png'}
          />
          <LazyLoadImage
            className="filter-back-img-icon"
            src={'images/Vcard_Icons/icon_backpage.png'}
            placeholderSrc={'images/Vcard_Icons/icon_backpage.png'}
          />
          <span className="filter-back-button-regresar-title">{handleTranslationchange('top_head_option_button_return')}</span>
        </button>
      </div>
      <div className='delete-title'>
        {handleTranslationchange('contentToDelete_modal_titleRecordingPresentEvent_label')}
      </div>
      <div className="image-class">
        <img src={recordingData?.channel ? recordingData?.channel?.image ?? recordingData?.image : "images/landscape_card.png"} className='delete-image-src' />
      </div>
      <div className='mycontent-title'>
        {handleTranslationchange('contentToDelete_modal_descriptionBegin_label')} "{recordingData?.channel ? recordingData?.channel?.event?.name : recordingData?.serie_name}" {handleTranslationchange('contentToDelete_modal_descriptionEnd_label')}
      </div>
      <div className="button-block">
        <button id="confirmbt" className="confirm-button focusable" onClick={(e) => handleWatchListDel(e)} > {handleTranslationchange('contentToDelete_modal_option_button_deleteRecord')}</button>

        <button id='cancelbtn' className="cancel-button focusable"
          onClick={(e) => clickcancel(e)}> {handleTranslationchange('contentToDelete_modal_option_button_cancel')}</button>
      </div>
    </div>
  )
}

export default RecordingDelete