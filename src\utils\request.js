//import 'whatwg-fetch';
//import 'isomorphic-fetch';
import { BASE_URL, URL } from '../utils/environment'
import { defaultHeader, defaultHeaders } from './constant'
// import { refreshToken } from '../routes/Login/LoginActions';

// Global handling for each specific response status code can be directly written under each case.
const GLOBAL_RESPONSE_STATUS_CODE_HANDLERS = {
  // Unauthorized
  401: () => {
    //localStorage.removeItem('token');
    // window.location.reload()
    return false
  },
  503: () => false,
  500: async (e, action) => {
    if (e.exception === 'io.jsonwebtoken.ExpiredJwtException') {
      // await store.dispatch(refreshToken());
      setTimeout(() => {
        store.dispatch({
          type: `${action.type}`,
          payload: action.body || action.payload
        })
      }, 2000)
    }
  }
}

// const redirectToLoginPage = () => {
//   localStorage.removeItem('authToken');
// };

export function get(url) {
  return api(url, 'GET')
}

export function post(url, payload) {
  return api(url, 'POST', payload)
}

function checkStatus(response) {
  if (response.status !== 503) {
    return response
  }
  const error = new Error(response.statusText)
  error.response = response
  throw error
}

/**
 *
 * @param url
 * @param options
 * @param callback
 * @param tokenRequired
 * @returns {Function}
 */

export function request(url, options, callback, action) {
  try {
    options.body =
      typeof options.body !== 'string'
        ? JSON.stringify(options.body)
        : options.body

    options.headers = options.headers
      ? Object.assign({}, defaultHeaders(), options.headers)
      : defaultHeader()

    let statusCode, responseStatus, responseStatusCode
    return fetch(url, {
      credentials: 'same-origin',
      ...options
    })
      .then(response => {
        statusCode = response.status
        responseStatus =
          statusCode >= 200 && statusCode < 300 ? 'onSuccess' : 'onError'
        responseStatusCode = statusCode.toString()
        const httpStatus = { httpStatusCode: statusCode }
        let responseBody = response.json().then(
          body => {
            return body
          },
          error => {
            return { message: 'Response body is empty' }
          }
        )
        if (statusCode < 500) return Object.assign(responseBody, httpStatus)
        else return httpStatus
      })
      .then(resp => {
        if (responseStatus in callback) {
          callback[responseStatus](resp)
        }
        if (responseStatusCode in callback) {
          callback[responseStatusCode](resp)
        }
        // if (responseStatusCode in GLOBAL_RESPONSE_STATUS_CODE_HANDLERS) {
        //   GLOBAL_RESPONSE_STATUS_CODE_HANDLERS[responseStatusCode](resp, action,url, options, callback)
        // }
        return resp
      })
  } catch (exception) {}
}

/**
 * For 502 we will not get JSON response, hence throw error
 * @param response
 * @returns {*}
 */

// WEBPACK FOOTER //
// ./src/utils/request.js
