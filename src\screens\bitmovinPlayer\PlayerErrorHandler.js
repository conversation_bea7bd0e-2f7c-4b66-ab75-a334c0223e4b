import React, { useEffect } from 'react'
import './PlayerErrorHandler.scss'
import { useSelector, useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
const region = localStorage.getItem('region')
const alertImage = 'images/ic_alert.png'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import { clearGetMediaErr } from '../../store/slices/PlayerSlice'

const PlayerErrorHandler = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const {
    errFromPlayer,
    chName,
    chNum,
    chImg,
    msg1,
    msg2,
    errorMessage1,
    errorMessage2,
    errorType,
    lockChannelFlag
  } = props.data

  const channelLockErrMessage =
    (chNum && chName && msg1?.replace('{[channel] [number]}', `${chName} ${chNum}`)) ||
    msg1
  const channelAlertErrMessage = 
    (chNum && chName && msg1?.replace('<channel>', `${chName} ${chNum}`)) ||
    msg1

  const slicedMessage1 = errorMessage2?.slice(0, errorMessage2.indexOf('.'))
  const LockedScreenImg = 'images/lock_icon_livetv_new.png'
  const slicedMessage2 = errorMessage2?.slice(
    errorMessage2.indexOf('.') + 1,
    errorMessage2.length
  )

  var miniId = document.getElementById('miniEpg')
  var mainId = document.getElementById('mainEpg')
  var programDetailsId = document.getElementById('programDetailsId')
  var alertId = document.getElementById('favouriteAlert')
  var alertButton = document.getElementById('alertMsg')
  var timeShiftAlertId = document.getElementById('timeShiftError')


  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  const handlesamsungkey = (key, keycode) => {
    if ((key.yellowcode === keycode || keycode === 10009 || keycode === 8) && !miniId) {
        dispatch(getNavTabValue('homeuser'))
        localStorage.setItem('currNavIdx', 0)
        navigate('/home')
    }
    else if ((key.greencode === keycode) && miniId) {
      dispatch(getNavTabValue(nav?.[0]?.code))
      localStorage.setItem('currNavIdx', 0)
      navigate('/home')
    }
  }

  const handleLgkey = keycode => {
    if ((keycode == 405 || keycode === 461 || keycode === 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) && !miniId) {
      dispatch(getNavTabValue('homeuser'))
        localStorage.setItem('currNavIdx', 0)
        navigate('/home')
      }
    else if (keycode == 404 && miniId) {
      dispatch(getNavTabValue(nav?.[0]?.code))
      localStorage.setItem('currNavIdx', 0)
      navigate('/home')
    }
  }

  useEffect(() => {
    !mainId && !programDetailsId && !alertId && !alertButton && !timeShiftAlertId && document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [miniId, mainId, programDetailsId, alertId, alertButton, timeShiftAlertId])

  const handleBack = event => {
    event.preventDefault()
    dispatch(getNavTabValue('homeuser'))
    navigate('/home')
    localStorage.setItem('currNavIdx', 0)
  }

  const handleRetry = () => {
    dispatch(clearGetMediaErr())
    props?.retry(true)
  }

  const truncateText = (str, length, ending) => {
    if (length == null) {
      length = 100
    }
    if (ending == null) {
      ending = '...'
    }
    if (str?.length > length) {
      return str?.substring(0, length - ending?.length) + ending
    } else {
      return str
    }
  }

  return errFromPlayer ? (
    <div className="player-err-screen" id='player-error-screen-div'>
      <div className="channel-icon-div">
        <img className="channel-icon" src={chImg} />
      </div>
      <div className="channel-error-div">
        <img className={lockChannelFlag ? "lock-icon" : "alert-icon"} src={lockChannelFlag ? LockedScreenImg : alertImage} />
        <p className="live-msg-1">{lockChannelFlag ? channelLockErrMessage : channelAlertErrMessage}</p>
        <p className="live-msg-2">{msg2}</p>
      </div>
    </div>
  ) : (
    <div id="player-error" className="player-error">
      {
        <button
          className="player-error-back-button focusable"
          autoFocus={true}
          onClick={handleBack}
        >
          <img
            className="yellow-indicator"
            src={'images/yellow_shortcut.png'}
          />
          <img className="back-image" src={'images/back_button.png'} />
          <p className="back-text">
            {apilanguage?.atv_back_notification || 'REGRESAR'}
          </p>
        </button>
      }
      <div className="live-tv-error">
        <img className="alert-icon" src={alertImage} />
        <p className="msg-1">{errorMessage1}</p>
        <p className="msg-2">{slicedMessage1}</p>
        <p className="msg-2">{slicedMessage2}</p>
      </div>
      {errorType === 'playback' && (
        <div className="bottom-btn-container">
          <button
            autoFocus
            onClick={handleRetry}
            className="retry-btn focusable"
          >
            <p className="bottom-btn-text">
                {`${truncateText(apilanguage?.[`playingLive_alert_limit_Reproductions_button_retry`] ?? 'playingLive_alert_limit_Reproductions_button_retry',20)}` }
            </p>
          </button>
          <button
            onClick={() => props?.closePopup({})}
            className="cancel-btn focusable"
          >
            <p className="bottom-btn-text">
            {`${truncateText(apilanguage?.[`playingLive_alert_limit_Reproductions_button_cancel`] ?? 'playingLive_alert_limit_Reproductions_button_cancel',20)}` }
            </p>
          </button>
        </div>
      )}
      {errorType === 'device' && (
        <div className="bottom-btn-container">
          <button
            autoFocus
            onClick={() => props?.closePopup({})}
            className="retry-btn focusable"
          >
            <p className="bottom-btn-text">
              {`${truncateText(apilanguage?.[`playingLive_alert_limit_Devices_button_close`] ??  'playingLive_alert_limit_Devices_button_close',30)}` }
            </p>
          </button>
        </div>
      )}
    </div>
  )
}

export default PlayerErrorHandler
