$color-white: #ffffff;
$font-family-roboto: 'Roboto';
$position-absolute: absolute;



.Vertrail-Container {
  // margin-bottom: 1rem;
  // overflow-x: hidden;
  height:560px;
  margin-bottom: 40px;

  .vertrailTitle {
    font-size: 32px;
    font-weight: normal;
    text-align: left;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin-left: 70px;
    // margin-bottom: 17px;
    font-family: $font-family-roboto;
    position: relative;
  }

  .vertrail-wrapper {
    display: flex;
    overflow: scroll;
    scroll-snap-type: x mandatory;
    margin-left: 30px;

    .ampliado_block {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      margin: 0.5rem;
      padding: 0.5rem;
      position: relative;
    }

    .vertical_block {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      margin-right: 20px;
      position: relative;
      padding: 4px 4px 4px 4px;
      opacity: 0.8;
    }

    .vertical_block:focus {
      scroll-snap-align: end;
      border: 4px solid #ffffff !important;
      border-radius: 10px;
      opacity: 1;
      transform: scale(1.03);
    }

    .rail-image-vert {
      display: flex;
      width: 326px;
      height: 490px;
    }

    .seriesMoviesTitleVert {
      position: $position-absolute;
      bottom: 3px;
      left: 0rem;
      opacity: 0;
    }

    .defaultTitleVert {
      font-size: 28px;
      text-wrap: wrap;
      color: #fff;
      padding-left: 0.5rem;
      font-family: 'Roboto';
      width: 20rem;
    }
  }

  .proveedorBlockRailAlq {
    position: absolute;
    top: 13px;
    left: 13px;
  }

  .proveedor-block-rail-alq-comp {
    position: absolute;
    top: 48px;
    left: 11px;
  }

  .comprar-tag-movies {
    position: absolute;
    top: 10px;
    left: 10px;
    height: 34px;
    width: 108px;
    border-radius: 6px;
    background-color: #477f9B;
    color: #ffffff;
    font-family: Roboto;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;

  }

  .premium-icon{
    width: auto;
    height: 34px;
  }

  .proveedorBlockRail_vero_hara {
    position: absolute;
    left: 13px;
    top: 13px;
    display: flex;
    height: 34px;
    width: auto;
  }

  .verahora-tag {
    height: 32px;
    width: 122px;
    border-radius: 6px;
    background-color: #68B75C;
    color: #FFFFFF;
    font-family: Roboto;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
  }

  .tagAlq {
    width: 100px;
    height: 34px;
  }

  .proveedor-block-rail {
    position: absolute;
    left: 9px;
    top: 15px;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  #icon1 {
    width: 111px;
    height: 38px;
  }

  .proveedorBlock {
    position: absolute;
    top: 0px;
    left: 0px;
  }

  .description {
    font-size: 28px;
    text-wrap: wrap;
    color: #fff;
    padding-left: 0.5rem;
    margin-top: 0.2rem;
  }

  .vertical_block:focus .seriesMoviesTitleVert {
    opacity: 1;
    padding-left: 0.5rem;
  }
}