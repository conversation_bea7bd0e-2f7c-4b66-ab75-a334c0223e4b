import initialSlice, {
    getNavData,
    getNavSuccess,
    getNavSuccessallNodes,
    getNavError,
    getNavDataKids,
    getNavKidsSuccess,
    getNavKidsError,
    getPage,
    getUrls,
    getLevel,
    getPageKids,
    getUrlsKids,
    getLevelKids,
    premiumCarouselData,
    premiumCarouselDataSuccess,
    premiumCarouselDataFailure,
    getBannerData,getBannerSuccess,getBannerError,
    getPremiumNodeValue,
    getPremiumValueStateClear,
    getNavValue,
    getMyContentPage,
    getNotificationmsg,
    getNotification,
    getClearState,
    getNavTabValue,
    getNavTabValueClear,
    getPremiumCardFocus 
} from './HomeSlice';

describe('initialSlice reducer', () => {
    const initialState = {
        bannerHightlight: {},
        navbarData: {},
        navbarNodeData: {},
        navbarKidsData: {},
        pageData: [],
        pageKidsData: [],
        urlsData:[],
        urlsDataKids:[],
        level:[],
        levelKids:[],
		levelUserV1Response:{},
		levelV1Response:{},
        premiumData: {},
        premiumNodeValue: {},
        NavTabValue:"homeuser",
        storenavdata: "",
        isLoading: false,
        error: {},
        mycontent:false,
        notificationmsg:false,
        notificationdata:false,
        premiumCardFocus: false,
    }
    it('should handle getNavData', () => {
		const action = { type: getNavData.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getNavSuccess', () => {
		const payload = { Nav: 'Success' };
		const action = { type: getNavSuccess.type, payload };
		const expectedState = { ...initialState, navbarData: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavSuccessallNodes', () => {
		const payload = { Nav: 'Success' };
		const action = { type: getNavSuccessallNodes.type, payload };
		const expectedState = { ...initialState, navbarNodeData: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPage', () => {
		const payload = { Get: 'Page' };
		const action = { type: getPage.type, payload };
		const expectedState = { ...initialState, pageData: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getUrls', () => {
		const payload = { Get: 'URL' };
		const action = { type: getUrls.type, payload };
		const expectedState = { ...initialState, urlsData: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getLevel', () => {
		const payload = { Get: 'Level' };
		const action = { type: getLevel.type, payload };
		const expectedState = { ...initialState, level: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavDataKids', () => {
		const action = { type: getNavDataKids.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavKidsSuccess', () => {
		const payload = { Nav: 'Kids' };
		const action = { type: getNavKidsSuccess.type, payload };
		const expectedState = { ...initialState, navbarKidsData: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPageKids', () => {
		const payload = { Page: 'Kids' };
		const action = { type: getPageKids.type, payload };
		const expectedState = { ...initialState, pageKidsData: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getUrlsKids', () => {
		const payload = { url: 'Kids' };
		const action = { type: getUrlsKids.type, payload };
		const expectedState = { ...initialState, urlsDataKids: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getLevelKids', () => {
		const payload = { level: 'Kids' };
		const action = { type: getLevelKids.type, payload };
		const expectedState = { ...initialState, levelKids: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getBannerData', () => {
		const action = { type: getBannerData.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getBannerSuccess', () => {
		const payload = { Get: 'Banner' };
		const action = { type: getBannerSuccess.type, payload };
		const expectedState = { ...initialState, bannerHightlight: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle premiumCarouselData', () => {
		const action = { type: premiumCarouselData.type };
		const expectedState = { ...initialState, isLoading: true };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle premiumCarouselDataSuccess', () => {
		const payload = { Carousel: 'Data' };
		const action = { type: premiumCarouselDataSuccess.type, payload };
		const expectedState = { ...initialState, premiumData: payload, isLoading: false };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPremiumNodeValue', () => {
		const payload = { Premium: 'Node' };
		const action = { type: getPremiumNodeValue.type, payload };
		const expectedState = { ...initialState, premiumNodeValue: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPremiumCardFocus', () => {
		const payload = { Premium: 'card' };
		const action = { type: getPremiumCardFocus.type, payload };
		const expectedState = { ...initialState, premiumCardFocus: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getPremiumValueStateClear', () => {
		const action = { type: getPremiumValueStateClear.type };
		const expectedState = { ...initialState, premiumNodeValue: {} };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavTabValue', () => {
		const payload = { Navtab: 'Value' };
		const action = { type: getNavTabValue.type, payload };
		const expectedState = { ...initialState, NavTabValue: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavTabValueClear', () => {
		const action = { type: getNavTabValueClear.type };
		const expectedState = { ...initialState, NavTabValue: {} };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavValue', () => {
		const payload = { Nav: 'Value' };
		const action = { type: getNavValue.type, payload };
		const expectedState = { ...initialState, storenavdata: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getMyContentPage', () => {
		const payload = { Content: 'Page' };
		const action = { type: getMyContentPage.type, payload };
		const expectedState = { ...initialState, mycontent: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNotificationmsg', () => {
		const payload = { Notification: 'message' };
		const action = { type: getNotificationmsg.type, payload };
		const expectedState = { ...initialState, notificationmsg: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNotification', () => {
		const payload = { Notification: 'message' };
		const action = { type: getNotification.type, payload };
		const expectedState = { ...initialState, notificationdata: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });

      it('should handle getClearState', () => {
		const action = { type: getClearState.type };
		const expectedState = { ...initialState,
            level : [],
            levelKids : [],
			levelUserV1Response:{},
		    levelV1Response:{},
            pageData:[],
            navbarData:{},
            navbarNodeData:{},
            urlsData:[],
         };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      /////////////////////ERROR CASES///////////////////////////////
      it('should handle getNavError', () => {
		const payload = 'Error message';
		const action = { type: getNavError.type, payload };
		const expectedState = { ...initialState, isLoading: false, error: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getNavKidsError', () => {
		const payload = 'Error message';
		const action = { type: getNavKidsError.type, payload };
		const expectedState = { ...initialState, isLoading: false, error: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle getBannerError', () => {
		const payload = 'Error message';
		const action = { type: getBannerError.type, payload };
		const expectedState = { ...initialState, isLoading: false, error: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
      
      it('should handle premiumCarouselDataFailure', () => {
		const payload = 'Error message';
		const action = { type: premiumCarouselDataFailure.type, payload };
		const expectedState = { ...initialState, isLoading: false, error: payload };
		expect(initialSlice(initialState, action)).toEqual(expectedState);
	  });
 

});

   