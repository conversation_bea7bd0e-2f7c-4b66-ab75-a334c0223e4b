import { takeEvery, put, call } from 'redux-saga/effects'
import {
  getSubMenuSuccess,
  getSubMenuFailure,
  subMenuRailCardsFailure,
  railsRes,
} from '../store/slices/subMenuDataSlice'
import { store } from '../store/sagaStore'
import { request } from '../utils/request'
import { URL, COMMON_URL } from '../utils/environment'


function* fetchCMSLevelContentList({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.CMS_LEVEL_V1_URL + `&node=${payload}`+ '&region='+region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getSubMenuSuccess(response))
        },
        onError(error) {
          store.dispatch(getSubMenuFailure(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}


function* fetchContentList({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload?.url}&HKS=${payload?.HKS}&user_id=${payload?.user_id}${URL.CONTENT_PARAM_URL}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          let resp = response?.response;
          let data = {rails: resp }
          localStorage.setItem('submenucard',resp?.total)
          store.dispatch(railsRes({railDat: data}));
        },
        onError(error) {
          store.dispatch(subMenuRailCardsFailure(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* fetchContentRailList({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload?.url}&HKS=${payload?.HKS}&user_id=${payload?.user_id}${URL.CONTENT_PARAM_URL}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
            let resp = response?.response;
            let data = {rails : resp }
            store.dispatch(railsRes({newRailDat: data}))
        },
        onError(error) {
            store.dispatch(railsRes({}))                      },
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* showGenreContentSaga() {
  yield takeEvery('SubMenuFilter/getSubMenu', fetchCMSLevelContentList)
  yield takeEvery('SubMenuFilter/subMenuRailCards', fetchContentList)
  yield takeEvery('SubMenuFilter/getNewRail', fetchContentRailList)
}
