import React, { useEffect } from 'react';
import <PERSON><PERSON> from 'react-lottie-player';
import animationData from '../json/animationData.json'
import './CommonLoading.scss'
import { CURRENT_PLATFORM } from '../utils/devicePlatform';


const CommonLoading = (props) => {
  return (
    <>
      <div className= {((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') ? "common-loading-container-netrange" : "common-loading-container" )} >
        <div className="manageText">
          <Lottie loop animationData={animationData} play />
        </div>
      </div>
    </>
  );
};
export default CommonLoading;
