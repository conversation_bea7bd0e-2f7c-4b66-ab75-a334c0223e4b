import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { getClearAllLoginStates, getPassword } from '../../store/slices/login'
import { pushForgotPasswordEvent, pushScreenViewEvent } from '../../GoogleAnalytics'
import '../loginregister/Loginpage.scss'
import { errorMailPattern } from './Regex'

const LoginForgotPassword = () => {
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const newpasswordCode = useSelector(state => state?.login?.newpasswordCode)
    const recovermail = useSelector(state => state?.login?.recovermail)
    const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
    
    const region = localStorage.getItem('region')
    const translations =
        apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const apilanguage = translations?.language?.[region]
    const navigate = useNavigate()
    const { state } = useLocation()
    const dispatch = useDispatch()
    const [invalidEmail, setIvalidEmail] = useState('')
    const [notRegisterEmail, setNotRegisterEmail] = useState('')
    const validemail = errorMailPattern.test(state?.value)
    useEffect(() => {
        state?.focus && document.getElementById('signid')?.focus()
        switch (true) {
            case recovermail?.status == '1' && !validemail:
              document.getElementById('email')?.focus()
              setNotRegisterEmail(handleTranslationchange('email_tooltip_valid_label_validation'))
              break
            case recovermail?.errors?.code == 'USR_USR_00013':
              document.getElementById('email')?.focus()
              setIvalidEmail(handleTranslationchange('loginMail_tooltip_error_label'))
              break 
            case newpasswordCode?.status == '0':
                navigate('/loginForgotPasswordMessage', { 
                    state: { 
                        value: state?.value, 
                        seriesEpisodeData: state?.seriesEpisodeData, 
                        fromDetailsPage: state?.fromDetailsPage,
                        pageName: state?.pageName
                    } 
                })
                break
            default:
              break
          }
    }, [newpasswordCode,recovermail])

    const navSignin = e => {
        e.preventDefault()
        dispatch(getPassword(state?.value))
        pushForgotPasswordEvent(
          handleTranslationchange(
            'Onboarding_OlvidasteContrasena_TextoBotonPrimario'
          )?.toLowerCase(),
          handleTranslationchange('Onboarding_OlvidasteContrasena_TextoTitulo')?.toLowerCase()
        )
    }
    const clickCancle = () => {
        dispatch(getClearAllLoginStates())
        pushForgotPasswordEvent(
          handleTranslationchange(
            'Onboarding_OlvidasteContrasena_TextoBotonSecundario'
          )?.toLowerCase(),
          handleTranslationchange('Onboarding_OlvidasteContrasena_TextoTitulo')?.toLowerCase()
        )
        navigate('/landing')
    }
    const navEmail = e => {
        e.preventDefault()
        dispatch(getClearAllLoginStates())
        navigate('/loginForgotPasswordMail', { 
            state: { 
                value: state?.value, 
                seriesEpisodeData: state?.seriesEpisodeData,
                fromDetailsPage: state?.fromDetailsPage,
                pageName: state?.pageName
            } 
        })
    }
    const navEmailkeyPress = e => {
        e.preventDefault()
        if (e.key == 'Enter') {
            dispatch(getClearAllLoginStates())
            navigate('/loginForgotPasswordMail', { 
                state: { 
                    value: state?.value, 
                    seriesEpisodeData: state?.seriesEpisodeData,
                    fromDetailsPage: state?.fromDetailsPage,
                    pageName: state?.pageName
                }
            })
        }
    }

    const keypresshandler = event => {
        if (event?.keyCode === 10009 || event?.keyCode === 461) {
            dispatch(getClearAllLoginStates())
            navigate('/signin', {
                state: { 
                    seriesEpisodeData: state?.seriesEpisodeData,
                    fromDetailsPage: state?.fromDetailsPage,
                    pageName: state?.pageName
                }
            })
        }
    }

    useEffect(() => {
        document.addEventListener('keyup', keypresshandler)
        return () => {
            document.removeEventListener('keyup', keypresshandler)
        }
    }, [keypresshandler])

    useEffect(()=>{
      pushScreenViewEvent({screenName:'login_forgot_password', screenData: userDetails, prevScreenName: 'sign_in'})
    },[])

    const handleTranslationchange = useCallback(keyname => {
        if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
            return [keyname]
        } else {
            return apilanguage?.[keyname]
        }
    }, [])

    return (
        <div className="App">
            <div className="App-logo">
                <img
                    src={'images/claro-video-logo.png'}
                    className="logo-img"
                    alt="logo"
                />
            </div>
            <p className="recpassword-Header">
                {handleTranslationchange('Onboarding_OlvidasteContrasena_TextoTitulo')}
            </p>
            <div className='recpassword-main'>
            <p className="recpassword-Title">
                {handleTranslationchange('Onboarding_OlvidasteContrasena_Texto1')}
            </p>
            <div>
                <button
                    className={`${state?.value && !invalidEmail && !notRegisterEmail ? 'recregister-namerec' : invalidEmail || notRegisterEmail? 'recregister-namerec-redborder':'forgotmail-placeholder'} focusable`}
                    id="email"
                    autoFocus
                    onClick={e => navEmail(e)}
                    onKeyPress={e => navEmailkeyPress(e)}>
                    {state?.value ?? handleTranslationchange('Onboarding_OlvidasteContrasena_TextoPlaceholder1')}
                </button>
                    <div className="loginn-error-box">
                        <div
                            id={`exampleInputEmailMsg`}
                            className={`err-text ${invalidEmail ? 'login-invalid-email' : notRegisterEmail ? "login-invalid-text": 'invisible'}`}>
                            <p className="login-err-msg">
                                {[invalidEmail||notRegisterEmail]}
                            </p>
                        </div>
                    </div>
            </div>
            <button
                className="recsign-next focusable"
                id="signid"
                disabled={!state?.email && !state?.value}
                onClick={e => navSignin(e)}>
                {handleTranslationchange('Onboarding_OlvidasteContrasena_TextoBotonPrimario')}
            </button>
            <button
                className="recsign-cancle focusable"
                id="cancleid"
                onClick={e => clickCancle(e)}>
                {handleTranslationchange('Onboarding_OlvidasteContrasena_TextoBotonSecundario')}
            </button>
            </div>
        </div>
    )
}

export default LoginForgotPassword