import React, { useState, useEffect, useRef } from 'react'
import './VideoTutorial.scss'
import { useSelector } from 'react-redux'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import {
  getIsLoggedinV1,
  getNavBarClicked,
  setSkeltonLoading
} from '../../store/slices/login'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const VideoTutorial = () => {
  const [enableSkip, setEnableSkip] = useState(false)
  const videoRef = useRef(null)
  const [acceptButtonEnableTime, setAcceptButtonEnableTime] = useState(15000)
  const vcardSeriesDetails = useSelector(
    state => state?.login?.vcardSeriesDetails
  )
  const region = localStorage.getItem('region')
  const vcardDetails = useSelector(state => state?.login?.vcardDetails)
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { state } = useLocation()
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const appMetaDataVideo = useSelector(
    state => state?.initialReducer?.appMetaDataVideo
  )
  const anonymousUser = useSelector(state => state?.login?.anonymousUser)
  const loginapi = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(state => state?.login?.registerSuccess?.response)
  const watchFree = useSelector(state => state?.login?.watchFreestate)

  const videoTutorialData =
    appMetaDataVideo?.versionUpdate_tutorial_control_options &&
    JSON?.parse(appMetaDataVideo?.versionUpdate_tutorial_control_options)

  const navigateScreen = () => {
    if (videoRef.current) {
      videoRef.current.pause()
    }
    switch (true) {
      case state?.fromDetailsPage:
        navigate('/watchprofile', {
          state: {
            data: '',
            seriesEpisodeData: state?.seriesEpisodeData,
            fromDetailsPage: state?.fromDetailsPage,
            pageName: state?.pageName
          },
          replace: true
        })
        break
      case anonymousUser?.page == 'livePlayer':
        watchFree && dispatch(getIsLoggedinV1(false))
        dispatch(setSkeltonLoading(false))
        navigate('/livePlayer', {
          state: { showControls: 'live', grid: anonymousUser?.grid }
        })
        break
      case vcardSeriesDetails?.page == 'series':
        navigate('/series', {
          state: { data: vcardSeriesDetails?.vodSeries }
        })
        break
      case vcardDetails?.page == 'movies':
        navigate('/movies', {
          state: { vodData: vcardDetails?.vodMoviesData }
        })
        break
      case vcardSeriesDetails?.playerpage == 'playerrecord':
        navigate('/series', {
          state: { data: vcardSeriesDetails?.playerepisode }
        })
        break
      default:
        dispatch(getNavTabValue('homeuser'))
        dispatch(getNavBarClicked(true))
        navigate('/home',{
          state: { gaPreviousPath: state?.userType ? 'registro' : 'landing'}
        })
        break
    }
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  useEffect(() => {
    pushScreenViewEvent({screenName:'video_tutorial', screenData: loginapi ?? registerInfo, prevScreenName: state?.pageName ?? state?.gaPageName })
    document.getElementById('btnStart')?.focus()
    setAcceptButtonEnableTime(videoTutorialData?.[region]?.time_to_skip_video)
    dispatch(setSkeltonLoading(true))
  }, [])

  const showSkipButton = () => {
    document.getElementById('videoTutorial')?.play()
    setEnableSkip(true)
  }

  useEffect(() => {
    if (enableSkip) {
      const skipButton = document.getElementById('btnSkip')
      skipButton.classList.add('visible')
      skipButton.focus()

      const timer = setTimeout(() => {
        skipButton.classList.add('enabled')
      }, acceptButtonEnableTime)

      return () => clearTimeout(timer)
    }
  }, [enableSkip, acceptButtonEnableTime])

  return (
    <div className="Tutorial-Container">
      <video
        id="videoTutorial"
        muted
        ref={videoRef}
        preload="auto"
        onCanPlay={() => {
          dispatch(setSkeltonLoading(false))
        }}
      >
        <source
          src={
            videoTutorialData?.[region]?.video_colors_button
              ? videoTutorialData?.[region]?.video_colors_button
              : videoTutorialData?.[region]?.video_hold_button
          }
          type="video/mp4"
        />
      </video>
      {enableSkip ? (
        <>
          <button id="btnSkip" className="focusable" onClick={navigateScreen}>
            {truncateText('Accept', 30)}
          </button>
        </>
      ) : (
        <>
          <button id="btnStart" className="focusable" onClick={showSkipButton}>
            VER TUTORIAL
          </button>
        </>
      )}
    </div>
  )
}

export default React.memo(VideoTutorial)
