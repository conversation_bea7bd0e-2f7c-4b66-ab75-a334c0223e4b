import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import EpgDescription from './Description'
import { FixedSizeList as List } from 'react-window'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { DateMarker } from './TimeLineMarker'
import { calculateHoursDifference } from './EpgSupport'

import {
  getProgramDetailsData,
  getLiveTvRecording,
  getLiveReminder,
  getEpgLineup,
  getEpgFilteredData,
  getEpgChannel,
  getEpgFilterName
} from '../../../store/slices/EpgSlice'
import './EpgGrid.scss'
import { useSelector, useDispatch } from 'react-redux'
import ProgramDetails from '../../programdetails/ProgramDetails'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import {
  getLockedChannelsList,
  getSubscriptionInfo
} from '../../../store/slices/settingsSlice'
import moment from 'moment'
import EpgFilterScreen from './EpgFilterScreen'
import { pushNewInteractionContentEvent, pushScreenViewEvent } from '../../../GoogleAnalytics'
import { COMMON_URL } from '../../../utils/environment'
import { CONTENIDO_BLOQUEADO, INTERACTION_PLAYER, NOT_APPLICABLE, PLAYER, TV } from '../../../GoogleAnalyticsConstants'

function EpgGrid(props) {
  // Refs for DOM elements and timers
  const myElementRef = useRef(null)
  const keyDown = useRef(true)
  const chRef = useRef()
  const progRef = useRef()
  const desRef = useRef()
  const zapRefTimer = useRef(null)
  const intersectionObserver = useRef(null)

  // Navigation and dispatch
  const { channelErr } = props
  const navigate = useNavigate()
  const dispatch = useDispatch()

  // Memoized selectors to prevent unnecessary re-renders
  const region = useMemo(() => localStorage.getItem('region'), [])
  const epgMenu = useSelector(state => state?.epg?.epgMenu)
  const epgLineup = useSelector(state => state?.epg?.epgLineup)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const epgChannelBackup = useSelector(state => state?.epg?.epgChannelBackup)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )
  const playerChannelData = useSelector(state => state?.player?.channelData)
  const paywayTokenResponse = useSelector(
    state => state?.epg?.paywayToken?.response
  )
  const reminderList = useSelector(state => state?.epg?.ReminderLive?.response)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const getFavouriteList = useSelector(state => state?.epg?.favouriteLive)
  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getSubsInfo
  )
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const recordingList = useSelector(
    state => state?.epg?.RecordingList?.response
  )
  const seriesRecordingList = useSelector(
    state => state?.epg?.RecordingListSeries
  )
  const channelDownId = useSelector(state => state?.epg?.channelDownId)
  // State variables
  const [day, setDay] = useState(props?.checkPastContent || props?.checkPastContentState ? 0 : 1)
  const [epgChannel, setEpgChannel] = useState(
    epgSevenDaysData?.[day]?.channelResponse
  )
  const [epgChannelDesc, setEpgChannelDesc] = useState([])
  const [liveDetailFlag, setLiveDetailFlag] = useState(false)
  const [description, setDescription] = useState(epgChannel?.[0]?.events?.[0])
  const [dayDirection, setDayDirection] = useState('')
  const [programDetails, setProgramDetails] = useState('')
  const [showOptionsPanel, setShowOptionsPanel] = useState(false)
  const [showFavouriteChannel, setShowFavouriteChannel] = useState(false)
  const [unlockedChannelsCount, setUnlockedChannelsCount] = useState(0)
  const [eventAlert, setEventAlert] = useState({})
  const [buttonClicked, setButtonClicked] = useState(false)
  const [channelInformation, setChannelInformation] = useState('')
  const [filterScreenOpen, setFilterScreenOpen] = useState(false)
  const [filterHeader, setFilterHeader] = useState(false)
  const [firstChannel, setFirstChannel] = useState('')

  // Additional refs
  const storeFocus1 = useRef('')
  const storeFocus2 = useRef('')
  const timeStoredRef = useRef(null)
  const audioData = useRef(false)
  const firstRender = useRef(true)
  const gaContentDataRef = useRef(null)

  // Memoized calculations to prevent unnecessary re-renders
  const epgIndex = useMemo(() => {
    return playerChannelData?.epgIndex
      ? epgSevenDaysData?.[day]?.channelResponse?.findIndex(
          itrObj => itrObj.group_id == playerChannelData?.group_id
        ) || 0
      : 0
  }, [playerChannelData?.epgIndex, epgSevenDaysData, day, playerChannelData?.group_id])

  const [initialOffset, setInitialOffset] = useState(epgIndex)
  const currentIndex = useRef(initialOffset)

  // Memoized video element reference
  const videoElement = useMemo(() => document.getElementById('bitmovinplayer-video-player'), [])

  // Memoized current time calculation to prevent recalculation on every render
  const now = useMemo(() => {
    const today = new Date()
    return today.getFullYear() +
      '/' +
      (today.getMonth() + 1 < 10
        ? '0' + (today.getMonth() + 1)
        : today.getMonth() + 1) +
      '/' +
      (today.getDate() < 10 ? '0' + today.getDate() : today.getDate()) +
      ' ' +
      today.toLocaleTimeString('en-US', { hour12: false })
  }, [])

  // Optimized live player setter with memoization
  const setLivePlayer = useCallback((channelInfo) => {
    if (!channelInfo?.group_id) return

    const videoElement = document.getElementById('bitmovinplayer-video-player')
    if (videoElement) {
      videoElement.remove()
    }

    dispatch(
      getChannelData({
        group_id: channelInfo.group_id,
        timeshift: channelInfo?.group?.common?.timeshift,
        switchChannel: 'yes',
        epgIndex: 0
      })
    )
  }, [dispatch])

  const zapHandler = useCallback((channelInfo, programInfo, currentIndex) => {
    zapRefTimer.current && clearTimeout(zapRefTimer.current)
    zapRefTimer.current = setTimeout(() => {
      props?.handlePastContentDay(programInfo)
      dispatch(
        getChannelData({
          group_id: channelInfo?.group_id,
          timeshift: channelInfo?.group?.common?.timeshift,
          switchChannel: 'yes',
          startTime: programInfo?.unix_begin,
          endTime: programInfo?.unix_end,
          catchup: programInfo?.ext_catchup,
          epgIndex: currentIndex
        })
      )
      localStorage.setItem('programId', programInfo?.id)
      videoElement && videoElement.remove()
    }, 1000)
  }, [props?.handlePastContentDay, dispatch, videoElement])

  // Memoized translations and language settings
  const translations = useMemo(() =>
    apaMetaData?.translations ? JSON?.parse(apaMetaData?.translations) : null,
    [apaMetaData?.translations]
  )
  const apilanguage = useMemo(() => translations?.language?.[region], [translations, region])

  // Additional selectors
  const epgFilterName = useSelector(state => state?.epg?.epgFilterName?.payload)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(state => state?.login?.registerSuccess?.response)

  // Memoized event poster settings
  const eventPosterImage = useMemo(() => {
    const epgEventPoster = apaMetaData?.epg_show_event_poster
      ? JSON?.parse(apaMetaData?.epg_show_event_poster)
      : null
    return epgEventPoster?.[region]?.enable
  }, [apaMetaData?.epg_show_event_poster, region])

  // Debounced dispatch functions to prevent rapid API calls and improve performance
  const debouncedDispatch = useCallback((action, delay = 300) => {
    if (window.dispatchTimeout) {
      clearTimeout(window.dispatchTimeout)
    }

    window.dispatchTimeout = setTimeout(() => {
      dispatch(action)
    }, delay)
  }, [dispatch])

  // Debounced program details dispatch for better performance
  const debouncedProgramDetailsDispatch = useCallback((programDetails) => {
    if (window.programDetailsTimeout) {
      clearTimeout(window.programDetailsTimeout)
    }

    window.programDetailsTimeout = setTimeout(() => {
      dispatch(getProgramDetailsData(programDetails))
    }, 200) // Shorter delay for program details
  }, [dispatch])

  // Debounced EPG channel dispatch with caching
  const debouncedEpgChannelDispatch = useCallback((channelData) => {
    if (window.epgChannelTimeout) {
      clearTimeout(window.epgChannelTimeout)
    }

    // Simple cache key based on channel data
    const cacheKey = JSON.stringify(channelData)
    if (window.lastEpgChannelRequest === cacheKey) {
      return // Prevent duplicate requests
    }

    window.epgChannelTimeout = setTimeout(() => {
      window.lastEpgChannelRequest = cacheKey
      dispatch(getEpgChannel(channelData))
    }, 250)
  }, [dispatch])

  // Request cache cleanup
  useEffect(() => {
    return () => {
      // Cleanup timeouts and cache on unmount
      if (window.dispatchTimeout) clearTimeout(window.dispatchTimeout)
      if (window.programDetailsTimeout) clearTimeout(window.programDetailsTimeout)
      if (window.epgChannelTimeout) clearTimeout(window.epgChannelTimeout)
      if (window.focusRestoreTimeout) clearTimeout(window.focusRestoreTimeout)
      delete window.lastEpgChannelRequest
    }
  }, [])

  // Optimized initialization effect with comprehensive cleanup
  useEffect(() => {
    // Send analytics event
    pushScreenViewEvent({
      screenName: 'main_epg',
      screenData: userDetails,
      prevScreenName: 'live_player'
    })

    // Initialize program visibility observer
    getProgramVisibility()

    return () => {
      // Comprehensive cleanup to prevent memory leaks

      // Cleanup intersection observer
      if (intersectionObserver.current) {
        intersectionObserver.current.disconnect()
        intersectionObserver.current = null
      }

      // Cleanup all timers
      if (zapRefTimer.current) {
        clearTimeout(zapRefTimer.current)
        zapRefTimer.current = null
      }

      if (timeStoredRef.current) {
        timeStoredRef.current = null
      }

      // Cleanup DOM event listeners
      document.removeEventListener('keydown', keypresshandler)

      // Reset all refs to prevent memory leaks
      keyDown.current = null
      storeFocus1.current = null
      storeFocus2.current = null
      currentIndex.current = null
      gaContentDataRef.current = null
      myElementRef.current = null
      chRef.current = null
      progRef.current = null
      desRef.current = null
      audioData.current = null
      firstRender.current = null

      // Clear any remaining focus classes
      const focusedElements = document.querySelectorAll('.focus-class')
      focusedElements.forEach(element => {
        element.classList.remove('focus-class')
      })
    }
  }, [])

  // Separate effect for data fetching with proper dependencies
  useEffect(() => {
    if (userDetails?.session_stringvalue && userDetails?.session_userhash) {
      dispatch(getLiveReminder({
        hks: userDetails.session_stringvalue,
        user_hash: userDetails.session_userhash
      }))

      dispatch(getLockedChannelsList({
        hks: userDetails.session_stringvalue,
        user_hash: userDetails.session_userhash
      }))
    }

    if (userDetails?.user_token) {
      dispatch(getLiveTvRecording({ user_token: userDetails.user_token }))
    }
  }, [userDetails?.session_stringvalue, userDetails?.session_userhash, userDetails?.user_token, dispatch])

  // Initialize GA content data
  useEffect(() => {
    if (!epgChannel?.[initialOffset]) return

    const currentChannel = epgChannel[initialOffset]
    const initialProgramData = currentChannel?.events?.find(event =>
      now > event?.date_begin && now < event?.date_end
    )

    gaContentDataRef.current = {
      user_id: userDetails?.user_id || loginInfo?.user_id || registerInfo?.user_id,
      parent_id: userDetails?.parent_id || loginInfo?.parent_id || registerInfo?.parent_id,
      sign_up_method: 'correo electronico',
      suscriptions: props?.subscriptions,
      user_type: watchFree ? 'anonimo' : 'registrado',
      device: COMMON_URL?.device_type,
      device_model: COMMON_URL?.device_model,
      device_name: COMMON_URL?.device_name,
      authpn: COMMON_URL?.authpn,
      content_subsection: NOT_APPLICABLE,
      content_section: 'player',
      country: (userDetails?.country_code || loginInfo?.country_code || registerInfo?.country_code)?.toLowerCase(),
      content_type: 'tv',
      content_availability: 'por suscripcion',
      provider: currentChannel?.group?.common?.proveedor_name?.toLowerCase(),
      channel_name: currentChannel?.name?.toLowerCase(),
      content_id: currentChannel?.group_id,
      content_name: initialProgramData?.ext_original_name?.toLowerCase(),
      content_category: initialProgramData?.dvb_content?.toLowerCase()
    }

    // Find and set initial live event
    const initialLiveEvent = currentChannel?.events?.find(event => {
      return props?.checkPastContent
        ? localStorage.getItem('programId') === event.id
        : now > event?.date_begin && now < event?.date_end
    })

    if (initialLiveEvent && currentChannel) {
      setProgramDetails({
        programData: initialLiveEvent,
        channelData: currentChannel
      })
    }
  }, [epgChannel, initialOffset, now, props?.checkPastContent, userDetails, loginInfo, registerInfo, watchFree, props?.subscriptions])

  //Minute based render means need to use
  /*   useEffect(() => {
    const intervalId = setInterval(() => {
      setMinutePos(new Date().getMinutes())
    }, 5000)
    return () => clearInterval(intervalId)
  }, []) */

  // Optimized complete guide handler with memoization
  const CompleteGuidehandleClick = useCallback(() => {
    if (!epgChannelBackup) return

    dispatch(getEpgChannel(epgChannelBackup))

    const firstChannel = epgChannelBackup[1]?.channelResponse?.[0]
    if (firstChannel) {
      setLivePlayer(firstChannel)
    }
  }, [dispatch, epgChannelBackup, setLivePlayer])
  // Optimized text truncation with memoization
  const truncateText = useCallback((str, length = 100) => {
    if (!str) return ''

    const text = apilanguage?.[str] ?? str

    if (text?.length >= length) {
      return `${text.slice(0, length)}...`
    }

    return text
  }, [apilanguage])

  // Optimized useEffect with proper dependencies
  useEffect(() => {
    if (epgSevenDaysData?.[day]?.channelResponse) {
      setEpgChannel(epgSevenDaysData[day].channelResponse)
    }
  }, [epgSevenDaysData, day])

  // Optimized EPG lineup fetching
  useEffect(() => {
    if (epgMenu?.response?.nodes?.[0]?.id) {
      dispatch(getEpgLineup({ nodeid: epgMenu.response.nodes[0].id }))
    }
  }, [epgMenu?.response?.nodes, dispatch])

  // Memoized and optimized EPG data processing
  const processedEpgData = useMemo(() => {
    if (!epgMenu?.response?.nodes || !epgLineup?.response?.channels) {
      return {}
    }

    const objectA = {}

    // Initialize genre categories
    epgMenu.response.nodes.forEach(node => {
      const key = node?.text?.toLowerCase()
      if (key) {
        objectA[key] = []
      }
    })

    // Process channels and their genres
    epgLineup.response.channels.forEach(channel => {
      const genres = channel?.group?.common?.extendedcommon?.genres?.genre
      if (!genres || !channel.group_id) return

      genres.forEach(genreInfo => {
        const genreDesc = genreInfo?.desc?.toLowerCase()
        if (!genreDesc) return

        // Direct assignment for exact matches
        if (objectA[genreDesc] && !objectA[genreDesc].includes(channel.group_id)) {
          objectA[genreDesc].push(channel.group_id)
        }

        // Check for partial matches
        Object.keys(objectA).forEach(key => {
          if (key.includes(genreDesc) && !objectA[key].includes(channel.group_id)) {
            objectA[key].push(channel.group_id)
          }
        })
      })
    })

    return objectA
  }, [epgMenu?.response?.nodes, epgLineup?.response?.channels])

  // Dispatch processed data when it changes
  useEffect(() => {
    if (Object.keys(processedEpgData).length > 0) {
      dispatch(getEpgFilteredData(processedEpgData))
    }
  }, [processedEpgData, dispatch])

  // Optimized focus management with better performance
  const focusCurrentProgram = useCallback(() => {
    if (showOptionsPanel || filterScreenOpen) return

    const currentChannel = epgChannel?.[initialOffset]
    if (!currentChannel?.events?.length) return

    let targetEvent
    let eventIndex = 0

    if (day === 1) {
      // For current day, find live event
      const isPastEvent = localStorage.getItem('pastEvent') === 'true'
      const programId = localStorage.getItem('programId')

      targetEvent = currentChannel.events.find((event, index) => {
        const isTarget = isPastEvent
          ? programId === event.id
          : now > event?.date_begin && now < event?.date_end

        if (isTarget) {
          eventIndex = index
          return true
        }
        return false
      })
    } else if (props?.checkPastContent) {
      // For past content
      const programId = localStorage.getItem('programId')
      eventIndex = currentChannel.events.findIndex(event =>
        programId === event.id
      )
      targetEvent = currentChannel.events[eventIndex]
    }

    if (targetEvent) {
      desRef.current = eventIndex
      const elementId = `program-${initialOffset}-${eventIndex}`
      const element = document.getElementById(elementId)

      if (element) {
        myElementRef.current = element
        element.classList.add('focus-class')
        element.focus()
        currentIndex.current = initialOffset

        // Handle tooltip scrolling
        const tooltip = document.getElementsByClassName('tooltip')[0]
        if (tooltip && day === 1) {
          const rect = tooltip.getBoundingClientRect()
          if ((rect.x < 300 || rect.x > 1920) &&
              now > targetEvent.date_begin && now < targetEvent.date_end) {
            tooltip.scrollIntoView({ block: 'start', inline: 'center' })
          }
        }
      }
    }

    // Update description
    if (targetEvent && currentChannel) {
      getDescriptionFocus(targetEvent, currentChannel)
    }
  }, [epgChannel, initialOffset, showOptionsPanel, filterScreenOpen, day, now, props?.checkPastContent])

  // Focus management effect with cleanup
  useEffect(() => {
    let timeoutId

    if (!showOptionsPanel && !filterScreenOpen) {
      focusCurrentProgram()

      // Delayed focus for TV compatibility
      timeoutId = setTimeout(() => {
        if (myElementRef.current && !myElementRef.current.classList.contains('focus-class')) {
          focusCurrentProgram()
        }
      }, 1000)
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [focusCurrentProgram, showOptionsPanel, filterScreenOpen])

  // useEffect(() => {
  //   if (!showOptionsPanel && !filterScreenOpen && day == 1) {
  //     let intitalLiveEvent
  //     intitalLiveEvent =
  //       epgChannel?.length > 0 &&
  //       epgChannel?.[initialOffset]?.events?.find(each => {
  //         if (
  //           localStorage.getItem('pastEvent') == 'true'
  //             ? localStorage.getItem('programId') == each.id
  //             : now > each?.date_begin && now < each?.date_end
  //         ) {
  //           return true
  //         }
  //         return false
  //       })
  //     if (
  //       epgChannel &&
  //       epgChannel.length > 0 &&
  //       epgChannel[initialOffset] &&
  //       epgChannel[initialOffset].events
  //     ) {
  //       desRef.current = epgChannel[initialOffset]?.events?.findIndex(
  //         (itrObj, index) => {
  //           return itrObj?.id === intitalLiveEvent?.id
  //         }
  //       )

  //       if (desRef.current !== -1) {
  //         const elementId = `program-${initialOffset}-${desRef.current}`
  //         const element = document.getElementById(elementId)
  //         myElementRef.current = element
  //       }
  //     }
  //     if (myElementRef.current) {
  //       myElementRef.current?.classList?.add('focus-class')
  //       myElementRef.current?.focus()
  //       let tooltip = document.getElementsByClassName('tooltip')[0]
  //       if (
  //         (tooltip?.getBoundingClientRect().x < 300 ||
  //           tooltip?.getBoundingClientRect().x > 1920) &&
  //         now > intitalLiveEvent?.date_begin &&
  //         now < intitalLiveEvent?.date_end
  //       ) {
  //         tooltip.scrollIntoView({ block: 'start', inline: 'center' })
  //       }
  //       currentIndex.current = initialOffset
  //     }
  //     getDescriptionFocus(
  //       epgChannel?.[initialOffset]?.events[desRef.current],
  //       epgChannel?.[initialOffset]
  //     )

  //     chRef?.current?.scrollToItem(initialOffset, 'start')
  //     progRef?.current?.scrollToItem(initialOffset, 'start')
  //     currentIndex.current = initialOffset
  //   }
  //   getProgramVisibility()
  // }, [isIdle]) // As the set of isIdle is commenting hence the effects are also commented

  // Optimized event alert with proper cleanup
  useEffect(() => {
    let alertTimeout

    if (eventAlert?.message) {
      alertTimeout = setTimeout(() => {
        setEventAlert({})
        const focusElement = document.getElementById(
          `program-${storeFocus1.current}-${storeFocus2.current}`
        )
        if (focusElement) {
          focusElement.focus()
        }
      }, 12000)
    }

    return () => {
      if (alertTimeout) {
        clearTimeout(alertTimeout)
      }
    }
  }, [eventAlert])

  // Optimized reminder availability check with memoization
  const isReminderAvailable = useCallback((programData) => {
    if (!programData?.id || typeof reminderList === 'string' || !reminderList) {
      return false
    }

    const isReminder = reminderList.filter(each => each?.event_id === programData.id)
    return isReminder.length > 0 && now < programData.date_begin
  }, [reminderList, now])

  // Optimized record availability check with memoization
  const isRecordAvailable = useCallback((eventData, channelData) => {
    if (!eventData || !channelData || !paywayTokenResponse?.paqs?.paq) {
      return false
    }

    const payway = paywayTokenResponse.paqs.paq.filter(
      each =>
        each?.groups?.includes(channelData.group_id) &&
        each?.npvrstorage !== 0 &&
        each?.timeshift !== 0
    )

    return (
      payway.length > 0 &&
      eventData.ext_recordable === '1' &&
      channelData?.group?.common?.timeshift !== null
    )
  }, [paywayTokenResponse?.paqs?.paq])

  // Optimized favourite check with memoization
  const handleFavourite = useCallback((grp_id) => {
    if (!grp_id || !getFavouriteList?.response?.groups) {
      return false
    }

    return getFavouriteList.response.groups.some(item => item.id === grp_id)
  }, [getFavouriteList?.response?.groups])

  //Removed the below getRecordText function for the req mentioned in MAUP-11109
  // const getRecordText = event => {
  //   const recordCheckValue = event?.event_alf_id
  //   const liveTVEpisodes = recordingList?.recordings?.find(
  //     each => each?.channel?.event?.event_alf_id == recordCheckValue
  //   )

  //   let liveTVSeries = []
  //   seriesRecordingList?.map(each =>
  //     each?.seriesResponse?.recordings?.map(every =>
  //       every?.channel?.event?.event_alf_id == recordCheckValue
  //         ? liveTVSeries.push(every)
  //         : null
  //     )
  //   )

  //   switch (
  //     liveTVEpisodes?.recording_status ??
  //     liveTVSeries?.[0]?.recording_status
  //   ) {
  //     case 0:
  //       return now < event?.date_begin
  //         ? `${truncateText('Metadata_TextoGrabacion_PorGrabar', 30)}`
  //         : `${truncateText('Metadata_TextoGrabacion_Grabando', 30)}`
  //     case 2:
  //       return `${truncateText('Metadata_TextoGrabacion_Grabado', 30)}`
  //     default:
  //       return `${truncateText('Player_Boton_TextoAccion_Grabar', 30)}`
  //   }
  // }

  // Optimized record icon getter with memoization
  const getRecordIcon = useCallback((event) => {
    if (!event?.event_alf_id) {
      return 'images/LiveTv/ic_recording_gray_epg.png'
    }

    const recordCheckValue = event.event_alf_id
    const liveTVEpisodes = recordingList?.recordings?.find(
      each => each?.channel?.event?.event_alf_id === recordCheckValue
    )

    let liveTVSeries = []
    if (seriesRecordingList) {
      seriesRecordingList.forEach(each => {
        each?.seriesResponse?.recordings?.forEach(every => {
          if (every?.channel?.event?.event_alf_id === recordCheckValue) {
            liveTVSeries.push(every)
          }
        })
      })
    }

    const recordingStatus = liveTVEpisodes?.recording_status ?? liveTVSeries?.[0]?.recording_status

    switch (recordingStatus) {
      case 0:
        return 'images/Program_Details_Icons/record_icon.png'
      case 2:
        return 'images/Record_Complete.png'
      default:
        return 'images/LiveTv/ic_recording_gray_epg.png'
    }
  }, [recordingList?.recordings, seriesRecordingList])

  // Optimized locked channels check with memoization
  const getLockedChannelsIcon = useCallback((item) => {
    if (!item || !lockedChannelsList) {
      return false
    }

    return lockedChannelsList.some(each => each.id === item)
  }, [lockedChannelsList])

  useEffect(() => {
    // Count unlocked channels
    const unlockedCount =
      epgChannel?.length > 0 &&
      epgChannel?.filter(channel => !getLockedChannelsIcon(channel.group_id))
        .length
    // Commenting the below code as lockedChannelsCount is nowhere used
    //  const lockedCount =
    //   epgChannel?.length > 0 &&
    //   epgChannel?.filter(channel => getLockedChannelsIcon(channel.group_id))
    //     .length
    // setLockedChannelsCount(lockedCount)
    setUnlockedChannelsCount(unlockedCount)
  }, [epgChannel, getLockedChannelsIcon])

  const isLastUnlockedChannel = () => {
    // Check if there's only one unlocked channel left
    return unlockedChannelsCount === 1
  }

  // Memoized helper functions for better performance
  const formatChannelNumber = useCallback((number) => {
    if (number < 10) return `00${number}`
    if (number < 100) return `0${number}`
    return number.toString()
  }, [])

  const isChannelLocked = useCallback((groupId) => {
    return lockedChannelsList?.some(channel => channel.id === groupId) || false
  }, [lockedChannelsList])

  const isChannelFavorite = useCallback((groupId) => {
    return getFavouriteList?.response?.groups?.some(item => item.id === groupId) || false
  }, [getFavouriteList?.response?.groups])

  // Optimized ChannelRow component with better memoization
  const ChannelRow = useCallback(
    ({ index, style }) => {
      const channel = epgChannel?.[index]
      if (!channel) return null

      const isLocked = isChannelLocked(channel.group_id)
      const isFavorite = isChannelFavorite(channel.group_id)
      const channelNumber = formatChannelNumber(channel.number)

      return (
        <div key={index} style={style} className="channel-scroll">
          <div className="epg-channel">
            <td scope="row">
              <div className="epg-channel-container">
                <div className="block-channel">
                  {isLocked && (
                    <img
                      className="block-icon-image"
                      src="images/lock_icon_liveTV.png"
                      alt="Locked channel"
                    />
                  )}
                </div>
                <div
                  className="channel"
                  style={{ opacity: isLocked ? '0.5' : '1' }}
                >
                  <div className="channel-details">
                    <div className="channel-number">{channelNumber}</div>
                    {isFavorite && (
                      <div className="epg-fav-channel">
                        <img
                          src="images/Program_Details_Icons/favorites_icon.png"
                          className="epg-fav-icon"
                          alt="Favorite channel"
                        />
                      </div>
                    )}
                  </div>
                  <div className="channel-container">
                    <div className="channel-image">
                      <LazyLoadImage
                        src={channel.image || 'images/Placeholder_FullEpg_Inactiva_new.png'}
                        placeholderSrc="images/Placeholder_FullEpg_Inactiva_new.png"
                        width={88.11}
                        height={49.51}
                        alt={`Channel ${channel.name}`}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </div>
        </div>
      )
    },
    [epgChannel, isChannelLocked, isChannelFavorite, formatChannelNumber]
  )

  // Memoized current time calculation for ProgramRow
  const currentDayTime = useMemo(() => {
    const currentDay = moment().add(day - 1, 'day')
    return currentDay.format('YYYY/MM/DD HH:mm:ss')
  }, [day])

  // Helper function to find live program index
  const findLiveProgramIndex = useCallback((events, currentTime) => {
    if (!events?.length) return 0

    return events.findIndex(event =>
      currentTime > event?.date_begin && currentTime < event?.date_end
    ) || 0
  }, [])

  // Optimized ProgramRow component
  const ProgramRow = useCallback(
    ({ index, style }) => {
      const channel = epgChannel?.[index]
      if (!channel?.events?.length) return null

      const previousChannel = index - 1
      const nextChannel = index + 1
      const previousChannelLiveProgram = previousChannel >= 0
        ? findLiveProgramIndex(epgChannel[previousChannel]?.events, currentDayTime)
        : 0
      const nextChannelLiveProgram = nextChannel < epgChannel.length
        ? findLiveProgramIndex(epgChannel[nextChannel]?.events, currentDayTime)
        : 0

      return (
        <div key={index} style={style}>
          <div className="main-epg-nav">
            {epgChannel?.[index]?.events?.map((program, index2) => (
              <>
                {index2 === 0 &&
                  program.date_begin?.split(' ')[1] !== ' 00:00:00' &&
                  program.date_begin >
                    program.date_end?.split(' ')[0] + ' 00:00:00' &&
                  (() => {
                    let pgmWidth = totalDuration(
                      calculateHoursDifference(
                        program?.date_begin?.split(' ')[0] + ' 00:00:00',
                        program?.date_begin
                      )
                    )
                    //code needed for future reference
                    // const programBeginTime = moment(program?.date_begin).format(
                    //   'HH:mm:ss'
                    // )
                    // const extraDivCellId = 'extra-div-cell'
                    // const extraDivFutureEvent =
                    //   programBeginTime >
                    //   moment(hourGenerator(0), 'HH.mm[hs]').format('HH:mm:ss')
                    // extraDivFutureEvent && setExtraDiv(true)
                    return (
                      // extraDivFutureEvent && (
                      <div className="extra-div-cell ">
                        <div
                          className="extra-div-content program focusable"
                          tabIndex={1}
                          id={`extra-div-cell-${index}`}
                          data-sn-up={
                            index != 0 &&
                            `#program-${previousChannel}-${previousChannelLiveProgram ?? 0}`
                          }
                          data-sn-down={
                            index != epgChannel?.length - 1 &&
                            `#program-${nextChannel}-${nextChannelLiveProgram ?? 0}`
                          }
                          onFocus={() => setDescription({ name: 'NA' })}
                          style={{
                            width: totalDuration(
                              calculateHoursDifference(
                                program?.date_begin.split(' ')[0] + ' 00:00:00',
                                program?.date_begin
                              )
                            ),
                            //code needed for future reference
                            /*    getProgramWidth(
                              program?.duration,
                              program?.date_begin,
                              program?.date_end,
                              index2,
                              epgChannel?.[index]?.number,
                              extraDivCellId
                             ), */

                            height: '110.67px',
                            backgroundColor: '#1a1a1a',
                            border: '2px solid #585858'
                          }}
                        >
                          <div className="program-details-wrapper">
                            <p className="extra-div-content-title">
                              {pgmWidth <= 100
                                ? 'No...'
                                : pgmWidth <= 200
                                ? 'No dis...'
                                : pgmWidth <= 300
                                ? 'No dispo....'
                                : 'No disponible'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )
                    //)
                  })()}

                <div className="epg-program">
                  <div
                    //ref={(el) => refs.current[index2] = el}
                    style={{
                      width: getProgramWidth(
                        program?.duration,
                        program?.date_begin,
                        program?.date_end,
                        index2,
                        epgChannel?.[index]?.number,
                        false,
                        program
                      )
                    }}
                    className={
                      !filterScreenOpen ? 'program focusable' : 'program'
                    }
                    id={`program-${index}-${index2}`}
                    tabIndex={1}
                    data-sn-up={
                      index != 0 &&
                      `#program-${previousChannel}-${previousChannelLiveProgram ?? 0}`
                    }
                    data-sn-down={
                      index != epgChannel?.length - 1 &&
                      `#program-${nextChannel}-${nextChannelLiveProgram ?? 0}`
                    }
                    onBlur={event =>
                      placeHolderHandleFocus(
                        event,
                        `epg-pgm-poster-${index}-${index2}`
                      )
                    }
                    onFocus={() => {
                      storeFocus1.current = index
                      storeFocus2.current = index2
                      scrollRightFunction(`program-${index}-${index2}`)
                    }}
                    onKeyUp={event => {
                      getDescription(
                        event,
                        program,
                        epgChannel?.[index],
                        index,
                        index2,
                        'epg'
                      ),
                        getDescriptionFocus(program, epgChannel?.[index])
                      placeHolderHandleFocus(
                        event,
                        `epg-pgm-poster-${index}-${index2}`
                      ),
                        (keyDown.current = false)
                      //Fixed focus logic need for future implementation
                      // checkMove(`program-${index}-${index2}`)
                    }}
                  >
                    <div
                      className="program-details-wrapper"
                      id={`wrap-${index}-${index2}`}
                    >
                      {getDuration(program?.duration) >= 20 &&
                      eventPosterImage ? (
                        <div className="program-image">
                          {program?.ext_eventimage_name ? (
                            <LazyLoadImage
                              id={`epg-pgm-poster-${index}-${index2}`}
                              src={program?.ext_eventimage_name}
                              placeholderSrc={
                                'images/Placeholder_FullEpg_inactiva.png'
                              }
                              wrapperClassName={'poster-image'}
                              width={161}
                              height={90}
                            />
                          ) : (
                            <LazyLoadImage
                              id={`epg-pgm-poster-${index}-${index2}`}
                              src={epgChannel?.[index]?.image}
                              placeholderSrc={
                                'images/Placeholder_FullEpg_inactiva.png'
                              }
                              wrapperClassName={'poster-image'}
                              width={161}
                              height={90}
                            />
                          )}
                        </div>
                      ) : (
                        <></>
                      )}
                      {/* 
                      Commenting and keeping for future referrence
                      <div
                        className="program-details"
                        // style={{
                        //   minWidth:
                        //     getDuration(program?.duration) > 20 &&
                        //     eventPosterImage
                        //       ? '70%'
                        //       : '95%'
                        // }}
                      > */}
                      <div
                        className="program-details"
                        style={{ overflow: 'hidden' }} //adding inline css as the className is not getting for random scenarios , probably a css loader issue
                      >
                        <div className="program-title">
                          <span
                            className="program-title-span"
                            // Commenting and keeping for future referrence
                            // style={{
                            //   minWidth : "5%"
                            //   // width: isRecordAvailable(
                            //   //   program,
                            //   //   epgChannel?.[index]
                            //   // )
                            //   //   ? '85%'
                            //   //   : '100%'
                            // }}
                          >
                            {program?.name === 'NA'
                              ? 'No disponible'
                              : program?.name}
                          </span>

                          {isRecordAvailable(program, epgChannel?.[index]) && (
                            <span
                              className={
                                getDuration(program?.duration) <= 10
                                  ? 'epg-grid-program-record-img2'
                                  : 'epg-grid-program-record-img'
                              }
                            >
                              <LazyLoadImage
                                src={getRecordIcon(program)}
                                width={32}
                                height={32}
                              />
                              {/* Removed for the req mentioned in MAUP-11109 <span className="epg-grid-record-text"> 
                                {getRecordText(program)}
                              </span> */}
                            </span>
                          )}
                        </div>
                        <div className="program-time">
                          {program?.name !== 'NA'
                            ? `${formatDate(
                                program?.date_begin
                              )} - ${formatDate(program?.date_end)}`
                            : ''}
                          {isReminderAvailable(program) && (
                            <span className="epg-grid-program-reminder-img">
                              <LazyLoadImage
                                src={'images/reminder_icon_small.png'}
                                width={30}
                              />
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ))}
          </div>
        </div>
      )
    },
    [epgChannel, filterScreenOpen]
  )

  useEffect(() => {
    if (dayDirection == 'left') {
      document
        .getElementById(
          `program-${initialOffset}-${
            epgChannel?.[initialOffset]?.events?.length - 1
          }`
        )
        ?.focus()
      setDayDirection('')
    } else if (dayDirection == 'right') {
      document.getElementById(`program-${initialOffset}-${0}`)?.focus()
      setDayDirection('')
    }
  }, [epgChannel])
  
  useEffect(() => {
    let programIndex

    epgChannel?.[
      dayDirection == 'up_zap' ? currentIndex.current : epgChannel?.length - 1
    ]?.events?.findIndex((itrObj, index) => {
      now > itrObj?.date_begin && now < itrObj?.date_end
        ? (document
            .getElementById(
              `program-${
                dayDirection == 'up_zap'
                  ? currentIndex.current
                  : epgChannel?.length - 1
              }-${index}`
            )
            ?.focus(),
          (programIndex = index))
        : null
    })

    if (dayDirection == 'up' || dayDirection == '') {
      document
        .getElementById(`program-${epgChannel?.length - 1}-${programIndex}`)
        ?.focus()
    } else if (dayDirection == 'up_zap') {
      document
        .getElementById(`program-${currentIndex.current}-${programIndex}`)
        ?.focus()
    }
    dayDirection != 'up_zap' && setDayDirection('')
  }, [dayDirection, ChannelRow, ProgramRow])

  useEffect(() => {
    let programIndex

    epgChannel?.[currentIndex.current]?.events?.findIndex((itrObj, index) => {
      if (day == 1) {
        (
          localStorage.getItem('pastEvent') == 'true'
            ? localStorage.getItem('programId') == itrObj.id
            : now > itrObj?.date_begin && now < itrObj?.date_end
        )
          ? (document
              .getElementById(`program-${currentIndex.current}-${index}`)
              ?.focus(),
            (programIndex = index))
          : null
      } else if (day == 0) {
        (
          props?.checkPastContent == true
            ? localStorage.getItem('programId') == itrObj.id
            : now > itrObj?.date_begin && now < itrObj?.date_end
        )
          ? (document
              .getElementById(`program-${currentIndex.current}-${index}`)
              ?.focus(),
            (programIndex = index))
          : null
      }
    })

    if (firstChannel == 'firstChannel') {
      document.getElementById(`program-${0}-${programIndex}`)?.focus()
    } else if (firstChannel == 'firstChannel_zap') {
      document
        .getElementById(`program-${currentIndex.current}-${programIndex}`)
        ?.focus()
    }
    setFirstChannel('')
  }, [firstChannel, ChannelRow, ProgramRow])

  // Optimized program visibility observer with proper cleanup
  const getProgramVisibility = useCallback(() => {
    // Clean up existing observer
    if (intersectionObserver.current) {
      intersectionObserver.current.disconnect()
    }

    const programElements = document.querySelectorAll('.program')
    if (programElements.length === 0) return

    intersectionObserver.current = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (!entry.isIntersecting) return

          const targetElement = entry.target?.firstChild
          if (!targetElement?.id) return

          const element = document.getElementById(targetElement.id)
          if (!element) return

          const rect = entry.boundingClientRect
          const isLargeItem = rect.left < 216 && rect.width > 300

          if (isLargeItem) {
            const marginLeft = rect.left < 216 && rect.left > 0
              ? 236 - Math.abs(rect.left)
              : Math.abs(rect.left) + 236
            element.style.marginLeft = `${marginLeft}px`
          } else {
            element.style.marginLeft = '16px'
          }
        })
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
      }
    )

    programElements.forEach(element => {
      intersectionObserver.current.observe(element)
    })
  }, [])

  // Optimized contract channel check with memoization
  const getIsContractChanel = useCallback((item) => {
    if (!item || !paywayTokenResponse?.paqs?.paq) {
      return true
    }

    const foundContract = paywayTokenResponse.paqs.paq.filter(each =>
      each?.groups?.includes(item)
    )

    return foundContract.length === 0
  }, [paywayTokenResponse?.paqs?.paq])

  const getDescription = (
    event,
    programInfo,
    channelInfo,
    channelIndex,
    programIndex,
    data
  ) => {
    // setDescription(programInfo)
    // setEpgChannelDesc(channelInfo)
    // // setLiveDetailFlag(flag)
    // setProgramDetails({ programData: programInfo, channelData: channelInfo })
    gaContentDataRef.current['provider'] = channelInfo?.group?.common?.proveedor_name?.toLowerCase()
    gaContentDataRef.current['channel_name'] = channelInfo?.name?.toLowerCase()
    gaContentDataRef.current['content_id'] = channelInfo?.group_id
    gaContentDataRef.current['content_name'] = programInfo?.ext_original_name?.toLowerCase()
    gaContentDataRef.current['content_category'] = programInfo?.dvb_content?.toLowerCase()

    const isBlocked = getLockedChannelsIcon(channelInfo?.group_id)
    // if (!isBlocked) {  //for future reference
    if (
      programIndex == 0 &&
      day > 0 &&
      (event?.key == 'ArrowLeft' || event?.keyCode == 37)
    ) {
      setInitialOffset(channelIndex)
      setDayDirection('left')
      setDay(day => day - 1)
      setEpgChannel(epgSevenDaysData[day - 1]?.channelResponse)
    } else if (
      programIndex == channelInfo?.events?.length - 1 &&
      day < 6 &&
      (event?.key == 'ArrowRight' || event?.keyCode == 39)
    ) {
      setInitialOffset(channelIndex)
      setDayDirection('right')
      setDay(day => day + 1)
      setEpgChannel(epgSevenDaysData[day + 1]?.channelResponse)
    }
    //  }
    const isContractChannel = getIsContractChanel(channelInfo?.group_id)
    let sameChannelPlay =
      channelInfo?.group_id == localStorage.getItem('live-channel-id')

    if (event?.keyCode == 13 || event?.key == 'Enter') {
      if (watchFree && isContractChannel) {
        navigate('/EPconfirmation', {
          state: { page: 'livePlayer', data: channelInfo, grid: 'mainEpg', gaContentData: gaContentDataRef.current }
        })
      }
      if (isBlocked && !sameChannelPlay) {
        // Navigate to security pin page
        gaContentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
        dispatch(
          getChannelData({
            group_id: channelInfo?.group_id,
            timeshift: channelInfo?.group?.common?.timeshift,
            switchChannel: 'yes',
            startTime: programInfo?.unix_begin, //Start time and end time are being passed so as to be used by player component for playing past events
            endTime: programInfo?.unix_end,
            catchup: programInfo?.ext_catchup,
            epgIndex: channelIndex
          })
        )
        dispatch(
          getProgramDetailsData({
            programData: programInfo,
            channelData: channelInfo
          })
        )
        gaContentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
        gaContentDataRef.current['content_section'] = PLAYER
        navigate('/my-settings/help-And-Settings/security-pin/configure', {
          state: {
            data,
            item: channelInfo,
            pageName: '/livePlayer',
            pastProgramData: programInfo,
            fromZap: false,
            checkPastContent: props?.checkPastContent,
            gaContentData: gaContentDataRef.current,
          }
        })
      }

      //checking purchaseble or not
      else if (isContractChannel) {
        callPurchaseApi(isContractChannel, channelInfo)
      }

      //Future Event
      else if (now < programInfo?.date_begin) {
        setEventAlert({
          message: (
            <span>
              <b>
                {truncateText(
                  'TvEnVivo_Notificacion_TextoTitulo_AunNoDisponible',
                  30
                )}
                {' | '}
              </b>
              {truncateText(
                'TvEnVivo_Notificacion_TextoCuerpo_EventoAunNoDisponible',
                30
              )}
            </span>
          ),
          status: 'future-event'
        })
        dispatch(
          getProgramDetailsData({
            programData: programInfo,
            channelData: channelInfo
          })
        )
      }

      //Past Event
      else if (now > programInfo?.date_begin && now > programInfo?.date_end) {
        const payway = paywayTokenResponse?.paqs?.paq?.filter(
          each =>
            each?.groups?.includes(channelInfo?.group_id) &&
            each?.npvrstorage != 0 &&
            each?.timeshift != 0
        )
        if (
          payway?.length > 0 &&
          Number(channelInfo?.group?.common?.timeshift) > 0 &&
          programInfo?.ext_catchup == 1 &&
          moment().unix() - programInfo?.unix_begin <=
            payway?.[0]?.timeshift * 60 * 60
        ) {
          pushNewInteractionContentEvent(
            {
              modulo_name: 'guia de programacion',
              ...gaContentDataRef.current
            },
            INTERACTION_PLAYER,
            'tv'
          )
          props?.handlePastContentDay(programInfo)
          dispatch(
            getChannelData({
              group_id: channelInfo?.group_id,
              timeshift: channelInfo?.group?.common?.timeshift,
              switchChannel: 'yes',
              startTime: programInfo?.unix_begin,
              endTime: programInfo?.unix_end,
              catchup: programInfo?.ext_catchup,
              epgIndex: channelIndex
            })
          )
          dispatch(
            getProgramDetailsData({
              programData: programInfo,
              channelData: channelInfo
            })
          )
          props?.setShowLiveControls('')
          localStorage.setItem('programId', programInfo?.id)
          localStorage.setItem('pastEvent', true)
        } else {
          setEventAlert({
            message: (
              <span>
                <b>
                  {truncateText(
                    'TvEnVivo_Notificacion_TextoTitulo_AccionNoDisponible',
                    30
                  )}
                  {' | '}
                </b>
                {truncateText(
                  'TvEnVivo_Notificacion_TextoCuerpo_EventoNoDisponible',
                  30
                )}
              </span>
            ),
            status: 'Past-Event'
          })
        }
      }

      //Present Event
      else {
        if (programInfo?.id == localStorage.getItem('programId')) {
          pushNewInteractionContentEvent(
            {
              modulo_name: 'guia de programacion',
              ...gaContentDataRef.current
            },
            INTERACTION_PLAYER,
            TV
          )
          dispatch(
            getProgramDetailsData({
              programData: programInfo,
              channelData: channelInfo
            })
          )
          setShowOptionsPanel(true)
        } else {
          pushNewInteractionContentEvent(
            { 
              modulo_name: 'guia de programacion', 
              ...gaContentDataRef.current 
            },
            INTERACTION_PLAYER,
            'tv'
          )
          props?.handlePastContentDay(programInfo)
          dispatch(
            getChannelData({
              group_id: channelInfo?.group_id,
              timeshift: channelInfo?.group?.common?.timeshift,
              switchChannel: 'yes',
              startTime: programInfo?.unix_begin, //Start time and end time are being passed so as to be used by player component for playing past events
              endTime: programInfo?.unix_end,
              catchup: programInfo?.ext_catchup,
              epgIndex: channelIndex
            })
          )
          dispatch(
            getProgramDetailsData({
              programData: programInfo,
              channelData: channelInfo
            })
          )
          props?.setShowLiveControls('')
          localStorage.setItem('programId', programInfo?.id)
          localStorage.setItem('pastEvent', true)
        }
      }
    }

    //to register tizen and lg device key code
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF3Blue'])
      const codes = {
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
   getProgramVisibility()
    event?.preventDefault()
  }

  const getDescriptionFocus = (programInfo, channelInfo) => {
    if (
      description?.id !== programInfo?.id ||
      description?.id == programInfo?.id
    ) {
      setDescription(programInfo)
      setEpgChannelDesc(channelInfo)
      setProgramDetails({ programData: programInfo, channelData: channelInfo })
    }
  }

  // Optimized placeholder focus handler with reduced DOM queries
  const placeHolderHandleFocus = useCallback((event, id) => {
    const element = document.getElementById(id)
    if (!element) return

    const isKeyUp = event?.type === 'keyup'
    const currentSrc = element.src

    if (isKeyUp) {
      if (currentSrc.includes('images/Placeholder_FullEpg_inactiva.png')) {
        element.src = 'images/Placeholder_FullEpg_Activa.png'
      }
    } else {
      if (currentSrc.includes('images/Placeholder_FullEpg_Activa.png')) {
        element.src = 'images/Placeholder_FullEpg_inactiva.png'
      }
    }
  }, [])

  // Optimized day change effect
  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false
    } else {
      getProgramVisibility()
    }
  }, [day, getProgramVisibility])

  // Optimized scroll function with reduced DOM queries and calculations
  const scrollRightFunction = useCallback((id) => {
    const element = document.getElementById(id)
    const wrapper = document.getElementById('epg-grid-wrapper-id')

    if (!element || !wrapper) return

    const rect = element.getBoundingClientRect()
    const rightPosition = rect.right

    if (rightPosition < 250 && rightPosition > 0) {
      wrapper.scrollLeft -= 250 - rightPosition
    }
  }, [])
  const callPurchaseApi = useCallback((isContractChannel, channelInfo) => {
    if (isContractChannel) {
      setChannelInformation(channelInfo)
      setButtonClicked(true)
      dispatch(
        getSubscriptionInfo({
          userId: userDetails?.parent_id,
          hks: userDetails?.session_stringvalue,
          url: `group_id=${channelInfo?.group?.common?.id}`
        })
      )
    }
  }, [])

  useEffect(() => {
    if (buttonClicked && addSubscriptions?.msg == 'OK') {
      const subscriptionResponse = addSubscriptions?.response?.listButtons
      const buttonMap = subscriptionResponse?.button ?? []
      if (buttonMap?.length > 0) {
        if (buttonMap?.length == 1) {
          buttonMap?.map(each => {
            if (each?.purchasable) {
              navigate('/premiumSubscription', {
                state: {
                  data: channelInformation?.group?.common,
                  priceDetails: each,
                  returnPage: 'epgToPlayer'
                }
              })
            } else {
              handleNavigateToNonContract(channelInformation)
            }
          })
        } else {
          const subScriptionValuesFalse = buttonMap.every(
            item => item?.purchasable === false
          )
          if (subScriptionValuesFalse) {
            handleNavigateToNonContract(channelInformation)
          } else {
            const subscriptionTrueValues = buttonMap.every(
              item => item?.purchasable === true
            )
            const singleSubscription = buttonMap?.filter(
              item => item?.purchasable === true
            )
            const subscriptionNodeValueFalse = buttonMap?.filter(
              item => item?.purchasable === false
            )
            const exactlySingleSubscriptionTrue =
              singleSubscription?.length === 1 &&
              subscriptionNodeValueFalse?.length === buttonMap?.length - 1
            if (subscriptionTrueValues) {
              navigate('/multiSubscription', {
                state: {
                  pageName: 'livePlayer',
                  data: channelInformation?.group?.common ?? channelInformation,
                  type: 'MultiSubscription'
                }
              })
            } else if (exactlySingleSubscriptionTrue) {
              navigate('/premiumSubscription', {
                state: {
                  data: channelInformation?.group?.common,
                  priceDetails: singleSubscription[0],
                  returnPage: 'epgToPlayer'
                }
              })
            }
          }
        }
      } else {
        handleNavigateToNonContract(channelInformation)
      }
    }
  }, [addSubscriptions])

  const handleNavigateToNonContract = channelInfoData => {
    navigate('/subScriptionCallPage', {
      state: {
        data: channelInfoData?.group?.common
          ? channelInfoData?.group?.common
          : channelInfoData,
        pageName: 'livePlayer'
      }
    })
  }

  // Optimized time calculation functions with memoization
  const timeToSeconds = useCallback((timeStr) => {
    if (!timeStr) return 0

    const [hours, minutes, seconds] = timeStr.split(':').map(Number)
    return hours * 3600 + minutes * 60 + seconds
  }, [])

  const totalDuration = useCallback((duration) => {
    if (!duration) return '0px'

    const totalDurationEvent =
      parseFloat(duration.slice(0, 2)) +
      (parseFloat(duration.slice(3, 5)) * 0.166667) / 10
    return `${totalDurationEvent * 637 * 2}px`
  }, [])

  const getProgramWidth = (
    duration,
    begin,
    end,
    i,
    _channelNumber, // unused but kept for compatibility
    flag,
    extraDivCellId
  ) => {
    if (i == 0) {
      let firstCell = extraDivCellId
      if (
        firstCell?.date_begin?.split(' ')[0] !=
        firstCell?.date_end?.split(' ')[0]
      ) {
        let startDate = firstCell?.date_end?.split(' ')[0] + ' 00:00:00'
        let duration = calculateHoursDifference(startDate, firstCell?.date_end)
        return totalDuration(duration)
      }
    }
    if (i !== 0) {
      return totalDuration(duration)
    } else {
      const epgTimescaleStartTime = moment(
        hourGenerator(0),
        'HH.mm[hs]'
      ).format('HH:mm:ss')
      // code for program begin and end time calculation
      const programBeginTime = moment(begin, 'YYYY/MM/DD HH:mm:ss').format(
        'HH:mm:ss'
      )
      const programEndTime = moment(end, 'YYYY/MM/DD HH:mm:ss').format(
        'HH:mm:ss'
      )

      //  Program width calculation for past programs
      if (programBeginTime < epgTimescaleStartTime) {
        // Diffrence b/w programEndTime and epgTimescaleStart, difference in seconds::
        let timeDifferenceInSecondsPastProgram =
          timeToSeconds(programEndTime) - timeToSeconds(epgTimescaleStartTime)
        // Calculate hours, minutes, and seconds from the difference
        const hoursPastProgram = Math.floor(
          timeDifferenceInSecondsPastProgram / 3600
        )
        timeDifferenceInSecondsPastProgram %= 3600
        const minutesPastProgram = Math.floor(
          timeDifferenceInSecondsPastProgram / 60
        )
        const secondsPastProgram = timeDifferenceInSecondsPastProgram % 60
        // Calculate total duration in hours for timeDifferenceEnd
        const totalDurationPastProgram =
          hoursPastProgram + minutesPastProgram / 60 + secondsPastProgram / 3600
        const pastProgramWidth = totalDurationPastProgram * 637 * 2 // Multiplying by 2 to match the scale for 30 mins = 637px
        return `${pastProgramWidth}px`
      }

      //  Program width calculation for Future programs
      else if (programBeginTime > epgTimescaleStartTime) {
        // Calculate the difference in seconds for programBeginTime to epgStartTime
        const timeDifferenceInSecondsFutureExtraDivProgram =
          timeToSeconds(programBeginTime) - timeToSeconds(epgTimescaleStartTime)
        // Convert the difference back to HH:mm:ss format
        const hoursFutureExtraDivProgram = Math.floor(
          timeDifferenceInSecondsFutureExtraDivProgram / 3600
        )
        const minutesFutureExtraDivProgram = Math.floor(
          (timeDifferenceInSecondsFutureExtraDivProgram % 3600) / 60
        )
        const secondsFutureExtraDivProgram =
          timeDifferenceInSecondsFutureExtraDivProgram % 60
        const extraProgramtime = `${
          hoursFutureExtraDivProgram < 10
            ? '0' + hoursFutureExtraDivProgram
            : hoursFutureExtraDivProgram
        }:${
          minutesFutureExtraDivProgram < 10
            ? '0' + minutesFutureExtraDivProgram
            : minutesFutureExtraDivProgram
        }:${
          secondsFutureExtraDivProgram < 10
            ? '0' + secondsFutureExtraDivProgram
            : secondsFutureExtraDivProgram
        }`

        // Convert extra duration to hours
        const extraTotalDuration =
          parseFloat(extraProgramtime.slice(0, 2)) +
          parseFloat(extraProgramtime.slice(3, 5)) / 60
        // Calculate width based on the total duration
        const extraWidth = extraTotalDuration * 637 * 2

        const extraDivFutureCell =
          document.getElementsByClassName('extra-div-cell')
        if (extraDivFutureCell && flag) {
          return `${extraWidth}px`
        }

        // code for future program event width calculation:
        return totalDuration(duration)
      }

      //  code for present program width calculation
      else {
        return totalDuration(duration)
      }
    }
  }

  // Optimized favourite channel timeout with proper cleanup
  useEffect(() => {
    let favouriteTimeout

    if (showFavouriteChannel) {
      favouriteTimeout = setTimeout(() => {
        setShowFavouriteChannel(false)
      }, 12000)
    }

    return () => {
      if (favouriteTimeout) {
        clearTimeout(favouriteTimeout)
      }
    }
  }, [showFavouriteChannel])

  // Optimized duration calculation with memoization
  const getDuration = useCallback((duration) => {
    if (!duration) return 0

    const durationHour = parseInt(duration.slice(0, 2), 10)
    const durationMinute = parseInt(duration.slice(3, 5), 10)

    if (durationHour === 0) {
      switch (true) {
        case durationMinute <= 5:
          return 5 / 2
        case durationMinute <= 10:
          return 10 / 2
        case durationMinute <= 15:
          return 15 / 2
        case durationMinute <= 20:
          return 20 / 1.5
        case durationMinute <= 25:
          return 25 / 1.5
        default:
          return 30 / 1.5
      }
    } else {
      return 60 / 1.5
    }
  }, [])

  // Optimized date formatting with memoization
  const formatDate = useCallback((date) => {
    return moment(date, 'YYYY-MM-DDTHH:mm:ss').format('HH:mm')
  }, [])

  // Enhanced focus restoration function with debouncing
  const restoreFocus = useCallback(() => {
    // Clear any existing focus restoration timeout
    if (window.focusRestoreTimeout) {
      clearTimeout(window.focusRestoreTimeout)
    }

    window.focusRestoreTimeout = setTimeout(() => {
      const currentElement = document.getElementById(
        `program-${storeFocus1.current}-${storeFocus2.current}`
      )
      if (currentElement && !currentElement.classList.contains('focus-class')) {
        // Remove focus from any other elements first
        const previousFocused = document.querySelector('.focus-class')
        if (previousFocused) {
          previousFocused.classList.remove('focus-class')
        }

        currentElement.focus()
        currentElement.classList.add('focus-class')
      } else if (!currentElement) {
        // Fallback to current program if stored focus is invalid
        const fallbackElement = document.getElementById(
          `program-${currentIndex.current}-0`
        )
        if (fallbackElement) {
          // Remove focus from any other elements first
          const previousFocused = document.querySelector('.focus-class')
          if (previousFocused) {
            previousFocused.classList.remove('focus-class')
          }

          fallbackElement.focus()
          fallbackElement.classList.add('focus-class')
          storeFocus1.current = currentIndex.current
          storeFocus2.current = 0
        }
      }
    }, 50) // Debounce focus restoration
  }, [])

  // Enhanced keypresshandler with aggressive throttling for continuous presses
  const keypresshandler = useCallback((event) => {
    const keycode = event?.keyCode
    const currentTime = Date.now()
    const diff = currentTime - (timeStoredRef.current || 0)

    // Speed key codes for different TV platforms
    const speedKeyCodes = [
      415, 416, 417, 418, // Samsung speed keys
      19, 20, 21, 22,     // LG speed keys
      413, 412,           // Additional speed keys
      174, 175, 176, 177  // Generic speed keys
    ]

    // Handle speed keys - prevent focus loss
    if (speedKeyCodes.includes(keycode)) {
      event.preventDefault()
      event.stopPropagation()
      // Restore focus after a brief delay to ensure speed action completes
      restoreFocus()
      return
    }

    // More aggressive throttling for continuous key presses
    const navigationKeys = [37, 38, 39, 40] // Arrow keys
    const isNavigationKey = navigationKeys.includes(keycode)

    // Different throttling for navigation vs other keys
    const throttleTime = isNavigationKey ? 0.15 : 0.5 // 150ms for navigation, 500ms for others

    if (diff / 1000 < throttleTime) {
      event.preventDefault()
      event.stopPropagation()
      // Still restore focus even when throttling
      if (speedKeyCodes.includes(keycode) || diff / 1000 < 0.1) {
        restoreFocus()
      }
      return
    }

    // Additional check for rapid continuous presses
    if (keyDown.current === true && diff / 1000 < 0.1) {
      event.preventDefault()
      event.stopPropagation()
      restoreFocus()
      return
    }
    switch (event?.keyCode) {
      case 38:
        //38 is the keycode for D-pad UP arrow
        setTimeout(() => {
          if (currentIndex.current == 0) {
            progRef?.current?.scrollToItem(epgChannel?.length - 1, 'end')
            currentIndex.current = epgChannel?.length - 1
            chRef?.current?.scrollToItem(epgChannel?.length - 1, 'end')
            setDayDirection('up')
          } else {
            currentIndex.current = currentIndex.current - 1
            chRef?.current?.scrollToItem(currentIndex.current - 1, 'center')
            progRef?.current?.scrollToItem(currentIndex.current - 1, 'center')
          }
        }, 0)
        break
      case 40:
        //40 is the keycode for D-pad DOWN arrow
        setTimeout(() => {
          if (currentIndex.current == epgChannel?.length - 1) {
            currentIndex.current = 0
            chRef?.current?.scrollToItem(0, 'start')
            progRef?.current?.scrollToItem(0, 'start')
            setFirstChannel('firstChannel')
          } else {
            currentIndex.current = currentIndex.current + 1
            chRef?.current?.scrollToItem(currentIndex.current + 1, 'center')
            progRef?.current?.scrollToItem(currentIndex.current + 1, 'center')
          }
        }, 0)
        break
      // 33 is the keycode for LG channel UP
      // 85 is the keycode for keyboard letter u
      case 33:
      case 85:
        setTimeout(() => {
          if (currentIndex.current != epgChannel?.length) {
            let localChannelCountLG =
              currentIndex.current == epgChannel?.length - 1
                ? 0
                : currentIndex.current + 1
            Array.from({ length: epgChannel?.length }).map(() => {
              if (
                getLockedChannelsIcon(
                  epgChannel?.[localChannelCountLG]?.group_id
                ) ||
                getIsContractChanel(epgChannel?.[localChannelCountLG]?.group_id)
              ) {
                ++localChannelCountLG
                if (localChannelCountLG > epgChannel?.length - 1) {
                  localChannelCountLG = 0
                  setFirstChannel('firstchannel_zap')
                }
              } else {
                return
              }
            })
            let programIndex
            currentIndex.current = localChannelCountLG
            chRef?.current?.scrollToItem(localChannelCountLG, 'center')
            progRef?.current?.scrollToItem(localChannelCountLG, 'center')
            epgChannel?.[localChannelCountLG]?.events?.findIndex(
              (itrObj, index) => {
                if (now > itrObj?.date_begin && now < itrObj?.date_end) {
                  document
                    .getElementById(`program-${localChannelCountLG}-${index}`)
                    ?.focus()
                  programIndex = index
                }
              }
            )
            zapHandler(
              epgChannel?.[localChannelCountLG],
              epgChannel?.[localChannelCountLG]?.events?.[programIndex],
              localChannelCountLG
            )
          }
        }, 0)
        break
      // 34 is the keycode for LG channel down
      //68 is the keycode for letter letter d
      case 34:
      case 68:
        setTimeout(() => {
          if (currentIndex.current != -1) {
            let chCountLG =
              currentIndex.current == 0
                ? epgChannel?.length - 1
                : currentIndex.current - 1
            let chDownFromFirstChannel =
              currentIndex.current == 0 ? true : false
            Array.from({ length: epgChannel?.length }).map(() => {
              if (
                getLockedChannelsIcon(epgChannel?.[chCountLG]?.group_id) ||
                getIsContractChanel(epgChannel?.[chCountLG]?.group_id)
              ) {
                --chCountLG
              } else {
                return
              }
            })
            let programIndex
            // setTimeout(() => {
            if (chDownFromFirstChannel) {
              progRef?.current?.scrollToItem(chCountLG, 'end')
              currentIndex.current = chCountLG
              chRef?.current?.scrollToItem(chCountLG, 'end')
              setDayDirection('up_zap')
            } else {
              currentIndex.current = chCountLG
              chRef?.current?.scrollToItem(chCountLG, 'center')
              progRef?.current?.scrollToItem(chCountLG, 'center')
            }
            // }, 0)
            epgChannel?.[chCountLG]?.events?.findIndex((itrObj, index) => {
              if (now > itrObj?.date_begin && now < itrObj?.date_end) {
                document
                  .getElementById(`program-${chCountLG}-${index}`)
                  ?.focus()
                programIndex = index
              }
            })
            zapHandler(
              epgChannel?.[chCountLG],
              epgChannel?.[chCountLG]?.events?.[programIndex],
              chCountLG
            )
          }
        }, 0)
        break
      // 427 is the keycode for Samsung channel UP
      case 427:
        setTimeout(() => {
          if (typeof tizen !== 'undefined') {
            tizen.tvinputdevice.registerKeyBatch(['ChannelUp', 'ChannelDown'])
            const codes = {
              chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
              chMinus: tizen.tvinputdevice.getKey('ChannelDown').code
            }
            if (
              codes.chPlus === event?.keyCode &&
              currentIndex.current != epgChannel?.length
            ) {
              let localChannelCountSamsung =
                currentIndex.current == epgChannel?.length - 1
                  ? 0
                  : currentIndex.current + 1
              Array.from({ length: epgChannel?.length }).map(() => {
                if (
                  getLockedChannelsIcon(
                    epgChannel?.[localChannelCountSamsung]?.group_id
                  ) ||
                  getIsContractChanel(
                    epgChannel?.[localChannelCountSamsung]?.group_id
                  )
                ) {
                  ++localChannelCountSamsung
                  if (localChannelCountSamsung > epgChannel?.length - 1) {
                    localChannelCountSamsung = 0
                    setFirstChannel('firstchannel_zap')
                  }
                } else {
                  return
                }
              })
              let programIndex
              currentIndex.current = localChannelCountSamsung
              chRef?.current?.scrollToItem(localChannelCountSamsung, 'center')
              progRef?.current?.scrollToItem(localChannelCountSamsung, 'center')
              epgChannel?.[localChannelCountSamsung]?.events?.findIndex(
                (itrObj, index) => {
                  if (now > itrObj?.date_begin && now < itrObj?.date_end) {
                    document
                      .getElementById(
                        `program-${localChannelCountSamsung}-${index}`
                      )
                      ?.focus()
                    programIndex = index
                  }
                }
              )
              zapHandler(
                epgChannel?.[localChannelCountSamsung],
                epgChannel?.[localChannelCountSamsung]?.events?.[programIndex],
                localChannelCountSamsung
              )
            }
          }
        }, 0)
        break
      // 428 is the keycode for Samsung channel DOWN
      case 428:
        setTimeout(() => {
          if (typeof tizen !== 'undefined') {
             tizen.tvinputdevice.registerKeyBatch(['ChannelUp', 'ChannelDown'])
             const codes = {
               chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
               chMinus: tizen.tvinputdevice.getKey('ChannelDown').code
             }
            if (codes.chMinus === event?.keyCode && currentIndex.current != -1) {
              let chCountSamsung =
                currentIndex.current == 0
                  ? epgChannel?.length - 1
                  : currentIndex.current - 1
              let chDownFromFirstCh = currentIndex.current == 0 ? true : false

              Array.from({ length: epgChannel?.length }).map(() => {
                if (
                  getLockedChannelsIcon(
                    epgChannel?.[chCountSamsung]?.group_id
                  ) ||
                  getIsContractChanel(epgChannel?.[chCountSamsung]?.group_id)
                ) {
                  --chCountSamsung
                  if (chCountSamsung < 1) {
                    chCountSamsung = epgChannel?.length - 1
                    setDayDirection('up_zap')
                  }
                } else {
                  return
                }
              })
              let programIndex
              if (chDownFromFirstCh) {
                progRef?.current?.scrollToItem(chCountSamsung, 'end')
                currentIndex.current = chCountSamsung
                chRef?.current?.scrollToItem(chCountSamsung, 'end')
                setDayDirection('up_zap')
              } else {
                currentIndex.current = chCountSamsung
                chRef?.current?.scrollToItem(chCountSamsung, 'center')
                progRef?.current?.scrollToItem(chCountSamsung, 'center')
              }

              epgChannel?.[chCountSamsung]?.events?.findIndex(
                (itrObj, index) => {
                  if (now > itrObj?.date_begin && now < itrObj?.date_end) {
                    document
                      .getElementById(`program-${chCountSamsung}-${index}`)
                      ?.focus()
                    programIndex = index
                  }
                }
              )
              zapHandler(
                epgChannel?.[chCountSamsung],
                epgChannel?.[chCountSamsung]?.events?.[programIndex],
                chCountSamsung
              )
            }
          }
        }, 0)
        break
      // Commenting the below code and keeping it for any near future use
      // //85 is the keycode for keyboard letter u
      // case 85:
      // setTimeout(() => {
      //   if (currentIndex.current != epgChannel?.length - 1) {
      //     let localChannelCountLG = currentIndex.current + 1
      //     Array.from({ length: epgChannel?.length }).map(() => {
      //       if (
      //         getLockedChannelsIcon(
      //           epgChannel?.[localChannelCountLG]?.group_id
      //         ) ||
      //         getIsContractChanel(epgChannel?.[localChannelCountLG]?.group_id)
      //       ) {
      //         ++localChannelCountLG
      //       } else {
      //         return
      //       }
      //     })

      //     let programIndex
      //     currentIndex.current = localChannelCountLG
      //     chRef?.current?.scrollToItem(localChannelCountLG, 'center')
      //     progRef?.current?.scrollToItem(localChannelCountLG, 'center')
      //     epgChannel?.[localChannelCountLG]?.events?.findIndex(
      //       (itrObj, index) => {
      //         if (now > itrObj?.date_begin && now < itrObj?.date_end) {
      //           document
      //             .getElementById(`program-${localChannelCountLG}-${index}`)
      //             ?.focus()
      //           programIndex = index
      //         }
      //       }
      //     )
      //     zapHandler(
      //       epgChannel?.[localChannelCountLG],
      //       epgChannel?.[localChannelCountLG]?.events?.[programIndex],
      //       localChannelCountLG
      //     )
      //   }
      // }, 0)
      // break
      //68 is the keycode for letter letter d
      // case 68:
      //   setTimeout(() => {
      //     if (currentIndex.current != 0) {
      //       let chCountLG = currentIndex.current - 1
      //       Array.from({ length: epgChannel?.length }).map(() => {
      //         if (
      //           getLockedChannelsIcon(epgChannel?.[chCountLG]?.group_id) ||
      //           getIsContractChanel(epgChannel?.[chCountLG]?.group_id)
      //         ) {
      //           --chCountLG
      //         } else {
      //           return
      //         }
      //       })
      //       let programIndex
      //       currentIndex.current = chCountLG
      //       chRef?.current?.scrollToItem(chCountLG, 'center')
      //       progRef?.current?.scrollToItem(chCountLG, 'center')
      //       epgChannel?.[chCountLG]?.events?.findIndex((itrObj, index) => {
      //         if (now > itrObj?.date_begin && now < itrObj?.date_end) {
      //           document
      //             .getElementById(`program-${chCountLG}-${index}`)
      //             ?.focus()
      //           programIndex = index
      //         }
      //       })
      //       zapHandler(
      //         epgChannel?.[chCountLG],
      //         epgChannel?.[chCountLG]?.events?.[programIndex],
      //         chCountLG
      //       )
      //     }
      //   }, 0)
      //   break
      default:
        setTimeout(() => {
          if (typeof tizen !== 'undefined') {
            tizen.tvinputdevice.registerKeyBatch([
              'ColorF3Blue',
              'ColorF1Green',
              'ChannelUp',
              'ChannelDown'
            ])
            tizen.tvinputdevice.registerKeyBatch([
              'ColorF3Blue',
              'ColorF1Green'
            ])
            const codes = {
              bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code,
              greencode: tizen.tvinputdevice.getKey('ColorF1Green').code,
              redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
            }
            handlesamsungkey(codes, keycode)
          } else {
            handleLgkey(keycode)
          }

          // Restore focus for unhandled keys to prevent focus loss
          setTimeout(() => {
            restoreFocus()
          }, 50)
        }, 0)
    }
    keyDown.current = true
    timeStoredRef.current = Date.now()
  }, [epgChannel, currentIndex, now, getLockedChannelsIcon, getIsContractChanel, zapHandler])

  //  Removing as part of SCT-1447
  // const handleNoChannelFilter = () => {
  //   setEventAlert({
  //     message: 'stv_mosaic_nochannel | stv_mosaic_description',
  //     status: 'no-channel-available'
  //   })
  // } 

  useEffect(() => {
    if (showOptionsPanel || filterScreenOpen) return

    const handleKeyDown = (event) => {
      keypresshandler(event)
    }

    document.addEventListener('keydown', handleKeyDown, { passive: false })

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [keypresshandler, showOptionsPanel, filterScreenOpen])

  // Optimized focus blur event listener with debouncing
  useEffect(() => {
    if (showOptionsPanel || filterScreenOpen) return

    let focusOutTimeout

    const handleFocusOut = (event) => {
      const target = event.target
      if (target && target.id && target.id.startsWith('program-')) {
        // Clear any existing timeout
        if (focusOutTimeout) {
          clearTimeout(focusOutTimeout)
        }

        // EPG element lost focus, restore it after a debounced delay
        focusOutTimeout = setTimeout(() => {
          const activeElement = document.activeElement
          if (!activeElement || !activeElement.id || !activeElement.id.startsWith('program-')) {
            restoreFocus()
          }
        }, 200) // Increased delay to reduce rapid firing
      }
    }

    document.addEventListener('focusout', handleFocusOut, { passive: true })

    return () => {
      document.removeEventListener('focusout', handleFocusOut)
      if (focusOutTimeout) {
        clearTimeout(focusOutTimeout)
      }
    }
  }, [showOptionsPanel, filterScreenOpen, restoreFocus])

  const handlesamsungkey = useCallback((key, keycode) => {
    const mosaicScreenOpen = document.getElementById('mosaicScreen')
    const filterScreenOpen = document.getElementById('epgFilter')

    // Handle speed keys specifically for Samsung
    const samsungSpeedKeys = [415, 416, 417, 418, 413, 412]
    if (samsungSpeedKeys.includes(keycode)) {
      setTimeout(() => restoreFocus(), 100)
      return
    }

    if (!mosaicScreenOpen || !filterScreenOpen) {
      if (key.bluecode === keycode) {
        if (
          programDetails?.channelData?.group_id &&
          channelDownId != programDetails?.channelData?.group_id
        ) {
          debouncedProgramDetailsDispatch(programDetails)
          setShowOptionsPanel(true)
        }
      }
      if (key.greencode === keycode) {
        pushNewInteractionContentEvent(
          {
            modulo_name: 'guia de programacion por favoritos',
            ...gaContentDataRef.current
          },
          INTERACTION_PLAYER,
          TV
        )
        if (!filterHeader) {
          if (getFavouriteList?.response?.groups?.length > 0) {
            let dataFiltered = []
            epgChannelBackup.map(val => {
              let singleDayResponse = val?.channelResponse?.filter(channel =>
                handleFavourite(channel.group_id)
              )
              dataFiltered.push({ channelResponse: singleDayResponse })
            })
            dispatch(
              getEpgFilterName(
                apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ??
                  'playingLive_fullEpg_title_favoritesChannels_label'
              )
            )
            debouncedEpgChannelDispatch(dataFiltered)
            dataFiltered[1].channelResponse[0] &&
              setLivePlayer(dataFiltered[1].channelResponse[0])
            setFilterHeader(true)
          } else {
            setEventAlert({
              message: `${translations?.language?.[region]?.filterSelector_alert_title_notfavorites_label} |
            ${translations?.language?.[region]?.filterSelector_alert_description_notfavorites_label}`,
              status: 'no-favourite'
            })
          }
        } else if (
          epgFilterName ==
            apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ??
          'playingLive_fullEpg_title_favoritesChannels_label'
        ) {
          CompleteGuidehandleClick()
          dispatch(
            getEpgFilterName(
              apilanguage?.playingLive_miniEpg_option_button_fullEpg?.toUpperCase() ??
                'playingLive_miniEpg_option_button_fullEpg'
            )
          )
          setFilterHeader(false)
        }
      } else if (key.redcode === keycode) {
        delete gaContentDataRef.current?.provider
        delete gaContentDataRef.current?.channel_name
        delete gaContentDataRef.current?.content_id
        delete gaContentDataRef.current?.content_name
        delete gaContentDataRef.current?.content_category
        setFilterScreenOpen(true)
      } else {
        // Handle unrecognized keys - restore focus to prevent loss
        setTimeout(() => restoreFocus(), 50)
      }
    }
  }, [
    programDetails,
    channelDownId,
    dispatch,
    filterHeader,
    getFavouriteList,
    epgChannelBackup,
    apilanguage,
    setLivePlayer,
    CompleteGuidehandleClick,
    translations,
    region,
    gaContentDataRef
  ])

  const handleLgkey = useCallback((keycode) => {
    const mosaicScreenOpen = document.getElementById('mosaicScreen')
    const filterScreenOpen = document.getElementById('epgFilter')

    // Handle speed keys specifically for LG
    const lgSpeedKeys = [19, 20, 21, 22, 174, 175, 176, 177]
    if (lgSpeedKeys.includes(keycode)) {
      setTimeout(() => restoreFocus(), 100)
      return
    }

    if (!mosaicScreenOpen || !filterScreenOpen) {
      switch (keycode) {
        case 66:
        case 406:
          if (
            programDetails?.channelData?.group_id &&
            channelDownId != programDetails?.channelData?.group_id
          ) {
            debouncedProgramDetailsDispatch(programDetails)
            setShowOptionsPanel(true)
          }
          break
          case 121 :
            if((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn')){
          if (
            programDetails?.channelData?.group_id &&
            channelDownId != programDetails?.channelData?.group_id
          ) {
            debouncedProgramDetailsDispatch(programDetails)
            setShowOptionsPanel(true)
          }}
          break
        case 404:
          case 119:
          if (!filterHeader) {
            if (getFavouriteList?.response?.groups?.length > 0) {
              let dataFiltered = []
              epgChannelBackup.map(val => {
                let singleDayResponse = val?.channelResponse?.filter(channel =>
                  handleFavourite(channel.group_id)
                )
                dataFiltered.push({ channelResponse: singleDayResponse })
              })
              dispatch(
                getEpgFilterName(
                  apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ??
                    'playingLive_fullEpg_title_favoritesChannels_label'
                )
              )
              debouncedEpgChannelDispatch(dataFiltered)
              dataFiltered[1].channelResponse[0] &&
                setLivePlayer(dataFiltered[1].channelResponse[0])
              setFilterHeader(true)
            } else {
              setEventAlert({
                message: `${translations?.language?.[region]?.filterSelector_alert_title_notfavorites_label} |
              ${translations?.language?.[region]?.filterSelector_alert_description_notfavorites_label}`,
                status: 'no-favourite'
              })
              pushNewInteractionContentEvent(
                {
                  modulo_name: 'guia de programacion por favoritos',
                  ...gaContentDataRef.current
                },
                INTERACTION_PLAYER,
                TV
              )
            }
          } else if (
            epgFilterName ==
              apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ??
            'playingLive_fullEpg_title_favoritesChannels_label'
          ) {
            CompleteGuidehandleClick()
            dispatch(
              getEpgFilterName(
                apilanguage?.playingLive_miniEpg_option_button_fullEpg?.toUpperCase() ??
                  'playingLive_miniEpg_option_button_fullEpg'
              )
            )
            setFilterHeader(false)
          }
          break


  case 118:
    if (CURRENT_PLATFORM === 'netrange' || CURRENT_PLATFORM === 'zeasn') {
      delete gaContentDataRef.current?.provider
          delete gaContentDataRef.current?.channel_name
          delete gaContentDataRef.current?.content_id
          delete gaContentDataRef.current?.content_name
          delete gaContentDataRef.current?.content_category
          setFilterScreenOpen(true)
    }
    break;
        case 403:
          delete gaContentDataRef.current?.provider
          delete gaContentDataRef.current?.channel_name
          delete gaContentDataRef.current?.content_id
          delete gaContentDataRef.current?.content_name
          delete gaContentDataRef.current?.content_category
          setFilterScreenOpen(true)
          break
        case 20:
          delete gaContentDataRef.current?.provider
          delete gaContentDataRef.current?.channel_name
          delete gaContentDataRef.current?.content_id
          delete gaContentDataRef.current?.content_name
          delete gaContentDataRef.current?.content_category
          setFilterScreenOpen(true)
          break
        case 71:
          if (!filterHeader) {
            if (getFavouriteList?.response?.groups?.length > 0) {
              let dataFiltered = []
              epgChannelBackup.map(val => {
                let singleDayResponse = val?.channelResponse?.filter(channel =>
                  handleFavourite(channel.group_id)
                )
                dataFiltered.push({ channelResponse: singleDayResponse })
              })
              dispatch(
                getEpgFilterName(
                  apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ??
                    'playingLive_fullEpg_title_favoritesChannels_label'
                )
              )
              debouncedEpgChannelDispatch(dataFiltered)
              dataFiltered[1].channelResponse[0] &&
                setLivePlayer(dataFiltered[1].channelResponse[0])
              setFilterHeader(true)
            } else {
              setEventAlert({
                message: `${translations?.language?.[region]?.filterSelector_alert_title_notfavorites_label} |
              ${translations?.language?.[region]?.filterSelector_alert_description_notfavorites_label}`,
                status: 'no-favourite'
              })
            }
          } else if (
            epgFilterName ==
              apilanguage?.playingLive_fullEpg_title_favoritesChannels_label?.toUpperCase() ??
            'playingLive_fullEpg_title_favoritesChannels_label'
          ) {
            CompleteGuidehandleClick()
            dispatch(
              getEpgFilterName(
                apilanguage?.playingLive_miniEpg_option_button_fullEpg?.toUpperCase() ??
                  'playingLive_miniEpg_option_button_fullEpg'
              )
            )
            setFilterHeader(false)
          }
          break
        default:
          // Handle unrecognized keys - restore focus to prevent loss
          setTimeout(() => restoreFocus(), 50)
          break
      }
    }
  }, [
    programDetails,
    channelDownId,
    dispatch,
    filterHeader,
    getFavouriteList,
    epgChannelBackup,
    apilanguage,
    setLivePlayer,
    CompleteGuidehandleClick,
    translations,
    region
  ])

  const hourGenerator = useCallback((index) => {
    // Start from midnight (00:00) in milliseconds
    let baseTime = 0 // Midnight in milliseconds

    // Calculate the offset in milliseconds for the given index
    let offset = index * 1800000 // 1800000 milliseconds (30 minutes)

    // Calculate the resulting time in milliseconds
    let resultTime = baseTime + offset

    // Convert the result time to hours and minutes
    let hours = Math.floor(resultTime / 3600000) % 24 // Total hours, modulo 24 to wrap around after 24 hours
    let minutes = (resultTime % 3600000) / 60000 // Remaining minutes

    // Format the result to match the desired output (hh.mmhs)
    let formattedHours = hours < 10 ? '0' + hours : hours.toString()
    let formattedMinutes = minutes === 0 ? '00' : '30'

    return `${formattedHours}.${formattedMinutes}hs.`
  }, [])

  // Memoized weekDay array to prevent recreation
  const weekDay = useMemo(() => [
    'Domingo',
    'Lunes',
    'Martes',
    'Miércoles',
    'Jueves',
    'Viernes',
    'Sábado'
  ], [])

  // Memoized timeline date to prevent recreation
  const timelineDate = useMemo(() => new Date(), [])

  const getDayName = useCallback((offset) => {
    const date = new Date(timelineDate.getTime() + offset * 24 * 60 * 60 * 1000)
    return weekDay[date.getDay()]
  }, [timelineDate, weekDay])
  const closeFilterScreen = useCallback(() => {
    setFilterScreenOpen(false)
  }, [])

  const closeMainEpgScreen = useCallback(() => {
    props.setShowLiveControls('')
  }, [props.setShowLiveControls])

  const resetFavScreen = useCallback(() => {
    setFilterHeader(false)
  }, [])

  const calculateHeight = useCallback((channelItem) => {
    if (channelItem >= 5) {
      return 560
    } else if (channelItem < 5) {
      return channelItem * 112
    }
    return 0
  }, [])

  return (
    <>
      {showOptionsPanel && (
        <ProgramDetails
          hideLanguage={setShowOptionsPanel}
          isLastUnlockedChannel={isLastUnlockedChannel}
          audioData={audioData}
          subscriptions={props?.subscriptions}
        />
      )}
      {filterScreenOpen && !showOptionsPanel ? (
        <EpgFilterScreen
          closeFilterScreen={closeFilterScreen}
          closeMainEpgScreen={closeMainEpgScreen}
          //handleNoChannelFilter={handleNoChannelFilter}   Removing as part of SCT-1447
          resetFavScreen={resetFavScreen}
          gaContentData={gaContentDataRef.current}
        />
      ) : !showOptionsPanel && !liveDetailFlag ? (
        <>
          <div className="epg-wrapper">
            <div className="description-wrapper">
              <EpgDescription
                channelErr={channelErr}
                liveTvDetail={false}
                groupData={epgChannelDesc}
                event={description}
              />
            </div>
            <div id="epg-grid-wrapper-id" className="epg-grid-wrapper">
              <div className="epg-timeline">
                <div
                  className="epg-day"
                  id={'time-line-' + day}
                  key={'time-line-' + day} // Set unique key here
                >
                  <span className="epg-day-font">
                    {day == 0
                      ? truncateText(
                          'TvEnVivo_GuiaCompleta_Horarios_Metadata_TextoDia_Ayer',
                          30
                        ) // Yesterday
                      : day == 1
                      ? truncateText(
                          'TvEnVivo_GuiaCompleta_Horarios_Metadata_TextoDia_Hoy',
                          30
                        ) // Today
                      : day == 2
                      ? truncateText(
                          'TvEnVivo_GuiaCompleta_Horarios_Metadata_TextoDia_Manana',
                          30
                        ) // Tomorrow
                      : day == 3
                      ? getDayName(2)
                      : day == 4
                      ? getDayName(3)
                      : day == 5
                      ? getDayName(4)
                      : getDayName(5)}
                  </span>
                </div>
                <div key={`marker`}>{day == 1 ? <DateMarker /> : <></>}</div>
                <div className={'epg-hours-scale'}>
                  {new Array(49).fill({})?.map((_, index) => {
                    return (
                      <>
                        <div
                          className={
                            index == 48 ? 'epg-timescale-48' : 'epg-timescale'
                          }
                          id={`time-line-${index}`}
                          key={`timescale-${index}`}
                        >
                          <span className="caliber-1">
                            <span className="hour">
                              {/* {hourGenerator(index - 1)} */}

                              {hourGenerator(index)}
                            </span>
                          </span>
                        </div>
                      </>
                    )
                  })}
                </div>
              </div>
              <div className="channel-program-list">
                <List
                  ref={chRef}
                  // height={560} for reference purpose
                  height={
                    epgChannel?.length ? calculateHeight(epgChannel?.length) : 0
                  }
                  itemSize={112}
                  width={216}
                  className="epg-channel-style"
                  itemCount={epgChannel?.length}
                  layout="vertical"
                  initialScrollOffset={112 * initialOffset}
                  overscanCount={7}
                >
                  {ChannelRow}
                </List>

                <List
                  ref={progRef}
                  height={560}
                  itemSize={112}
                  width={637 * 48}
                  className="epg-program-style"
                  itemCount={epgChannel?.length}
                  layout="vertical"
                  overscanCount={7}
                  initialScrollOffset={112 * initialOffset}
                >
                  {ProgramRow}
                </List>
              </div>
            </div>
            {eventAlert?.message && (
              <div className="event-alert-main-epg">
                <div className="main-alert-container">
                  <div className="event-tooltip">
                    <span>
                      <span className="alert-text">{eventAlert?.message}</span>
                    </span>
                  </div>
                  {eventAlert?.status == 'future-event' && (
                    <button
                      className={
                        !filterScreenOpen
                          ? 'shortcut-tooltip focusable'
                          : 'shortcut-tooltip'
                      }
                      onClick={e => {
                        if (e.key === 'Enter' || e.keyCode === 13) {
                          setShowOptionsPanel(true)
                          setEventAlert(false)
                        }
                      }}
                      id="alert-msg"
                      autoFocus={true}
                    >
                      <img
                        className="shortcut-bar-icon"
                        src={'images/Home_icons/bluedot.png'}
                      />
                      <span className="tooptip-sub-text">
                        {apilanguage?.timeshift_alert_option_button_menuOptions
                          ? apilanguage?.timeshift_alert_option_button_menuOptions
                          : 'timeshift_alert_option_button_menuOptions'}
                      </span>
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </>
      ) : (
        !showOptionsPanel && (
          <EpgDescription
            channelErr={channelErr}
            liveTvDetail={true}
            groupData={epgChannelDesc}
            event={description}
          />
        )
      )}
    </>
  )
}

// Memoize the component with custom comparison for better performance
export default React.memo(EpgGrid, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.channelErr === nextProps.channelErr &&
    prevProps.checkPastContent === nextProps.checkPastContent &&
    prevProps.subscriptions === nextProps.subscriptions &&
    prevProps.setShowLiveControls === nextProps.setShowLiveControls &&
    prevProps.handlePastContentDay === nextProps.handlePastContentDay
  )
})
