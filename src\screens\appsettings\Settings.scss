.app-css-settings {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: #121212;
	display: flex;
	justify-content: center;


	.main-container-settings {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.setting-buttons {
			box-sizing: border-box;
			height: 64px;
			width: 557px;
			border: 2px solid #ffffff;
			border-radius: 31px;
			margin-top: 16px;

			.button-content-settings {
				justify-content: center;
				display: flex;
				align-items: center;
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 35px;
			}
		}

		.setting-buttons-active {
			box-sizing: border-box;
			height: 64px;
			width: 557px;
			border: 2px solid #ffffff;
			border-radius: 31px;
			margin-top: 16px;
			background-color: #ffffff;

			.button-content-settings {
				justify-content: center;
				display: flex;
				align-items: center;
				color: #000000;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 35px;
			}
		}

		.img-button {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			color: #ffffff;
			border: 2px solid #ffffff;
			border-radius: 31px;
			width: 557px;
			height: 64px;
			margin-top: 16px;
			padding: 0 17px;
			position: relative;
			box-sizing: border-box;

			.user-image {
				height: 40px;
				width: 40px;
			}

			.img-button-content-settings {
				font-size: 30px;
				letter-spacing: 0;
				margin: 0;
				line-height: 35px;
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
			}
		}

		.setting-buttons:focus,
		.img-button:focus {
			border: 6px solid #ffffff;
		}
	}

}


//Reminder View Settings
.reminder-settings {
	position: fixed;
	margin-left: 68px;

	.reminder-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 60px;
		letter-spacing: 0;
		line-height: 71px;
	}

	.horizontal-line {
		box-sizing: border-box;
		height: 1px;
		width: 1781px;
		border: 1px solid #475865;
	}

	.reminder-sub-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 30px;
		letter-spacing: 0;
		line-height: 35px;
	}

	.reminder-events-div {
		position: relative;
		height: 590px;
		overflow-y: scroll;
		scroll-snap-type: y mandatory;

		.reminder-button {
			height: 178px;
			width: 1743px;
			margin-top: 16px;
			margin-left: 10px;

			.reminder-event-name {
				position: absolute;
				margin-top: 54px;
				margin-left: 7px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 38px;
				letter-spacing: 0;
				line-height: 44px;
			}

			.reminder-channel-name {
				margin-left: 8px;
				position: absolute;
				margin-top: 115px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				font-weight: bold;
				letter-spacing: 0;
				line-height: 35px;
			}

			.reminder-begin-time {
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 35px;
				float: right;
				margin-top: 75px;
				margin-right: 10px;
			}
		}

		.reminder-button:focus {
			box-sizing: border-box;
			height: 196px;
			width: 1764px;
			border: 7px solid #ffffff;
			scroll-snap-align: end;
			margin-left: 0px;

			.reminder-channel-image {
				//margin-left: 13px;
				margin-left: 8px;
   			    margin-top: 4px;
			}

			.reminder-begin-time {
				margin-right: 17px;
			}
		}
	}

	.more-info-shortcut {
		position: fixed;
		right: 0px;
		bottom: 60px;
		border: 2px solid #000000;
		border-radius: 15px;
		background-color: #000000;
		height: 35px;
		width: 395px;
		align-content: center;
		text-align: center;
		margin-right: 60px;
		color: #ffffff;
		font-family: Roboto;
		font-size: 25px;
		letter-spacing: 0px;
		line-height: 29px;
	}

	.reminder-not-available {
		box-sizing: border-box;
		height: 413px;
		width: 877px;
		border: 2px dashed #ffffff;
		border-radius: 15px;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		margin-top: 142px;
		justify-content: center;

		.reminder-not-available-title {
			color: #ffffff;
			font-family: Roboto;
			font-size: 44px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 30px;
			width: 766px;
		}

		.reminder-not-available-sub-title {
			height: 210px;
			width: 766px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
			margin-top: 52px;

			.reminder-clock-icon {
				vertical-align: middle;
			}
		}
	}
}

//Action Screen

.action-screen-settings {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: #121212;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;

	.delete-button-div {
		display: flex;
		margin-top: 70px;
	}

	.action-screen-buttons {
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		height: 64px;
		width: 557px;
		border: 2px solid #ffffff;
		border-radius: 31px;
		margin-top: 15px;

		.action-screen-button-contents {
			color: #ffffff;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
		}
	}

	.action-screen-buttons:focus {
		border: 5px solid #ffffff;
	}

	.action-screen-buttons-active {
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		height: 64px;
		width: 557px;
		border: 2px solid #ffffff;
		border-radius: 31px;
		margin-top: 15px;
		background-color: #ffffff;

		.action-screen-button-contents {
			color: #000000;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
		}

	}

	.delete-screen-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 48px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 52px;
	}

	.delete-screen-sub-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 50px;
		letter-spacing: 0;
		line-height: 58px;
		text-align: center;
		margin-top: 67.45px;
	}

}

//Parental Control Settings
.parental-control-settings {
	position: fixed;
	margin-left: 68px;
	margin-top: 65px;

	.parental-sub-div {
		display: flex;
		justify-content: space-between;
	}

	.parental-control-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 60px;
		letter-spacing: 0;
		line-height: 66px;
		text-transform: capitalize;
	}

	.parental-control-button {
		margin-right: 36px;
		box-sizing: border-box;
		height: 71px;
		width: 310px;
		border: 3px solid #ffffff;
		border-radius: 30px;

		.parental-control-button-contents {
			color: #ffffff;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 33px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.parental-control-button-deactivate {
		box-sizing: border-box;
		height: 71px;
		width: 310px;
		border: 3px solid #ffffff;
		border-radius: 30px;

		.parental-control-button-contents {
			color: #ffffff;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 33px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.parental-control-button:focus,
	.parental-control-button-deactivate:focus {
		box-sizing: border-box;
		height: 65px;
		width: 304px;
		border: 8px solid #ffffff;
		border-radius: 30px;
	}

	.horizontal-line {
		box-sizing: border-box;
		height: 1px;
		width: 1781px;
		border: 1px solid #475865;
		margin-top: 22px;
	}

	.parental-control-div {
		display: flex;
		justify-content: space-between;

		.parental-control-sub-div {
			display: flex;
			flex-direction: column;
			margin-top: 40px;
			margin-bottom: 36px;

			.parental-control-sub-title {
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				font-weight: bold;
				letter-spacing: 0;
				line-height: 33px;
				text-transform: uppercase;
			}

			.parental-control-description {
				margin-top: 32px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 33px;
				height: 105px;
				width: 893px;
			}

			.parental-control-description-classfication {
				margin-top: 32px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 33px;
				height: 70px;
				width: 909px;
			}
		}

		.button-div {
			display: flex;
			justify-content: space-between;
			width: 621px;
		}

		.no-pin-button-div {
			display: flex;
			justify-content: space-between;
		}

		.sub-button {
			box-sizing: border-box;
			height: 71px;
			width: 359px;
			border: 3px solid #ffffff;
			border-radius: 30px;
			margin-top: 109px;
			display: flex;
			justify-content: center;
			align-items: center;

			.sub-button-contents {
				height: 35px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 30px;
				letter-spacing: 0;
				line-height: 33px;
			}
			.dropdown-sub-button {
				.dropdown-sub-button-img {
				  height: 10px;
				  width: 21px;
				  margin-left: 15px;
				}
			}
		}

		.sub-button:focus {
			box-sizing: border-box;
			border: 8px solid #ffffff;
			border-radius: 30px;
		}

		.toggle-button {
			margin-top: 110px;
			height: 67px;
			width: 136px;
			border-radius: 34px;
			border: 1px solid #ffffff;
			position: relative;

			.toggle-button-circle {
				margin-left: 6px;
				height: 56px;
				width: 56px;
				display: flex;
				border-radius: 50%;
				background-color: #ffffff;
			}
		}

		.toggle-activated {
			margin-top: 110px;
			height: 67px;
			width: 136px;
			border-radius: 34px;
			border: 1px solid #ffffff;
			background-color: #26a4Da;
			position: relative;

			.toggle-activated-circle {
				margin-left: 6px;
				height: 56px;
				width: 56px;
				display: flex;
				float: right;
				margin-right: 6px;
				border-radius: 50%;
				background-color: #ffffff;
			}
		}

		.toggle-button:focus,
		.toggle-activated:focus {
			border: 4px solid #ffffff;
		}
	}

}


// change-security-pin

.app-css-change-pin {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: #121212;

	.change-pin-logo,
	.deactivate-pin-logo,
	.check-pin-logo {
		margin-top: 40px;
		margin-left: 88px;
	}

	.back-indicator {
		display: flex;
		height: 48px;
		width: 292px;
		border-radius: 6.6px;
		background-color: #2e303d;
		align-items: center;
		float: right;
		margin-top: 47px;
		margin-right: 64px;

		.yellow-indicator {
			height: 20px;
			width: 20px;
			margin-left: 24px;
			margin-right: 24px;
		}

		.back-image {
			height: 24px;
			width: 30px;
			margin-right: 24px;
		}

		.back-text {
			height: 30px;
			width: 146px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 29.04px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 29.04px;
			margin-top: 32px;
		}
	}

	.back-indicator-button-pin {
		display: inline-block;
		color: #ffffff;
		width: 290px;
		height: 48px;
		border-radius: 6.6px;
		font-size: 16px;
		background-color: #2e303d;
		vertical-align: middle;
		float: right;
		margin-top: 25px;
		margin-right: 40px;


		.yellow-indicator-button {
			width: 20px;
			height: 20px;
			padding: 0px 24px 0px 24px;
			vertical-align: middle;
		}

		.back-indicator-image {
			width: 35px;
			height: 28px;
			padding: 0px 24px 0px 0px;
			vertical-align: middle;
		}

		span {
			display: inline-block;
			vertical-align: middle;
			font-family: Roboto;
			font-weight: bold;
			font-size: 29px;
			color: #ffffff;
			width: 146px;
			height: 34px;
		}
	}

	.back-indicator:focus,
	.back-indicator-button-pin:focus {
		background-color: #981c15;
	}

	.left-container-div {
		display: flex;
		position: absolute;
		margin-left: 108px;
		top: 50%;
		transform: translateY(-43%);
	}

	.right-container-div {
		display: grid;
		position: absolute;
		top: 50%;
		transform: translateY(-43%);
		margin-left: 964px;

		.pin-screen-title {
			height: 72px;
			width: 766px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 60px;
			letter-spacing: 0;
			line-height: 71px;
			margin-left: 50px;
		}

		.pin-screen-sub-title {
			height: 94px;
			width: 720px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 47px;
			margin-top: 40px;
			margin-left: 50px;
		}

		.pin-screen-sub-title-width {
			width: 760px;
		}

		.right-container-extra-div {
			margin-top: 30px;
		}

		.deactivate-pin-div {
			margin-top: 168px;
		}

		.deactivate-pin-subtitle {
			width: 760px;
			height: 233px;
		}

		.pin-box-div {
			display: flex;
			margin-top: 45px;
			margin-left: 50px;
			height: 83px;
			align-items: center;

			.pin-focused {
				height: 72px;
				width: 64px;
				border-radius: 6px;
				background-color: #212224;
				margin-right: 16px;
				font-size: 55px !important;
				text-align: center;
				color: #eeeeee;
				pointer-events: none;
				border: 4px solid #4c6f94;
				box-sizing: border-box;

			}
			.pin-wrapper {
				position: relative;
				display: inline-block;
			  }
			  
			  .pin-cursor {
				position: absolute;
				top: 50%;
				left: 40%;
				transform: translate(-50%, -50%);
				font-size: 24px; /* Small pipe */
				color: #eeeeee;
				animation: blink 1s step-start infinite;
				pointer-events: none;
			  }
			  @keyframes blink {
				50% { opacity: 0; }
			  }

			.pin-field-invalid {
				height: 72px;
				width: 64px;
				border-radius: 6px;
				background-color: #212224;
				margin-right: 16px;
				font-size: 55px !important;
				text-align: center;
				color: #eeeeee;
				pointer-events: none;
				border: 4px solid #981c15;
				box-sizing: border-box;
			}

			.pin-field {
				height: 72px;
				width: 64px;
				border-radius: 6px;
				background-color: #212224;
				margin-right: 16px;
				font-size: 55px !important;
				text-align: center;
				color: #eeeeee;
				border: none;
				pointer-events: none;
			}

			.see-pin-button {
				height: 72px;
				width: 225px;
				border-radius: 6px;
				background-color: #2e303d;
				margin-left: 15px;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.see-pin-button:focus {
				width: 259px;
				height: 83px;
				margin-left: 0px;
			}
		}

		.pin-screen-button {
			height: 72px;
			width: 722px;
			border-radius: 6px;
			background-color: #4b1512;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			font-weight: bold;
			letter-spacing: -0.51px;
			line-height: 38px;
			text-align: center;
			margin-top: 26px;
			margin-left: 50px;
		}

		.pin-screen-button-top {
			margin-top: 84px;
		}

		.deactivate-pin-screen-button {
			height: 72px;
			width: 722px;
			border-radius: 6px;
			background-color: #4b1512;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			font-weight: bold;
			letter-spacing: -0.51px;
			line-height: 38px;
			text-align: center;
			margin-top: 84px;
			margin-left: 50px;
		}

		.pin-screen-button:focus,
		.deactivate-pin-screen-button:focus {
			height: 83px;
			width: 828px;
			border-radius: 6.9px;
			background-color: #981c15;
			margin-left: 0px;
			margin-top: 73px;
		}

		.pin-screen-button:disabled,
		.deactivate-pin-screen-button:disabled {
			opacity: 0.5;
		}

		.pin-screen-button-cancel {
			height: 72px;
			width: 722px;
			border-radius: 6px;
			background-color: #2e303d;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			font-weight: bold;
			letter-spacing: -0.51px;
			line-height: 38px;
			text-align: center;
			margin-top: 26px;
			margin-left: 50px;
		}

		.pin-screen-button-cancel:focus {
			height: 83px;
			width: 828px;
			border-radius: 6.9px;
			margin-left: 0px;
			margin-top: 17px;
		}

		.forgot-pin-button {
			height: 72px;
			width: 342px;
			border-radius: 6px;
			background-color: #212224;
			margin-top: 124px;
			margin-left: 430px;
			display: flex;
			justify-content: center;

			.forgot-pin-content {
				height: 56px;
				width: 319px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 24px;
				letter-spacing: 0;
				line-height: 28px;
				text-align: center;
				position: relative;
				bottom: 15px;
			}
		}

		.deactivate-forgot-pin-button {
			height: 72px;
			width: 342px;
			border-radius: 6px;
			background-color: #212224;
			margin-top: 67px;
			margin-left: 430px;
			display: flex;
			justify-content: center;

			.deactivate-forgot-pin-content {
				height: 56px;
				width: 319px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 24px;
				letter-spacing: 0;
				line-height: 28px;
				text-align: center;
				position: relative;
				bottom: 15px;
			}
		}

		.forgot-pin-button:focus,
		.deactivate-forgot-pin-button:focus {
			background-color: #981c15;
		}

		.pin-error {
			border-radius: 6px;
			background-color: #ffffff;
			height: 93px;
			width: 466px;
			display: flex;
			justify-content: center;
			text-align: center;
			align-items: center;
			position: absolute;
			z-index: 1;
			left: 50px;
			bottom: 322px;

			.pin-error-contents {
				color: #981c15;
				font-family: 'Roboto';
				font-size: 31px;
				width: 415px;
			}

		}

		.pin-error:after,
		.pin-error:before {
			left: 214px;
			top: 4%;
			border: solid transparent;
			content: " ";
			width: 0;
			position: absolute;
			pointer-events: none;
		}

		.pin-error:after {
			border-width: 10px 10px 10px 20px;
			border-left: 27px solid #ffffff;
		}


		.pin-error::before {
			content: "";
			position: absolute;
			width: 60px;
			height: 25px;
			z-index: -1;
			right: 528px;
			transform: rotate(43deg);
			background-color: #ffffff;
			bottom: 185px;
		}


	}
}


// Content Classification
.content-classification {
	width: 1920px;
	height: 1080px;
	position: fixed;
	color: #121212;
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-top: 187px;

	.content-classification-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 48px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 52px;
	}

	.content-classification-sub-title {
		color: #ffffff;
		font-family: Roboto;
		font-size: 50px;
		letter-spacing: 0;
		line-height: 58px;
		text-align: center;
		margin-top: 40px;
		margin-bottom: 32px;
	}

	.content-classification-buttons {
		margin-top: 16px;
		box-sizing: border-box;
		height: 62px;
		width: 557px;
		border: 2px solid #ffffff;
		border-radius: 49px;

		.content-classification-button-contents {
			color: #ffffff;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
			text-align: center;
			display: block;
		}
	}

	.content-classification-button-height {
		height: 100px;
	}

	.content-classification-buttons-active {
		margin-top: 16px;
		box-sizing: border-box;
		width: 557px;
		border-radius: 49px;
		background-color: #ffffff;

		.content-classification-button-contents {
			color: #000000;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
			text-align: center;
			display: block;
		}

		.check-icon {
			height: 30px;
			position: relative;
			float: right;
			right: 35px;
		}
	}

	.content-classification-buttons:focus {
		border: 5px solid #ffffff;
	}
}

//Pin creation success

.pin-creation-success {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background-color: #121212;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;

	.green-tick {
		margin-bottom: 58px;
	}

	.pin-creation-success-text1 {
		color: #ffffff;
		font-family: Roboto;
		font-size: 48px;
		letter-spacing: 0;
		line-height: 48px;
		text-align: center;
		display: block;
		margin-bottom: 49px;
	}

	.pin-creation-success-text2 {
		display: block;
		margin-bottom: 64px;
		height: 114px;
		width: 1766px;
		color: #ffffff;
		font-family: Roboto;
		font-size: 48px;
		letter-spacing: 0;
		line-height: 57px;
		text-align: center;
	}

	.pin-creation-success-button {
		box-sizing: border-box;
		height: 64px;
		width: 557px;
		border: 2px solid #ffffff;
		border-radius: 31px;
		margin-top: 16px;
		background-color: #ffffff;

		.pin-creation-success-button-content {
			justify-content: center;
			display: flex;
			align-items: center;
			color: #000000;
			font-family: Roboto;
			font-size: 30px;
			letter-spacing: 0;
			line-height: 35px;
		}
	}
}