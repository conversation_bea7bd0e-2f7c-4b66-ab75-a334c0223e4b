import React, { useState, useEffect, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { TRANSLATIONS_EN } from '../../appsettings/translations'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './Railcard.scss'
import { COMMON_URL } from '../../../utils/environment'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import { getRegisterPopup } from '../../../store/slices/login'
import { getContractBackHandle } from '../../../store/slices/settingsSlice'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { contentSelectionType } from '../../../GoogleAnalyticsConstants'
import { getPremiumNodeValue } from '../../../store/slices/HomeSlice'
import { getTabvalue } from '../../../store/slices/subMenuDataSlice'
import { CURRENT_PLATFORM } from '../../../utils/devicePlatform'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const RailCards = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const railRef = useRef([])
  const [data, setData] = useState([])
  const [content, setContent] = useState([])
  const [title, setTitle] = useState('')
  const [duration, setDuration] = useState('')
  const [focusedId, setFocusedId] = useState(null)
  const [focusContent, setFocusContent] = useState(false)

  const payWayToken = useSelector(
    state => state?.epg?.paywayToken?.response?.paqs?.paq
  )
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const epgChannel = epgSevenDaysData && epgSevenDaysData[1]?.channelResponse

  const railImage = props.byUser ? data?.response?.groups || data?.data?.groups : props.dataObject?.groups
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const posterTitle =
    apaMetaData?.poster_title_configuration &&
    JSON?.parse(apaMetaData?.poster_title_configuration)
  const region = localStorage.getItem('region')
  const mycontentdata = localStorage.getItem('miscontenidos')
  const mycontentplaceholder = mycontentdata
    ? 'images/mycontent_placeholder.png'
    : 'images/landscape_card.png'

  const backelement = document.getElementById(props?.backfocusid)
  const filterlistConfiguration =
    apaMetaData?.byr_filterlist_configuration &&
    JSON?.parse(apaMetaData?.byr_filterlist_configuration)
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
  const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
  const filterlistParam = filterlistConfiguration?.[region]?.filterlist
  let indexValue = railImage?.[0] && props?.index ? props?.index : ''
  const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const nav = useSelector(state => state?.homeReducer?.navbarData)
  const navData = userDetails?.is_kids === 'true' ? navKids : nav
  const seriesIndex = navData?.findIndex(item => item?.code === 'seriesnv' && item?.page === 'Series' );
  const moviesIndex = navData?.findIndex(item => item?.code === 'peliculas' && item?.page === 'Películas' );


  useEffect(() => {
    if(backelement){
    backelement?.focus()
    }else if(props?.deleteHomeMycontent && props?.byUser &&
      !props.dataObject?.includes('purchasedactive') && railImage?.length > 0){
      document.getElementById(`railMoviesrailcard${props?.railIndex}${railImage?.length-1}`)?.focus()
    }
  }, [backelement,railImage])

  useEffect(() => {
    const element = document.getElementById(props?.id)
    element?.scrollIntoView({
      block: 'center',
      inline: 'center',
      behavior: 'smooth'
    })
    const rows = document.querySelectorAll('.my-list-container')
    rows?.length > 0 &&
      rows.forEach(row => {
        row.addEventListener('keydown', handleKeyDown)
      })
    return () => {
      rows?.length > 0 &&
        rows.forEach(row => {
          row.removeEventListener('keydown', handleKeyDown)
        })
    }
  }, [])

  const focusedEleCheck =
    document?.getElementById('index00') ??
    document?.getElementById('index10')
  useEffect(() => {
    focusedEleCheck?.focus()
  }, [focusedEleCheck])

  const getIsContractChanel = item => {
    const foundContract = payWayToken?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return true
    }
    return false
  }

  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
     let userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbarTab,
        content_list : props?.title,
        modulo_name : 'carrusel'
      }
    dispatch(getRegisterPopup(false))
    if (item?.live_enabled === '1' || item?.contentType === 'channel') {
      if (!getIsContractChanel(item?.id)) {
        dispatch(
          getContractBackHandle({ backPage: '/home', focusedId: focusedId })
        )
      }
      dispatch(
        getChannelData({
          group_id: item?.id,
          timeshift: item?.timeshift,
          switchChannel: 'yes',
          epgIndex: epgChannel?.findIndex(itrObj => itrObj.group_id == item?.id)
        })
      ),
        navigate('/livePlayer', {
          state: {
            showControls: 'live',
            data: item,
            backfocusid: `index${indexValue}${index}`
          }, //Added showControls flag which will enable live player after navigation
          replace: true
        })
    } else if (item?.is_series || item?.contentType === 'chapter') {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', seriesIndex)
      pushContentSelectionEvent(userData,item,index,contentSelectionType.HOME_SERIES)
      navigate('/series', {
        state: {
          data: item,
          card: 'railSeries',
          backfocusid: `index${indexValue}${index}`
        }
      })
    } else if (item.type === 'node') {
      dispatch(getPremiumNodeValue(item?.section))
      dispatch(getTabvalue(item?.section))
    }
    else {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', moviesIndex)
      pushContentSelectionEvent(userData,item,index,contentSelectionType.HOME_MOVIES)
      navigate('/movies', {
        state: {
          vodData: item,
          card: 'railMovies',
          backfocusid: `index${indexValue}${index}`
        }
      })
    }
  }
  const element = document.getElementById('myListRail')
  useEffect(() => {
    if (focusContent) {
      element?.focus()
      element?.scrollIntoView({
        block: 'center',
        inline: 'center',
        behavior: 'smooth'
      })
    }
  }, [focusContent, element])

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const handleFocus = (data,index) => {
    railRef?.current?.[index]?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    });
    setContent(data)
    props.byUser && setFocusContent(true)
    {
      data.is_series === true
        ? (setTitle(data.title), setDuration(data.duration))
        : null
    }
  }

  const handleBlur = data => {
    setFocusContent(false)
  }

  function removeLastTouchParam(url) {
    const urlParts = url?.split('?')
    if (urlParts.length > 1) {
      const params = urlParts[1]?.split('&')
      const updatedParams = params.filter(
        param => !param.startsWith('lasttouch=')
      )
      return `${urlParts[0]}?${updatedParams.join('&')}`
    }
    return url
  }

  const fetchApi = async params => {
    const newParam = params && removeLastTouchParam(params)
    const urlParts = newParam?.split('?')
    const splitParams = urlParts && urlParts[1]?.split('&')
    const favoriteParam = splitParams?.filter(param =>
      param.startsWith('filterlist=')
    )
    if (newParam !== undefined) {
      return await fetch(
        props?.byUser === true &&
          localStorage.getItem('lasttouch') &&
          favoriteParam?.length > 0
          ? `${COMMON_URL.BASE_URL}/${newParam}&authpn=${
              COMMON_URL.authpn
            }&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${
              COMMON_URL.device_id
            }&device_manufacturer=${
              COMMON_URL.device_manufacturer
            }&device_model=${COMMON_URL.device_model}&device_name=${
              COMMON_URL.device_name
            }&device_type=${
              COMMON_URL.device_type
            }&lasttouch=${localStorage.getItem('lasttouch')}`
          : props?.byUser === true &&
            localStorage.getItem('lasttouch') &&
            (favoriteParam?.length == 0 || !favoriteParam)
          ? `${COMMON_URL.BASE_URL}/${newParam}&authpn=${
              COMMON_URL.authpn
            }&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${
              COMMON_URL.device_id
            }&device_manufacturer=${
              COMMON_URL.device_manufacturer
            }&device_model=${COMMON_URL.device_model}&device_name=${
              COMMON_URL.device_name
            }&device_type=${
              COMMON_URL.device_type
            }&lasttouch=${localStorage.getItem(
              'lasttouch'
            )}&filterlist=${filterlistParam}`
          : `${COMMON_URL.BASE_URL}/${params}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}`
      ).then(async data => {
        const value = await data.json()
        setData(value)
      })
    }
  }

  useEffect(() => {
    if (props.byUser) {
      fetchApi(props?.dataObject)
    }
  }, [props?.dataObject, props?.byUser])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (props?.title !== 'Mis alquileres') {
        if (key.redcode == keycode) {
          navigate('/deletecard', {
            state: {
              deteleData: content,
              page: 'mylist',
              backfocusid: focusedId,
              railIndex: props?.index,
              deleteHomeMycontent: true
            }
          })
        }
      }
    }
  }

  const handleLgkey = keycode => {
    if (focusContent) {
      if (props?.title !== 'Mis alquileres') {
        if (keycode == 403 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 118)
) {
          navigate('/deletecard', {
            state: {
              deteleData: content,
              page: 'mylist',
              backfocusid: focusedId,
              railIndex: props?.index,
              deleteHomeMycontent: true
            }
          })
        }
      }
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  const handleKeyDown = event => {
    const rows = document.querySelectorAll('.rail-wrapper')
    if (rows?.length > 0) {
      rows.forEach(row => {
        const cards = row.querySelectorAll('.rail_block')
        const focusedElement = document.activeElement
        const focusedIndex = Array.from(cards).indexOf(focusedElement)

        if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
          const nextCard = cards[focusedIndex + 1]
          const containerRect = row.getBoundingClientRect()
          const nextCardRect = nextCard.getBoundingClientRect()

          if (nextCardRect.right > containerRect.right) {
            row.scrollLeft += nextCardRect.right - containerRect.right
          }
        } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
          const prevCard = cards[focusedIndex - 1]
          const containerRect = row.getBoundingClientRect()
          const prevCardRect = prevCard.getBoundingClientRect()

          if (prevCardRect.left < containerRect.left) {
            row.scrollLeft -= containerRect.left - prevCardRect.left
          }
        }
      })
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div
      className={`${(railImage?.[0] || mycontentdata) && 'my-list-container'}`}
      style={{height: railImage?.[0] && props?.title === 'Mi lista' || railImage?.[0] && props?.title === 'myListRail' ? 370 : railImage?.[0] && mycontentdata ? 300 : !railImage?.[0] && mycontentdata && 316}}
      id={props.title === 'Mi lista' && 'myListRail'}>
      {railImage?.[0] ? (
        <div className="railTitle">
          <SafeHTML html={props?.title || ''} />
        </div>
      ) : (
        ''
      )}

      <div className="railContainer">
        {railImage?.[0] ? (
          <div className="rail-wrapper" id="rail-carousel">
            {railImage?.map((each, index, array) => (
              <>
                <button
                 ref={el => (railRef.current[index] = el)}
                  className="rail_block focusable"
                  key={index}
                  id={`index${indexValue}${index}`}
                  data-testid={`rail_card_click${index}`}
                  onClick={() => goToMoviesSeries(each, index)}
                  onFocus={() => {
                    handleFocus(each,index)
                    setFocusedId(`index${indexValue}${index}`)
                  }}
                  onBlur={() => handleBlur(each)}
                  data-sn-up={document?.getElementById(`index${indexValue-1}0`) ? `#index${props?.index-1}0` : undefined}
                  data-sn-down={document.getElementById(`index${indexValue+1}0`) ? `#index${indexValue+1}0` : undefined}
                  data-sn-right={index != array.length - 1 && undefined}
                  data-sn-left={index == 0 ? '' : undefined}
                  style={{
                    marginBottom:
                      props?.title === 'Mi lista' ||
                      props?.title === 'myListRail'
                        ? 47
                        : 0
                  }}
                >
                  {each.image_small && each.image_small !== '' || each?.images?.small && each?.images?.small !== '' ? (
                    <>
                      <LazyLoadImage
                        src={each.image_small ?? each?.images?.small}
                        placeholderSrc="images/landscape_card.png"
                        key={index}
                        className="rail-image"
                        id={`railfocus${index}`}
                      />
                      {props?.byUser == true &&
                      !props.dataObject?.includes('purchasedactive') ? (
                        <div className="rail-title-icon-block">
                          <div className="mycontent-rail">
                            <div className="mycontent-rail-title">
                              {each?.title?.length >= 20
                                ? `${each?.title?.slice(0, 20)}...`
                                : each?.title}
                            </div>
                          </div>
                          <div className="deleteIcons-mylist">
                            <img
                              src={'images/Home_icons/red.png'}
                              className="redDot"
                            />
                            <img
                              src={'images/Home_icons/delete.png'}
                              className="delete"
                            />
                          </div>
                        </div>
                      ) : null}
                    </>
                  ) : (
                    <LazyLoadImage
                      src="images/landscape_card.png"
                      loading="lazy"
                      alt="PlaceHolder"
                      className="rail-image"
                    />
                  )}
                  {each?.contentType === 'chapter' && !props?.byUser ? (
                    <div className="seriesMoviesTitlerail">
                      <div
                        className={`defaultTitle ${
                          props.title === 'Contin&uacute;a viendo'
                            ? 'showTitle'
                            : ''
                        }`}
                      >
                        {posterTitle?.default[each?.proveedor_code??each?.provider?.code]?.landscape
                          ? each?.title
                          : ''}
                      </div>
                    </div>
                  ) : (
                    (!props?.byUser || props?.byUser == 'premium') && (
                      <div className="seriesMoviesTitlerail">
                        <div className="defaultTitlerail">
                          {posterTitle?.default[each?.proveedor_code??each?.provider?.code]?.landscape
                            ? each?.title
                            : ''}
                        </div>
                      </div>
                    )
                  )}
                  {/* tags */}
                  {each.image_small && each?.proveedor_code == 'amco' || each?.images?.small && each?.provider?.code == 'amco' ? (
                    each?.format_types === 'ppe,download' ||
                    each?.format_types === 'ppe'||
                    each?.contentAttributes?.marketingType === 'ppe,download' ||
                    each?.contentAttributes?.marketingType === 'ppe' ? (
                      <div className="proveedorBlockRailAlq">
                        <img src={'images/Alquilar.svg'} className="tagAlq" />
                      </div>
                    ) : each?.format_types === 'ppe,est' || each?.contentAttributes?.marketingType === 'ppe,est' ? (
                      <>
                        <div
                          className="comprar-tag-movies"
                          style={
                            each?.is_series || each?.contentType === 'chapter'
                              ? { backgroundColor: '#40336f' }
                              : null
                          }
                        >
                          COMPRAR
                        </div>
                        <div className="proveedor-block-rail-alq-comp">
                          <img src={'images/Alquilar.svg'} className="tagAlq" />
                        </div>
                      </>
                    ) : (each?.format_types === 'free' ||
                      each?.format_types === 'free,download'||
                      each?.contentAttributes?.marketingType === 'free' ||
                      each?.contentAttributes?.marketingType === 'free,download') &&
                      userDetails?.subscriptions?.length == 0 ? (
                      <div className="verahora-tag">VER AHORA</div>
                    ) : (
                      each?.format_types === 'est' || each?.contentAttributes?.marketingType === 'est' && (
                        <div
                          className="comprar-tag-movies"
                          style={
                            each?.is_series || each?.contentType === 'chapter'
                              ? { backgroundColor: '#40336f' }
                              : null
                          }
                        >
                          COMPRAR
                        </div>
                      )
                    )
                  ) : each.image_small &&
                  each?.proveedor_code &&
                  each?.image_medium && each?.live_enabled == 0 || each?.images?.small &&
                    each?.provider?.code &&
                    each?.images?.medium && each?.contentType != 'channel' ? (
                    <div className="proveedorBlockRail_vero_hara">
                      {Addproveedor(providerLabel?.[each?.proveedor_code ?? each?.provider?.code]?.susc) && (
                        <img
                          id="#icon1"
                          className={`${
                            each?.provider?.code === 'picardia2' || each?.proveedor_code === 'picardia2'
                              ? 'picardia-image'
                              : 'premium-icon'
                          }`}
                          src={Addproveedor(providerLabel?.[each?.proveedor_code ?? each?.provider?.code]?.susc)}
                        />
                      )}
                      {each.image_small &&
                        each?.proveedor_code === 'picardia2' &&
                        each?.image_medium || each?.images?.small &&
                         each?.provider?.code === 'picardia2' &&
                        each?.images?.medium ? (
                          <div className="picardia-proveedorBlockRail">
                            <img
                              src={'images/Adultus.svg'}
                              className="picardia-tag"
                            />
                          </div>
                        ):null}
                      {(each?.format_types === 'free' ||
                        each?.format_types === 'free,download' ||
                        each?.contentAttributes?.marketingType === 'free' ||
                        each?.contentAttributes?.marketingType === 'free,download') &&
                      userDetails?.subscriptions?.length == 0 ? (
                        <div className="verahora-tag">VER AHORA</div>
                      ) : null}
                    </div>
                  ) : null}
                </button>
              </>
            ))}
          </div>
        ) : mycontentdata ? (
          <div>
            <div className="mycontentlist-railtitle">
              <SafeHTML html={props?.title || ''} />
            </div>
            <div className="nocontent-card-main">
              <button
                className="nocontent-card focusable"
                id={`index${props?.index}0`}
                onFocus={handleFocus}
                onBlur={handleBlur}
                data-sn-right
                data-sn-left
                data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
              >
                <p className="mycontent-rail-text">
                  {' '}
                  {
                    TRANSLATIONS_EN?.language?.peru
                      ?.ribbons_placeholder_Rentas_label
                  }
                </p>
                <div className="nocontent-sub-card">
                  <img
                    className="cardimage1 focusable"
                    src={mycontentplaceholder}
                  />
                  <img className="cardimage2" src={mycontentplaceholder} />
                  <img className="cardimage3" src={mycontentplaceholder} />
                </div>
              </button>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default RailCards
