$position-static: static;
$position-relative: relative;
$position-sticky: sticky;
$position-absolute: absolute;
$display-inline-type: inline-flex;

.mosaic-main-page {
  margin: 0;
  padding: 0;
  background: #121212;
  width: 1920px;
  height: 1080px;
  overflow: hidden;

  .mosaictopgrid-container {
    width: 1919px;
    height: 99px;
    overflow: scroll hidden;
    position: $position-sticky;
    top: 0;
    background: rgba(216, 216, 216, 0);
  }

  .mosaictopgrid-content {
    display: $display-inline-type;
    align-items: center;
  }

  .mosaic-top-grid-item {
    $item-width: 382px;
    $item-height: 90px;
    background: #4b1512;
    flex-direction: column;
    display: flex;
    align-items: center;
    width: $item-width;
    height: $item-height;

    margin-right: 18px;
  }

  .mosaic-title {
    $font-size: 28px;
    $line-height: 32px;
    $font-family: 'Roboto', sans-serif;
    font: #{$font-size}/#{$line-height} $font-family; // Interpolation for font shorthand
    color: #ffffff;
    text-transform: uppercase;
  }

  .mosaic-top-grid-item-select {
    $item-width: 382px;
    $item-height: 99px;
    background: #981c15 !important;
    flex-direction: column;
    display: flex;
    align-items: center;
    width: $item-width;
    height: $item-height;

    margin-right: 18px;
    font-weight: bold;

    &:focus {
      background: #4b1512;
      border: 2px solid transparent !important;
    }
  }

  .mosaic-top-grid-item {
    $item-width: 382px;
    $item-height: 90px;
    width: $item-width;
    height: $item-height;

    &:focus,
    &:active {
      background: #4b1512;
      border: 2px solid #fffff0 !important;
    }
  }
}