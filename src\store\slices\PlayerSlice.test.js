import initialSlice, {
  getDrmLicense,
  getMediaAPI,
  getMediaAPISuccess,
  getMediaAPIError,
  clearGetMediaRes,
  clearGetMediaErr,
  getLinealPayway,
  getLinealPaywayApiSuccess,
  getLinealPaywayApiError,
  getLivePlayer,
  getLivePlayerSuccess,
  getLivePlayerError,
  resetLivePlayer,
  getChannelData,
  getTrailerPlayer,
  getTrailerPlayerSuccess,
  getTrailerPlayerError,
  clearTrailerPlayerError,
  removeTrailerPlayerData,
  EnableplayerEpisodeScreen,
  getAvailableAudio,
  getDefaultAudio,
  getAvailableSubtitles,
  setPlayerInstance,
  removePlayerInstance,
  getRecordPlayer,
  getRecordPlayerSuccess,
  getRecordPlayerError
  } from './PlayerSlice';

  describe('initialSlice reducer', () => {
    const initialState = {
        playerinfo: {},
        contToken: '',
        paywaytoken: {},
        getMediaRes: {},
        getMediaError: {},
        trailerplayerinfo: {},
        trailerplayerResponseError: {},
        license: '',
        channelData: {},
        audioOptions: [],
        defaultAudio: '',
        playerInstance: null,
        EnablePlayerEpisode: false,
        recordplayerinfo: {}
    };

    
    it('should handle getLivePlayer', () => {
        const payload = { live: 'player' };
        const action = { type: getLivePlayer.type, payload };
        const expectedState = { ...initialState, playerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      
      it('should handle getLivePlayerSuccess', () => {
        const payload = { live: 'player' };
        const action = { type: getLivePlayerSuccess.type, payload };
        const expectedState = { ...initialState, playerinfo: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle getLivePlayerError', () => {
        const payload = { live: 'player' };
        const action = { type: getLivePlayerError.type, payload };
        const expectedState = { ...initialState, playerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle resetLivePlayer', () => {
        const payload = { live: 'player' };
        const action = { type: resetLivePlayer.type, payload };
        const expectedState = { ...initialState, playerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getMediaAPI', () => {
        const payload = { meadia: 'API' };
        const action = { type: getMediaAPI.type, payload };
        const expectedState = { ...initialState, getMediaRes: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle getMediaAPISuccess', () => {
        const payload = { meadia: 'API' };
        const action = { type: getMediaAPISuccess.type, payload };
        const expectedState = { ...initialState, getMediaRes: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      
      it('should handle getMediaAPIError', () => {
        const payload = { meadia: 'API' };
        const action = { type: getMediaAPIError.type, payload };
        const expectedState = { ...initialState, getMediaError: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle clearGetMediaRes', () => {
        const payload = { clearget: 'meadia' };
        const action = { type: clearGetMediaRes.type, payload };
        const expectedState = { ...initialState, getMediaRes: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle clearGetMediaErr', () => {
        const payload = { clearget: 'meadia' };
        const action = { type: clearGetMediaErr.type, payload };
        const expectedState = { ...initialState, getMediaError: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      
      it('should handle getRecordPlayer', () => {
        const payload = { record: 'player' };
        const action = { type: getRecordPlayer.type, payload };
        const expectedState = { ...initialState, recordplayerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getRecordPlayerSuccess', () => {
        const payload = { record: 'player' };
        const action = { type: getRecordPlayerSuccess.type, payload };
        const expectedState = { ...initialState, recordplayerinfo: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

    it('should handle getRecordPlayerError', () => {
        const payload = { record: 'player' };
        const action = { type: getRecordPlayerError.type, payload };
        const expectedState = { ...initialState, trailerplayerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getTrailerPlayer', () => {
        const payload = { trailer: 'player' };
        const action = { type: getTrailerPlayer.type, payload };
        const expectedState = { ...initialState, trailerplayerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getTrailerPlayerSuccess', () => {
        const payload = { trailer: 'player' };
        const action = { type: getTrailerPlayerSuccess.type, payload };
        const expectedState = { ...initialState, trailerplayerinfo: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      
      it('should handle getTrailerPlayerError', () => {
        const payload = { trailer: 'player' };
        const action = { type: getTrailerPlayerError.type, payload };
        const expectedState = { ...initialState, trailerplayerResponseError: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle clearTrailerPlayerError', () => {
        const payload = { trailer: 'player' };
        const action = { type: clearTrailerPlayerError.type, payload };
        const expectedState = { ...initialState, trailerplayerResponseError: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

    it('should handle getLinealPayway', () => {
        const payload = { trailer: 'player' };
        const action = { type: getLinealPayway.type, payload };
        const expectedState = { ...initialState, paywaytoken: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getLinealPaywayApiSuccess', () => {
        const payload = { trailer: 'player' };
        const action = { type: getLinealPaywayApiSuccess.type, payload };
        const expectedState = { ...initialState, paywaytoken: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
    it('should handle getLinealPaywayApiError', () => {
        const payload = { trailer: 'player' };
        const action = { type: getLinealPaywayApiError.type };
        const expectedState = { ...initialState};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle getDrmLicense', () => {
        const payload = { trailer: 'player' };
        const action = { type: getDrmLicense.type, payload };
        const expectedState = { ...initialState, license: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getChannelData', () => {
        const payload = { trailer: 'player' };
        const action = { type: getChannelData.type, payload };
        const expectedState = { ...initialState, channelData: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle removeTrailerPlayerData', () => {
        const payload = { trailer: 'player' };
        const action = { type: removeTrailerPlayerData.type, payload };
        const expectedState = { ...initialState, trailerplayerinfo: {}};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle EnableplayerEpisodeScreen', () => {
        const payload = { trailer: 'player' };
        const action = { type: EnableplayerEpisodeScreen.type, payload };
        const expectedState = { ...initialState, EnablePlayerEpisode: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      it('should handle getAvailableAudio', () => {
        const payload = { trailer: 'player' };
        const action = { type: getAvailableAudio.type, payload };
        const expectedState = { ...initialState, audioOptions: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });

      it('should handle getDefaultAudio', () => {
        const payload = { trailer: 'player' };
        const action = { type: getDefaultAudio.type, payload };
        const expectedState = { ...initialState, defaultAudio: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });


      it('should handle getAvailableSubtitles', () => {
        const payload = { trailer: 'player' };
        const action = { type: getAvailableSubtitles.type, payload };
        const expectedState = { ...initialState, subtitleOptions: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle setPlayerInstance', () => {
        const payload = { trailer: 'player' };
        const action = { type: setPlayerInstance.type, payload };
        const expectedState = { ...initialState, playerInstance: payload};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
      
      it('should handle removePlayerInstance', () => {
        const payload = { trailer: 'player' };
        const action = { type: removePlayerInstance.type };
        const expectedState = { ...initialState};
        expect(initialSlice(initialState, action)).toEqual(expectedState);
      });
});
