import React, { useCallback, useEffect, useRef, useState } from 'react'
import './vodseries.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import moment from 'moment/moment'
import ProgressBar from '../Progressbar/Progressbar'
import { useSelector } from 'react-redux'
import { useDispatch } from 'react-redux'
import animationData from '../../../src/json/animationData.json'
import {
  getWatchList,
  addWatchList,
  delWatchList,
  continueWatchlist,
  lastSeenWatch
} from '../../store/slices/getWatchListSlice'
import {
  getTalentSearchData,
  setShowApiFailureModal,
  getTalentSearch,
  setShowTalentModule,
  setVodReturnFocusById
} from '../../store/slices/SearchSlice'
import {
  clearVodSeries,
  clearVodSeriesCast,
  clearVodSeriesMlt,
  getEpisodeMoreInfo,
  getEpisodeVariable,
  vodSeries,
  vodSeriesCast,
  vodSeriesMLT
} from '../../store/slices/vodSeriesSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import {
  clearGetMediaRes,
  clearTrailerPlayerError,
  getMediaAPI,
  getTrailerPlayer,
  removeTrailerPlayerData,
  setGenreDetails
} from '../../store/slices/PlayerSlice'
import { CastSkeletonLoading } from '../SkeletonScreenLoading/SkeletonScreenloading'
import {
  getControlPin,
  getViewSubscribeData,
  getVodSubscriptionInfo,
  getStatusControlPin,
  clrSubscriptionInfo,
  getFinishSubscriptionPlayerData,
  getVodID
} from '../../store/slices/settingsSlice'
import Navbar from '../home/<USER>'
import { getProgressbarBookmark } from '../../store/slices/SearchSlice'
import Lottie from 'react-lottie-player'
import PlayerModelSelection from '../VodMovies/PlayerModelSelection'
import { store } from '../../store/sagaStore'
import PlayerErrorHandler from '../bitmovinPlayer/PlayerErrorHandler'
import ErrorEventModule from '../talentSearch/ErrorEventModule'
import TalentSearch from '../talentSearch/TalentSearch'
import { useContentSeriesTitleStyles } from './useSeriesStyles'
import {
  calculateVODTop,
  calculateLargeVODTop,
  calculateLargeVODPosition
} from './seriesHelpers'
import {
  pushContentDetailsEvent,
  pushContentSelectionEvent,
  pushInteractionContentEvent,
  pushAddSubscriptionEvent
} from '../../GoogleAnalytics'
import { contentSelectionType, CONTENIDO_BLOQUEADO, interactionType, MOVIE, NOT_APPLICABLE, PLAYER, SERIES } from '../../GoogleAnalyticsConstants'
import { getIsLoggedinV1 } from '../../store/slices/login'
import { DEFAULT_THINGS } from '../../constants'
import { COMMON_URL } from '../../utils/environment'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const VodSeries = () => {
  const { state } = useLocation()
  const { data, card } = state

  const cardRef = useRef(card)

  useEffect(() => {
    card ? (cardRef.current = card) : (cardRef.current = '')
  }, [card])

  const moreInfoGetvalue = state?.moreInfoVariable
  const seriesSeasonsData = state?.vodSeriesSeasons
  let episodeItemClicked = state?.episodeItemClicked

  const [seriesData, setSeriesData] = useState(data)
  const [watchList, setWatchList] = React.useState(false)
  const [genreData, setGenreData] = useState([])
  const [contentData, setContentData] = useState('')
  const [watchListData, setWatchListData] = useState([])
  const [apiCallStart, setApiCallStart] = useState(false)
  const [currentButtonFocus, setCurrentButtonFocus] = useState('')

  const [seasonData, setSeasonData] = useState('')
  const [mltData, setMltData] = useState([])
  const [castData, setCastData] = useState([])
  const [currentSeason, setCurrentSeason] = useState('')
  const [seasonstate, setSeasonstate] = useState(false)
  const [showvodseriesdetail, setShowvodseriesdetail] = useState(false)
  const [playBtnClkFocus, setPlayBtnClkFocus] = useState(true)
  const [videoplayerWarning, setVideoPlayerWarning] = useState(false)
  const [watchlistButtonClickFocus, setWatchlistButtonClickFocus] =
    useState(false)
  const [trailerBtnClkFocus, setTrailerBtnClkFocus] = useState(false)
  const [recomendationTab, setRecomendationTab] = useState(false)
  const [moreInfoTab, setMoreInfoTab] = useState(false)
  const [seasonTab, setSeasonTab] = useState(true)
  const [focusRecomendRed, setRecomendFocusRed] = useState(false)
  const [focusMoreinfoRed, setMoreinfoFocusRed] = useState(false)
  const [focusSeasonsRed, setSeasonsFocusRed] = useState(false)
  const [threebuttonvalue, setThreebuttonvalue] = useState('')
  const [upkeyfocus, setUpkeyFocus] = useState(false)
  const [playerInfo, setPlayerInfo] = useState('')
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const [viewDetailButton, setViewDetailButton] = useState('')
  const [promoLabel, setPromoLabel] = useState('')
  const [familyPromoLabel, setFamilyPromoLabel] = useState('')
  const [priceMonth, setPriceMonth] = useState('')
  const [symbolCurrency, setSymbolCurrency] = useState('')
  const [progressBarResponse, setProgressBarResponse] = useState('')
  const [lastFocusedSeasonIndex, setLastFocusedSeasonIndex] = useState(null)
  const [isFocusFrozen, setIsFocusFrozen] = useState(false)
  const [clickedPlaybackId, setClickedPlaybackId] = useState('')
  const [filterlist, setFilterlist] = useState('')
  const [playButtonClicked, setPlayButtonClicked] = useState('')
  const [episodesIds, setEpisodesIds] = useState([])
  const [vodSimultaneous, setVodSimultaneous] = useState({})
  const [playbackRetry, setPlaybackRetry] = useState(false)
  const [streamType, setStreamType] = useState('')
  const [trailerPreviewVisible, setTrailerPreviewVisible] = useState()
  const [isLeavingCurrentState, setIsLeavingCurrentState] = useState(false)
  const [getFocusedElementId, setGetFocusedElementId] = useState(false)
  const [talentSearchfield, setTalentSearchfield] = useState('')
  const [initialFocusDone, setInitialFocusDone] = useState('')
  const [lastSeenPercentage, setLastSeenPercentage] = useState(0)
  const [trailerClicked, setTrailerClicked] = useState(false)
  const [disablePlayBtn, setDisablePlayBtn] = useState(false)
  const [playerBackEpisodeFocus, setPlayerBackEpisodeFocus] = useState(false)
  const [producttype, setproductType] = useState('')
  const [periodicity, setPeriodicity] = useState('')

  const smallprogress = true
  const serieslargeProgress = true
  const navIndex = localStorage.getItem('currNavIdx')

  const watchListRef = useRef(null)
  const seasonRef = useRef([])
  const moreInfoSeasonRefs = useRef([])
  const episodeRef = useRef([])
  const descriptionRef = useRef(null)
  const upBackKeyFocus = useRef(false)
  const isbackMaintainFocus = useRef(false)
  const contentDataRef = useRef(null)
  const subscriptionKeyRef = useRef(null)

  const navigate = useNavigate()

  const getWatchListRedux = useSelector(state => state?.watchList?.watchList)
  const addWatchListRedux = useSelector(state => state?.watchList?.addWatchList)
  const delWatchListRedux = useSelector(state => state?.watchList?.delWatchList)
  const vodSeriesDataRedux = useSelector(
    state => state?.getVodSeries?.seriesData
  )
  const vodSeriesCastRedux = useSelector(
    state => state?.getVodSeries?.seriesCastData
  )
  const vodSeriesMLTRedux = useSelector(
    state => state?.getVodSeries?.seriesMLTData
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const getMediaRes = useSelector(state => state?.player?.getMediaRes?.response)
  const subscriptionInfo = useSelector(
    state => state?.settingsReducer?.getVodSubsInfo?.response
  )
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles?.[0]?.parental
        ?.active
  )
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const trailerPlayerRedux = useSelector(
    state => state?.player?.trailerplayerinfo?.response
  )
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const vodDetails = useSelector(state => state?.login?.vcardDetails)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )
  const continueWatchListResponse = useSelector(
    state => state?.watchList?.continuewatchlist?.response?.groups
  )
  const progressbarContentData = useSelector(
    state => state?.search?.progressbarContent
  )
  const getMediaError = useSelector(
    state => state?.player?.getMediaError?.errors?.[0]
  )
  const getLastSeenData = useSelector(
    state => state?.watchList?.lastSeenWatchListData?.response
  )
  const getTrailerMediaError = useSelector(
    state => state?.player?.trailerplayerResponseError?.errors?.[0]
  )

  const lastTouch = localStorage.getItem('lasttouch')
  const userId = userDetails?.user_id
  const age_rating = vodSeriesCastRedux?.common?.extendedcommon?.media?.rating

  let talentSearchData = useSelector(state => state?.search?.talentSearchData)
  const showApiFailureModal = useSelector(
    state => state?.search?.showApiFailureModal
  )
  const navbar = useSelector(state => state?.homeReducer?.storenavdata)   
  const showTalentModule = useSelector(state => state?.search?.showTalentModule)
  const navbarTab = useSelector(state => state?.homeReducer?.NavTabValue)
  const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
  const nav = useSelector(state => state?.homeReducer?.navbarData)
  const navData = userDetails?.is_kids === 'true' ? navKids : nav
  const homeIndex = navData?.findIndex(item => item?.code === 'homeuser' && item?.page === 'Inicio' );
  const seriesIndex = navData?.findIndex(item =>item?.code === 'seriesnv' && item?.page === 'Series');
  const mycontentIndex = navData?.findIndex(item =>item?.code === 'miscontenidos')
  const contentMenu = useSelector(state => state?.homeReducer?.contentMenu)
  const setVodReturnFocus = useSelector(
    state => state?.search?.setVodReturnFocus
  )
  const statusControlPin = useSelector(
    state =>
      state?.settingsReducer?.statusControlPin?.response?.pin_parental?.info
        ?.value
  )
  const moreEpisodeClick = useSelector(
    state => state?.getVodSeries?.episodeMoreInfo
  )
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const navbarData = useSelector(state => state?.homeReducer?.navbarNodeData?.response?.nodes) ?? []
  const contentSection = navbarData?.find(each => each?.code === navbarTab )

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')

  const apilanguage = translations?.language?.[region]
  let filteredRegionLists = apaMetaData?.byr_filterlist_configuration
    ? JSON.parse(apaMetaData?.byr_filterlist_configuration)
    : ''
  const titleLength =
    vodSeriesCastRedux?.common?.extendedcommon?.media?.serie?.title?.length || 0
  const originalTitleLength =
    vodSeriesCastRedux?.common?.extendedcommon?.media?.originaltitle?.length ||
    0
  const contentSeriesTitleStyles =
    useContentSeriesTitleStyles(vodSeriesCastRedux)
  const imageUrl = vodSeriesCastRedux?.common?.image_base_horizontal
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
  
  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length > length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const calculateTop = useCallback(() => {
    if (titleLength > 28 && originalTitleLength > 27) return '30px'
    if (originalTitleLength > 27) return '106px'
    if (titleLength > 28 && originalTitleLength <= 27) return '136px'
    if (titleLength > 28) return '71px'
    return '136px'
  }, [titleLength, originalTitleLength])

  const calculateHeight = useCallback(() => {
    if (titleLength < 28 && originalTitleLength < 27) return '250px'
    if (originalTitleLength > 27 && titleLength > 28) return '252px'
    if (originalTitleLength >= 27 || titleLength >= 28) return '255px'
    return ''
  }, [titleLength, originalTitleLength])

  const calculateMaxWidth = useCallback(() => {
    return titleLength > 28 && originalTitleLength <= 27 ? '1035px' : '933px'
  }, [titleLength, originalTitleLength])

  const calculateJustifyContent = useCallback(() => {
    if (titleLength == 29 && originalTitleLength == 29) return 'space-between'
    if (titleLength > 28 && originalTitleLength > 27) return 'space-around'
    return ''
  }, [titleLength, originalTitleLength])

  const SeriesMaintop = calculateTop()
  const seriesMainHeight = calculateHeight()
  const seriesMainMaxWidth = calculateMaxWidth()
  const seriesJustifyContent = calculateJustifyContent()
  const supportedStream =
    apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)
  const dispatch = useDispatch()

  const fetchApi = id => {
    setApiCallStart(true)
    callGetWatchList('')
    dispatch(
      getIsLoggedinV1({HKS:
        userDetails?.session_stringvalue
          ? userDetails?.session_stringvalue
          : loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue,
            pageName:'series'
  })
    )
    dispatch(
      vodSeries({
        id,
        userId,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids
      })
    )
    dispatch(
      vodSeriesCast({
        id,
        userId,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids
      })
    )
    dispatch(
      vodSeriesMLT({
        id,
        userId,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids,
        display_only: 'series',
        user_token: userDetails?.user_token,
        filterlist:
          filteredRegionLists?.[`${region}`]?.filterlist ??
          filteredRegionLists?.default?.filterlist
      })
    )
    dispatch(
      continueWatchlist({
        id: userDetails?.lasttouch?.seen
          ? userDetails?.lasttouch?.seen
          : lastTouch,
        userId: userId,
        HKS: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(
      getStatusControlPin({
        hks: userDetails?.session_stringvalue,
        userId: userDetails?.user_id,
        loadingPage :'series'
      })
    )
    if (watchFree) {
      callVodSubsInfo(id, 'watchfree')
    } else {
      callVodSubsInfo(id)
      dispatch(getVodID(id))
    }
    const payload = {
      hks: vodDetails?.confirmscreen
        ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
        : userDetails?.session_stringvalue,
      user_id: vodDetails?.confirmscreen
        ? loginInfo?.user_id ?? registerInfo?.user_id
        : userDetails?.user_id,
      user_token: vodDetails?.confirmscreen
        ? loginInfo?.user_token ?? registerInfo?.user_token
        : userDetails?.user_token,
        loadingPage :'series'
    }
    dispatch(getControlPin(payload))
    dispatch(setShowTalentModule(false))
    dispatch(setShowApiFailureModal(false))
  }

  useEffect(() => {
    if (userDetails?.lasttouch?.seen && apiCallStart) {
      dispatch(
        lastSeenWatch({
          user_id: userId,
          group_id: seriesData?.id ?? seriesData?.group_id,
          user_hash: userDetails?.session_userhash,
          filterlist: filterlist,
          lasttouch: userDetails?.lasttouch?.seen
        })
      )
      setApiCallStart(false)
    }
  }, [userDetails])

  useEffect(() => {
    setLastSeenPercentage(getLastSeenData?.vistime?.last?.progress || 0)
  }, [getLastSeenData])

  useEffect(() => {
    const element = document.getElementById(setVodReturnFocus)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
        block: 'center'
      })
      element.focus()
      setCurrentButtonFocus(setVodReturnFocus)
    }
    if (currentButtonFocus?.slice(0, 6) == 'castId') {
      setCurrentButtonFocus('cast')
    }
  }, [showTalentModule])

  const handleTranslationchange = useCallback(keyname => {
    if (!apilanguage?.[keyname]) {
      return keyname?.slice(0, 22) + '...'
    } else {
      return handleOriginalText(keyname)
    }
  }, [])

  const handleOriginalText = useCallback(
    keyname => {
      if (keyname === 'Vcard_TabsMasInformacion_TextoTitulo') {
        const lowercasedText = apilanguage?.[keyname]?.toLowerCase()
        return lowercasedText.charAt(0).toUpperCase() + lowercasedText.slice(1)
      } else {
        return apilanguage?.[keyname]
          ?.toLowerCase()
          ?.replace(/(^|\s)\S/g, char => char.toUpperCase())
      }
    },
    [apilanguage]
  )

  const handleWatchListAdd = async () => {
    dispatch(
      addWatchList({
        id: seriesData?.id ?? seriesData?.group_id,
        userId,
        HKS: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        user_hash: vodDetails?.confirmscreen
          ? loginInfo?.session_userhash ?? registerInfo?.session_userhash
          : userDetails?.session_userhash
      })
    )
  }
  const handleWatchListDel = async () => {
    dispatch(
      delWatchList({
        id: seriesData?.id ?? seriesData?.group_id,
        userId,
        HKS: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        user_hash: vodDetails?.confirmscreen
          ? loginInfo?.session_userhash ?? registerInfo?.session_userhash
          : userDetails?.session_userhash
      })
    )
  }

  const handleCastClick = (cast, focusIndex) => {
    dispatch(setVodReturnFocusById(focusIndex))
    setTalentSearchfield(
      cast?.first_name === undefined && cast?.last_name === undefined
        ? cast?.surname + ' ' + cast?.name
        : cast?.first_name + ' ' + cast?.last_name
    )
    dispatch(
      getTalentSearchData({
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        value: cast?.id,
        typemusic: 'album',
        provider_id: '3',
        filterlist: filterlist,
        field: 'TALENT'
      })
    )
  }

  const addProveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  useEffect(() => {
    if (talentSearchData) {
      talentSearchData?.length > 0
        ? dispatch(setShowTalentModule(true))
        : dispatch(setShowApiFailureModal(true))
    }
  }, [talentSearchData])

  useEffect(() => {
    setFilterlist(
      filteredRegionLists?.[`${region}`]?.filterlist ??
        filteredRegionLists?.default?.filterlist
    )
  }, [filteredRegionLists])

  useEffect(() => {
    if (playerInfo == 1) {
      if(state?.vCardBackFocus === 'trailer'){
        setCurrentButtonFocus('trailer')
        document.getElementById('trailerButton')?.focus()
        setTrailerBtnClkFocus(true)
        setPlayBtnClkFocus(false)
      }else{
        setPlayBtnClkFocus(
          (playerInfo == 1 ||
            state?.vCardBackFocus === 'play') ||
            playBtnClkFocus
        )
        setCurrentButtonFocus('play')
        setTrailerBtnClkFocus(false)
        document.getElementById('play')?.focus()

      } 
    } else if(playerInfo == 0 && state?.vCardBackFocus === 'trailer' || state?.trailerNode == "true"){
      dispatch(setVodReturnFocusById('trailer'))
      setTrailerBtnClkFocus(true)
      setPlayBtnClkFocus(false)
      setCurrentButtonFocus('trailer')
      document.getElementById('trailerButton')?.focus()
    } 
    if (seriesData?.id || seriesData?.group_id) {
      fetchApi(seriesData?.id ?? seriesData?.group_id)
    }
    if (state?.flag == true) {
      handleBackInfoTab()
    }
  }, [seriesData])

  useEffect(() => {
    handleDefaultRecom()
  }, [showvodseriesdetail])

  const handleDefaultRecom = () => {
    if (episodeItemClicked) {
      return
    } else if (showvodseriesdetail) {
      if (moreInfoGetvalue) {
        return
      } else if (episodeItemClicked) {
        return
      } else {
        setSeasonTab(true)
        setUpkeyFocus(true)
      }
    }
  }
  useEffect(() => {
    if (subscriptionInfo) {
      if (clickedPlaybackId && subscriptionInfo?.playButton?.visible == '0') {
        let monthData
        subscriptionInfo?.listButtons?.button?.length > 0 &&
          subscriptionInfo?.listButtons?.button?.map(
            eachdata => (monthData = eachdata)
          )
        const viewPlan = {
          logo: settingLogoUrl(monthData?.family),
          workflowStart: monthData?.linkworkflowstart,
          verticalImage: vodSeriesDataRedux?.image_medium,
          family: monthData?.family,
          periodicity: monthData?.periodicity,
          price: monthData?.price,
          currency: monthData?.currency,
          styles: monthData?.style,
          taxLabel: getTaxLabel(monthData?.family),
          infoString: getFreeChargeString(monthData?.bonus),
          subscribeButton: getSubscribeButton(monthData?.family),
          viewButton: getViewDetailsButton(monthData?.family),
          frequency: monthData?.frequency,
          producttype: monthData?.producttype,
          isSeries: true
        }
        dispatch(getViewSubscribeData(viewPlan))
        dispatch(
          getFinishSubscriptionPlayerData({
            data: vodSeriesCastRedux?.common,
            contentDataplayer: contentData,
            episodeData: seasonData?.episodes,
            inputValue: state?.inputValue
          })
        )
        navigate(
          '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          {
            state: {
              pageName: '/series',
              dataId: vodSeriesCastRedux?.common,
              data: seriesData ?? state?.data ?? state?.dataId
            }
          }
        )
      } else {
        setPlayerInfo(subscriptionInfo?.playButton?.visible)
        if (watchFree && (seasonData?.id || seriesData?.group_id || data?.id)) {
          callGetMedia('watchfree')
        } else if (!watchFree && subscriptionInfo?.playButton?.visible && (seasonData?.id || seriesData?.group_id || data?.id)) {
          callGetMedia()
         trailerPreviewVisible && callGetMediaTrailer('watchfree')
        }
      }
    }
  }, [subscriptionInfo])

  useEffect(() => {
    if (playbackRetry && streamType) {
      callGetMedia()
    }
  }, [playbackRetry, streamType])

  useEffect(() => {
    const code = getMediaError?.code
    if (
      (code == 'PLY_DEV_00006' ||
        code == 'PLY_CSS_00001' ||
        code == 'PLY_CSS_00004' ||
        code == 'PLY_PLY_00009') &&
      playbackRetry
    ) {
      setPlaybackRetry(false)
      setStreamType(
         !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
    }
    if (code == 'PLY_PLY_00009' && !playbackRetry) {
      setStreamType(
        supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      setPlaybackRetry(true)
    }
  }, [getMediaError])

  const handleColourButton = eachdata => {
    switch (eachdata?.oneoffertype) {
      case 'subscrition':
        return translations?.language?.[region]?.['Color_ColorSuscripcion']
      case 'buy':
        return eachdata?.oneofferdesckey ===
          'offer_button_desc_subscription_cv_seasonbuy'
          ? translations?.language?.[region]?.['Color_ColorTemporada']
          : eachdata?.oneofferdesckey ===
            'offer_button_desc_subscription_cv_episodebuy'
          ? translations?.language?.[region]?.['Color_ColorEpisodio']
          : translations?.language?.[region]?.['Color_ColorCompra']
      case 'download_buy':
        return eachdata?.oneofferdesckey ===
          'offer_button_desc_subscription_cv_seasonbuy'
          ? translations?.language?.[region]?.['Color_ColorTemporada']
          : eachdata?.oneofferdesckey ===
            'offer_button_desc_subscription_cv_episodebuy'
          ? translations?.language?.[region]?.['Color_ColorEpisodio']
          : translations?.language?.[region]?.['Color_ColorCompra']

      case 'download_rent':
        return translations?.language?.[region]?.['Color_ColorRenta']
      case 'rent':
        return translations?.language?.[region]?.['Color_ColorRenta']
      default:
        return DEFAULT_THINGS.defaultColor
    }
  }

  const handleSubInteractiontype = (eachdata) =>{
     switch (eachdata?.oneoffertype) {
      case 'subscrition':
        return interactionType.SUBSCRIPTION_INTERACTION_TYPE
      case 'buy':
        return interactionType.COMPRAR_INTERACTION_TYPE
      case 'download_buy':
        return interactionType.COMPRAR_INTERACTION_TYPE
      case 'download_rent':
        return interactionType.RENTAR_INTERACTION_TYPE
      case 'rent':
        return interactionType.RENTAR_INTERACTION_TYPE
      default:
        return 
    }
  }

  const handleGAInteractionEvent = (interactionType) => {
    pushInteractionContentEvent({
      contentData:vodSeriesCastRedux,
      interactionType:interactionType ,
      contentType:"series",
      screenData:userDetails,
      contentSection:contentSection?.text,
      subscriptionInfo:subscriptionInfo?.listButtons?.button?.[0]
    })
  }

  const handleGAAddSubscriptionEvent = (interactionType,subsButton) => {
    pushAddSubscriptionEvent({
      contentData:vodSeriesCastRedux,
      interactionType:interactionType == 'subscriptionbuttons'? handleSubInteractiontype(subsButton) : interactionType,
      contentType:"series",
      screenData:userDetails,
      contentSection:contentSection?.text,
      subscriptionInfo:subscriptionInfo?.listButtons?.button?.[0]
    })
  }

  useEffect(() => {

    if (getMediaRes?.media || getMediaError?.code) {
      if (clickedPlaybackId) {
        checkPlaybackError()

        getMediaRes?.media?.initial_playback_in_seconds > 0
        ? handleClick('resume')
        : handleClick('begin')
        setClickedPlaybackId('')
      } else if (playbackRetry) {
        setVodSimultaneous({})
        setPlaybackRetry(false)
        setStreamType(
           !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
            ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
        )
        const payload = {
          vistime: {
            last: { progress: getMediaRes?.media?.initial_playback_in_seconds }
          }
        }
        getMediaError?.code != 'PLY_PLY_00009' && handlePlayBtn(payload)
      }
      contentDataRef.current = {
        user_id:
          userDetails?.user_id || loginInfo?.user_id || registerInfo?.user_id,
        parent_id: userDetails?.parent_id,
        sign_up_method: 'correo electronico',
        suscriptions: subscriptionKeyRef.current?.toLowerCase(),
        user_type: watchFree ? 'anonimo' : 'registrado',
        device: 'tv',
        device_model: COMMON_URL?.device_model,
        device_name: COMMON_URL?.device_name,
        authpn: COMMON_URL?.authpn,
        content_subsection: NOT_APPLICABLE,
        content_section: contentMenu,
        country:
          userDetails?.country_code?.toLowerCase() ||
          loginInfo?.country_code?.toLowerCase() ||
          registerInfo?.country_code?.toLowerCase(),
        content_id: getMediaRes?.group?.common?.id,
        content_name: getMediaRes?.group?.common?.title?.toLowerCase(),
        content_type: getMediaRes?.group?.common?.extendedcommon?.media?.episode
          ?.number
          ? SERIES
          : MOVIE,
        content_category: genreData,
        content_availability: 'por suscripcion',
        content_episode: getMediaRes?.group?.common?.extendedcommon?.media?.serie?.title?.toLowerCase(),
        modulo_name: 'continua con tu reproduccion',
        content_price: '0.0',
        content_season: getMediaRes?.group?.common?.extendedcommon?.media
          ?.episode?.season
          ? `temporada ${getMediaRes?.group?.common?.extendedcommon?.media?.episode?.season}`
          : NOT_APPLICABLE,
        content_episode: getMediaRes?.group?.common?.extendedcommon?.media
          ?.episode?.number
          ? `episodio ${
              getMediaRes?.group?.common?.extendedcommon?.media?.episode?.number
            } ${getMediaRes?.group?.common?.extendedcommon?.media?.serie?.title?.toLowerCase()}`
          : NOT_APPLICABLE,
        provider:
          getMediaRes?.group?.common?.extendedcommon?.media?.proveedor?.codigo?.toLowerCase()
      }
    }
  }, [getMediaRes, getMediaError, playbackRetry, disablePlayBtn])

  useEffect(() => {
    if (trailerPlayerRedux && trailerClicked) {
      navigate('/vodPlayer', {
        state: {
          data: vodSeriesCastRedux?.common,
          contentDataplayer: contentData,
          episodeData: seasonData?.episodes,
          content_list_id: seasonData?.id,
          showControls: 'vod',
          trailerGetMedia: trailerPlayerRedux,
          inputValue: state?.inputValue,
          returnPage: state?.returnPage == 'search' ? state?.returnPage : '',
          backfocusid: state?.backfocusid,
          vCardBackFocus: 'trailer'
        }
      })
      setTrailerClicked(false)
    }
  }, [trailerPlayerRedux])

  useEffect(() => {
    if (isLeavingCurrentState) {
      // Reset focus to the first episode and the first season
      setCurrentButtonFocus('season1')
      setCurrentSeason(1)
      setLastFocusedSeasonIndex(0)
      setSeasonData(contentData?.seasons[0])
      setIsLeavingCurrentState(false) // Reset the flag
    }
  }, [isLeavingCurrentState])

   useEffect(() => {
     // GA: Content details event
    vodSeriesCastRedux && subscriptionInfo && pushContentDetailsEvent({contentData:vodSeriesCastRedux, contentType:"series",
    screenData:userDetails, contentSection:contentSection?.text, subscriptionInfo:subscriptionInfo?.listButtons?.button?.[0]})
   },[vodSeriesCastRedux,subscriptionInfo])

  useEffect(() => {
    const trailerPreview =
      vodSeriesCastRedux?.common?.extendedcommon?.media?.haspreview
    const seriesCast =
      vodSeriesCastRedux?.external?.gracenote?.cast ??
      vodSeriesCastRedux?.common?.extendedcommon?.roles?.role

    const genre =
      vodSeriesCastRedux?.external?.gracenote?.genres ??
      vodSeriesCastRedux?.common?.extendedcommon?.genres?.genre

    let genredata = []
    genre &&
      genre?.length > 0 &&
      genre?.map((item, index) => {
        if (item?.desc) {
          genredata?.push(item?.desc)
        } else if (item) {
          genredata?.push(item)
        }
      })

        let removedExtraGenre = genredata.slice(0,2)
        let filteredGenreData = removedExtraGenre.map((item=>{
          if(typeof item === 'string' && item?.endsWith(',')){
            return item?.slice(0,-1)
          }
          return item
        }))
      setGenreData(filteredGenreData?.join(', '))
      dispatch(setGenreDetails(filteredGenreData?.join(', ')))

    let castdata = []
    seriesCast &&
      seriesCast?.length > 0 &&
      seriesCast?.map(item => {
        if (
          item?.role_id == '1' ||
          item?.role_id == '114' ||
          item?.role_id == '9' ||
          item?.role_id == '16'
        ) {
          item?.talents.map(i => {
            castdata.push({ ...i, role: item.role_name })
          })
        } else if (item?.id == '13617516' || item?.id == '13617517') {
          item?.talents?.talent.map(i => {
            castdata.push({ ...i, role: item.name })
          })
        }
      })
    setCastData(castdata)
    if (trailerPreview) {
      setTrailerPreviewVisible(trailerPreview)
    }
  }, [vodSeriesCastRedux])

  useEffect(() => {
    if (imageUrl) {
      const img = new Image()
      img.src = imageUrl
    }
  }, [imageUrl])

  useEffect(() => {
    setContentData(vodSeriesDataRedux)
  }, [vodSeriesDataRedux])

  useEffect(() => {
    setWatchListData(getWatchListRedux)
  }, [getWatchListRedux])

  useEffect(() => {
    let isSeries = []
    vodSeriesMLTRedux?.length > 0 &&
      vodSeriesMLTRedux?.map(item => {
        if (item?.is_series) {
          isSeries.push(item)
        }
      })
    setMltData(isSeries)
  }, [vodSeriesMLTRedux])

  const vodDuration = moment(
    vodSeriesCastRedux?.common?.duration,
    'HH:mm:ss'
  ).format(
    vodSeriesCastRedux?.common?.duration?.startsWith('00')
      ? `mm [min]`
      : `H [h] mm [min]`
  )

  useEffect(() => {
    if (delWatchListRedux?.msg === 'OK') {
      callGetWatchList(delWatchListRedux)
      localStorage.setItem('lasttouch', delWatchListRedux?.lasttouch?.favorited)
      setWatchList(false)
      setCurrentButtonFocus('addToWatchlist')
    }
  }, [delWatchListRedux])

  useEffect(() => {
    if (addWatchListRedux?.msg === 'OK') {
      setWatchList(true)
      callGetWatchList(addWatchListRedux)
      localStorage.setItem('lasttouch', addWatchListRedux?.lasttouch?.favorited)
      setCurrentButtonFocus('watchlist')
    }
  }, [addWatchListRedux])

  useEffect(() => {
    if (moreInfoGetvalue || episodeItemClicked) {
      return
    } else {
      contentData && handleSeasonEpisodes()
    }
  }, [contentData])

  useEffect(() => {
    if (watchListData?.length > 0) {
      const getWatchId = seriesData?.id ?? seriesData?.group_id
      const watchListPresent = watchListData?.some(
        item => item?.id === getWatchId
      )
      setWatchList(watchListPresent)
    }
  }, [watchListData])

  const handleClick = value => {
    if (getMediaRes) {
      if (
        (securityPinCheck &&
          statusControlPin == 50 &&
          parseInt(age_rating?.code) > 18) ||
        (statusControlPin == 40 && parseInt(age_rating?.code) >= 16) ||
        (statusControlPin == 30 && parseInt(age_rating?.code) >= 13) ||
        (statusControlPin == 20 && parseInt(age_rating?.code) >= 7)
      ) {
        contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
        contentDataRef.current['content_section'] = PLAYER
        navigate('/my-settings/help-And-Settings/security-pin/configure', {
          state: {
            data: 'Create',
            item: data,
            pageName: 'series',
            contentDataplayer: contentData,
            episodeData: seasonData?.episodes,
            getMediaData: getMediaRes,
            resume: value == 'begin' ? false : true,
            backfocusid: state?.backfocusid,
            seasonTabStatus: playerBackEpisodeFocus,
            episodeIndexToFocus: episodeIndexToFocus,
            gaContentData: contentDataRef.current,
            episodeItemClicked:
              playButtonClicked == 'episodeClicked' ? true : false
          }
        })
      } else {
        if (value == 'resume') {
          setVideoPlayerWarning(true)
        } else {
          dispatch(clearVodSeriesMlt())
          dispatch(
            getFinishSubscriptionPlayerData({
              data: vodSeriesCastRedux?.common,
              contentDataplayer: contentData,
              episodeData: seasonData?.episodes,
              inputValue: state?.inputValue
            })
          )
          navigate('/vodPlayer', {
            state: {
              data: vodSeriesCastRedux?.common,
              contentDataplayer: contentData,
              episodeData: seasonData?.episodes,
              showControls: 'vod',
              getMedia: getMediaRes,
              resume: value == 'begin' ? false : true,
              inputValue: state?.inputValue,
              episodeItemClicked:
                playButtonClicked == 'episodeClicked' ? true : false,
              returnPage:
                state?.returnPage == 'search' ? state?.returnPage : '',
              backfocusid: state?.backfocusid,
              vCardBackFocus: 'play',
              seasonTabStatus: playerBackEpisodeFocus,
              episodeIndexToFocus: episodeIndexToFocus
            }
          })
          localStorage.setItem('introFinished', '')
        }
      }
    }
  }

  const handleTrailerClick = () => {
    const code = getTrailerMediaError?.code
    if (code) {
      navigate('/GeneralError', {
        state: { vcardSeries: data, vcardType: 'series' }
      })
    } else {
      setTrailerClicked(true)
      callGetMediaTrailer()
    }
  }

  const handleEpisodeClick = (event, item, indx) => {
    setPlayerBackEpisodeFocus(true)
    store?.dispatch(getEpisodeVariable(true))
    dispatch(
      getEpisodeMoreInfo({
        lastSeasonIndex: lastFocusedSeasonIndex,
        episodeItem: item,
        episodeButtonIndx: indx,
        episodeclicked: true,
        seasonData: seasonData?.episodes,
        contentDataFromMoreInfo: contentData
      })
    )
    setPlayButtonClicked('episodeClicked')
    setClickedPlaybackId(item?.id)
    event.preventDefault()
    if (
      watchFree &&
      item?.format_types !== 'free,download' &&
      item?.format_types !== 'free'
    ) {
      store?.dispatch(getEpisodeVariable(false))
      navigate('/EPconfirmation', {
        state: { seriesEpisodeData: item, page: 'series', pageName: '/series', episodeItemClicked: true, gaContentData: contentDataRef.current}
      })
    } else {
      callVodSubsInfo(item?.id)
    }
  }

  const handleEpisodeMoreClick = (item, index) => {
    dispatch(
      getEpisodeMoreInfo({
        lastSeasonIndex: lastFocusedSeasonIndex,
        episodeItem: item,
        moreInfoButtonIndex: index,
        moreInfoClicked: true,
        seasonData: seasonData?.episodes,
        contentDataFromMoreInfo: contentData
      })
    )
    const indexepisode = index
    navigate('/EpMoreInfo', {
      state: {
        episodeItem: item,
        moreseriesData: data,
        episodeMoreInfo: true,
        indexepisode: indexepisode,
        castData: castData,
        genreData: genreData,
        userDetails: userDetails,
        filterlist: filterlist,
        talentSearchData: talentSearchData,
        apaAssetData: apaAssetData,
        vodSeasonsData: vodSeriesDataRedux
      }
    })
  }

  const handleSeasonEpisodes = () => {
    !currentSeason && setCurrentSeason(1)
    contentData?.seasons && setSeasonData(contentData?.seasons[0])
  }

  const handleSeasons = (item, index) => {
    setSeasonData(item)
    setCurrentSeason(index + 1)
    setCurrentButtonFocus(`season${index + 1}`)
    setSeasonstate('seasonsdata')
  }

  const handleMLT = (value,index) => {
    const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbar,
        content_list : handleTranslationchange('Vcard_CarruselRecomendaciones_TextoTitulo'),
        modulo_name : 'carrusel vcard',
        content_list_id : value?.id
      }
    if (value?.is_series) {
      setSeriesData(value)
      fetchApi(value?.id)
      setCurrentButtonFocus('play')
      setPlayBtnClkFocus(false)
      setContentData(value)
      setCastData(value)
      setSeasonTab(true)
      setWatchListData([])
      setShowvodseriesdetail(false)
      setIsFocusFrozen(false)
      setWatchList(false)
      pushContentSelectionEvent(userData,value,index,contentSelectionType.VCARD)
    } else {
      pushContentSelectionEvent(userData,value,index,contentSelectionType.VCARD)
      navigate('/movies', { state: { vodData: value } })
    }
  }

  const handleAgerating = rating => {
    if (rating?.id == '10') {
      return 'Todos'
    } else if (rating?.id == '20' && rating?.code == '+7') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.id == '30' && rating?.code == '+12') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.id == '40' && rating?.code == '+16') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.id == '50' && rating?.code == '+18') {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    } else if (rating?.code) {
      return `${' ' + rating?.code + ' ' + `Años` + ' '}`
    }
  }

  useEffect(() => {
    handleMainFocus()
  }, [playerInfo, viewDetailButton, trailerPreviewVisible, subscriptionInfo])

  const handleMainFocus = useCallback(() => {
    if (moreInfoGetvalue || episodeItemClicked) {
      const elementId = `episode-image-button${state?.episodeIndexToFocus}`
      const element = document.getElementById(elementId)
      if (element) {
        element.focus() // Focus on the element
      }
      return
    }

    if (playerInfo == 1) {
      setCurrentButtonFocus('play')
    } else {
      if (playerInfo == 0 && subscriptionInfo?.listButtons) {
        if(state?.vCardBackFocus === 'trailer'){
              document.getElementById('trailerButton')?.focus()
              setTrailerBtnClkFocus(true)
          dispatch(setVodReturnFocusById('trailer'))
        }else if (viewDetailButton) {
          const viewDetailElement = document.getElementById('viewDetail')
          if (viewDetailElement) {
            viewDetailElement.focus()
          }
        } else {
          setCurrentButtonFocus('subscribe-button')
          document.getElementById('subscription-button')?.focus()
        }
      } else {
          document.getElementById('deleteButton')?.focus()
          setWatchlistButtonClickFocus(false)
      }
    }
  }, [
    playerInfo,
    viewDetailButton,
    trailerPreviewVisible,
    subscriptionInfo,
    upBackKeyFocus
  ])

  const checkPlaybackError = () => {
    const code = getMediaError?.code
    if (
      code == 'PLY_DEV_00006' ||
      code == 'PLY_CSS_00001' ||
      code == 'PLY_CSS_00004'
    ) {
      const data = {
        errFromPlayer: false,
        errorMessage1:
          code == 'PLY_DEV_00006'
            ? (apilanguage?.PLY_DEV_00006_title ?? 'PLY_DEV_00006_title').slice(
                0,
                19
              )
            : (apilanguage?.PLY_CSS_00001_title ?? 'PLY_CSS_00001_title').slice(
                0,
                19
              ),
        errorMessage2:
          code == 'PLY_DEV_00006'
            ? (apilanguage?.PLY_DEV_00010 ?? 'PLY_DEV_00010').slice(0, 19)
            : (apilanguage?.PLY_CSS_00001 ?? 'PLY_CSS_00001').slice(0, 19),
        errorType: code == 'PLY_DEV_00006' ? 'device' : 'playback'
      }
      setVodSimultaneous(data)
      return
    }
  }

  const callVodSubsInfo = (id, type = '') => {
    const payload = {
      ...(type !== 'watchfree' && {
        userId: vodDetails?.confirmscreen
          ? loginInfo?.parent_id ?? registerInfo?.parent_id
          : userDetails?.parent_id
      }),
      ...(type !== 'watchfree' && {
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue
      }),
      is_kids: vodDetails?.confirmscreen
        ? loginInfo?.is_kids ?? registerInfo?.is_kids
        : userDetails?.is_kids,
      url: `group_id=${id ?? seriesData?.id ?? seriesData?.group_id}`,
      ...(type === 'watchfree' && { type })
    }
    dispatch(getVodSubscriptionInfo(payload))
  }

  const callGetMedia = (type = '') => {
    const payload = {
      id: playButtonClicked == 'episodeClicked' ? clickedPlaybackId : seriesData?.id ?? seriesData?.group_id ?? data?.id ?? data?.group_id ?? clickedPlaybackId,
      payway_token: subscriptionInfo?.playButton?.payway_token,
      ...(type !== 'watchfree' && {
        HKS: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue
      }),
      ...(type !== 'watchfree' && {
        userId: vodDetails?.confirmscreen
          ? loginInfo?.user_id ?? registerInfo?.user_id
          : userDetails?.user_id
      }),
      ...(type !== 'watchfree' && {
        user_token: loginInfo?.user_token ?? registerInfo?.user_token
      }),
      streamType,
      ...(type === 'watchfree' && { type })
    }
   if(subscriptionInfo?.playButton?.payway_token) dispatch(getMediaAPI(payload))
  }

  const callGetMediaTrailer = (type = '') => {
    const userToken = loginInfo?.user_token ?? registerInfo?.user_token
    const payload = {
      id: seriesData?.id ?? seriesData?.group_id,
       streamType: supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
        ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
        : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0],
      ...(type !== 'watchfree' &&
        userToken && {
          HKS: vodDetails?.confirmscreen
            ? loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue
            : userDetails?.session_stringvalue
        }),
      ...(type !== 'watchfree' &&
        userToken && {
          userId: vodDetails?.confirmscreen
            ? loginInfo?.user_id ?? registerInfo?.user_id
            : userDetails?.user_id
        }),
      ...(type !== 'watchfree' &&
        userToken && {
          user_token: userToken
        }),
      ...(type === 'watchfree' && { type })
    }
    trailerPreviewVisible == 'true' && dispatch(getTrailerPlayer(payload))
  }

  const callGetWatchList = data => {
    const payload = {
      id: data?.lasttouch?.favorited || lastTouch,
      userId,
      HKS: vodDetails?.confirmscreen
        ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
        : userDetails?.session_stringvalue,
      user_hash: vodDetails?.confirmscreen
        ? loginInfo?.session_userhash ?? registerInfo?.session_userhash
        : userDetails?.session_userhash,
      filterlist:
        filteredRegionLists?.[`${region}`]?.filterlist ??
        filteredRegionLists?.default?.filterlist
    }
    dispatch(getWatchList(payload))
  }

  const handlePlayBtn = item => {
    // GA: Interaction Content Event
    handleGAInteractionEvent(interactionType.PLAY_INTERACTION_TYPE)
    setDisablePlayBtn(true)
    // if (watchFree) {
    //   callVodSubsInfo(item?.id, 'watchfree')
    // } else {
    //   callVodSubsInfo(item?.id || item?.group_id)
    // }
    setPlayButtonClicked('')
    setClickedPlaybackId(seriesData?.id ?? seriesData?.group_id ?? item?.id ?? item?.group_id)
    setPlayBtnClkFocus(true)
    setCurrentButtonFocus('resume')
    setWatchlistButtonClickFocus(false)
    setTrailerBtnClkFocus(false)
  }

  const handlePlayKeyDown = e => {
    if (e.key == 'ArrowDown' || e.keyCode == 40) {
      setWatchlistButtonClickFocus(false)
    }
    if (e.key == 'ArrowUp' || e.keyCode == 38) {
      document.getElementById(`nav-${navIndex}`)?.focus()
    }
  }

  const handletrailer = () => {
    setVideoPlayerWarning(false)
    setPlayBtnClkFocus(false)
    setCurrentButtonFocus('trailer')
    setWatchlistButtonClickFocus(false)
    setTrailerBtnClkFocus(true)
    handleTrailerClick()
  }

  const handleWatchlist = () => {
    setWatchlistButtonClickFocus(true)
    setTrailerBtnClkFocus(false)
    setPlayBtnClkFocus(false)
  }

  const handleWatchlistUnfocus = e => {
    setTrailerBtnClkFocus(false)
    if (e.key === 'ArrowDown') {
      setWatchlistButtonClickFocus(false)
    }
  }

  const handleRecomendationTab = () => {
    setRecomendationTab(true)
    setSeasonstate('recommenddata')
    setThreebuttonvalue('recommenddata')
    setMoreInfoTab(false)
    setSeasonTab(false)
    setShowvodseriesdetail(true)
    setRecomendFocusRed(true)
    setMoreinfoFocusRed(false)
    setSeasonsFocusRed(false)
  }

  const handleSecondRecomendationTab = () => {
    setRecomendationTab(true)
    setSeasonstate('recommenddata')
    setThreebuttonvalue('recommenddata')
    setMoreInfoTab(false)
    setSeasonTab(false)
    setShowvodseriesdetail(true)
    setRecomendFocusRed(true)
    setMoreinfoFocusRed(false)
    setSeasonsFocusRed(false)
  }

  const handleMoreInfoTab = () => {
    setMoreInfoTab(true)
    setSeasonstate('moreinfodata')
    setSeasonTab(false)
    setRecomendationTab(false)
    setShowvodseriesdetail(true)
    setRecomendFocusRed(false)
    setMoreinfoFocusRed(true)
    setSeasonsFocusRed(false)
  }

  const handleSecondMoreInfoTab = () => {
    setMoreInfoTab(true)
    setSeasonstate('moreinfodata')
    setSeasonTab(false)
    setRecomendationTab(false)
    setShowvodseriesdetail(true)
    setRecomendFocusRed(false)
    setMoreinfoFocusRed(true)
    setSeasonsFocusRed(false)
  }

  const handleSeasonsTab = indx => {
    setSeasonTab(true)
    setSeasonstate('seasonsdata')
    setThreebuttonvalue('seasonsdata')
    setMoreInfoTab(false),
      setRecomendationTab(false),
      setShowvodseriesdetail(true)
    setRecomendFocusRed(false)
    setMoreinfoFocusRed(false)
    setSeasonsFocusRed(true)
    if (!upBackKeyFocus.current) {
      setIsFocusFrozen(true)
    }
  }

  const handleFirstSeasonFocus = () => {
    setCurrentButtonFocus('seasons')
    setSeasonTab(true)
    !upBackKeyFocus.current && handleSeasonsTab()
  }

  const handleSecondSeasonsTab = () => {
    setSeasonTab(true)
    setSeasonstate('seasonsdata')
    setThreebuttonvalue('seasonsdata')
    setMoreInfoTab(false),
      setRecomendationTab(false),
      setShowvodseriesdetail(true)
    setRecomendFocusRed(false)
    setMoreinfoFocusRed(false)
    setSeasonsFocusRed(true)
  }

  const handleFocusredRecom = event => {
    if (event.code === 'Enter' && event.keyCode === 13) {
      setRecomendFocusRed(true)
      setSeasonsFocusRed(false)
      setMoreinfoFocusRed(false)
    } else if (event.keyCode === 38) {
      upBackKeyFocus.current = true
      handleBackandUp(event)
    } else if (event.keyCode == 40) {
      setCurrentButtonFocus(`season${lastFocusedSeasonIndex + 1}`)
    }
  }

  const handleFocusredMoreInfo = event => {
    if (event.key === 'Enter' && event.keyCode === 13) {
      setRecomendFocusRed(false)
      setSeasonsFocusRed(false)
      setMoreinfoFocusRed(true)
    } else if (event.keyCode === 38) {
      upBackKeyFocus.current = true
      handleBackandUp(event)
    } else if (event.keyCode == 40) {
      setCurrentButtonFocus(`season${lastFocusedSeasonIndex + 1}`)
    }
  }

  const handleFocusredSeason = event => {
    if (event.key == 'Enter' && event.keyCode == 13) {
      setSeasonsFocusRed(true)
      setRecomendFocusRed(false)
      setMoreinfoFocusRed(false)
    } else if (event.keyCode == 40 || event.key === 'ArrowDown') {
      if (contentData?.seasons_count == 1) {
        setCurrentButtonFocus(`season${0 + 1}`)
      }
      if (lastFocusedSeasonIndex) {
        const lastFocusedSeasonButton =
          seasonRef?.current[lastFocusedSeasonIndex - 1]
        if (lastFocusedSeasonButton) {
          lastFocusedSeasonButton.focus()
        }
      }
    } else if (event.keyCode === 38) {
      upBackKeyFocus.current = true
      handleBackandUp(event)
    }
  }

  const handleBackInfoTab = event => {
    dispatch(setShowTalentModule(false))
    dispatch(setShowApiFailureModal(false))
    setDisablePlayBtn(false)
    setMoreInfoTab(false)
    setRecomendationTab(false)
    setSeasonsFocusRed(false)
    setMoreinfoFocusRed(false)
    setRecomendFocusRed(false)
    setShowvodseriesdetail(false)
    setVideoPlayerWarning(false)
    setSeasonTab(true)
  }

  useEffect(() => {
    // If vodSeriesDataRedux or seasons is empty/undefined, set empty array and return
    if (!vodSeriesDataRedux?.seasons?.length) {
      setEpisodesIds([]);
      return;
    }
    // Transform seasons data into array of episode IDs in one chain
    const episodeIds = vodSeriesDataRedux.seasons
      .flatMap(season => season.episodes) // Flatten all episodes from all seasons
      .map(episode => episode.id);        // Extract just the IDs
    setEpisodesIds(episodeIds);
  }, [showvodseriesdetail, vodSeriesDataRedux]);

  useEffect(() => {
    continueWatchListResponse?.length > 0 &&
      continueWatchListResponse
        ?.filter(e => e?.id == (seriesData?.id ?? seriesData?.group_id))
        ?.map(progressData => {
          setProgressBarResponse(progressData)
        })
  }, [continueWatchListResponse, seriesData])

  useEffect(() => {
    if (episodesIds?.length > 0) {
      dispatch(
        getProgressbarBookmark({
          user_id: userId,
          group_id: episodesIds,
          user_hash: userDetails?.session_userhash,
          filterlist: filterlist,
          lasttouch: userDetails?.lasttouch?.seen
        })
      )
    }
  }, [episodesIds, userDetails])

  useEffect(() => {
    watchFree && setShowvodseriesdetail(false)
  }, [watchFree])

  const handleBackandUp = event => {
    setIsLeavingCurrentState(true)
    setInitialFocusDone(false)
    setIsFocusFrozen(false)
    handleBackInfoTab(event)
  }

  const keySeriesPressFunc = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      var codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
    }
    if (
      document?.getElementById('seriessecondpage') ||
      event.target.id == 'secondSeasonTab'
    ) {
      if (
        event.keyCode === 10009 ||
        event.keyCode === 461 ||
        event?.keyCode === 8
      ) {
        isbackMaintainFocus.current = true
        handleBackandUp(event)
        //  setPlayButtonClicked('')
        episodeItemClicked = false
      }
    }
    if (document.getElementById('background-image-page')) {
      if (
        event.keyCode === 10009 ||
        event.keyCode === 461 ||
        event?.keyCode === 8
      ) {
        if (state?.returnPage === 'search') {
          state?.talentData &&
            dispatch(
              getTalentSearch({
                data: state?.talentData,
                name: state?.talentName
              })
            )
          navigate('/search', {
            state: { inputValue: state?.inputValue },
            replace: true
          })
        } else {
         localStorage.setItem('currNavIdx',navbarTab == 'homeuser' ? homeIndex : navbarTab == 'miscontenidos'? mycontentIndex : seriesIndex)
         navigate('/home', {
            state: {
              id:
                cardRef.current + seriesData?.id ??
                cardRef.current + seriesData?.group_id,
              backfocusid: state?.backfocusid,
              fromPage: state?.fromPage
            }
          })
        }
      } else if (event.keyCode == 38) {
        if (!upBackKeyFocus.current) {
          document.getElementById(`nav-${navIndex}`)?.focus()
        } else {
          upBackKeyFocus.current = false
          handleMainFocus()
        }
      }
    }
    if (
      document.getElementById('video-Player-Warning') &&
      (event.keyCode === 10009 ||
        event.keyCode === 461 ||
        event.keyCode === 405 ||
        event?.keyCode === 8 ||
        codes?.yellowcode === keycode)
    ) {
      handleBackInfoTab()
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keySeriesPressFunc)

    return () => {
      document.removeEventListener('keyup', keySeriesPressFunc)
    }
  }, [keySeriesPressFunc])

  const handledescription = (descript, maxchar) => {
    let descriptionText = descript

    if (maxchar === 147) {
      if (descriptionText.length > maxchar) {
        descriptionText = descriptionText.substring(0, maxchar) + '...'
      }
    } else if (maxchar === 53) {
      if (descriptionText.length > maxchar) {
        descriptionText = descriptionText.substring(0, maxchar) + '...'
      }
    }
    return descriptionText
  }

  const handleMainTitle = tle => {
    let largecontentTitle = tle || ''
    const maxCharacters = 58 // Total character limit for two lines
    // Check if content length exceeds the limit
    if (largecontentTitle.length > maxCharacters) {
      // Truncate content to fit within two lines
      const truncatedText = largecontentTitle.substring(0, maxCharacters)
      // Find the last space within the truncated text to avoid cutting off words
      const lastSpaceIndex = truncatedText.lastIndexOf(' ')
      // Update the truncated content
      largecontentTitle = truncatedText.substring(0, lastSpaceIndex) + '...'
    }
    return largecontentTitle
  }

  const handleoriginalTitle = tle => {
    let largecontentTitle = tle || ''
    const maxCharacters = 93 // Total character limit for two lines
    // Check if content length exceeds the limit
    if (largecontentTitle.length > maxCharacters) {
      // Truncate content to fit within two lines
      const truncatedText = largecontentTitle.substring(0, maxCharacters)
      // Find the last space within the truncated text to avoid cutting off words
      const lastSpaceIndex = truncatedText.lastIndexOf(' ')
      // Update the truncated content
      largecontentTitle = truncatedText.substring(0, lastSpaceIndex) + '...'
    }
    return largecontentTitle
  }

  const handlegenrechar = genrechar => {
    let GenrechartTitle = genrechar || ''
    // const maxCharacters = 27 // Total character limit for two lines
    const parts = GenrechartTitle.split(',') // Split the string by commas
    const result = parts.map((part, index) =>
      index < parts.length - 1 ? part.trim() + ', ' : part.trim()
    ) // Add space after each part (except the last one)
    let resultchar = result.join('')
    // if (resultchar.length > maxCharacters) { commented for future development
    //   // Truncate content to fit within two lines
    //   const truncatedText = resultchar.substring(0, maxCharacters)
    //   // Update the truncated content
    //   resultchar = truncatedText + '...'
    // }
    return resultchar
  }

  const handleEpisodemaintitle = epistitle => {
    let epistitletTitle = epistitle || ''
    const maxCharacters = 82 // Total character limit for two lines
    if (epistitletTitle.length > maxCharacters) {
      const truncatedText = epistitletTitle.substring(0, maxCharacters)
      epistitletTitle = truncatedText + '...'
    }
    return epistitletTitle
  }

  const handleMainDescript = maindesc => {
    const description = document.querySelector('.seriesvod-description')
    const layout = document.querySelector('.seriesvod-description-layouts')

    if (description && layout) {
      layout.style.height = `${description.scrollHeight}px`
    }

    let maindescriptTitle = maindesc || ''
    const maxCharacters = 262 // Total character limit for two lines
    if (maindescriptTitle.length > maxCharacters) {
      const truncatedText = maindescriptTitle.substring(0, maxCharacters)
      maindescriptTitle = truncatedText + '...'
    }
    return maindescriptTitle
  }

  const getPromotitle = data => {
    return apilanguage?.[`transactional_${data}_subscription_vcard_description`] //checking the data is there in apa/assets
  }

  const getPromoFamily = data => {
    return apilanguage?.[`${data}`]
  }

  const getPromoFamilySlash = (producttype,periodicity) => {
    return apilanguage?.[`subscription_${producttype}_periodicitySeparator_${periodicity}_label`] 
  }

  const getPromolabel = data => {
    return apilanguage[`${data}_access_commercialInfo_label`]
  }

  const settingLogoUrl = data => {
    return apaAssetsImages?.[
      `Transaccionales_Suscripcion_ImagenLogoCanal_${data}`
    ] //checking the data is there in apa/assets
  }

  const getTaxLabel = data => {
    return apilanguage?.[
      `${data}_subscriptionDescription_costTaxIncluded_label`
    ]
  }

  const getFreeChargeString = data => {
    return apilanguage?.[
      `transactional_${data}_subscription_plan_tryInvite_description${data}`
    ]
  }

  const getSubscribeButton = data => {
    return apilanguage?.['buy_' + data]
  }

  const getViewDetailsButton = data => {
    return apilanguage?.['includes_' + data]
  }

  const handleViewDetailSubscription = (
    e,
    item,
    subcriptionType,
    index = 0
  ) => {
    // if (
    //   watchFree &&
    //   vodSeriesCastRedux?.common?.extendedcommon?.format?.types !==
    //     'free,download' &&
    //   vodSeriesCastRedux?.common?.extendedcommon?.format?.types !== 'free'
    // ) {
    //   navigate('/EPconfirmation', {
    //     state: { seriesEpisodeData: vodSeriesCastRedux?.common, page: 'series' }
    //   })
    // } else {

    //Commenting this line for timebeing. Checked for various scenarios as well. 
    //Will be removed after sometime.
    e.preventDefault()
    let monthData
    monthData = item?.[index]
    const viewPlan = {
      logo: settingLogoUrl(monthData?.family),
      workflowStart: monthData?.linkworkflowstart,
      verticalImage: vodSeriesDataRedux?.image_medium,
      title: vodSeriesDataRedux?.title,
      family: monthData?.family,
      periodicity: monthData?.periodicity,
      price: monthData?.price,
      currency: monthData?.currency,
      styles: monthData?.style,
      taxLabel: getTaxLabel(monthData?.family),
      infoString: getFreeChargeString(monthData?.bonus),
      subscribeButton: getSubscribeButton(monthData?.family),
      viewButton: getViewDetailsButton(monthData?.family),
      producttype: monthData?.producttype,
      offertype: monthData?.oneoffertype,
      isSeries: true
    }
    dispatch(getViewSubscribeData(viewPlan))
    subcriptionType === 'longSubscription'
      ? navigate(
          '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
          {
            state: {
              pageName: '/series',
              dataId: vodSeriesCastRedux?.common,
              data: state?.data ?? state?.dataId
            }
          }
        )
      : watchFree
      ? navigate('/EPconfirmation', {
          state: {
            seriesEpisodeData: vodSeriesCastRedux?.common,
            page: 'series',
            pageName: '/series',
            gaContentData: contentDataRef.current
          }
        })
      : (dispatch(
          getFinishSubscriptionPlayerData({
            data: vodSeriesCastRedux?.common,
            contentDataplayer: contentData,
            episodeData: seasonData?.episodes,
            inputValue: state?.inputValue
          })
        ),
        navigate(
          '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          {
            state: {
              pageName: '/series',
              dataId: vodSeriesCastRedux?.common,
              data: seriesData ?? state?.data ?? state?.dataId
            }
          }
        ))
    // }
  }

  const handleMoreInfoButtonFocus = indx => {
    handleSeasonsTab(indx)
  }

  useEffect(() => {
    dispatch(clearGetMediaRes())
    let accountInfo = loginInfo || registerInfo || userDetails
    subscriptionKeyRef.current =
      accountInfo?.paywayProfile?.subscriptions?.reduce(
        (resultant, value, index) =>
          index == 0 ? resultant + value?.key : resultant + ', ' + value?.key,
        ''
      )
    setStreamType(
       !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
        ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
        : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
    )
    setDisablePlayBtn(false)
    SpatialNavigation.focus()
    if (document.getElementById('viewDetail')) {
      setViewDetailButton('month')
    }
    if (playerInfo == 1) {
      if(state?.vCardBackFocus === 'trailer'){
        setCurrentButtonFocus('trailer')
        document.getElementById('trailerButton')?.focus()
        setTrailerBtnClkFocus(true)
        setPlayBtnClkFocus(false)
      }else{
        setPlayBtnClkFocus(
          (playerInfo == 1 ||
            state?.vCardBackFocus === 'play') ||
            playBtnClkFocus
        )
        setCurrentButtonFocus('play')
        !playBtnClkFocus &&
          setTrailerBtnClkFocus(
            (state?.vCardBackFocus !== 'play' &&
              state?.vCardBackFocus === 'trailer') ||
              trailerBtnClkFocus
          )
      }
    }else if(playerInfo == 0 && state?.vCardBackFocus === 'trailer'){
      document.getElementById('trailerButton')?.focus()
      setTrailerBtnClkFocus(true)
      setTrailerBtnClkFocus(
        true
    )
    } else if(state?.vCardBackFocus === 'trailer'){
        setPlayBtnClkFocus(false)
      setCurrentButtonFocus('trailer')
      setTrailerBtnClkFocus(
        true
    )
      document.getElementById('trailerButton')?.focus()
    }  else if(state?.vCardBackFocus === 'play'){
      setPlayBtnClkFocus(true)
    setCurrentButtonFocus('play')
    document.getElementById('play')?.focus()
  } 
    
    return () => {
      setMltData([])
      setCastData([])
      setContentData('')
      setPlayerInfo('')
      setIsFocusFrozen(false)
      setCurrentButtonFocus('')
      setSeasonData('')
      setproductType('')
      setPeriodicity('')
      dispatch(clearVodSeries())
      dispatch(clearVodSeriesCast())
      dispatch(clearVodSeriesMlt())
      dispatch(removeTrailerPlayerData())
      dispatch(clearTrailerPlayerError())
      dispatch(getTalentSearchData())
      dispatch(setShowTalentModule(false))
      setShowvodseriesdetail(false)
      dispatch(clrSubscriptionInfo())
      contentDataRef.current = null
    }
  }, [])

  useEffect(() => {
    subscriptionInfo?.listButtons &&
      subscriptionInfo?.listButtons?.button?.map(each => {
        each?.periodicity === 'month' || each?.periodicity === 'year'
          ? (setViewDetailButton(true),
            setPromoLabel(each?.bonus),
            setFamilyPromoLabel(each?.family),
            setproductType(each?.producttype),
          setPeriodicity(each?.periodicity),
            setPriceMonth(each?.price),
            setSymbolCurrency(each?.currency))
          : ''
      })
  }, [subscriptionInfo?.listButtons?.button])

  const targetEpisodeNumber = moreEpisodeClick?.episodeItem?.episode_number
  const episodeIndexToFocus = moreEpisodeClick?.seasonData?.findIndex(
    episode => episode?.episode_number === targetEpisodeNumber
  )

  useEffect(() => {
    if (moreInfoGetvalue || episodeItemClicked) {
      setShowvodseriesdetail(true)
      const targetSeasonNumber = moreEpisodeClick?.episodeItem?.season_number
      const seasonIndexToFocus =
        moreEpisodeClick?.contentDataFromMoreInfo?.seasons?.findIndex(
          season => season?.number === targetSeasonNumber
        )
      if (episodeIndexToFocus >= 0 && seasonIndexToFocus >= 0) {
        handleSeasons(
          moreEpisodeClick?.contentDataFromMoreInfo?.seasons[
            seasonIndexToFocus
          ],
          episodeIndexToFocus
        )
        setLastFocusedSeasonIndex(seasonIndexToFocus)
        setCurrentButtonFocus(`season${seasonIndexToFocus + 1}`)
      }
      handleMoreInfoButtonFocus(episodeIndexToFocus)
    }
  }, [moreInfoGetvalue, episodeItemClicked])

  useEffect(() => {
    if (isFocusFrozen) {
      const targetEpisodeNumber =
        vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.number
      const episodeIndexToFocus = seasonData?.episodes?.findIndex(
        episode => episode?.episode_number === targetEpisodeNumber
      )

      const targetSeasonNumber =
        vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.season
      const seasonIndexToFocus = contentData?.seasons?.findIndex(
        season => season?.number === targetSeasonNumber
      )

      if (episodeIndexToFocus >= 0 && seasonIndexToFocus >= 0) {
        handleSeasons(
          contentData?.seasons[seasonIndexToFocus],
          episodeIndexToFocus
        )
        setLastFocusedSeasonIndex(seasonIndexToFocus)
        setCurrentButtonFocus(`season${seasonIndexToFocus + 1}`)
        // Apply initial focus
        handleEpisodeFocus(episodeIndexToFocus, true)
      }
    }
  }, [isFocusFrozen])

  const handleEpisodeFocus = (index, isInitialFocus = false) => {
    episodeItemClicked &&
      setCurrentButtonFocus(`season${moreEpisodeClick?.lastSeasonIndex + 1}`)
    setUpkeyFocus(false)
    const targetElement = episodeRef.current[index]
    if (targetElement) {
      if (isInitialFocus && !initialFocusDone) {
        setInitialFocusDone(true)
      }
      targetElement.focus()
    }
  }

  const handleKeyDown = event => {
    const isMoreInfoSeasonFocused = moreInfoSeasonRefs?.current?.some(
      button => document.activeElement === button
    )
    const targetSeason = contentData?.seasons?.[0]?.episodes?.[0]?.season_number
    const seasonLeftToFocus = contentData?.seasons?.findIndex(
      season => season?.number === targetSeason
    )
    if (event.key === 'ArrowLeft') {
      dispatch(getEpisodeVariable(false))
      if (contentData?.seasons_count == 1) {
        event.preventDefault()
      } else {
        if (isMoreInfoSeasonFocused) {
          event.preventDefault()
        } else if (
          lastFocusedSeasonIndex !== null ||
          seasonLeftToFocus !== null
        ) {
          const lastFocusedSeasonButton =
            seasonRef?.current[lastFocusedSeasonIndex ?? seasonLeftToFocus]
          if (lastFocusedSeasonButton) {
            lastFocusedSeasonButton.focus()
          }
        }
      }
    }
    if (event.key === 'ArrowDown') {
      setInitialFocusDone(false)
      dispatch(getEpisodeVariable(false))
    }
    if (event.key === 'ArrowUp') {
      setInitialFocusDone(false)
      dispatch(getEpisodeVariable(false))
    }
  }

  const handleSecondSeasonFocus = () => {
    setUpkeyFocus(true)
    setCurrentButtonFocus('seasons')
    if (
      seriesSeasonsData?.seasons?.length == 1 ||
      vodSeriesDataRedux?.seasons?.length == 1
    ) {
      setGetFocusedElementId(true)
      setCurrentButtonFocus(`season${0 + 1}`)
    }
  }

  const handlePlayBlur = () => {
    setTrailerBtnClkFocus(false)
    setCurrentButtonFocus('')
  }

  const handleScrollUp = () => {
    const element = document.querySelector('.moreInfo-description-detail')
    if (element) {
      element.scrollTop -= 10
      if (element.scrollTop == 0) {
        if (element.scrollTop == 0 && currentButtonFocus == 'moreinfo') {
          if (!upBackKeyFocus.current) {
            document.getElementById('nav-2').focus()
          } else {
            upBackKeyFocus.current = false
            handleMainFocus()
          }
        } else if (
          element.scrollTop == 0 &&
          currentButtonFocus == 'recomendation'
        ) {
          if (!upBackKeyFocus.current) {
            document.getElementById('nav-0').focus()
          } else {
            upBackKeyFocus.current = false
            handleMainFocus()
          }
        } else if (element.scrollTop == 0 && currentButtonFocus == 'seasons') {
          if (!upBackKeyFocus.current) {
            document.getElementById('nav-0').focus()
          } else {
            upBackKeyFocus.current = false
            handleMainFocus()
          }
        }
      }
    }
  }

  const handleScrollDown = () => {
    const element = document.querySelector('.moreInfo-description-detail')
    if (element) {
      element.scrollTop += 10
      const maxScroll = element.scrollHeight - element.clientHeight
      if (element.scrollTop == maxScroll) {
        if (
          currentButtonFocus == 'moreinfo' ||
          currentButtonFocus == 'recomendation' ||
          currentButtonFocus == 'seasons'
        ) {
          const castElement = document.getElementById('castId0')
          if (castElement) castElement.focus()
        }
      }
    }
  }

  const handleScrollingEpisodes = event => {
    const element = document.querySelector('.moreInfo-description-detail')
    if (element && event.key === 'ArrowDown') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollDown(event)
    } else if (element && event.key === 'ArrowUp') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollUp(event)
    } else {
      handleFocusredSeason(event)
    }
  }

  const handleScrollingRecommendation = event => {
    const element = document.querySelector('.moreInfo-description-detail')
    if (element && event.key === 'ArrowDown') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollDown(event)
    } else if (element && event.key === 'ArrowUp') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollUp(event)
    } else {
      handleFocusredRecom(event)
    }
  }

  const handleScrollingMoreInfo = event => {
    const element = document.querySelector('.moreInfo-description-detail')
    if (element && event.key === 'ArrowDown') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollDown(event)
    } else if (element && event.key === 'ArrowUp') {
      event.preventDefault()
      event.stopPropagation()
      handleScrollUp(event)
    } else {
      handleFocusredMoreInfo(event)
    }
  }

  useEffect(() => {
    const checkElement = () => {
      const element = descriptionRef.current

      if (element) {
        const lineHeight = parseInt(
          window.getComputedStyle(element).lineHeight,
          10
        )
        const height = element.scrollHeight
        const numberOfLines = height / lineHeight
        if (numberOfLines > 9) {
          element.classList.add('faded')
        } else {
          element.classList.remove('faded')
        }
      }
    }
    const timeoutId = setTimeout(checkElement, 100)
    return () => clearTimeout(timeoutId)
  }, [moreInfoTab, currentButtonFocus])

  useEffect(() => {
    if (state?.seasonTabStatus) {
      setUpkeyFocus(true)
      const getSeasonIndex =
        vodSeriesDataRedux &&
        vodSeriesDataRedux?.seasons.findIndex(
          itrObj => itrObj.number == state?.currentPlayingSeasonData?.season
        )
      const getEpisodeData =
        vodSeriesDataRedux &&
        vodSeriesDataRedux?.seasons.find(
          itrObj => itrObj.number == state?.currentPlayingSeasonData?.season
        )
      getEpisodeData && setSeasonData(getEpisodeData)
    }
  }, [state, vodSeriesDataRedux])

  return !data ||
    (continueWatchListResponse?.length > 0
      ? !continueWatchListResponse
      : false) ||
    !vodSeriesDataRedux ||
    !playerInfo ||
    !vodSeriesCastRedux ||
    !vodSeriesCastRedux?.common ||
    (vodSeriesMLTRedux ? !vodSeriesMLTRedux : false) ||
    (progressbarContentData?.length > 0 ? !progressbarContentData : false) ||
    !data ||
    (subscriptionInfo ? !subscriptionInfo : false) ||
    !contentData ||
    !castData ||
    (subscriptionInfo?.listButtons?.button?.length > 0
      ? !subscriptionInfo?.listButtons
      : false) ? (
    <>
      <Navbar page={'voddetail'} disableFocus={true} myContentData={true} />
      <div className="vod-series-loader">
        <Lottie
          options={{
            rendererSettings: {
              preserveAspectRatio: 'xMidYMid slice'
            }
          }}
          loop
          animationData={animationData}
          play
        />
      </div>
    </>
  ) : (
    <div>
      {!videoplayerWarning && clickedPlaybackId && (
        <Lottie
          options={{
            rendererSettings: {
              preserveAspectRatio: 'xMidYMid slice'
            }
          }}
          loop
          animationData={animationData}
          play
          className="player-loader"
        />
      )}
      {videoplayerWarning && !vodSimultaneous?.errorType ? (
        <div id="video-Player-Warning">
          <PlayerModelSelection
            handleBackInfoTab={handleBackInfoTab}
            handleIconClick={handleClick}
            keyParam={'series'}
            id={'videoPlayerWarning'}
            gaContentData={contentDataRef.current}
          />
        </div>
      ) : vodSimultaneous?.errorType ? (
        <PlayerErrorHandler
          retry={setPlaybackRetry}
          closePopup={setVodSimultaneous}
          data={vodSimultaneous}
        />
      ) : showTalentModule ? (
        <TalentSearch
          filterlist={filterlist}
          talentSearchData={talentSearchData}
          talentSearchfield={talentSearchfield}
          Addproveedor={addProveedor}
          setCurrentButtonFocus={setCurrentButtonFocus}
          userDeatilResp={userDetails}
          currentButtonFocus={currentButtonFocus}
          keyParam={'vodSeries'}
        />
      ) : showApiFailureModal ? (
        <ErrorEventModule
          handlebackButton={handleBackInfoTab}
          pageName={'vodSeries'}
          setCurrentButtonFocus={setCurrentButtonFocus}
        />
      ) : (
        <>
          <Navbar
            page={'voddetail'}
            disableFocus={false}
            myContentData={true}
          />
          <div
            className="series-Main-Layout"
            style={{
              opacity: !videoplayerWarning && clickedPlaybackId ? '0.5' : 1
            }}
          >
            {!showvodseriesdetail ? (
              <>
                <div
                  className="back-ground-images"
                  id="background-image-page"
                  style={{
                    backgroundImage: `url(${imageUrl}),
                    linear-gradient(180deg, rgba(18,18,18,0) 0%, rgba(18,18,18,0.08) 58.3%, rgba(18,18,18,0.7) 100%), linear-gradient(270deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0) 0.08%, rgba(18,18,18,0.8) 35.98%, rgba(18,18,18,0.9) 45.73%, #121212 55.25%, #000000 100%)`
                  }}
                >
                  <div
                    style={{
                      top: playerInfo == 0 && viewDetailButton ? '-41px' : '',
                      paddingTop:
                        vodSeriesCastRedux?.common?.extendedcommon?.media?.serie
                          ?.title?.length > 28 &&
                        vodSeriesCastRedux?.common?.extendedcommon?.media
                          ?.originaltitle?.length > 27
                          ? '30px'
                          : '',
                      position: 'relative'
                    }}
                  >
                    <div
                      className="series-layout"
                      id="series-layoutt"
                      style={{
                        top: SeriesMaintop,
                        height: seriesMainHeight,
                        maxWidth: seriesMainMaxWidth,
                        justifyContent: seriesJustifyContent
                      }}
                    >
                      <div
                        className="content-seriestitle"
                        style={contentSeriesTitleStyles}
                      >
                        {handleMainTitle(
                          vodSeriesCastRedux?.common?.extendedcommon?.media
                            ?.serie?.title
                        )}
                      </div>
                      {vodSeriesCastRedux?.common?.extendedcommon?.media
                        ?.originaltitle?.length <= 27 ? (
                        <div
                          className="vod-season-seriesdetails"
                          style={{ top: calculateVODTop(vodSeriesCastRedux) }}
                        >
                          <span className="seriescontent-name">
                            {
                              vodSeriesCastRedux?.common?.extendedcommon?.media
                                ?.originaltitle
                            }
                          </span>
                          {vodSeriesCastRedux?.common?.extendedcommon?.media
                            ?.originaltitle?.length > 35 ? (
                            <br />
                          ) : (
                            <span className="pipe">|</span>
                          )}
                          <span className="seriesvod-genero">
                            {handlegenrechar(genreData?.toString())}
                          </span>{' '}
                          <span className="seriesvod-duration-year">
                            {
                              vodSeriesCastRedux?.common?.extendedcommon?.media
                                ?.publishyear
                            }
                          </span>
                          <span className="seriesstroke seriesvod-duration-ano">
                            {handleAgerating(age_rating)}
                          </span>
                          <span className="seriesduration-ano-copy ">
                            {vodDuration}
                          </span>
                        </div>
                      ) : (
                        <div
                          className="largevod-season-seriesdetails"
                          style={{
                            top: calculateLargeVODTop(vodSeriesCastRedux),
                            position:
                              calculateLargeVODPosition(vodSeriesCastRedux)
                          }}
                        >
                          <div className="largeseriescontent-name">
                            {handleoriginalTitle(
                              vodSeriesCastRedux?.common?.extendedcommon?.media
                                ?.originaltitle
                            )}
                          </div>
                          <div className="largedetails">
                            <span className="largeseriesvod-genero">
                              {handlegenrechar(genreData?.toString())}
                            </span>
                            <span className="largeseriesvod-duration-year">
                              {
                                vodSeriesCastRedux?.common?.extendedcommon
                                  ?.media?.publishyear
                              }
                            </span>
                            <button className="largeseriesstroke">
                              <span className="largeseriesvod-duration-ano">
                                {handleAgerating(age_rating)}
                              </span>
                            </button>
                            <span className="largeseriesduration-ano-copy ">
                              {vodDuration}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="seriesvod-description-layouts">
                      <span className="EpisodeDetails">
                        {handleTranslationchange(
                          'vcard_access_abbreviationSeason_label'
                        )}{' '}
                        {
                          vodSeriesCastRedux?.common?.extendedcommon?.media
                            ?.episode?.season
                        }{' '}
                        |{' '}
                        {apilanguage?.vcard_access_abbreviationEpisode_label?.slice(
                          0,
                          8
                        )}{' '}
                        {
                          vodSeriesCastRedux?.common?.extendedcommon?.media
                            ?.episode?.number
                        }
                        :{' '}
                        <span className="episode-title">
                          {handleEpisodemaintitle(
                            vodSeriesCastRedux?.common?.title
                          )}
                        </span>
                      </span>

                      <div className="seriesvod-description">
                        {handleMainDescript(
                          vodSeriesCastRedux?.common?.description
                        )}
                      </div>
                    </div>
                    <div
                      className="seriesltv-progressbar-position"
                      style={{
                        top:
                          getLastSeenData?.episode?.vistime?.last?.progress >
                            0 ||
                          progressBarResponse?.vistime?.last?.progress > 0
                            ? '140px'
                            : '',
                        visibility: document.getElementById('promoCardLabel')
                          ? 'hidden'
                          : progressBarResponse?.vistime?.last?.progress > 0 ||
                            getLastSeenData?.episode?.vistime?.last?.progress >
                              0
                          ? ''
                          : 'hidden'
                      }}
                    >
                      {lastSeenPercentage > 0 &&
                        userId &&
                        lastSeenPercentage !== 100 &&
                        subscriptionInfo?.playButton?.visible == 1 && (
                          <ProgressBar
                            style={{ visibility: 'visible' }}
                            isLoading={false}
                            percent={lastSeenPercentage}
                            size={'large'}
                            showInfo={true}
                            serieslargeProgress={serieslargeProgress}
                            sliderWidth={1045}
                          />
                        )}
                    </div>
                  </div>
                  {viewDetailButton &&
                    subscriptionInfo?.listButtons?.button?.length > 0 &&
                    playerInfo == 0 && (
                      <div className="promo-card" id="promoCardLabel">
                        <span className="promo-first-title">{`${getPromolabel(
                          familyPromoLabel
                        )}${' '}${symbolCurrency}${priceMonth}${
                           priceMonth ? `${getPromoFamilySlash(producttype,periodicity)}${apilanguage?.[`${periodicity}`]}` : ''
                        }`}</span>
                        <span className="promo-second-title">
                          {' '}
                          {!getPromoFamily(promoLabel)
                            ? null
                            : `${getPromotitle(
                                promoLabel
                              )}${' '}${getPromoFamily(promoLabel)}`}
                        </span>
                      </div>
                    )}

                  <div
                    className="button-layout"
                    id="buttonlayoutid"
                    style={{
                      top:
                        getLastSeenData?.episode?.vistime?.last?.progress > 0 ||
                        progressBarResponse?.vistime?.last?.progress > 0
                          ? '153px'
                          : '105px',
                      height: userId ? '' : '104px'
                    }}
                  >
                    {playerInfo == 1 ? (
                      <button
                        className={`button-icons focusable ${
                          playBtnClkFocus ? 'active' : ''
                        } `}
                        id="playerbutton"
                        autoFocus={
                          state?.vCardBackFocus == 'play' ||
                          (playerInfo == 1 &&
                            state?.vCardBackFocus != 'trailer')
                        }
                        onFocus={() => {
                          setCurrentButtonFocus('play')
                        }}
                        onBlur={handlePlayBlur}
                        data-testid="playbutton"
                        onClick={() => {
                          handlePlayBtn(data)
                        }}
                        disabled={disablePlayBtn}
                        onKeyDown={e => handlePlayKeyDown(e)}
                      >
                        {playBtnClkFocus ? 
                        <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/play-reproducera.png'}
                          />
                        </div>
                        : 
                        <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/play_button_white.png'}
                          />
                        </div>}
                        <p
                          className={
                            playBtnClkFocus || currentButtonFocus === 'play'
                              ? 'button-text'
                              : 'button-textfocus-out'
                          }
                        >
                          {handleTranslationchange(
                            'vcard_access_option_button_play'
                          )}
                        </p>
                      </button>
                    ) : (
                      playerInfo == 0 &&
                      subscriptionInfo?.listButtons?.button?.length > 0 && (
                        <div className="total-wrapper">
                          {viewDetailButton && (
                            <button
                              id="viewDetail"
                              autoFocus={true}
                              className="large-subscibre focusable"
                              onFocus={() =>
                                setCurrentButtonFocus('large-subscribe')
                              }
                              onClick={e =>{
                                handleViewDetailSubscription(
                                  e,
                                  subscriptionInfo?.listButtons?.button,
                                  'longSubscription',
                                  subscriptionInfo?.listButtons?.button
                                    ?.length - 1
                                ),
                                handleGAAddSubscriptionEvent(interactionType.VER_CON_CLARO_VIDEO_INTERACTION_TYPE)
                                 
                              }}
                            >
                              <div className="whole-larger-text">
                                <span className={'large-subscribe-details-out'}>
                                 {`${truncateText( apilanguage?.[`${familyPromoLabel}_access_title_button_subscriptionDescription`] ?? apilanguage?.[`${familyPromoLabel?.toUpperCase()}_access_title_button_subscriptionDescription`] ?? '_access_title_button_subscriptionDescription',50)}` }
                                </span>
                                <span
                                  className={
                                    'large-second-subscribe-details-out'
                                  }
                                >
                                 {`${truncateText(apilanguage?.[`${familyPromoLabel}_access_description_button_subscriptionDescription`] ?? apilanguage?.[`${familyPromoLabel?.toUpperCase()}_access_description_button_subscriptionDescription`] ?? '_access_description_button_subscriptionDescription',30)}` }
                                </span>
                              </div>
                            </button>
                          )}
                          {playerInfo == 0 &&
                            subscriptionInfo?.listButtons &&
                            subscriptionInfo?.listButtons?.button?.map(
                              (eachButton, index) => (
                                <button
                                  key={index}
                                  autoFocus={viewDetailButton ? false : true}
                                  id="subscription-button"
                                  className="subscibr-month focusable"
                                  onFocus={() =>
                                    setCurrentButtonFocus('subscribe-button')
                                  }
                                  onClick={e =>{
                                    handleViewDetailSubscription(
                                      e,
                                      subscriptionInfo?.listButtons?.button,
                                      'shortSubscription',
                                      index
                                    ),
                                    handleGAAddSubscriptionEvent('subscriptionbuttons',eachButton)
                                   }}
                                >
                                  <div
                                    className="whole-text"
                                    style={{
                                      backgroundColor:
                                        handleColourButton(eachButton)
                                    }}
                                  >
                                    <span
                                      className={
                                        currentButtonFocus ===
                                        'subscribe-button'
                                          ? 'price-details'
                                          : 'price-details-out'
                                      }
                                    >
                                      {eachButton?.currency}
                                      {eachButton?.price}
                                    </span>
                                    <span
                                      className={
                                        currentButtonFocus ===
                                        'subscribe-button'
                                          ? 'includio-text'
                                          : 'includio-text-out'
                                      }
                                    >
                                      {eachButton?.price
                                        ? getTaxLabel(eachButton?.producttype)
                                        : ''}
                                    </span>
                                  </div>
                                  <p
                                    className={
                                      currentButtonFocus === 'subscribe-button'
                                        ? 'button-subscribe-text'
                                        : 'button-subscribe-textfocus-out'
                                    }
                                  >
                                     {truncateText(
                                      subscriptionInfo?.listButtons?.button?.[index]?.oneofferdesckey,45) === "Comprar Episodio"
                                      ? `${truncateText(subscriptionInfo?.listButtons?.button?.[index]?.oneofferdesckey,45)}
                                       ${vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.number}`
                                      : truncateText(subscriptionInfo?.listButtons?.button?.[index]?.oneofferdesckey,45)
                                    }
                                  </p>
                                </button>
                              )
                            )}
                        </div>
                      )
                    )}
                    {trailerPreviewVisible === 'true' && (
                      <button
                        id="trailerButton"
                        className={`button-icons focusable ${
                          trailerBtnClkFocus ? 'active' : ''
                        } `}
                        autoFocus={
                          state?.vCardBackFocus == 'trailer' ||
                          (trailerPreviewVisible &&
                            playerInfo == 0 &&
                            !subscriptionInfo?.listButtons)
                        }
                        onClick={() => {
                          handletrailer()
                          // GA: Interaction Content Event
                          handleGAInteractionEvent(interactionType.TRAILER_INTERACTION_TYPE)
                        }}
                        onFocus={e => {
                          setCurrentButtonFocus('trailer')
                        }}
                        onBlur={() => {
                          setCurrentButtonFocus('')
                        }}
                        data-testid="seriestrailerbutton"
                      >
                        {!trailerBtnClkFocus ?
                          <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/trailer.png'}
                          />
                           </div>
                         :   <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/trailer_black.png'}
                          />
                          </div>
                        }
                        <p
                          className={
                            trailerBtnClkFocus ||
                            currentButtonFocus === 'trailer'
                              ? 'button-text'
                              : 'button-textfocus-out'
                          }
                        >
                          {handleTranslationchange(
                            'vcard_access_option_button_trailer'
                          )}
                        </p>
                      </button>
                    )}
                    {watchList ? (
                      <button
                        autoFocus={
                          playerInfo == 0 && !trailerPreviewVisible
                            ? true
                            : false
                        }
                        onClick={() => {
                          handleWatchListDel(), handleWatchlist(),
                          handleGAInteractionEvent(interactionType.REMOVE_LIST_INTERACTION_TYPE)
                        }}
                        id="deleteButton"
                        data-testid="delWatchListTest"
                        data-sn-right={true}
                        className={
                          userId &&
                          (vodDetails?.confirmscreen
                            ? loginInfo ?? registerInfo
                            : userDetails)
                            ? `button-icons focusable ${
                                watchlistButtonClickFocus ? 'active' : ''
                              }`
                            : ''
                        }
                        style={{
                          visibility:
                            userId &&
                            (vodDetails?.confirmscreen
                              ? loginInfo ?? registerInfo
                              : userDetails)
                              ? ''
                              : 'hidden'
                        }}
                        onFocus={e => {
                          setCurrentButtonFocus('watchlist')
                        }}
                        onBlur={() => {
                          setCurrentButtonFocus('')
                        }}
                        onKeyDown={e => handleWatchlistUnfocus(e)}
                      >
                        {!watchlistButtonClickFocus ? 
                         <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/Icons_Vcards_minus.png'}
                          />
                        </div>: 
                        <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/removeWatchlist_black.png'}
                          />
                          </div>
                        }
                        <p
                          className={
                            watchlistButtonClickFocus ||
                            currentButtonFocus === 'watchlist'
                              ? 'button-text'
                              : 'button-textfocus-out'
                          }
                        >
                          {handleTranslationchange(
                            'playing_playerControls_toolbar_button_deleteList'
                          )
                            .toLowerCase()
                            .replace(/^q/, 'Q')
                            .replace(/(\s)m/, ' M')}
                        </p>
                      </button>
                    ) : (
                      <button
                        onClick={() => {
                          handleWatchListAdd(), 
                          handleWatchlist(),
                          // GA: Interaction Content Event
                          handleGAInteractionEvent(interactionType.ADD_LIST_INTERACTION_TYPE)
                        }}
                        data-sn-right={true}
                        data-testid="addwatchlisttest"
                        id="deleteButton"
                        onFocus={e => {
                          setCurrentButtonFocus('addToWatchlist')
                        }}
                        ref={watchListRef}
                        className={
                          userId &&
                          (vodDetails?.confirmscreen
                            ? loginInfo ?? registerInfo
                            : userDetails)
                            ? `button-icons focusable ${
                                watchlistButtonClickFocus ? 'active' : ''
                              }`
                            : ''
                        }
                        style={{
                          visibility:
                            userId &&
                            (vodDetails?.confirmscreen
                              ? loginInfo ?? registerInfo
                              : userDetails)
                              ? ''
                              : 'hidden'
                        }}
                        onKeyDown={e => handleWatchlistUnfocus(e)}
                        onBlur={() => {
                          setCurrentButtonFocus('')
                        }}
                        autoFocus={
                          playerInfo == 0 && !trailerPreviewVisible
                            ? true
                            : false
                        }
                      >
                        {!watchlistButtonClickFocus ? 
                        <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/Icons_Vcards_Add.png'}
                          />
                        </div>:
                        <div className='seriesicon-container'>
                          <img
                            className="seriesicons-vcards"
                            src={'images/Vcard_Icons/addwatchlist_Black.png'}
                          />
                        </div>
                        }
                        <p
                          className={
                            watchlistButtonClickFocus ||
                            currentButtonFocus === 'addToWatchlist'
                              ? 'button-text'
                              : 'button-textfocus-out'
                          }
                        >
                          {handleTranslationchange(
                            'playing_playerControls_toolbar_button_addList'
                          )
                            .toLowerCase()
                            .replace(/^a/, 'A')
                            .replace(/(\s)m/, ' M')}
                        </p>
                      </button>
                    )}
                  </div>
                  {region === 'mexico' && subscriptionInfo?.listButtons &&  subscriptionInfo?.playButton?.visible != 1 &&
                    <div className='disclaimer-text-vcardseries'>
                      { subscriptionInfo?.listButtons?.button?.map((each) => (
                        each?.oneofferdesckey === 'offer_button_desc_subscription_cv_seasonbuy'
                        && translations?.language?.[region]?.['Vcard_Disclaimer_TextoDisclaimer']
                        ))
                      }
                    </div>
                  }             
                </div>
                <div className="tab-tray-serieswrapper" id="second-show-page">
                  <button
                    autoFocus={currentButtonFocus === 'seasons'}
                    onFocus={() => {
                      handleFirstSeasonFocus()
                    }}
                    data-testid="playbutton"
                    id="seasontab"
                    className={`firstseriesrecommended-tab-focus ${
                      seasonTab == true ? 'focusable' : null
                    }`}
                    onClick={() => {}}
                  >
                    <span className="firstseason-title">
                      {contentData?.seasons_count > 1
                        ? `${handleTranslationchange(
                            'playing_playerControls_toolbar_button_seasons'
                          )}`
                        : `${handleTranslationchange(
                            'vcard_access_abbreviationSeason_label'
                          )} ${contentData?.seasons_count}`}
                    </span>
                  </button>
                  <button
                    onFocus={() => {
                      setCurrentButtonFocus('recomendation'),
                        handleRecomendationTab()
                    }}
                    data-testid="playbutton"
                    id="recommendtab"
                    className={`firstmore-information-tab focusable ${
                      threebuttonvalue == 'recommenddata' ? 'active' : ''
                    }`}
                  >
                    <span className="firstrecommend-title">
                      Recomendaciones
                    </span>
                  </button>
                  <button
                    onFocus={() => {
                      setCurrentButtonFocus('moreinfo')
                      handleMoreInfoTab()
                    }}
                    data-testid="playbutton"
                    id="moreinfotab"
                    className={`firstmore-information-tab focusable ${
                      threebuttonvalue == 'moreinfo' ? 'active' : ''
                    }`}
                  >
                    <span className="firstmoreinfo-title">
                      {handleTranslationchange(
                        'Vcard_TabsMasInformacion_TextoTitulo'
                      )}
                    </span>
                  </button>
                </div>
              </>
            ) : (
              <div
                className="seriestab-tray-serieswrapper"
                id="seriessecondpage"
              >
                <div className="buttonslayoutthree" id="threeButtonLayout">
                  <button
                    autoFocus={seasonTab == true ? true : false}
                    onFocus={handleSecondSeasonFocus}
                    onBlur={() => setUpkeyFocus(false)}
                    onKeyDown={event => {
                      handleScrollingEpisodes(event)
                    }}
                    data-testid="playbutton"
                    id="secondSeasonTab"
                    className={
                      focusSeasonsRed
                        ? 'seriesrecommended-tab-focus focusable'
                        : currentButtonFocus == 'seasons' && upkeyfocus
                        ? 'secondrecommended-tab focusable'
                        : 'secondmore-information-tab focusable'
                    }
                    onClick={() => {
                      handleSecondSeasonsTab()
                    }}
                    data-sn-down={
                      recomendationTab
                        ? '#recommendimage0'
                        : moreInfoTab
                        ? '#castId0'
                        : contentData?.seasons_count == 1
                        ? '#episode-image-button0'
                        : null
                    }
                  >
                    <span className="Secondseasontitle">
                      {vodSeriesDataRedux?.seasons_count > 1
                        ? `${handleTranslationchange(
                            'playing_playerControls_toolbar_button_seasons'
                          )}`
                        : `${handleTranslationchange(
                            'vcard_access_abbreviationSeason_label'
                          )} ${
                            !vodSeriesDataRedux?.seasons_count
                              ? ' '
                              : vodSeriesDataRedux?.seasons_count
                          }`}
                    </span>
                  </button>
                  <button
                    autoFocus={recomendationTab == true ? true : false}
                    id="second-recommend"
                    onFocus={() => {
                      setCurrentButtonFocus('recomendation')
                    }}
                    onBlur={() => setUpkeyFocus(false)}
                    onKeyDown={event => {
                      handleScrollingRecommendation(event)
                    }}
                    data-testid="playbutton"
                    className={
                      focusRecomendRed
                        ? 'seriesrecommended-tab-focus focusable'
                        : currentButtonFocus == 'recomendation' && upkeyfocus
                        ? 'secondrecommended-tab focusable'
                        : 'secondmore-information-tab focusable'
                    }
                    onClick={() => {
                      handleSecondRecomendationTab()
                    }}
                    data-sn-down={
                      recomendationTab
                        ? '#recommendimage0'
                        : moreInfoTab
                        ? '#castId0'
                        : '#episode-image-button0'
                    }
                    data-sn-right="#secondMoreButton"
                  >
                    <span className="secondRecommendtitle">
                      {handleTranslationchange(
                        'Vcard_Tabs_TextoTitulo_Recomendaciones'
                      )}
                    </span>
                  </button>
                  <button
                    autoFocus={moreInfoTab ? true : false}
                    onFocus={() => {
                      setCurrentButtonFocus('moreinfo')
                    }}
                    onBlur={() => setUpkeyFocus(false)}
                    onKeyDown={event => {
                      handleScrollingMoreInfo(event)
                    }}
                    data-testid="playbutton"
                    id="secondMoreButton"
                    className={
                      focusMoreinfoRed
                        ? 'seriesrecommended-tab-focus focusable'
                        : currentButtonFocus == 'moreinfo' && upkeyfocus
                        ? 'secondrecommended-tab focusable'
                        : 'secondmore-information-tab focusable'
                    }
                    onClick={() => {
                      handleSecondMoreInfoTab()
                    }}
                    data-sn-down={
                      moreInfoTab
                        ? '#castId0'
                        : recomendationTab
                        ? '#recommendimage0'
                        : '#episode-image-button0'
                    }
                  >
                    <span className="secondMoreinfotitle">
                      {handleTranslationchange(
                        'Vcard_TabsMasInformacion_TextoTitulo'
                      )}
                    </span>
                  </button>
                </div>
                {seasonstate == 'seasonsdata' ? (
                  <div className="TotalContainer">
                    <div className="seasoncontainer">
                      {!moreInfoTab && threebuttonvalue === 'seasonsdata' ? (
                        <>
                          {contentData?.seasons &&
                            contentData?.seasons.map((item, index) => (
                              <button
                                className="SeasonButtonseriesFocused focusable"
                                data-testid="seasonTest"
                                id={`seasonbutton-${index}`}
                                ref={el => (seasonRef.current[index] = el)}
                                key={index}
                                data-sn-right="#episode-image-button0"
                                onFocus={() => {
                                  setCurrentButtonFocus(`season${index + 1}`)
                                  setSeasonstate('seasonsdata')
                                  setUpkeyFocus(false)
                                  setLastFocusedSeasonIndex(index)
                                  handleSeasons(item, index)
                                }}
                                data-sn-down={
                                  index === contentData?.seasons?.length - 1
                                    ? true
                                    : null
                                }
                              >
                                <p
                                  className="SeasonData"
                                  style={{
                                    fontWeight:
                                      currentButtonFocus ===
                                      `season${index + 1}`
                                        ? 'bold'
                                        : 'normal'
                                  }}
                                >
                                  {handleTranslationchange(
                                    'vcard_access_abbreviationSeason_label'
                                  )}{' '}
                                  {item?.number} &nbsp;{' '}
                                </p>
                                <span className="episode-count">
                                  {currentButtonFocus ===
                                    `season${index + 1}` ?? getFocusedElementId
                                    ? `${item?.episodes_count} ${
                                        item.episodes_count > 1
                                          ? `${handleTranslationchange(
                                              'vcard_access_abbreviationEpisode_label'
                                            )}`
                                          : apilanguage?.vcard_access_abbreviationEpisode_label?.slice(
                                              0,
                                              8
                                            )
                                      } `
                                    : ' '}
                                </span>
                              </button>
                            ))}
                        </>
                      ) : null}
                    </div>
                    {seasonstate === 'seasonsdata' && seasonData?.episodes && (
                      <div
                        className="totalepisodescontainer"
                        id="totalepisodescontainer"
                        onKeyDown={handleKeyDown}
                      >
                        {seasonData?.episodes
                          ?.slice()
                          ?.sort((a, b) => a.episode_number - b.episode_number)
                          .map((item, index) => (
                            <div
                              className="episodecontainer"
                              id={`episode-container-${index}`}
                              key={index}
                              style={{ display: 'flex' }}
                            >
                              <button
                                id={`episode-image-button${index}`}
                                className={
                                  initialFocusDone
                                    ? `initial-episode focusable`
                                    : `episode-card-img-series focusable`
                                }
                                ref={el => (episodeRef.current[index] = el)}
                                onClick={e =>
                                  handleEpisodeClick(e, item, index)
                                }
                                onFocus={() => {
                                  handleEpisodeFocus(index)
                                }}
                                data-sn-up={
                                  index === 0 ? '#secondSeasonTab' : null
                                }
                                data-sn-left={
                                  contentData?.seasons_count == 1
                                    ? '#secondSeasonTab'
                                    : null
                                }
                                autoFocus={
                                  moreEpisodeClick?.episodeButtonIndx == index
                                }
                                data-sn-down={
                                  seasonData?.episodes?.length - 1 === index
                                    ? true
                                    : null
                                }
                              >
                                <LazyLoadImage
                                  className="episode-card-imgin-series"
                                  src={item?.image_still}
                                  id="episode-season-image"
                                  placeholderSrc={'images/landscape_card.png'}
                                />
                                {progressbarContentData?.map(eachepisodeData =>
                                  eachepisodeData?.id == item?.id ? (
                                    <div
                                      className="episode-progress-bar"
                                      style={{
                                        visibility:
                                          eachepisodeData?.vistime?.last
                                            ?.progress > 0
                                            ? ''
                                            : 'hidden'
                                      }}
                                    >
                                      <ProgressBar
                                        style={{
                                          visibility:
                                            eachepisodeData?.vistime?.last
                                              ?.progress > 0 &&
                                            eachepisodeData?.vistime?.last
                                              ?.progress !== 100
                                              ? ''
                                              : 'hidden'
                                        }}
                                        isLoading={false}
                                        percent={
                                          eachepisodeData?.vistime?.last
                                            ?.progress
                                        }
                                        size={'small'}
                                        smallprogress={smallprogress}
                                      />
                                    </div>
                                  ) : null
                                )}
                              </button>
                              <div className="episodeText">
                                <p className="EpisodeCardTitle">
                                  {apilanguage?.vcard_access_abbreviationEpisode_label?.slice(
                                    0,
                                    8
                                  )}{' '}
                                  {item?.episode_number}:{' '}
                                  {handledescription(item?.title_episode, 53)}
                                </p>
                                <div className="EpisodeDesLayout">
                                  <p className="EpisodeDescription">
                                    {handledescription(item?.description, 147)}
                                  </p>
                                </div>
                                <button
                                  className={`moreinfoseason focusable`}
                                  id={`eachButtonIndex${index}`}
                                  ref={el =>
                                    (moreInfoSeasonRefs.current[index] = el)
                                  }
                                  data-sn-up={
                                    index === 0
                                      ? '#secondSeasonTab'
                                      : `#episode-image-button${index - 1}`
                                  }
                                  onFocus={() => {
                                    setCurrentButtonFocus(
                                      `season${lastFocusedSeasonIndex + 1}`
                                    )
                                    setUpkeyFocus(false)
                                  }}
                                  data-sn-down={`#episode-image-button${
                                    index + 1
                                  }`}
                                  onBlur={() => {
                                    dispatch(
                                      getEpisodeMoreInfo({
                                        moreInfoClicked: false
                                      })
                                    )
                                    setUpkeyFocus(false)
                                  }}
                                  autoFocus={
                                    moreEpisodeClick?.moreInfoButtonIndex ==
                                    index
                                  }
                                  onClick={() =>
                                    handleEpisodeMoreClick(item, index)
                                  }
                                >
                                  <p className="moreinfotitleseriesseason">
                                    {handleTranslationchange(
                                      'Vcard_TabsMasInformacion_TextoTitulo'
                                    )}
                                  </p>
                                </button>
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                ) : seasonstate == 'recommenddata' ? (
                  mltData && (
                    <div className="RailLayout">
                      <span className="SeasonHeader">
                        {handleTranslationchange(
                          'Vcard_CarruselRecomendaciones_TextoTitulo'
                        )}
                      </span>
                      <div className="MLTContainer">
                        {mltData &&
                          mltData.map((item, index) => (
                            <button
                              data-testid="mltTest"
                              id={`recommendimage${index}`}
                              className="MLTCard focusable"
                              onClick={() => {
                                handleMLT(item,index)
                              }}
                              onFocus={() => {
                                setCurrentButtonFocus(`MLT-${index}`)
                              }}
                              key={item?.id}
                              data-sn-up="#second-recommend"
                            >
                              <div className="MLTCardImage">
                                {item?.image_small ? (
                                  <LazyLoadImage
                                    src={item?.image_small}
                                    loading="lazy"
                                    alt="PlaceHolder"
                                    className="MLTCardImage"
                                    key={index}
                                    id={`mltfocus${index}`}
                                    placeholderSrc={'images/landscape_card.png'}
                                  />
                                ) : (
                                  <LazyLoadImage
                                    src="images/landscape_card.png"
                                    loading="lazy"
                                    alt="PlaceHolder"
                                    className="MLTCardImage"
                                    placeholderSrc={'images/landscape_card.png'}
                                  />
                                )}
                              </div>
                              <div
                                style={{
                                  display:
                                    currentButtonFocus === `MLT-${index}`
                                      ? ''
                                      : 'none'
                                }}
                                className="MLTDetails"
                              >
                                <div className="MLTTitle">{item?.title}</div>
                              </div>
                              {item.image_small &&
                              item?.proveedor_code == 'amco' ? (
                                item?.format_types === 'ppe,download' ? (
                                  <div className="proveedorBlockRailAlq">
                                    <img
                                      src={'images/Alquilar.svg'}
                                      className="tagAlq"
                                    />
                                  </div>
                                ) : item?.format_types === 'ppe' ? (
                                  <div className="proveedorBlockRailAlq">
                                    <img
                                      src={'images/Alquilar.svg'}
                                      className="tagAlq"
                                    />
                                  </div>
                                ) : null
                              ) : item.image_small &&
                                item?.proveedor_code &&
                                item?.image_medium ? (
                                <div className="proveedorBlockRail_vero_hara">
                                  {addProveedor(providerLabel?.[item?.proveedor_code]?.susc) && (
                                    <img
                                      id="#icon1"
                                      className={
                                        item?.proveedor_code === 'picardia2' &&
                                        'picardia-image'
                                      }
                                      src={addProveedor(providerLabel?.[item?.proveedor_code]?.susc)}
                                    />
                                  )}
                                  {item.image_small &&
                                    item?.proveedor_code === 'picardia2' &&
                                    item?.image_medium && (
                                      <div className="picardia-proveedorBlockRail">
                                        <img
                                          src={'images/Adultus.svg'}
                                          className="picardia-tag"
                                        />
                                      </div>
                                    )}
                                  {item?.format_types === 'free' &&
                                  userDetails?.subscriptions?.length == 0 ? (
                                    <div className="verahora-tag">
                                      VER AHORA
                                    </div>
                                  ) : null}
                                </div>
                              ) : null}
                            </button>
                          ))}
                      </div>
                    </div>
                  )
                ) : (
                  <>
                    <div className="more-info-layouts">
                      {currentButtonFocus !== 'cast' && (
                        <>
                          <div className="moreInfo-title">
                            {handleMainTitle(
                              vodSeriesCastRedux?.common?.extendedcommon?.media
                                ?.serie?.title
                            )}
                          </div>
                          <div className="moreInfo-description-layout">
                            <div className="moreInfo-detail-layout">
                              <span
                                className="moreInfo-description-detail"
                                ref={descriptionRef}
                              >
                                {
                                  vodSeriesCastRedux?.common?.extendedcommon
                                    ?.media?.description_extended
                                }
                              </span>
                            </div>
                            <div className="moreInfo-vod-detail">
                              <div className="year-div">
                                <div className="moreInfo-year">
                                  {handleTranslationchange(
                                    'Vcard_MasInformacion_TextoTituloAnio_TituloAnio'
                                  )}
                                </div>
                                <div className="moreInfo-year-copy">
                                  {
                                    vodSeriesCastRedux?.common?.extendedcommon
                                      ?.media?.publishyear
                                  }
                                </div>
                              </div>

                              <div className="generos-div">
                                <div className="generos">{`${handleTranslationchange(
                                  'GENRE'
                                )}:`}</div>
                                <div className="generos-copy">
                                  {handlegenrechar(genreData?.toString())}
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                      <div className="casts-layout">
                        <span className="casts-title">
                          {handleTranslationchange(
                            'Vcard_CarruselReparto_TextoTitulo_Titulo'
                          )}
                        </span>
                        {moreInfoTab && moreInfoTab ? (
                          <div className="series-Casts">
                            {castData &&
                              castData?.map((cast, index) => {
                                return (
                                  <button
                                    data-testid="castTest"
                                    id={`castId${index}`}
                                    data-sn-up="#secondMoreButton"
                                    className={'mlts-block focusable'}
                                    onFocus={() => {
                                      setCurrentButtonFocus('cast')
                                    }}
                                    autoFocus={
                                      setVodReturnFocus == `castId${index}`
                                    }
                                    onClick={() =>
                                      handleCastClick(cast, `castId${index}`)
                                    }
                                    key={index}
                                  >
                                    <div className="castcard-img">
                                      <LazyLoadImage
                                        className="cast-img"
                                        src={
                                          cast?.image && cast?.image != 'undefined' &&
                                          cast?.image != 'null' && cast?.image != ''
                                            ? cast?.image
                                            : 'images/Vod_Movies_Icons/cast_thumbnail.png'
                                        }
                                        key={index}
                                        placeholderSrc={
                                          'images/Vod_Movies_Icons/cast_thumbnail.png'
                                        }
                                      />
                                      <div className="cast-details">
                                        <div className="cast-role">
                                          {cast?.role}
                                        </div>
                                        <div className="cast-name">
                                          <p>
                                            {cast?.first_name === undefined &&
                                            cast?.last_name === undefined
                                              ? cast?.surname + ' ' + cast?.name
                                              : cast?.first_name +
                                                ' ' +
                                                cast?.last_name}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </button>
                                )
                              })}
                          </div>
                        ) : (
                          <div style={{ display: 'flex' }}>
                            <CastSkeletonLoading listsToRender={7} />
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default VodSeries
