import { call, takeEvery } from '@redux-saga/core/effects'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getVodMoviesSuccess,
  getVodMoviesError,
  getVodMoviesCastSuccess,
  getVodMoviesCastError,
  getVodMoviesWatchListSuccess,
  getVodMoviesWatchlistError,
  addVodMoviesWatchlistSuccess,
  addVodMoviesWatchListError,
  deleteVodMoviesWatchListSuccess,
  deleteVodMoviesWatchListError
} from '../store/slices/VodMoviesSlice'

export function* fetchVodMoviesSaga({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.VOD_MOVIES_RECOMENDATION_URL}&user_id=${
        payload?.userId
      }&group_id=${payload?.id}&display_only=${payload?.display_only}&region=${region}&HKS=${payload?.hks}&is_kids=${
        payload.is_kids == 'false' ? 0 : 1
      }&terminos=0&user_token=${
        payload?.user_token
      }&is_only_provider=true&quantity=50&filterlist=${
        payload?.filterlist
      }`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getVodMoviesSuccess(response))
        },
        onError(error) {
          store.dispatch(getVodMoviesError(error))
        }
      }
    )
  } catch (error) {}
}

export function* fetchVodMoviesCastSaga({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.VOD_MOVIES_CONTENT_CAST_URL}&user_id=${payload?.userId}&group_id=${
        payload?.id
      }&region=${region}&HKS=${payload?.hks}&is_kids=${
        payload.is_kids == 'false' ? 0 : 1
      }&terminos=0`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getVodMoviesCastSuccess(response))
        },
        onError(error) {
          store.dispatch(getVodMoviesCastError(error))
        }
      }
    )
  } catch (error) {}
}

export function* getVodMoviesWatchlist(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.GET_WATCHLIST_URL}&user_id=${data?.payload?.userId}&HKS=${data?.payload?.HKS}&user_hash=${data?.payload?.user_hash}&lasttouch=${data?.payload?.id}&region=${region}&filterlist=${data?.payload?.filterlist}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getVodMoviesWatchListSuccess(response))
        },
        onError(error) {
          store.dispatch(getVodMoviesWatchlistError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(getVodMoviesWatchlistError(error))
  }
}

export function* addVodMoviesWatchList(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.ADD_VOD_MOVIES_WATCHLIST_URL}&user_id=${data?.payload?.userId}&HKS=${data?.payload?.HKS}&user_hash=${data?.payload?.user_hash}&object_id=${data?.payload?.id}&object_type=1&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(addVodMoviesWatchlistSuccess(response))
        },
        onError(error) {
          store.dispatch(addVodMoviesWatchListError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(addVodMoviesWatchListError(error))
  }
}

export function* deleteVodMoviesWatchList(data) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.DEL_VOD_MOVIES_WATCHLIST_URL}&user_id=${data?.payload?.userId}&HKS=${data?.payload?.HKS}&user_hash=${data?.payload?.user_hash}&object_id=${data?.payload?.id}&object_type=1&region=${region}`,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(deleteVodMoviesWatchListSuccess(response))
        },
        onError(error) {
          store.dispatch(deleteVodMoviesWatchListError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(deleteVodMoviesWatchListError(error))
  }
}

export default function* VodMoviesDataSaga() {
  yield takeEvery('movies/getVodMoviesData', fetchVodMoviesSaga)
  yield takeEvery('movies/getVodMoviesCastData', fetchVodMoviesCastSaga)
  yield takeEvery('movies/getVodMoviesWatchlist', getVodMoviesWatchlist)
  yield takeEvery('movies/addVodMoviesWatchList', addVodMoviesWatchList)
  yield takeEvery('movies/deleteVodMoviesWatchList', deleteVodMoviesWatchList)
}
