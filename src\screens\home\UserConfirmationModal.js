import React, { useEffect, useRef } from 'react'
import '@procot/webostv/webOSTV'
import './UserConfirmationModal.scss'
import { useSelector } from 'react-redux'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const exitApplication = e => {
  if (typeof tizen !== 'undefined') {
    tizen.application.getCurrentApplication().exit()
    e.preventDefault()
  } else {
    window.close()
    e.preventDefault()
  }
}

const UserConfirmationModal = ({ hideConfirmpopup }) => {  
  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const navData = useSelector(state => state?.homeReducer?.navbarNodeData?.response?.nodes) ?? []
  const navbarTab = useSelector(state => state?.homeReducer?.NavTabValue)
  const contentSection = navData?.find(each => each?.code === navbarTab)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const exitRef = useRef(null)
  const cancelRef = useRef(null)

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  useEffect(() => {
    pushScreenViewEvent({screenName:'user_confirmation_modal', screenData: userDetails, prevScreenName: 'inicio', contentSection: contentSection?.text})
    const timer = setTimeout(() => {
      if (exitRef.current) {
        exitRef.current?.focus()
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  const handleKeyDown = e => {
    if (e.key === 'ArrowRight' && document.activeElement === exitRef.current) {
      cancelRef.current?.focus()
    } else if (
      e.key === 'ArrowLeft' &&
      document.activeElement === cancelRef.current
    ) {
      exitRef.current?.focus()
    }
  }

  return (
    <>
      <div className="user-confirmation-container"></div>
      <div className="user-confirm-body" onKeyDown={handleKeyDown}>
        <div className="user-confirm-block">
          <div className="user-main-block">
            <span className="go-out-message">
              {truncateText('exit_title_msg', 40)}
            </span>
            <div className="user-confirm-button-block">
              <button
                onClick={e => exitApplication(e)}
                className="default-btn focusable"
                ref={exitRef}
                id="exitBtn"
              >
                {truncateText('exit_btn_exit_txt', 40)}
              </button>
              <button
                onClick={hideConfirmpopup}
                className="default-btn focusable"
                ref={cancelRef}
                id="hidepopup"
              >
                {truncateText('exit_btn_cancel_txt', 40)}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default UserConfirmationModal
