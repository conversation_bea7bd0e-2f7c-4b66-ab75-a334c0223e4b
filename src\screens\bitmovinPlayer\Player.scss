.container-loader {
  position: absolute;
  top: 23%;
  right: 40%;
}

.progress-bar-shown {
  display: '';
}

.progress-bar-hide,
.bmpui-ui-buffering-overlay {
  //default progressbar and loader
  display: none !important;
}

.bmpui-controls-hidden {
  all: unset !important;
}

.bmpui-ui-controlbar.bmpui-hidden {
  visibility: unset !important;
  opacity: unset !important;
}

.bmpui-ui-titlebar.bmpui-hidden {
  visibility: unset !important;
  opacity: unset !important;
}

.bmpui-ui-controlbar .bmpui-controlbar-bottom {
  display: none;
}

.bmpui-ui-watermark {
  //to hide bitmovin watermark
  display: none;
}

.bmpui-ui-titlebar {
  //to hide the titlebar
  display: none;
}

.bmpui-ui-playbacktimelabel {
  display: none;
}

.bmpui-ui-controlbar {
  //bitmovin timer and progressbar container
  bottom: 248px !important;
  background: transparent !important;
  display: flex;
  justify-content: center;
}

.bmpui-ui-recommendation-overlay {
  //bitmovin refresh button hide
  display: none !important;
}

.bmpui-ui-seekbar {
  display: none !important;
  width: 1504px !important;
  outline: unset !important;
  box-shadow: none !important;
  position: absolute !important;
  bottom: 72px;
  left: 200px;
}

.bmpui-seekbar-bufferlevel {
  background: transparent !important;
}

.bmpui-seekbar-backdrop {
  // progressbar
  height: 10px !important;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.5) !important;
}

.bmpui-container-wrapper {
  //subtitle position
  display: contents !important;
}

.subtitle-text {
  background-color: #000 !important;
  padding: 5px !important;
}

.bmpui-controlbar-visible {
  bottom: 6em !important;
}

.bmpui-ui-subtitle-overlay {
  //subtitle position
  display: flex;
  align-items: flex-end;
  justify-content: center;
  margin-bottom: 150px;
  font-size: 2.2em !important;
}

.bmpui-ui-hugeplaybacktogglebutton {
  // to hide onclick play button over the player
  display: none;
}

.player-loader {
  width: '50%';
  height: 1080px;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  margin: 0px auto;
  right: 0;
}

.bottom-controls {
  z-index: 100;

  .metadata {
    height: 80px;
    width: 1504px;
    position: absolute;
    bottom: 280px;
    left: 225px;
    display: flex;
    flex-direction: row;
    text-align: center;
    align-items: center;
    justify-content: center;

    .channel-number {
      height: 32px;
      width: 20px;
    }

    .event-title {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .event-title,
    .channel-number,
    .channel-logo {
      color: #ffffff;
      font-family: Roboto;
      font-size: 29px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 34px;
    }
  }

  .content-details {
    position: absolute;
    bottom: 329px;
    left: 210px;
    display: flex;

    .content-title {
      text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
      font-family: Roboto;
      font-size: 40px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      text-align: left;
      opacity: 1;
      color: #ffffff;
      letter-spacing: 0px;
      margin: 0;
      align-self: center;
    }

    .season-details-wrapper {
      margin-left: 15px;
      margin-top: 4px;
    }

    .season-ep-details {
      text-align: left;
      letter-spacing: 0px;
      opacity: 1;
      color: #ffffff;
      text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
      font-family: Roboto;
      font-size: 32px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .ep-title {
      color: #ffffff;
      letter-spacing: 0;
      text-align: right;
      text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
      font-family: Roboto;
      font-size: 32px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-left: 15px;
    }
  }

  .left-timer {
    position: absolute;
    z-index: 1;
    bottom: 246px;
    left: 90px;

    .timer-font {
      text-align: right;
      color: #d8d8d8;
      font-family: Roboto;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;
    }
  }

  .vod-custom-slider-container {
    position: absolute;
    z-index: 10;
    justify-content: center;
    display: flex;
    bottom: 278px;
    left: 200px;

    .vod-custom-slider {
      width: 1504px;
      height: 10px;
      outline: unset;
      margin-right: 35px;
      border-radius: 2px;
      -webkit-appearance: none;
      appearance: unset;
    }

    .sprites-image-pos {
      position: absolute;
      bottom: 75px;
      border: 4px solid white;
      border-radius: 15px;
    }

    .vod-custom-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      height: 30px !important;
      width: 30px !important;
      background-color: #c60100 !important;
      border-radius: 50%;
      border: none;
      transition: 0.2s ease-in-out;
    }

    .vod-custom-slider::-moz-range-thumb {
      height: 30px !important;
      width: 30px !important;
      background-color: #c60100 !important;
      border-radius: 50%;
      border: none;
      transition: 0.2s ease-in-out;
    }

    .vod-custom-slider::-ms-thumb {
      -webkit-appearance: none;
      border: none;
      border-radius: 12px;
      height: 30px !important;
      width: 30px !important;
      background-color: #c60100 !important;
    }

    .vod-custom-slider::-webkit-slider-thumb:hover {
      box-shadow: 0 0 0 10px rgba(198, 0, 0, 0.1);
    }

    .vod-custom-slider:active::-webkit-slider-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }

    .vod-custom-slider:focus::-webkit-slider-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }

    .vod-custom-slider::-moz-range-thumb:hover {
      box-shadow: 0 0 0 10px rgba(198, 0, 0, 0.1);
    }

    .vod-custom-slider:active::-moz-range-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }

    .vod-custom-slider:focus::-moz-range-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }
  }

  .right-timer {
    position: absolute;
    z-index: 1;
    bottom: 246px;
    right: 90px;

    .timer-font {
      text-align: right;
      color: #d8d8d8;
      font-family: Roboto;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 30px;
    }

    .live-icon {
      position: relative;
      bottom: 25px;
    }
  }

  .player-controls-cont {
    position: absolute;
    bottom: 86px;
    z-index: 100;
    display: flex;
    justify-content: center;
    height: 154px;

    .skip-intro-alone {
      margin: 0px auto 95px 135px;
    }

    .hide-skip-intro-alone {
      display: none !important;
    }

    .skip-intro {
      height: 82.08px;
      width: 500px;
      border-radius: 10.03px;
      background-color: #1a1a1a;

      &:focus {
        background-color: #981c15;
      }

      .skip-intro-text {
        height: 43px;
        width: 100%;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36.5px;
        font-weight: bold;
        letter-spacing: -0.58px;
        line-height: 42.18px;
        text-align: center;
        margin: 0;
        text-transform: uppercase;
      }
    }

    .bottom-btn {
      width: 88px;
      margin-right: 33px;
      margin-left: 33px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .play-pause-btn {
        width: 80px;
        height: 80px;
        display: flex;
        margin: 0px auto;
      }

      .btn-text-focus-out {
        color: #bbbbbb;
        font-family: Roboto;
        font-size: 24px;
        letter-spacing: -0.62px;
        line-height: 29px;
        text-align: center;
        width: 128px;
        height: 100px;
        margin: 10px 0px 0px 0px;
        word-wrap: break-word;
        cursor: pointer;
      }

      .btn-text-focus-in {
        text-align: center;
        font-family: Roboto;
        font-size: 24px;
        letter-spacing: -0.62px;
        line-height: 29px;
        color: #ffffff;
        opacity: 1;
        width: 128px;
        height: 100px;
        margin: 10px 0px 0px 0px;
        word-wrap: break-word;
        cursor: pointer;
      }
    }

    .bottom-btn-2 {
      width: 88px;
      margin-left: 38px;
      margin-right: 38px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .play-pause-btn {
        width: 80px;
        height: 80px;
        display: flex;
        margin: 0px auto;
      }

      .btn-text-focus-out {
        color: #bbbbbb;
        font-family: Roboto;
        font-size: 24px;
        letter-spacing: -0.62px;
        line-height: 29px;
        text-align: center;
        width: 128px;
        height: 100px;
        margin: 10px 0px 0px 0px;
        word-wrap: break-word;
        cursor: pointer;
      }

      .btn-text-focus-in {
        text-align: center;
        font-family: Roboto;
        font-size: 24px;
        letter-spacing: -0.62px;
        line-height: 29px;
        color: #ffffff;
        opacity: 1;
        width: 128px;
        height: 100px;
        margin: 10px 0px 0px 0px;
        word-wrap: break-word;
        cursor: pointer;
      }
    }

    .dot,
    .dot-red {
      margin: 0px auto;
      height: 80px;
      width: 80px;
      border-radius: 50%;
      display: flex;
      position: relative;
      text-align: center;
    }

    .dot {
      background-color: rgb(26, 26, 26);
    }

    .dot-red {
      background-color: rgb(158, 28, 21);
    }

    .control-btns {
      position: absolute;
      height: 30px;
      width: 40px;
      left: 25%;
      top: 33%;
    }
  }

  .player-controls-cont-live {
    @extend .player-controls-cont;
    bottom: 60px;
  }
}

.bottom-controls-live {
  z-index: 100;

  .metadata {
    height: 80px;
    width: 1504px;
    position: absolute;
    bottom: 280px;
    left: 5.5%;
    display: flex;
    flex-direction: row;
    text-align: center;
    align-items: center;
    justify-content: center;

    .channel-number {
      height: 32px;
      width: 52px;
    }

    .channel-logo {
      width: 130px;
      height: 70px;
    }

    .event-title {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .event-title,
    .channel-number,
    .channel-logo {
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 34px;
    }
  }

  .content-details {
    position: absolute;
    bottom: 280px;
    left: 210px;
    padding-bottom: 20px;
    display: flex;

    .content-title {
      text-align: left;
      width: auto;
      font: normal normal bolder 27px Roboto;
      opacity: 1;
      color: #ffffff;
      letter-spacing: 0px;
      line-height: 30px;
      text-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.5);
      margin: 0;
      align-self: center;
    }

    .season-ep-details {
      text-align: left;
      font: normal normal normal 22px Roboto;
      letter-spacing: 0px;
      text-shadow: 0px 6px 6px #00000029;
      opacity: 1;
      color: #ffffff;
      line-height: 24px;
      margin-left: 8px;
      text-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.5);
    }

    .ep-title {
      font: normal normal bolder 22px Roboto;
      color: #ffffff;
      letter-spacing: 0;
      line-height: 24px;
      text-align: right;
      text-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.5);
    }
  }

  .left-timer {
    position: absolute;
    z-index: 1;
    bottom: 232px;
    left: 5%;

    .timer-font {
      color: #ffffff;
      font-family: Roboto;
      font-size: 20px;
      letter-spacing: 0;
      line-height: 20px;
      text-align: right;
      padding-bottom: 10px;
    }
  }

  .right-timer {
    position: absolute;
    z-index: 1;
    bottom: 232px;
    left: 88.5%; //since right attr is not working fine when live icons are changing, hence implemented with left attr
    display: flex;
    flex-direction: row;

    .timer-font {
      color: #ffffff;
      font-family: Roboto;
      font-size: 20px;
      letter-spacing: 0;
      line-height: 20px;
      text-align: right;
      padding-bottom: 10px;
    }

    .live-icon {
      position: relative;
      top: 20px;
      height: 25px;
      left: 10px;
    }
  }

  .custom-slider-container {
    // width: 100%;
    position: absolute;
    bottom: 264px;
    z-index: 10;
    justify-content: center;
    display: flex;
    left: 9.5%;

    .custom-slider {
      width: 1504px;
      height: 12px;
      outline: unset;
      margin-right: 35px;
      border-radius: 8px;
      -webkit-appearance: none;
      appearance: unset;
    }

    .custom-slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      height: 30px !important;
      width: 30px !important;
      background-color: #981c15 !important;
      border-radius: 50%;
      border: none;
      transition: 0.2s ease-in-out;
    }

    .custom-slider::-moz-range-thumb {
      height: 30px !important;
      width: 30px !important;
      background-color: #981c15 !important;
      border-radius: 50%;
      border: none;
      transition: 0.2s ease-in-out;
    }

    .custom-slider::-ms-thumb {
      -webkit-appearance: none;
      border: none;
      border-radius: 12px;
      height: 30px !important;
      width: 30px !important;
      background-color: #981c15 !important;
    }

    .custom-slider::-webkit-slider-thumb:hover {
      box-shadow: 0 0 0 10px rgba(198, 0, 0, 0.1);
    }

    .custom-slider:active::-webkit-slider-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }

    .custom-slider:focus::-webkit-slider-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }

    .custom-slider::-moz-range-thumb:hover {
      box-shadow: 0 0 0 10px rgba(198, 0, 0, 0.1);
    }

    .custom-slider:active::-moz-range-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }

    .custom-slider:focus::-moz-range-thumb {
      box-shadow: 0 0 0 13px rgba(198, 0, 0, 0.2);
    }
  }

  .player-controls-cont {
    position: absolute;
    bottom: 50px;
    z-index: 100;
    display: flex;
    justify-content: center;
    width: 890px;

    .skip-intro-alone {
      margin: 0px auto 50px 40px;
    }

    .skip-intro {
      height: 82.08px;
      width: 500px;
      border-radius: 10.03px;
      background-color: #1a1a1a;

      &:focus {
        background-color: #981c15;
      }

      .skip-intro-text {
        height: 43px;
        width: 100%;
        color: #ffffff;
        font-family: Roboto;
        font-size: 36.5px;
        font-weight: bold;
        letter-spacing: -0.58px;
        line-height: 42.18px;
        text-align: center;
        margin: 0;
        text-transform: uppercase;
      }
    }

    .play-pause-btn {
      width: 80px;
      height: 80px;
      display: flex;
      margin: 0px auto;
    }

    .btn-text-focus-out {
      color: #bbbbbb;
      margin: 0px;
    }

    .btn-text-focus-in {
      color: #ffffff;
      margin: 0px;
    }

    .dot,
    .dot-red {
      margin: 0px auto;
      height: 80px;
      width: 80px;
      border-radius: 50%;
      display: flex;
      position: relative;
      text-align: center;
    }

    .dot {
      background-color: rgb(26, 26, 26);
    }

    .dot-red {
      background-color: rgb(158, 28, 21);
    }

    .control-btns {
      position: absolute;
      height: 30px;
      width: 40px;
      left: 25%;
      top: 33%;
    }

    .button-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: center;
    }

    .button-text-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 75px;
      height: 28px;
      font-size: 24px;
      font-family: Roboto;
    }
  }

  .player-controls-cont-live {
    @extend .player-controls-cont;
    bottom: 113px;

    button {
      width: 160px;
      height: 128px;
    }

    img {
      width: 80px;
      height: 80px;
    }
  }
}

.bottom-bar-container {
  position: absolute;
  bottom: 24px;
  width: 100%;
  justify-content: center;
  display: flex;
  z-index: 101;

  .go-out-button {
    width: 350px;
    height: 48px;
    border-radius: 6.6px;
    background-color: #2e303d;
    text-align: center;
    padding: 0 10px;
    margin: 0 20px;

    .go-out-btn-txt {
      color: #ffffff;
      font-family: Roboto;
      font-size: 29px;
      font-weight: bold;
      letter-spacing: 0;
      margin-left: 10px;
    }

    &:focus {
      height: 55px;
      width: 370px;
      padding: 0 10px;
    }
  }
}

#alertDivWrapper {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 48.21%, rgba(0, 0, 0, 0.71) 70.47%, rgba(0, 0, 0, 0.96) 100%);//provided as per VD
  height: 1080px;//provided as per VD
  width: 1920px;//provided as per VD
  position: absolute;
  top: 0px;
  flex-shrink: 0;//provided as per VD
}

.record-tooltip-main-div {
  display: flex;
  position: absolute;
  bottom: 128px;
  align-items: center;
  z-index: 101;
  flex-direction: row;
  width: 1920px;
  left: 50%;
  transform: translateX(-50%);
  justify-content: center;
}

.add-record-dimensions {
  width: 52px;
  height: 42px;
}

.record-tooltip {
  display: flex;
  padding: 0px 30px;
  border-radius: 18px;
  height: 80px;
  color: white;
  background: #2e303d;
  font-size: 40px;
  align-items: center;
}

.lock-unlock-tooltip-main-div {
  display: flex;
  position: absolute;
  bottom: 128px;
  align-items: center;
  z-index: 101;
  white-space: nowrap;
  left: 50%;
  transform: translateX(-50%);

  .lock-unlock-tooltip {
    display: flex;
    padding: 0px 30px 0px 0px;
    border-radius: 18px;
    height: 80px;
    color: white;
    background: #2e303d;
    font-size: 40px;
    align-items: center;

    .lock-unlock-image {
      display: flex;
      width: 100px;
    }

    .lock-unlock-image-dimensions {
      width: 40px;
      margin-left: 34px;
    }
  }
}

.reminder-tooltip-main-div {
  display: flex;
  width: 100%;
  justify-content: center;
  justify-items: center;
  position: absolute;
  bottom: 128px;
  align-items: center;
  z-index: 101;

  .reminder-tooltip {
    display: flex;
    padding: 0px 30px;
    border-radius: 18px;
    height: 80px;
    color: white;
    background: #2e303d;
    font-size: 40px;
    align-items: center;
    margin-right: 15px;

    .reminder-image {
      display: flex;
      width: 50px;
      height: 50px;
      align-items: center;
    }
  }

  .reminder-width {
    width: 30px;
    height: 30px;
  }
}

.shortcut-tooltip {
  display: flex;
  padding: 15px;
  border-radius: 10px;
  color: white;
  background: #2e303d;
  align-items: center;
  justify-content: center;
  font-family: Roboto;
  margin-left: 20px;
}

.tooltip-main-text {
  font-size: 36px;
  color: #ffffff;
  font-family: Roboto;
}

.action {
  font-weight: bold;
}

.tooltip-main-text-lock {
  font-size: 36px;
  color: #ffffff;
  font-family: Roboto;
}

.lock-alert {
  font-size: 36px;
  color: #ffffff;
  font-family: Roboto;
  font-weight: bold;
}

.tooptip-sub-text {
  font-size: 32px;
  color: #ffffff;
  text-align: justify;
  left: 10px;
  position: relative;
  margin-right: 15px;
}

.favourite-icon-container {
  width: 52px;
  height: 40px;
}

.favourite-alert-icon {
  width: 40px;
  height: 35px;
}

.zap-channel-number {
  width: 180px;
  height: 100px;
  position: absolute;
  left: 1680px;
  top: 180px;
  background: rgba(0, 0, 0, 0.5) 0% 0% no-repeat padding-box;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zap-number {
  font-family: Roboto;
  font-weight: bold;
  font-size: 52px;
  color: #ffffff;
  letter-spacing: 3px;
}

.player-controls-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.subtitle-container {
  position: absolute;
  width: 80%;
  padding: 0% 10% 0% 10%;
}

.subtitle-image {
  width: 100%;
}
