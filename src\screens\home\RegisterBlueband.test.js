import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import RegisterBlueband from "./RegisterBlueband";


const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
describe('RegisterBlueband page test', () => {
    test('it should render the RegisterBlueband Page', () => {
        renderWithState(<RegisterBlueband />)
    })
    test('it should render the RegisterBlueband Page onclick green button', () => {
        const { container } = renderWithState(<RegisterBlueband />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'bluebandGreenButton');
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('it should render the RegisterBlueband Page onclick yellow button', () => {
        const { container } = renderWithState(<RegisterBlueband />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'bluebandyellowButton');
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })

})