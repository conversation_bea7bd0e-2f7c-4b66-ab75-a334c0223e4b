import React, { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './ContinueWatching.scss'
import './RecordingEpisodeList.scss'
import moment from 'moment'
import {
  getChannelData,
  getRecordPlayer
} from '../../../store/slices/PlayerSlice'
import { useDispatch } from 'react-redux'
import { getNavTabValue } from '../../../store/slices/HomeSlice'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'

const RecordingEpisodeList = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()
  const [content, setContent] = useState([])
  const [focusContent, setFocusContent] = useState(false)
  const [focusedId, setFocusedId] = useState(null)
  const [selectedContent, setSelectedContent] = useState('')
  const [episodeIds, setEpisodeIds] = useState([])
  const [lockedEpisodeIds, setLockedEpisodeIds] = useState([])
  const [lockedEpisodeContent, setLockedEpisodeContent] = useState([])

  const railImageseries = useSelector(
    state => state?.epg?.EpisodeRecordingList?.response
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const liveChannelsData = useSelector(state => state?.epg?.epgChannel)
  const recordingplayer =
    useSelector(state => state?.player?.recordplayerinfo?.response) ?? {}
  const navData = useSelector(state => state?.homeReducer?.navbarData)
    

  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const railImage = railImageseries?.recordings
  const backelement = document.getElementById(props?.backfocusid)
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)

  useEffect(() => {
    if (railImage?.length) {
      const ids = railImage.map(episode => episode?.channel?.group_id);
      setEpisodeIds(ids);
    }
  }, [railImage]);
  
  useEffect(() => {
    if (lockedChannelsList?.length) {
      const ids = lockedChannelsList.map(channel => channel?.id);
      setLockedEpisodeIds(ids);
    }
  }, [lockedChannelsList]); 
  

  useEffect(() => {
    setLockedEpisodeContent(
      episodeIds.filter(id => lockedEpisodeIds.includes(String(id)))
    )
  }, [episodeIds, lockedEpisodeIds])


  useEffect(() => {
    if (backelement) {
      backelement?.focus()
      backelement?.scrollIntoView({
        block: 'start',
        inline: 'start',
        behavior: 'smooth'
      })
    }
  }, [backelement])

  useEffect(() => {
    if (recordingplayer && Object.keys(recordingplayer)?.length > 0) {
      navigate('/vodPlayer', {
        state: {
          page: 'record',
          backfocusid: focusedId,
          recordTitle: selectedContent,
          gaPreviousPath: 'player grabaciones'
        }
      })
    }
  }, [recordingplayer])

  useEffect(()=>{
    pushScreenViewEvent({screenName:'recording_episode_list', screenData: userDetails, prevScreenName: 'inicio'})
  },[])

  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
    if(lockedChannelsList
      ?.find(
        e =>
          e?.id ==
        item?.channel?.group_id)){
        setFocusedId(`episodeRecord${index}`)
        navigate(
          '/my-settings/help-And-Settings/security-pin/configure',
          {
            state: {
              data: 'record',
              item : item,
              pageName: '/vodPlayer',
              contentName: 'record',
              selectedContent : item,
              backfocusid : focusedId,
              returnPage: 'episodescreen'
            },
            replace: true
          }
        )
      }else{
    let recordgetmedia = item?.actions?.play?.dashwv
    setSelectedContent(item?.channel?.event?.name)
    if (recordgetmedia) {
      dispatch(getRecordPlayer(recordgetmedia))
    } else {
      dispatch(
        getChannelData({
          group_id: item?.channel?.event?.group_id,
          timeshift: item?.channel?.event?.timeshift,
          epgIndex: liveChannelsData?.[1]?.channelResponse?.findIndex(
            itrObj => itrObj.group_id == item?.channel?.event?.group_id
          )
        })
      ),
        navigate('/livePlayer', {
          state: { showControls: 'live', data: item?.channel?.event,gaPreviousPath: 'player grabaciones'
          }, //Added showControls flag which will enable live player after navigation
          replace: true,
          backfocusid: `recordepisode${index}`
        })
    }
  }
  }
  const handleFocus = data => {
    setFocusContent(true)
    setContent(data)
  }
  const handleBlur = data => {
    setFocusContent(false)
  }
  const handleTranslationchange = useCallback(keyname => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (key.redcode == keycode) {
        navigate('/deleterecording', {
          state: {
            recordingData: content,
            url: content?.actions?.delete,
            deletecontent: 'recordigepi',
            backfocusid: focusedId
          }
        })
      }
      } if (key.yellowcode === keycode || keycode === 10009 ||  keycode == 8 ||
        keycode == 'backClick') {
        navigate('/home', {
                   state: { myContentData: true, backfocusid: state?.backfocusid },
                   replace: true
                 })
               
               localStorage.setItem('currNavIdx', navData?.length - 1),
               dispatch(getNavTabValue('miscontenidos'))
      }
    
  }
  const handleLgkey = keycode => {
    if (focusContent) {
      if (keycode == 403) {
        navigate('/deleterecording', {
          state: {
            recordingData: content,
            url: content?.actions?.delete,
            deletecontent: 'recordigepi',
            backfocusid: focusedId
          }
        })
      }
      } if (keycode == 405 || keycode === 461 || keycode == 8 ||
        keycode == 'backClick' ) {
          navigate('/home', {
            state: { myContentData: true, backfocusid: state?.backfocusid },
            replace: true
          })
        localStorage.setItem('currNavIdx', navData?.length - 1),
        dispatch(getNavTabValue('miscontenidos'))
      }
    
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red', 'ColorF2Yellow'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code,
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  function secondsToHms(duration) {
    return moment(duration).format(`H [hr] mm [min]`)
  }

  const dateToYMD = curdate => {
    return moment(curdate).format('MMM D, YYYY')
  }

  useEffect(() => {
    SpatialNavigation.focus()
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className="rec-episode-list">
        <button className="rec-back-button focusable" 
        id="recordEpisodeBack"  
        data-sn-down={`#recordepisode0`}
        onClick={e => handleLgkey('backClick')}>
          <img
            src={'images/Profile_Icons/ic_shortcut_amarillo.png'}
            className="rec-yellow-dot"
            alt="img not found"
          />
          <img
            src={'images/Profile_Icons/ic_shortcut_back.png'}
            className="rec-back-arrow"
            alt="img not found"
          />
          <span className="rec-back-button-text">
            {translations?.language?.[region]?.top_head_option_button_return}
          </span>
        </button>
      <div className="rec-series-title">
        {railImage?.[0] ? (
          <div className="rec-railTitle">
            {state?.sericetitle}:{' '}
            {handleTranslationchange('content_data_abbreviationSeason_label')}{' '}
            {state?.seasonid}
          </div>
        ) : (
          ''
        )}
      </div>
      <div className="episode-recording-list-container">
        {railImage?.[0] ? (
          <div className="record-rail-wrapper continue-wrapper">
            {railImage?.map((each, index, array) => (
              <div className="episode-recording-block">
                <>
                  <button
                  className={`${
                    lockedEpisodeContent.includes(
                      each?.channel?.group_id
                    ) ? "rail_block-recordig-locked focusable" : 
                     "rail_block-recordig focusable"}`
                    }
                    key={index}
                    onClick={() => goToMoviesSeries(each, index)}
                    onFocus={() => {
                      handleFocus(each)
                      setFocusedId(`recordepisode${index}`)
                    }}
                    onBlur={() => handleBlur()}
                    data-testid={`rail_card_click${index}`}
                    id={`recordepisode${index}`}
                    autoFocus={index==0}
                    data-sn-up={
                      Math.ceil((index + 1) / 2) === 1 ? `#recordEpisodeBack` : undefined
                    }
                  >
                    {lockedEpisodeContent.includes(each?.channel?.group_id) && (
                      <div className="locked-channel-icon">
                        <img
                          src={'images/lock_icon_liveTV.png'}
                          className="tag-alq"
                          alt="Locked Channel"
                        />
                      </div>
                    )}
                    {each?.channel?.image ? (
                      <>
                        <LazyLoadImage
                          src={each?.channel?.image}
                          placeholderSrc="images/landscape_card.png"
                          key={index}
                          className={`rail-image ${
                            lockedEpisodeContent.includes(
                              each?.channel?.group_id
                            )
                              ? 'image-layout-lock'
                              : ''
                          }`}
                          id={`railfocus${index}`}
                        />
                      </>
                    ) : (
                      <LazyLoadImage
                        src="images/landscape_card.png"
                        loading="lazy"
                        alt="PlaceHolder"
                        className={`rail-image ${
                          lockedEpisodeContent.includes(each?.channel?.group_id)
                            ? 'image-layout-lock'
                            : ''
                        }`}
                      />
                    )}
                    {state?.page == 'recorded' && (
                      <LazyLoadImage
                        src={'images/Search_Icons/ic_card_play.png'}
                        loading="lazy"
                        alt="PlaceHolder"
                        className={'record-play-icon'}
                        placeholderSrc={'images/Search_Icons/ic_card_play.png'}
                      />
                    )}
                    <div className="deleteIcons-record">
                      <img
                        src={'images/Home_icons/red.png'}
                        className="redDot"
                      />
                      <img
                        src={'images/Home_icons/delete.png'}
                        className="delete"
                      />
                    </div>
                  </button>
                  <div className="record-metadata-block">
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <div className="content-title-rec">
                        {' '}
                        {`${
                          apilanguage?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion2_TextoEpisodio ??
                          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion2_TextoEpisodio'.slice(
                            0,
                            8
                          )
                        }...`}{' '}
                        {each?.channel?.event?.ext_episode_id} :{' '}
                        {each?.channel?.event?.name?.length > 30
                          ? `${each?.channel?.event?.name.slice(0, 27)}...`
                          : each?.channel?.event?.name}
                      </div>{' '}
                    </div>
                    <div className="duration-time">
                      {secondsToHms(each?.channel?.event?.duration)}{' '}
                      {dateToYMD(each?.date)}
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                      <div className="recording-image">
                        <img
                          src={
                            state?.page == 'recorded'
                              ? 'images/Search_Icons/ic_grabado.png'
                              : 'images/LiveTv/startRecordngFocus.png'
                          }
                          className={
                            state?.page == 'recorded'
                              ? 'grabado'
                              : 'img-grabando'
                          }
                        />
                      </div>
                      <div className="record-status">
                        {state?.page == 'recorded'
                          ? handleTranslationchange(
                              'recording_npvr_recordingBlink_label'
                            )
                          : handleTranslationchange(
                              'recording_npvr_recorded_label'
                            )}
                      </div>
                    </div>
                  </div>
                </>
              </div>
            ))}
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default RecordingEpisodeList
