import React from 'react'
const BackButtonComponent = props => {
  return (
    <>
      <button
        className="upper-button-back focusable"
        onMouseOver={e => {
          e.target.focus()
        }}
        onClick={e => {
          props.onCustomClick()
        }}
        id={props.uid}
      >
        <img
          className="yellow-indicator-back"
          src={'images/yellow_shortcut.png'}
        />
        <img className="image-back" src={'images/back_button.png'} />
        <p className="text-back"> {props.text} </p>
      </button>
    </>
  )
}

export { BackButtonComponent }
