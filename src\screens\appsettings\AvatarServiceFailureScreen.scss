.error-page {
  position: fixed;
  width: 1920px;
  height: 1080px;

  .claro-video-logo-container {
    margin: 60px;
    width: 189px;
    height: 40px;
  }

  .error-screen-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 60px;
  }

  .alert-img {
    width: 80px;
    height: 80px;
  }

  .unexpected-error-text,
  .action-incomplete-text {
    margin: 0;
    width: 1120px;
    text-align: center;
    font-family: '<PERSON>o', sans-serif;
    letter-spacing: 0;
  }

  .unexpected-error-text {
    color: #fff;
    font-size: 48px;
    font-weight: bold;
    line-height: 1.19;
  }

  .action-incomplete-text {
    width: 900px;
    color: #eee;
    font-size: 40px;
    line-height: 1.2;
  }

  .accept-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 580.26px;
    height: 82.08px;
    border-radius: 10.03px;
    background-color: #981c15;

    .accept-text {
      color: #fff;
      font-family: '<PERSON>o', sans-serif;
      font-size: 36.5px;
      font-weight: bold;
      letter-spacing: -0.58px;
      line-height: 1.16;
      text-align: center;
      text-transform: uppercase;
    }
  }
}