import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import './VodMovies.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { pushNewInteractionContentEvent } from '../../GoogleAnalytics'
import {
  INTERACTION_PLAYER,
  interactionType
} from '../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const PlayerModelSelection = props => {
  const region = localStorage.getItem('region')

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const showReturnFocus = useSelector(state => state?.search?.setReturnFocus)

  const apilanguage = translations?.language?.[region]

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      if (props?.keyParam == 'search') {
        props?.handleBackInfoTab()
        props?.setSearchValue(props?.searchValue)
        props?.setCurrentButtonFocus(showReturnFocus)
      } else if (props?.keyParam == 'movies' || props?.keyParam == 'series') {
        props?.handleBackInfoTab()
      } else if (props?.keyParam == 'securitypin') {
        props?.handleBackInfoTab()
      }
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      if (props?.keyParam == 'search') {
        props?.handleBackInfoTab()
        props?.setSearchValue(props?.searchValue)
        props?.setCurrentButtonFocus(showReturnFocus)
      } else if (props?.keyParam == 'movies' || props?.keyParam == 'series') {
        props?.handleBackInfoTab()
      } else if (props?.keyParam == 'securitypin') {
        props?.handleBackInfoTab()
      }
    }
  }

  useEffect(() => {
    if (props?.keyParam == 'search') {
      let navtag = document.getElementById('nav-bar-focus')
      navtag && navtag.setAttribute('style', 'display:none')
      return () => {
        navtag && navtag.setAttribute('style', 'display:block')
      }
    }
  }, [])

  const handleUserClick = key => {
    if (props?.keyParam == 'movies') {
      if (key == 'resume') {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.REANUDAR
        )
        props?.handleIconClick('resumenoagerating')
      } else {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.DESDE_EL_PRINCIPIO
        )
        props?.handleIconClick('begin')
      }
    } else if (props?.keyParam == 'search') {
      if (key == 'resume') {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.REANUDAR
        )
        props?.handleIconClick('resume')
        props?.handleClick(props?.selectedPlayBack)
      } else {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.DESDE_EL_PRINCIPIO
        )
        props?.handleIconClick('begin')
        props?.handleClick(props?.selectedPlayBack)
      }
    } else if (props?.keyParam == 'series') {
      if (key == 'resume') {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.REANUDAR
        )
        props?.handleIconClick('resumenoagerating')
      } else {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.DESDE_EL_PRINCIPIO
        )
        props?.handleIconClick('begin')
      }
    } else if (props?.keyParam == 'securitypin') {
      if (key == 'resume') {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.REANUDAR
        )
        props?.handleIconClick('securitypin')
      } else {
        pushNewInteractionContentEvent(
          props?.gaContentData,
          INTERACTION_PLAYER,
          props?.gaContentData?.content_type,
          interactionType.DESDE_EL_PRINCIPIO
        )
        props?.handleIconClick('begin')
      }
    }
  }

  return (
    <div>
      <div className="vod-player-warning-container">
        <div>
          <div className="regresser-box">
            <button
              data-testid="playbutton"
              className="talent-warning-backscreen focusable"
              onClick={() => props?.handleBackInfoTab()}
              data-sn-down={'#resume'}
              id={'regresser'}
            >
              <LazyLoadImage
                className="talent-back-img-icon"
                src={'images/Vcard_Icons/yellowcircle_small.png'}
                placeholderSrc={'images/Vcard_Icons/yellowcircle_small.png'}
              />
              <LazyLoadImage
                className="talent-back-img-icon"
                src={'images/Vcard_Icons/icon_backpage.png'}
                placeholderSrc={'images/Vcard_Icons/icon_backpage.png'}
              />
              <span className="talent-back-button-regresar-title">
                {apilanguage?.BotonShortcut_TextoTitulo_Regresar ??
                  'BotonShortcut_TextoTitulo_Regresar'.slice(0, 7)}
              </span>
            </button>
          </div>
          <div
            className="player-warning-button-container-div"
            style={{ display: 'flex', flexDirection: 'column' }}
          >
            <div style={{ marginBottom: '48px' }}>
              <span className="player-warning-title">
                {apilanguage?.PlayerVOD_ContinuaReproduccion_TextoTitulo ??
                  'PlayerVOD_ContinuaReproduccion_TextoTitulo'.slice(0, 7)}
              </span>
            </div>
            <div className="player-warning-button-div">
              <>
                <button
                  className="player-warning-title-button focusable"
                  //style={currentButtonFocus == 'beginning' ? { background: '#981c15 0% 0% no-repeat padding-box' } : { background: '#2e303d 0% 0% no-repeat padding-box' }}
                  onClick={() => handleUserClick('resume')}
                  autoFocus={true}
                  data-sn-up={'#regresser'}
                  data-sn-down="#continue"
                  id={'resume'}
                >
                  <span className="player-warning-title-button-Contents">
                    {apilanguage?.PlayerVOD_ContinuaReproduccion_BotonPrimario_TextoTitulo
                      ? apilanguage?.PlayerVOD_ContinuaReproduccion_BotonPrimario_TextoTitulo.toUpperCase()
                      : 'PlayerVOD_ContinuaReproduccion_BotonPrimario_TextoTitulo'.slice(
                          0,
                          10
                        )}
                  </span>
                </button>

                <button
                  className="player-warning-title-button focusable"
                  data-testid="resumeTest"
                  onClick={() => handleUserClick('begin')}
                  data-sn-up={'#resume'}
                  id="continue"
                >
                  <span className="player-warning-title-begining-button-Contents">
                    {apilanguage?.PlayerVOD_ContinuaReproduccion_BotonSecundario_TextoTitulo ??
                      'PlayerVOD_ContinuaReproduccion_BotonSecundario_TextoTitulo'.slice(
                        0,
                        10
                      )}
                  </span>
                </button>
              </>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PlayerModelSelection
