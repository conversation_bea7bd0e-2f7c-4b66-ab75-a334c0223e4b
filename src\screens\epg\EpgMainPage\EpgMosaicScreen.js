import React, { useRef } from 'react'
import './EpgMosaicScreen.scss'
import { useSelector } from 'react-redux'
import { useDispatch } from 'react-redux'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import { getEpgChannel } from '../../../store/slices/EpgSlice'
import { mosaicImages } from './EpgMosaicImages'
import { getEpgFilterName } from '../../../store/slices/EpgSlice'
import { pushPlayerInteractionEvent } from '../../../GoogleAnalytics'

const EpgMosaicScreen = ({
  selectedFilter,
  closeMosaicScreen,
  closeFilterScreen,
  closeMainEpgScreen,
  resetFavScreen,
  gaContentData
}) => {
  const dispatch = useDispatch()
  const region = localStorage.getItem('region')
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannelBackup)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const catagoryMossaic = useSelector(
    state => state?.epg?.epgMenu?.response?.nodes
  )
  const epgFilterReduxData = useSelector(
    state => state?.epg?.viewEpgFilteredData
  )
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const todayChannelMossaic =
    epgSevenDaysData?.length > 1 && epgSevenDaysData[1].channelResponse
  const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []
  const liveChannnelInfo = useSelector(state => state?.player?.playerinfo)
  const startTimeRef = useRef(null)
   useEffect(() => {
      startTimeRef.current = Date.now()
      return () => {
        startTimeRef.current = null
      }
    }, [])
  const getIsContractChanel = item => {
    const foundContract = paywayResponse?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return false
    }
    return true
  }

  const setLivePlayer = channelInfo => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    videoElement && videoElement.remove()
    getIsContractChanel(channelInfo?.group_id) &&
      dispatch(getEpgChannel(epgSevenDaysData))

    dispatch(
      getChannelData({
        group_id: channelInfo?.group_id,
        timeshift: channelInfo?.group?.common?.timeshift,
        switchChannel: 'yes',
        epgIndex: channelInfo.index,
        // fromMosaic : true // The below loc may be needed as we noticed an issue with the flow of mosaic
      })
    )
  }

  const handleClick = index => {
    let dataFiltered = []
    let selectedMenuList =
      epgFilterReduxData?.payload[
        catagoryMossaic?.length > 0 && catagoryMossaic[index].text?.toLowerCase()
      ]
    epgSevenDaysData.map(val => {
      let singleDayResponse = val?.channelResponse?.filter(channel =>
        selectedMenuList?.includes(channel.group_id)
      )
      dataFiltered.push({ channelResponse: singleDayResponse })
    })
    if (
      dataFiltered?.length > 0 &&
      dataFiltered[0].channelResponse?.length > 0
    ) {
      dispatch(getEpgChannel(dataFiltered))
      dataFiltered[1].channelResponse[0] &&
        setLivePlayer({ ...dataFiltered[1].channelResponse[0], index })
      return true
    } else {
      return false
    }
  }

  return (
    <div id="mosaicScreen" className="mossaic-main-container">
      <div className="epg-mossaic-header">
        <div className="epg-mossaic-top-button">
          <button
            className="green-button focusable"
            data-sn-right={'.yellow-button'}
            data-sn-down={'.category-item'}
            id='yellowButtonBack'
            onKeyUp={e => {
              if (e.key === 'Enter' || e.keyCode === 13) {
                closeMosaicScreen()
                closeFilterScreen()
                closeMainEpgScreen()
              }
            }}
          >
            <img
              className="green-button-image"
              src={'images/green_shortcut.png'}
            />
            <p className="green-button-text">
              {apilanguage?.top_head_option_button_exit ||
                'top_head_option_button_exit'}
            </p>
          </button>
          <button
            className="yellow-button focusable"
            data-sn-left={'.green-button'}
            data-sn-down={'.category-item'}
            onKeyUp={e => {
              if (e.key === 'Enter' || e.keyCode === 13) {
                closeMosaicScreen()
                // closeFilterScreen()
              }
            }}
          >
            <img
              className="yellow-button-image"
              src={'images/yellow_shortcut.png'}
            />
            <img className="back-image" src={'images/back_button.png'} />
            <p className="yellow-button-text">
              {apilanguage?.top_head_option_button_back ||
                'top_head_option_button_back'}
            </p>
          </button>
        </div>
        <h1>
          {selectedFilter === 'categories'
            ? apilanguage?.filterSelector_modal_option_button_categorieSelector
            : apilanguage?.filterSelector_modal_option_button_channelSelector}
        </h1>
      </div>

      {selectedFilter === 'categories' ? (
        <div className="category-grid-main">
          <div className="catagory-grid-flow">
            {catagoryMossaic?.map((category, index) => (
              <button
                tabIndex={2}
                key={category.id ? category.id : index}
                id={'i-' + index}
                autoFocus={index == 0}
                className="category-item focusable"
                data-sn-up={
                  [0, 1, 2, 3].includes(index) ? '.green-button' : undefined
                }
                data-sn-down={
                  index ==
                    catagoryMossaic?.length -
                      (catagoryMossaic?.length % 4) -
                      1 ||
                  index ==
                    catagoryMossaic?.length - (catagoryMossaic?.length % 4) - 2
                    ? '#i-' + (catagoryMossaic?.length - 1)
                    : '#i-' + (index + 4)
                }
                onFocus={e => {
                  e.target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                  })
                }}
                onKeyUp={e => {
                  if (e.key === 'Enter' || e.keyCode === 13) {
                    if (handleClick(index)) {
                      gaContentData['engagement_time_msec'] = Date.now() - startTimeRef.current
                      pushPlayerInteractionEvent(
                        {
                          modulo_name: 'guia de programacion por categoria',
                          content_id: 'no aplica',
                          content_name: 'no aplica',
                          content_category: 'no aplica',
                          channel_name: liveChannnelInfo?.group?.common?.title?.toLowerCase(),
                          ...gaContentData
                        },
                        category?.text?.toLowerCase()
                      )
                      startTimeRef.current = Date.now()
                      dispatch(getEpgFilterName(category.text))
                      resetFavScreen()
                      closeMosaicScreen()
                      closeFilterScreen()
                    }                   
                  }
                }}
              >
                <img
                  className="catagory-image"
                  src={
                    (category && category?.image) ??
                    mosaicImages?.[category?.text]
                  }
                  alt={category ? category.text : ''}
                />
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="category-grid-main">
          <div className="catagory-grid-flow">
            {todayChannelMossaic?.map((category, index) => (
              <button
                tabIndex={2}
                key={category.id ? category.id : index}
                id={'i' + index}
                autoFocus={index === 0}
                className="category-item focusable channel-bg"
                data-sn-up={
                  [0, 1, 2, 3].includes(index) ? '.green-button' : undefined
                }
                data-sn-down={
                  index ==
                    todayChannelMossaic?.length -
                      (todayChannelMossaic?.length % 4) -
                      1 ||
                  index ==
                    todayChannelMossaic?.length -
                      (todayChannelMossaic?.length % 4) -
                      2
                    ? '#i' + (todayChannelMossaic?.length - 1)
                    : '#i' + (index + 4)
                }
                onFocus={e => {
                  e.target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                  })
                }}
                onKeyUp={e => {
                  if (e.key === 'Enter' || e.keyCode === 13) {
                    gaContentData['engagement_time_msec'] = Date.now() - startTimeRef.current
                    pushPlayerInteractionEvent({
                      modulo_name: 'guia de programacion por canales',
                      content_id: 'no aplica',
                      content_name: 'no aplica',
                      content_category: 'no aplica',
                      channel_name: category?.name?.toLowerCase(),
                      ...gaContentData
                    })
                    startTimeRef.current = Date.now()
                    dispatch(getEpgFilterName(''))
                    dispatch(getEpgChannel(epgSevenDaysData))
                    setLivePlayer({ ...category, index })
                    closeMosaicScreen()
                    closeFilterScreen()
                    closeMainEpgScreen()
                  }
                }}
              >
                <div className="category-title">
                  {category ? category.number : category.id}
                </div>
                <img
                  
                  src={
                    category ? category.image : 'images/mosaic_placeholder.png'
                  }
                  alt="Image"
                />
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default EpgMosaicScreen
