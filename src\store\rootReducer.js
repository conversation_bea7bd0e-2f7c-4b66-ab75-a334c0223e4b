import { combineReducers } from 'redux'
import loginReducer from '../store/slices/login'
import ImageReducer from '../store/slices/Images'
import epgReducer from '../store/slices/EpgSlice'

import epgChannelReducer from '../store/slices/EpgChannelSlice'
import settingsReducer from './slices/settingsSlice'
import homeReducer from './slices/HomeSlice'
import initialReducer from './slices/initialSlices'
import vodMoviesReducer from '../store/slices/VodMoviesSlice'
import profileReducer from '../store/slices/ProfileSlice';

import watchListSlice from './slices/getWatchListSlice'
import getVodSeriesSlice from './slices/vodSeriesSlice'
import SubMenuFilterReducer from './slices/subMenuDataSlice'
import SearchSlice from './slices/SearchSlice'
import PlayerSlice from './slices/PlayerSlice'



export const rootReducer = combineReducers({
  login: loginReducer,
  Images: ImageReducer,
  epg: epgReducer,
  epgChannel: epgChannelReducer,
  homeReducer: homeReducer,
  settingsReducer: settingsReducer,
  initialReducer: initialReducer,
  vodMovies: vodMoviesReducer,
  watchList: watchListSlice,
  getVodSeries: getVodSeriesSlice,
  SubMenuFilter: SubMenuFilterReducer,
  profile:profileReducer,
  search: SearchSlice,
  player:PlayerSlice
  
  
})
