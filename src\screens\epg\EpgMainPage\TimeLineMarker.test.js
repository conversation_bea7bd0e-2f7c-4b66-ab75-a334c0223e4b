import React from "react";
import { fireEvent, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import '@testing-library/jest-dom';
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import { DateMarker } from "./TimeLineMarker";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};


describe('Add Profile page test', () => {
    test('should render api mock data', () => {
             // Mock Date to return a fixed time
    const mockDate = new Date(2022, 0, 1, 0, 0); // Year, Month (0-indexed), Day, Hour, Minute
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
        jest.useFakeTimers();
        renderWithState(<DateMarker />)
        const hour = mockDate.getHours().toString().padStart(2, '0');
        const minute = mockDate.getMinutes().toString().padStart(2, '0');
        expect(hour+':'+minute).toBe('00:00')
        jest.advanceTimersByTime(60000);
        global.Date.mockRestore();
        // jest.useFakeTimers();

        // jest.advanceTimersByTime(1000);

    })

})