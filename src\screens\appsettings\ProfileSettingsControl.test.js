import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ProfileSettingsControl from "./ProfileSettingsControl";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<MemoryRouter
			history={history}
			initialEntries={[{ state: { pageName: 'parentalControl' } }]}
		>
			{children}
		</MemoryRouter>
	</Provider>
);

export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper });
};

const mockErrorResponse = {
	"status": "1"
}

const mockIsLoggedInSuccessResponse = {
	response: {
		session_stringvalue: "ZTEATV412001224226580292a69e33",
		user_id: "********",
		user_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.85CTxu2wUyWObpGgpBiIEcGWwdGeGonriZotenfEtOk"
	}
}

describe('Profile Settings control Landing page test', () => {
	global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };

	test('should render without api mock data', () => {
		initialState.login = {
			isLoggedIn: mockErrorResponse
		}
		renderWithState(<ProfileSettingsControl />)
	})
	test('should render with api mock data', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		}
		renderWithState(<ProfileSettingsControl />)
	})

	test('open language page', () => {
		const { container } = renderWithState(<ProfileSettingsControl />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'idiomaButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		fireEvent.keyUp(buttonClick, { keyCode: '461' })
	})

	test('open reminders page', () => {
		const { container } = renderWithState(<ProfileSettingsControl />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'recordatoriosButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('open parental control page', () => {
		const { container } = renderWithState(<ProfileSettingsControl />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'parentalControlButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		history.push('/settings/profile-settings')
	})

})
