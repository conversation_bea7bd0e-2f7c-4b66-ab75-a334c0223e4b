import React, { useEffect, useState, useCallback } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import Lottie from 'react-lottie-player'
import {
  getPaywayWorkflow,
  getSubscribePlan,
  getClient,
  getAvailableCreditCards,
  clrSubscriptionInfo,
  addTelcelPayment
} from '../../../store/slices/settingsSlice'
import './BuySubscriptionNew.scss'
import { useDispatch } from 'react-redux'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { BackButtonComponent } from '../../../components/CommonComponent'
import animationData from '../../../json/animationData.json'
import TransactionsError from './TransactionsError'

const BuySubscriptionNew = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const region = localStorage.getItem('region')
  const { state } = useLocation()
  const viewData = useSelector(
    state => state?.settingsReducer?.viewSubscribeData
  )
  const [selectedPaymentMethodName, setSelectedPaymentMethodName] = useState('')
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const workflowResponse = useSelector(
    state => state?.settingsReducer?.paywayWorkFlow?.response
  )
  const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
  const claroPagosGateConfig =
    apaMeta?.claropagos_configuration &&
    JSON?.parse(apaMeta?.claropagos_configuration)?.[region]?.request
  const translations =
    apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const apilanguage = translations?.language?.[region]
  const [subscriptionMethodText, setSubscriptionMethodText] = useState('')
  const [defaultPaymentMethodInfo, setDefaultPaymentMethodInfo] = useState({})
  const [claroPagosPayment, setClaroPagosPayment] = useState([])
  const [purchaseType, setPurchaseType] = useState('')
  const [episodeName, setEpisodeName] = useState('')
  const [episodeNumber, setEpisodeNumber] = useState(0)
  const [seasonNumber, setSeasonNumber] = useState(0)
  const [paymentError, setPaymentError] = useState(false)
  const [paymentErrorResponse, setPaymentErrorResponse] = useState({})

  const fixedPaymentResponse = useSelector(
    state => state?.settingsReducer?.subscribePlan
  )

  const selectedPaymentError = useSelector(
    state => state?.settingsReducer?.error
  )
  const paymentProcessLoading = useSelector(
    state => state?.settingsReducer?.isLoading
  )
  const getCustomerDetails = useSelector(
    state => state?.settingsReducer?.getClientDetails
  )
  const addedCreditCardDetails = useSelector(
    state => state?.settingsReducer?.addCardDetails
  )
  const vodSeriesCastRedux = useSelector(
    state => state?.getVodSeries?.seriesCastData
  )
  const telcelAddPayment = useSelector(
    state => state?.settingsReducer?.telcelPayment
  )
  const episodeData = useSelector(state => state?.getVodSeries?.episodeMoreInfo)

  useEffect(() => {
    if (workflowResponse && Object.keys(workflowResponse)?.length > 0) {
      let workFlowPaymentMethods =
        workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list
      let claroPagos = workFlowPaymentMethods?.filter(item => {
        return item?.gateway == 'claropagosgate'
      })
      setClaroPagosPayment(claroPagos)
    }
  }, [workflowResponse])

  useEffect(() => {
    getCustomerDetails &&
      Object.keys(getCustomerDetails)?.length > 0 &&
      dispatch(
        getAvailableCreditCards({
          url: claroPagosGateConfig?.server_url,
          clientId: getCustomerDetails?.data?.cliente?.id,
          headerToken:
            claroPagosGateConfig?.create_client?.header?.authorization
        })
      )
  }, [getCustomerDetails])

  useEffect(() => {
    vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.number &&
      setEpisodeNumber(
        vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.number
      )
    vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.season &&
      setSeasonNumber(
        vodSeriesCastRedux?.common?.extendedcommon?.media?.episode?.season
      )
    vodSeriesCastRedux?.common?.title &&
      setEpisodeName(vodSeriesCastRedux?.common?.title)
  }, [vodSeriesCastRedux])

  useEffect(() => {
    if (viewData?.offertype == 'subscrition') {
      if (viewData?.isPremiumChannel) {
        setPurchaseType('PagarAddon')
      } else if (viewData?.isMultiPack) {
        setPurchaseType('ContratarPaquetes')
      } else {
        setPurchaseType('Suscripcion')
      }
    } else if (
      viewData?.offertype &&
      (viewData?.offertype == 'buy' || viewData?.offertype == 'download_buy')
    ) {
      setPurchaseType('Compra')
    } else {
      setPurchaseType('Renta')
    }
  }, [viewData])

  const handlePayments = () => {
    if (selectedPaymentMethodName && workflowResponse?.hasSavedPayway == '1') {
      handleBuySubscription()
    } else {
      navigate('/my-settings/my-Accounts/manage-payments/payments-method', {
        state: {
          pageName:
            '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          previousPageName: state?.pageName,
          vodData: state?.vodData,
          returnPage: state?.returnPage,
          dataId: state?.dataId ?? state?.data,
          data: state?.data ?? state?.dataId,
          subscribeInfoData: state?.subscribeInfoData,
          sendIndex: state?.sendIndex,
          previousPage: state?.previousPage,
          selectedPaymentMethodName: selectedPaymentMethodName,
          selectedPaymentIndex: state?.selectedPaymentIndex
        }
      })
    }
  }

  const handleCondition = () => {
    if (
      workflowResponse?.hasSavedPayway == '1' &&
      selectedPaymentMethodName != 'amcogate' &&
      selectedPaymentMethodName?.length > 0 &&
      !viewData?.isMultiPack &&
      !viewData?.isPremiumChannel
    ) {
      navigate('/my-settings/my-Accounts/manage-payments/payments-method', {
        state: {
          pageName:
            '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          previousPageName: state?.pageName,
          vodData: state?.vodData,
          returnPage: state?.returnPage,
          dataId: state?.dataId ?? state?.data,
          data: state?.data ?? state?.dataId,
          sendIndex: state?.sendIndex,
          previousPage: state?.previousPage,
          selectedPaymentMethodName: selectedPaymentMethodName,
          selectedPaymentIndex: state?.selectedPaymentIndex
        }
      })
    } else handleGoPreviousPage()
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleBuySubscription = () => {
    if (claroPagosPayment?.[0]?.paymentMethodData?.cardsData?.length > 0) {
      navigate('/my-settings/my-Accounts/manage-payments/addPaymentMethods', {
        state: {
          paymentMethod: 'claropagosgate',
          previousPageName: state?.previousPageName,
          previousPage: state?.previousPage,
          vodData: state?.vodData,
          dataId: state?.dataId ?? state?.data,
          data: state?.data ?? state?.dataId,
          pageName: state?.pageName,
          returnPage: state?.returnPage,
          sendIndex: state?.sendIndex,
          subscribeInfoData: state?.subscribeInfoData,
          selectedPaymentIndex: state?.selectedPaymentIndex,
          confirmCvv: true
        }
      })
    } else if (
      addedCreditCardDetails &&
      Object.keys(addedCreditCardDetails)?.length > 0
    ) {
      navigate('/my-settings/my-Accounts/manage-payments/addPaymentMethods', {
        state: {
          paymentMethod: 'claropagosgate',
          previousPageName: state?.previousPageName,
          previousPage: state?.previousPage,
          vodData: state?.vodData,
          dataId: state?.dataId ?? state?.data,
          data: state?.data ?? state?.dataId,
          pageName: state?.pageName,
          returnPage: state?.returnPage,
          sendIndex: state?.sendIndex,
          subscribeInfoData: state?.subscribeInfoData,
          selectedPaymentIndex: state?.selectedPaymentIndex,
          confirmCvv:
            addedCreditCardDetails &&
            Object.keys(addedCreditCardDetails)?.length > 0
              ? true
              : false
        }
      })
    } else if (selectedPaymentMethodName == 'promogate') {
      navigate('/my-settings/my-Accounts/manage-payments/addPaymentMethods', {
        state: {
          paymentMethod: 'promogate',
          previousPageName: state?.previousPageName,
          previousPage: state?.previousPage,
          vodData: state?.vodData,
          dataId: state?.dataId ?? state?.data,
          data: state?.data ?? state?.dataId,
          pageName: state?.pageName,
          returnPage: state?.returnPage,
          sendIndex: state?.sendIndex,
          subscribeInfoData: state?.subscribeInfoData,
          selectedPaymentIndex: state?.selectedPaymentIndex
        }
      })
    } else if (defaultPaymentMethodInfo?.gatewaytext == 'Telcel') {
      dispatch(
        addTelcelPayment({
          apiUrl: defaultPaymentMethodInfo?.buyLink,
          userToken: userDetails?.user_token,
          payway: defaultPaymentMethodInfo?.gateway,
          userId: userDetails?.parent_id,
          existingTelcel: true
        })
      )
    } else {
      dispatch(
        getSubscribePlan({
          buyToken: defaultPaymentMethodInfo?.buyToken,
          userToken: userDetails?.user_token,
          apiUrl: defaultPaymentMethodInfo?.buyLink,
          paywayGate: defaultPaymentMethodInfo?.gateway,
          userId: userDetails?.user_id,
          hks: userDetails?.session_stringvalue
        })
      )
    }
  }

  const defaultPaymentMethodReceipt = defaultPayment => {
    let pagoText = ''
    if (viewData?.isPremiumChannel) {
      pagoText = 'Transaccionales_PagarAddon_TextoMedioDePago'
    } else if (viewData?.isMultiPack) {
      pagoText = 'Transaccionales_ContratarPaquetes_TextoMedioDePago'
    } else if (purchaseType == 'Compra') {
      pagoText = viewData?.isSeries
        ? viewData?.episodePurchase
          ? 'Transaccionales_CompraEpisodioCheckoutA_TextoPagaCon'
          : 'Transaccionales_CompraTemporadaCheckoutA_TextoPagaCon'
        : 'Transaccionales_CompraCheckoutA_TextoPagaCon'
    } else if (purchaseType == 'Renta') {
      pagoText = viewData?.isSeries
        ? viewData?.episodePurchase
          ? 'Transaccionales_CompraEpisodioCheckoutA_TextoPagaCon'
          : 'Transaccionales_CompraTemporadaCheckoutA_TextoPagaCon'
        : 'Transaccionales_CompraCheckoutA_TextoPagaCon'
    } else {
      pagoText = 'Transaccionales_SuscripcionCheckoutA_TextoPagaCon'
    }
    switch (defaultPayment?.gateway) {
      case 'hubfacturafijagate':
        return `${truncateText(pagoText, 30)} ${truncateText(
          'ticket_subscription_hubfacturafijagate_label',
          30
        )} ${subscriptionMethodText}`
      case 'hubgate':
        return `${truncateText(pagoText, 30)} ${truncateText(
          'ticket_subscription_hubgate_label',
          30
        )} ${subscriptionMethodText}`
      case 'amcogate':
        return `${truncateText(pagoText, 30)} ${defaultPayment?.gatewaytext}`
      case 'claropagosgate':
        return claroPagosPayment?.[0]?.paymentMethodData?.cardsData?.length > 0
          ? `${truncateText(pagoText, 30)} ${truncateText(
              'ticket_subscription_claropagosgate_label',
              30
            )} ${subscriptionMethodText}`
          : ''
      default:
        return ` ${truncateText(pagoText, 30)} ${truncateText(
          'ticket_subscription_promogate_label',
          30
        )}`
    }
  }

  useEffect(() => {
    try {
      let responseObj =
        defaultPaymentMethodInfo?.gatewaytext == 'Telcel'
          ? telcelAddPayment
          : fixedPaymentResponse
      let errorObj = selectedPaymentError
      if (responseObj && responseObj.errors) {
        setPaymentError(true)
        setPaymentErrorResponse(responseObj)
        let error = responseObj?.errors[0]?.message
        console.error('ERROR===> in on click payment', error)
        //BELOW RESPONSE IS CHECKING ON THE BEHALF OF RENT MOVIES BY FIXED PAYMENT
      } else if(errorObj && Object.keys(errorObj)?.length > 0) {
        setPaymentError(true)
        setPaymentErrorResponse(errorObj)
      } else if (
        responseObj?.response?.purchase_data &&
        responseObj?.response?.uuid
      ) {
        navigate(
          '/my-settings/my-Accounts/manage-payments/payment/finish-subscription',
          {
            state: {
              endDay: responseObj?.response?.end_date.day,
              endMonth: responseObj?.response?.end_date.month,
              endYear: responseObj?.response?.end_date.year,
              paymentAccount: responseObj?.response?.paymentMethodData?.account,
              vodData: state?.vodData,
              pageName: state?.pageName,
              dataId: state?.dataId ?? state?.data,
              paymentGateway: defaultPaymentMethodInfo?.gatewaytext,
              subscribeInfoData: state?.subscribeInfoData,
              paymentMethod: defaultPaymentMethodInfo?.gateway
            }
          }
        )
      }
    } catch (error) {
      console.error('error in response Parse for fixed payment', error)
    }
  }, [fixedPaymentResponse, telcelAddPayment, selectedPaymentError])

  const getScopeDescription = data => {
    return apilanguage?.[data + '_scope_description_label']
  }

  const getDescriptionLabel = data => {
    return apilanguage?.[data + '_disclaimer_description_label']
  }

  useEffect(() => {
    //Collecting Default Previous Payment
    workflowResponse?.selectedPaymentMethod &&
      setSelectedPaymentMethodName(workflowResponse?.selectedPaymentMethod)
    let workflowPayment =
      workflowResponse?.workflow?.listBuyLinks ?? workflowResponse?.list

    let selectedPaymentMethodDetail = workflowPayment?.filter(payment => {
      return payment?.gateway == workflowResponse?.selectedPaymentMethod
    })

    let changePaymentMethod = state?.selectedPaymentIndex
      ? workflowPayment?.[state?.selectedPaymentIndex]
      : null

    setDefaultPaymentMethodInfo(
      state?.selectedPaymentIndex
        ? changePaymentMethod
        : selectedPaymentMethodDetail?.[0]
    )

    setSubscriptionMethodText(
      state?.selectedPaymentIndex
        ? changePaymentMethod?.paymentMethodData?.account?.slice(-8)
        : selectedPaymentMethodDetail?.[0]?.paymentMethodData?.cardsData?.[0]?.pan?.slice(
            -8
          ) ??
            selectedPaymentMethodDetail?.[0]?.paymentMethodData?.account?.slice(
              -8
            )
    )
    document.getElementById('buySubscriptionButton')?.focus()
    addedCreditCardDetails &&
      Object.keys(addedCreditCardDetails)?.length > 0 &&
      setSubscriptionMethodText(
        addedCreditCardDetails?.data?.tarjeta?.pan?.slice(8)
      )
  }, [workflowResponse, state, addedCreditCardDetails])

  useEffect(() => {
    dispatch(
      getPaywayWorkflow({
        workflow: viewData?.workflowStart,
        hks: userDetails?.session_stringvalue
      })
    )
    region == 'mexico' &&
      dispatch(
        getClient({
          url: claroPagosGateConfig?.server_url,
          email: userDetails.email,
          headerToken:
            claroPagosGateConfig?.create_client?.header?.authorization
        })
      )
  }, [])

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009 || keycode === 8) {
      handleCondition()
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || keycode == 89) {
      handleCondition()
    }
  }

  const closeErrorScreen = () => {
    setPaymentError(false)
  }

  const handleGoPreviousPage = () => {
    if (state?.pageName === '/home') {
      localStorage.setItem('currNavIdx', 0)
      navigate(state?.pageName, {
        state: {
          data: state?.data ?? state?.dataId,
          vodData: state?.vodData,
          pageName: state?.previousPage,
          media: state?.media,
          focusIndex: state?.focusIndex
        }
      })
    } else if (props?.pageName === 'vodPlayer') {
      props?.showBuySubs(false)
      return props?.showFin(true)
    } else if (
      state?.pageName ==
        '/my-settings/my-subscriptions/add-subscriptions/viewDetails' ||
      state?.previousPageName ==
        '/my-settings/my-subscriptions/add-subscriptions/viewDetails'
    ) {
      navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
        state: {
          data: state?.data ?? state?.dataId,
          vodData: state?.vodData,
          pageName: state?.previousPage,
          sendIndex: state?.sendIndex,
          returnPage: state?.returnPage,
          previousPageName: state?.previousPageName,
          inputValue: state?.inputValue,
          media: state?.media,
          subscribeInfoData: state?.subscribeInfoData,
          focusIndex: state?.focusIndex,
          groupId: state?.groupId
        }
      })
    } else {
      dispatch(clrSubscriptionInfo())
      const pageName =
        state?.previousPageName || state?.previousPage || state?.pageName
      pageName === '/movies' ||
      pageName === '/series' ||
      pageName === '/premiumSubscription' ||
      pageName == '/multiSubscription'
        ? navigate(pageName, {
            state: {
              ...(pageName === '/movies' && {
                vodData: state?.vodData ?? state?.dataId,
                vCardBackFocus: state?.vCardBackFocus
              }),
              ...(pageName === '/series' && {
                data: state?.data ?? state?.dataId,
                vCardBackFocus: state?.vCardBackFocus
              }),
              ...(pageName === '/premiumSubscription' && {
                data: state?.data ?? state?.dataId,
                subscribeInfoData: state?.subscribeInfoData,
                returnPage: state?.returnPage,
                groupId: state?.groupId
              }),
              ...(pageName === '/multiSubscription' && {
                data: state?.data ?? state?.dataId,
                backIndex: state?.sendIndex,
                pageName: state?.returnPage
              })
            }
          })
        : state?.pageName ==
          '/my-settings/my-subscriptions/add-subscriptions/subscribe-new'
        ? navigate('/home')
        : navigate(state?.pageName, {
            state: {
              data: state?.data ?? state?.dataId,
              vodData: state?.vodData,
              pageName: state?.previousPage,
              sendIndex: state?.sendIndex,
              media: state?.media,
              subscribeInfoData: state?.subscribeInfoData,
              selectedPaymentIndex: state?.selectedPaymentIndex
            }
          })
    }
  }

  const displayBuyButtonText = paymentMethod => {
    if (workflowResponse?.hasSavedPayway == '1' && paymentMethod?.length > 0) {
      if (viewData?.isPremiumChannel) {
        return truncateText('Transaccionales_PagarAddon_TextoBotonPrimario', 13)
      } else if (viewData?.isMultiPack) {
        return truncateText(
          'Transaccionales_ContratarPaquetes_TextoBotonPrimario',
          13
        )
      } else if (purchaseType == 'Suscripcion') {
        return truncateText(
          'Transaccionales_SuscripcionCheckoutA_TextoBotonPrimarioContratar',
          13
        )
      } else if (purchaseType == 'Compra') {
        return viewData?.isSeries
          ? viewData?.episodePurchase
            ? truncateText(
                'Transaccionales_CompraEpisodioCheckoutA_TextoBotonPrimarioComprar',
                10
              )
            : truncateText(
                'Transaccionales_CompraTemporadaCheckoutA_TextoBotonPrimarioComprar',
                10
              )
          : truncateText(
              'Transaccionales_CompraCheckoutA_TextoBotonPrimarioComprar',
              10
            )
      } else if (purchaseType == 'Renta') {
        return truncateText(
          'Transaccionales_RentaCheckoutA_TextoBotonPrimarioComprar',
          10
        )
      } else {
        return truncateText(
          'Transaccionales_SuscripcionCheckoutA_TextoBotonPrimarioContratar',
          13
        )
      }
    } else {
      if (viewData?.isPremiumChannel) {
        return truncateText('Transaccionales_PagarAddon_TextoBotonPrimario', 25)
      } else if (viewData?.isMultiPack) {
        return truncateText(
          'Transaccionales_ContratarPaquetes_TextoBotonPrimario',
          25
        )
      } else if (purchaseType == 'Suscripcion') {
        return truncateText(
          'Transaccionales_SuscripcionCheckoutA_TextoBotonPrimarioAgregarmdp',
          25
        )
      } else if (purchaseType == 'Compra') {
        return viewData?.isSeries
          ? viewData?.episodePurchase
            ? truncateText(
                'Transaccionales_CompraEpisodioCheckoutA_TextoBotonPrimarioAgregarmdp',
                25
              )
            : truncateText(
                'Transaccionales_CompraTemporadaCheckoutA_TextoBotonPrimarioAgregarmdp',
                25
              )
          : truncateText(
              'Transaccionales_CompraCheckoutA_TextoBotonPrimarioAgregarmdp',
              25
            )
      } else {
        return truncateText(
          'Transaccionales_RentaCheckoutA_TextoBotonPrimarioAgregarmdp',
          25
        )
      }
    }
  }

  const displaySecondaryButtonText = paymentMethod => {
    if (
      workflowResponse?.hasSavedPayway == '1' &&
      paymentMethod != 'amcogate' &&
      paymentMethod?.length > 0
    ) {
      if (
        purchaseType == 'Suscripcion' ||
        viewData?.isPremiumChannel ||
        viewData?.isMultiPack
      ) {
        return truncateText(
          'Transaccionales_SuscripcionCheckoutA_TextoBotonSecundarioCambiarmdp',
          23
        )
      } else if (purchaseType == 'Compra') {
        return viewData?.isSeries
          ? viewData?.episodePurchase
            ? truncateText(
                'Transaccionales_CompraEpisodioCheckoutA_TextoBotonSecundarioCambiarmdp',
                23
              )
            : truncateText(
                'Transaccionales_CompraTemporadaCheckoutA_TextoBotonSecundarioCambiarmdp',
                23
              )
          : truncateText(
              'Transaccionales_CompraCheckoutA_TextoBotonSecundarioCambiarmdp',
              23
            )
      } else {
        return truncateText(
          'Transaccionales_RentaCheckoutA_TextoBotonSecundarioCambiarmdp',
          23
        )
      }
    } else {
      if (viewData?.isPremiumChannel) {
        return truncateText(
          'Transaccionales_PagarAddon_TextoBotonSecundario',
          10
        )
      } else if (viewData?.isMultiPack) {
        return truncateText(
          'Transaccionales_ContratarPaquetes_TextoBotonSecundario',
          10
        )
      } else if (purchaseType == 'Suscripcion') {
        return truncateText(
          'Transaccionales_SuscripcionCheckoutA_TextoBotonSecundarioCancelar',
          10
        )
      } else if (purchaseType == 'Compra') {
        return viewData?.isSeries
          ? viewData?.episodePurchase
            ? truncateText(
                'Transaccionales_CompraEpisodioCheckoutA_TextoBotonSecundarioCancelar',
                10
              )
            : truncateText(
                'Transaccionales_CompraTemporadaCheckoutA_TextoBotonSecundarioCancelar',
                10
              )
          : truncateText(
              'Transaccionales_CompraCheckoutA_TextoBotonSecundarioCancelar',
              10
            )
      } else {
        return truncateText(
          'Transaccionales_RentaCheckoutA_TextoBotonSecundarioCancelar',
          10
        )
      }
    }
  }

  const displayPageTitle = () => {
    if (viewData?.isPremiumChannel) {
      return ''
    } else if (viewData?.isMultiPack) {
      return truncateText('Transaccionales_ContratarPaquetes_TextoTitulo', 20)
    } else if (purchaseType == 'Compra') {
      return viewData?.episodePurchase == true
        ? truncateText(
            'Transaccionales_CompraEpisodioCheckoutA_TextoTitulo',
            20
          )
        : truncateText('Transaccionales_CompraCheckoutA_TextoTitulo', 20)
    } else if (purchaseType == 'Renta') {
      return viewData?.episodePurchase == true
        ? truncateText('Transaccionales_RentaEpisodioCheckoutA_TextoTitulo', 20)
        : truncateText('Transaccionales_RentaCheckoutA_TextoTitulo', 20)
    } else {
      return truncateText(
        'Transaccionales_SuscripcionCheckoutA_TextoTitulo',
        20
      )
    }
  }

  const displayPremiumLiveContract = () => {
    return (
      <div className="text-area">
        <img
          className="buy-sub-logo"
          src={viewData?.logo || 'images/CV_MENSUAL.png'}
        />
        <span className="free-detail-wrapper">
          <span className="free-charge-string-live">
            {truncateText(
              `Transaccionales_PagarAddon_TextoOferta_${viewData?.producttype}`,
              30
            )}
          </span>
          <span className="free-scope-live">
            {truncateText('Transaccionales_PagarAddon_TextoOferta2', 15)}
          </span>
        </span>
        <span className="live-contract-total">
          {`${viewData?.currency}${viewData?.price} ${truncateText(
            `Transaccionales_${purchaseType}CheckoutA_TextoDiagonal`,
            5
          )} ${truncateText(viewData?.periodicity, 8)}`}
        </span>
        <span className="live-scope-description">
          {truncateText(
            `Transaccionales_PagarAddon_TextoPromoción_${viewData?.producttype}`,
            40
          )}
        </span>
        <span className="subscription-info live-subscription-info">
          <>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_PagarAddon_TextoComplementario1_${viewData?.producttype}`,
                45
              )}
            </span>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_PagarAddon_TextoComplementario2_${viewData?.producttype}`,
                35
              )}
            </span>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_PagarAddon_TextoComplementario3_${viewData?.producttype}`,
                40
              )}
            </span>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_PagarAddon_TextoComplementario4_${viewData?.producttype}`,
                40
              )}
            </span>
          </>
        </span>
      </div>
    )
  }

  const displayMultiPackContract = () => {
    return (
      <div className="text-area">
        <img
          className="multi-live-buy-sub-logo"
          src={viewData?.logo || 'images/checkout_logo_placeholder.png'}
        />
        <span className="multi-live-contract-total-wrapper">
          <span className="multi-live-contract-total">
            {`${viewData?.currency}${viewData?.price} ${truncateText(
              `Transaccionales_${purchaseType}CheckoutA_TextoDiagonal`,
              2
            )} ${truncateText(viewData?.periodicity, 8)}  `}
          </span>
          <span className="multi-live-tax">
            {truncateText(
              `Transaccionales_ContratarPaquetes_TextoImpuestos_${viewData?.producttype}`,
              15
            )}
          </span>
        </span>
        <span className="subscription-info multi-live-subscription-info">
          <>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_ContratarPaquetes_TextoComplementario1_${viewData?.producttype}`,
                45
              )}
            </span>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_ContratarPaquetes_TextoComplementario2_${viewData?.producttype}`,
                35
              )}
            </span>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_ContratarPaquetes_TextoComplementario3_${viewData?.producttype}`,
                40
              )}
            </span>
            <span className="live-checkout-description">
              {truncateText(
                `Transaccionales_ContratarPaquetes_TextoComplementario4_${viewData?.producttype}`,
                40
              )}
            </span>
          </>
        </span>
      </div>
    )
  }

  const displaySubscription = () => {
    return (
      <div className="text-area">
        <div className="buy-image-wrapper">
          <img
            className="multi-live-buy-sub-logo"
            src={viewData?.logo || 'images/CV_MENSUAL.png'}
          />
        </div>
        <span className="suscripcion-total-wrapper">
          <span className="total-suscripcion-label">
            {truncateText('Transaccionales_SuscripcionCheckoutA_TextoTotal', 8)}
          </span>
          <span
            className={`total-suscripcion-price ${
              viewData?.price?.length == 3 ? 'total-3digit-price' : ''
            }`}
          >{`${viewData?.currency}${viewData?.price}`}</span>
          <span className="total-suscripcion-periodicity">{`${truncateText(
            `Transaccionales_SuscripcionCheckoutA_TextoDiagonal`,
            2
          )} ${truncateText(viewData?.periodicity, 8)}`}</span>
        </span>
        <span className="tax-label">
          {truncateText(
            `Transaccionales_SuscripcionCheckoutA_TextoImpuestos_${viewData?.producttype}`,
            15
          )}
        </span>
        <span className="free-detail-wrapper">
          <span className="free-charge-string-live">
            {truncateText(
              `Transaccionales_SuscripcionCheckoutA_TextoOferta_${viewData?.producttype}`,
              30
            )}
          </span>
        </span>
        <span className="live-scope-description">
          {truncateText(
            `Transaccionales_SuscripcionCheckoutA_TextoPromocion_${viewData?.producttype}`,
            40
          )}
        </span>
        <span className="subscription-info live-subscription-info">
          <span className="suscripcion-checkout-description">
            {`${truncateText(
              `Transaccionales_SuscripcionCheckoutA_TextoPoliticas1_${viewData?.producttype}`,
              35
            )} ${truncateText(
              `Transaccionales_SuscripcionCheckoutA_TextoPoliticas2_${viewData?.producttype}`,
              45
            )} ${truncateText(
              `Transaccionales_SuscripcionCheckoutA_TextoPoliticas3_${viewData?.producttype}`,
              40
            )} ${truncateText(
              `Transaccionales_SuscripcionCheckoutA_TextoPoliticas4_${viewData?.producttype}`,
              40
            )}`}
          </span>
        </span>
      </div>
    )
  }

  const displayCompra = () => {
    return (
      <div className="text-area">
        <div className="asset-container">
          <span className="asset-title">{viewData?.title}</span>
          {seasonNumber && viewData?.isSeries && region == 'mexico' && (
            <span className="asset-title">
              {episodeData?.episodeItem?.episode_number &&
              viewData?.episodePurchase
                ? `Temp. ${seasonNumber} | Ep. ${episodeNumber} ${episodeData}`
                : `${truncateText(
                    `Transaccionales_CompraTemporadaCheckoutA_TextoAbreParentesis`,
                    2
                  )} Temporada ${seasonNumber} ${truncateText(
                    `Transaccionales_CompraTemporadaCheckoutA_TextoCierraParentesis`,
                    2
                  )}`}
            </span>
          )}
          {episodeData?.episodeItem?.episode_number &&
            viewData?.episodePurchase && (
              <span className="asset-title">{episodeName}</span>
            )}
        </div>
        <span className="suscripcion-total-wrapper">
          <span className="total-suscripcion-label">
            {truncateText(
              viewData?.isSeries
                ? viewData?.episodePurchase
                  ? 'Transaccionales_CompraEpisodioCheckoutA_TextoTotal'
                  : 'Transaccionales_CompraTemporadaCheckoutA_TextoTotal'
                : 'Transaccionales_CompraCheckoutA_TextoTotal',
              8
            )}
          </span>
          <span className="total-suscripcion-price total-compra-price">{`${viewData?.currency}${viewData?.price}`}</span>
        </span>
        <span className="tax-label">
          {truncateText(
            viewData?.isSeries
              ? viewData?.episodePurchase
                ? `Transaccionales_CompraEpisodioCheckoutA_TextoImpuestos_${viewData?.producttype}`
                : `Transaccionales_CompraTemporadaCheckoutA_TextoImpuestos_${viewData?.producttype}`
              : `Transaccionales_CompraCheckoutA_TextoImpuestos_${viewData?.producttype}`,
            15
          )}
        </span>
        <span className="subscription-info live-subscription-info">
          <span className="compra-checkout-description">
            {viewData?.isSeries
              ? viewData?.episodePurchase
                ? `${truncateText(
                    `Transaccionales_CompraEpisodioCheckoutA_TextoPoliticas1_${viewData?.producttype}`,
                    40
                  )} ${truncateText(
                    `Transaccionales_CompraEpisodioCheckoutA_TextoPoliticas2_${viewData?.producttype}`,
                    20
                  )}`
                : `${truncateText(
                    `Transaccionales_CompraTemporadaCheckoutA_TextoPoliticas1_${viewData?.producttype}`,
                    40
                  )} ${truncateText(
                    `Transaccionales_CompraTemporadaCheckoutA_TextoPoliticas2_${viewData?.producttype}`,
                    20
                  )} ${truncateText(
                    `Transaccionales_CompraTemporadaCheckoutA_TextoPoliticas3_${viewData?.producttype}`,
                    5
                  )}`
              : `${truncateText(
                  `Transaccionales_CompraCheckoutA_TextoPoliticas1_${viewData?.producttype}`,
                  30
                )} ${truncateText(
                  `Transaccionales_CompraCheckoutA_TextoPoliticas2_${viewData?.producttype}`,
                  40
                )} ${truncateText(
                  `Transaccionales_CompraCheckoutA_TextoPoliticas3_${viewData?.producttype}`,
                  15
                )} ${truncateText(
                  `Transaccionales_CompraCheckoutA_TextoPoliticas4_${viewData?.producttype}`,
                  5
                )}`}
          </span>
        </span>
      </div>
    )
  }

  const displayRenta = () => {
    return (
      <div className="text-area">
        <div className="asset-container">
          <span className="asset-title">{viewData?.title}</span>
        </div>
        <span className="renta-total-wrapper">
          <span className="total-suscripcion-label">
            {truncateText('Transaccionales_RentaCheckoutA_TextoTotal', 8)}
          </span>
          <span className="total-suscripcion-price">{`${viewData?.currency}${viewData?.price}`}</span>
          <span className="total-suscripcion-periodicity">{`${truncateText(
            `Transaccionales_SuscripcionCheckoutA_TextoDiagonal`,
            2
          )} ${viewData?.frequency} ${truncateText(
            viewData?.periodicity,
            8
          )}`}</span>
        </span>
        <span className="tax-label">
          {truncateText(
            `Transaccionales_RentaCheckoutA_TextoImpuestos_${viewData?.producttype}`,
            15
          )}
        </span>
        <span className="subscription-info live-subscription-info">
          <span className="renta-checkout-description">
            {`${truncateText(
              `Transaccionales_RentaCheckoutA_TextoPoliticas1_${viewData?.producttype}`,
              35
            )} ${truncateText(
              `Transaccionales_RentaCheckoutA_TextoPoliticas2_${viewData?.producttype}`,
              40
            )} ${truncateText(
              `Transaccionales_RentaCheckoutA_TextoPoliticas3_${viewData?.producttype}`,
              15
            )} ${truncateText(
              `Transaccionales_RentaCheckoutA_TextoPoliticas4_${viewData?.producttype}`,
              5
            )}`}
          </span>
        </span>
      </div>
    )
  }

  return (
    <>
      {paymentError ? (
        <TransactionsError
          paymentErrorResponse={paymentErrorResponse}
          handleGoPreviousPage={closeErrorScreen}
        />
      ) : (
        <div
        className={`checkout-subsription-page ${
          paymentProcessLoading ? 'checkout-subscription-page-loader' : ''
        }`}
      >
        <div className="upper-back-button-area">
          <BackButtonComponent
            uid={'subscription-page-back'}
            onCustomClick={handleGoPreviousPage}
            text={truncateText('BotonShortcut_TextoTitulo_Regresar', 10)}
          />
        </div>
        {paymentProcessLoading && (
          <div className="subscription-container-loader">
            <Lottie
              options={{
                rendererSettings: {
                  preserveAspectRatio: 'xMidYMid slice'
                }
              }}
              loop
              animationData={animationData}
              play
            />
          </div>
        )}
        <div className="contract-title">{displayPageTitle()}</div>
        <div className="image-and-text">
          <div className="image-area">
            <LazyLoadImage
              className="banner"
              src={
                viewData?.bannerUrl ||
                viewData?.verticalImage ||
                'images/checkout_banner_placeholder.png'
              }
            />
          </div>
          {viewData?.isPremiumChannel && displayPremiumLiveContract()}
          {viewData?.isMultiPack && displayMultiPackContract()}
          {purchaseType == 'Suscripcion' && displaySubscription()}
          {purchaseType == 'Compra' && displayCompra()}
          {purchaseType == 'Renta' && displayRenta()}
        </div>
        <div
          className={`buttons-area ${
            purchaseType == 'Suscripcion' ? 'buttons-suscripcion' : ''
          }`}
        >
          <button
            id="buySubscriptionButton"
            className="subscription-page-buttons contrator focusable"
            autoFocus
            tabIndex={1}
            onClick={handlePayments}
            disabled={paymentProcessLoading}
            style={{ backgroundColor: truncateText('Color_ColorPrimario', 25) }}
          >
            <span className="subscription-button-text">
              {displayBuyButtonText(selectedPaymentMethodName)}
            </span>
          </button>
          {selectedPaymentMethodName && (
            <span className="text-payment">
              {`${
                selectedPaymentMethodName == 'claropagosgate'
                  ? defaultPaymentMethodReceipt({ gateway: 'claropagosgate' })
                  : defaultPaymentMethodReceipt(defaultPaymentMethodInfo)
              } `}
            </span>
          )}
          <button
            className={`subscription-page-buttons ${
              selectedPaymentMethodName
                ? selectedPaymentMethodName &&
                  !claroPagosPayment?.[0]?.paymentMethodData?.cardsData
                  ? ''
                  : 'subscription-cancel-button-with-account'
                : ''
            } cancelar focusable`}
            style={{
              backgroundColor: truncateText('Color_ColorSecundario', 25)
            }}
            id="subscriptionPageCancelButton"
            onClick={handleCondition}
          >
            <span className="subscription-button-text">
              {displaySecondaryButtonText(selectedPaymentMethodName)}
            </span>
          </button>
        </div>
      </div>
      )}
    </>
  )
}
export default BuySubscriptionNew
