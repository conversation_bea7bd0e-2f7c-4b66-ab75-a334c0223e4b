.delete-profile {
  width: 1920px;
  height: 1080px;
  background-color: #121212;

  .profile-app-logo {
    width: 1920px;
    display: flex;
    justify-content: space-between;
    background-color: #121212;

    .logo-img {
      margin-top: 67px;
      margin-left: 90px;
      width: 171px;
      height: 36px;
    }

    .back-button {
      height: 48px;
      width: 292px;
      border-radius: 6.6px;
      background-color: #2e303d;
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 50px;
      margin-left: 96px;
      margin-right: 53px;

      .yellow-dot {
        height: 20px;
        width: 20px;
      }

      .back-arrow {
        height: 24px;
        width: 30px;
      }

      .back-button-text {
        height: 30px;
        width: 146px;
        color: #ffffff;
        font-family: Roboto;
        font-size: 29.04px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 29.04px;
      }
      &:focus{
        background-color: #981c15;  
      }
    }

    .back-button:focus {
      background-color: #981c15;
    }
  }

  .delete-profile-container {
    display: flex;
    flex-direction: column;

    .del-profile-heading {
      height: 57px;
      width: 1920px;
      color: #ffffff;
      font-family: <PERSON><PERSON>;
      font-size: 48px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 57px;
      text-align: center;
      margin-top: 96px;
      margin: 0px;
    }

    .del-profile-title {
      height: 32px;
      width: 362px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
      margin-left: 795px;
      margin-bottom: 48px;
    }

    .del-profile-image {
      margin-left: auto;
      margin-right: auto;
      height: 250px;
      width: 250px;
      margin-top: 80px;
    }

    .cancel-button-del {
      height: 72px;
      width: 500px;
      border-radius: 4px;
      background-color: #2e303d;
      display: flex;
      justify-content: center;
      flex-direction: center;
      align-items: center;
      margin-left: 712px;
    }

    .cancel-del-txt {
      height: 38px;
      width: 605px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: -0.51px;
      line-height: 38px;
      text-align: center;
    }

    .del-profile-button {
      height: 72px;
      width: 500px;
      border-radius: 4px;
      background-color: #981c15;
      margin-left: 712px;
      margin-bottom: 34px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 26px;

      .del-profile-txt {
        color: #ffffff;
        font-family: Roboto;
        font-size: 36.8px;
        font-weight: bold;
        letter-spacing: -0.59px;
        line-height: 72px;
        text-align: center;
        display: block;
      }
    }
  }

  .del-profile-height {
    height: 135px;
    width: 712px;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: center;
    align-items: center;

    .del-profile-name {
      height: 162px;
      width: 794px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 34px;
      letter-spacing: 0;
      line-height: 54px;
      text-align: center;
      margin: 0px;
    }
  }

  .del-profile-button:focus,
  .del-profile-button:active {
    transform: scale(1.2);
    background: #981c15 0% 0% no-repeat padding-box;
    border-radius: 8.8px;
    opacity: 1;
  }

  .cancel-button-del:focus {
    transform: scale(1.2);
    background: #2e303d;
    border-radius: 8.8px;
    opacity: 1;
  }
}