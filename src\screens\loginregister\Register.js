import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import '../loginregister/Registerpage.scss'
import { useDispatch } from 'react-redux'
import { getProfileReadData, getPushSession } from '../../store/slices/ProfileSlice'
import {
  getBackNavigate,
  getClearAllLoginStates,
  getRegister,
  getUserInfo,
  getGuestUserPlayerData,
  setSkeltonLoading
} from '../../store/slices/login'
import { pushScreenViewEvent, pushRegisterSuccessEvent } from '../../GoogleAnalytics'

const Register = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [regMailErrMsg, setRegmailErr] = useState('')
  const [regInvalidMail, setRegInvalidMail] = useState('')
  const [checkboxerrMsg, setCheckboxerrMsg] = useState('')
  const [checkboxerrFocus, setCheckboxerrFocus] = useState(false)
  const [showPromotionContent, setShowPromotionContent] = useState(false)
  const [isChecked, setIsChecked] = useState(false)
  const userInfo = useSelector(state => state?.login?.userInfo)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const registerEmail = localStorage.getItem('username')
  const registerPassword = localStorage.getItem('pwd')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const registerSuccess = useSelector(state => state?.login?.registerSuccess)
  const gamificationid = registerSuccess?.response?.gamification_id
  const userid = registerSuccess?.response?.parent_id
  const lasttouch = registerSuccess?.response?.lasttouch.profile
  const seenLastTouch = registerSuccess?.response?.lasttouch?.seen
  const hks = registerSuccess?.response?.session_stringvalue
  const user_hash = registerSuccess?.response?.session_userhash
  const registerError = useSelector(state => state?.login?.registerError)
  const token = localStorage.getItem('token')
  const startheaderinfoData = useSelector(
    state => state?.initialReducer?.startHeaderInfo?.response
  )
  const vcardSeriesDetails = useSelector(
    state => state?.login?.vcardSeriesDetails
  )
  const vodDetails = useSelector(state => state?.login?.vcardSeriesDetails)
  const vcardDetails = useSelector(state => state?.login?.vcardDetails)
  const anonymousUser = useSelector(state => state?.login?.anonymousUser)
  const { state }  = useLocation()
  const startTimeRef = useRef(null)

  useEffect(() => {
      startTimeRef.current = Date.now()
      // GA : ScreenView Event
      pushScreenViewEvent({screenName:'register',screenData:registerSuccess?.response,prevScreenName:'landing'})
      return () => {
      startTimeRef.current = null
     }
    }, [])

  useEffect(() => {
    setCheckboxerrFocus(false)
    state?.focus == true &&
    registerEmail &&
    registerPassword &&
    !checkboxerrFocus
      ? document.getElementById('checkbox')?.focus()
      : ''
    switch (true) {
      case userInfo?.status == '1' && registerError?.status == '1':
        document.getElementById('email')?.focus()
        setRegInvalidMail(
          handleTranslationchange('Onboarding_CorreoInvalidoRCU_Form_TextoTooltip1')
        )
        break
      case userInfo?.status == '0' && registerError?.status == '1':
        document.getElementById('email')?.focus()
        setRegmailErr(
          handleTranslationchange('Onboarding_RegistroCorreoVinculadoRCU_Form_TextoTooltip1'), handleTranslationchange('Onboarding_RegistroCorreoVinculadoRCU_Form_TextoTooltip2')
        )
        break
      case userInfo?.status == '1' && registerSuccess?.status == '0':
        localStorage.setItem('username', userInfo?.entry?.email)
        localStorage.setItem('gamificationid', gamificationid)
        localStorage.setItem('loginId', userid)
        localStorage.setItem('lasttouch', lasttouch)
        localStorage.setItem('seenLastTouch', seenLastTouch)
        localStorage.setItem('user_hash', user_hash)
        localStorage.setItem('hks', hks)
        dispatch(
          getPushSession({
            hks: hks,
            userId: userid,
            userSession: registerSuccess?.response?.session_userhash,
            pageName: 'register'
          })
        )
        dispatch(getProfileReadData({
          hks: hks,
          userid: userid,
          token: token,
          gamificationid: gamificationid,
          lasttouch: lasttouch
        })
      )
        //GA register event
        const engagement_time_msec = Date.now() - startTimeRef.current
        pushRegisterSuccessEvent(registerSuccess?.response,engagement_time_msec)
        dispatch(setSkeltonLoading(false))
        navigate('/prewelcome', {
          state: { 
            confirmscreen: state?.confirmscreen, 
            seriesEpisodeData: state?.seriesEpisodeData, 
            page: state?.page,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage,
            gaPageName:'register_screen'
          } 
        })
        startTimeRef.current = Date.now()
        break
      default:
        break
    }
  }, [userInfo, registerSuccess, registerError])

  const clickNext = e => {
    e.preventDefault()
    setCheckboxerrFocus(true)
    dispatch(getUserInfo({ data: state?.regmail }))
    dispatch(
      getRegister({
        password: state?.regpassword,
        hks: startheaderinfoData?.session_stringvalue
      })
    ) 
  }

  const focusFunction = () => {
    if (isChecked == false) {
      setCheckboxerrMsg(
        handleTranslationchange(
          'Onboarding_RegistroErrorRCU_Form_TextoTooltip1'
        )
      )
    }
  }
  const blurFunction = () => {
    setCheckboxerrMsg('')
  }

  const clickTelemaxView = e => {
    const engagementTime =  Date.now() - startTimeRef.current;
    pushScreenViewEvent({screenName:'promotion_screen', screenData:registerSuccess?.response, prevScreenName: 'register_screen',engagement_time_msec: engagementTime })
    setShowPromotionContent(true)
    e.preventDefault()
  }

  const closePromotionPage = e => {
    const engagementTime =  Date.now() - startTimeRef.current;
    pushScreenViewEvent({screenName:'register_screen', screenData: registerSuccess?.response, prevScreenName: 'promotion_screen',engagement_time_msec: engagementTime })
    setShowPromotionContent(false)
    e.preventDefault()
  }

  const handleCheckboxChange = e => {
    e.preventDefault()
    setCheckboxerrMsg('')
    setIsChecked(!isChecked)
  }
  const handleTermsConditons = e => {
    e.preventDefault()
    navigate('/Terms-and-Conditons', {
      state: { 
        regmail: state?.regmail, 
        regpassword: state?.regpassword,
        seriesEpisodeData: state?.seriesEpisodeData, 
        page: state?.page,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage
      }
    })
  }

  const onclickRegEmail = e => {
    e.preventDefault()
    dispatch(getClearAllLoginStates())
    navigate('/regemail', {
      state: { 
        regmail: state?.regmail, 
        regpassword: state?.regpassword,
        password: state?.password,
        seriesEpisodeData: state?.seriesEpisodeData,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage,
        gaPageName:'register_screen'
      }
    })
  }

  const onclickRegPassword = e => {
    e.preventDefault()
    dispatch(getBackNavigate(true))
    navigate('/regpassword', {
      state: {
        page: 'register',
        regmail: state?.regmail,
        regpassword: state?.regpassword,
        seriesEpisodeData: state?.seriesEpisodeData,
        pageName: state?.pageName,
        fromDetailsPage: state?.fromDetailsPage
      }
    })
  }
  const clickclear = e => {
    e.preventDefault()
    switch (true) {
      case anonymousUser?.page == 'livePlayer':
        dispatch(getGuestUserPlayerData({}))
        navigate('/livePlayer', {
          state: { showControls: 'live', grid: anonymousUser?.grid,gaPreviousPath: 'register' }
        })
        break
      case state?.fromDetailsPage:
        navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
          state: { 
            data: state?.seriesEpisodeData,
            seriesEpisodeData: state?.seriesEpisodeData,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage
          }
        })
        break
      case vcardSeriesDetails?.page == 'series':
        navigate('/series', { state: { data: vcardSeriesDetails?.vodSeries } })
        break
      case vcardDetails?.page == 'movies':
        navigate('/movies', { state: { vodData: vcardDetails?.vodMoviesData } })
        break
      case vcardSeriesDetails?.playerpage == 'playerrecord':
        navigate('/series', {
          state: { data: vcardSeriesDetails?.playerepisode }
        })
        break
        case state?.page == 'search':
          navigate('/EPconfirmation', {
            state: { inputValue: state?.inputValue,page:state?.page  }
          })
          break   
      default:
        localStorage.removeItem('pwd')
        localStorage.removeItem('username')
        navigate('/landing')
        break
    }
  }

  const keypresshandler = event => {
    if (
      event?.keyCode === 10009 ||
      event?.keyCode === 405 ||
      event?.keyCode === 89 ||
      event?.keyCode === 461 ||
      event?.keyCode === 409 ||
      event?.keyCode === 8
    ) {
      if (vodDetails?.confirmscreen || anonymousUser?.confirmscreen) {
        navigate('/EPconfirmation', {
          state: {
            page: anonymousUser?.page ?? state?.page,
            seriesEpisodeData: state?.seriesEpisodeData,
            vodMoviesData: state?.vodMoviesData,
            pageName: state?.pageName,
            fromDetailsPage: state?.fromDetailsPage
          }
        })
      } else {
        localStorage.removeItem('pwd')
        localStorage.removeItem('username')
        navigate('/landing')
      }
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handleTranslationchange = useCallback(
    keyname => {
      return apilanguage?.[keyname] ? apilanguage?.[keyname] : [keyname]
    },
    [apilanguage]
  )

  useEffect(() => {
    if (showPromotionContent) {
      document.getElementById('promotion-telmex-telcel').innerHTML =
        handleTranslationchange('promotions_access_text_html')
    }
  }, [showPromotionContent])

  return (
    <div className="App">
      <div className="App-logo">
        <img
          src={'images/claro-video-logo.png'}
          className="logo-img"
          alt="logo"
        />
      </div>
      {showPromotionContent ? (
        <div className="promotion-telmex-telcel-outer-container">
          {/* <div dangerouslySetInnerHTML={{__html: handleTranslationchange('promotions_access_text_html')}}></div> */}
          <div
            id="promotion-telmex-telcel"
            className="promotion-telmex-telcel-inner-container"
          ></div>
          <div className="promotion-telmex-telcel-button-container">
            <button
              autoFocus={true}
              className="promotion-telmex-telcel-button focusable"
              onClick={e => closePromotionPage(e)}
            >
              <span className="promotion-font">
                {handleTranslationchange(
                  'promotions_access_option_button_close'
                )}
              </span>
            </button>
          </div>
        </div>
      ) : (
        <form className="register-page">
          {/* Vd is still in discssion so commented code will use later */}
          {/* <div className="registerpage-height"> */}
          <p className="register-header">Elige cómo registrarte:</p>
          {/* <div className='choose-reg-option'>
                        <div>
                            <button className="Reg-mobile">CON TU TELÉFONO MÓVIL</button>
                            <button className="Reg-Remote active">CON TU CONTROL REMOTO</button>
                        </div>
                    </div> */}
          <div className="register-title">
            <label className="email">{handleTranslationchange('Onboarding_RegistroRCU_Texto1')}</label>
          </div>
          <div className="register-input-height">
            <button
              className={`${
                regMailErrMsg || regInvalidMail
                  ? 'regemail-redborder'
                  : state?.focus == true && registerEmail
                  ? 'register-namereg'
                  : 'placeholder-color'
              } focusable`}
              onClick={e => onclickRegEmail(e)}
              id="email"
              autoFocus
            >
              {`${
                state?.focus && state?.regmail
                  ? state?.regmail?.length > 44
                    ? `${state?.regmail.slice(0, 44)}...`
                    : state?.regmail
                  : handleTranslationchange('Onboarding_RegistroRCU_TextoPlaceholder1')
              }`}
            </button>

            <div className="email-error-box">
              <div
                id={`exampleInputEmailMsg`}
                className={`${
                  regMailErrMsg
                    ? 'invalid-text'
                    : regInvalidMail
                    ? 'invalid-mail-msg'
                    : 'invisible'
                }`}
              >
                <p className="email-error-msg">
                  {' '}
                  {[regMailErrMsg || regInvalidMail]}{' '}
                </p>
              </div>
            </div>
            <button
              className={`${
                state?.focus == true && registerPassword
                  ? 'register-password'
                  : 'password-placeholder'
              } focusable`}
              id="password"
              onClick={e => onclickRegPassword(e)}
            >
              {`${
                state?.focus && state?.regpassword
                  ? state?.regpassword?.length > 26
                    ? '*'.repeat(26)
                    : '*'.repeat(state?.regpassword?.length || 0)
                  : handleTranslationchange('Onboarding_RegistroRCU_TextoPlaceholder2')
              }`}
            </button>
          </div>
          {/* </div> */}
          <div className="radio-box">
            <button
              id="checkbox"
              className="checkbox-containerreg focusable"
              onClick={handleCheckboxChange}
              onFocus={() => focusFunction()}
              onBlur={() => blurFunction()}
            >
              <label className="checkbox-subcontainereg">
                <input
                  className="checkbox-focus"
                  type="checkbox"
                  checked={isChecked}
                />
                <span className="checkmarkreg"></span>
              </label>
              <div className="error-box">
                <div
                  id={`exampleInputEmailMsg`}
                  className={`${
                    checkboxerrMsg ? 'invalid-text-check' : 'invisible'
                  }`}
                >
                  <span>{[checkboxerrMsg]}</span>
                </div>
              </div>
              <div className="checkbox_name">
              <p className="text_terms_and">
                {handleTranslationchange(
                  'Onboarding_RegistroRCU_Texto2'
                )}
                </p><p className="text_condition">
                {handleTranslationchange(
                  'Onboarding_RegistroRCU_Texto3'
                )}
              </p>
              </div>
            </button>
          </div>
          <div className="read-details">
            <button
              className="read-detail focusable"
              id="readetails"
              onClick={e => handleTermsConditons(e)}
            >
              <p className="terms-and-conditon">
                {handleTranslationchange(
                  'Onboarding_RegistroRCU_TextoTycos1'
                )}</p>
              <p className="terms-and-conditon-text">
                {handleTranslationchange(
                  'Onboarding_RegistroRCU_TextoTycos2'
                )}</p>
            </button>
          </div>
          <div className="next-button">
            <button
              className={`${!registerEmail || !registerPassword || !isChecked?'next':'next-enable'} focusable`}
              id="registernext"
              disabled={!registerEmail || !registerPassword || !isChecked}
              onClick={e => clickNext(e)}
            >
              {handleTranslationchange('Onboarding_RegistroRCU_TextoBotonPrimario')}{' '}
            </button>
          </div>
          <div className="clear-button">
            <button
              className="clear focusable"
              id="clearbutton"
              onClick={e => clickclear(e)}
            >
              {handleTranslationchange('Onboarding_RegistroRCU_TextoBotonSecundario')}{' '}
            </button>
          </div>
          {region == 'mexico' && (
            <div className="telemexview">
              <button
                className="view-button focusable"
                id="telemaxView"
                onClick={e => clickTelemaxView(e)}
              >
                <p className="view-button-text">
                  {`${handleTranslationchange(
                    'Onboarding_RegistroRCU_TextoTelmexTelcel1'
                  )} ${handleTranslationchange(
                    'Onboarding_RegistroRCU_TextoTelmexTelce2'
                  )}`}
                </p>
              </button>
            </div>
          )}
        </form>
      )}
    </div>
  )
}

export default Register
