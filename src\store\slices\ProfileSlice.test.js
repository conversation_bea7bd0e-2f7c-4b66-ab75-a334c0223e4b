import { createSlice } from "@reduxjs/toolkit";
import initialSlice, {
    getProfileDeleteData,
    getProfileDeleteSuccess,
    getProfileDeleteError,
    getProfileAvatarData,
    getProfileAvatarSuccess,
    getProfileAvatarError,
    getProfileReadData,
    getProfileReadError,
    getProfileReadSuccess,
    getProfileData,
    getProfileSuccess,
    getProfileError,
    getProfileUpdateData,
    getProfileUpdateSuccess,
    getProfileUpdateError,
    getUserAuthDevice,
    getUserAuthDeviceSuccess,
    getUserAuthDeviceError,
    getPushSession,
    getPushSessionSuccess,
    getPushSessionError,
    getUserProfile,
  } from './ProfileSlice'
import { Session } from "inspector";

  describe('initialSlice reducer', () => {
    const initialState = {
      profileData: {},
      profileCreateData: {},
      profileEditData: {},
      profileDeleteData: {},
      profileAvatarData: {},
      userAuthDevice: {},
      pushSession:{},
      userProfile:{},
      isLoading: false,
      error: null,
      isHomeScreen: false,
      pushSessionError: {},
    };
  
    it('should return the initial state', () => {
      expect(initialSlice(undefined, {})).toEqual(initialState);
    });

    it('should handle getProfileDeleteSuccess', () => {
      const payload = { profile: 'delete' };
      const action = { type: getProfileDeleteSuccess.type, payload };
      const expectedState = { ...initialState, profileDeleteData: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
      
    it('should handle getProfileAvatarSuccess', () => {
      const payload = { profile: 'avatar' };
      const action = { type: getProfileAvatarSuccess.type, payload };
      const expectedState = { ...initialState, profileAvatarData: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileReadSuccess', () => {
      const payload = { profile: 'read' };
      const action = { type: getProfileReadSuccess.type, payload };
      const expectedState = { ...initialState, profileData: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });

    it('should handle getProfileSuccess', () => {
      const payload = { profile: 'read' };
      const action = { type: getProfileSuccess.type, payload };
      const expectedState = { ...initialState, profileCreateData: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });

    it('should handle getProfileUpdateSuccess', () => {
      const payload = { profile: 'read' };
      const action = { type: getProfileUpdateSuccess.type, payload };
      const expectedState = { ...initialState, profileEditData: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getUserAuthDeviceSuccess', () => {
      const payload = { Authdevice: 'read' };
      const action = { type: getUserAuthDeviceSuccess.type, payload };
      const expectedState = { ...initialState, userAuthDevice: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getPushSessionSuccess', () => {
      const payload = { Session: 'read' };
      const action = { type: getPushSessionSuccess.type, payload };
      const expectedState = { ...initialState, pushSession: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getUserProfile', () => {
      const payload = { Session: 'read' };
      const action = { type: getUserProfile.type, payload };
      const expectedState = { ...initialState, userProfile: payload, isLoading: false };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
      

    it('should handle profileData', () => {
      const action = { type: getProfileDeleteData.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
  
    it('should handle getProfileAvatarData', () => {
      const action = { type: getProfileAvatarData.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    

    it('should handle getProfileReadData', () => {
      const action = { type: getProfileReadData.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });

    it('should handle getProfileData', () => {
      const action = { type: getProfileData.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileUpdateData', () => {
      const action = { type: getProfileUpdateData.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getUserAuthDevice', () => {
      const action = { type: getUserAuthDevice.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getPushSession', () => {
      const action = { type: getPushSession.type };
      const expectedState = { ...initialState, isLoading: true };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileDeleteError', () => {
      const payload = 'Error message';
      const action = { type: getProfileDeleteError.type, payload };
      const expectedState = { ...initialState, isLoading: false, error: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileAvatarError', () => {
      const payload = 'Error message';
      const action = { type: getProfileAvatarError.type, payload };
      const expectedState = { ...initialState, isLoading: false, error: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileReadError', () => {
      const payload = 'Error message';
      const action = { type: getProfileReadError.type, payload };
      const expectedState = { ...initialState, isLoading: false, error: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileError', () => {
      const payload = 'Error message';
      const action = { type: getProfileError.type, payload };
      const expectedState = { ...initialState, isLoading: false, error: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getProfileUpdateError', () => {
      const payload = 'Error message';
      const action = { type: getProfileUpdateError.type, payload };
      const expectedState = { ...initialState, isLoading: false, error: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
    
    it('should handle getUserAuthDeviceError', () => {
      const payload = 'Error message';
      const action = { type: getUserAuthDeviceError.type, payload };
      const expectedState = { ...initialState, isLoading: false, error: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });

    it('should handle getPushSessionError', () => {
      const payload = 'Error message';
      const action = { type: getPushSessionError.type, payload };
      const expectedState = { ...initialState, isLoading: false, pushSessionError: payload };
      expect(initialSlice(initialState, action)).toEqual(expectedState);
    });
  })
