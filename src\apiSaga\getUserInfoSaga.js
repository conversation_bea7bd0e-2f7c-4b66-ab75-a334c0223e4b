import { call, takeEvery } from '@redux-saga/core/effects'
import { store } from '../store/sagaStore'
import { getUserInfoSuccess } from '../store/slices/login'
import { URL } from '../utils/environment'
import { request } from '../utils/request'

function* getUserInfoApi({ payload }) {
  try {
    yield call(
      request,
      URL.GET_USERINFO_URL + '&email=' + payload.data,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getUserInfoSuccess(response))
        },
        onError(error) {
          console.error('error --> ', error)
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* getUserInfoSaga() {
  yield takeEvery('login/getUserInfo', getUserInfoApi)
}
