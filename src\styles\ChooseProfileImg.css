.ChooseProfile_title {
    width: 458px;
    height: 57px;
    text-align: center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.chooseprofile_icon {
    width: 128px;
    height: 128px;
    background: #2E303D 0% 0% no-repeat padding-box;
    border: 5px solid #2E303D;
    opacity: 1;
}

.ChooseProfileimg {
    display: flex;
    justify-content: space-between;
}

.ChooseProfileimg {
    top: 0px;
    left: 0px;
    width: 1920px;
    height: 1080px;
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

.app-logo {
    width: 1920px;
    height: 1080px;
}

.logo-img {
    margin-top: 51px;
    margin-left: 96px;
    width: 149px;
    height: 41px;
    opacity: 1;
}


.chooseprofileimg_container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.chooseimagetitle {
    text-align: center;
    width: 1150px;
}

.choosewatchprof_names {
    width: 224px;
    height: 224px;
    padding-left: 2rem;
    padding-right: 4rem;
}

.chooseprofilewatch {
    width: 458px;
    height: 57px;
    text-align: center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
    margin-left: 47rem;
    margin-top: 70px;
}

.chooseprofilewatch_imgs {
    width: 320px;
    height: 102px;
    text-align: center;
    font-size: 48px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
    margin-left: 4rem;
    margin-top: 70px;
}

.chooseimg {
    margin-top: 1rem;
    margin-right: 4rem;
    width: 128px;
    height: 128px;
}

.chooseprof_imgs {
    width: 240px;
    height: 240px;
}

.arrowBtn {
    width: 296px;
    display: flex;
    height: 538px;
}

.ImgButton {
    margin: 0px 25px 25px 0px;
}

.ImgButton:focus>.lazy-load-image-background,
.ImgButton:active>.lazy-load-image-background {
    border: 2px solid #DE1717;
    outline: 1px solid #DE1717;
    box-shadow: 0px 0px 24px #DE1717;
    border-radius: 131px;
    padding: 10px;
}

.arrowBtn:focus>.chooseleftarrow,
.arrowBtn:active>.chooseleftarrow,
.arrowBtn:focus>.chooserightarrow,
.arrowBtn:active>.chooserightarrow {
    border: 3px solid #DE1717;
    outline: 1px solid #DE1717;
    box-shadow: 0px 0px 24px #DE1717;
    border-radius: 50px;
    padding: 5px;
}

.chooseprofile-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4.5rem;
    background: transparent linear-gradient(90deg, #2B2C31F2 0%, #34353BF2 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 20px #00000029;
    opacity: 1;
    margin-top: 140rem;
    display: flex;
    justify-content: space-between;
}


.chooseprofile_backbtn {
    margin-top: 1rem;
    width: 24px;
    height: 24px;
    margin-left: 57rem;
}

.choosefooter-text {
    width: 57px;
    height: 25px;
    text-align: left;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    font-family: "Roboto";
    font-size: 22px;
    margin-right: 57rem;
}

.ChooseProfileImage {
    width: 1920px;
    height: 1080px;
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

.chooserightarrow {
    width: 72px;
    height: 72px;
    opacity: 1;
    margin-top: auto;
    margin-bottom: auto;
    margin-right: 14rem;
}

.chooseleftarrow {
    width: 72px;
    height: 72px;
    opacity: 1;
    transform: rotate(180deg);
    margin-top: auto;
    margin-left: auto;
    margin-bottom: auto;
}