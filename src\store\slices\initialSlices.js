import { createSlice } from '@reduxjs/toolkit'

export const initialSlices = createSlice({
  name: 'initialSlices',
  initialState: {
    startHeaderInfo: {},
    appMetaData: {},
    metaDataHelp: {},
    isLoading: false,
    error: {},
    appMetaDataVideo: {}
  },

  reducers: {
    getStartHeaderInfo: state => {
      state.isLoading = true
    },
    getStartHeaderInfoSuccess: (state, { payload }) => {
      state.startHeaderInfo = payload
      state.isLoading = false
    },
    getStartHeaderInfoError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getAppMetaData: state => {
      state.isLoading = true
    },
    getAppMetaDataSuccess: (state, { payload }) => {
      state.appMetaData = payload
      state.isLoading = false
    },

    getAppMetaDataError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getAppMetaDataVideoSuccess: (state, { payload }) => {
      state.appMetaDataVideo = payload
      state.isLoading = false
    },

    getAppMetaDataVideoError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getMetaDataHelp: state => {
      state.isLoading = true
    },
    getMetaDataHelpSuccess: (state, { payload }) => {
      state.metaDataHelp = payload
      state.isLoading = false
    },
    getMetaDataHelpError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getIpDetails: state => {
      state.isLoading = true
    },
    getIpDetailsSuccess: (state, { payload }) => {
      state.ipDetails = payload
      state.isLoading = false
    },
    getIpDetailsError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    }
  }
})

export const {
  getStartHeaderInfo,
  getStartHeaderInfoSuccess,
  getStartHeaderInfoError,
  getAppMetaData,
  getAppMetaDataSuccess,
  getAppMetaDataError,
  getMetaDataHelp,
  getMetaDataHelpSuccess,
  getMetaDataHelpError,
  getIpDetails,
  getIpDetailsSuccess,
  getIpDetailsError,
  getAppMetaDataVideoSuccess,
  getAppMetaDataVideoError
} = initialSlices.actions

export default initialSlices.reducer
