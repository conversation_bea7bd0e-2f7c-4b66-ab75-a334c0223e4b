import React, { useCallback, useState, useEffect, useRef } from 'react'
import './EditProfile.scss'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate, useLocation } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import {
  getProfileAvatarData,
  getProfileReadData,
  getProfileUpdateData
} from '../../store/slices/ProfileSlice'
import AlphaNumeric from '../Keyboard/AlphaNumeric'
import {
  clearFPNotification,
  getCheckControlPin,
  getClearAllSettingsState,
  setControlPin,
  setLastTouch
} from '../../store/slices/settingsSlice'
import AlphaNumericKeyboard from '../Keyboard/AlphaNumericKeboard'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const EditProfile = () => {
  const { state } = useLocation()
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const saveBtnRef = useRef(null)
  const blinkerText = useRef()

  const { data } = state

  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const profileSaveRedux = useSelector(state => state?.profile?.profileEditData)
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles?.[0]?.parental
        ?.active
  )
  const securityPin = useSelector(
    state => state?.settingsReducer?.setControlPinData
  )
  const verifySecurityPin = useSelector(
    state => state?.settingsReducer?.checkControlPin
  )
  const remindSecurityPin = useSelector(
    state => state?.settingsReducer?.remindControlPin?.response?.email_sent
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')

  const [selectedImage, setSelectedImage] = useState('')
  const [isChecked, setIsChecked] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [showInputPopup, setShowInputPopup] = useState(false)
  const [pinCreated, setPinCreated] = useState(false)
  const [eyeFocused, setEyeFocused] = useState(false)
  const [buttonDisable, setButtonDisable] = useState(true)
  const [keyboardFocus, setKeyboardFocus] = useState(false)
  const [showSaveBtn, setShowSaveBtn] = useState(false)
  const [focusedIdx, setFocusedIdx] = useState(0)
  const [pin, setPin] = useState(new Array(6).fill(''))
  const [pinInvalid, setPinInvalid] = useState('')
  const [showNotification, setShowNotification] = useState(false)
  const numRef = useRef(null)
  const abcRef = useRef(null)
  const borrarRef = useRef(null)
  const vaciarRef = useRef(null)
  const chooseProfileButtonRef = useRef(null)

  const [currentButtonFocus, setCurrentButtonFocus] = useState('profilename')

  
  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate('/watcheditprofile', {
        state: { pageName: '/watcheditprofile', data: data }
      })
    }
  }

  const handleLgkey = keycode => {
    if (keycode === 405 || keycode === 461 ) {
      navigate('/watcheditprofile', {
        state: { pageName: '/watcheditprofile', data: data }
      })
    }
  }

  const focusShift = keycode => {
    if (keycode === 38) {
      setTimeout(() => {
        numRef?.current?.focus()
        abcRef?.current?.focus()
        borrarRef?.current?.focus()
        vaciarRef?.current?.focus()
      }, 200)
    }
  }


  useEffect(() => {
    if (chooseProfileButtonRef.current) {
      chooseProfileButtonRef.current.focus()
    }
    pushScreenViewEvent({screenName:'edit_profile', screenData: loginInfo ?? registerInfo, prevScreenName: 'edit_watching_profile'})
  }, [])

 // Blinker effect
 useEffect(() => {
  let isBlinking = true;
  
  const handleBlinker = () => {
    const prfNameCheck = document.querySelector('.profile-name-input');
    if (prfNameCheck) {
      // Only manipulating the actual input value, not the display value
      const baseValue = inputValue || '';
      prfNameCheck.value = isBlinking ? baseValue + '|' : baseValue;
      isBlinking = !isBlinking;
    }
  };

  blinkerText.current = setInterval(handleBlinker, 700);

 
  handleBlinker();

  return () => {
    if (blinkerText.current) {
      clearInterval(blinkerText.current);
    }
  };
}, [inputValue]); 

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }
  
  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    setInputValue(state?.username || data?.username)
    setIsChecked(data?.is_kids == 'false' ? false : true)
    setSelectedImage(
      state?.selectedProf?.length > 1
        ? state?.selectedProf
        : data?.user_image || 'images/Profile_Icons/profile_image.png'
    )
    setShowSaveBtn(true)
  }, [])

  useEffect(() => {
    setPinCreated(securityPinCheck)
  }, [securityPinCheck])

  const updateProfile = () => {
    const payload = {
      hks: loginInfo
        ? loginInfo?.session_stringvalue
        : registerInfo?.session_stringvalue,
      userid: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
      gamificationid: data?.gamification_id,
      firstname: inputValue,
      user_token: loginInfo ? loginInfo?.user_token : registerInfo?.user_token,
      user_image: selectedImage,
      is_kids: isChecked
    }
    dispatch(getProfileUpdateData(payload))
  }


  useEffect(() => {
    if (
      verifySecurityPin?.msg === 'OK' &&
      isChecked &&
      pin &&
      pin[0].length > 0 &&
      pin[1].length > 0 &&
      pin[2].length > 0 &&
      pin[4].length > 0 &&
      pin[5].length > 0
    ) {
      updateProfile()
    } else if (verifySecurityPin?.msg === 'ERROR') {
      setButtonDisable(true)
      setFocusedIdx(0)
      setPinInvalid(verifySecurityPin?.errors)
      dispatch(getClearAllSettingsState())
      setPin(new Array(6).fill(''))
      setKeyboardFocus(true)
      setButtonDisable(true)
      setShowSaveBtn(false)
    }
  }, [verifySecurityPin])

  useEffect(() => {
    if (remindSecurityPin) {
      dispatch(clearFPNotification())
      setShowNotification(true)
      setTimeout(() => {
        setShowNotification(false)
      }, 4000)
    }
  }, [remindSecurityPin])

  const goToWatchEdit = useCallback(event => {
    if (event.keyCode === 10009 || event.keyCode === 461  || event.keyCode === 8) {
      navigate('/watcheditprofile', {
        state: { data: '', id: data?.gamification_id }
      })
      return null
    }
  }, [])


  useEffect(() => {
    document.body.addEventListener('keyup', goToWatchEdit)
    return () => {
      document.body.removeEventListener('keyup', goToWatchEdit)
    }
  }, [goToWatchEdit])

 
 
 const handleInputChange = newValue => { 
  setCurrentButtonFocus('keyBoard');
  const trimmedValue = newValue.replace(/^\s+/, '');
  if (trimmedValue.length <= 25) {
    // Removed any existing blinker character before setting new value
    const cleanValue = trimmedValue.replace(/\|$/, '');
    setInputValue(cleanValue);
  }
};

  useEffect(() => {
    if (isChecked && data?.is_kids == 'false') {
      setShowSaveBtn(false)
    } else {
      setShowSaveBtn(true)
    }

    if (!isChecked) {
      setPin(new Array(6).fill(''))
      setFocusedIdx(0)
      setShowSaveBtn(true)
    }
  }, [isChecked])

  useEffect(() => {
    if (
      securityPin?.profiles?.[0]?.parental.active &&
      securityPin?.profiles?.[0]?.channel.active &&
      isChecked &&
      pin &&
      pin[0].length > 0 &&
      pin[1].length > 0 &&
      pin[2].length > 0 &&
      pin[4].length > 0 &&
      pin[5].length > 0
    ) {
      updateProfile()
    }
  }, [securityPin])

  useEffect(() => {
    if (
      pin &&
      pin[0].length > 0 &&
      pin[1].length > 0 &&
      pin[2].length > 0 &&
      pin[4].length > 0 &&
      pin[5].length > 0
    ) {
      setButtonDisable(false)
    } else {
      setButtonDisable(true)
    }
  }, [pin])

  
  useEffect(() => {
    if (profileSaveRedux?.msg === 'OK') {
      const payload = {
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue,
        userid: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
        gamificationid: loginInfo
          ? loginInfo?.gamification_id
          : registerInfo?.gamification_id,
        token: loginInfo ? loginInfo?.user_token : registerInfo?.user_token,
        lasttouch: profileSaveRedux?.response?.lasttouch?.profile
      }
      dispatch(setLastTouch(profileSaveRedux?.response?.lasttouch?.profile))
      dispatch(getProfileReadData(payload))
      navigate('/watcheditprofile', {
        state: {
          data: 'Profile Updated',
          id: profileSaveRedux?.response?.gamification_id
        }
      })
    }
  }, [profileSaveRedux])

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handle_delete = () => {
    navigate('/deleteprofile', { state: { data } })
  }

  const handle_profimage = () => {
    navigate('/chooseprofile', {
      state: {
        pageName: '/editprofile',
        data: { ...data, user_image: selectedImage, username: inputValue }
      }
    })
    dispatch(
      getProfileAvatarData({
        hks: loginInfo
          ? loginInfo?.session_stringvalue
          : registerInfo?.session_stringvalue
      })
    )
  }

  const handleSaveProfile = () => {
    if (isChecked && data?.is_kids == 'false') {
      if (pinCreated) {
        const payload = {
          controlPIN: pin.join(''),
          userId: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
          hks: loginInfo
            ? loginInfo?.session_stringvalue
            : registerInfo?.session_stringvalue,
          parental: 1
        }
        dispatch(getCheckControlPin(payload))
      } else {
        const payload = {
          hks: loginInfo
            ? loginInfo?.session_stringvalue
            : registerInfo?.session_stringvalue,
          userId: loginInfo ? loginInfo?.user_id : registerInfo?.user_id,
          user_token: loginInfo
            ? loginInfo?.user_token
            : registerInfo?.user_token,
          parental: 1,
          purchase: 0,
          channel: 1,
          rating: 30,
          code: pin.join('')
        }
        dispatch(setControlPin(payload))
      }
      return
    } else {
      updateProfile()
    }
  }
  const handle_editcancel = () => {
    navigate('/watcheditprofile', {
      state: { pageName: '/watcheditprofile', data: data }
    })
  }
  return (
    <div
      className="add-profile"
      style={{
        height:
          isChecked && !showSaveBtn ? '1500px' : isChecked ? '1300px' : '1080px'
      }}
    >
      <div className="profile-app-logo">
        <img
          src={'images/claro-profile-logo.png'}
          className="logo-img"
          alt="logo"
        />
        <button className="back-button focusable"
          data-sn-down="#ChooseProfileText_Container_id"
          onClick={handle_editcancel}>
          <img
            src={'images/Profile_Icons/ic_shortcut_amarillo.png'}
            className="yellow-dot"
            alt="img not found"
            id='abcBtn'
          />
          <img
            src={'images/Profile_Icons/ic_shortcut_back.png'}
            className="back-arrow"
            alt="img not found"
          />
          <span className="back-button-text">
            {translations?.language?.[region]?.atv_back_notification}
          </span>
        </button>
      </div>
      <div className="main-container">
        <h5 className="prf-watch-header">
          {translations?.language?.[region]?.updateProfile_access_title_label
            ? translations?.language?.[region]?.updateProfile_access_title_label
            : 'updateProfile_access_title_label'}
        </h5>
        <div className="edit-container">
          <div className="edit-key-board">
            <AlphaNumericKeyboard
              type="alphaNumeric"
              onChange={e => handleInputChange(e)}
              value={inputValue}
              autoFocus={keyboardFocus}
              name="edit-profile"
            />
          </div>
          <div className="edit-profile-cont">
            <div className="edit-content">
              <div className="edit-image-container">
                <button
                  className="prof-button"
                  id="profButton"
                  onFocus={() => setEyeFocused(true)}
                  onBlur={() => setEyeFocused(false)}
                >
                  <LazyLoadImage
                    className="profile-image"
                    src={selectedImage}
                  />
                </button>
                <button
                  autoFocus={true}
                  id="ChooseProfileText_Container_id"
                  onClick={handle_profimage}
                  className="choose-profile-text-container focusable"
                  ref={chooseProfileButtonRef}
                  data-sn-down="#profilename"
                >
                  <span className="choose-profile-text">
                    {translations?.language?.[region]
                      ?.addProfile_access_option_button_avatarSelector
                      ? translations?.language?.[region]
                          ?.addProfile_access_option_button_avatarSelector
                      : 'addProfile_access_option_button_avatarSelector'}
                  </span>
                </button>
              </div>
              <div className="profile-container">
                <div className="profile-name-container">
                  <label className="profile-name">
                    {translations?.language?.[region]
                      ?.addProfile_access_nameProfile_label
                      ? translations?.language?.[region]
                          ?.addProfile_access_nameProfile_label
                      : 'aaddProfile_access_nameProfile_label'}
                  </label>
                </div>
                <div>{showInputPopup}</div>
                <button
                  className="profile-name-input-container focusable"
                  id="profilename"
                  data-sn-up="#ChooseProfileText_Container_id"
                >
                  {/* <span className="cursor-point-addprofile">
                  { !inputValue?.length &&'|'}
                  </span> */}
                  <input
                    className="profile-name-input"
                    name="profilename"
                    maxLength={25}
                    onChange={e => {
                      handleInputChange(e)
                    }}
                    value={inputValue}
                    onBlur={() => {
                      setShowInputPopup(false)
                      setEyeFocused(false)
                    }}
                    onFocus={() => {
                      setShowInputPopup(true)
                      setEyeFocused(true)
                    }}
                    inputMode="none"
                    autoComplete="off"
                    disabled
                  />
                </button>
              </div>
              {/* {showSaveBtn ? ( */}
              <div className="edit-profile-container">
                <div
                  className="inline-edit-btns"
                  style={data?.admin ? { marginTop: '133px' } : null}
                >
                  <button
                    className="cancelbtn-editprof focusable"
                    data-testid="edit-btns"
                    onClick={handle_editcancel}
                  >
                    <span className="cancelbtntxtEditprof">
                      {' '}
                      {translations?.language?.[region]
                        ?.userProfile_password_option_button_cancel
                        ? translations?.language?.[region]
                            ?.userProfile_password_option_button_cancel
                        : 'userProfile_password_option_button_cancel'}
                    </span>
                  </button>
                  <button
                    ref={saveBtnRef}
                    id="handle-profile"
                    onClick={() => handleSaveProfile()}
                    disabled={
                      inputValue && inputValue.length > 0 ? false : true
                    }
                    style={{
                      opacity: inputValue && inputValue.length > 0 ? '1' : '0.5'
                    }}
                    className="edit-profile-save focusable"
                  >
                    <span className="edit-profile-save-text">
                      {translations?.language?.[region]
                        ?.addProfile_access_option_button_save
                        ? translations?.language?.[region]
                            ?.addProfile_access_option_button_save
                        : 'addProfile_access_option_button_save'}
                    </span>
                  </button>
                </div>
                {data?.admin ? null : (
                  <button
                    style={{
                      opacity: inputValue && inputValue.length < 0 ? '0.5' : '1'
                    }}
                    onClick={handle_delete}
                    className="delete-profilebtn focusable"
                  >
                    <span className="delete-profilebtn-text">
                      {translations?.language?.[region]
                        ?.updateProfile_access_option_button_deleteProfile
                        ? translations?.language?.[region]
                            ?.updateProfile_access_option_button_deleteProfile
                        : 'updateProfile_access_option_button_deleteProfile'}
                    </span>
                  </button>
                )}
              </div>
              {isChecked && data?.is_kids == 'false' ? (
                <div
                  style={{ margin: '30px 0px' }}
                  onFocus={() => setShowSaveBtn(false)}
                ></div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EditProfile
