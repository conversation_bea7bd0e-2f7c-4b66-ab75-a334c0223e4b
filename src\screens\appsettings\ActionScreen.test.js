import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ActionScreen from "./ActionScreen";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<MemoryRouter
			history={history}
			initialEntries={[{ state: { pageName: 'parentalControl' } }]}
		>
			{children}
		</MemoryRouter>
	</Provider>
);
export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper });
};
const mockErrorResponse = {
	"status": "1"
}

const mockIsLoggedInSuccessResponse = {
	response: {
		session_stringvalue: "ZTEATV412001224226580292a69e33",
		session_userhash: "************************************************************************************************"
	},
}

const mockLockChannelDeleteSuccessResponse = {
	msg: 'OK'
}

const mockDeleteReminderSuccessResponse = {
	response: true
}

const epgSevenDaysDataMockSuccessResponse = {
	response: {
		group_id: "1135375",
		group: {
			common: {
				timeshift: "4000"
			}
		}
	}
}



describe('Action Screen Landing page test', () => {

	test('should render without api mock data', () => {
		initialState.login = {
			isLoggedIn: mockErrorResponse
		}
		initialState.settingsReducer = {
			lockedChannelDelete: mockErrorResponse
		}
		initialState.epg = {
			delReminder: mockErrorResponse
		}
		initialState.epg = {
			epgChannel: mockErrorResponse
		}
		renderWithState(<ActionScreen />)
	})

	test('should render with api mock data', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		}
		initialState.settingsReducer = {
			lockedChannelDelete: mockLockChannelDeleteSuccessResponse
		}
		initialState.epg = {
			delReminder: mockDeleteReminderSuccessResponse
		}
		initialState.epg = {
			epgChannel: epgSevenDaysDataMockSuccessResponse
		}
		renderWithState(<ActionScreen />)
	})

	test('ver button click', () => {
		const { container } = renderWithState(<ActionScreen />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'verButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		fireEvent.keyUp(buttonClick, { keyCode: '10009' })
		fireEvent.keyUp(buttonClick, { keyCode: '405' })
	})

	test('ver button click with parental control state', () => {
		const { container } = renderWithState(<ActionScreen />)
		const getById = queryByAttribute.bind(null, 'id')
		const buttonClick = getById(container, 'verButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})


	// test('no button click', () => {
	// 	const { container } = renderWithState(<ActionScreen />)
	// 	const getById = queryByAttribute.bind(null, 'id')
	// 	const borrarButtonClick = getById(container, 'borrarButton')
	// 	const buttonClick = getById(container, 'noButton')
	// 	fireEvent.focus(borrarButtonClick)
	// 	fireEvent(
	// 		borrarButtonClick,
	// 		new MouseEvent('click', {
	// 			bubbles: true,
	// 			cancelable: true
	// 		})
	// 	)
	// 	fireEvent(
	// 		buttonClick,
	// 		new MouseEvent('click', {
	// 			bubbles: true,
	// 			cancelable: true
	// 		})
	// 	)
	// })

	// test('yes button click', () => {
	// 	const { container } = renderWithState(<ActionScreen />)
	// 	const getById = queryByAttribute.bind(null, 'id')
	// 	const borrarButtonClick = getById(container, 'borrarButton')
	// 	const buttonClick = getById(container, 'yesButton')
	// 	fireEvent.focus(borrarButtonClick)
	// 	fireEvent(
	// 		borrarButtonClick,
	// 		new MouseEvent('click', {
	// 			bubbles: true,
	// 			cancelable: true
	// 		})
	// 	)
	// 	fireEvent(
	// 		buttonClick,
	// 		new MouseEvent('click', {
	// 			bubbles: true,
	// 			cancelable: true
	// 		})
	// 	)
	// })

	test('no button click', () => {
		// Mock required state
		const mockState = {
			pageName: 'parentalControl',
			data: {
				id: '123',
				channel_group: {
					common: {
						id: '123',
						image_small: 'test.jpg',
						timeshift: '0'
					}
				}
			}
		};
		
		const { container } = renderWithState(
			<ActionScreen 
				location={{
					state: mockState
				}}
			/>
		);
		
		// First click the delete button to show the confirmation screen
		const getById = queryByAttribute.bind(null, 'id');
		const borrarButton = getById(container, 'borrarButton');
		fireEvent.click(borrarButton);
		
		// Then click the no button
		const noButton = getById(container, 'noButton');
		fireEvent.click(noButton);
	});
	
	test('yes button click', () => {
		// Similar setup as above
		const mockState = {
			pageName: 'parentalControl',
			data: {
				id: '123',
				channel_group: {
					common: {
						id: '123',
						image_small: 'test.jpg',
						timeshift: '0'
					}
				}
			}
		};
		
		const { container } = renderWithState(
			<ActionScreen 
				location={{
					state: mockState
				}}
			/>
		);
		
		// First click the delete button
		const getById = queryByAttribute.bind(null, 'id');
		const borrarButton = getById(container, 'borrarButton');
		fireEvent.click(borrarButton);
		
		// Then click the yes button
		const yesButton = getById(container, 'yesButton');
		fireEvent.click(yesButton);
	});
})
