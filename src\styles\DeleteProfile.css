.app-logo {
    width: 1920px;
    height: 1080px;
}

.DeleteProfileCointainer {
    display: flex;
    flex-direction: column;
}

.delprofile_heading {
    left: 873px;
    height: 57px;
    font-weight: 'Regular';
    text-align: center;
    font-family: "Roboto";
    font-size: 48px;
    letter-spacing: 0px;
    color: #F1F2F3;
    opacity: 1;

}

.DelProfileImage {
    margin-left: auto;
    margin-right: auto;
    width: 128px;
    height: 128px;
}

.DelProfileTitle {
    width: 644px;
    height: 47px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    font-family: "<PERSON>o";
    font-size: 40px;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.delete-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4.5rem;
    background: transparent linear-gradient(90deg, #2B2C31F2 0%, #34353BF2 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 20px #00000029;
    opacity: 1;
    margin-top: 11rem;
    display: flex;
    justify-content: space-between;
}

.delfooter-text {
    width: 57px;
    height: 25px;
    text-align: left;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    font-family: "Roboto";
    font-size: 22px;
    margin-right: 57rem;
}

.delprofile_backbtn {
    margin-top: 1rem;
    width: 24px;
    height: 24px;
    margin-left: 57rem;
}

.DelProfileButton {
    margin-top: 67px;
    margin-left: auto;
    margin-right: auto;
    font-size: 30px;
    color: #EEEEEE;
    background: #2E303D 0% 0% no-repeat padding-box;
    border-radius: 44px;
    opacity: 1;
    text-align: center;
    font-size: 34px;
    width: 360px;
    height: 88px
}

.DelProfileButton:focus,
.DelProfileButton:active {
    box-shadow: 0px 0px 5px #981C15;
    border: 3px solid #981C15;
    outline: 1px solid #981C15;
    background: #981C15 0% 0% no-repeat padding-box;
}

.DelProfileHeight {
    height: 135px;
    width: 712px;
    margin-left: auto;
    margin-right: auto;
}

.DeleteProfile {
    top: 0px;
    left: 0px;
    width: 1920px;
    height: 1080px;
    background: transparent linear-gradient(39deg, #FFFFFF00 0%, #FFFFFF 697%) 0% 0% no-repeat padding-box;
}

.DelProfileName {
    text-align: center;
    font-size: 32px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 0.64;
}

.delprof_name {
    width: 680px;
    height: 76px;
    text-align: center;
    font-size: 32px;
    font-family: Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 0.64;
    margin-top: 0px;
    margin-left: 40rem;
}