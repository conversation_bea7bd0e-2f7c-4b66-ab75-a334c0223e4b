import React from 'react'
import {
  fireEvent,
  render,
  screen,
  queryByAttribute,
  act
} from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter as Router } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import '@testing-library/jest-dom'
import { createHashHistory as createHistory } from 'history'
import AlphaNumericKeyboard from './AlphaNumericKeboard'

const mockStore = configureStore([])

const history = createHistory()

const initialState = {}
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <Router history={history}>{children}</Router>
  </Provider>
)
export const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}

describe('AlphaNumericKeyboard component', () => {
  const defaultProps = {
    onChange: jest.fn(),
    onSubmit: jest.fn(),
    type: 'alphaNumeric',
    id: 'profileup',
    setCurrentButtonFocus: jest.fn()
  }

  beforeEach(() => {
    jest.useFakeTimers()
    localStorage.setItem('region', 'en')
  })

  afterEach(() => {
    jest.useRealTimers()
    localStorage.clear()
  })

  test('should render the profileform for the smart tv part', () => {
    let props = {
      onChange: 'ƒ onChange() {}',
      onSubmit: 'ƒ onSubmit() {}',
      type: 'alphaNumeric',
      id: 'profileup',
      setCurrentButtonFocus: jest.fn()
    }
    const { rerender } = renderWithState(
      <AlphaNumericKeyboard {...defaultProps} />
    )
    rerender(<AlphaNumericKeyboard {...props} />)
  })

  test('handles email domain buttons', () => {
    const onChange = jest.fn()
    const props = {
      onChange,
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn(),
      name: 'email'
    }
    const { getByText } = renderWithState(<AlphaNumericKeyboard {...props} />)

    fireEvent.click(getByText('@gmail.com'))
    expect(onChange).toHaveBeenCalledWith('@gmail.com')
  })
  test('renders numeric keypad layout', () => {
    const props = {
      onChange: jest.fn(),
      type: 'numeric',
      setCurrentButtonFocus: jest.fn()
    }
    const { getAllByText } = renderWithState(
      <AlphaNumericKeyboard {...props} />
    )
    expect(getAllByText('1')[0]).toBeInTheDocument()
    expect(getAllByText('0')[0]).toBeInTheDocument()
  })

  test('should render numeric keyboard when type is numeric', () => {
    renderWithState(<AlphaNumericKeyboard {...defaultProps} />)

    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
  })

  test('Shift ', () => {
    const { container } = renderWithState(
      <AlphaNumericKeyboard {...defaultProps} />
    )
    const getById = queryByAttribute.bind(null, 'id')
    const buttonClick = getById(container, 'shftId')
    fireEvent.focus(buttonClick)
    fireEvent.blur(buttonClick)
    fireEvent.mouseOver(buttonClick)
    fireEvent(
      buttonClick,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })

  test('gmail ', () => {
    const { container } = renderWithState(
      <AlphaNumericKeyboard {...defaultProps} />
    )
    const getById = queryByAttribute.bind(null, 'id')
    const buttonClick = getById(container, 'gmlId')
    fireEvent.focus(buttonClick)
    fireEvent.blur(buttonClick)
    fireEvent.mouseOver(buttonClick)
    fireEvent(
      buttonClick,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })

  test('yahoo ', () => {
    const { container } = renderWithState(
      <AlphaNumericKeyboard {...defaultProps} />
    )
    const getById = queryByAttribute.bind(null, 'id')
    const buttonClick = getById(container, 'yhoId')
    fireEvent.focus(buttonClick)
    fireEvent.blur(buttonClick)
    fireEvent.mouseOver(buttonClick)
    fireEvent(
      buttonClick,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })

  test('space ', () => {
    const { container } = renderWithState(
      <AlphaNumericKeyboard {...defaultProps} />
    )
    const getById = queryByAttribute.bind(null, 'id')
    const buttonClick = getById(container, 'spcId')
    fireEvent.focus(buttonClick)
    fireEvent.blur(buttonClick)
    fireEvent.mouseOver(buttonClick)
    fireEvent(
      buttonClick,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })
  test('respects character length limit', () => {
    const onChange = jest.fn();
    const props = {
      onChange,
      type: 'alphaNumeric',
      length: 5, // Setting a character limit
      value: '1234',
      setCurrentButtonFocus: jest.fn()
    };

    renderWithState(<AlphaNumericKeyboard {...props} />);

    // Find a button to click (e.g., 'a')
    const aButton = screen.getByText('a');

    // Click once - should work as we're below the limit
    fireEvent.click(aButton);
    expect(onChange).toHaveBeenCalledWith('1234a');

    // Update props to simulate the component receiving the new value
    const updatedProps = { ...props, value: '1234a' };
    renderWithState(<AlphaNumericKeyboard {...updatedProps} />);

    // Click again - should not add another character as we'd exceed the limit
    fireEvent.click(aButton);
    // Should still be called with the same value as before
    expect(onChange).toHaveBeenLastCalledWith('1234a');
  });

  // Test for special character keyboard section (lines 518-541)
  test('toggles to special character keyboard and handles special characters', () => {
    const onChange = jest.fn();
    const props = {
      onChange,
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn()
    };

    const { container } = renderWithState(<AlphaNumericKeyboard {...props} />);

    // Find and click the button to switch to special characters
    // Using queryByAttribute instead of getByText since the text might not be directly accessible
    const getById = queryByAttribute.bind(null, 'id');
    const specialCharsButton = container.querySelectorAll('.keyBoardSelection')[1]; // Second button should be the special chars button
    fireEvent.click(specialCharsButton);

    // Now we should be showing special characters
    // Find and click a special character
    // Since the DOM structure changes after clicking, we need to look for a specific element
    // Let's use the first special character button available
    const specialChar = getById(container, 'Key_0'); // First key in special chars mode
    fireEvent.click(specialChar);

    // Expect onChange to have been called with whatever character is on that button
    expect(onChange).toHaveBeenCalled();
  });

  test('handles .ar and .com buttons', () => {
    const onChange = jest.fn();
    const props = {
      onChange,
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn(),
      name: 'email', // Needed to show these buttons
      value: '' // Start with empty value
    };

    const { container } = renderWithState(<AlphaNumericKeyboard {...props} />);

    // Test .com button using queryByAttribute
    const getById = queryByAttribute.bind(null, 'id');
    const comButton = getById(container, '.comId');
    if (comButton) {
      fireEvent.click(comButton);
      expect(onChange).toHaveBeenCalledWith('.com');
    }

    // Reset the mock to start fresh for .ar test
    onChange.mockReset();

    // For .ar button, test with empty value to avoid concatenation issues
    const arProps = {
      ...props,
      onChange: onChange, // Use the reset mock
      value: '' // Make sure we start with empty string
    };

    // Re-render with fresh props
    const { container: newContainer } = renderWithState(<AlphaNumericKeyboard {...arProps} />);

    const arButton = getById(newContainer, '.arId');
    if (arButton) {
      fireEvent.click(arButton);
      expect(onChange).toHaveBeenCalledWith('.ar');
    }
  });

  test('handles keyboard events', () => {
    // Mock document.getElementById to return a button element
    const mockElement = document.createElement('button');
    mockElement.click = jest.fn();
    const originalGetElementById = document.getElementById;
    document.getElementById = jest.fn().mockImplementation(id => {
      if (id === 'clearBtn') return mockElement;
      return originalGetElementById.call(document, id);
    });

    // Mock tizen
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockImplementation(key => {
          if (key === 'ColorF1Green') return { code: 403 };
          if (key === 'ColorF3Blue') return { code: 405 };
          return { code: 0 };
        })
      }
    };

    const onChange = jest.fn();
    const props = {
      onChange,
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn()
    };

    renderWithState(<AlphaNumericKeyboard {...props} />);

    // Simulate pressing the green key (clear button)
    fireEvent.keyUp(document, { keyCode: 403 });
    expect(mockElement.click).toHaveBeenCalled();

    // Clean up mocks
    document.getElementById = originalGetElementById;
    delete global.tizen;
  });

  test('handles LG TV key events', () => {
    // Mock document.getElementById to return a button element
    const mockElement = document.createElement('button');
    mockElement.click = jest.fn();
    const originalGetElementById = document.getElementById;
    document.getElementById = jest.fn().mockImplementation(id => {
      if (id === 'clearBtn') return mockElement;
      return originalGetElementById.call(document, id);
    });

    // Ensure tizen is undefined for this test
    global.tizen = undefined;

    const onChange = jest.fn();
    const props = {
      onChange,
      type: 'alphaNumeric',
      setCurrentButtonFocus: jest.fn()
    };

    renderWithState(<AlphaNumericKeyboard {...props} />);

    // Simulate pressing the green key (404 or 67 for LG)
    fireEvent.keyUp(document, { keyCode: 404 });
    expect(mockElement.click).toHaveBeenCalled();

    // Clean up mocks
    document.getElementById = originalGetElementById;
  });

  // Test autoFocus functionality
  test('sets focus automatically when autoFocus is true', () => {
    jest.useFakeTimers();

    const props = {
      onChange: jest.fn(),
      type: 'numeric',
      autoFocus: true,
      setCurrentButtonFocus: jest.fn()
    };

    renderWithState(<AlphaNumericKeyboard {...props} />);

    // Fast-forward timers
    act(() => {
      jest.advanceTimersByTime(1500);
    });

    // Verify focus was set (this depends on your DOM structure)
    // You might need to adjust this assertion based on how focus is managed
    const focusedElement = document.activeElement;
    expect(focusedElement).toHaveAttribute('id', 'Key_0');

    jest.useRealTimers();
  });

  // Test fromMic functionality
  test('sets focus when fromMic is true', () => {
    jest.useFakeTimers();

    const props = {
      onChange: jest.fn(),
      type: 'alphaNumeric',
      fromMic: true,
      setCurrentButtonFocus: jest.fn()
    };

    renderWithState(<AlphaNumericKeyboard {...props} />);

    // Fast-forward timers
    act(() => {
      jest.advanceTimersByTime(300);
    });

    // Verify focus was set (this depends on your DOM structure)
    const focusedElement = document.activeElement;
    expect(focusedElement).toHaveAttribute('id', 'Key_0');

    jest.useRealTimers();
  });

})