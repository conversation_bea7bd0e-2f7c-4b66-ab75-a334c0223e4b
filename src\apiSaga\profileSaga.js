import { call, takeEvery } from '@redux-saga/core/effects'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getProfileUpdateSuccess, getProfileUpdateError,
  getProfileReadError, getProfileReadSuccess,
  getProfileDeleteSuccess, getProfileDeleteError,
  getProfileAvatarSuccess, getProfileAvatarError,
  getProfileSuccess, getProfileError,
  getUserAuthDeviceSuccess, getUserAuthDeviceError,
  getPushSessionSuccess, getPushSessionError
} from '../store/slices/ProfileSlice'
import { getIsLoggedinV1, setSkeltonLoading } from '../store/slices/login'

function* fetchProfileReadApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.PROFILE_READ_URL}&HKS=${payload.hks}&user_id=${payload.userid}&user_token=${payload.token}&gamification_id=${payload.gamificationid}&lasttouch=${payload.lasttouch}&region=${region}`,
      { method: 'GET' },
      {
        onSuccess(response) {
          store.dispatch(getProfileReadSuccess(response))
        },
        onError(error) {
          store.dispatch(getProfileReadError(error))
        }
      }
    )
  } catch (error) { }
}

// profilecreate api
function* fetchProfileApi({ payload }) {

  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.PROFILE_URL}&HKS=${payload.hks}&user_id=${payload.userid}&firstname=${payload.firstname}&user_token=${payload.user_token}&user_image=${payload.user_image}&is_kids=${payload.is_kids}&region=${region}`,
      { method: 'POST' },
      {
        onSuccess(response) {
          store.dispatch(getProfileSuccess(response))
        },
        onError(error) {
          store.dispatch(getProfileError(error))
        }
      }
    )
  } catch (error) { }
}

function* fetchProfileDeleteApi({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(
      request,
      `${URL.PROFILE_DELETE_URL}&HKS=${payload.hks}&user_id=${payload.userid}&gamification_id=${payload.gamificationid}&region=${region}`,
      { method: 'POST' },
      {
        onSuccess(response) {
          store.dispatch(getProfileDeleteSuccess(response))
        },
        onError(error) {
          store.dispatch(getProfileDeleteError(error))
        }
      }
    )
  } catch (error) { }
}

function* fetchProfileAvatarApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.PROFILE_AVATAR_URL}&HKS=${payload.hks}&region=${region}`,
      { method: 'GET' },
      {
        onSuccess(response) {
          store.dispatch(getProfileAvatarSuccess(response))
        },
        onError(error) {
          store.dispatch(getProfileAvatarError(error))
        }
      }
    )
  } catch (error) { }
}

function* fetchProfileUpdateApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.PROFILE_UPDATE_URL}&HKS=${payload.hks}&user_id=${payload.userid}&gamification_id=${payload.gamificationid}&firstname=${payload.firstname}&user_token=${payload.user_token}&user_image=${payload.user_image}&is_kids=${payload.is_kids}&region=${region}`,
      { method: 'POST' },
      {
        onSuccess(response) {
          store.dispatch(getProfileUpdateSuccess(response))
        },
        onError(error) {
          store.dispatch(getProfileUpdateError(error))
        }
      }
    )
  } catch (error) { }
}

function* fetchUserAuthDeviceApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(request,
      `${URL.USER_AUTHDEVICE_URL}&HKS=${payload.hks}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getUserAuthDeviceSuccess(response))
        },
        onError(error) {
          store.dispatch(getUserAuthDeviceError(error))
        }
      }
    )
  } catch (error) {
  }
}

function* fetchPushSessionApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(request,
      `${URL.PUSH_SESSION_URL}&HKS=${payload?.hks}&user_id=${payload?.userId}&user_session=${payload?.userSession}&region=${region}`,
      {
        method: 'POST'
      },
      {
        onSuccess(response) {
          store.dispatch(getPushSessionSuccess(response))
          payload.pageName && store.dispatch(getIsLoggedinV1({HKS:payload?.hks}))
          !payload.pageName && store.dispatch(getIsLoggedinV1({HKS:payload?.hks}))
        },
        onError(error) {
          store.dispatch(getPushSessionError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
  }
}

export default function* profileDataSaga() {
  yield takeEvery('profile/getProfileData', fetchProfileApi)
  yield takeEvery('profile/getProfileReadData', fetchProfileReadApi)
  yield takeEvery('profile/getProfileDeleteData', fetchProfileDeleteApi)
  yield takeEvery('profile/getProfileAvatarData', fetchProfileAvatarApi)
  yield takeEvery('profile/getProfileUpdateData', fetchProfileUpdateApi)
  yield takeEvery('profile/getUserAuthDevice', fetchUserAuthDeviceApi)
  yield takeEvery('profile/getPushSession', fetchPushSessionApi)
}
