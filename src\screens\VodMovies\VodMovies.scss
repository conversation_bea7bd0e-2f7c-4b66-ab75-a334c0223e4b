@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);

$color-white: #ffffff;
$font-family-roboto: Roboto;
$position-absolute: absolute;
$text-wrap-nowrap: nowrap;
$font-weight-bold: bold;
$box-sizing-border: border-box;
$position-relative: relative;
$display-flex: flex;

.vod-layout {
  width: 1920px;
  height: 930px;
  position: fixed;
  opacity: 1;

  .back-ground-images {
    background-size: 1432.67px 770px;
    height: 770px;
    width: 1920px;
    background-blend-mode: overlay;
    background-repeat: no-repeat;
    background-position: right;
  }

  .vod-details-layout {
    margin-left: 86px;
    top: 134px;
    width: 1585px;
    height: auto;
    opacity: 1;
    position: absolute;
  }

  .content-layout {
    height: auto;
    width: 1580px;
  }

  .content-title {
    height: auto;
    width: auto;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 72px;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .content-title1 {
    width: auto;
    color: #ffffff;
    font-family: Roboto;
    font-size: 72px;
    letter-spacing: 0;
    line-height: 72px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .moreInfo-tab-title {
    height: 88px;
    width: 1134px;
    color: $color-white;
    font-family: Roboto;
    font-size: 60px;
    letter-spacing: 0;
    line-height: 88px;
    margin-top: 30px;
  }

  .content-name {
    height: 28px;
    width: 280px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 52px;
  }

  .pipe {
    height: 28px;
    width: 7px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 26px;
    margin-right: 24px;
    margin-left: 24px;
  }

  .vod-season-details {
    height: 33px;
    width: 1600px;
    margin-top: 16px;
  }

  .vod-genero {
    height: 28px;
    width: 241px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    letter-spacing: 0;
    line-height: 60px;
    margin-right: 24px;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
  }

  .vod-duration-year {
    height: 28px;
    width: 54px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    letter-spacing: 0;
    line-height: 26px;
    margin-right: 24px;
  }

  .vod-duration-ano {
    height: 32px;
    width: 110px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 32px;
    letter-spacing: 0.25px;
    line-height: 32px;
    margin-right: 24px;
  }

  .duracion-ano-copy {
    height: 28px;
    width: 114px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 26px;
  }

  .genero {
    height: 28px;
    width: 241px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 26px;
  }

  .duration-year {
    height: 28px;
    width: 54px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 26px;
    margin-right: 15px;
  }

  .duration-age {
    height: 32px;
    width: 609px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 24px;
    letter-spacing: 0.25px;
    line-height: 32px;
    margin-right: 15px;
  }

  .duration-min-copy {
    height: 32px;
    width: 609px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 24px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 26px;
  }

  .stroke {
    box-sizing: $box-sizing-border;
    height: 32px;
    width: 128px;
    border: 3px solid $color-white;
    border-radius: 3px;
  }

  .vod-description-layouts {
    width: 1054px;
    margin-top: 60px;
    display: flex;
  }

  .vod-description-length-layouts {
    width: 1054px;
    margin-top: 50px;
    display: flex;
  }

  .vod-margin-layouts {
    width: 1554px;
    height: auto;
    margin-top: 16px;
  }

  .vod-description {
    width: 1054px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    letter-spacing: 0;
    line-height: 40px;
    display: flex;
  }

  .ltv-progressbar-position {
    width: 1080px;
    margin-top: 25px;
    margin-left: auto;
    margin-right: 846px;
    margin-bottom: 24px;
  }

  .button-layout {
    position: $position-relative;
    display: $display-flex;
    margin-top: 24px;
    width: 1650px;
    height: auto;
    flex-direction: row;
  }

  .button-reproducer {
    box-sizing: $box-sizing-border;
    height: 118px;
    width: 166px;
    border-radius: 12px;
    margin-right: 20px;
    border-color: #1a1a1a;
    background-color: #282828;
    border-radius: 5px;

    &:focus,
    &:active {
      box-shadow: 0 0 0 3px $color-white;
      border: 5px solid white;
      border-color: #1a1a1a;
    }
  }

  .button-icons {
    height: 104px;
    width: 152px;
    border-radius: 7px;
    margin-right: 24px;
    align-content: center;
    border: 4px solid transparent;
    &:focus {
          box-shadow: 0 0 0 3px $color-white;
          border-color:#121212;
        }
    &:active {
      border-radius: 12px;
      box-shadow: 0 0 0 3px $color-white;
      // border-color: #121212;
      background-color: #fff;
    }

    .button-icons-trailer {
      height: 104px;
      width: 152px;
      box-sizing: border-box;
      border-radius: 7px;
      background-color: #282828;
      margin-right: 24px;
      display: flex;
    }
  }

  .sub-button-inline-layout {
    display: flex;
    flex-direction: column;
    margin-bottom: 22px;
  }

  .subscription-price {
    color: #ffffff;
    font-family: Roboto;
    font-size: 32px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 32px;
    text-align: center;
    margin-top: 20px;
  }

  .subscription-price-alone{
    color: #ffffff;
     font-family: Roboto;
     font-size: 32px;
     font-weight: 500;
     letter-spacing: 0;
     line-height: 32px;
     text-align: center;
     margin-top: 37px;
   }
   
  .subscription-price-detail {
    color: #ffffff;
    font-family: Roboto;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 24px;
    text-align: center;
    margin-top: 5px;
  }

  .button-subscription-copy1 {
    box-sizing: border-box;
    height: 104px;
    width: 152px;
    margin-right: 24px;
    border-color: #121212;
    border-radius: 7px;
  }
  .button-subscription-copy2{
    box-sizing: border-box;
    height: 104px;
    width: 152px;
    margin-right: 24px;
    border-color: #121212;
    border-radius: 7px;
  }

  .button-subscription-reproducer {
    box-sizing: $box-sizing-border;
    height: 118px;
    width: 166px;
    border: 3px solid $color-white;
    border-radius: 12px;
    margin-right: 20px;
    border-color: #1a1a1a;
    border-radius: 5px;
    align-content: center;

    &:focus,
    &:active {
      box-shadow: 0 0 0 3px $color-white;
      border: 5px solid white;
      border-color: #121212;
    }
  }

  .button-subscription-icons {
    height: 104px;
    width: 152px;
    margin-right: 24px;
    border: 4px solid transparent;
    &:focus,
    &:active {
    box-shadow: 0 0 0 3px $color-white;
    border-radius: 12px;
  }
  }

  .button-reproducer-copy1 {
    box-sizing: border-box;
    background-color: #ffffff;
    height: 104px;
    display: flex;
    border-color: #1a1a1a;
    border-radius: 10px !important;
  }

  .button-chanelSubscription-reproducer {
    box-sizing: $box-sizing-border;
    height: 118px;
    width: 444px;
    border-radius: 12px;
    margin-right: 20px;
    border-color: #1a1a1a;
    background-color: #ecaf2a;
    outline: solid $color-white 3px;
    border-radius: 5px;

    &:focus,
    &:active {
      transform: scale(1.1);
      outline: 3px solid $color-white;
      border: 5px solid white;
      border-color: #1a1a1a;
    }
  }

  .button-chanelsubscription-icons {
    height: 104px;
    margin-right: 22px;
    flex: 0 0 auto;
    border: 4px solid transparent;
    &:focus,
    &:active {
    box-shadow: 0 0 0 3px $color-white;
    border-radius: 12px;
    }
  }

  .button-chanelsubscription-copy1 {
    box-sizing: border-box;
    height: 104px;
    background-color: #ecaf2a;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .textPosition{
      margin-left: 15px;
      margin-right: 15px;

    .chanelsubscription-yellow-button {
      height: 32px;
      color: #1e1e1e;
      font-family: Roboto;
      font-size: 28px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 32px;
      display: flex;
    }

    .chanelsubscription-yellow-button-detail {
      height: 32px;
      color: #1e1e1e;
      font-family: Roboto;
      font-size: 28px;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 5px;
      display: flex;
    }
  }
  }

  .chanelSubscription-price {
    height: 32px;
    width: 430px;
    color: #1e1e1e;
    font-family: Roboto;
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 32px;
    margin-top: 22px;
    display: flex;
    justify-content: center;
  }

  .chanelSubscription-price-detail {
    height: 32px;
    width: 430px;
    color: #1e1e1e;
    font-family: Roboto;
    font-size: 28px;
    letter-spacing: 0;
    line-height: 32px;
    margin-top: 5px;
    display: flex;
    justify-content: center;
  }

  .button-focus-border {
    box-sizing: $box-sizing-border;
    height: 79px;
    width: 111px;
    border: 2px solid $color-white;
    border-radius: 8px;
  }

  .icons-vcards {
    height: 64px;
    width: 64px;
    display: flex;
    margin: auto;
  }

  .tab-tray-wrapper {
    margin-top: 20px;
    margin-left: 86px;
    width: 1815px;
  }

  .tab-first-tray-wrapper {
    margin-top: 45px;
    margin-left: 88px;
    width: 1885px;
  }

  .recommended-tab {
    height: 80px;
    width: 340px;
    border-radius: 7px;
    margin-right: 28px;
    border-radius: 5px;

    &:focus {
      opacity: 1;
      border: solid 4px #ffffff;
      background-color: #1e1e1e;
    }

    &:active {
      background-color: #981c15;
    }
  }

  .recommended-tab-focus {
    height: 80px;
    width: 340px;
    border-radius: 7px;
    background-color: #981c15;
    margin-right: 28px;
    font-weight: bold;

    &:focus,
    &:active {
      box-shadow: 0 0 0 3px #ffffff;
      border: 4px solid white;
      border-color: #1a1a1a;
    }
  }

  .recommended-tab-default {
    height: 80px;
    width: 340px;
    border-radius: 7px;
    background-color: #981c15;
    margin-right: 28px;
    font-weight: bold;
  }

  .recomendation-title {
    height: 33px;
    width: 339px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    letter-spacing: 0;
    line-height: 30px;
    display: flex;
    justify-content: space-around;
  }

  .recomendation-title-focus {
    height: 33px;
    width: 339px;
    display: flex;
    justify-content: space-around;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
  }

  .button-text {
    font-family: $font-family-roboto;
    color: $color-white;
    height: 60px;
    width: 125px;
    font-size: 23px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
    margin: 10px auto;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .button-textfocus-out {
    font-family: $font-family-roboto;
    height: 60px;
    width: 125px;
    color: #ffffff;
    font-size: 23px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
    display: flex;
    margin: 10px auto;
    cursor: pointer;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .button-text-sub {
    height: 60px;
    width: auto;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 23px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
    margin-top: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .button-textfocus-out-sub {
    height: 60px;
    width: auto;
    color: #ffffff;
    font-family: Roboto;
    font-size: 23px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
    margin-top: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .container {
    display: $display-flex;
    flex-wrap: $text-wrap-nowrap;
    overflow-x: scroll;
    justify-content: flex-start;
    scroll-snap-type: x mandatory;

    .container-block {
      margin-right: 40px;
      margin-bottom: 22px;
      position: relative;
      border: 4px solid transparent !important;

      &:focus,
      &:active {
        border-radius: 4px;
        opacity: 1;
        scroll-snap-align: end;
        border: 5.6px solid #fff !important;
        opacity: 1;
        transform: scale(1);
      }

      .container-block:last-child :focus {
        margin-right: 0px;
      }
    }
  }

  .container::-webkit-scrollbar {
    height: 8px;
  }

  .container::-webkit-scrollbar-thumb {
    background-color: gray;
    border-radius: 10px;
  }

  .container::-webkit-scrollbar-track {
    background-color: $color-white;
    border-radius: 10px;
  }

  .cast-layout {
    margin-bottom: 50px;
    height: 364px;
    width: 1780px;
  }

  .Cast-img {
    height: 490px;
    width: 326px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
    display: $display-flex;
    flex-direction: column;
  }

  .notification-layout {
    background: #1a1a1a 0% 0% no-repeat padding-box;
    border: 2px solid #50595e;
    border-radius: 10px 0px 0px 10px;
    opacity: 1;
    width: 0;
    align-items: center;
    display: $display-flex;
    position: $position-absolute;
    white-space: $text-wrap-nowrap;
    height: 140px;
    top: 60px;
    right: 0;
    transition: cubic-bezier(1, 0, 0, 1);
    overflow-x: hidden;
  }

  .notification-layout.show {
    width: 750px;
    opacity: 1 !important;
  }

  .notification-text-layout {
    margin-left: 62px;
  }

  .notification-text {
    text-align: left;
    font: normal normal normal 28px/33px $font-family-roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin: 13px 0px;
    text-wrap: wrap;
    width: 500px !important;
  }

  .notification-path {
    text-align: left;
    font: normal normal normal 24px/28px $font-family-roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin: 13px 0px;
  }

  .mlt-layout {
    display: flex;
    flex-direction: column;
    text-align: justify;
    margin-left: 88px;

    .recomendation-content-title {
      height: 32px;
      color: #ffffff;
      font-family: Roboto;
      font-size: 32px;
      letter-spacing: 0;
      line-height: 32px;
      margin-top: 55px;
      margin-bottom: 20px;
    }

    .mlt-wrapper {
      display: flex;
      overflow-x: scroll;
      align-items: center;
      scroll-snap-type: x mandatory;

      .mlt-block {
        opacity: 0.8;
        margin-right: 40px;
        position: relative;
        border: 4px solid transparent !important;

        &:focus,
        &:active {
          border-radius: 4px;
          opacity: 1;
          scroll-snap-align: end;
          border: 4px solid #fff !important;
        }

        .mlt-block:last-child :focus {
          margin-right: 0px;
        }

        .mlt-card-image {
          display: flex;
          height: 232px;
          width: 412px;
        }

        .mlt-detail {
          position: absolute;
          bottom: 22px;
          margin-left: 25px;
          width: 250px;
          display: flex;
        }

        .default-titlerail-vod {
          height: 35px;
          width: 408px;
          color: $color-white;
          font-family: $font-family-roboto;
          font-size: 30px;
          letter-spacing: 0;
          line-height: 33px;
          margin-top: 40px;
        }
      }
    }
  }

  .moviestab-tray {
    height: 940px;
    position: fixed;

    margin-top: 20px;
    margin-left: 29px;
    width: 1802px;
  }

  .cast_block {
    height: 490px;
    width: 326px;
    word-wrap: break-word;
    position: relative;

    &:focus,
    &:active {
      z-index: 1;
      background: transparent 0% 0% no-repeat padding-box;
      box-shadow: 0px 0px 24px $color-white;
      border: 4px solid $color-white;
      outline: 1px solid $color-white;
      border-radius: 12px;
      opacity: 1;
      transform: scale(1);
    }
  }

  .Casts {
    width: 1770px;
    height: 545px;
  }

  .cast-image {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
    border-radius: 3px;
    opacity: 1;
    height: 490px;
    width: 326px;
  }

  .cast-card-image {
    height: 490px;
    width: 326px;
    border-radius: 3px;
    background-size: 100% 100%;
    background-blend-mode: overlay;
  }

  .cast-names {
    height: auto;
    max-height: 32px;
    width: 100%;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 28px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;
  }

  .cast-roles {
    height: auto;
    max-height: 32px;
    width: 100%;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 10px;
  }

  .cast-title {
    height: 32px;
    width: 113px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 32px;
    letter-spacing: 0;
  }

  .description {
    color: $color-white;
  }

  .description-vod {
    text-align: left;
    font: normal normal normal 24px/48px $font-family-roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
    margin: 0;
  }


  .cast-detail {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 111px;
    background: linear-gradient(180deg, rgb(113 79 79 / 0%) 0%, #000000c7 100%);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding-bottom: 10px;
    overflow: hidden;
  }

  .more-information-tab {
    height: 80px;
    width: 340px;
    border-radius: 7px;
    background-color: #1e1e1e;
    margin-right: 28px;

    &:focus {
      box-sizing: border-box;
      opacity: 1;
      box-shadow: 0 0 0 3px white;
    }

    &:active {
      background-color: #981c15;
    }
  }

  .more-info-layout {
    width: 1780px;
    height: 610px;
    margin-left: 86px;
    margin-top: 28px;
  }

  .info-detail-layout {
    float: left;
    height: 422px;
  }

  .info-description {
    width: 1126px;
  }

  .info-vod-detail {
    margin-top: 35px;
    width: 600px;
    margin-left: 160px;
    background-blend-mode: overlay;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
  }

  .infomore-description-layouts {
    display: $display-flex;
    width: 1750px;
    height: 425px;
    margin-top: 50px;
  }

  .info-description-detail {
    height: 400px;
    width: 1125px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 32px;
    letter-spacing: 0;
    line-height: 48px;
    background-blend-mode: overlay;
    background-repeat: no-repeat;
    overflow-y: scroll  !important;
    text-align: justify;
    margin-top: 35px;
    padding-bottom: 32px;

    &:focus,
    &:active {
      z-index: 1;
      background: transparent 0% 0% no-repeat padding-box;
      border: 4px solid $color-white;
      outline: 1px solid $color-white;
      border-radius: 12px;
      opacity: 1;
      transform: scale(1);
      overflow-y: scroll !important;
    }
  }
  .info-description-detail::-webkit-scrollbar {
    width: 8px; /* Adjust the width as needed */
    background-color: transparent; /* Set the background color of the scrollbar track */
    display: flex;
  }

  .info-description-detail::-webkit-scrollbar-thumb {
    background-color: #2e303d; /* Set the color of the scrollbar thumb */
    height: 180px;
  }

  .info-voddetail-description {
    height: 480px;
    width: 600px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 21.33px;
    letter-spacing: 0;
    line-height: 32px;
  }

  .info-duration {
    height: 40px;
    width: 459px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 36px;
    letter-spacing: 0;
    line-height: 40px;
  }

  .info-duration-copy {
    height: 42px;
    width: 459px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 36px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 39px;
  }

  .info-year {
    height: 40px;
    width: 459px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 36px;
    letter-spacing: 0;
    line-height: 40px;
    margin-top: 80px;
  }

  .info-year-copy {
    height: 42px;
    width: 459px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 36px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 39px;
  }

  .genero {
    height: 40px;
    width: 459px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 36px;
    letter-spacing: 0;
    line-height: 40px;
    margin-top: 80px;
  }

  .genero-copy {
    height: auto;
    width: 459px;
    color: $color-white;
    font-family: $font-family-roboto;
    font-size: 36px;
    font-weight: $font-weight-bold;
    letter-spacing: 0;
    line-height: 39px;
  }

  .duration-div1 {
    height: 55px;
    width: 306px;
  }

  .year-div2 {
    height: 55px;
    width: 306px;
    margin-top: 53px;
  }

  .genero-div {
    height: 127px;
    width: 306px;
    margin-top: 53px;
  }

  .error1 {
    text-align: center;
    font: normal normal normal 32px/38px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
    margin-bottom: 40px;
  }

  .vod-promo-card {
    width: 1800px;
    height: 54px;
    position: relative;
    top: 24px;
    bottom: 25px;
    text-align: justify;

    .vod-promo-first-title {
      color: $color-white;
      font-family: Roboto;
      font-size: 32px;
      font-weight: bold;
      letter-spacing: -0.7px;
      line-height: 24px;
    }

    .vod-promo-second-title {
      color: #00aaff;
      font-family: $font-family-roboto;
      font-size: 32px;
      font-weight: $font-weight-bold;
      letter-spacing: -0.7px;
      line-height: 24px;
    }
  }
}

.vod-player-warning-container {
  width: 1906px;
  height: 1030px;
  background: rgba(0, 0, 0, 0.7882352941) linear-gradient(90deg,
      #121212 0%,
      rgba(18, 18, 18, 0.9490196078) 27%,
      rgba(3, 21, 31, 0) 100%) 0% 0% no-repeat padding-box;
  top: 30px;
  position: relative;
  display: flex;
  flex-direction: column;

  .regresser-box {
    display: flex;
    justify-content: flex-end;
    width: 1920px;

    .talent-warning-backscreen {
      box-sizing: border-box;
      height: 58px;
      width: 298px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 20px;
      margin-left: 96px;
      margin-right: 53px;
      border-radius: 6.6px;
      background-color: #2e303d;

      &:focus,
      &:active {
        transform: scale(1.1);
        background: #981c15;
      }

      .talent-back-img-icon {
        margin-bottom: 17px;
        margin-left: 20px;
        margin-right: 3px;
        margin-top: 20px;
      }

      .talent-back-button-regresar-title {
        height: 33px;
        width: 208px;
        color: white;
        font-family: Roboto;
        font-size: 28px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 30px;
        text-align: center;
        margin-bottom: 17px;
        margin-right: 3px;
        margin-top: 20px;
      }
    }
  }

  .player-warning-button-container-div {
    text-align: left;
    opacity: 1;
    height: 840px;
    background: rgba(0, 0, 0, 0.7882352941) linear-gradient(90deg,
        #121212 0%,
        rgba(18, 18, 18, 0.9490196078) 27%,
        rgba(3, 21, 31, 0) 100%) 0% 0% no-repeat padding-box;
    position: relative;
    top: 35px;
    z-index: 1;
    width: 1899px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .player-warning-title {
      height: 50px;
      width: 523px;
      color: white;
      font-family: Roboto;
      font-size: 50px;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
    }

    .player-warning-button-div {
      flex-direction: column;
      display: flex;
      margin-top: 50px;
      margin-bottom: 75px;
    }

    .player-warning-title-button {
      display: flex;
      justify-content: center;
      background: #2e303d 0% 0% no-repeat padding-box;
      opacity: 1;
      border-radius: 4.6px;
      margin-bottom: 23px;
      height: 76px;
      width: 574px;
      border-radius: 7px;

      &:focus,
      &:active {
        z-index: 1;
        border-radius: 7px;
        opacity: 1;
        background: #981c15 0% 0% no-repeat padding-box;
        display: flex;
        justify-content: center;
        transform: scale(1.09);
        margin-bottom: 38px;
      }
    }

    .player-warning-title-button-Contents {
      width: 450px;
      height: 43px;
      color: white;
      font-family: roboto;
      font-size: 32px;
      font-weight: 700;
      letter-spacing: -0.587px;
      line-height: normal;
      text-align: center;
      margin-bottom: 20.25px;
      margin-top: 19.55px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-transform: uppercase;
    }

    .player-warning-title-begining-button-Contents {
      margin-top: 17px;
      margin-bottom: 17px;
      width: 605px;
      color: #fff;
      text-align: center;
      font-family: Roboto;
      font-size: 32px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -0.51px;
      text-transform: uppercase;
    }
  }

  .vod-container-loader {
    position: absolute;
    top: 207px;
    right: 767px;
  }
}