import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Player, PlayerEvent } from 'bitmovin-player'
import { UIFactory } from 'bitmovin-player/bitmovinplayer-ui'
import 'bitmovin-player/bitmovinplayer-ui.css'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import moment from 'moment'
import <PERSON><PERSON> from 'react-lottie-player'

import './Player.scss'
import {
  addWatchList,
  delWatchList,
  getWatchList,
  lastSeenWatch
} from '../../store/slices/getWatchListSlice'
import {
  clearRecordPlayerInfo,
  getMediaAPI,
  getRecordPlayer,
  getPlayerTrackAPI
} from '../../store/slices/PlayerSlice'
import {
  clrSubscriptionInfo,
  getVodSubscriptionInfo
} from '../../store/slices/settingsSlice'
import { playerImg } from './PlayerImages'
import { reqOptionsForGet } from '../../apiCall/query'
import {
  clearVodSeriesMlt,
  vodSeries,
  vodSeriesMLT
} from '../../store/slices/vodSeriesSlice'
import animationData from '../../../src/json/animationData.json'
import BuySubscriptionNew from '../appsettings/SubscriptionManagement/BuySubscriptionNew'
import FinPlayerEpisodeBanner from './../../components/FinPlayerEpisodeBanner'
import Language from '../../components/Language'
import PlayerEpisode from '../../components/PlayerEpisode'
import {
  pushContentPlayEvent,
  pushContentPauseEvent,
  pushContentForwardEvent,
  pushContentRewindEvent,
  pushPlayerInteractionEvent,
  pushContentProgressEvent,
  pushNewInteractionContentEvent
} from '../../GoogleAnalytics'
import {
  CONTENIDO_BLOQUEADO,
  INTERACTION_PLAYER,
  interactionType,
  MOVIE,
  NOT_APPLICABLE,
  SELECT_CONTENT,
  SERIES
} from '../../GoogleAnalyticsConstants'
import appInfo from '../../../amx/appinfo.json'
import { COMMON_URL } from '../../utils/environment'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import { setReturnFocusById } from '../../store/slices/SearchSlice'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const VodPlayer = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  var preloadedCertificate
  var image = new Image()

  // Using the useLocation hook to get the current location object
  const { state } = useLocation()
  const playerDiv = useRef('')
  const playerRef = useRef({})
  const skipIntroRef = useRef()
  const languageRef = useRef()
  const episodeRef = useRef()
  const addFavRef = useRef()
  const rmFavRef = useRef()
  const startedOverTimeRef = useRef(0)
  const startedOverRef = useRef(null)
  const rwFwRef = useRef(false)
  const lastSeekType = useRef('')
  const contentDataRef = useRef(null)
  const seekLongPressRef = useRef(false)
  const shouldWaitRef = useRef(false)
  const playerEpRef = useRef()
  const spriteHideTimeoutRef = useRef(null)
  const subscriptionKeyRef = useRef('')
  const currentSubscriptionInfoRef = useRef({})
  const trackStartTimer = useRef('') //This useRef will be used to store the setInterval function and to set again only whern this value is ''
  const playerDetroyComplete = useRef(false) // This useRef will help in understanding whether the player is destroyed ot not
  const lastSeekedTimeRef = useRef(0) //For storing seek value to use in trackApi.
  const region = localStorage.getItem('region')
  const lastTouch = localStorage.getItem('lasttouch')
  const deviceId = localStorage.getItem('npaw.npawDeviceUUID')

  let hideBar = 0
  let hideSkip = 0

  const [player, setPlayer] = useState(null)
  const [playerUi, setPlayerUi] = useState(null)
  const [currentVodData, setCurrentVodData] = useState([])
  const [currentButtonFocus, setCurrentButtonFocus] = useState('play')
  const [playerStatus, setPlayerStatus] = useState('')
  const [totalDuration, setTotalDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [showVodControls, setShowVodControls] = useState('hide')
  const [totalTime, setTotalTime] = useState(0)
  const [runningTime, setRunningTime] = useState(0)
  const [favorites, setFavorites] = useState(false)
  const [languageEnable, setLanguageEnable] = useState(false)
  const [pinpageEnable, setPinpageEnable] = useState(false)
  const [finPlayerEnable, setFinPlayerEnable] = useState(false)
  const [finPlayerClose, setFinPlayerClose] = useState(false)
  const [nxtEpDuration, setNxtEpDuration] = useState(false)
  const [episodeEnable, setEpisodeEnable] = useState(false)
  const [selSubs, setSelSubs] = useState(-1)
  const [selSubId, setSelSubId] = useState('')
  const [introStartTime, setIntroStartTime] = useState(null)
  const [introFinishTime, setIntroFinishTime] = useState(null)
  const [skipIntroShow, setSkipIntroShow] = useState(false)
  const [skipFocus, setSkipFocus] = useState(false)
  const [nxtGrpId, setNxtGrpId] = useState('')
  const [nxtEpData, setNxtEpData] = useState({})
  const [introFinished, setIntroFinished] = useState(false)
  const [playerClose, setPlayerClose] = useState(false)
  const [subscriptionEnable, setSubscriptionEnable] = useState(false)
  const [favClicked, setFavClicked] = useState(false)
  const [clickedNxtEp, setClickedNxtEp] = useState(false)
  const [fltrRecomm, setFltrRecomm] = useState([])
  const [spritesImages, setSpritesImages] = useState([])
  const [showSpritesImg, setShowSpritesImg] = useState(false)
  const [playbackRetry, setPlaybackRetry] = useState(false)
  const [streamType, setStreamType] = useState('')
  const [subsBtnInfo, setSubsBtnInfo] = useState({})
  const [enableAudSwitch, setEnableAudSwitch] = useState(false)
  const [enableSubSwitch, setEnableSubSwitch] = useState(false)
  const [audioContId, setAudioContId] = useState('')
  const [playbackFinished, setplaybackFinished] = useState(false)
  const [visiblePlayButton, setVisiblePlayButton] = useState(false)

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const vodDetails = useSelector(state => state?.login?.vcardDetails)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )
  const subscriptionsInfo =
    useSelector(state => state?.settingsReducer?.getVodSubsInfo?.response) ?? {}
  const subscriptionsLoading = useSelector(
    state => state?.settingsReducer?.isLoading
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const startheaderinfo = useSelector(
    state => state?.initialReducer?.startHeaderInfo?.response
  )
  const getWatchListRedux = useSelector(state => state?.watchList?.watchList)
  const addWatchListRedux = useSelector(state => state?.watchList?.addWatchList)
  const delWatchListRedux = useSelector(state => state?.watchList?.delWatchList)
  const getMediaRedux = useSelector(
    state => state?.player?.getMediaRes?.response
  )
  const getMediaError = useSelector(
    state => state?.player?.getMediaError?.errors?.[0]
  )

  const genreDetails = useSelector(state => state?.player?.genreDetails)
  const vodRecommRedux = useSelector(
    state => state?.getVodSeries?.seriesMLTData
  )
  const vodSeriesDataRedux = useSelector(
    state => state?.getVodSeries?.seriesData
  )
  const contentMenu = useSelector(state => state?.homeReducer?.contentMenu)
  const playerControlsInterval =
    apaMetaData?.playerControls_auto_hide_time &&
    JSON?.parse(apaMetaData?.playerControls_auto_hide_time)
  const spriteConfigDefault = {
    default: {
      default: {
        sprite_width_frame: '200',
        sprite_seconds_by_frame: '10',
        sprite_height_frame: '113',
        sprite_frames_by_row: '10'
      }
    }
  }
  //writing a fallback config when BE config is not available
  const spritesConfiguration = apaMetaData?.sprites_configuration
    ? JSON?.parse(apaMetaData?.sprites_configuration)
    : spriteConfigDefault
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const recordingplayer =
    useSelector(state => state?.player?.recordplayerinfo?.response) ?? {}
  const youboraInstance = useSelector(
    state => state?.player?.npawPluginInstance
  )

  const userId = vodDetails?.confirmscreen
    ? loginInfo?.user_id ?? registerInfo?.user_id
    : userDetails?.user_id
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const navData = useSelector(state => state?.homeReducer?.navbarData)
  let filteredRegionLists = apaMetaData?.byr_filterlist_configuration
    ? JSON.parse(apaMetaData?.byr_filterlist_configuration)
    : ''

  const supportedStream =
    apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)
  useEffect(() => {
    setFavClicked(true)
    setStreamType(
     !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
        ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
        : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
    )
    let accountInfo = loginInfo || registerInfo || userDetails
    subscriptionKeyRef.current =
      accountInfo?.paywayProfile?.subscriptions?.reduce(
        (resultant, value, index) =>
          index == 0 ? resultant + value?.key : resultant + ', ' + value?.key,
        ''
      )
    currentSubscriptionInfoRef.current = subscriptionsInfo
    fetchApi()
    const interval = setInterval(() => {
      updateTime()
    }, 1000)
    document.addEventListener('keyup', handleEvent)
    SpatialNavigation.focus()
    return () => {
      //The get media response to be cleared out when player component is unmounted
      localStorage.setItem('currentVod', '')
      localStorage.setItem('introFinished', '')
      setIntroFinished(false)
      document.removeEventListener('keyup', handleEvent)
      clearInterval(interval)
      subscriptionKeyRef.current = ''
      currentSubscriptionInfoRef.current = {}
      clearInterval(trackStartTimer.current)
      playerDetroyComplete.current = false
      contentDataRef.current = null
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', handleEvent)
  }, [episodeEnable])

  useEffect(() => {
    if (
      recordingplayer &&
      Object.keys(recordingplayer)?.length > 0 &&
      state?.page == 'record'
    ) {
      setCurrentVodData({
        ...recordingplayer,
        group: {
          common: {
            title: state?.recordTitle
          }
        }
      })
    }
  }, [recordingplayer])

  useEffect(() => {
    if (nxtGrpId && playerStatus == 'play') {
      dispatch(
        getVodSubscriptionInfo({
          userId: vodDetails?.confirmscreen
            ? loginInfo?.parent_id ?? registerInfo?.parent_id
            : userDetails?.parent_id,
          hks: vodDetails?.confirmscreen
            ? loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue
            : userDetails?.session_stringvalue,
          url: `group_id=${nxtGrpId}`
        })
      )
    }
  }, [nxtGrpId, playerStatus])

  useEffect(() => {
    showVodControls == 'vod' &&
      (!skipFocus
        ? document
            .getElementById(
              currentButtonFocus == 'seekbar'
                ? 'seekbar'
                : currentButtonFocus == 'rewind'
                ? 'rewind'
                : currentButtonFocus == 'forward'
                ? 'forward'
                : 'play'
            )
            ?.focus()
        : skipIntroRef.current?.focus())
    ;(showVodControls === 'hide' || episodeEnable || finPlayerEnable) &&
      skipIntroRef.current?.focus()
  }, [showVodControls, playerStatus, episodeEnable, finPlayerEnable, skipFocus])

  useEffect(() => {
    if (finPlayerEnable) {
      // Check if the Episodes carousel is open
      if (episodeEnable) {
        setEpisodeEnable(false)
      }
      if (
        !state?.trailerGetMedia &&
        !nxtGrpId &&
        !nxtEpData?.id &&
        currentVodData?.group?.common?.id
      ) {
        const nextId = currentVodData?.next_group_id
        dispatch(clrSubscriptionInfo())
        if (
          nextId &&
          currentVodData?.next_group?.common?.extendedcommon?.media?.episode
            ?.number
        ) {
          setNxtGrpId(nextId)
          setNxtEpData(currentVodData?.next_group)
        } else if (fltrRecomm?.length > 0 && !nextId) {
          setNxtGrpId(fltrRecomm[0]?.id)
          setNxtEpData(fltrRecomm[0])
        }
      }
    }
  }, [finPlayerEnable])

  useEffect(() => {
    if (
      (getMediaRedux?.media && Object.keys(getMediaRedux)?.length > 0) ||
      state?.trailerGetMedia
    ) {
      if (!nxtGrpId && !clickedNxtEp) {
        setCurrentVodData(state?.trailerGetMedia ?? getMediaRedux)
        localStorage.setItem('currentVod', JSON.stringify(getMediaRedux))
        !getMediaRedux?.next_group_id && callRecomm()
        audioContId && playNextContent(getMediaRedux)
      } else if (
        nxtEpData &&
        Object.keys(nxtEpData)?.length > 0 &&
        clickedNxtEp
      ) {
        if (pinpageEnable) {
          setPinpageEnable(false)
          contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
          navigate('/my-settings/help-And-Settings/security-pin/configure', {
            state: {
              data: 'Create',
              pageName: 'movies',
              gaContentData: contentDataRef.current,
              resume:
                getMediaRedux?.media?.initial_playback_in_seconds > 0
                  ? true
                  : false
            }
          })
        }
        playNextContent(getMediaRedux)
      }
    }
  }, [getMediaRedux || nxtGrpId])

  useEffect(() => {
    const code = getMediaError?.code
    if (getMediaRedux?.media || code) {
      if (code == 'PLY_PLY_00009') {
        setStreamType(
          supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
            ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
        )
        setPlaybackRetry(true)
      }
      if (playbackRetry) {
        setPlaybackRetry(false)
        setStreamType(
          !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
            ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
        )
      }
    }
  }, [getMediaRedux, getMediaError, playbackRetry])

  useEffect(() => {
    if (playbackRetry && streamType) {
      callGetMedia()
    }
  }, [playbackRetry, streamType])

  useEffect(() => {
    contentDataRef.current = {
      user_id:
        userDetails?.user_id || loginInfo?.user_id || registerInfo?.user_id,
      parent_id: userDetails?.parent_id,
      sign_up_method: 'correo electronico',
      suscriptions: subscriptionKeyRef.current?.toLowerCase(),
      user_type: watchFree ? 'anonimo' : 'registrado',
      device: COMMON_URL?.device_type,
      device_model: COMMON_URL?.device_model,
      device_name: COMMON_URL?.device_name,
      authpn: COMMON_URL?.authpn,
      content_subsection: NOT_APPLICABLE,
      content_section: contentMenu,
      country:
        userDetails?.country_code?.toLowerCase() ||
        loginInfo?.country_code?.toLowerCase() ||
        registerInfo?.country_code?.toLowerCase(),
      content_id: currentVodData?.group?.common?.id,
      content_name: currentVodData?.group?.common?.title?.toLowerCase(),
      content_type: currentVodData?.group?.common?.extendedcommon?.media
        ?.episode?.number
        ? SERIES
        : MOVIE,
      content_category: genreDetails,
      content_availability: 'por suscripcion',
      content_episode: '',
      modulo_name: 'player',
      content_price: '0.0',
      content_season: currentVodData?.group?.common?.extendedcommon?.media
        ?.episode?.season
        ? `temporada ${currentVodData?.group?.common?.extendedcommon?.media?.episode?.season}`
        : NOT_APPLICABLE,
      content_episode: currentVodData?.group?.common?.extendedcommon?.media
        ?.episode?.number
        ? `episodio ${currentVodData?.group?.common?.extendedcommon?.media?.episode?.number} ${currentVodData?.group?.common?.extendedcommon?.media?.serie?.title}`
        : NOT_APPLICABLE,
      provider:
        currentVodData?.group?.common?.extendedcommon?.media?.proveedor?.codigo?.toLowerCase()
    }
    if (currentVodData?.media?.certificate_url) {
      const xhr = new XMLHttpRequest()
      xhr.responseType = 'arraybuffer'
      xhr.open('GET', !nxtGrpId && currentVodData?.media?.certificate_url)
      xhr.addEventListener('loadend', function () {
        if (xhr.readyState === XMLHttpRequest.DONE) {
          if (xhr.status === 200) {
            preloadedCertificate = xhr.response
            currentVodData?.media?.certificate_url &&
              !player &&
              !nxtGrpId &&
              setupPlayer()
          }
        }
      })
      xhr.send()
    } else if (currentVodData?.media?.video_url) {
      setupPlayer()
    }
  }, [currentVodData])

  useEffect(() => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    const finPlayerCheck = document.getElementById('finPlayer')
    videoElement?.duration && handleTotDuration(videoElement?.duration)
    setNxtEpDuration(videoElement?.currentTime)
    if (
      videoElement?.currentTime <=
      videoElement?.duration - handleFinPlayerRolling(videoElement?.duration)
    ) {
      setFinPlayerClose(false)
    }
    if (
      !state?.trailerGetMedia &&
      videoElement?.currentTime >=
        videoElement?.duration -
          handleFinPlayerRolling(videoElement?.duration) &&
      !finPlayerClose
    ) {
      const nextContent = currentVodData?.next_group ?? fltrRecomm[0]
      const checkId =
        currentVodData?.next_group_id ?? currentVodData?.group?.common?.id
      setFinPlayerEnable(
        (checkId && nextContent && Object.keys(nextContent)?.length > 0) ||
          fltrRecomm?.length > 0
          ? true
          : false
      )
      if (
        (checkId && nextContent && Object.keys(nextContent)?.length > 0) ||
        fltrRecomm?.length > 0
      ) {
        contentDataRef.current['modulo_name'] = 'banner fin player'
      }
    }
    if (
      Math.round(videoElement?.currentTime) ===
        Math.round(videoElement?.duration) &&
      !finPlayerCheck &&
      !subscriptionEnable &&
      !clickedNxtEp
    ) {
      visiblePlayButton && setplaybackFinished(true)
      !state?.trailerGetMedia &&
      nxtGrpId &&
      fltrRecomm?.length === 0 &&
      subscriptionsInfo?.playButton?.visible == 1
        ? autoPlayback()
        : !subscriptionsLoading && !episodeEnable && setPlayerClose(true)
    }
    const thumbWidth = 18
    const range = document.querySelector('#progressRange')
    const tooltip = document.querySelector('.sprites-image-pos')

    if (range && tooltip) {
      const maxLeft = 1000
      const minLeft = 0

      var off =
        (range.clientWidth - thumbWidth) /
        (parseInt(range.max) - parseInt(range.min))
      var px =
        (range.valueAsNumber - parseInt(range.min)) * off -
        tooltip.clientWidth / 2 +
        thumbWidth / 2
      px = Math.max(minLeft, Math.min(px, maxLeft))
      tooltip.style.left = px + 'px'

      // Handle input changes dynamically
      range.oninput = function () {
        let px =
          (range.valueAsNumber - parseInt(range.min)) * off -
          tooltip.clientWidth / 2 +
          thumbWidth / 2
        px = Math.max(minLeft, Math.min(px, maxLeft))
        tooltip.style.left = px + 'px'
      }
    }
  }, [moment().unix()])

  useEffect(() => {
    let seriesData = []
    if (
      !currentVodData?.group?.common?.extendedcommon?.media?.episode?.number
    ) {
      if (vodRecommRedux?.length > 0) {
        setFltrRecomm(vodRecommRedux)
        setNxtGrpId(vodRecommRedux[0]?.id)
      } else {
        setNxtGrpId('')
      }
    } else {
      vodRecommRedux?.length > 0 &&
        vodRecommRedux?.map(item => {
          item?.is_series && seriesData?.push(item)
        })
      if (seriesData?.length > 0) {
        setFltrRecomm(seriesData)
        setNxtGrpId(vodRecommRedux[0]?.id)
      } else {
        setNxtGrpId('')
      }
    }
  }, [vodRecommRedux])

  useEffect(() => {
    if (getWatchListRedux && getWatchListRedux?.length > 0 && favClicked) {
      favorites && currentButtonFocus == 'addFav'
        ? setCurrentButtonFocus('removeFav')
        : currentButtonFocus != 'play' &&
          currentButtonFocus != 'skipIntro' &&
          currentButtonFocus == 'removeFav' &&
          setCurrentButtonFocus('removeFav')
      setFavClicked(false)
      getWatchListRedux.map(item => {
        currentVodData?.group?.common?.id === item.id && setFavorites(true)
      })
    }
  }, [getWatchListRedux])

  useEffect(() => {
    if (delWatchListRedux?.msg === 'OK') {
      currentButtonFocus == 'removeFav' && setCurrentButtonFocus('addFav')
      callGetWatchList(delWatchListRedux)
      setFavorites(false)
    }
  }, [delWatchListRedux])

  useEffect(() => {
    if (addWatchListRedux?.msg === 'OK') {
      currentButtonFocus == 'addFav' && setCurrentButtonFocus('removeFav')
      setFavorites(true)
      callGetWatchList(addWatchListRedux)
    }
  }, [addWatchListRedux])

  useEffect(() => {
    if (
      playerClose &&
      (playerStatus == 'play' ||
        playerStatus == 'pause' ||
        playerStatus == 'error')
    ) {
      clearVodData()
      player?.destroy().then(() => {
        playerDetroyComplete.current = true
        return goToMoviesSeries()
      })
    } else {
      if (playerClose) {
        clearVodData()
        if (Object.keys(playerRef.current)?.length > 0) {
          if (player.current?.destroy) {
            player.current?.destroy().then(() => {
              playerDetroyComplete.current = true
              goToMoviesSeries()
            })
          }
        } else if (player) {
          player?.destroy().then(() => {
            goToMoviesSeries()
          })
        } else {
          goToMoviesSeries()
        }
        setPlayerClose(false)
      }
    }
  }, [playerClose, playerStatus])

  useEffect(() => {
    !languageEnable &&
      currentButtonFocus == 'language' &&
      languageRef.current?.focus()
  }, [languageEnable])

  useEffect(() => {
    !episodeEnable &&
      currentButtonFocus == 'tempo' &&
      episodeRef.current?.focus()
    episodeRef.current?.blur()
    document.getElementById('tempo')?.focus()
  }, [episodeEnable])

  useEffect(() => {
    if (
      subsBtnInfo?.playButton?.visible ||
      (subscriptionsInfo?.playButton?.visible && finPlayerEnable)
    ) {
    }
  }, [subsBtnInfo, subscriptionsInfo, finPlayerEnable])

  useEffect(() => {
    if (
      subscriptionsInfo &&
      Object.keys(subscriptionsInfo)?.length > 0 &&
      nxtGrpId
    ) {
      !finPlayerEnable && setSubsBtnInfo(subscriptionsInfo)
      setNxtGrpId('')
      if ((enableAudSwitch || enableSubSwitch) && audioContId) {
        setStreamType(
          audioContId == 'or'
            ? !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
              ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
              : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
            ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
            : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
        )
        setPlaybackRetry(true)
      }
    }
  }, [subscriptionsInfo])

  // spinner loader
  let progress = document.querySelector('.vod-custom-slider')
  let subtitle = document.querySelector('.bmpui-ui-subtitle-overlay')
  if (progress && subtitle) {
    subtitle.classList.add('bmpui-controlbar-visible')
  }
  if (!progress && subtitle) {
    subtitle.classList.remove('bmpui-controlbar-visible')
  }

  const playerConfig = {
    key: 'fc3afd27-8180-4ac4-80ef-dbb6854f9637', //older-key for ref d0584d33-f55f-4af6-8625-8b967ea115ae
    ui: false,
    tweaks: {
      file_protocol: true,
      app_id: 'com.bitmovin.claro.smarttvapp'
    },
    playback: {
      muted: true
    },
    analytics: {
      key: '6dc21e2a-820a-4b2f-b7d1-dc94e252e49a',
      config: {
        origin: 'com.bitmovin.claro.smarttvapp'
      }
    },
    network: {
      retryHttpRequest: function (type, response) {
        // delay the retry by 1 second
        return new Promise(function (resolve) {
          setTimeout(function () {
            resolve(response.request)
            youboraInstance
              ?.getAdapter()
              ?.fireError(
                response?.status,
                response?.statusText,
                response?.request
              )
          }, 1000)
        })
      }
    }
  }

  const fetchApi = () => {
    callGetWatchList('')
    dispatch(
      vodSeries({
        id: getMediaRedux?.group?.common?.id,
        userId,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids
      })
    )
  }

  const setPlay = () => {
    setPlayerStatus('play')
  }

  const toUTF8 = str => {
    const encoded = encodeURIComponent(str)
    const utf8 = unescape(encoded)
    const result = new Uint8Array(utf8?.length)
    for (var i = 0; i < utf8?.length; ++i) {
      result[i] = utf8?.charCodeAt(i)
    }
    return result?.buffer
  }

  const setupPlayer = () => {
    const playerInstance = new Player(playerDiv?.current, playerConfig)
    const adapterStringObject = COMMON_URL?.npawAdapterString
if(CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn'){
 const playerSource = {
      smooth: currentVodData?.media?.video_url,
      drm: {
        playready: {
          LA_URL: currentVodData?.media?.server_url,
          headers: {
            'X-DRM-Message': btoa(
              JSON.stringify({
                token: JSON.parse(currentVodData?.media?.challenge)?.token,
                device_id: COMMON_URL?.device_id
              })
            ),
            'Content-Type': 'text/xml'
          },
          utf8message: true,
          plaintextChallenge: true
        }
      },
      options: {
        startOffset: currentVodData?.media?.initial_playback_in_seconds || 0.001
      }
    }
 if (
      location.protocol === 'https:' &&
      playerSource['smooth'].toLowerCase().indexOf('http:') === 0
    ) {
      console.warn('Stream URL is only HTTP, attempting re-write to HTTPS')
      playerSource['smooth'] = playerSource['smooth'].replace(
        /http:/i,
        'https:'
      )
    }
}else{
    const playerSource = {
      dash: currentVodData?.media?.video_url,
      ...(currentVodData?.media?.server_url &&
        currentVodData?.media?.challenge && {
          drm: {
            widevine: {
              LA_URL: currentVodData?.media?.server_url,
              serverCertificate: preloadedCertificate,
              prepareMessage: function (keyMessage) {
                return new Uint8Array(
                  toUTF8(
                    JSON.stringify({
                      token: JSON.parse(currentVodData?.media?.challenge)
                        ?.token,
                      device_id: COMMON_URL?.device_id,
                      widevineBody: btoa(
                        String.fromCharCode.apply(
                          null,
                          new Uint8Array(keyMessage.message)
                        )
                      )
                    })
                  )
                ).buffer
              },
              title: currentVodData?.group?.common?.title,
              description: currentVodData?.group?.common?.description
            },
            //As part of player VST optimization the license request to be done immediately
            immediateLicenseRequest: true
          }
        })
    }
}

    var myUiManager
    // The default UI of bitmovin is not needed for live, hence added check as only for vod as of now
    // UIFactory.buildDefaultTvUI(playerInstance)
    myUiManager = UIFactory.buildDefaultUI(playerInstance)

    let progress = document.querySelector('.bmpui-ui-controlbar')
    if (progress && !playerStatus) {
      progress.classList.remove('progress-bar-shown')
      progress.classList.add('progress-bar-hide')
    }
    setShowVodControls('hide')
    youboraInstance &&
      youboraInstance?.registerAdapter(playerInstance, adapterStringObject)
    let analyticsOptions = {
      'app.name': appInfo?.title,
      'app.releaseVersion': appInfo?.version,
      'content.title': currentVodData?.group?.common?.title ?? 'NA',
      'content.program':
        currentVodData?.group?.common?.extendedcommon?.media?.originaltitle ??
        'NA',
      'content.channel': 'N/A',
      'content.duration': getMediaRedux?.media?.duration?.seconds ?? 'NA',
      'content.streamingProtocol': 'DASH',
      'content.playbackType': 'VOD',
      'content.customDimension.1':
        loginInfo?.paywayProfile?.paymentMethods?.[0]?.user_category ?? 'NA',
      'content.customDimension.2': subscriptionKeyRef.current ?? 'NA',
      'content.customDimension.3':
        currentSubscriptionInfoRef.current?.playButton?.key ?? 'NA',
      'content.customDimension.4':
        getMediaRedux?.group?.common?.extendedcommon?.media?.originaltitle ??
        'NA',
      'content.customDimension.5': userDetails?.email ?? 'NA',
      'content.customDimension.6':
        currentSubscriptionInfoRef.current?.playButton?.paymentmethod
          ?.gateway ?? 'NA',
      'content.customDimension.7': userDetails?.parent_id ?? 'NA',
      'content.customDimension.8': userDetails?.user_id ?? 'NA',
      'content.customDimension.9': region,
      'content.customDimension.10': state?.trailerGetMedia ? 1 : 0,
      'content.customDimension.11': deviceId ?? COMMON_URL?.device_id ?? 'NA',
      'content.customDimension.12':
        `${
          localStorage.getItem('hks') ??
          loginInfo?.session_stringvalue ??
          registerInfo?.session_stringvalue ??
          userDetails?.session_stringvalue
        }${new Date().toTimeString().split(' ')[0]}` ?? 'NA',
      'content.customDimension.13': `${
        typeof tizen == 'undefined' ? 'WebOS' : 'Tizen'
      }`,
      'content.customDimension.14': appInfo?.version,
      'content.customDimension.15': 'N/A',
      'content.customDimension.16': userDetails?.subregion ?? 'NA',
      'device.name': typeof tizen == 'undefined' ? 'LG' : 'Samsung',
      'device.osName': typeof tizen == 'undefined' ? 'WebOS' : 'Tizen',
      'device.type': 'Smart TV',
      'user.name': userDetails?.user_id ?? 'NA'
    }
    playerInstance.load(playerSource).then(
      () => {
        const videoElement = document.getElementById(
          'bitmovinplayer-video-player'
        )
        const playedDuration = state?.resume
          ? runningTime !== 0
            ? runningTime
            : currentVodData?.media?.initial_playback_in_seconds
          : 0
        if (youboraInstance) {
          youboraInstance?.setAnalyticsOptions(analyticsOptions)

          youboraInstance?.setLogLevel(5)
        }
        playerInstance.on(PlayerEvent.Error, e => {
          setPlayerStatus('error')
          youboraInstance?.getAdapter()?.fireError(e?.code, e?.message, e?.data)
        })

        playerInstance?.on(PlayerEvent.Warning, e =>
          youboraInstance?.getAdapter()?.fireError(e?.code, e?.message, e?.data)
        )
        playerInstance?.on(PlayerEvent.StallStarted, e => {
          playerStatus == 'play' &&
            youboraInstance
              ?.getAdapter()
              ?.fireError(e?.code, e?.message, e?.data)
        })

        playerInstance?.on(PlayerEvent.Play, () =>
          pushContentPlayEvent(contentDataRef.current)
        )

        if (playerInstance?.isPlaying()) {
          playerInstance?.pause()
        } else {
          if (playedDuration > 0) {
            !playerClose &&
              playerInstance?.play()?.then(() => {
                //below is the dispatch of track/view(Start) API
                dispatch(
                  getPlayerTrackAPI({
                    url: getMediaRedux?.tracking?.urls?.view
                      ?.replace('(', '')
                      ?.replace(')', '') // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
                  })
                )
                if (trackStartTimer.current == '') {
                  //below will be the setInterval function as we need to update BE peridically about the running time of the video
                  trackStartTimer.current = setInterval(() => {
                    //below is the dispatch of track/tick(Update) API
                    dispatch(
                      getPlayerTrackAPI({
                        url: getMediaRedux?.tracking?.urls?.tick
                          ?.replace('(', '')
                          ?.replace(')', ''), // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
                        trackType: 'tick',
                        timecode: Math.round(videoElement?.currentTime)
                      })
                    )
                  }, getMediaRedux?.tracking?.policies?.tick_interval * 1000 || 60000)
                }
                playerInstance?.unmute()
                hideBar = 0
                localStorage.setItem('hideTimer', playedDuration)
                setShowVodControls('vod')
                const languageData = currentVodData?.language?.options
                const audioData = playerInstance?.getAvailableAudio()
                const subtitleData = playerInstance?.subtitles?.list()
                setEnableAudSwitch(audioData?.length === 1)
                setEnableSubSwitch(subtitleData?.length === 0)
                const isCurrentLang = languageData?.findIndex(
                  item => item.is_current
                )
                const langFromSettings =
                  localStorage.getItem('vodContentLanguage') ||
                  languageData?.[isCurrentLang]?.option_id

                const convertedSelSettingAud =
                  langFromSettings && langFromSettings?.slice(-2)?.toLowerCase()
                const idx =
                  languageData?.length > 0 &&
                  languageData.findIndex(
                    item =>
                      item?.option_id ===
                      (langFromSettings === 'O-OR' ? 'O-EN' : langFromSettings)
                  )
                idx >= 0 &&
                  enableSubtitles(
                    playerInstance,
                    languageData?.[idx],
                    -1,
                    convertedSelSettingAud
                  )
              })
            playerInstance?.seek(playedDuration)
            //GA: Content Forward Event
          } else {
            !playerClose &&
              playerInstance &&
              playerInstance?.play()?.then(() => {
                //below is the dispatch of track/view(Start) API
                dispatch(
                  getPlayerTrackAPI({
                    url: getMediaRedux?.tracking?.urls?.view
                      ?.replace('(', '')
                      ?.replace(')', '') // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
                  })
                )
                if (trackStartTimer.current == '') {
                  //below will be the setInterval function as we need to update BE peridically about the running time of the video
                  trackStartTimer.current = setInterval(() => {
                    //below is the dispatch of track/tick(Update) API
                    dispatch(
                      getPlayerTrackAPI({
                        url: getMediaRedux?.tracking?.urls?.tick
                          ?.replace('(', '')
                          ?.replace(')', ''), // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
                        trackType: 'tick',
                        timecode: Math.round(videoElement?.currentTime)
                      })
                    )
                  }, getMediaRedux?.tracking?.policies?.tick_interval * 1000 || 60000)
                }
                playerInstance?.unmute()
                hideBar = 0
                localStorage.setItem('hideTimer', playedDuration + 1)
                setShowVodControls('vod')
                const languageData = currentVodData?.language?.options
                const audioData = playerInstance?.getAvailableAudio()
                const subtitleData = playerInstance?.subtitles?.list()
                setEnableAudSwitch(audioData?.length === 1)
                setEnableSubSwitch(subtitleData?.length === 0)
                const isCurrentLang = languageData?.findIndex(
                  item => item.is_current
                )

                const langFromSettings =
                  localStorage.getItem('vodContentLanguage') ||
                  languageData?.[isCurrentLang]?.option_id
                const convertedSelSettingAud =
                  langFromSettings && langFromSettings?.slice(-2)?.toLowerCase()
                const idx =
                  languageData?.length > 0 &&
                  languageData.findIndex(
                    item =>
                      item?.option_id ===
                      (langFromSettings === 'O-OR' ? 'O-EN' : langFromSettings)
                  )
                idx >= 0 &&
                  enableSubtitles(
                    playerInstance,
                    languageData?.[idx],
                    -1,
                    convertedSelSettingAud
                  )
              })
          }
        }
        const spritesCheck = currentVodData?.group?.common?.image_sprites
        if (spritesCheck) {
          image.setAttribute('crossOrigin', 'anonymous')
          image.onload = handleSpritesImage
          image.src = spritesCheck
        }
        currentVodData?.group?.common?.extendedcommon?.media?.episode?.number &&
          currentVodData?.next_group_id &&
          setNxtGrpId(currentVodData?.next_group_id)
        setPlayer(playerInstance)
        playerRef.current = playerInstance
        setPlayerUi(myUiManager)
        playerInstance.on(PlayerEvent.Ready, () => {
          setPlay()
        })
        if (
          !currentVodData?.group?.common?.extendedcommon?.media?.episode?.number
        ) {
          callRecomm()
        }
      },
      e => {
        console.warn('Error while loading source')
        youboraInstance?.setAnalyticsOptions(analyticsOptions)
        youboraInstance?.setLogLevel(5)
        youboraInstance?.getAdapter()?.fireError(e)
        myUiManager?.release()
        //The below method destroy is to remove the player instance including the added UI
        playerInstance?.destroy()
      }
    )
  }

  const handleFinPlayerRolling = duration => {
    if (duration <= 300 && duration > 0) {
      return 3
    } else if (duration <= 900) {
      return 10
    } else {
      return 30
    }
  }

  const handleEvent = event => {
    let codes = ''
    const languageCheck = document.getElementById('language')
    const episodeCheck = document.getElementById('playerEpisode')
    const finPlayerCheck = document.getElementById('finPlayer')
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch([
        'ColorF0Red',
        'ColorF2Yellow',
        'ColorF3Blue',
        'ColorF1Green',
        'ChannelUp',
        'ChannelDown'
      ])
      codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code,
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
    }

    //player controls
    let language = document.getElementById('language')
    const videoElement = document.getElementById('bitmovinplayer-video-player')

    //Only for the Arrowup and ok button. the player controls are made visible
    if (!language && (event?.key == 'ArrowUp' || event?.keyCode === 13)) {
      hideBar = 0
      localStorage.setItem('hideTimer', Math.round(videoElement?.currentTime))
      setShowVodControls('vod')
    }
    const hideTimer = localStorage.getItem('hideTimer')
    const progressbarId = document.getElementById('progressRange')

    if (
      parseInt(hideTimer) > 0 &&
      (event?.key == 'ArrowUp' ||
        event?.key == 'ArrowDown' ||
        event?.key == 'ArrowLeft' ||
        event?.key == 'ArrowRight')
    ) {
      hideBar = 0
      localStorage.setItem('hideTimer', Math.round(videoElement?.currentTime))
    }

    if (
      (languageCheck || episodeCheck) &&
      (event?.keyCode === 10009 ||
        event?.keyCode === 461 ||
        event?.keyCode === 405 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') && event?.keyCode === 120) ||

        codes.yellowcode === event?.keyCode)
    ) {
      languageCheck && setLanguageEnable(false)
      episodeCheck && setEpisodeEnable(false)
    }
    if (
      finPlayerCheck &&
      (event?.keyCode === 10009 ||
        event?.keyCode === 8 ||
        event?.keyCode === 461 ||
        event?.keyCode === 406 ||
        codes.bluecode === event?.keyCode)
    ) {
      setFinPlayerClose(true)
      setFinPlayerEnable(false)
    }
    if (
      event?.keyCode === 403 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') && event?.keyCode === 118) ||

      codes.redcode === event?.keyCode ||
      event?.code == 'KeyR' ||
      event?.keyCode === 82
    ) {
      if (showVodControls == 'vod' || progressbarId) handleStartOver()
    }
    if (
      !languageCheck &&
      !episodeCheck &&
      !finPlayerCheck &&
      (event?.keyCode === 10009 ||
        event?.keyCode === 8 ||
        event?.keyCode === 461 ||
        event?.keyCode === 405 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') && event?.keyCode === 120) ||

        codes.yellowcode === event?.keyCode)
    ) {
      goBack()
    }
    if (event?.keyCode === 415 || event?.keyCode === 19) {
      handlePlayPause()
    }
    if (event?.keyCode === 417) {
      handleForward()
    }
    if (event?.keyCode === 412) {
      handleRewind()
    }
  }

  const updateTime = () => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    const languageCheck = document.getElementById('language')
    const episodeCheck = document.getElementById('playerEpisode')
    playerRef.current &&
      Object.keys(playerRef.current)?.length > 0 &&
      playerRef.current?.on(PlayerEvent.Seeked, () => {
        seekLongPressRef.current = false
      })

    if (!seekLongPressRef.current) {
      setCurrentTime(
        moment.utc(videoElement?.currentTime * 1000).format('HH:mm:ss')
      )
      setRunningTime(videoElement?.currentTime)
      //Below logic is employed for checking the duration match for progress bar GA event. 
      switch (true) {
        case Math.floor(videoElement?.duration / 4) ==
          Math.floor(videoElement?.currentTime):
          pushContentProgressEvent(contentDataRef.current, 25)
          break
        case Math.floor(videoElement?.duration / 2) ==
          Math.floor(videoElement?.currentTime):
          pushContentProgressEvent(contentDataRef.current, 50)
          break
        case Math.floor(videoElement?.duration / 1.333) ==
          Math.floor(videoElement?.currentTime):
          pushContentProgressEvent(contentDataRef.current, 75)
          break
        case Math.floor(videoElement?.duration) ==
          Math.floor(videoElement?.currentTime):
          pushContentProgressEvent(contentDataRef.current, 100)
          break
      }
    }
    parseInt(localStorage.getItem('hideTimer')) > 0 &&
    !languageCheck &&
    !seekLongPressRef.current &&
    !episodeCheck
      ? (hideBar += 1)
      : ''
    if (
      hideBar === (playerControlsInterval?.default?.seconds ?? 12) &&
      !shouldWaitRef.current
    ) {
      setShowVodControls('hide')
      localStorage.setItem('hideTimer', 0)
      hideBar = 0
    }
    const skip = document.getElementById('skipIntro')
    const skipVisible = skip?.classList?.contains('skip-intro-alone')
    const skipShown = skip?.style['display']
    const rewind = document.getElementById('rewind')
    skipShown === '' && !rewind ? (hideSkip += 1) : ''
    setSkipFocus(skipShown === '' && !seekLongPressRef.current ? true : false)
    skipIntroShowHide(videoElement?.currentTime)
    if (skipVisible) {
      skipIntroRef.current?.focus()
    }
    if (skipVisible && !rewind && hideSkip === 7) {
      hideSkip = 0
      setSkipIntroShow(false)
    } else if (rewind) {
      setSkipIntroShow(true)
    }
  }

  const goBack = () => {
    if (episodeEnable) {
      return setEpisodeEnable(false), setCurrentButtonFocus('tempo')
    }
    if (finPlayerEnable) {
      pushNewInteractionContentEvent(
        {modulo_name:'banner fin player', ...contentDataRef.current},
        INTERACTION_PLAYER,
        currentVodData?.group?.common?.extendedcommon?.media?.episode?.number
          ? SERIES
          : MOVIE,
        interactionType.CERRAR
      )
      setFinPlayerClose(true)
      return setFinPlayerEnable(false)
    }
    if (!episodeEnable) {
      pushNewInteractionContentEvent(
        contentDataRef.current,
        INTERACTION_PLAYER,
        currentVodData?.group?.common?.extendedcommon?.media?.episode?.number
          ? SERIES
          : MOVIE,
        interactionType.SALIR
      )
      setPlayerClose(true)
    }
  }

  const goToMoviesSeries = () => {
    setCurrentVodData([])
    const vodData =
      currentVodData?.group?.common ?? getMediaRedux?.group?.common
    if (vodData?.extendedcommon?.media?.episode?.number) {
      const getEpisodeData =
        vodSeriesDataRedux &&
        vodSeriesDataRedux?.seasons?.find(
          itrObj =>
            itrObj.number == vodData?.extendedcommon?.media?.episode?.season
        )
      navigate('/series', {
        state: {
          data:
            state?.vCardBackFocus == 'trailer'
              ? currentVodData?.group?.common ?? getMediaRedux?.group?.common
              : watchFree
              ? playbackFinished
                ? currentVodData?.next_group?.common ??
                  getMediaRedux?.next_group?.common
                : vodData
              : playbackFinished
              ? currentVodData?.next_group?.common ??
                getMediaRedux?.next_group?.common ??
                currentVodData?.group?.common ??
                getMediaRedux?.group?.common
              : vodData,
          inputValue: state?.inputValue,
          returnPage: state?.returnPage,
          EpisodeData: state?.episodeData,
          episodeItemClicked: !playbackFinished && state?.episodeItemClicked,
          backfocusid: state?.backfocusid,
          vCardBackFocus: state?.vCardBackFocus,
          fromPage: 'vodplayer',
          seasonTabStatus: state?.seasonTabStatus,
          episodeIndexToFocus:
            getEpisodeData &&
            getEpisodeData?.episodes?.findIndex(
              each =>
                each?.episode_number ==
                vodData?.extendedcommon?.media?.episode?.number
            ),
          currentPlayingSeasonData: vodData?.extendedcommon?.media?.episode,
          trailerNode:
            currentVodData?.group?.common?.extendedcommon?.media?.haspreview
        },
        replace: true
      })
      setplaybackFinished(false)
    } else if (state?.page == 'record') {
      if (state?.returnPage == 'search') {
        navigate('/search', {
          state: { inputValue: state?.inputValue },
          replace: true
        })
        localStorage.setItem('currNavIdx', -1)
        dispatch(getNavTabValue('search'))
        dispatch(setReturnFocusById(state?.backfocusid))
        dispatch(getRecordPlayer())
      } else {
        if (state?.returnPage == 'episodescreen') {
          navigate('/episodescreen', {
            state: {
              myContentData: true,
              backfocusid: state?.backfocusid,
              episodes: state?.selectedContent?.actions?.episodes,
              sericetitle: state?.selectedContent?.channel?.event?.name,
              seasonid: state?.selectedContent?.channel?.event?.ext_season_id
            },
            replace: true
          })
        } else {
          navigate('/home', {
            state: { myContentData: true, backfocusid: state?.backfocusid },
            replace: true
          })
        }
        localStorage.setItem('currNavIdx', navData?.length - 1),
          dispatch(getNavTabValue('miscontenidos'))
      }

      dispatch(clearRecordPlayerInfo())
    } else {
      navigate('/movies', {
        state: {
          vodData,
          inputValue: state?.inputValue,
          returnPage: state?.returnPage,
          backfocusid: state?.backfocusid,
          vCardBackFocus: state?.vCardBackFocus,
          fromPage: 'vodplayer'
        },
        replace: true
      })
    }
    clearVodData(true)
  }

  const autoPlayback = () => {
    setClickedNxtEp(true)
    watchFree
      ? dispatch(
          getMediaAPI({
            id: currentVodData?.next_group_id,
            payway_token: subscriptionsInfo?.playButton?.payway_token,
            type: 'watchfree'
          })
        )
      : callGetMedia()
  }

  const callGetMedia = () => {
    const contentIdCheck =
      (enableSubSwitch || enableAudSwitch) && audioContId != 'or'
    const payload = {
      id: currentVodData?.next_group_id ?? currentVodData?.group?.common?.id,
      payway_token: subscriptionsInfo?.playButton?.payway_token,
      HKS: vodDetails?.confirmscreen
        ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
        : userDetails?.session_stringvalue ??
          startheaderinfo?.session_stringvalue,
      userId: vodDetails?.confirmscreen
        ? loginInfo?.user_id ?? registerInfo?.user_id
        : userDetails?.user_id,
      user_token: loginInfo?.user_token ?? registerInfo?.user_token,
      streamType,
      ...(contentIdCheck && { content_id: audioContId })
    }
    dispatch(getMediaAPI(payload))
  }

  const callGetWatchList = data => {
    const payload = {
      id: data?.lasttouch?.favorited ?? lastTouch,
      userId,
      HKS: userDetails?.session_stringvalue,
      user_hash: userDetails?.session_userhash,
      filterlist:
        filteredRegionLists?.[`${region}`]?.filterlist ??
        filteredRegionLists?.default?.filterlist
    }
    dispatch(getWatchList(payload))
  }

  const playNextContent = async item => {
    setClickedNxtEp(true)

    const episodeChange = currentVodData?.tracking?.urls?.episodechange
    if (episodeChange) {
      reqOptionsForGet(`${episodeChange}&timecode=${Math.round(runningTime)}`)
    }
    clearVodData()
    setplaybackFinished(false)
    await player?.destroy().then(() => {
      playerDetroyComplete.current = true
      setFinPlayerEnable(false)
    })
    setCurrentVodData(item)
  }

  const clearVodData = (fromGoToMoviewSeries) => {
    dispatch(clrSubscriptionInfo())
    // const stop = currentVodData?.tracking?.urls?.stop
    fromGoToMoviewSeries != true &&
      playerStatus != '' &&
      runningTime &&
      dispatch(
        getPlayerTrackAPI({
          url: getMediaRedux?.tracking?.urls?.stop
            ?.replace('(', '')
            ?.replace(')', ''), // this replace methods are used currently as sometimes from BE we see unwanted brackets ()
          trackType: 'stop',
          timecode: Math.round(runningTime)
        })
      )
    // !state?.trailerGetMedia &&
    // stop &&
    // runningTime &&
    // reqOptionsForGet(`${stop}&timecode=${Math.round(runningTime)}`)

    localStorage.setItem('introFinished', '')
    setIntroFinished(false)
    setTotalDuration(0)
    setTotalTime(0)
    dispatch(clearVodSeriesMlt())
    setPlayerStatus('')
    setFinPlayerClose(true)
    setFinPlayerEnable(false)
    setAudioContId('')
    setEnableSubSwitch(false)
    setEnableAudSwitch(false)
    setLanguageEnable(false)
    setPlayer(null)
    setClickedNxtEp(false)
    setNxtGrpId('')
    setSubsBtnInfo({})
    setNxtEpData({})
    playerRef.current = {}
    if (player !== null && playerUi) {
      if (playerDetroyComplete.current == false) {
        player?.mute()
        player?.off(PlayerEvent.Ready)
        player?.off(PlayerEvent.Seeked)
      }
      playerUi?.release()
    }
  }

  const callRecomm = () => {
    const vodData =
      currentVodData?.group?.common ?? getMediaRedux?.group?.common
    dispatch(
      vodSeriesMLT({
        id: getMediaRedux?.group?.common?.id,
        userId,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        is_kids: vodDetails?.confirmscreen
          ? loginInfo?.is_kids ?? registerInfo?.is_kids
          : userDetails?.is_kids,
        display_only: vodData?.extendedcommon?.media?.episode?.number
          ? 'series'
          : 'movies',
        user_token: loginInfo?.user_token ?? registerInfo?.user_token,
        filterlist:
          filteredRegionLists?.[`${region}`]?.filterlist ??
          filteredRegionLists?.default?.filterlist
      })
    )
  }

  const enableSubtitles = (
    playerInstance,
    value,
    index,
    convertedSelAud = ''
  ) => {
    const videoElement = document.getElementById('bitmovinplayer-video-player')
    const selectedId = value?.option_id?.split('-').pop().toLowerCase()
    const subtitleList = playerInstance?.subtitles?.list()
    const audioList = playerInstance?.getAvailableAudio()
    setSelSubs(index)
    let audio = []
    let language = []
    const dubSubChange = currentVodData?.tracking?.urls?.dubsubchange
    const checkSelValue = value?.option_id?.toLowerCase()?.startsWith('s')
    if (checkSelValue) {
      const idx =
        subtitleList?.length > 0 &&
        subtitleList.findIndex(item => item.lang === selectedId)
      subtitleList?.[idx] && language.push(subtitleList?.[idx])
      const languageData = currentVodData?.language?.options
      const LangDataIdx =
        languageData?.length > 0 &&
        languageData.findIndex(item => item?.option_id === 'O-EN')

      if (language?.length === 0 && enableSubSwitch) {
        dispatch(clrSubscriptionInfo())
        setAudioContId('or' || languageData?.[LangDataIdx]?.content_id)
        setNxtGrpId(currentVodData?.group?.common?.id)
      }
      playerInstance?.subtitles.enable(language?.[0]?.id)
      localStorage.setItem('vodContentLanguage', value?.option_id)
      setSelSubId(language?.[0]?.id)
      if (dubSubChange && language?.length > 0) {
        const url = new URL(dubSubChange)
        url.searchParams.set('content_id', value?.content_id)
        url.searchParams.set(
          'preferred_audio',
          currentVodData?.media?.audio?.selected
        )
        url.searchParams.set('preferred_subtitle', selectedId.toUpperCase())
        reqOptionsForGet(
          `${url}&timecode=${Math.round(videoElement?.currentTime)}`
        )
      }
    } else {
      audioList?.length > 0 &&
        audioList?.map(item => {
          item?.lang.toLowerCase() === convertedSelAud.toLowerCase() &&
            audio.push(item)
        })
      if (audio?.length === 0 && enableAudSwitch) {
        dispatch(clrSubscriptionInfo())
        setAudioContId(value?.content_id)
        setNxtGrpId(currentVodData?.group?.common?.id)
      }
      playerInstance?.setAudio(audio?.[0]?.id)
      playerInstance?.subtitles?.disable(selSubId ?? '')
      localStorage.setItem(
        'vodContentLanguage',
        value?.option_id == 'O-EN' ? 'O-OR' : value?.option_id
      )
      // player?.subtitles?.disable(selSubId) commented for the pending ticket MAUP-8773
      if (dubSubChange && audio.length > 0) {
        const url = new URL(dubSubChange)
        url.searchParams.set('content_id', value?.content_id)
        url.searchParams.set(
          'preferred_subtitle',
          currentVodData?.media?.subtitles?.selected
        )
        url.searchParams.set(
          'preferred_audio',
          convertedSelAud == 'or' ? 'ORIGINAL' : convertedSelAud.toUpperCase()
        )
        reqOptionsForGet(
          `${url}&timecode=${Math.round(videoElement?.currentTime)}`
        )
      }
    }
  }

  const handleFavAdd = async () => {
    setFavClicked(true)
    dispatch(
      addWatchList({
        id: currentVodData?.group?.common?.id,
        userId,
        HKS: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
  }

  const handleFavDel = async () => {
    setFavClicked(true)
    dispatch(
      delWatchList({
        id: currentVodData?.group?.common?.id,
        userId,
        HKS: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
  }

  const handleLanguage = () => {
    setLanguageEnable(true)
    // GA: Player Interaction Event
    pushPlayerInteractionEvent(
      contentDataRef.current,
      interactionType.LANGUAGE_INTERACTION_TYPE
    )
  }

  const handleTemperodo = e => {
    pushNewInteractionContentEvent(
      {
        action_type: 'click imagen',
        content_list_id: state?.content_list_id,
        ...contentDataRef.current
      },
      SELECT_CONTENT,
      SERIES,
      interactionType.CLICK_IMAGEN
    )
    setEpisodeEnable(true)
  }

  const handleTotDuration = duration => {
    if ((!totalDuration || !totalTime) && duration) {
      setTotalDuration(moment.utc(duration * 1000).format('HH:mm:ss'))
      setTotalTime(duration)
    }
  }

  const getRange = ev => {
    player?.seek(ev.target.value)
  }

  const handleStartOver = () => {
    // let startOverCounter = 0
    // const videoElement = document.getElementById('bitmovinplayer-video-player')
    // startedOverRef.current = true
    // startedOverTimeRef.current =
    //   moment().unix() - currentEventRef.current?.unix_begin
    // videoElement.currentTime =
    //   parseInt(videoElement.currentTime) -
    //   (parseInt(videoElement.currentTime) -
    //     parseInt(currentEventRef.current?.unix_begin))
    playerRef.current?.seek(0)
  }

  const skipIntroShowHide = currTime => {
    const currentVod =
      !state?.trailerGetMedia &&
      localStorage.getItem('currentVod') &&
      JSON.parse(localStorage.getItem('currentVod'))

    const skipOptions =
      currentVod?.group?.common?.extendedcommon?.media?.language?.options
        ?.option || []
    const introCheck = localStorage.getItem('introFinished')

    // Find the first valid skip option (if any)
    const skipOption = skipOptions.find(item => item?.intro_finish_time)

    if (skipOption) {
      const intro_finish =
        parseInt(skipOption.intro_finish_time) <= Math.round(currTime)
      setIntroStartTime(
        moment.utc(skipOption.intro_start_time * 1000).format('HH:mm:ss')
      )
      setIntroFinishTime(
        moment.utc(skipOption.intro_finish_time * 1000).format('HH:mm:ss')
      )

      if (intro_finish && !introCheck) {
        setIntroFinished(true)
        localStorage.setItem('introFinished', true)
      }

      return intro_finish
    }

    return false
  }

  const skipIntro = () => {
    const skip =
      currentVodData?.group?.common?.extendedcommon?.media?.language?.options
        ?.option ?? []
    const skipVisible = skip?.classList?.contains('skip-intro-alone')
    skip?.length > 0 &&
      skip.map(item => {
        player?.seek(item?.intro_finish_time)
        //GA: Content Forward Event
        pushNewInteractionContentEvent(
          contentDataRef.current,
          INTERACTION_PLAYER,
          currentVodData?.group?.common?.extendedcommon?.media?.episode?.number
            ? SERIES
            : MOVIE,
          interactionType.OMITIR_INTRO,
        )
      })
    skipVisible && setShowVodControls('hide')
  }

  const handlePlayPause = () => {
    if (playerStatus === 'play') {
      player?.pause()
      setPlayerStatus('pause')
      dispatch(
        getPlayerTrackAPI({
          url: getMediaRedux?.tracking?.urls?.pause
            ?.replace('(', '')
            ?.replace(')', ''),
          trackType: 'pause',
          timecode: Math.round(runningTime)
        })
      )
      // GA: Content Pause Event
      pushContentPauseEvent(contentDataRef.current)
    } else {
      player?.play()
      setPlayerStatus('play')
       dispatch(
        getPlayerTrackAPI({
          url: getMediaRedux?.tracking?.urls?.resume
            ?.replace('(', '')
            ?.replace(')', ''),
          trackType: 'resume',
          timecode: Math.round(runningTime)
        })
      )
      // GA: Content Play Event
      pushContentPlayEvent(contentDataRef.current)
    }
  }

  const handleSeek = e => {
    e?.key === 'ArrowLeft' && handleRewind()
    e?.key === 'ArrowRight' && handleForward()
  }

  const seekbarLongPress = e => {
    shouldWaitRef.current = true
    seekLongPressRef.current = true
    setShowSpritesImg(true)
    let value
    if (e == 'right') {
      const durationDiff = runningTime >= totalTime ? 0 : 10
      const seekDuration = runningTime + durationDiff
      const setRunTime = seekDuration > totalTime ? totalTime : seekDuration
      lastSeekedTimeRef.current = setRunTime
      value = (setRunTime / totalTime) * 100
      setRunningTime(setRunTime)
      setCurrentTime(moment.utc(setRunTime * 1000).format('HH:mm:ss'))
      player?.seek(setRunTime)
      lastSeekType.current = e
    } else if (e == 'left') {
      const durationDiff =
        runningTime < 10 && runningTime >= 0 ? runningTime : 10
      const setRunTime = runningTime - durationDiff
      lastSeekedTimeRef.current = setRunTime
      value = (setRunTime / totalTime) * 100
      setRunningTime(setRunTime)
      setCurrentTime(moment.utc(setRunTime * 1000).format('HH:mm:ss'))
      player?.seek(setRunTime)
      lastSeekType.current = e
    }
    //Below logic is different from above one, as during long press seek above logic is not firing.
    switch (true) {
      case value >= 25 && value <= 25.15:
        pushContentProgressEvent(contentDataRef.current, 25)
        break
      case value >= 50 && value <= 50.15:
        pushContentProgressEvent(contentDataRef.current, 50)
        break
      case value >= 75 && value <= 75.15:
        pushContentProgressEvent(contentDataRef.current, 75)
        break
      case value >= 100 && value <= 100.15:
        pushContentProgressEvent(contentDataRef.current, 100)
        break
    }
  }

  const handleSeekLongPrs = e => {
    e.persist()
    if (e?.key === 'ArrowLeft') {
      throttle(seekbarLongPress, 100, 'left')
    } else if (e?.key === 'ArrowRight') {
      throttle(seekbarLongPress, 100, 'right')
    } else {
      handleSeek(e)
    }
  }

  const throttle = useCallback((callback, delay, ...args) => {
    if (shouldWaitRef.current) return

    callback(...args)
    shouldWaitRef.current = true

    setTimeout(() => {
      shouldWaitRef.current = false
    }, delay)
  }, [])

  const hideImageSpriteAfterDelay = (event) => {
    if((event?.key == 'ArrowLeft' || event?.keyCode == 37) || (event?.key == 'ArrowRight' || event?.keyCode == 39)){
    dispatch(
        getPlayerTrackAPI({
          url: getMediaRedux?.tracking?.urls?.seek
            ?.replace('(', '')
            ?.replace(')', ''),
          trackType: 'seek',
          timecode: Math.round(lastSeekedTimeRef.current)
        })
      )
    }
    if (spriteHideTimeoutRef.current) {
      clearTimeout(spriteHideTimeoutRef.current)
    }
    spriteHideTimeoutRef.current = setTimeout(() => {
      setShowSpritesImg(false)
      spriteHideTimeoutRef.current = null
    }, 4000)
  }

  const handleForwardSeeking = e => {
    shouldWaitRef.current = true
    seekLongPressRef.current = true
    setShowSpritesImg(true)
    const durationDiff = runningTime >= totalTime ? 0 : 10
    const seekDuration = runningTime + durationDiff
    const setRunTime = seekDuration > totalTime ? totalTime : seekDuration
    lastSeekedTimeRef.current = setRunTime
    setRunningTime(setRunTime)
    setCurrentTime(moment.utc(setRunTime * 1000).format('HH:mm:ss'))
    player?.seek(setRunTime)
    lastSeekType.current = e
    hideImageSpriteAfterDelay()
  }

  const handleRewindSeeking = e => {
    shouldWaitRef.current = true
    seekLongPressRef.current = true
    setShowSpritesImg(true)
    const durationDiff = runningTime >= totalTime ? 0 : 10
    const seekDuration = runningTime - durationDiff
    const setRunTime = seekDuration > totalTime ? totalTime : seekDuration
    lastSeekedTimeRef.current = setRunTime
    setRunningTime(setRunTime)
    setCurrentTime(moment.utc(setRunTime * 1000).format('HH:mm:ss'))
    player?.seek(setRunTime)
    lastSeekType.current = e
    hideImageSpriteAfterDelay()
  }

  const handleSeekTrackApi = (event) => {
    if( event?.key == 'Enter' || event?.keyCode == 13 ){
    dispatch(
        getPlayerTrackAPI({
          url: getMediaRedux?.tracking?.urls?.seek
            ?.replace('(', '')
            ?.replace(')', ''),
          trackType: 'seek',
          timecode: Math.round(lastSeekedTimeRef.current)
        })
      )
    }
  }

  const handleForward = e => {
    e.persist()
    pushContentForwardEvent(contentDataRef.current)
    throttle(handleForwardSeeking, 100, 'right')
  }

  const handleRewind = e => {
    e.persist()
    pushContentRewindEvent(contentDataRef.current)
    throttle(handleRewindSeeking, 100, 'left')
  }

  const hidePlayerControls = () => {
    setFinPlayerEnable(false)
    setFinPlayerClose(true)
    setSubscriptionEnable(true)
    setShowVodControls('hide')
  }

  const getProgressBarStyle = useCallback(() => {
    let value = (runningTime / totalTime) * 100
    return `linear-gradient(to right, #8a0000 0%, #8a0000 ${
      !value ? 0.03110083 : value
    }%, ${rwFwRef.current ? 'rgb(138, 0, 0)' : 'rgba(0, 0, 0, 0.5)'} ${
      !value ? 0.03110083 : value
    }%, ${rwFwRef.current ? 'rgb(138, 0, 0)' : 'rgba(0, 0, 0, 0.5)'} 100%)`
  }, [runningTime])

  const handleSpritesImage = () => {
    const spritesConfig = spritesConfiguration?.default?.default
    var imagePieces = []
    const numColsToCut =
      parseInt(image?.naturalHeight) /
      parseInt(spritesConfig?.sprite_height_frame)
    const numRowsToCut = spritesConfig?.sprite_frames_by_row
    const widthOfOnePiece = spritesConfig?.sprite_width_frame
    const heightOfOnePiece = spritesConfig?.sprite_height_frame
    for (var x = 0; x < numColsToCut; ++x) {
      for (var y = 0; y < numRowsToCut; ++y) {
        var canvas = document.createElement('canvas')
        canvas.width = widthOfOnePiece
        canvas.height = heightOfOnePiece
        var context = canvas.getContext('2d')
        context.drawImage(
          image,
          y * widthOfOnePiece,
          x * heightOfOnePiece,
          widthOfOnePiece,
          heightOfOnePiece,
          0,
          0,
          canvas.width,
          canvas.height
        )
        const contentType = 'image/png'
        const blob = b64toBlob(canvas?.toDataURL(), contentType)
        const blobUrl = URL.createObjectURL(blob)

        imagePieces.push(blobUrl)
      }
    }
    setSpritesImages(imagePieces)
  }

  const b64toBlob = (b64Data, contentType = '', sliceSize = 512) => {
    const replaceB64 = b64Data.replace('data:image/png;base64,', '')
    const byteCharacters = atob(replaceB64)
    const byteArrays = []

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize)

      const byteNumbers = new Array(slice.length)
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i)
      }

      const byteArray = new Uint8Array(byteNumbers)
      byteArrays.push(byteArray)
    }

    const blob = new Blob(byteArrays, { type: contentType })
    return blob
  }

  const handleSpritesIdx = () => {
    const value = Math.round(runningTime) + 10
    const res = value.toString().length == 1 ? `0${value}` : value
    return res.toString().slice(0, -1)
  }

  return (
    <div className="bitmovin">
      <div id={'player'} ref={playerDiv} />
      {((playerStatus !== 'play' && playerStatus !== 'pause') ||
        playerStatus === 'stop') && (
        <Lottie
          options={{
            rendererSettings: {
              preserveAspectRatio: 'xMidYMid slice'
            }
          }}
          loop
          animationData={animationData}
          play
          className="player-loader"
        />
      )}
      {showVodControls === 'vod' && episodeEnable && (
        <PlayerEpisode
          ref={playerEpRef}
          id={'playerEpisode'}
          playNext={playNextContent}
          playFocusImg={playerImg?.play_focus}
          closePlayerEp={setEpisodeEnable}
          content={vodSeriesDataRedux}
          currentEp={
            currentVodData?.group?.common?.extendedcommon?.media?.episode
              ?.number
          }
          currentVodData={currentVodData}
          contentDataplayer={state?.contentDataplayer}
          episodeData={state?.episodeData}
          inputValue={state?.inputValue}
          percent={((runningTime / totalTime) * 100).toFixed(2)}
          pauseplayer={player}
          currentButtonFocus={currentButtonFocus}
          gaContentData={{
            action_type: 'click imagen',
            content_list_id: state?.content_list_id,
            ...contentDataRef.current
          }}
        />
      )}
      {playerStatus !== 'stop' &&
        (playerStatus === 'play' || playerStatus === 'pause') &&
        finPlayerEnable &&
        !state?.trailerGetMedia && (
          <FinPlayerEpisodeBanner
            id={'finPlayer'}
            player={player}
            playerStatus={setPlayerStatus}
            nxtGrpId={nxtGrpId}
            setClickedNxtEp={setClickedNxtEp}
            nxtEpDuration={nxtEpDuration}
            totalDuration={totalTime}
            nxtEventData={nxtEpData}
            backToVcard={setPlayerClose}
            hidePlayerCont={hidePlayerControls}
            data={state?.data}
            preLoadSubsInfo={subsBtnInfo}
            autoPlay={fltrRecomm?.length === 0}
            disabled={languageEnable}
            clearVodData={clearVodData}
            vCardBackFocus={state?.vCardBackFocus}
            playbackFinish={setplaybackFinished}
            enablePinPage={setPinpageEnable}
            gaContentData={{modulo_name:'banner fin player', ...contentDataRef.current}}
          />
        )}

      {showVodControls === 'vod' && languageEnable && (
        <Language
          subtitles={enableSubtitles}
          id={'language'}
          data={currentVodData}
          hideLanguage={setLanguageEnable}
          player={player}
          selSubtitle={selSubs}
          enableAudSwitch={enableAudSwitch}
        />
      )}
      {subscriptionEnable && !finPlayerEnable && (
        <BuySubscriptionNew
          pageName={'player'}
          dataNxt={nxtEpData}
          showBuySubs={setSubscriptionEnable}
          finClose={setFinPlayerClose}
          showFin={setFinPlayerEnable}
        />
      )}
      {runningTime >= 0 && currentTime && (
        <div className="bottom-controls">
          {showVodControls === 'vod' &&
            !episodeEnable &&
            !finPlayerEnable &&
            !subscriptionEnable &&
            (playerStatus == 'play' || playerStatus == 'pause') && (
              <>
                {!showSpritesImg && (
                  <div className="content-details">
                    <span className="content-text-wrapper">
                      {currentVodData?.group?.common?.extendedcommon?.media
                        ?.episode?.number ? (
                        <span className="content-title">
                          {currentVodData?.group?.common?.extendedcommon?.media
                            ?.serie?.title &&
                          currentVodData?.group?.common?.extendedcommon?.media
                            ?.serie?.title?.length > 30
                            ? `${currentVodData?.group?.common?.extendedcommon?.media?.serie?.title.slice(
                                0,
                                37
                              )}...`
                            : currentVodData?.group?.common?.extendedcommon
                                ?.media?.serie?.title}
                        </span>
                      ) : (
                        <span className="content-title">
                          {currentVodData?.group?.common?.title &&
                          currentVodData?.group?.common?.title?.length > 67
                            ? `${currentVodData?.group?.common?.title.slice(
                                0,
                                67
                              )}...`
                            : currentVodData?.group?.common?.title}
                        </span>
                      )}
                    </span>
                    <span
                      style={{
                        display:
                          currentVodData?.group?.common?.extendedcommon?.media
                            ?.episode?.number && !state?.trailerGetMedia
                            ? ''
                            : 'none',
                        alignSelf: 'center'
                      }}
                      className="season-details-wrapper"
                    >
                      <span className="season-ep-details">
                        {`${
                          apilanguage?.playing_playerControls_abbreviationSeason_label ??
                          'playing_playerControls_abbreviationSeason_label'.slice(
                            0,
                            5
                          )
                        } ${
                          currentVodData?.group?.common?.extendedcommon?.media
                            ?.episode?.season
                        } | ${
                          apilanguage?.playing_playerControls_abbreviationEpisode_label
                        } ${
                          currentVodData?.group?.common?.extendedcommon?.media
                            ?.episode?.number
                        }:`}{' '}
                      </span>
                      <span className="ep-title">
                        {`${
                          currentVodData?.group?.common?.title &&
                          currentVodData?.group?.common?.title?.length > 30
                            ? `${currentVodData?.group?.common?.title.slice(
                                0,
                                30
                              )}...`
                            : currentVodData?.group?.common?.title
                        }`}
                      </span>
                    </span>
                  </div>
                )}
                <div className="left-timer">
                  <p className="timer-font">
                    {currentTime == 0 || totalDuration == 0 ? '' : currentTime}
                  </p>
                </div>
                <div className="vod-custom-slider-container">
                  <input
                    className={'vod-custom-slider focusable'}
                    onFocus={() => setCurrentButtonFocus('seekbar')}
                    onBlur={() => setCurrentButtonFocus('')}
                    type="range"
                    data-sn-down={skipFocus ? '#skipIntro' : '#rewind'}
                    id="progressRange"
                    min={0}
                    max={totalTime}
                    value={runningTime}
                    step={1}
                    draggable
                    disabled={languageEnable}
                    style={{
                      background: getProgressBarStyle()
                    }}
                    onChange={getRange}
                    onKeyUp={hideImageSpriteAfterDelay}
                    onKeyDown={handleSeekLongPrs}
                  />

                  <img
                    className="sprites-image-pos"
                    style={{ display: showSpritesImg ? '' : 'none' }}
                    width={484}
                    height={272}
                    src={spritesImages[handleSpritesIdx()]}
                  />
                </div>

                <div className="right-timer">
                  <p className="timer-font">
                    {currentTime == 0 || totalDuration == 0
                      ? ''
                      : totalDuration}
                  </p>
                </div>
              </>
            )}
          <div className="player-controls-wrapper">
            <div
              className="player-controls-cont"
              style={{
                width:
                  currentTime >= introStartTime &&
                  currentTime < introFinishTime &&
                  (playerStatus == 'play' || playerStatus == 'pause')
                    ? 1600
                    : state?.page == 'record'
                    ? 500
                    : state?.trailerGetMedia
                    ? 700
                    : 1200
              }}
            >
              <>
                {skipIntroShow &&
                  !episodeEnable &&
                  !finPlayerEnable &&
                  !subscriptionEnable && (
                    <button
                      autoFocus={skipFocus}
                      id="skipIntro"
                      ref={skipIntroRef}
                      style={{
                        display:
                          currentTime >= introStartTime &&
                          currentTime < introFinishTime &&
                          (playerStatus == 'play' || playerStatus == 'pause')
                            ? ''
                            : 'none'
                      }}
                      className={`skip-intro ${
                        showVodControls === 'hide' && 'skip-intro-alone'
                      } ${
                        showVodControls === 'hide' &&
                        introFinished &&
                        'hide-skip-intro-alone'
                      } focusable`}
                      onFocus={() => setCurrentButtonFocus('skipIntro')}
                      onBlur={() => setCurrentButtonFocus('')}
                      onClick={skipIntro}
                      disabled={languageEnable}
                      data-sn-up={'#progressRange'}
                      data-sn-down={'#back'}
                    >
                      <p className="skip-intro-text">
                        {apilanguage?.Boton_TextoTítulo ??
                          'Boton_TextoTítulo'.slice(0, 19)}
                      </p>
                    </button>
                  )}
                {showVodControls === 'vod' &&
                  !episodeEnable &&
                  !finPlayerEnable &&
                  !subscriptionEnable &&
                  (playerStatus == 'play' || playerStatus == 'pause') && (
                    <>
                      <button
                        id="rewind"
                        className={
                          state?.episodeData
                            ? 'bottom-btn focusable'
                            : 'bottom-btn-2 focusable'
                        }
                        onFocus={() => setCurrentButtonFocus('rewind')}
                        onBlur={() => setCurrentButtonFocus('')}
                        onClick={handleRewind}
                        data-sn-down={'#back'}
                        data-sn-up={'#progressRange'}
                        disabled={languageEnable}
                        onKeyUp={handleSeekTrackApi}
                      >
                        <img
                          className="play-pause-btn"
                          src={
                            currentButtonFocus === 'rewind'
                              ? playerImg?.rewind_focus
                              : playerImg?.rewind_unfocus
                          }
                        />
                        <span
                          className={
                            currentButtonFocus !== 'rewind'
                              ? 'btn-text-focus-out'
                              : 'btn-text-focus-in'
                          }
                        >
                          {apilanguage?.Player_Boton_TextoAccionAtrasar ??
                            'Player_Boton_TextoAccionAtrasar'.slice(0, 19)}
                        </span>
                      </button>
                      <button
                        id="play"
                        autoFocus={!skipFocus && !seekLongPressRef.current}
                        className={
                          state?.episodeData
                            ? 'bottom-btn focusable'
                            : 'bottom-btn-2 focusable'
                        }
                        onFocus={() => setCurrentButtonFocus('play')}
                        onBlur={() => setCurrentButtonFocus('')}
                        onClick={handlePlayPause}
                        data-sn-down={'#back'}
                        data-sn-up={'#progressRange'}
                        disabled={languageEnable}
                      >
                        <img
                          id="playPause"
                          className="play-pause-btn"
                          src={
                            playerStatus === 'play'
                              ? currentButtonFocus === 'play'
                                ? playerImg?.pause_focus
                                : playerImg?.pause_unfocus
                              : currentButtonFocus === 'play'
                              ? playerImg?.play_focus
                              : playerImg?.play_unfocus
                          }
                        />
                        <span
                          className={
                            currentButtonFocus !== 'play'
                              ? 'btn-text-focus-out'
                              : 'btn-text-focus-in'
                          }
                        >
                          {playerStatus == 'play'
                            ? apilanguage?.Player_Boton_TextoAccionPausar ??
                              'Player_Boton_TextoAccionPausar'.slice(0, 19)
                            : apilanguage?.playing_playerControls_toolbar_button_play ??
                              'playing_playerControls_toolbar_button_play'.slice(
                                0,
                                19
                              )}
                        </span>
                      </button>
                      <button
                        className={
                          state?.episodeData
                            ? 'bottom-btn focusable'
                            : 'bottom-btn-2 focusable'
                        }
                        onFocus={() => setCurrentButtonFocus('forward')}
                        onBlur={() => setCurrentButtonFocus('')}
                        onClick={handleForward}
                        data-sn-down={'#back'}
                        data-sn-up={'#progressRange'}
                        disabled={languageEnable}
                        onKeyUp={handleSeekTrackApi}
                      >
                        <img
                          className="play-pause-btn"
                          src={
                            currentButtonFocus === 'forward'
                              ? playerImg?.forward_focus
                              : playerImg?.forward_unfocus
                          }
                        />
                        <span
                          className={
                            currentButtonFocus !== 'forward'
                              ? 'btn-text-focus-out'
                              : 'btn-text-focus-in'
                          }
                        >
                          {apilanguage?.Player_Boton_TextoAccionAdelantar ??
                            'Player_Boton_TextoAccionAdelantar'.slice(0, 19)}
                        </span>
                      </button>
                      {state?.page != 'record' && (
                        <>
                          <button
                            className={
                              state?.episodeData
                                ? 'bottom-btn focusable'
                                : 'bottom-btn-2 focusable'
                            }
                            ref={languageRef}
                            onFocus={() => setCurrentButtonFocus('language')}
                            onBlur={() => setCurrentButtonFocus('')}
                            onClick={handleLanguage}
                            data-sn-down={'#back'}
                            data-sn-up={'#progressRange'}
                            disabled={languageEnable}
                          >
                            <img
                              className="play-pause-btn"
                              src={
                                currentButtonFocus === 'language'
                                  ? playerImg?.language_focus
                                  : playerImg?.language_unfocus
                              }
                            />
                            <span
                              className={
                                currentButtonFocus !== 'language'
                                  ? 'btn-text-focus-out'
                                  : 'btn-text-focus-in'
                              }
                            >
                              {apilanguage?.Player_Boton_TextoAccionIdioma ??
                                'Player_Boton_TextoAccionIdioma'.slice(0, 19)}
                            </span>
                          </button>
                          {currentVodData?.group?.common?.extendedcommon?.media
                            ?.episode?.number &&
                            !state?.trailerGetMedia && (
                              <button
                                id="tempo"
                                className={
                                  state?.episodeData
                                    ? 'bottom-btn focusable'
                                    : 'bottom-btn-2 focusable'
                                }
                                ref={episodeRef}
                                onFocus={() => setCurrentButtonFocus('tempo')}
                                onBlur={() => setCurrentButtonFocus('')}
                                onClick={e => handleTemperodo(e)}
                                data-sn-down={'#back'}
                                disabled={languageEnable}
                              >
                                <img
                                  className="play-pause-btn"
                                  src={
                                    currentButtonFocus === 'tempo'
                                      ? playerImg?.tempo_focus
                                      : playerImg?.tempo_unfocus
                                  }
                                />
                                <span
                                  className={
                                    currentButtonFocus !== 'tempo'
                                      ? 'btn-text-focus-out'
                                      : 'btn-text-focus-in'
                                  }
                                >
                                  {apilanguage?.Player_Boton_TextoAccionTemporadas ??
                                    'Player_Boton_TextoAccionTemporadas'.slice(
                                      0,
                                      19
                                    )}
                                </span>
                              </button>
                            )}
                          {favorites && userId && !state?.trailerGetMedia ? (
                            <button
                              className={
                                state?.episodeData
                                  ? 'bottom-btn focusable'
                                  : 'bottom-btn-2 focusable'
                              }
                              ref={rmFavRef}
                              onFocus={() => setCurrentButtonFocus('removeFav')}
                              onBlur={() => setCurrentButtonFocus('')}
                              onClick={handleFavDel}
                              data-sn-down={'#back'}
                              data-sn-up={'#progressRange'}
                              disabled={languageEnable}
                            >
                              <img
                                className="play-pause-btn"
                                src={
                                  currentButtonFocus === 'removeFav'
                                    ? playerImg?.remove_fav_focus
                                    : playerImg?.remove_fav_unfocus
                                }
                              />
                              <span
                                className={
                                  currentButtonFocus !== 'removeFav'
                                    ? 'btn-text-focus-out'
                                    : 'btn-text-focus-in'
                                }
                              >
                                {apilanguage?.playing_playerControls_toolbar_button_deleteList ??
                                  'playing_playerControls_toolbar_button_deleteList'.slice(
                                    0,
                                    19
                                  )}
                              </span>
                            </button>
                          ) : (
                            userId &&
                            !state?.trailerGetMedia && (
                              <button
                                className={
                                  state?.episodeData
                                    ? 'bottom-btn focusable'
                                    : 'bottom-btn-2 focusable'
                                }
                                ref={addFavRef}
                                onFocus={() => setCurrentButtonFocus('addFav')}
                                onBlur={() => setCurrentButtonFocus('')}
                                onClick={handleFavAdd}
                                data-sn-down={'#back'}
                                data-sn-up={'#progressRange'}
                                disabled={languageEnable}
                              >
                                <img
                                  className="play-pause-btn"
                                  src={
                                    currentButtonFocus === 'addFav'
                                      ? playerImg?.add_fav_focus
                                      : playerImg?.add_fav_unfocus
                                  }
                                />
                                <span
                                  className={
                                    currentButtonFocus !== 'addFav'
                                      ? 'btn-text-focus-out'
                                      : 'btn-text-focus-in'
                                  }
                                >
                                  {apilanguage?.playing_playerControls_toolbar_button_addList ??
                                    'playing_playerControls_toolbar_button_addList'.slice(
                                      0,
                                      19
                                    )}
                                </span>
                              </button>
                            )
                          )}
                        </>
                      )}
                    </>
                  )}
              </>
            </div>
          </div>
          {(showVodControls === 'vod' || finPlayerEnable) &&
            !subscriptionEnable &&
            (playerStatus == 'play' || playerStatus == 'pause') && (
              <div className="bottom-bar-container">
                <button
                  id="back"
                  data-sn-up={
                    episodeEnable
                      ? '#episodeBtn'
                      : finPlayerEnable
                      ? document.getElementById('episodeTimelineContainer')
                        ? `#${
                            document.getElementById('episodeTimelineContainer')
                              ?.lastChild?.children?.[0]?.id
                          }`
                        : document.getElementById('finWatchBtn')
                        ? '#finWatchBtn'
                        : document.getElementById('episodeTimelineContainer') &&
                          `#${
                            document.getElementById('episodeTimelineContainer')
                              ?.lastChild?.children?.[0]?.id
                          }`
                      : '#play'
                  }
                  className="go-out-button focusable"
                  onClick={goBack}
                  style={{
                    width:
                      episodeEnable ||
                      finPlayerEnable ||
                      document.getElementById('back') === document.activeElement
                        ? 203
                        : 173
                  }}
                  onKeyDown={e =>
                    episodeEnable &&
                    e.key == 'ArrowUp' &&
                    playerEpRef.current?.handleSeasonKey(e)
                  }
                  onFocus={() => setCurrentButtonFocus('back')}
                  onBlur={() => setCurrentButtonFocus('')}
                  disabled={languageEnable}
                >
                  <img
                    className="yellowdot"
                    width={20}
                    height={20}
                    src={
                      finPlayerEnable
                        ? 'images/Home_icons/bluedot.png'
                        : 'images/Home_icons/yellowdot.png'
                    }
                  />

                  <span className="go-out-btn-txt">
                    {' '}
                    {!episodeEnable && !finPlayerEnable
                      ? (
                          apilanguage?.playing_playerControls_option_button_exit ??
                          'playing_playerControls_option_button_exit'
                        ).slice(0, 7)
                      : (
                          apilanguage?.BotonShortcut_TextoTitulo ??
                          'BotonShortcut_TextoTitulo'
                        ).slice(0, 7)}
                  </span>
                </button>
                {state?.page == 'record' && (
                  <button
                    id="back"
                    data-sn-up={
                      episodeEnable
                        ? '#episodeBtn'
                        : finPlayerEnable
                        ? document.getElementById('finWatchBtn')
                          ? '#finWatchBtn'
                          : '#seasonBtn-0'
                        : '#play'
                    }
                    className="go-out-button focusable"
                    onClick={handleStartOver}
                  >
                    <img
                      className="yellowdot"
                      width={20}
                      height={20}
                      src={'images/Home_icons/red.png'}
                    />
                    <span className="go-out-btn-txt">
                      {' '}
                      {apilanguage?.playingLive_playerControls_option_button_playFromTheStart ??
                        'apilanguage?.playingLive_playerControls_option_button_playFromTheStart'}
                    </span>
                  </button>
                )}
              </div>
            )}
        </div>
      )}
    </div>
  )
}

export default VodPlayer
