import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { BackButtonComponent } from '../../../components/CommonComponent'
import './AddTelephoneViaWeb.scss'

const AddTelephoneViaWeb = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const ClaroImg = 'images/claroLogo.png'
  const region = localStorage.getItem('region')
  const apilanguage = translations?.language?.[region]
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)

  const handleGoPreviousPage = e => {
    navigate('/my-settings/my-Accounts/manage-payments/payments-method', {
      state: { pageName: 'subscription' }
    })
  }
  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  useEffect(() => {
    document.getElementById('landlineFinishSubscriptionOkBtn').focus()
  }, [])
  return (
    <div className="land-line-add-number-via-web">
      <div className="upper-back-button-area">
        <BackButtonComponent
          uid={'land-line-redirect-web-back'}
          onCustomClick={handleGoPreviousPage}
          text={'REGRESAR'}
        />
      </div>
      <div className="logo-image">
        <img src={'images/logo.png'} className="logo-img" alt="logo" />
      </div>
      <div className="icon-info-button">
        <div className="icon-div">ℹ️</div>
        <div className="landline-text-desc">
          <p>
            Te invitamos a agregar el mdedo de pago Temlmex/Telnor desde un
            navedador web,para que ,puedas realizer tu transddionesy disgurtar
            del conetinao en este dipsitiovo
          </p>
        </div>
        <div className="add-telephone-via-web-buttons-area">
          <button
            id="landlineFinishSubscriptionOkBtn"
            onClick={e => {
              handleGoPreviousPage(e)
            }}
            className="landline-finish-subscription-btn focusable"
          >
            <span className="option-button-close-text">
              {truncateText(
                'CDF_HD_subscription_modal_option_button_close',
                30
              )}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}
export default AddTelephoneViaWeb
