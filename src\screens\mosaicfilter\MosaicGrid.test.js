import React from "react";
import { fireEvent, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import MosaicGrid from "./MosaicGrid";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
const mockerrorresponse = {
    
}
const mocksuccessresponse =   {
  id: "35357",
  image: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  name: "CANAL 3 GT",
  number: "3",
  code: "tv_nacional_not_demo",
  text: "Nacionales",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
}
const mocksuccessresponse6 = [
  {
  id: "35357",
  image: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  name: "CANAL 3 GT",
  number: "3",
  code: "tv_nacional_not_demo",
  text: "Nacionales",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
},
{
  id: "35358",
  image: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL7GT/EXPORTACION_WEB/SS/CANAL7GT_t-290x163.png",
  name: "CANAL 7 GT",
  number: "7",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
},
{
  id: "35617",
  image: "https://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/TN23/EXPORTACION_WEB/SS/TN23_t-290x163.png",
  name: "TN23",
  number: "9",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
},
{
  id: "35359",
image: "https://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/CANAL11GT/EXPORTACION_WEB/SS/CANAL11GT_t-290x163.png",
name: "CANAL 11 GT",
number: "11",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
},
{
  id: "35360",
image: "https://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/CANAL13GT/EXPORTACION_WEB/SS/CANAL13GT_t-290x163.png",
name: "CANAL 13 GT",
number: "13",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
},
{
  id: "35362",
  image: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  name: "CANAL 3 GT",
  number: "3",
  code: "tv_nacional_not_demo",
  id: "19482",
  text: "Nacionales",
  lockchannelnumber:"CANAL 3 GT",
  lockimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  lockname:"CANAL 3 GT",
  favchannelnumber:"CANAL 3 GT",
  favimage: "https://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/CANAL3GT/EXPORTACION_WEB/SS/CANAL3GT_t-290x163.png",
  favname:"CANAL 3 GT"
}

]

describe('Mosaic Grid main page test', () => {
  test('should render api lineup data', () => {
    initialState.mosaic = {
        userInfo: mocksuccessresponse
    }
    renderWithState(<MosaicGrid />)
})
test('to check lockchannel props', () => {
 const props ={
  node:'lockScreen' 
}
  renderWithState(<MosaicGrid {...props}/>)
})
test('to check favchannel props', () => {
  const props ={
   node:'FavoriteScreen' 
 }
   renderWithState(<MosaicGrid {...props}/>)
 })

})