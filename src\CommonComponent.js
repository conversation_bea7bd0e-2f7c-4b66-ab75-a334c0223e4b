import React from 'react'
const BackButtonComponent = props => {
  return (
    <>
      <button
        className="back-button  focusable"
        onMouseOver={e => {e.target.focus()}}
        onClick={e => {props.onClick_()}}
        id={props.id_}
      >
        <span className='btn-internal'>
        <img className="yellow-indicator_" src={'images/yellow_shortcut.png'} />
        <img className="back-image_" src={'images/back_button.png'} />
        <p className="back-text_"> {props.text} </p>
        </span>
        
      </button>
    </>
  )
}

export { BackButtonComponent }
