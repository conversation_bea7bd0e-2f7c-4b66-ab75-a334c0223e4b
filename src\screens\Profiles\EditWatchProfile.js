import React, { useCallback, useEffect, useRef, useState } from 'react'
import './EditWatchProfile.scss'
import { useSelector } from 'react-redux'
import { useNavigate, useLocation } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useDispatch } from 'react-redux'
import {
  getAddInEdit,
  getImageFocusInEdit
} from '../../store/slices/getWatchListSlice'
import { store } from '../../store/sagaStore'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const WatchProfileEdit = () => {
  const { state } = useLocation()
  const navigate = useNavigate()
  const userProfileRef = useRef([])
  const addRef = useRef(null)
  const data = state
  const dispatch = useDispatch()

  const profiledataRedux = useSelector(
    state => state?.profile?.profileData?.response?.data
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const imageFocus = useSelector(state => state?.watchList?.getEditImageFocus)
  const loginapi = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(state => state?.login?.registerSuccess?.response)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')

  const [profiledata, setProfiledata] = useState(profiledataRedux)
  const [showNotification, setShowNotification] = useState(false)
  const [profileUpdateDelete, setProfileUpdateDelete] = useState('')

  useEffect(() => {
    setProfiledata(profiledataRedux)
    const index = profiledataRedux?.members.findIndex(
      i => i.gamification_id === state?.id
    )
    // setTimeout(() => {
    //   userProfileRef?.current[index == -1 ? 0 : index]?.focus()
    // }, 2000)
  }, [profiledataRedux])
  const firstprofile = state?.dafaultfocus
  useEffect(() => {
    if (firstprofile) {
      document.getElementById('editProfile0')?.focus()
    }
  }, [firstprofile])
  useEffect(() => {
    pushScreenViewEvent({screenName:'edit_watch_profile',screenData:loginapi ?? registerInfo, prevScreenName:'watch_profile'})
    SpatialNavigation.focus()
    if (data == 'Profile Updated') {
      setShowNotification(true)
      setProfileUpdateDelete('updated')

      setTimeout(() => {
        setShowNotification(false)
      }, 4000)
    } else if (data == 'Profile Deleted') {
      setShowNotification(true)
      setProfileUpdateDelete('deleted')

      setTimeout(() => {
        setShowNotification(false)
      }, 4000)
    }
  }, [])

  const goToWatch = useCallback(event => {
    if (
      event.keyCode === 10009 ||
      event.keyCode === 461 ||
      event.keyCode === 8
    ) {
      navigate('/watchprofile')
      return null
    }
  }, [])

  useEffect(() => {
    document.body.addEventListener('keyup', goToWatch)
    return () => {
      document.body.removeEventListener('keyup', goToWatch)
    }
  }, [goToWatch])

  const handleSamsungKey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate('/watchprofile')
    }
  }
  const handleLgkey = keycode => {
    if (keycode === 405 || keycode === 461 || keycode == 8 || keycode === 'back') {
      navigate('/watchprofile')
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handleSamsungKey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handleEdit = (item, index) => {
    dispatch(getImageFocusInEdit({ index: index }))
    navigate('/editprofile', {
      state: { data: item, selectedProf: item?.user_image }
    })
  }

  const handleListo = () => {
    navigate('/watchprofile')
  }
  const handleAddProfile = () => {
    navigate('/addprofile', { state: { selectedProf: '', addIndex: true } })
  }

  useEffect(() => {
    if (state?.addIndex) {
      const element = document.getElementById('addProfile')
      element?.focus()
    }
  }, [state])

  const handleKeydown = e => {
    if (
      e.key == 'ArrowLeft' ||
      e.key == 'ArrowRight' ||
      e.key == 'ArrowDown' ||
      e.key == 'ArrowUp'
    ) {
      dispatch(getImageFocusInEdit({ index: 0 }))
    }
  }

  return (
    <div className="watchingProfile">
      <div className="appLogo">
        <img
          src={'images/claro-profile-logo.png'}
          className="logoImg"
          alt="logo"
        />
        <button className="backButton focusable" id="backButtonId" onClick={() => handleLgkey('back')}>
          <img
            src={'images/Profile_Icons/ic_shortcut_amarillo.png'}
            className="yellowDot"
            alt="img not found"
          />
          <img
            src={'images/Profile_Icons/ic_shortcut_back.png'}
            className="backArrow"
            alt="img not found"
          />
          <span className="backButtonText">
            {translations?.language?.[region]?.atv_back_notification}
          </span>
        </button>
      </div>

      <p className="profileWatch">
        {translations?.language?.[region]?.Perfiles_SeleccionarPerfil_Título_TextoTitulo
          ? translations?.language?.[region]?.Perfiles_SeleccionarPerfil_Título_TextoTitulo
          : 'Perfiles_SeleccionarPerfil_Título_TextoTitulo'}
      </p>
      <div className="parent-editwatch-profile-container">
        <div className="profileImgContainer">
          {profiledata &&
            profiledata?.members.map((item, index, array) => (
              index < 5 &&
              <div className="imageTitle" key={index}>
                <button
                  ref={ref => (userProfileRef.current[index] = ref)}
                  id={`editProfile${index}`}
                  className="userProfileImage focusable"
                  key={index}
                  autoFocus={imageFocus?.index == index}
                  onClick={() => handleEdit(item, index)}
                  data-sn-down="#ListoEditButton_id"
                  data-sn-up="#backButtonId"
                  onKeyDown={e => handleKeydown(e)}
                >
                  <div
                    className="editUserImage"
                    style={{
                      background: `linear-gradient(301deg, rgba(6, 6, 6, 0) 0%, rgb(0 0 0 / 12%) 50%, rgb(0 0 0 / 0%) 100%),url(${item?.user_image})`
                    }}
                  ></div>
                  <div className="penIconImg">
                    <img
                      src="images/Profile_Icons/ic edit.png"
                      className="editIconImg"
                      alt="img not found"
                    />
                  </div>
                  <p className="userProfName">
                    {item?.username.length > 15
                      ? `${item?.username.slice(0, 15)}...`
                      : item?.username}
                  </p>
                </button>
              </div>
            ))}

          {profiledata && profiledata?.totalMembers <= 4 ? (
            <div className="imageTitle">
              <button
                className="userProfileImage focusable"
                id="addProfile"
                onClick={handleAddProfile}
              >
                <LazyLoadImage
                  src="images/Profile_Icons/Atoms_AAF_Onboarding_Profile_AddProfile.png"
                  className="user-image"
                />
                <p className="user-prof-name">
                  {' '}
                  {translations?.language?.[region]
                    ?.addProfile_access_title_label
                    ? translations?.language?.[region]
                      ?.addProfile_access_title_label
                    : 'addProfile_access_title_label'}
                </p>
              </button>
            </div>
          ) : null}
        </div>
        <div className="listButtonContainer">
          <button
            id="ListoEditButton_id"
            className="listoEditButton focusable"
            onClick={handleListo}
          >
            <span className="listoText">
              {' '}
              {translations?.language?.[region]
                ?.Perfiles_EditarPerfil_TextoBotonPrimario
                ? translations?.language?.[region]
                  ?.Perfiles_EditarPerfil_TextoBotonPrimario
                : 'Perfiles_EditarPerfil_TextoBotonPrimario'}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default WatchProfileEdit
