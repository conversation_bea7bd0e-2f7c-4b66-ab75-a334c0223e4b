.mosaic-grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    padding: 0px 190px 0px 105px;
    overflow: scroll;
    height: 700px;
    top: 0;
  
    .mosaic-grid-item {
      background: transparent linear-gradient(
          149deg,
          #3a3b41 0%,
          #28292f 100%
        )
        0% 0% no-repeat padding-box;
      border-radius: 4px;
      opacity: 1;
      text-align: center;
      width: 407px;
      height: 229px;
      flex-direction: column;
      justify-content: center;
      display: flex;
  
      .mosaic-channel-number {
        font: normal normal normal 30px/39px Roboto;
        letter-spacing: 0px;
        color: #ffffff;
        opacity: 1;
        margin: -4px 35px 0px auto;
      }
  
      .mosaic-channel-image {
        height: 53px;
        display: flex;
        margin: 0px auto;
        justify-content: center;
        padding-top: 16px;
        padding-bottom: 22px;
  
        img {
          text-align: center;
        }
      }
  
      .mosaic-channel-title {
        text-align: center;
        font: normal normal normal 30px/38px Roboto;
        letter-spacing: 0px;
        color: #ffffff;
        opacity: 1;
        bottom: 0px;
        padding-top: 33px;
      }
  
      &:focus {
        border: 2px solid red;
        opacity: 1;
        border-radius: 3px;
        outline: unset;
      }
  
      & .lockedchannel-image {
        margin-right: 0px !important;
      }
    }
  }
  