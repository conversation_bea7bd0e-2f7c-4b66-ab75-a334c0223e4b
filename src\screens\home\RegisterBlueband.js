import React, { useEffect, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { getRegisterPopup } from '../../store/slices/login'
import { pushRegisterCompleteEvent } from '../../GoogleAnalytics'
import './Navbar.scss'

const RegisterBlueband = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const apaMeta = useSelector((state) => state?.initialReducer?.appMetaData)
  const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const region = localStorage.getItem('region')
  const startTimeRef = useRef(null)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const okButton = translations?.language?.[region]?.BotonShortcut_TextoOK ?? 'BotonShortcut_TextoOK'
  const cerrarButton = translations?.language?.[region]?.BotonShortcut_TextoCerrar ?? 'BotonShortcut_TextoCerrar'

  useEffect(() => {
    startTimeRef.current = Date.now()
    return () => {
      startTimeRef.current = null
    }
  }, [])

  const navToSubscribe = (event) => {
    const engagement_time_msec = Date.now() - startTimeRef.current
    if (document.getElementById('search-container')) {
      event.preventDefault()
    } else {
      dispatch(getRegisterPopup(false))
      pushRegisterCompleteEvent(loginInfo,okButton?.toLowerCase(),engagement_time_msec)
      startTimeRef.current = Date.now()
      navigate('/my-settings/my-subscriptions/new-plan-selector', { state: { gaPageName :'inicio', pageName: 'home' } })
    }
  }
  const handleBack = (event) => {
    const engagement_time_msec = Date.now() - startTimeRef.current
    event.preventDefault()
    dispatch(getRegisterPopup(false))
    pushRegisterCompleteEvent(loginInfo,cerrarButton?.toLowerCase(),engagement_time_msec)
    startTimeRef.current = Date.now()
  }

  return (
    <div className='register-popup'>
      <p className="notification-title">
        {translations?.language?.[region]?.Onboarding_Registro_TextoCintillo1 ?? 'Onboarding_Registro_TextoCintillo1'}
      </p>
      <div className="blueband-ok-button">
        <button
          className="ok-button focusable"
          onClick={e => navToSubscribe(e)}
          id='bluebandGreenButton'
        >
          <img
            className="ok-greendot"
            src="images/green_shortcut.png"
          />
          <span className="okbutton-Text">{okButton}</span>

        </button></div>
      <div className="blueband-back-button">
        <button
          className="resgister-back-button focusable"
          onClick={e => handleBack(e)}
          id='bluebandyellowButton'
        >
          <img
            className="back-yellowdot"
            src="images/Home_icons/yellowdot.png"
          />
          <span className="backbutton-Text">{cerrarButton}</span>
        </button>
      </div>
    </div>
  )
}

export default RegisterBlueband
