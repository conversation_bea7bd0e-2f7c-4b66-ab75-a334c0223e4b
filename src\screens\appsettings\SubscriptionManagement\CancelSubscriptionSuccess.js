import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import './SubscriptionManagements.scss';
import { useNavigate } from "react-router-dom";

const CancelSubscriptionSuccess = () => {

	const navigate = useNavigate();

	const region = localStorage.getItem('region')

	const apaMeta = useSelector((state) => state?.initialReducer?.appMetaData)
	const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)


	const handleAcceptClick = () => {
		navigate('/my-settings/my-subscriptions/active-subscriptions')
	}

	useEffect(() => {
		document.getElementById('acceptButton').focus();
		setTimeout(() => {
			navigate('/my-settings/my-subscriptions/active-subscriptions')
		},5000)
	},[])

	return(
		<div className="appCss">
			<img src={'images/Logos_Claro_Video.svg'} className="claroLogo" alt="logo" />
			<div className="cancelSuccessDiv">
				<img src={'images/greenTick.png'}className={'greenTick'} />
				<span className="subscriptionCancelText">{translations?.language?.[region]?.atv_subscription_cancelled_title}</span>
				<button className="cancelSuccessAccept" id='acceptButton' onClick={handleAcceptClick}>{translations?.language?.[region]?.Accept}</button>
			</div>
		</div>
	)

}

export default CancelSubscriptionSuccess;
