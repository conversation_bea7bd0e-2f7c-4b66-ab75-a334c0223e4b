import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import ProgressBar from '../screens/Progressbar/Progressbar'
import './PlayerEpisode.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useDispatch } from 'react-redux'
import {
  EnableplayerEpisodeScreen,
  getMediaAPI
} from '../store/slices/PlayerSlice'
import { getProgressbarBookmark } from '../store/slices/SearchSlice'
import { getFinishSubscriptionPlayerData, getViewSubscribeData, getVodSubscriptionInfo } from '../store/slices/settingsSlice'
import EpConfirmation from '../screens/VodSeries/EpConfirmation'
import { getEpisodeMoreInfo, vodSeries, vodSeriesCast } from '../store/slices/vodSeriesSlice'
import { setReturnFocusById } from '../store/slices/SearchSlice'
import { interactionType, SELECT_CONTENT, SERIES } from '../GoogleAnalyticsConstants'
import { pushNewInteractionContentEvent } from '../GoogleAnalytics'
import { CURRENT_PLATFORM } from '../utils/devicePlatform'

const PlayerEpisode = forwardRef(
  (
    {
      content,
      id,
      currentEp,
      currentButtonFocus,
      percent,
      playNext,
      pauseplayer,
      closePlayerEp,
      playFocusImg,
      contentDataplayer,
      episodeData,
      inputValue,
      gaContentData
    },
    ref
  ) => {
    const dispatch = useDispatch()
    const navigate = useNavigate()
    
    const seasonPlayRef = useRef([])
    const episodeRailRef = useRef([])
    const lastFocusedIdx = useRef(-1)
    const smallprogress = true

    const [currentBtnFocus, setcurrentBtnFocus] = useState('')
    const [seasonData, setSeasonData] = useState('')
    const [currentSeason, setCurrentSeason] = useState('')
    const [focusedEpisode, setFocusedEpisode] = useState(-1)
    const [selSeasonData, setSelSeasonData] = useState({})
    const [clickedEp, setClickedEp] = useState('')
    const [lastFocusedEp, setLastFocusedEp] = useState(0)
    const [playbackRetry, setPlaybackRetry] = useState(false)
    const [streamType, setStreamType] = useState('')
    const [episodesIds, setEpisodesIds] = useState([])
    const [filterlist, setFilterlist] = useState('')
    const [epicCard, setEpicCard] = useState(false)
    const [focusedEpisodeIndex, setFocusedEpisodeIndex] = useState(0)
    
    
    const watchFree = useSelector(state => state?.login?.watchFreestate)
    const playerRedux = useSelector(
      state => state?.player?.getMediaRes?.response
    )
    const subscriptionsInfo = useSelector(
      state => state?.settingsReducer?.getVodSubsInfo?.response
    )
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    let filteredRegionLists = apaMetaData?.byr_filterlist_configuration
    ? JSON.parse(apaMetaData?.byr_filterlist_configuration)
    : ''
    const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
    const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
    const vodDetails = useSelector(state => state?.login?.vcardDetails)
    const userId = userDetails?.user_id
    const showReturnFocus = useSelector(state => state?.search?.setReturnFocus)
    

    const registerInfo = useSelector(
      state => state?.login?.registerSuccess?.response
    )
    const playerEpisode = useSelector(
      state => state?.player?.EnablePlayerEpisode
    )
    const getMediaError = useSelector(
      state => state?.player?.getMediaError?.errors?.[0]
    )
    const vodSeriesCastRedux = useSelector(
      state => state?.getVodSeries?.seriesCastData
    )
    const subscriptionInfo = useSelector(
      state => state?.settingsReducer?.getVodSubsInfo?.response
    )
    const translations =
        apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const region = localStorage.getItem('region')
    
    const apilanguage = translations?.language?.[region]
    const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
    const vodSeriesDataRedux = useSelector(
        state => state?.getVodSeries?.seriesData
      )
    const progressbarContentData = useSelector(
          state => state?.search?.progressbarContent
        )

    
    const supportedStream =
      apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)
    useImperativeHandle(ref, () => ({
      handleSeasonKey() {
        const e = { key: 'ArrowDown' }
        handleSeasonKey(e)
      }
    }))

        useEffect(() => {
          setFilterlist(
            filteredRegionLists?.[`${region}`]?.filterlist ??
              filteredRegionLists?.default?.filterlist
          )
        }, [filteredRegionLists])

      
        useEffect(() => {
          // If vodSeriesDataRedux or seasons is empty/undefined, set empty array and return
          if (!vodSeriesDataRedux?.seasons?.length) {
            setEpisodesIds([]);
            return;
          }
          // Transform seasons data into array of episode IDs in one chain
          const episodeIds = vodSeriesDataRedux.seasons
            .flatMap(season => season.episodes) // Flatten all episodes from all seasons
            .map(episode => episode.id);        // Extract just the IDs
          setEpisodesIds(episodeIds);
        }, [vodSeriesDataRedux]);


    useEffect(() => {
      SpatialNavigation.focus()
      setStreamType(
       !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      setSelSeasonData(
        playerRedux?.group?.common?.extendedcommon?.media?.episode
      )
      const seasonMain = document.getElementById('seasonMain')
      seasonMain && seasonMain.addEventListener('keydown', handleKeyDown)
      return () => {
        seasonMain && seasonMain.removeEventListener('keydown', handleKeyDown)
      }
    }, [])

    useEffect(() => {
      if (subscriptionsInfo?.playButton?.payway_token && clickedEp) {
        watchFree
          ? dispatch(
              getMediaAPI({
                id: clickedEp,
                payway_token: subscriptionsInfo?.playButton?.payway_token,
                type: 'watchfree'
              })
            )
          : callGetMedia()
      }
    }, [subscriptionsInfo])

    useEffect(() => {
      content && handleSeasonEpisodes()
    }, [content])

    useEffect(() => {
      seasonPlayRef?.current[selSeasonData?.season - 1]?.focus()
      setcurrentBtnFocus(`season${selSeasonData?.season}`)
      const res =
        content?.seasons &&
        content.seasons.length > 0 &&
        content.seasons[selSeasonData?.season - 1]
      res && handleSeasons(res, selSeasonData?.season - 1)
    }, [selSeasonData])

    useEffect(() => {
      if (!playerEpisode) {
        episodeRailRef?.current[lastFocusedEp]?.focus()
      }
    }, [playerEpisode])

    useEffect(() => {
      const code = getMediaError?.code
      if (playerRedux?.media || code) {
        if (code == 'PLY_PLY_00009') {
          setStreamType(
              supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
              ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
              : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
          )
          setPlaybackRetry(true)
        }
        if (playbackRetry) {
          setPlaybackRetry(false)
          setStreamType(
            !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
              ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
              : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
          )
        }
      }
    }, [playerRedux, getMediaError, playbackRetry])

    useEffect(() => {
      if (playbackRetry && streamType) {
        callGetMedia()
      }
    }, [playbackRetry, streamType])

    useEffect(() => {
      if (playerRedux?.media && clickedEp) {
        closePlayerEp(false)
        playNext(playerRedux)
        setClickedEp('')
      }
    }, [playerRedux])

    const callGetMedia = () => {
      const payload = {
        id: clickedEp,
        payway_token: subscriptionsInfo?.playButton?.payway_token,
        HKS: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDetails?.session_stringvalue,
        userId: vodDetails?.confirmscreen
          ? loginInfo?.user_id ?? registerInfo?.user_id
          : userDetails?.user_id,
        user_token: loginInfo?.user_token ?? registerInfo?.user_token,
        streamType
      }
      dispatch(getMediaAPI(payload))
    }

    const handleKeyDown = event => {
      const element = document.getElementById('episodeFocus')
      if (element) {
        const cards = document.querySelectorAll('.episode-card-img')
        const focusedElement = document.activeElement
        const focusedIndex = Array.from(cards).indexOf(focusedElement)

        if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
          const nextCard = cards[focusedIndex + 1]
          const containerRect = element.getBoundingClientRect()
          const nextCardRect = nextCard.getBoundingClientRect()

          if (nextCardRect.right > containerRect.right) {
            element.scrollLeft += nextCardRect.right - containerRect.right
          }
        } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
          const prevCard = cards[focusedIndex - 1]
          const containerRect = element.getBoundingClientRect()
          const prevCardRect = prevCard.getBoundingClientRect()

          if (prevCardRect.left < containerRect.left) {
            element.scrollLeft -= containerRect.left - prevCardRect.left
          }
        }
      }
    }

    const handleSeasonEpisodes = () => {
    !currentSeason && setCurrentSeason(1)
     content?.seasons_count == '1' &&  content?.last_season == '2' && content?.seasons && setSeasonData(content?.seasons[0])
    }

    const handleSeasons = (item, index) => {
      setcurrentBtnFocus(
        `season${
          content?.seasons_count == '1' ? content?.last_season : index + 1
        }`
      )
      setSeasonData(item)
      setCurrentSeason(
        content?.seasons_count == '1' ? content?.last_season : index + 1
      )
    }

    const handleSeasonKey = e => {
      if (e.key == 'ArrowDown') {
        const focusIdx =
          focusedEpisode == -1 && lastFocusedIdx.current == -1
            ? 0
            : lastFocusedIdx.current ?? focusedEpisode
        setFocusedEpisode(focusIdx)
      } 
      if(e?.target?.id == 'episodeBtn'){
        setEpicCard(true)
      }else{
        setEpicCard(false)
      }
    }

    const handleEpisodes = (e, index) => {
      if(e?.target?.id === 'episodeBtn'){
        setEpicCard(true)
      }else if(e?.target?.id === `season${index + 1}`){
        setEpicCard(false)
      }

      if (seasonData?.episodes && epicCard) {
        const carousel = document.querySelector('.carousel')

        let currentIndex = focusedEpisode
        let prevIndex
        const images = document.querySelectorAll('.episode-container')
        const totalImages = Object.keys(images)?.length

        const imageWidth = 370

        if (e.key == 'ArrowLeft') {
          prevIndex = currentIndex
          // images[currentIndex].classList.remove('carousel-border') //commented for future development
          setFocusedEpisode(-1)
          currentIndex = (currentIndex - 1 + totalImages) % totalImages
          carousel.style.transform = `translateX(-${imageWidth}px)`
          carousel.insertBefore(images[currentIndex], carousel.firstChild)
          setTimeout(() => {
            carousel.style.transform = ''
            carousel.classList.add('sliding-transition')
            // images[currentIndex].classList.add('carousel-border') //commented for future development
            // setFocusedEpisode(images[currentIndex]?.getAttribute('data-value'))
            setFocusedEpisode(Number(images[currentIndex]?.getAttribute('data-value')))

          }, 10)

          setTimeout(() => {
            carousel.classList.remove('sliding-transition')
          }, 490)
        }

        if (e.key == 'ArrowRight') {
          prevIndex = currentIndex
          // images[prevIndex]?.classList?.remove('carousel-border') //commented for future development
          setFocusedEpisode(-1)
          currentIndex = (currentIndex + 1) % totalImages

          carousel.style.transform = `translateX(-${imageWidth}px)`
          carousel.appendChild(images[prevIndex])

          setTimeout(() => {
            carousel.style.transform = ''
            carousel.classList.add('sliding-transition')
            // images[currentIndex].classList.add('carousel-border') //commented for future development
            // setFocusedEpisode(images[currentIndex]?.getAttribute('data-value'))
            setFocusedEpisode(Number(images[currentIndex]?.getAttribute('data-value')))
          }, 10)

          setTimeout(() => {
            carousel.classList.remove('sliding-transition')
          }, 500)
        }
      }
    }
    // const handleEpisodes = (e,index) => {
    //   if(e?.target?.id === 'episodeBtn'){
    //     setEpicCard(true)
    //   }else if(e?.target?.id === `season${index + 1}`){
    //     setEpicCard(false)
    //   }
    //   const episodes = seasonData?.episodes?.slice().sort((a, b) => a?.episode_number - b?.episode_number)


    //   if (!episodes || !episodes?.length || episodes?.length === 0 ||  !epicCard) return
    
    //   let newIndex = focusedEpisodeIndex
    
    //   if (e.key === 'ArrowLeft') {
    //     newIndex = (focusedEpisodeIndex - 1 + episodes?.length) % episodes?.length
    //   }
    
    //   if (e.key === 'ArrowRight') {
    //     newIndex = (focusedEpisodeIndex + 1) % episodes?.length
    //   }
    
    //   setFocusedEpisodeIndex(newIndex)
    //   setFocusedEpisode(newIndex)
    
    //   // 💡 Scroll the episode card into view
    //   const nextEpisodeEl = episodeRailRef?.current?.[newIndex]
    //   nextEpisodeEl?.scrollIntoView({ behavior: 'smooth', inline: 'center' })
    
    //   // 💡 Set focus on the next episode's button
    //   setTimeout(() => {
    //     nextEpisodeEl?.focus()
    //   }, 100)
    // }

    const episodeClick = (item, index) => {
      pushNewInteractionContentEvent(
        gaContentData,
        SELECT_CONTENT,
        SERIES,
        interactionType.CLICK_IMAGEN
      )
      if (
        item?.format_types !== 'free,download' &&
        item?.format_types !== 'free' &&
        watchFree
      ) {
        pauseplayer?.pause()
        dispatch(setReturnFocusById(index))
        setLastFocusedEp(index)
        dispatch(EnableplayerEpisodeScreen(true))
        setClickedEp(item)
      } else {
        setClickedEp(item?.id)
        dispatch(EnableplayerEpisodeScreen(false))
         dispatch(
              vodSeriesCast({
                id: item?.id,
                userId,
                hks: vodDetails?.confirmscreen
                  ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
                  : userDetails?.session_stringvalue,
                is_kids: vodDetails?.confirmscreen
                  ? loginInfo?.is_kids ?? registerInfo?.is_kids
                  : userDetails?.is_kids
              })
            )
             dispatch(
                  vodSeries({
                    id: item?.id,
                    userId,
                    hks: vodDetails?.confirmscreen
                      ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
                      : userDetails?.session_stringvalue,
                    is_kids: vodDetails?.confirmscreen
                      ? loginInfo?.is_kids ?? registerInfo?.is_kids
                      : userDetails?.is_kids
                  })
                )
        dispatch(
          getVodSubscriptionInfo({
            userId: vodDetails?.confirmscreen
              ? loginInfo?.parent_id ?? registerInfo?.parent_id
              : userDetails?.parent_id,
            hks: vodDetails?.confirmscreen
              ? loginInfo?.session_stringvalue ??
                registerInfo?.session_stringvalue
              : userDetails?.session_stringvalue,
            url: `group_id=${item?.id}`
          })
        )

        if (subscriptionInfo && subscriptionInfo?.playButton?.visible == '0') {
        let monthData
                subscriptionInfo?.listButtons?.button?.length > 0 &&
                  subscriptionInfo?.listButtons?.button?.map(
                    eachdata => (monthData = eachdata)
                  )
                const viewPlan = {
                  logo: settingLogoUrl(monthData?.family),
                  workflowStart: monthData?.linkworkflowstart,
                  verticalImage: vodSeriesDataRedux?.image_medium,
                  family: monthData?.family,
                  periodicity: monthData?.periodicity,
                  price: monthData?.price,
                  currency: monthData?.currency,
                  styles: monthData?.style,
                  taxLabel: getTaxLabel(monthData?.family),
                  infoString: getFreeChargeString(monthData?.bonus),
                  subscribeButton: getSubscribeButton(monthData?.family),
                  viewButton: getViewDetailsButton(monthData?.family),
                  producttype: monthData?.producttype,
                  title:item?.title,
                  offertype: monthData?.oneoffertype,
                 isSeries: true
                }
                dispatch(getViewSubscribeData(viewPlan))
          dispatch(
              getEpisodeMoreInfo({
                // lastSeasonIndex: lastFocusedSeasonIndex,
                episodeItem: item,
                episodeButtonIndx: index,
                episodeclicked: true,
                seasonData: episodeData,
                contentDataFromMoreInfo: contentDataplayer
              })
            )
         dispatch(
                  getFinishSubscriptionPlayerData({
                    data: vodSeriesCastRedux?.common,
                    contentDataplayer: contentDataplayer,
                    episodeData: episodeData,
                    inputValue: inputValue
                  })
                )
               
          dispatch(
            getProgressbarBookmark({
              user_id: userId,
              group_id: episodesIds,
              user_hash: userDetails?.session_userhash,
              filterlist: filterlist,
              lasttouch: userDetails?.lasttouch?.seen
            })
          )
        navigate(
          '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
          {
            state: {
              pageName: '/vodPlayer',
              dataId: vodSeriesCastRedux?.common,
              data: item
            }
          }
        )
      }
      }
    }

    const settingLogoUrl = data => {
      return apaAssetsImages?.[
        `Transaccionales_Suscripcion_ImagenLogoCanal_${data}`
      ] //checking the data is there in apa/assets
    }
  
    const getTaxLabel = data => {
      return apilanguage?.[
        `${data}_subscriptionDescription_costTaxIncluded_label`
      ]
    }
  
    const getFreeChargeString = data => {
      return apilanguage?.[
        `transactional_${data}_subscription_plan_tryInvite_description${data}`
      ]
    }
  
    const getSubscribeButton = data => {
      return apilanguage?.['buy_' + data]
    }
  
    const getViewDetailsButton = data => {
      return apilanguage?.['includes_' + data]
    }

    return (
      <div id={id} className="player-Episode-season-container">
        {playerEpisode && (
          <EpConfirmation
            page={'playerrecord'}
            playplayer={pauseplayer}
            clickedepisode={clickedEp}
          />
        )}
        <div className="season-main-container">
          <div className="season-main-block">
            <div id="seasonMain" className="Season-button-header">
              {content?.seasons &&
                content?.seasons.length > 0 &&
                content.seasons.map((item, index) => (
                  <button
                    id={`season${index + 1}`}
                    ref={ref => (seasonPlayRef.current[index] = ref)}
                    key={index}
                    onKeyDown={handleSeasonKey}
                    onKeyUp={(e)=>handleEpisodes(e,index)}
                    data-sn-down={`#episodeBtn`}
                    onClick={() => {
                      episodeRailRef.current[focusedEpisode]?.scrollIntoView({
                        behavior: 'smooth'
                      })
                      setcurrentBtnFocus(
                        `season${
                          content?.seasons_count == '1'
                            ? content?.last_season
                            : index + 1
                        }`
                      )
                      episodeRailRef?.current[focusedEpisode]?.focus()
                      // const images =
                      //   document.querySelectorAll('.episode-container')
                      // images[focusedEpisode]?.classList?.add('carousel-border')
                      handleSeasons(item, index)
                    }}
                    className={`season-btn-series-focused focusable ${
                      currentBtnFocus ===
                      `season${
                        content?.seasons_count == '1'
                          ? content?.last_season
                          : index + 1
                      }`
                        ? 'focused'
                        : ''
                    }`}
                  >
                    <p className="season-title">
                      {`Temporada ${
                        content?.seasons_count == '1'
                          ? content?.last_season
                          : index + 1
                      }`.length > 13
                        ? `Temporada ${
                            content?.seasons_count == '1'
                              ? content?.last_season
                              : index + 1
                          }`.slice(0, 13)
                        : `Temporada ${
                            content?.seasons_count == '1'
                              ? content?.last_season
                              : index + 1
                          }`}{' '}
                    </p>
                  </button>
                ))}
            </div>
            <section className="carousel-main">
              <div className="carousel-container">
                <div className="carousel">
                  {seasonData?.episodes &&
                    seasonData.episodes.length > 0 &&
                    seasonData.episodes
                      .slice()
                      .sort((a, b) => a.episode_number - b.episode_number)
                      .map((item, index) => (
                        <div
                          id={`episodeContainer${index}`}
                          className={`episode-container ${
                            epicCard && currentButtonFocus != 'back' && focusedEpisode == index && 'carousel-border'
                          }`}
                          data-value={index}
                          key={index}
                        >
                          <button
                            ref={ref => (episodeRailRef.current[index] = ref)}
                            id="episodeBtn"
                            key={index}
                            className="episode-card-img focusable"
                            data-sn-down="#back"
                            data-sn-up={`#${currentBtnFocus}`}
                            onKeyUp={(e)=>handleEpisodes(e,index)}
                            onBlur={() => {
                              lastFocusedIdx.current = focusedEpisode
                              // const image =
                              //   document.getElementById('episodeContainer')
                              // image?.classList?.remove('carousel-border')
                            }}
                            // onKeyDown={handleEpisodes} future development
                            autoFocus={showReturnFocus == index}
                            onClick={() => {
                              episodeClick(item, index)
                              // setFocusedEpisode(index)
                              // setFocusedEpisodeIndex(index)
                            }}
                          >
                            <LazyLoadImage
                              className="carousel-image episode-card-lazy"
                              src={item?.image_still}
                              placeholderSrc={'images/landscape_card.png'}
                            />
                            <div
                              id="episodePlayBtn"
                              className="episode-play-btn"
                            >
                              <img
                                id="playPause"
                                style={{
                                  top: 0,
                                  visibility: `${
                                    focusedEpisode == index
                                      ? 'visible'
                                      : 'hidden'
                                  }`
                                }}
                                className="play-pause-btn"
                                src={playFocusImg}
                              />
                            </div>
                            <div className="episode-progress-bar">
                               {progressbarContentData?.map(eachepisodeData =>
                                  eachepisodeData?.id == item?.id || currentEp == item?.episode_number ?  (
                                    <div
                                      className="episode-progress-bar"
                                      style={{
                                        visibility:
                                          (eachepisodeData?.vistime?.last
                                            ?.progress > 0) || currentEp == item?.episode_number
                                            ? 'visible'
                                            : 'hidden'
                                      }}
                                    >
                                       <ProgressBar
                                style={{
                                  top: 0,
                                  visibility:
                                  (eachepisodeData?.vistime?.last
                                    ?.progress > 0 &&
                                  eachepisodeData?.vistime?.last
                                    ?.progress !== 100) || currentEp == item?.episode_number
                                    ? 'visible'
                                    : 'hidden'
                                }}
                                percent={ 
                                  eachepisodeData?.vistime?.last
                                    ?.progress
                                }
                                smallprogress={smallprogress}
                                isLoading={false}
                                // percent={percent}
                                size={'small'}
                                sliderWidth={0}
                              />
                                    </div>
                                  ) : null
                                )}
                            </div>
                          </button>
                          <div className="episodeText">
                            <p className="episode-card-title">
                              {item?.episode_number}. {item?.title_episode}
                            </p>
                          </div>
                        </div>
                      ))}
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    )
  }
)

export default PlayerEpisode
