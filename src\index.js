import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'
import { Provider } from 'react-redux'
import { store } from './store/sagaStore'
import { CURRENT_PLATFORM } from './utils/devicePlatform'

const rootElement = document.getElementById('root')
const root = createRoot(rootElement)
// Set global variable
window.devicePlatform = CURRENT_PLATFORM;
console.log('Detected Smart TV Platform:', window.devicePlatform);

root.render(
  <Provider store={store}>
    <App />
  </Provider>
)
