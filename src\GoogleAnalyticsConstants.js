// Event lists
export const LANDING = 'landing'
export const LOGIN = 'login'
export const INTERACTION_LOGIN = 'interaction_login'
export const ERROR_LOGIN = 'error_login'
export const PRIVACY_NOTICE = 'privacy_notice'
export const LOGOUT = 'log_out'
export const PROFILE_ADMIN = 'profile_admin'
export const SIGN_UP = 'sign_up'
export const INTERACTION_SIGN_UP = 'interaction_sign_up'
export const INTERACTION_SELECT_PLAN = 'interaction_select_plan'
export const FORGOT_PASSWORD = 'interaction_forgotten_password'
export const MENU = 'menu'
export const MENU_PROFILE = 'menu_profile'
export const SUB_MENU = 'submenu'
export const SEARCH = 'search'
export const CONTENT_PLAY = 'content_play'
export const CONTENT_PAUSE = 'content_pause'
export const CONTENT_DELAY = 'content_delay'
export const CONTENT_REWIND = 'content_back'
export const CONTENT_FORWARD = 'content_advance'
export const INTERACTION_PLAYER = 'interaction_player'
export const SCREEN_VIEW = 'screen_view'
export const INTERACTION_CONTENT = 'interaction_content'
export const SELECT_CONTENT = 'select_content'
export const PROTECTED_CONTENT = 'protected_content'
export const PIN_PROTECTION = 'pin_protection'
export const INTERACTION_FORGOTTEN_PIN = 'interaction_forgotten_pin'


// export const RESUME = 'resume'
export const CONTENT_PROGRESS = 'content_progress'
// export const SHARE = 'share'
// export const SUBSCRIPTION = 'subscription'
// export const BUY = 'buy'
// export const PRE_BUY = 'pre_buy'
// export const PRE_SUBSCRIPTION = 'pre_subscription'
// export const PURCHASE_SUBSCRIPTION = 'purchase_suscription'
// export const PURCHASE_RENT = 'purchase_rent'
// export const PURCHASE = 'purchase'

export const CONTENT_SELECTION_HOME = 'select_content'
export const CONTENT_SELECTION_MOVIE = 'content_selection_movie'
export const CONTENT_SELECTION_SERIES = 'content_selection_series'
export const CONTENT_SELECTION_KIDS = 'content_selection_kids'
export const CONTENT_SELECTION_PREMIUM = 'content_selection_addon'
export const CONTENT_SELECTION_MY_CONTENT = 'content_selection_my_content'
export const CONTENT_SELECTION_CLARO_SPORTS = 'content_selection_claro_sports'
export const CONTENT_SELECTION_SUBCATEGORIES = 'content_selection_subcategories'
export const CONTENT_SELECTION_PLUGIN_SUBCATEGORIES =
  'content_selection_plugin_subcategories'
export const CONTENT_SELECTION_TALENT = 'content_selection_talent_subcategories'
export const CONTENT_SELECTION_SEARCH_RESULT = 'content_selection_search_result'
export const CONTENT_SELECTION_START = 'content_selection_start'
export const CONTENT_SELECTION_VCARD = 'content_selection_vcard'
export const VIEW_VCARD = 'view_vcard'
export const END_PLAYER = 'end_player'
export const CONTENT_DETAIL_NO_RIGHT = 'content_detail_no_right'
export const CONTENT_DETAIL_NO_REPRODUCTION_RIGHT =
  'content_detail_no_reproduction_right'
export const PLAYER_TV = 'player_tv'
export const NOT_AVAILABLE = 'not_available'
export const NOT_APPLY = 'not_apply'
export const ADD_PAYMENT_METHOD = 'add_payment'
export const NOT_APPLICABLE = 'no aplica'
export const CONTENIDO_BLOQUEADO = 'contenido bloqueado'
export const PLAYER = 'player'
export const CONTINUA_VIENDO = 'continua viendo'

// Events payload constants
export const USER_ID = 'user_id'
export const PARENT_ID = 'parent_id'
export const SIGN_UP_METHOD = 'sign_up_method'
export const SUSCRIPTIONS = 'suscriptions'
export const DEVICE = 'device'
export const DEVICE_MODEL = 'device_model'
export const DEVICE_NAME = 'device_name'
export const AUTHPN = 'authpn'
export const DEVICE_OS = 'device'
export const OS_VERSION = 'os_version'
export const CONTENT_SUBSECTION = 'content_subsection'
export const COUNTRY = 'country'
export const MODULO_NAME = 'modulo_name'
export const ERROR_NAME = 'error_name'
export const MENU_NAME = 'menu_name'
export const SUBMENU_NAME = 'submenu_name'
export const CONTENT_SEASON = 'content_season'
export const ACTION_TYPE = 'action_type'
export const CONTENT_LIST_ID = 'content_list_id'
export const PROVIDER = 'provider'
export const CHANNEL_NAME = 'channel_name'
export const METHOD = 'method'
export const EMAIL = 'correo electronico'
export const SEARCH_TERM = 'search_term'
export const CONTENT_SECTION = 'content_section'
export const PAYMENT_METHOD = 'payment_method'
export const CONTRACT_TYPE = 'contract_type'
export const CONTENT_LIST = 'content_list'
export const CONTENT_ID = 'content_id'
export const CONTENT_NAME = 'content_name'
export const CONTENT_TYPE = 'content_type'
export const CONTENT_CATEGORY = 'content_category'
export const CONTENT_POSITION = 'content_position'
export const CONTENT_AVAILABILITY = 'content_availability'
export const CONTENT_BRAND = 'content_brand'
export const PRICE = 'price'
export const CONTENT_PRICE = 'content_price'
export const INTERACTION_TYPE = 'interaction_type'
export const CONTENT_EPISODE = 'content_episode'
export const SCREEN_NAME = 'screen_name'
export const SCREEN_CLASS = 'screen_class'
export const SUBCATEGORIES = 'subcategories'
export const SEARCH_ENGINE = 'search engine'
export const SELECT_PLAN = 'select_plan'
export const SUBSCRIPTION_CAROUSEL = 'subscription carousel'
export const SUBSCRIPTION_CAROUSEL_LIVE = 'guia de programacion'
export const GRABAR_PROGRAMA = 'grabar programa'
export const CAMBIAR_IDIOMA_DEL_CANAL = 'cambiar idioma del canal'
export const USER_TYPE = 'user_type'
export const PROGRESS_PERCENTAGE = 'progress_percentage'
export const EPISODIO = 'episodio'
export const TEMPORADA = 'temporada'
export const PAGE_URL = 'page_url'
export const PREVIOUS_CLASS = 'previous_class'
// Content section names
export const contentSelectionType = {
  HOME_START: 'home start',
  HOME_MOVIES: 'home movies',
  HOME_SERIES: 'home series',
  HOME_KIDS: 'home kids',
  HOME_ADD_ON: 'home addon',
  HOME_MY_CONTENTS: 'home my contents',
  ADD_ON_SUBCATEGORIES: 'addon subcategories',
  TALENT_SUBCATEGORIES: 'subcategories by talent',
  HOME_CLARO_SPORTS: 'home claro sports',
  VCARD: 'vcard'
}
// Content type names
export const TRAILER = 'trailer'
export const MOVIE = 'movie'
export const SERIES = 'serie'
export const EPISODES = 'episodes'
export const DOCUMENTARY = 'documentary'
export const TV_CHANNEL = 'tv channel'
export const APPS = 'apps'
export const TV = 'tv'

// Content availability
export const FREE = 'free'
export const RENT = 'rent'
export const BY_SUBSCRIPTION = 'por suscripcion'

// Interaction type
export const interactionType = {
  LANGUAGE_INTERACTION_TYPE: 'language',
  PLAY_INTERACTION_TYPE: 'reproducir',
  RESUME_INTERACTION_TYPE: 'resume',
  TRAILER_INTERACTION_TYPE: 'ver trailer',
  OMITIR_INTRO: 'omitir intro',
  DESDE_EL_PRINCIPIO: 'desde el principio',
  STARTOVER_INTERACTION_TYPE: 'start over',
  REANUDAR: 'reanudar',
  ADD_LIST_INTERACTION_TYPE: 'agregar a mi lista',
  REMOVE_LIST_INTERACTION_TYPE: 'quitar de mi lista',
  WATCH_NOW: 'watch_now',
  SALIR: 'salir',
  VER_CON_CLARO_VIDEO_INTERACTION_TYPE:'ver con claro video',
  SUBSCRIPTION_INTERACTION_TYPE:'suscribete',
  COMPRAR_INTERACTION_TYPE:'comprar',
  RENTAR_INTERACTION_TYPE:'rentar',  SIGUIENTE: 'siguiente',
  CANCELAR: 'cancelar',
  OLVIDASTE_TU_PIN_DE_SEGURIDAD: 'olvidaste tu pin de seguridad',
  ENTENDIDO: 'entendido',
  REGISTRATE: 'registrate',
  INICIO_SESSION: 'inicio session',
  CERRAR: 'cerrar',
  NEXT_EPISODE: 'next_episode',
  GO_LIVE: 'go_live'
}

// Payment details
export const NUMBER_TELMEX = 'number telmex'
export const NUMBER_TELCEL = 'number telcel'
export const PROMOTIONAL_CODE = 'promotional code'
export const TELMEX_BUISNESS = 'business'
export const TELMEX_HOME = 'home'

// Screen names
export const SPLASH_SCREEN = 'splash_screen'
export const ADD_PROFILE_SCREEN = 'add_profile_screen'
export const PROFILE_SCREEN = 'profile_screen'
export const MODAL_SCREEN = 'modal_screen'
export const EDIT_PROFILE_SCREEN = 'edit_profile_screen'
export const CAST_DETAILS_SCREEN = 'cast_details_screen'
export const SEARCH_SCREEN = 'search_screen'
export const SETTINGS_SCREEN = 'settings_screen'
export const MY_ACCOUNT_SCREEN = 'my_account_screen'
export const TRANSACTION_HISTORY_SCREEN = 'transaction_history_screen'
export const SUBSCRIPTION_MANAGEMENT_SCREEN = 'subscription_management_screen'
export const ADD_SUBSCRIPTION_SCREEN = 'add_subscription_screen'
export const HELP_SETTINGS_SCREEN = 'help_settings_screen'
export const MY_DEVICES_SCREEN = 'my_devices_screen'
export const CONFIGURE_PIN_SCREEN = 'configure_pin_screen'
export const EDIT_PIN_SCREEN = 'edit_pin_screen'
export const SECURITY_PIN_SCREEN = 'security_pin_screen'
export const LOCKED_CHANNEL_SCREEN = 'locked_channel_screen'
export const UNLOCK_PIN_SCREEN = 'unlock_pin_screen'
export const HELP_SCREEN = 'help_screen'
export const MORE_INFO_SCREEN = 'more_info_screen'
export const CONTACT_INFO_SCREEN = 'contact_information_screen'
export const FAQ_SCREEN = 'faq_screen'
export const TERMS_SCREEN = 'terms_and_conditions_screen'
export const PRIVACY_POLICY_SCREEN = 'privacy_policy_screen'
export const NOTICE_OF_PRIVACY_SCREEN = 'notice_of_privacy_screen'
export const CURRENT_PASSWORD_SCREEN = 'current_password_screen'
export const CHANGE_PASSWORD_SCREEN = 'change_password_screen'
export const LOGOUT_SCREEN = 'logout_screen'
export const SIGN_IN_EMAIL_SCREEN = 'sign_in_email_screen'
export const SIGN_IN_PASSW_SCREEN = 'sign_in_password_screen'
export const REGISTER_EMAIL_SCREEN = 'register_email_screen'
export const DETAIL_SEASON_AND_EPISODE_SCREEN =
  'detail_season_and_episode_screen'
export const CHOOSE_TO_REGISTER_SCREEN = 'choose_to_register_screen'
export const REGISTER_PSWD_SCREEN = 'register_password_screen'
export const WELCOME_SCREEN = 'welcome_screen'
export const HOME_SCREEN = 'home_screen'
export const MOVIES_SCREEN = 'movies_screen'
export const SERIES_SCREEN = 'series_screen'
export const KIDS_SCREEN = 'kids_screen'
export const PREMIUM_SCREEN = 'premium_screen'
export const MY_CONTENT_SCREEN = 'my_content_screen'
export const APPS_SCREEN = 'apps_games_screen'
export const PARENTAL_CONTROL = 'parental_control_screen'
export const ACTIVE_SUBSCRIPTION = 'active_subscription_screen'
export const CANCEL_SUBSCRIPTION = 'cancel_subscription_screen'
export const TRANSACTION_RESTRICTION_SCREEN = 'transaction_restriction_screen'
export const CONTENT_RESTRICTION = 'content_restriction_screen'
export const CLARO_SPORTS_SCREEN = 'claro_sports_screen'
export const LANGUAGE_OPTION_SCREEN = 'language_option_screen'
export const GENRE_SCREEN = 'genre_screen'
export const RENT_SCREEN = 'rent_screen'
export const PAYMENT_SUCCESS_SCREEN = 'payment_success_screen'
export const PLAN_DETAIL_SCREEN = 'plan_detail_screen'
export const MY_CONTENT_DELETE_SCREEN = 'my_content_delete_screen'
export const FORGOT_PASSWORD_ENTER_EMAIL_SCREEN =
  'forgot_password_enter_email_screen'
export const RECORDED_EPISODES_SCREEN = 'recorded_episodes_screen'

export const GUEST_PERSON = 'anonimo'
export const LOGGED_IN = 'registrado'
export const VCARD = 'vcard'
export const ADD_SUBSCRIPTION = 'add_subscription'
//Country code
export const regionToCountryCode = {
  'mexico': 'MX',
  'colombia': 'CO',
  'argentina': 'AR',
  'el salvador': 'SV',
  'peru': 'PE',
  'brasil': 'BR',
  'paraguay': 'PY',
  'uruguay': 'UY',
  'honduras': 'HN',
  'chile': 'CL',
  'costa rica': 'CR',
  'ecuador': 'EC',
  'guatemala': 'GT',
  'nicaragua': 'NI',
  'dominicana': 'RD'
}

