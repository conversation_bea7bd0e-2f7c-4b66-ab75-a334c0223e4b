# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.
# This workflow will build a Java project with <PERSON>radle and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-gradle

name: NodeJS with Webpack

on:
   workflow_dispatch:
    inputs:
      apktarget:
        type: choice
        description: APK Type
        required: true
        default: 'build'
        options: 
        - dev
        - dev-prod
        - build
        - build-tizen
        - build-tizen-prod
        - build-webos
        - build-webos-prod
        - build-tv
        
    branches: [ "master" ]
#  push:
#    branches: [ "master" ]
#  pull_request:
#    branches: [ "master" ]

#env:
  # The name of the main module repository
  #main_project_module: app
  
jobs:
  build:

    runs-on: windows-latest
    
    strategy:
      matrix:
        node-version: [20.x]
        #node-version: [18.x, 20.x, 22.x]
    
    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
    
    #- name: "SonarCloud: Analyze ${{github.repository}}@${{github.ref_name}}"
    #  uses: sonarsource/sonarqube-scan-action@master
    #  env:
    #    SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    #  with:
    #    args: >
    #      -Dsonar.organization=clarovideo
    #      -Dsonar.project=sct_nonandroid-repo-code
    #      -Dsonar.projectKey=sct_nonandroid-repo-code
    #      -Dsonar.exclusions=**/*.java
    
    - name: Build
      run: |
        npm install -g npm@8.19.4
        npm install -D webpack-cli
        npm run ${{ github.event.inputs.apktarget }}
#        npx webpack --config ${{ github.event.inputs.apktarget }}.js --mode=development
        

    # Upload Artifact Build
    # Noted For Output [main_project_module]/build/outputs/apk/
    #- name: Upload APK - ${{ github.event.inputs.apktarget }}
    #  uses: actions/upload-artifact@v4
    #  with:
    #    name: ${{ env.date_today }} - ${{ github.event.inputs.apktarget }} - ${{ env.repository_name }} - APK(s) generated
    #    path: ${{ env.main_project_module }}/build/outputs/apk/