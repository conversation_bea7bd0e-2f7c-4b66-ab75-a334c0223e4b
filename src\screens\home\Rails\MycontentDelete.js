import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import '../../../screens/home/<USER>'
import {
  deleteVodMoviesWatchList,
  getVodMoviesWatchlist
} from '../../../store/slices/VodMoviesSlice'
import {
  clearContentState,
  continueWatchlist,
  delContinueWatch
} from '../../../store/slices/getWatchListSlice'
import './MycontentDelete.scss'
import { clearFavouriteRespose, delFavouriteLive, getFavouriteLive } from '../../../store/slices/EpgSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { CURRENT_PLATFORM } from '../../../utils/devicePlatform'

const MycontentDelete = props => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const deteleData = state?.deteleData
  const userId = localStorage.getItem('userId')
  const lastTouch = localStorage.getItem('lasttouch')
  const delfavourite = useSelector(state => state?.epg?.delfavourite)

  const dispatch = useDispatch()
  const deleteWatchList = useSelector(state => state?.vodMovies?.delWatchList)
  const getdeletecontinuewatch = useSelector(
    state => state?.watchList?.deletecontinuewatchlist,
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const epgVersion = useSelector(state => state?.epg?.epgVersion)
  const apaMetaData = useSelector((state) => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      navigate('/home', { state: { id: state?.id, backfocusid:state?.backfocusid } })
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode == 46 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
) {
      navigate('/home', { state: { id: state?.id, backfocusid:state?.backfocusid } })
    }
  }
  const handleTranslationchange = useCallback((keyname) => {
    if (!apilanguage) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [apilanguage])

  const deleteContent = useCallback(() => {
    if (state?.delContinuewatch) {
      return handleTranslationchange("contentToDelete_modal_titleLastSeen_label")
    } else if (state?.page == "favourite") {
      return handleTranslationchange('contentToDelete_modal_titleFavoritedLive_label')
    } else return handleTranslationchange('contentToDelete_modal_titleFavorite_label')
  }, [])

  const handleWatchListDel = e => {
    e.preventDefault()
    if (state?.delContinuewatch) {
      dispatch(
        delContinueWatch({
          id: deteleData?.id,
          userId,
          user_hash: userDetails?.session_userhash
        })
      )
    } else if (state?.page == "favourite") {
      dispatch(
        delFavouriteLive({
          epg_version: epgVersion,
          user_hash: userDetails?.session_userhash,
          user_id: userDetails?.parent_id,
          user_token: userDetails?.user_token,
          object_id: deteleData?.id
        })
      )
    }
    else {
      dispatch(
        deleteVodMoviesWatchList({
          id: deteleData?.id,
          userId,
          HKS: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
    }
  }

  const clickcancel = e => {
    e.preventDefault()
    navigate('/home', { state: { id: state?.id,backfocusid:state?.backfocusid } })
  }

  const handleBack = e => {
    e.preventDefault()
    navigate('/home',{state:{backfocusid:state?.backfocusid}})
  }
  useEffect(() => {
    SpatialNavigation.focus();
    document.getElementById('confirmbt').focus()
  }, [])

  useEffect(() => {
    if (delfavourite?.msg === 'OK') {
      dispatch(
        getFavouriteLive({
          epg_version: epgVersion,
          hks: userDetails?.session_stringvalue,
          user_id: userDetails?.user_id,
          user_token: userDetails?.user_token,
          lasttouch: delfavourite?.lasttouch?.favorited ?? lastTouch,
          user_hash: userDetails?.session_userhash,
        })
      )
      localStorage.setItem('lasttouch', delfavourite?.lasttouch?.favorited)
      dispatch(clearFavouriteRespose())
      navigate('/home',{state:{backfocusid:state?.backfocusid}})
    }
  }, [delfavourite, userDetails])

  useEffect(() => {
    if (getdeletecontinuewatch?.msg === 'OK') {
      dispatch(
        continueWatchlist({
          id: getdeletecontinuewatch?.lasttouch?.seen ?? lastTouch,
          userId,
          HKS: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
      localStorage.setItem('lasttouch', getdeletecontinuewatch?.lasttouch?.seen)
      dispatch(clearContentState())
      navigate('/home', { state: { backfocusid: state?.backfocusid, delContinuewatch: state?.delContinuewatch } })
    }
  }, [getdeletecontinuewatch, userDetails])

  useEffect(() => {
    if (deleteWatchList?.msg === 'OK') {
      dispatch(
        getVodMoviesWatchlist({
          id: deleteWatchList?.lasttouch?.favorited ?? lastTouch,
          userId,
          HKS: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
      localStorage.setItem('lasttouch', deleteWatchList?.lasttouch?.favorited)
      dispatch(clearContentState())
      navigate('/home', { state: { backfocusid: state?.backfocusid, deleteHomeMycontent: state?.deleteHomeMycontent, railIndex: state?.railIndex } })
    }
  }, [deleteWatchList])

  const keyBackPress = useCallback(event => {
    if (
      event?.keyCode === 10009 ||
      event?.keyCode === 8 ||
      event?.keyCode === 461 ||
      event?.keyCode === 13
    ) {
      navigate('/home', {
        state: {
          page: 'continuewatch',
          page: 'mylist',
          backfocusid:state?.backfocusid
        }
      })
    }
  }, [])

  useEffect(() => {
    props?.continueDel && props?.listDel && document.body.addEventListener('keyup', keyBackPress)

    return () => {
      props?.continueDel && props?.listDel && document.body.removeEventListener('keyup', keyBackPress)
    }
  }, [keyBackPress])
  return (
    <div className="deletecard-mainclass">
      <div className="delete-regresser-box">
        <button
          className="filter-backScreen focusable"
          onClick={e => handleBack(e)}
          id='backbutton'
        >
          <LazyLoadImage
            className="filter-back-img-icon"
            src={'images/Vcard_Icons/yellowcircle_small.png'}
            placeholderSrc={'images/Vcard_Icons/yellowcircle_small.png'}
          />
          <LazyLoadImage
            className="filter-back-img-icon"
            src={'images/Vcard_Icons/icon_backpage.png'}
            placeholderSrc={'images/Vcard_Icons/icon_backpage.png'}
          />
          <span className="filter-back-button-regresar-title">{handleTranslationchange('top_head_option_button_return')}</span>
        </button>
      </div>
      <div className="delete-title">
        {deleteContent()}
      </div>
      <div className="image-class">
        <img src={deteleData?.image_small ?? "images/landscape_card.png"} className="delete-image" />
      </div>
      <div className="mycontent-title">{handleTranslationchange('contentToDelete_modal_descriptionBegin_label')} "{deteleData?.title}"{handleTranslationchange('contentToDelete_modal_descriptionEnd_label')}</div>
      <div className="button-block">
        <button
          id="confirmbt"
          className="confirm-button  focusable"
          onClick={e => handleWatchListDel(e)}
        >
          {handleTranslationchange('contentToDelete_modal_option_button_deleteRecord')}
        </button>
        <button
          id="cancelbtn"
          className="cancel-button focusable"
          onClick={e => clickcancel(e)}
        >
          {handleTranslationchange('contentToDelete_modal_option_button_cancel')}
        </button>
      </div>
    </div>
  )
}

export default MycontentDelete
