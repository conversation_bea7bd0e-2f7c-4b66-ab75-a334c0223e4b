import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import PinCreationSuccess from "./PinCreationSuccess";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<MemoryRouter
			history={history}
			initialEntries={[{ state: { pageName: 'livePlayer' } }]}
		>
			{children}
		</MemoryRouter>
	</Provider>
);
export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper });
};

const mockErrorResponse = {
	"status": "1"
}

const mockEpgChannelSuccessResponse = {
	response: {
		channels: [
			{
				group_id: "1135375"
			},
			{
				group_id: "784942"
			}
		]
	}
}


describe('Parental Control Settings page test', () => {

	test('should render without api mock data', () => {
		initialState.epg = {
			epgChannel: mockErrorResponse
		}
		renderWithState(<PinCreationSuccess />)
	})

	test('should render with api mock data', () => {
		initialState.epg = {
			epgChannel: mockEpgChannelSuccessResponse
		}
		renderWithState(<PinCreationSuccess />)
	})

	test('navigate to another page on accept click', () => {
		const { container } = renderWithState(<PinCreationSuccess />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'acceptButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})


})
