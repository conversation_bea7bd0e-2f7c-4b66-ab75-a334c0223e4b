import React from "react";
import BottomBar from "../BottomBar";
import '../../../styles/ManagePayments.css';

const SubscriptionError = (props) => {


	return(
		<div className="App-Settings">
			<div className="App-logo">
        <img src={'images/logo.png'} className="logo-img" alt="logo" />
      </div>
			<div className="sub1">
                <span className="txt1">To be able to access this channel, subscribe to</span>
                <img src={'images/logo.png'} className="imglogo" alt="logo" />
                <span className="txt2">Movies and series +5 TV channels</span>
                <span className="txt3">To subscribe to this channel contact the help centre at</span> 
                <span className="txt4">(01 800 252 9999)</span>
            </div>

            <div className="sub2">
                <button className="button1" >What does this include?</button>
                <button className="button2" >Cancel</button>
                </div>
			<BottomBar image={"images/selectBack.png"} title={"Back"}/>
		</div>
	)

}

export default SubscriptionError;