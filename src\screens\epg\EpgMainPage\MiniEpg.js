import React, { useEffect, useState, useCallback, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { FixedSizeList as List } from 'react-window'
import {
  getProgramDetailsData,
  getLiveReminder,
  getAlerts,
  getEventDetails,
  getNoLockChannels,
  getLiveTvRecording,
  getLiveTvSeriesRecordingList,
  getFavouriteLive
} from '../../../store/slices/EpgSlice'
import { MiniEpgSkeleton } from './EpgSkeletonLoading'
import MenuBar from './MenuBar'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import ProgramDetails from '../../programdetails/ProgramDetails'
import {
  getLiveIndex,
  getLockedChannelsList,
  getSubscriptionInfo
} from '../../../store/slices/settingsSlice'
import moment from 'moment'
import './NewMiniEpg.scss'
import { useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { COMMON_URL } from '../../../utils/environment'
import { CONTENIDO_BLOQUEADO, PLAYER, TV } from '../../../GoogleAnalyticsConstants'
import { pushScreenViewEvent } from '../../../GoogleAnalytics'

function MiniEpg(props) {
  const channelErr = props?.channelErr
  const lockedScreen = props?.lockedScreen

  const dispatch = useDispatch()
  const navigate = useNavigate()
  const zapRefTimer = useRef(null)

  const region = localStorage.getItem('region')
  const videoElement = document.getElementById('bitmovinplayer-video-player')

  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const playerChannelData = useSelector(state => state?.player?.channelData)
  const getFavouriteList = useSelector(state => state?.epg?.favouriteLive)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )
  const recordingList = useSelector(
    state => state?.epg?.RecordingList?.response
  )
  const seriesRecordingList = useSelector(
    state => state?.epg?.RecordingListSeries
  )
  const paywayTokenResponse = useSelector(
    state => state?.epg?.paywayToken?.response
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const reminderList = useSelector(state => state?.epg?.ReminderLive?.response)
  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getSubsInfo
  )
  const watchFree = useSelector(state => state?.login?.watchFreestate)

  const epgIndex = playerChannelData?.epgIndex
    ? epgSevenDaysData[1]?.channelResponse.findIndex(
        itrObj => itrObj.group_id == playerChannelData?.group_id
      )
    : 0
  const [channelCount, setChannelCount] = useState(epgIndex ? epgIndex : 0)
  const [day, setDay] = useState(props?.checkPastContent || props?.checkPastContentState ? 0 : 1)
  const [epgChannel, setEpgChannel] = useState(
    epgSevenDaysData[day]?.channelResponse
  )
  const [showOptionsPanel, setShowOptionsPanel] = useState(false)
  const [dayDirection, setDayDirection] = useState('')
  const [channelInformation, setChannelInformation] = useState('')
  const [buttonClicked, setButtonClicked] = useState(false)
  const [nowData, setNowData] = useState(
    epgChannel?.[channelCount]?.events?.[0]?.id
  )
  const [eventNotAvailable, setEventNotAvailable] = useState(false)
  const [areAllEventsNotAvailable, setAreAllEventsNotAvailable] =
    useState(false)
  const audioData = useRef(false)
  const keyDown = useRef(true)
  const timeStoredRef = useRef(null)
  const channelKeyDownRef = useRef(true)
  const channelTimeStoredRef = useRef(null)
  const contentDataRef = useRef(null)
  let zappingData = []
  const lastTouch = localStorage.getItem('lasttouch')
  const epgVersion = useSelector(state => state?.epg?.epgVersion)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const today = new Date()
  const now =
    today.getFullYear() +
    '/' +
    (today.getMonth() + 1 < 10
      ? '0' + (today.getMonth() + 1)
      : today.getMonth() + 1) +
    '/' +
    (today.getDate() < 10 ? '0' + today.getDate() : today.getDate()) +
    ' ' +
    today.toLocaleTimeString('en-US', { hour12: false })

  useEffect(() => {
    pushScreenViewEvent({screenName:'mini_epg',screenData:userDetails,prevScreenName:'live_player'})
    dispatch(
      getLiveReminder({
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(
      getLiveTvRecording({
        user_token: userDetails?.user_token,
        userId: userDetails?.parent_id
      })
    )
    dispatch(
      getLockedChannelsList({
        hks: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(
      getFavouriteLive({
        epg_version: epgVersion,
        hks: userDetails?.session_stringvalue,
        user_id: watchFree ? 0 : userDetails?.user_id,
        user_token: userDetails?.user_token,
        lasttouch:  lastTouch,
        user_hash: userDetails?.session_userhash
      })
    )
    dispatch(getEventDetails(epgChannel?.[channelCount]?.events?.[0]))
    let intitalLiveEvent
    epgChannel?.[channelCount]?.events.find(each => {
      now > each?.date_begin && now < each?.date_end
        ? (intitalLiveEvent = each)
        : null
    })

    const past = localStorage.getItem('pastEvent')
    localStorage.setItem('programName', intitalLiveEvent?.ext_original_name)
    !past && localStorage.setItem('programId', intitalLiveEvent?.id)

    contentDataRef.current = {
      user_id:
        userDetails?.user_id,
      parent_id: userDetails?.parent_id,
      sign_up_method: 'correo electronico',
      suscriptions: props?.subscriptions,
      user_type: watchFree ? 'anonimo' : 'registrado',
      device: COMMON_URL?.device_type,
      device_model: COMMON_URL?.device_model,
      device_name: COMMON_URL?.device_name,
      authpn: COMMON_URL?.authpn,
      content_subsection: 'no aplica',
      content_section: CONTENIDO_BLOQUEADO,
      country:
        userDetails?.country_code?.toLowerCase(),
      content_type: TV,
      content_availability: 'por suscripcion',
      page_title:'player live',
      previous_screen: 'player live'
    }

    return () => {
      channelTimeStoredRef.current = null
      timeStoredRef.current = null
      keyDown.current = null
      channelKeyDownRef.current = null
      contentDataRef.current = null
    }
  }, [])

  useEffect(() => {
    setChannelCount(epgIndex)
  }, [epgIndex])

  useEffect(() => {
    zappingData = epgChannel
      ?.map(each => {
        if (!lockedChannelsList?.some(every => every?.id == each?.group_id)) {
          return each
        }
      })
      .filter(Boolean)
    zappingData?.length > 0 && dispatch(getNoLockChannels(zappingData))
  }, [lockedChannelsList])

  useEffect(() => {
    let programId = localStorage.getItem('programId')
    let prevChannelCount = localStorage.getItem('prevChannelCount')
    if (!props?.isPastContent) {
      let liveIndex = epgChannel?.[channelCount]?.events?.findIndex(
        itrObj => itrObj?.id == nowData
      )
      liveIndex &&
        document.getElementById(`mini-epg-program-${liveIndex}`)?.focus()
    } else {
      let programIndex
      if (day == 1 && prevChannelCount != channelCount) {
        programIndex = epgChannel?.[channelCount]?.events?.findIndex(
          itrObj => itrObj?.id == nowData
        )
      } else {
        programIndex = epgChannel?.[channelCount]?.events?.findIndex(
          itrObj => itrObj?.id == programId
        )
      }
      programId &&
        document.getElementById(`mini-epg-program-${programIndex}`)?.focus()
    }
  }, [nowData, props?.checkPastContent, props?.isPastContent, day])

  useEffect(() => {
    if (!showOptionsPanel && day == 1) {
      let liveIndex = epgChannel?.[channelCount]?.events?.findIndex(
        itrObj => itrObj?.id == nowData
      )
      liveIndex &&
        document.getElementById(`mini-epg-program-${liveIndex}`)?.focus()
    } else {
      !showOptionsPanel && SpatialNavigation.focus()
    }
  }, [showOptionsPanel])

  useEffect(() => {
    localStorage.setItem('live-channel-id', playerChannelData?.group_id)
  }, [playerChannelData])

  useEffect(() => {
    if (dayDirection === 'left') {
      document
        .getElementById(
          `mini-epg-program-${epgChannel?.[channelCount]?.events?.length - 1}`
        )
        ?.focus()
    }
    setDayDirection('')
  }, [day])

  const handleFavourite = grp_id => {
    let filteredResp = getFavouriteList?.response?.groups?.filter(
      item => item?.id == grp_id
    )
    return filteredResp?.length > 0 ? true : false
  }

  const isReminderAvailable = programData => {
    const isReminder =
      typeof reminderList != 'string' &&
      reminderList?.filter(each => each?.event_id == programData?.id)
    if (isReminder?.length > 0 && now < programData?.date_begin) {
      return true
    } else {
      return false
    }
  }
  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  const isRecordAvailable = (eventData, channelData) => {
    const payway = paywayTokenResponse?.paqs?.paq?.filter(
      each =>
        each?.groups?.includes(channelData?.group_id) &&
        each?.npvrstorage != 0 &&
        each?.npvrstorage != null &&
        each?.timeshift != 0 &&
        each?.timeshift != null
    )
    if (
      payway?.length > 0 &&
      eventData?.ext_recordable == '1' &&
      channelData?.group?.common?.timeshift !== null
    ) {
      return true
    } else {
      return false
    }
  }

  const getRecordText = event => {
    const recordCheckValue = event?.event_alf_id
    const liveTVEpisodes = recordingList?.recordings?.find(
      each => each?.channel?.event?.event_alf_id == recordCheckValue
    )

    let liveTVSeries = []
    seriesRecordingList?.map(each =>
      each?.seriesResponse?.recordings?.map(every =>
        every?.channel?.event?.event_alf_id == recordCheckValue
          ? liveTVSeries.push(every)
          : null
      )
    )

    switch (
      liveTVEpisodes?.recording_status ??
      liveTVSeries?.[0]?.recording_status
    ) {
      case 0:
        return now < event?.date_begin
          ? `${truncateText('Metadata_TextoGrabacion_PorGrabar', 30)}`
          : `${truncateText('Metadata_TextoGrabacion_Grabando', 30)}`
      case 2:
        return `${truncateText('Metadata_TextoGrabacion_Grabado', 30)}`
      default:
        return `${truncateText('Player_Boton_TextoAccion_Grabar')}`
    }
  }

  const getRecordIcon = event => {
    const recordCheckValue = event?.event_alf_id
    const liveTVEpisodes = recordingList?.recordings?.find(
      each => each?.channel?.event?.event_alf_id == recordCheckValue
    )

    let liveTVSeries = []
    seriesRecordingList?.map(each =>
      each?.seriesResponse?.recordings?.map(every =>
        every?.channel?.event?.event_alf_id == recordCheckValue
          ? liveTVSeries.push(every)
          : null
      )
    )

    switch (
      liveTVEpisodes?.recording_status ??
      liveTVSeries?.[0]?.recording_status
    ) {
      case 0:
        return 'images/Program_Details_Icons/record_icon.png'
      case 2:
        return 'images/Record_Complete.png'
      default:
        return 'images/LiveTv/ic_recording_gray_epg.png'
    }
  }
  const handleInitialFocus = () => {
    setTimeout(() => {
      const rows = document.querySelectorAll('.mini-epg-nav')
      if (rows.length > 0) {
        rows.forEach(row => {
          const cards = row.querySelectorAll('.new-mini-epg-program')
          const focusedElement = document.activeElement
          const focusedIndex = Array.from(cards).indexOf(focusedElement)
          const containerRect = row.getBoundingClientRect()

          if (
            focusedElement &&
            (focusedIndex < cards.length - 1 || focusedIndex > 0)
          ) {
            const nextCard = cards[focusedIndex + 1]
            const prevCard = cards[focusedIndex - 1]

            if (nextCard) {
              const nextCardRect = nextCard.getBoundingClientRect()
              if (nextCardRect.right > containerRect.right) {
                row.scrollLeft += nextCardRect.right - containerRect.right
              }
            }

            if (prevCard) {
              const prevCardRect = prevCard.getBoundingClientRect()
              if (prevCardRect.left < containerRect.left) {
                row.scrollLeft -= containerRect.left - prevCardRect.left
              }
            }
          }
        })
      }
    }, 0)
  }

  const handleKeyDown = event => {
    let diff = Date.now() - timeStoredRef.current
    if (
      (keyDown.current == true && diff / 1000 < 0.25) ||
      (keyDown.current == false && diff / 1000 < 0.1)
    ) {
      event.preventDefault()
      event.stopPropagation()
      return
    }
    setTimeout(() => {
      const rows = document.querySelectorAll('.mini-epg-nav')
      if (rows.length > 0) {
        rows.forEach(row => {
          const cards = row.querySelectorAll('.new-mini-epg-program')
          const focusedElement = document.activeElement
          const focusedIndex = Array.from(cards).indexOf(focusedElement)
          const containerRect = row.getBoundingClientRect()

          if (
            (event?.keyCode == 39 ||
              event?.keyCode == 38 ||
              event?.keyCode == 40) &&
            focusedIndex < cards.length - 1
          ) {
            const nextCard = cards[focusedIndex + 1]
            const nextCardRect = nextCard.getBoundingClientRect()
            if (nextCardRect.right > containerRect.right) {
              row.scrollLeft += nextCardRect.right - containerRect.right
            }
          } else if (
            (event?.keyCode == 37 ||
              event?.keyCode == 38 ||
              event?.keyCode == 40) &&
            focusedIndex > 0
          ) {
            const prevCard = cards[focusedIndex - 1]
            const prevCardRect = prevCard.getBoundingClientRect()
            if (prevCardRect.left < containerRect.left) {
              row.scrollLeft -= containerRect.left - prevCardRect.left
            }
          }
        })
      }
    }, 0)
    keyDown.current = true
    timeStoredRef.current = Date.now()
  }
  useEffect(() => {
    handleInitialFocus()
    const rows = document.querySelectorAll('.mini-epg-nav')
    if (rows.length > 0) {
      rows.forEach(row => {
        row.addEventListener('keydown', handleKeyDown)
      })
    }
    return () => {
      if (rows.length > 0) {
        rows.forEach(row => {
          row.removeEventListener('keydown', handleKeyDown)
        })
      }
    }
  }, [])

  const getEventTags = (event, data) => {
    if (now > event?.date_begin && now < event?.date_end) {
      setNowData(event?.id)
      return data == 'tag' ? 'images/LiveTv/tag_ahora.png' : 'Present Event'
    } else if (now < event?.date_begin) {
      return data == 'tag' ? 'images/LiveTv/tag_mastarde.png' : 'Future Event'
    } else {
      return data == 'tag' ? 'images/LiveTv/tag_emitido.png' : 'Past Event'
    }
  }

  const getLockedChannelsIcon = item => {
    const found = lockedChannelsList?.find(each => each.id === item)

    if (found) {
      return true
    }
    return false
  }

  const getIsContractChanel = item => {
    const foundContract = paywayTokenResponse?.paqs?.paq?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return false
    }
    return true
  }

  const programHandler = (event, channelInfo, index, data) => {
    contentDataRef.current['provider'] = channelInfo?.group?.common?.proveedor_name?.toLowerCase(),
    contentDataRef.current['channel_name'] = channelInfo?.name?.toLowerCase(),
    contentDataRef.current['content_id'] = channelInfo?.group_id,
    contentDataRef.current['content_name'] = channelInfo?.events?.[index]?.ext_original_name?.toLowerCase(),
    contentDataRef.current['content_category'] = channelInfo?.events?.[index]?.dvb_content?.toLowerCase()
    contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
    contentDataRef.current['content_section'] = PLAYER
    const isContractChannel = getIsContractChanel(channelInfo?.group_id)
    const isBlocked = getLockedChannelsIcon(channelInfo?.group_id)
    let sameChannelPlay =
      channelInfo?.group_id == localStorage.getItem('live-channel-id')
    if (event?.keyCode === 13 || event?.key == 'Enter') {
      if (isContractChannel && watchFree) {
        navigate('/EPconfirmation', {
          state: { page: 'livePlayer', data: channelInfo, grid: 'miniEpg', gaContentData: contentDataRef.current }
        })
      }

      //Future Event
      else if (now < channelInfo?.events[index]?.date_begin) {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_AunNoDisponible',
              30
            )} |
            ${truncateText(
              'TvEnVivo_Notificacion_TextoCuerpo_EventoAunNoDisponible',
              50
            )}`,
            image: '',
            status: 'channel-events-future'
          })
        )
        dispatch(
          getProgramDetailsData({
            programData: channelInfo?.events[index],
            channelData: channelInfo
          })
        )
      }

      //Past Event
      else if (now > channelInfo?.events[index]?.date_end) {
        const payway = paywayTokenResponse?.paqs?.paq?.filter(
          each =>
            each?.groups?.includes(channelInfo?.group_id) &&
            each?.npvrstorage != 0 &&
            each?.timeshift != 0
        )
        if (
          payway?.length > 0 &&
          Number(channelInfo?.group?.common?.timeshift) > 0 &&
          channelInfo?.events[index]?.ext_catchup == 1 &&
          moment().unix() - channelInfo?.events[index]?.unix_begin <=
            payway?.[0]?.timeshift * 60 * 60
        ) {
          dispatch(
            getChannelData({
              group_id: channelInfo?.group_id,
              timeshift: channelInfo?.group?.common?.timeshift,
              switchChannel: 'yes',
              startTime: channelInfo?.events[index]?.unix_begin,
              endTime: channelInfo?.events[index]?.unix_end,
              catchup: channelInfo?.events[index]?.ext_catchup,
              epgIndex: channelCount
            })
          )
          props?.handlePastContentDay(channelInfo?.events?.[index])
          dispatch(
            getProgramDetailsData({
              programData: channelInfo?.events[index],
              channelData: channelInfo
            })
          )
          localStorage.setItem('programId', channelInfo?.events[index]?.id)
          localStorage.setItem(
            'programName',
            channelInfo?.events?.[index]?.ext_original_name
          )
          localStorage.setItem('prevChannelCount', channelCount)
          localStorage.setItem('pastEvent', true)
          if (isBlocked && !sameChannelPlay && !watchFree) {
            // Navigate to security pin page
            navigate('/my-settings/help-And-Settings/security-pin/configure', {
              state: {
                data,
                item: channelInfo,
                pageName: '/livePlayer',
                pastProgramData: channelInfo?.events?.[index],
                fromZap: false,
                checkPastContent: props?.checkPastContent,
                gaContentData: contentDataRef.current,
                eventData: now > channelInfo?.events[index]?.date_end ? {
                  startTime: channelInfo?.events[index]?.unix_begin,
                  endTime: channelInfo?.events[index]?.unix_end,
                  catchup: channelInfo?.events[index]?.ext_catchup,
                  epgIndex: channelCount
                } : null
              }
            })
          } else if (isContractChannel) {
            callPurchaseApi(isContractChannel, channelInfo)
          }
        } else {
          dispatch(
            getAlerts({
              message: `${truncateText(
                'TvEnVivo_Notificacion_TextoTitulo_AccionNoDisponible',
                30
              )} |
              ${truncateText(
                'TvEnVivo_Notificacion_TextoCuerpo_EventoNoDisponible',
                50
              )}`,
              image: '',
              status: 'channel-events-past'
            })
          )
        }
      }

      // Present Event
      else {
        if (
          channelInfo?.events[index]?.id == localStorage.getItem('programId')
        ) {
          dispatch(
            getProgramDetailsData({
              programData: channelInfo?.events[index],
              channelData: channelInfo
            })
          )
          setShowOptionsPanel(true)
        } else {
          dispatch(
            getChannelData({
              group_id: channelInfo?.group_id,
              timeshift: channelInfo?.group?.common?.timeshift,
              switchChannel: 'yes',
              startTime: channelInfo?.events[index]?.unix_begin,
              endTime: channelInfo?.events[index]?.unix_end,
              catchup: channelInfo?.events[index]?.ext_catchup,
              epgIndex: channelCount
            })
          )
          props?.handlePastContentDay(channelInfo?.events?.[index])
          dispatch(
            getProgramDetailsData({
              programData: channelInfo?.events[index],
              channelData: channelInfo
            })
          )
          localStorage.setItem('programId', channelInfo?.events[index]?.id)
          localStorage.setItem(
            'programName',
            channelInfo?.events[index]?.ext_original_name
          )
          localStorage.setItem('pastEvent', false)
          if (isBlocked && !sameChannelPlay && !watchFree) {
            // Navigate to security pin page
            navigate('/my-settings/help-And-Settings/security-pin/configure', {
              state: {
                data,
                item: channelInfo,
                pageName: '/livePlayer',
                pastProgramData: channelInfo?.events?.[index],
                fromZap: false,
                checkPastContent: props?.checkPastContent,
                gaContentData: contentDataRef.current,
                eventData: now > channelInfo?.events[index]?.date_end ? {
                  startTime: channelInfo?.events[index]?.unix_begin,
                  endTime: channelInfo?.events[index]?.unix_end,
                  catchup: channelInfo?.events[index]?.ext_catchup,
                  epgIndex: channelCount
                } : null
              }
            })
          } else if (isContractChannel) {
            callPurchaseApi(isContractChannel, channelInfo)
          }
        }
      }
    }

    // Code for LG buttons keys  ch + - button
    if (
      event?.keyCode == 33 ||
      event?.keyCode == 34 ||
      event?.keyCode == 85 ||
      event?.keyCode == 68
    ) {
      if (isContractChannel && watchFree) {
        navigate('/EPconfirmation', {
          state: { page: 'livePlayer', data: channelInfo, gaContentData: contentDataRef.current }
        })
      }
      if (isContractChannel) {
        callPurchaseApi(isContractChannel, channelInfo)
      } else if (
        localStorage.getItem('live-channel-id') != channelInfo?.group_id &&
        (!lockedChannelsList ||
          lockedChannelsList?.length == 0 ||
          (lockedChannelsList?.length > 0 &&
            !lockedChannelsList.some(
              every => every?.id == channelInfo?.group_id
            )))
      ) {
        zapRefTimer.current && clearTimeout(zapRefTimer.current)
        zapRefTimer.current = setTimeout(() => {
          dispatch(
            getChannelData({
              group_id: channelInfo?.group_id,
              timeshift: channelInfo?.group?.common?.timeshift,
              switchChannel: 'yes',
              startTime: channelInfo?.events[index]?.unix_begin,
              endTime: channelInfo?.events[index]?.unix_end,
              catchup: channelInfo?.events[index]?.ext_catchup,
              epgIndex: channelCount
            })
          )
          localStorage.setItem('programId', channelInfo?.events[index]?.id)
          localStorage.setItem(
            'programName',
            channelInfo?.events[index]?.ext_original_name
          )
          videoElement && videoElement.remove()
        }, 300)
      }
    } else if (event?.keyCode == 406 || event?.keyCode == 66) {
      let errorScreen = document.getElementsByClassName('player-err-screen')
      let playerError = document.getElementById('player-error')
      // !(errorScreen?.length > 0) && setShowOptionsPanel(true)
      if (!playerError && errorScreen?.length == 0) {
        dispatch(
          getProgramDetailsData({
            programData: channelInfo?.events?.[index],
            channelData: channelInfo
          })
        )

        setShowOptionsPanel(true)
      }
    }

    //Tizen os check for triggering the event
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch([
        'ColorF3Blue',
        'ChannelUp',
        'ChannelDown'
      ])
      const codes = {
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code,
        chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
        chMinus: tizen.tvinputdevice.getKey('ChannelDown').code
      }

      //Code for Samsung buttons keys forblue button and ch + - button
      if (codes.bluecode == keycode) {
        let errorScreen = document.getElementsByClassName('player-err-screen')
        let playerError = document.getElementById('player-error')
        // !(errorScreen?.length > 0) && setShowOptionsPanel(true)
        if (!playerError && errorScreen?.length == 0) {
          dispatch(
            getProgramDetailsData({
              programData: channelInfo?.events?.[index],
              channelData: channelInfo
            })
          )
          setShowOptionsPanel(true)
        }
      } else if (codes.chPlus === keycode || codes.chMinus === keycode) {
        if (isContractChannel && watchFree) {
          navigate('/EPconfirmation', {
            state: { page: 'livePlayer', data: channelInfo, gaContentData: contentDataRef.current }
          })
        }
        if (isContractChannel) {
          callPurchaseApi(isContractChannel, channelInfo)
        } else if (
          localStorage.getItem('live-channel-id') != channelInfo?.group_id &&
          (!lockedChannelsList ||
            lockedChannelsList?.length == 0 ||
            (lockedChannelsList?.length > 0 &&
              !lockedChannelsList.some(
                every => every?.id == channelInfo?.group_id
              )))
        ) {
          zapRefTimer.current && clearTimeout(zapRefTimer.current)
          zapRefTimer.current = setTimeout(() => {
            dispatch(
              getChannelData({
                group_id: channelInfo?.group_id,
                timeshift: channelInfo?.group?.common?.timeshift,
                switchChannel: 'yes',
                startTime: channelInfo?.events[index]?.unix_begin,
                endTime: channelInfo?.events[index]?.unix_end,
                catchup: channelInfo?.events[index]?.ext_catchup,
                epgIndex: channelCount
              })
            )
            localStorage.setItem('programId', channelInfo?.events[index]?.id)
            localStorage.setItem(
              'programName',
              channelInfo?.events[index]?.ext_original_name
            )
            videoElement && videoElement.remove()
          }, 300)
        }
      }
    }
    keyDown.current == false
    channelKeyDownRef.current = false
  }

  const callPurchaseApi = useCallback((isContractChannel, channelInfo) => {
    if (isContractChannel) {
      setChannelInformation(channelInfo)
      setButtonClicked(true)
      dispatch(
        getSubscriptionInfo({
          userId: userDetails?.parent_id,
          hks: userDetails?.session_stringvalue,
          url: `group_id=${channelInfo?.group?.common?.id}`
        })
      )
    }
  }, [])

  useEffect(() => {
    if (buttonClicked && addSubscriptions?.msg == 'OK') {
      const subscriptionResponse = addSubscriptions?.response?.listButtons
      const buttonMap = subscriptionResponse?.button ?? []
      if (buttonMap?.length > 0) {
        if (buttonMap?.length == 1) {
          buttonMap?.map(each => {
            if (each?.purchasable) {
              dispatch(
                getLiveIndex(
                  channelCount
                )
              )
              navigate('/premiumSubscription', {
                state: {
                  data: channelInformation?.group?.common,
                  priceDetails: each,
                  returnPage: 'epgToPlayer'
                }
              })
            } else {
              handleNavigateToNonContract(channelInformation)
            }
          })
        } else {
          const subScriptionValuesFalse = buttonMap.every(
            item => item?.purchasable === false
          )
          if (subScriptionValuesFalse) {
            handleNavigateToNonContract(channelInformation)
          } else {
            const subscriptionTrueValues = buttonMap.every(
              item => item?.purchasable === true
            )
            const singleSubscription = buttonMap.filter(
              item => item?.purchasable === true
            )
            const subscriptionNodeValueFalse = buttonMap.filter(
              item => item?.purchasable === false
            )
            const exactlySingleSubscriptionTrue =
              singleSubscription?.length === 1 &&
              subscriptionNodeValueFalse?.length === buttonMap?.length - 1
            if (subscriptionTrueValues) {
              navigate('/multiSubscription', {
                state: {
                  pageName: 'livePlayer',
                  data: channelInformation?.group?.common ?? channelInformation,
                  type: 'MultiSubscription'
                }
              })
            } else if (exactlySingleSubscriptionTrue) {
              dispatch(
                getLiveIndex(
                  channelCount
                )
              )
              navigate('/premiumSubscription', {
                state: {
                  data: channelInformation?.group?.common,
                  priceDetails: singleSubscription[0],
                  returnPage: 'epgToPlayer'
                }
              })
            }
          }
        }
      } else {
        handleNavigateToNonContract(channelInformation)
      }
    }
  }, [addSubscriptions])

  const handleNavigateToNonContract = channelInfoData => {
    navigate('/subScriptionCallPage', {
      state: {
        data: channelInfoData?.group?.common
          ? channelInfoData?.group?.common
          : channelInfoData,
        pageName: 'livePlayer'
      }
    })
  }

  //Migrating the useEffect here for recording list to liveplayer for real time updates.

  const channelHandler = (event, channelData, index) => {
    let diff = Date.now() - channelTimeStoredRef.current
    if (
      (channelKeyDownRef.current == true && diff / 1000 < 0.1) ||
      (channelKeyDownRef.current == false && diff / 1000 < 0.09)
    ) {
      event.preventDefault()
      event.stopPropagation()
      return
    }
    handleAllEventForChannel(channelData?.events)
    setTimeout(() => {
      switch (event?.keyCode) {
        case 38:
          setDay(1)
          setChannelCount(
            channelCount =>
              (channelCount - 1 + epgChannel.length) % epgChannel.length
          )
          setEpgChannel(epgSevenDaysData[1]?.channelResponse)
          break
        case 40:
          setDay(1)
          setChannelCount(
            channelCount => (channelCount + 1) % epgChannel.length
          )
          setEpgChannel(epgSevenDaysData[1]?.channelResponse)
          break
        case 33:
        case 85:
          let localChannelCountLG = channelCount + 1
          Array.from({ length: epgChannel?.length }).map(() => {
            if (
              getLockedChannelsIcon(
                epgChannel?.[localChannelCountLG]?.group_id
              ) ||
              getIsContractChanel(epgChannel?.[localChannelCountLG]?.group_id)
            ) {
              ++localChannelCountLG
            } else {
              return
            }
          })

          localChannelCountLG < epgChannel?.length &&
            setChannelCount(localChannelCountLG)
          break
        case 427:
          if (typeof tizen !== 'undefined') {
            tizen.tvinputdevice.registerKeyBatch(['ChannelUp', 'ChannelDown'])
            const codes = {
              chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
              chMinus: tizen.tvinputdevice.getKey('ChannelDown').code
            }
            let localChannelCount = channelCount + 1
            Array.from({ length: epgChannel?.length }).map(() => {
              if (
                getLockedChannelsIcon(
                  epgChannel?.[localChannelCount]?.group_id
                ) ||
                getIsContractChanel(epgChannel?.[localChannelCount]?.group_id)
              ) {
                ++localChannelCount
              } else {
                return
              }
            })
            codes.chPlus === event?.keyCode &&
              localChannelCount < epgChannel?.length &&
              setChannelCount(localChannelCount)
          }
          break
        case 34:
        case 68:
          let chCountLG = channelCount - 1
          Array.from({ length: epgChannel?.length }).map(() => {
            if (
              getLockedChannelsIcon(epgChannel?.[chCountLG]?.group_id) ||
              getIsContractChanel(epgChannel?.[chCountLG]?.group_id)
            ) {
              --chCountLG
            } else {
              return
            }
          })
          chCountLG >= 0 && setChannelCount(chCountLG)
          break
        case 428:
          if (typeof tizen !== 'undefined') {
            tizen.tvinputdevice.registerKeyBatch(['ChannelUp', 'ChannelDown'])
            const codes = {
              chPlus: tizen.tvinputdevice.getKey('ChannelUp').code,
              chMinus: tizen.tvinputdevice.getKey('ChannelDown').code
            }
            let chCount = channelCount - 1
            Array.from({ length: epgChannel?.length }).map(() => {
              if (
                getLockedChannelsIcon(epgChannel?.[chCount]?.group_id) ||
                getIsContractChanel(epgChannel?.[chCount]?.group_id)
              ) {
                --chCount
              } else {
                return
              }
            })
            codes.chMinus === event?.keyCode &&
              chCount >= 0 &&
              setChannelCount(chCount)
          }
          break
        case 37:
          if (day > 0 && index == 0) {
            setDayDirection('left')
            setDay(day => day - 1)
            setEpgChannel(epgSevenDaysData[day - 1]?.channelResponse)
          }
          break
        case 39:
          if (day < 6 && index == channelData?.events?.length - 1) {
            setDay(day => day + 1)
            setEpgChannel(epgSevenDaysData[day + 1]?.channelResponse)
          }
          break
      }
    }, 0)
    channelKeyDownRef.current = true
    channelTimeStoredRef.current = Date.now()
  }

  const channelColumn = useCallback(() => {
    return (
      <div
        id="new-mini-epg-channel-container"
        className="new-mini-epg-channel-container"
      >
        <span
          id="epg-top-chevron"
          className="epg-top-chevron"
          style={{ marginBottom: '16px' }}
        >
          <img
            id="epg-chevron-top-img"
            className="epg-chevron-top-img"
            src="images/LiveTv/ic_chevron_up.png"
          />
        </span>
        <div id="new-mini-epg-channel" className="new-mini-epg-channel">
          {lockedChannelsList &&
          getLockedChannelsIcon(epgChannel?.[channelCount]?.group_id) &&
          !watchFree ? (
            <div className="mini-epg-block-channel">
              <img
                className="mini-epg-block-icon-image"
                src={'images/lock_icon_miniEPG.png'}
              />
            </div>
          ) : null}

          <span
            className="new-mini-epg-channel-number"
            style={
              lockedChannelsList &&
              getLockedChannelsIcon(epgChannel?.[channelCount]?.group_id) &&
              !watchFree
                ? { opacity: '0.3' }
                : null
            }
          >
            {epgChannel?.[channelCount]?.number < 100
              ? (epgChannel?.[channelCount]?.number < 10 ? '00' : '0') +
                epgChannel?.[channelCount]?.number
              : epgChannel?.[channelCount]?.number}
          </span>
          <>
            {getFavouriteList &&
            handleFavourite(epgChannel?.[channelCount]?.group_id) ? (
              <div className="new-mini-epg-fav-channel">
                <img
                  src="images/Program_Details_Icons/favorites_icon.png"
                  style={{ width: '25px' }}
                />
              </div>
            ) : (
              <></>
            )}
          </>
          <span
            className="new-mini-epg-channel-image"
            style={
              lockedChannelsList &&
              getLockedChannelsIcon(epgChannel?.[channelCount]?.group_id)
                ? { opacity: '0.3' }
                : null
            }
          >
            {epgChannel?.[channelCount]?.image && !areAllEventsNotAvailable ? (
              <LazyLoadImage
                src={epgChannel?.[channelCount]?.image}
                width={150}
                height={150}
                className="channel-img"
                placeholderSrc="images/Placeholder_MiniEpg3.png"
              />
            ) : (
              <LazyLoadImage
                src="images/Placeholder_MiniEpg3.png"
                placeholderSrc="images/Placeholder_MiniEpg3.png"
                width={150}
                height={150}
                className="channel-img"
              />
            )}
          </span>
        </div>
        <span
          id="epg-bottom-chevron"
          className="epg-bottom-chevron"
          style={{ marginTop: '14px' }}
        >
          <img
            id="epg-chevron-down-img"
            className="epg-chevron-down-img"
            src="images/LiveTv/ic_chevron_down.png"
          />
        </span>
      </div>
    )
  }, [channelCount])

  const availableEventChecker = (data, index) => {
    data?.events?.[index]?.name === 'NA'
      ? setEventNotAvailable(true)
      : setEventNotAvailable(false)
  }

  const handleAllEventForChannel = data => {
    setAreAllEventsNotAvailable(data?.every(item => item?.name == 'NA'))
  }

  const ProgramColumn = useCallback(
    ({ index, style }) => {
      return (
        <div
          style={{ ...style, top: 10, left: index === 0 ? 5 : index * 700 }}
          key={index}
        >
          <div
            id={`mini-epg-program-${index}`}
            className="new-mini-epg-program focusable"
            onKeyDown={event => {
              channelHandler(event, epgChannel?.[channelCount], index)
            }}
            onKeyUp={event => {
              programHandler(event, epgChannel?.[channelCount], index, 'epg')
            }}
            onFocus={() => {
              availableEventChecker(epgChannel?.[channelCount], index)
            }}
            tabIndex={2}
          >
            <div className="new-mini-epg-program-image">
              {epgChannel?.[channelCount]?.events?.[index]
                ?.ext_eventimage_name &&
              epgChannel?.[channelCount]?.events?.[index]?.name !== 'NA' ? (
                <LazyLoadImage
                  src={
                    epgChannel[channelCount].events[index].ext_eventimage_name
                  }
                  alt="Program Image"
                  width={214}
                  height={120}
                  placeholderSrc="images/Placeholder_MiniEpg.png"
                />
              ) : epgChannel?.[channelCount]?.image &&
                epgChannel?.[channelCount]?.events?.[index]?.name !== 'NA' ? (
                <LazyLoadImage
                  src={epgChannel[channelCount].image}
                  alt="Channel Image"
                  width={214}
                  height={120}
                  placeholderSrc="images/Placeholder_MiniEpg.png"
                />
              ) : (
                <LazyLoadImage
                  src="images/Epg_error_placeholder.png"
                  alt="Placeholder Image"
                  width={214}
                  height={120}
                  placeholderSrc="images/Epg_error_placeholder.png"
                />
              )}
            </div>

            <div>
              {isRecordAvailable(
                epgChannel?.[channelCount]?.events?.[index],
                epgChannel?.[channelCount]
              ) &&
                epgChannel?.[channelCount]?.events?.[index]?.name !== 'NA' && (
                  <div className="new-mini-epg-program-recording">
                    <span className="new-mini-epg-record-text">
                      {getRecordText(
                        epgChannel?.[channelCount]?.events?.[index]
                      )}
                    </span>
                    <span className="new-mini-epg-program-record-img">
                      <img
                        src={getRecordIcon(
                          epgChannel?.[channelCount]?.events?.[index]
                        )}
                        width={38.15}
                        height={38.15}
                      />
                    </span>
                  </div>
                )}
            </div>

            <div className="new-mini-epg-program-details">
              <span className="new-mini-epg-program-title">
                {epgChannel?.[channelCount]?.events?.[index]?.name?.length < 20
                  ? epgChannel?.[channelCount]?.events?.[index]?.name === 'NA'
                    ? 'No disponible'
                    : epgChannel?.[channelCount]?.events?.[index]?.name
                  : `${epgChannel?.[channelCount]?.events?.[index]?.name?.slice(
                      0,
                      20
                    )}...`}
              </span>
              <span className="new-mini-epg-duration">
                {epgChannel?.[channelCount]?.events?.[index]?.name != 'NA' && (
                  <>
                    {day != 1 && (
                      <span className="new-mini-epg-date">
                        {epgChannel?.[channelCount]?.events?.[index]?.date_begin
                          ?.split('/')[2]
                          .slice(0, 2) +
                          '/' +
                          epgChannel?.[channelCount]?.events?.[
                            index
                          ]?.date_begin?.split('/')[1]}
                      </span>
                    )}
                    <span className="new-mini-epg-timing">
                      {epgChannel?.[channelCount]?.events?.[
                        index
                      ]?.date_begin?.slice(11, 16) +
                        ' - ' +
                        epgChannel?.[channelCount]?.events?.[
                          index
                        ]?.date_end?.slice(11, 16)}
                    </span>{' '}
                  </>
                )}
                {epgChannel?.[channelCount]?.events?.[index]?.name !== 'NA' && (
                  <span
                    className="new-mini-epg-label"
                    style={
                      epgChannel?.[channelCount]?.events?.[index]?.name == 'NA'
                        ? { left: '0px' }
                        : null
                    }
                  >
                    <img
                      src={getEventTags(
                        epgChannel?.[channelCount]?.events?.[index],
                        'tag'
                      )}
                      width={
                        getEventTags(
                          epgChannel?.[channelCount]?.events?.[index]
                        ) == 'Present Event'
                          ? 98
                          : getEventTags(
                              epgChannel?.[channelCount]?.events?.[index]
                            ) == 'Past Event'
                          ? 146
                          : 147
                      }
                      height={32}
                    />
                  </span>
                )}
                {isReminderAvailable(
                  epgChannel?.[channelCount]?.events?.[index]
                ) &&
                  epgChannel?.[channelCount]?.events?.[index]?.name !==
                    'NA' && (
                    <span className="new-mini-epg-reminder-img">
                      <img src={'images/reminder_icon_small.png'} width={30} />
                    </span>
                  )}
              </span>
              {/* Mini Epg progress bar for program or events*/}
              {getEventTags(epgChannel?.[channelCount]?.events?.[index]) ==
                'Present Event' && (
                <div id="progress-bar" className="mini-epg-progress-bar">
                  <div
                    id="progress"
                    className="mini-epg-progress"
                    style={{
                      width: `${Math.min(
                        ((moment().unix() -
                          moment(
                            epgChannel?.[channelCount]?.events?.[index]
                              ?.date_begin,
                            'YYYY/MM/DD HH:mm:ss'
                          ).unix()) /
                          (moment(
                            epgChannel?.[channelCount]?.events?.[index]
                              ?.date_end,
                            'YYYY/MM/DD HH:mm:ss'
                          ).unix() -
                            moment(
                              epgChannel?.[channelCount]?.events?.[index]
                                ?.date_begin,
                              'YYYY/MM/DD HH:mm:ss'
                            ).unix())) *
                          100,
                        100
                      )}%`
                    }}
                  ></div>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    [channelCount, epgChannel]
  )

  return (
    <>
      {showOptionsPanel ? (
        <ProgramDetails
          hideLanguage={setShowOptionsPanel}
          audioData={audioData}
          subscriptions={props?.subscriptions}
        />
      ) : epgChannel ? (
        <>
          <div
            id="new-mini-epg-container"
            className="new-mini-epg-container"
            tabIndex={1}
          >
            <span id="epg-left-chevron" className="epg-left-chevron">
              <img
                id="epg-chevron-left-img"
                className="epg-chevron-left-img"
                src="images/LiveTv/ic_chevron_left.png"
              />
            </span>
            <List
              className="new-mini-epg-channel-container"
              height={230}
              itemSize={700}
              itemCount={epgChannel?.length}
              width={350}
              layout="horizontal"
              overscanCount={epgChannel?.length / 2}
            >
              {channelColumn}
            </List>
            <div
              id="new-mini-epg-program-container"
              className="new-mini-epg-program-container"
            >
              <List
                className="mini-epg-nav"
                height={170}
                itemSize={700}
                itemCount={epgChannel?.[channelCount]?.events?.length}
                width={1400}
                layout="horizontal"
                overscanCount={epgChannel?.[channelCount]?.events?.length}
              >
                {ProgramColumn}
              </List>
            </div>
            <span id="epg-right-chevron" className="epg-right-chevron">
              <img
                id="epg-chevron-right-img"
                className="epg-chevron-right-img"
                src="images/LiveTv/ic_chevron_right.png"
              />
            </span>
          </div>
        </>
      ) : (
        <MiniEpgSkeleton />
      )}
      {!showOptionsPanel && (
        <MenuBar
          miniEpg={true}
          isSelectedChannelTS={
            !getLockedChannelsIcon(epgChannel?.[channelCount]?.group_id)
          }
          channelErr={
            channelErr &&
            !getLockedChannelsIcon(epgChannel?.[channelCount]?.group_id)
          }
          eventNotAvailable={eventNotAvailable}
          lockedScreen={lockedScreen}
        />
      )}
    </>
  )
}

export default React.memo(MiniEpg)
