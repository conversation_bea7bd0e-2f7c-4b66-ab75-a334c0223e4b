// Add this before your imports
jest.mock('npaw-plugin-es5', () => {
    return {
      __esModule: true,
      default: jest.fn().mockImplementation(() => ({
        initialize: jest.fn(),
        // Add any other methods from NpawPlugin that your code uses
      }))
    };
  });


import React from 'react'
import { fireEvent, queryByAttribute, render } from '@testing-library/react/'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import { BrowserRouter as Router } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import LandingPage from './landing'
import { fromJS } from 'immutable'

const initialState = fromJS({})
const mockStore = configureStore([])
const history = createHistory()
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
)
export const renderWithState = ui => {
    return render(ui, { wrapper: Wrapper })
}
describe('landing page test', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('it should render the landing Page', () => {
        renderWithState(<LandingPage />)
    })
    test('it should render the landing Page onclick signin', () => {
        const { container } = renderWithState(<LandingPage />)
        const getById = queryByAttribute.bind(null, 'id')
        const scroll = getById(container, 'signinId')
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/signin')
    })
    test('it should render the landing Page onclick register', () => {
        const { container } = renderWithState(<LandingPage />)
        const getById = queryByAttribute.bind(null, 'id')
        const scroll = getById(container, 'registerId')
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/register')
    })
    test('it should render the landing Page onclick watchfree button', () => {
        const { container } = renderWithState(<LandingPage />)
        const getById = queryByAttribute.bind(null, 'id')
        const scroll = getById(container, 'watchid')
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/welcome')
    })
})