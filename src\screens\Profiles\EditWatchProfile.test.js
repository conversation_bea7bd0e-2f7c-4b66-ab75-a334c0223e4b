import React from "react";
import {
  fireEvent,
  queryByAttribute,
  render,
  screen,
  waitFor,
} from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import EditWatchProfile from "./EditWatchProfile";
import '@testing-library/jest-dom';


const mockStore = configureStore([]);
const history = createHistory();

const getMockStore = (stateOverrides = {}) => {
  return mockStore({
    profile: {
      profileData: {
        response: {
          data: {
            totalMembers: 2,
            members: [
              {
                gamification_id: "6579a68439b44705076d056b",
                username: "Test User",
                user_image: "http://test.com/image.jpg",
              },
            ],
          },
        },
      },
    },
    watchList: {
      getEditImageFocus: { index: 0 },
    },
    initialReducer: {
      appMetaData: {
        translations: JSON.stringify({
          language: {
            region: {
              atv_back_notification: "Back",
              Perfiles_SeleccionarPerfil_Título_TextoTitulo: "Select Profile",
              Perfiles_EditarPerfil_TextoBotonPrimario: "Save",
              addProfile_access_title_label: "Add Profile",
            },
          },
        }),
      },
    },
    ...stateOverrides,
  });
};

const Wrapper = ({ children, reduxStore }) => (
  <Provider store={reduxStore}>
    <MemoryRouter
      history={history}
      initialEntries={[
        {
          state: {
            data: "Profile Updated",
            id: "6579a68439b44705076d056b",
            dafaultfocus: true,
            addIndex: true,
          },
        },
      ]}
    >
      {children}
    </MemoryRouter>
  </Provider>
);

const renderWithState = (ui, storeOverrides = {}) => {
  return render(ui, {
    wrapper: ({ children }) => (
      <Wrapper reduxStore={getMockStore(storeOverrides)}>
        {children}
      </Wrapper>
    ),
  });
};

describe("Edit Watch Profile test", () => {
  global.SpatialNavigation = {
    focus: jest.fn(),
    init: jest.fn(),
    add: jest.fn(),
    makeFocusable: jest.fn(),
  };

  test("should render api mock data", () => {
    renderWithState(<EditWatchProfile />);
  });

  test("should do button click", () => {
    const { container } = renderWithState(<EditWatchProfile />);
    const getById = queryByAttribute.bind(null, "id");
    const scroll = getById(container, "ListoEditButton_id");
    fireEvent.keyUp(scroll, { keyCode: 405 });
    fireEvent(
      scroll,
      new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
      })
    );
  });

  test("handles Samsung TV green key", () => {
    const props = {
      onChange: jest.fn(),
      type: "EditProfile",
      setCurrentButtonFocus: jest.fn(),
    };
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 10009 }),
      },
    };
    const { container } = renderWithState(<EditWatchProfile {...props} />);
    const getById = queryByAttribute.bind(null, "id");
    const scroll = getById(container, "backButtonId");
    fireEvent.keyUp(scroll, { keyCode: 10009 });
    fireEvent(
      scroll,
      new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
      })
    );
    delete global.tizen;
  });

  test("should dispatch handleEdit correctly", async () => {
    const { container } = renderWithState(<EditWatchProfile />);
    const profileBtn = container.querySelector(".userProfileImage");
    expect(profileBtn).toBeInTheDocument();
    fireEvent.click(profileBtn);
  });

  test("should focus add profile button on mount", async () => {
    const { container } = renderWithState(<EditWatchProfile />);
    const addProfileBtn = container.querySelector("#addProfile");
    expect(addProfileBtn).toBeInTheDocument();
    expect(document.activeElement).toBe(addProfileBtn);
  });


  test("displays notification on profile update", async () => {
    jest.useFakeTimers();
    renderWithState(<EditWatchProfile />);

    expect(screen.getByText("Perfiles_SeleccionarPerfil_Título_TextoTitulo")).toBeInTheDocument();

    await waitFor(() => {
      jest.advanceTimersByTime(4000);
    }, { timeout: 5000 });  // Increase the timeout duration (default is 1000ms)

    jest.useRealTimers();
  });

});
