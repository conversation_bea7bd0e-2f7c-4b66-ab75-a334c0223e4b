import initialSlice, {
  getStartHeaderInfo,
  getStartHeaderInfoSuccess,
  getStartHeaderInfoError,
  getAppMetaData,
  getAppMetaDataSuccess,
  getAppMetaDataError,
  getMetaDataHelp,
  getMetaDataHelpSuccess,
  getMetaDataHelpError,
  getAppMetaDataVideoSuccess,
  getAppMetaDataVideoError,
  getIpDetails,
  getIpDetailsSuccess,
  getIpDetailsError
} from './initialSlices';

describe('initialSlice reducer', () => {
  const initialState = {
    startHeaderInfo: {},
    appMetaData: {},
    metaDataHelp: {},
    isLoading: false,
    error: {},
    appMetaDataVideo: {}
  };

  it('should return the initial state', () => {
    expect(initialSlice(undefined, {})).toEqual(initialState);
  });

  it('should handle getStartHeaderInfo', () => {
    const action = { type: getStartHeaderInfo.type };
    const expectedState = { ...initialState, isLoading: true };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getStartHeaderInfoSuccess', () => {
    const payload = { header: 'info' };
    const action = { type: getStartHeaderInfoSuccess.type, payload };
    const expectedState = { ...initialState, startHeaderInfo: payload, isLoading: false };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getStartHeaderInfoError', () => {
    const payload = 'Error message';
    const action = { type: getStartHeaderInfoError.type, payload };
    const expectedState = { ...initialState, isLoading: false, error: payload };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getAppMetaData', () => {
    const action = { type: getAppMetaData.type };
    const expectedState = { ...initialState, isLoading: true };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getAppMetaDataSuccess', () => {
    const payload = { metaData: 'data' };
    const action = { type: getAppMetaDataSuccess.type, payload };
    const expectedState = { ...initialState, appMetaData: payload, isLoading: false };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getAppMetaDataError', () => {
    const payload = 'Error message';
    const action = { type: getAppMetaDataError.type, payload };
    const expectedState = { ...initialState, isLoading: false, error: payload };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getMetaDataHelp', () => {
    const action = { type: getMetaDataHelp.type };
    const expectedState = { ...initialState, isLoading: true };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getMetaDataHelpSuccess', () => {
    const payload = { help: 'data' };
    const action = { type: getMetaDataHelpSuccess.type, payload };
    const expectedState = { ...initialState, metaDataHelp: payload, isLoading: false };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getMetaDataHelpError', () => {
    const payload = 'Error message';
    const action = { type: getMetaDataHelpError.type, payload };
    const expectedState = { ...initialState, isLoading: false, error: payload };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getAppMetaDataVideoSuccess', () => {
    const payload = { video: 'data' };
    const action = { type: getAppMetaDataVideoSuccess.type, payload };
    const expectedState = { ...initialState, appMetaDataVideo: payload, isLoading: false };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getAppMetaDataVideoError', () => {
    const payload = 'Video error message';
    const action = { type: getAppMetaDataVideoError.type, payload };
    const expectedState = { ...initialState, isLoading: false, error: payload };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getIpDetails', () => {
    const action = { type: getIpDetails.type };
    const expectedState = { ...initialState, isLoading: true };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getIpDetailsSuccess', () => {
    const payload = { ip: '***********', location: 'US' };
    const action = { type: getIpDetailsSuccess.type, payload };
    const expectedState = { ...initialState, ipDetails: payload, isLoading: false };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });

  it('should handle getIpDetailsError', () => {
    const payload = 'IP error message';
    const action = { type: getIpDetailsError.type, payload };
    const expectedState = { ...initialState, isLoading: false, error: payload };
    expect(initialSlice(initialState, action)).toEqual(expectedState);
  });
});