import React, { useEffect, useState } from 'react'

const ringStyles = {
  position: 'fixed',
  pointerEvents: 'none',
  border: '4px solid #fff',
  borderRadius: '8px',
  zIndex: 99,
  transition:
    'top 0.3s ease-in-out, left 0.3s ease-in-out, width 0.3s ease-in-out, height 0.3s ease-in-out, opacity 0.15s ease-in-out, scale 0.15s ease-in-out',
  
}

const circleStyles = {
  position: 'fixed',
  pointerEvents: 'none',
  border: '4px solid #fff',
  borderRadius: '50%',
  zIndex: 99,
}

const FocusRing = () => {
  const [activeElement, setActiveElement] = useState(document.body)

  const focusInListener = event => {
    const newElement = event.target
    if (newElement !== activeElement) {
      setActiveElement(newElement)
    }
  }

  const focusOutListener = event => {
    const newElement = event.relatedTarget
    if (!newElement) {
      // setActiveElement(document.body)
    }
  }

  useEffect(() => {
    document.body.addEventListener('focusin', focusInListener, true)
    document.body.addEventListener('focusout', focusOutListener, true)

    return () => {
      document.body.removeEventListener('focusin', focusInListener, true)
      document.body.removeEventListener('focusout', focusOutListener, true)
    }
  }, [activeElement])

  const updateRing = () => {
    const newBox = activeElement.getBoundingClientRect()
    const id = activeElement?.id;
    let opacity = activeElement === document.body ? 0 : 1
    const updatedCircle = {
      ...circleStyles,
      top: `${newBox.top - 4}px`,
      bottom: `${newBox.bottom - 5}px`,
      right: `${newBox.right - 5}px`,
      left: `${newBox.left - 5}px`,
      width: `${newBox.width}px`,
      height: `${newBox.height}px`,
    }
    if (id.includes('nav-')) {
      return
    } else if (id.includes('toon-')) {
      return updatedCircle
    } else if (id.includes('serachId')) {
      return
    } else if(id.includes('profileimg')){
      return
    }
    const updatedStyles = {
      ...ringStyles,
      top: `${newBox.top -4}px`,
      bottom: `${newBox.bottom-6}px`,
      right: `${newBox.right -7}px`,
      left: `${newBox.left -5}px`,
      width: `${newBox.width}px`,
      height: `${newBox.height}px`,
      opacity
    }
    return updatedStyles
  }
  return (
    <div
      id="focus-ring"
      className={`init-hidden ${activeElement === document.body ? 'hidden' : 'visible'
        }`}
      style={updateRing()}
    />
  )
}

export default FocusRing
