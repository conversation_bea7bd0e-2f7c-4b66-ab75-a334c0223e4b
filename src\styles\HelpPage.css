.button-content{
	font: normal normal normal 30px/48px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin-left: 9%;
	text-transform: uppercase;
}

.main-title{
	font: normal normal normal 48px/57px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	display:flex;
	justify-content: center;
	margin-top: 2%;
}

.main-div-contents{
	display: flex;
	margin-top: 2%;
}

.left-div{
	display: flex;
	flex-direction: column;
	/* gap: 54px; */
	margin-left: 4%;

}

.sub{
	font: normal normal normal 36px/42px Roboto;
	letter-spacing: 0px;
	color: #A3A3A3;
	opacity: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 1%;
}

.div1{
	padding-top: 8%;
}

.div2{
	margin-top: 2%;
	padding-bottom: 8%;
}

.steps{
	font: normal normal normal 36px/42px Roboto;
	letter-spacing: 0px;
	color: #A3A3A3;
	opacity: 1;
	width: 102px;
	height: 42px;
}

.text{
	text-align: left;
	font: normal normal normal 30px/30px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	display: flex;
	margin-top: 3%;
}

.claroUrl{
	font: normal normal normal 36px/50px Roboto;
	letter-spacing: 0px;
	color: #FFFFFF;
	opacity: 1;
	display: flex;
	margin-top: 2%;
	margin-bottom: 5%;
}

.partition{
	width: 0px;
	height: 620px;
	border: 1px solid #707070;
	opacity: 1;
	position: absolute;
	margin-left: 6%;
	margin-left: 10%;
}

.right{
	display: flex;
	flex-direction: column;
	margin-left: 17%;
}

.claro{
	width: 528px;
	height: 65px;
	font: normal normal normal 36px/50px Roboto;
	text-align: center;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	margin-top: 20%;
}

.contact{
	/* width: 528px; */
	text-align: center;
	letter-spacing: 0px;
	font: normal normal normal 30px/30px Roboto;
	color: #EEEEEE;
	opacity: 1;
	margin-top: 12%;
	margin-right: 12%;
}

/* .contacts{
	width: 528px;
	text-align: center;
	letter-spacing: 0px;
	font: normal normal normal 30px/30px Roboto;
	color: #EEEEEE;
	opacity: 1;
	margin-top: 10px;
} */