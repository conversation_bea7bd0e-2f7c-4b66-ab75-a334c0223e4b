
$primaryColor: "#fff";

.purchaseHoursContainer {
  .backContainer {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    width: 100%;
    margin-top: 30px;

    .purchaseblock {
      color: #fff;
    }
    .backBtnhour {
      display: flex;
      // position: absolute;
      right: 87px;
      top: 130px;
      justify-content: space-between;
      align-items: center;
      width: 292px;
      height: 42px;
      padding: 13px;
      background-color: #2e303d;
      margin-right: 27px;
    }

    .backBtnhour:focus {
      border: 2px solid #fff;
    }
    .backTexthour {
      color: #fff;
      font-size: 29px;
      line-height: 29px;
    }
  }

  .purchaseHoursMainBlock {
    display: flex;
    justify-content: flex-start;
    align-items: stretch;
    .purchaseHoursCenter {
      display: flex;
      flex-direction: column;
      width: 100%;
      justify-content: center;
      align-items: center;
      .miniTitle {
        color: #969696;
        font-size: 30px;
        line-height: 32px;
        margin-bottom: 15px;
        text-align: center;
      }
      .Title {
        font-size: 40px;
        font-family: Roboto-Regular, Roboto, sans-serif;
        margin-bottom: 28px;
      }
      .purchaseCardList {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .purchaseCardItem {
          position: relative;
          .card {
            width: 248px;
            height: 591px;
            margin-right: 32px;
          }
          .purchaseContent {
            position: absolute;
            bottom: 0px;
            left: 0px;
            right: 0px;
            .contentBlock {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              .hoursImg {
                width: 248px;
                height: 65px;
              }
              .rupeeBlock {
                display: flex;
                margin-bottom: 16px;
                flex-direction: column;
                .rupeeLine {
                  display: flex;
                  flex-direction: row;
                  .rupee {
                    font-size: 35px;
                    color: #eeeeee;
                    font-family: Roboto-Bold;
                  }
                  .next {
                    color: #eeeeee;
                    font-size: 18px;
                    font-family: Roboto-Regular;
                  }
                }
                .subtxt {
                  color: #eeeeee;
                  font-size: 18px;
                  font-family: Roboto-Regular;
                }
              }
              .description {
                font-size: 21px;
                color: #eeeeee;
                height: 72px;
                margin-bottom: 16px;
                padding: 33px;
              }
              .hoursBtn {
                width: 200px;
                height: 40px;
                font-size: 16px;
                background-color: #981c15;
                color: #ffffff;
                border-radius: 4px;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
}
