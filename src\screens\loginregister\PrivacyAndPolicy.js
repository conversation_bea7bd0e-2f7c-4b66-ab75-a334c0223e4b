import React, { useEffect, useCallback } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import '../loginregister/PrivacyAndPolicy.scss'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const PrivacyAndPolicy = () => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const apaMetaDataTnC = useSelector(
    state => state?.initialReducer?.metaDataHelp
  )
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const translationsTnC =
    apaMetaDataTnC?.translations && JSON?.parse(apaMetaDataTnC?.translations)

  const handleScrollUp = () => {
    const element = document.getElementById('data-div')
    element.scrollTop += -300
    element.scrollTop == 0 && document.getElementById("privacyPolicy").focus()
  }

  const handleScrollDown = () => {
    const element = document.getElementById('data-div')
    element.scrollTop += 300
    element.scrollTop >= 300 && document.getElementById("privacyPolicy").blur()
  }

  const handlesamsungkey = useCallback((key, keycode) => {
      if (key.yellowcode === keycode || keycode === 10009) {
        navigate('/login-Terms-and-Conditons', { state: { backfocus: state?.page, checkbox: state?.checkbox } })
      }
    },
    [navigate]
  )

  const handleLgkey = useCallback(keycode => {
      if (keycode === 405 || keycode === 461 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
) {
        navigate('/login-Terms-and-Conditons', { state: { backfocus: state?.page, checkbox: state?.checkbox } })
      }
    },
    [navigate]
  )

  const keypresshandler = useCallback(
    event => {
      const keycode = event.keyCode
      if (keycode === 38) {
        handleScrollUp()
      } else if (keycode === 40) {
        handleScrollDown()
      } else if (typeof tizen !== 'undefined') {
        tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
        const codes = {
          yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
        }
        handlesamsungkey(codes, keycode)
      } else {
        handleLgkey(keycode)
      }
    },
    [handlesamsungkey, handleLgkey]
  )

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const termsAndConditionsText =
    translationsTnC?.language?.[region]?.pp_full_text || ''
  return (
    <div className="privacy-policy">
      <div>
        <img
          src={'images/claro-video-logo.png'}
          className="claro-logo"
          alt="logo"
        />
        <button
          className="back-indicator focusable"
          id="privacyPolicy"
          onClick={() => handleLgkey(405)}>
          <img
            className="yellow-indicator"
            src={'images/yellow_shortcut.png'}
            alt="Yellow shortcut"
          />
          <img
            className="back-image"
            src={'images/back_button.png'}
            alt="Back button"
          />
          <p className="back-text">
            {translations?.language?.[region]?.atv_back_notification || 'Back'}
          </p>
        </button>
      </div>
      <div className="page-div">
        <div className="data-class" id="data-div">
          <div id="tyc_full_text_id">
            <SafeHTML html={termsAndConditionsText} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PrivacyAndPolicy