.title{
	font: normal normal normal 48px/57px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	display:flex;
	margin-top: 2%;
	justify-content: center;
}
.subTitle{
	text-align: left;
	font: normal normal normal 30px/30px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	display: flex;
	justify-content: center;
	margin-top: 7%;
}
.transaction-div-title{
	display: flex;
	flex-direction: column;
}
.filterTitle{
	font: normal normal normal 36px/42px Roboto;
	letter-spacing: 0px;
	color: #ADADAD;
	opacity: 1;
	display: flex;
	justify-content: center;
	margin-bottom: 3%;
	text-transform: uppercase;
}
.filterTitle:focus{
	color: #981C15
}
.t2:focus{	
	background: #981C15 0% 0% no-repeat padding-box;
	transform: scale(1.05);
}
.add-payments{
	display: flex;
	justify-content: center;
}
.addPaymentsMethodButton{
	width: 463px;
	height: 73px;
	background: #2E303D 0% 0% no-repeat padding-box;
	border-radius: 44px;
	opacity: 1;
	margin-top: 10%;
}
.addPaymentsMethodButton:focus{
	background: #981C15 0% 0% no-repeat padding-box;
}
.buttonContents{
	font: normal normal normal 34px/40px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	display: flex;
	justify-content: center;
}
.paymentsDiv{
	display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
	margin-top: 5%;
}
.payment-buttons {
	width: 264px;
    height: 264px;
	background: #2E303D  0% 0% no-repeat padding-box;
	opacity: 1;
	margin-top: 2%;
	margin: 1%;
	font: normal normal normal 34px/40px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}
.payment-buttons:focus{
	background: #981C15 0% 0% no-repeat padding-box;
	border:3px solid white;
	transform: scale(1.10);
	
}
.subText{
	font: normal normal normal 28px/33px Roboto;
	letter-spacing: 0px;
	color: #A4A9AE;
	opacity: 1;
	margin-top: 5%;
	margin-left: 31%;
}
.inputBox{
	width: 716px;
	height: 88px;
	background: #000000 0% 0% no-repeat padding-box;
	border: 3px solid #34353B;
	border-radius: 44px;
	opacity: 1;
	font: normal normal normal 30px/35px Roboto;
	letter-spacing: 0px;
	color: #A4A9AE;
	margin-left: 31%;
	margin-top: 1%;
}
.inputBox:focus{
	border:3px solid #981C15
}
.resendOTPButton{
	width: 215px;
	height: 69px;
	background: #34353B 0% 0% no-repeat padding-box;
	border-radius: 5px;
	opacity: 1;
	color: #EEEEEE;
	margin-left: 1%;
	margin-bottom: 2%;
}
.resendOTPSpan{
	width: 164px;
	height: 35px;
	text-align: center;
	font: normal normal normal 30px/30px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	padding: 11%;
}
.bottom-button{
	display: flex;
	justify-content: center;
	margin-top: 10%;
}
.nextButton{
	width: 360px;
	height: 72px;
	background: #2E303D 0% 0% no-repeat padding-box;
	border-radius: 44px;
	display: flex;
	justify-content: center;
	align-items: center;
	opacity: 1;
	font: normal normal normal 34px/40px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
}
.nextButton:focus {
	background: #981C15 0% 0% no-repeat padding-box;
}
.transaction-info{
	text-align: left;
	font: normal normal normal 30px/30px Roboto;
	letter-spacing: 0px;
	color: #ADADAD;
	opacity: 1;
	display: flex;
	justify-content: center;
}
.gradients{
	width: 1562px;
	border: 1px solid #FFFFFF33;
	opacity: 1;
	display: flex;
	margin-bottom: 1%;
	margin-top: -1%;
}
.tableData{
	display: flex;
	justify-content: center;
}
.table-body{
	display: flex;
	flex-direction: column;
	overflow-x: scroll;
	height: 54vh;
}
.table-body::-webkit-scrollbar {
  width: 0.5em; /* Adjust the width as needed */
  background-color: transparent; /* Set the background color of the scrollbar track */
	display: flex;
}
.table-body::-webkit-scrollbar-thumb {
  background-color: #515151; /* Set the color of the scrollbar thumb */
	height: 405px;
}
.th, tr{
	display: flex;
	/* column-gap: 5%; */
	margin-left: 3%;
	margin-bottom: 3%;
	font: normal normal normal 30px/30px Roboto;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
}
.buttons-div{
	margin-top: 6%;
	display: flex;
	flex-direction: column;
	margin-left: 5%;
	margin-right: 5%;
}
.view-payments-div{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 15%;
	gap:40px;
}
.view-screen-text{
	text-align: left;
	letter-spacing: 0px;
	color: #EEEEEE;
	opacity: 1;
	font: normal normal normal 25px/30px Roboto;
}
.invalid-otp{
	width: 426px;
	height: 105px;
	background: #EEEEEE 0% 0% no-repeat padding-box;
	border-radius: 12px;
	font: normal normal normal 28px/36px Roboto;
	letter-spacing: -0.45px;
	color: #981C15;
	opacity: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	left: 100px;
	top:50%
}
.invalid-otp:after,
.invalid-otp:before {
    left: 424px;
    border: solid transparent;
    content: " ";
    width: 0;
    position: absolute;
    pointer-events: none;
}
.invalid-otp:after {
    border-width: 10px 10px 10px 20px;
    border-left: 27px solid #EEEEEE;
}
.invalid-promoCode{
	width: 426px;
	height: 100px;
	background: #EEEEEE 0% 0% no-repeat padding-box;
	border-radius: 12px;
	font: normal normal normal 22px/36px Roboto;
	letter-spacing: -0.45px;
	color: #981C15;
	opacity: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 36vh;
    right: 30vh;
}
.invalid-promoCode:after,
.invalid-promoCode:before {
	border: solid transparent;
    content: " ";
    width: 0;
    position: absolute;
    pointer-events: none;
    top: -33px;
}
.invalid-promoCode:after {
	border-width: 10px 20px 10px 20px;
    border-bottom: 32px solid #EEEEEE;
}
.sub-text{
    margin-top: 4%;
}
.text1{
    color: #EEEEEE;
    font: normal normal normal 30px/50px Roboto;
    margin-left: 30%;
}
.text2{
    color: #EEEEEE;
    font: normal normal normal 30px/50px Roboto;
    margin-left: 24%;
}
.logoimg{
    margin-left: 44%;
    margin-top: 10%;
    height: 8%;
    width: 13%;
}
.sub1{
	margin-top: 3%;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}
.txt1{
	color: #ADADAD;
    font: normal normal normal 30px/42px Roboto;
}
.imglogo{
	margin-top: 2%;
	height: 70px;
    width: 170px;
}
.txt2{
font: normal normal normal 30px/42px Roboto;
color: #ADADAD;
margin-top: 1.5%;
}
.txt3{
	font: normal normal normal 30px/42px Roboto;
    color: #ADADAD;
    margin-top: 3%;
    text-align: center;
    width: 23%;
}
.txt4{
	font: normal normal normal 40px/42px Roboto;
    color: #EEEEEE;
    margin-top: 1%;
}
.sub2{
	display: flex;
    flex-direction: column;
    margin-top: 4%;
    margin-left: 35%;
}
.button1{
	width: 560px;
	height: 100px;
	background: #981C15 0% 0% no-repeat padding-box;
	font: normal normal bold 34px/40px Roboto;
	color: #FFFFFF;
	text-align: center;
	border-radius: 50px;
}
.button2{
	width: 560px;
    height: 100px;
    background: #2E303D 0% 0% no-repeat padding-box;
    border-radius: 50px;
    text-align: center;
    font: normal normal normal 34px/40px Roboto;
    letter-spacing: 0px;
    color: #EEEEEE;
    margin-top: 3%;
}
.ControlpanelNotificationText {
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    width: 500px !important;
    white-space:pre-wrap;
    word-break: break-word;
    margin: 13px 0px;
}
