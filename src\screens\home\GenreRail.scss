.genrerailcarddata {
    .FooterGenre {
        justify-content: center;
        align-items: center;
        display: flex;
        width: 100%;
        position: fixed;


        .FooterButtonGenre {
            margin-top: 16px;
            flex-direction: row;
            border-radius: 2px;
            display: flex;
            width: 203px;
            height: 48px;
            padding: 7px;
            background-color: #2E303D;
            justify-content: center;
            align-content: center;
            border: 0 !important;
        }

        .FooterButtonGenre:focus {
            background-color: #981C15;
            border: 4px solid #fff !important;
            margin-top: 13px;
            flex-direction: row;
            display: flex;
            width: 203px;
            height: 48px;
            padding: 7px;
            justify-content: center;
            align-content: center;
        }

        .FooterTextGenre {
            text-align: left;
            font: normal normal normal 28px/28px Roboto;
            letter-spacing: 0px;
            color: #FFFFFF;
            text-transform: uppercase;
            opacity: 1;
            margin-left: 25px;
            line-height: 29.36px;
            font-size: 30px;
            font-family: Roboto;
            margin-top: 10px;
        }
    }

    .FooterGenre:focus {
        border: 2px solid #fff;
    }

    .genrecarddataval {
        overflow-y: scroll;
        overflow-x: hidden;
        scroll-snap-type: y mandatory;
        height: 700px;
        overflow-x: hidden;
        margin-top: 100px;
        position: fixed;
        margin-left: 25px;

        .genrecard {
            margin: 13px 0px 36px 40px;
            height: 232px !important;
            width: 412px !important;
            border: 0 !important;
            opacity: 0.8;
            position: relative;
        }

        .genrecard:focus {
            border-radius: 10px;
            padding: 3px;
            margin-left: 31px;
            margin-right: -10px;
            z-index: 1;
            opacity: 1;
            height: 232px !important;
            border: 3px solid #fff !important;
            transform: scale(1.02);
            background: transparent 0% 0% no-repeat padding-box;
            scroll-snap-align: center;
        }

        .submenurail-image {
            height: 232px;
            width: 412px;
        }
    }

        .genre-content-title {
            color: #fff;
            font-size: 28px;
            position: relative;
            bottom: 56px;
            left: 10px;
            text-wrap: wrap;
            font-family: "Roboto";
        }
    
        .submenu-rail-alquilar {
            top: 0px;
            position: absolute;
    
            .submenu-tagAlq-img {
                width: 111px;
                height: 39px;
                position: relative;
            }
        }
    
        .submenu-verohara {
            position: absolute;
            top: 12px;
            left: 8px;
            transform: translateY(-20%);
            position: absolute;
        }
    
        .submenu-picardia {
            position: absolute;
            display: flex;
        }

.picardia-proveedorBlockRail-1 {
  position: relative;
  right: 153px;
  top: 30px;
  display: flex;
  .picardia-tag-1 {
    position: absolute;
    width: 161px;
    height: 38px;
    left: 121px;
    bottom: -9px;
  }
}
}



.SubfilterTab {
    margin-top: 34px;
    margin-left: 720px;
    width: 500px;
    height: 72px;
    background: #2E303D;
    opacity: 1;
    border-radius: 7px;
    font-weight: bold;
    font-family: 'Roboto';
    color: #FFFFFF;
    font-size: 36px;
    text-align: center;
}

.SubfilterTab:focus {
    background: #981C15 0% 0% no-repeat padding-box;
    margin-top: 34px;
    margin-left: 697px;
    width: 544px;
    height: 72px;
    opacity: 1;
    border-radius: 7px;
    font-weight: bold;
    font-family: "Roboto";
    color: #FFFFFF;
    font-size: 36px;
    text-align: center;
}

.SubfilterTab:active {
    margin-top: 34px;
    margin-left: 720px;
    width: 500px;
    height: 72px;
    background: #4B1512 0% 0% no-repeat padding-box;
    opacity: 1;
    border-radius: 7px;
    font-weight: bold;
    font-family: 'Roboto';
    color: #FFFFFF;
    font-size: 36px;
    text-align: center;
}

.SubfilterTab-active {
    margin-top: 34px;
    margin-left: 720px;
    width: 500px;
    height: 72px;
    background: #4B1512 0% 0% no-repeat padding-box;
    opacity: 1;
    border-radius: 7px;
    font-weight: bold;
    font-family: 'Roboto';
    color: #FFFFFF;
    font-size: 36px;
    text-align: center;
}

.submenufiltercontainer {
    width: 0px;
    margin-top: 213px;
}

.genrecarddata {
    overflow: scroll hidden;
    scroll-snap-type: x mandatory;
}

.hidetata {
    display: none;
}

.submenu-verohara{
    position: absolute;
}

.verahora-tag {
    height: 32px;
    width: 122px;
    border-radius: 6px;
    background-color: #68B75C;
    color: #FFFFFF;
    font-family: Roboto;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
}

.filter-regresser-box {
    position: absolute;
    right: 10px;
    top: 50px;

    .filter-backScreen {
        box-sizing: border-box;
        height: 62px;
        width: 306px;
        border-radius: 6px;
        background: #2e303d 0% 0% no-repeat padding-box;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 6px;
        margin-left: 96px;
        margin-right: 53px;

        &:focus,
        &:active {
            outline: 3px solid white;
            border-radius: 6px;
            background: #981c15 0% 0% no-repeat padding-box;
        }

        .filter-back-img-icon {
            margin-bottom: 17px;
            margin-left: 28px;
            margin-right: 3px;
            margin-top: 16px;
        }

        .filter-back-button-regresar-title {
            height: 33px;
            width: 208px;
            color: white;
            font-family: Roboto;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 30px;
            text-align: center;
            margin-bottom: 17px;
            margin-right: 3px;
            margin-top: 17px;
        }
    }
}
