import React, { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './ContinueWatching.scss'
import { store } from '../../../store/sagaStore'
import { getFavouriteLive } from '../../../store/slices/EpgSlice'
import { getChannelData } from '../../../store/slices/PlayerSlice'
import { useDispatch } from 'react-redux'
import { CURRENT_PLATFORM } from '../../../utils/devicePlatform'

const FavoriteList = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [content, setContent] = useState([])
  const [focusContent, setFocusContent] = useState(false)
  const [focusedId, setFocusedId] = useState(null)

  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const epgVersion = useSelector(state => state?.epg?.epgVersion)
  const railImage = useSelector(
    state => state?.epg?.favouriteLive?.response?.groups
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const liveChannelsData = useSelector(state => state?.epg?.epgChannel)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const lastTouch = localStorage.getItem('lasttouch')
  const backelement = document.getElementById(props?.backfocusid)
  const providersLabelConfiguration =
  apaMetaData?.providers_label_configuration &&
  JSON?.parse(apaMetaData?.providers_label_configuration)
  const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
  useEffect(() => {
    if (focusContent) {
      let myContents = document.getElementById(`favoriteListPage`)
      myContents?.focus()
      myContents?.scrollIntoView({
        block: 'center',
        inline: 'center',
        behavior: 'smooth'
      })
    }
  }, [focusContent])

  useEffect(() => {
    if (backelement) {
      setContent(railImage?.[0])
      backelement?.focus()
      backelement?.scrollIntoView({ block: "center", inline: 'center', behavior: 'smooth' });
    }
  }, [backelement,railImage])

  const goToMoviesSeries = (item,index) => {
    localStorage.setItem('subMenu', 1)
    dispatch(
      getChannelData({
        group_id: item?.id,
        timeshift: item?.timeshift,
        switchChannel: 'yes',
        epgIndex: liveChannelsData?.[1]?.channelResponse?.findIndex(
          itrObj => itrObj.group_id == item?.id
        )
      })
    ),
      navigate('/livePlayer', {
        state: { showControls: 'live', data: item, page: "record", backfocusid:`index${props?.index}${index}` }, //Added showControls flag which will enable live player after navigation
        replace: true
      })
  }

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }
  const handleFocus = data => {
    setContent(data)
    setFocusContent(true)
  }
  const handleBlur = data => {
    setFocusContent(false)
  }
  const handleTranslationchange = useCallback(keyname => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  useEffect(() => {
    store.dispatch(
      getFavouriteLive({
        epg_version: epgVersion,
        hks: userDetails?.session_stringvalue,
        user_id: userDetails?.user_id,
        user_token: userDetails?.user_token,
        lasttouch: lastTouch,
        user_hash: userDetails?.session_userhash,
      })
    )
  }, [userDetails, lastTouch])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (key.redcode == keycode) {
        navigate('/deletecard', {
          state: { deteleData: content, page: 'favourite', backfocusid: focusedId }
        })
      }
    }
  }

  const handleLgkey = keycode => {
    if (focusContent) {
      if (keycode == 403 || keycode == 46 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 118)
) {
        navigate('/deletecard', {
          state: { deteleData: content, page: 'favourite', backfocusid: focusedId }
        })
      }
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className='ContinueWatchingrailContainer'
    style={{height : railImage?.[0] ? 370 : 340, marginBottom: railImage?.[0] ? 40 : 10}}
     id="favoriteListPage">
      {railImage?.[0] ? (
        <div className="railTitle">
          {' '}
          {handleTranslationchange('atv_my_content_rail_favourite_channel')}
        </div>
      ) : (
        ''
      )}
      {
        <div className='ContinueWatchingsub'>
          {railImage?.[0] ? (
            <div className="continue-wrapper">
              {railImage?.map((each, index, array) => (
                <>
                  <button
                    className="rail_block focusable"
                    key={index}
                    onClick={() => goToMoviesSeries(each,index)}
                    onFocus={() => {
                      setFocusedId(`index${props?.index}${index}`)
                      handleFocus(each)
                    }}
                    onBlur={() => handleBlur(each)}
                    id={`index${props?.index}${index}`}
                    data-testid={`rail_card_click${index}`}
                    data-sn-right={index != array.length - 1 && undefined}
                    data-sn-left={index == 0 ? '' : undefined}
                    data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                    data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                  >
                    {each?.image_small &&
                      each?.image_small !== '' ? (
                      <>
                        <LazyLoadImage
                          src={each?.image_small}
                          placeholderSrc="images/landscape_card.png"
                          key={index}
                          className="rail-image"
                          id={`railfocus${index}`}
                        />

                        <div className="title-icon-block">
                          <div className="continuewatchingShow-title">
                            <div className="continuewatchingtitle">
                              {each?.title}
                            </div>
                          </div>
                          <div className="deleteIcons">
                            <img
                              src={'images/Home_icons/red.png'}
                              className="redDot"
                            />
                            <img
                              src={'images/Home_icons/delete.png'}
                              className="delete"
                            />
                          </div>
                        </div>
                      </>
                    ) : (
                      <LazyLoadImage
                        src="images/landscape_card.png"
                        loading="lazy"
                        alt="PlaceHolder"
                        className="rail-image"
                      />
                    )}

                    {/* tags */}

                    {each?.image_small && each?.proveedor_code == 'amco' ? (
                      each?.format_types === 'ppe,download' ? (
                        <div className="proveedorBlockRailAlq">
                          <img src={'images/Alquilar.svg'} className="tagAlq" />
                        </div>
                      ) : each?.format_types === 'ppe' ? (
                        <div className="proveedorBlockRailAlq">
                          <img src={'images/Alquilar.svg'} className="tagAlq" />
                        </div>
                      ) : null
                    ) : each?.image_small &&
                      each?.proveedor_code &&
                      each?.image_medium ? (
                      <div className="proveedorBlockRail_vero_hara">
                        {Addproveedor(providerLabel?.[each?.proveedor_code]?.susc) && (
                          <img
                            id="#icon1"
                            className={
                              each?.proveedor_code === 'picardia2' &&
                              'picardia-image'
                            }
                            src={Addproveedor(providerLabel?.[each?.proveedor_code]?.susc)}
                          />
                        )}
                        {each?.format_types === 'free' && userDetails?.subscriptions?.length == 0 ? (
                          <div className="verahora-tag">VER AHORA</div>
                        ) : null}
                        {each?.image_small &&
                          each?.proveedor_code === 'picardia2' &&
                          each?.image_medium && (
                            <div className="picardia-proveedorBlockRail">
                              <img
                                src={'images/Adultus.svg'}
                                className="picardia-tag"
                              />
                            </div>
                          )}
                      </div>
                    ) : null}
                  </button>
                </>
              ))}
            </div>
          ) : (
            localStorage.getItem('miscontenidos') && (
              <div>
                <div className="mycontent-railTitle">
                  {handleTranslationchange(
                    'atv_my_content_rail_favourite_channel'
                  )}
                </div>
                <div className="nocontent-card-main">
                    <button
                      className="nocontent-card focusable"
                      id={`index${props?.index}0`}
                      onFocus={handleFocus}
                      onBlur={handleBlur}
                      data-sn-right
                      data-sn-left
                      data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                      data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}

                    >
                      <p className='mycontent-rail-text'> {handleTranslationchange(
                        'ribbons_placeholder_MisCanalesFavoritos_label'
                      )}</p>
                      <div className='nocontent-sub-card'>
                        <img className="cardimage1 focusable" src={'images/favorite_placeholder.png'} />
                        <img className="cardimage2" src={'images/favorite_placeholder.png'} />
                        <img className="cardimage3" src={'images/mycontent_placeholder.png'} />
                      </div>
                    </button>
                </div>
              </div>
            )
          )}
        </div>
      }
    </div>
  )
}

export default FavoriteList
