import React from "react";
import { fireEvent, getByTestId, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import RailCards from "./RailCard";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const mockdata = {
    "groups": [
        {
            "id": "1114102",
            "title": "Manual de Supervivencia Escolar de Ned",
            "title_episode": "El primer día / Casilleros",
            "title_uri": "Manual-de-Supervivencia-Escolar-de-Ned",
            "title_original": "Ned's Declassified School Survival Guide",
            "description": "Ned nos ayuda a sobrevivir el primer día de escuela con consejos de su manual. / Ned tiene que lidiar con un vecino de casillero maloliente.",
            "description_large": "Ned nos ayuda a sobrevivir el primer día de escuela con consejos de su manual. Ned y Cookie descubren que están en diferentes clases que su amiga Moze y traman un plan para reunirse con ella. / Ned tiene que lidiar con un vecino de casillero maloliente. Mientras tanto, Moze adorna su casillero para atraer nuevas amigas. Y Cookie tiene problemas para llegar a la clase de gimnasia a tiempo debido a que su \"armario está en el final del universo\".",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn0.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/STILLS/XND101A-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/NEDSDECLASSIFIEDSCHOOLSURVIVALGUIDE-03-03-00/EXPORTACION_WEB/CLEAN/NEDSDECLASSIFIEDSCHOOLSURVIVALGUIDE-03-03-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:23:43",
            "date": "20230522181913",
            "year": "2004",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "0",
            "live_type": null,
            "live_ref": null,
            "source_uri": "",
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "G",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": true,
            "channel_number": null
        },
        {
            "id": "1110876",
            "title": "George & Tammy",
            "title_episode": "La Carrera Comenzó",
            "title_uri": "George-Tammy",
            "title_original": "George & Tammy",
            "description": "Tammy está encantada cuando la eligen para ser telonera de la estrella de la música country George Jones.",
            "description_large": "La prometedora cantante, Tammy Wynette, tiene una gran oportunidad cuando le piden que sea la telonera de su ídolo, la leyenda del country George Jones. Los dos tienen una conexión romántica inmediata y una innegable química musical, pero su posible pareja se enfrenta a un gran obstáculo: el marido de Tammy, Don Chapel.",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/STILLS/HDGNTM101A-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/GEORGETAMMY-01-01-00/EXPORTACION_WEB/CLEAN/GEORGETAMMY-01-01-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:50:02",
            "date": "25/02/2023",
            "year": "2022",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "0",
            "live_type": null,
            "live_ref": null,
            "source_uri": "",
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "R",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": true,
            "channel_number": null
        },
    ],
    "total": 4
}
const liverailcardmockdata = {
    "groups": [
        {
            "id": "1114102",
            "title": "Manual de Supervivencia Escolar de Ned",
            "title_episode": "El primer día / Casilleros",
            "title_uri": "Manual-de-Supervivencia-Escolar-de-Ned",
            "title_original": "Ned's Declassified School Survival Guide",
            "description": "Ned nos ayuda a sobrevivir el primer día de escuela con consejos de su manual. / Ned tiene que lidiar con un vecino de casillero maloliente.",
            "description_large": "Ned nos ayuda a sobrevivir el primer día de escuela con consejos de su manual. Ned y Cookie descubren que están en diferentes clases que su amiga Moze y traman un plan para reunirse con ella. / Ned tiene que lidiar con un vecino de casillero maloliente. Mientras tanto, Moze adorna su casillero para atraer nuevas amigas. Y Cookie tiene problemas para llegar a la clase de gimnasia a tiempo debido a que su \"armario está en el final del universo\".",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn0.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/STILLS/XND101A-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/NEDSDECLASSIFIEDSCHOOLSURVIVALGUIDE-03-03-00/EXPORTACION_WEB/CLEAN/NEDSDECLASSIFIEDSCHOOLSURVIVALGUIDE-03-03-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:23:43",
            "date": "20230522181913",
            "year": "2004",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "1",
            "live_type": null,
            "live_ref": null,
            "source_uri": "",
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "G",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": true,
            "channel_number": null
        },
        {
            "id": "1110876",
            "title": "George & Tammy",
            "title_episode": "La Carrera Comenzó",
            "title_uri": "George-Tammy",
            "title_original": "George & Tammy",
            "description": "Tammy está encantada cuando la eligen para ser telonera de la estrella de la música country George Jones.",
            "description_large": "La prometedora cantante, Tammy Wynette, tiene una gran oportunidad cuando le piden que sea la telonera de su ídolo, la leyenda del country George Jones. Los dos tienen una conexión romántica inmediata y una innegable química musical, pero su posible pareja se enfrenta a un gran obstáculo: el marido de Tammy, Don Chapel.",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/STILLS/HDGNTM101A-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/GEORGETAMMY-01-01-00/EXPORTACION_WEB/CLEAN/GEORGETAMMY-01-01-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:50:02",
            "date": "25/02/2023",
            "year": "2022",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "1",
            "live_type": null,
            "live_ref": null,
            "source_uri": "",
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "R",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": true,
            "channel_number": null
        },
    ],
    "total": 4
}
const moviesrailcardmockdata = {
    "groups": [
        {
            "id": "1114102",
            "title": "Manual de Supervivencia Escolar de Ned",
            "title_episode": "El primer día / Casilleros",
            "title_uri": "Manual-de-Supervivencia-Escolar-de-Ned",
            "title_original": "Ned's Declassified School Survival Guide",
            "description": "Ned nos ayuda a sobrevivir el primer día de escuela con consejos de su manual. / Ned tiene que lidiar con un vecino de casillero maloliente.",
            "description_large": "Ned nos ayuda a sobrevivir el primer día de escuela con consejos de su manual. Ned y Cookie descubren que están en diferentes clases que su amiga Moze y traman un plan para reunirse con ella. / Ned tiene que lidiar con un vecino de casillero maloliente. Mientras tanto, Moze adorna su casillero para atraer nuevas amigas. Y Cookie tiene problemas para llegar a la clase de gimnasia a tiempo debido a que su \"armario está en el final del universo\".",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn0.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/STILLS/XND101A-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/NEDSDECLASSIFIEDSCHOOLSURVIVALGUIDE-03-03-00/EXPORTACION_WEB/CLEAN/NEDSDECLASSIFIEDSCHOOLSURVIVALGUIDE-03-03-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/SS/13697WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/13697/EXPORTACION_WEB/CLEAN/13697WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn2.clarovideo.net/PARAMOUNT/PELICULAS/XND101A-01-01-01/EXPORTACION_WEB/SPRITES/XND101A-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:23:43",
            "date": "20230522181913",
            "year": "2004",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "0",
            "live_type": null,
            "live_ref": null,
            "source_uri": "",
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "G",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": false,
            "channel_number": null
        },
        {
            "id": "1110876",
            "title": "George & Tammy",
            "title_episode": "La Carrera Comenzó",
            "title_uri": "George-Tammy",
            "title_original": "George & Tammy",
            "description": "Tammy está encantada cuando la eligen para ser telonera de la estrella de la música country George Jones.",
            "description_large": "La prometedora cantante, Tammy Wynette, tiene una gran oportunidad cuando le piden que sea la telonera de su ídolo, la leyenda del country George Jones. Los dos tienen una conexión romántica inmediata y una innegable química musical, pero su posible pareja se enfrenta a un gran obstáculo: el marido de Tammy, Don Chapel.",
            "short_description": null,
            "image_large": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg?size=529x297",
            "image_medium": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WVERTICAL.jpg?size=200x300",
            "image_small": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg?size=290x163",
            "image_still": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/STILLS/HDGNTM101A-01-01-01-STILL-02.jpg",
            "image_background": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/GEORGETAMMY-01-01-00/EXPORTACION_WEB/CLEAN/GEORGETAMMY-01-01-00_e-1280x720.jpg",
            "url_imagen_t1": "https://clarovideocdn7.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WVERTICAL.jpg?size=200x300",
            "url_imagen_t2": "https://clarovideocdn9.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WHORIZONTAL.jpg?size=290x163",
            "image_base_horizontal": "https://clarovideocdn3.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WHORIZONTAL.jpg",
            "image_base_vertical": "https://clarovideocdn1.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/SS/66610WVERTICAL.jpg",
            "image_base_square": "",
            "image_clean_horizontal": "https://clarovideocdn6.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WHORIZONTAL.jpg",
            "image_clean_vertical": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/SERIES/66610/EXPORTACION_WEB/CLEAN/66610WVERTICAL.jpg",
            "image_clean_square": "",
            "image_sprites": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-SPRITEBAR.jpg",
            "image_frames": "https://clarovideocdn4.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-00h-00m-00s-00f.jpg",
            "image_trickplay": "https://clarovideocdn8.clarovideo.net/PARAMOUNT/PELICULAS/HDGNTM101A-01-01-01/EXPORTACION_WEB/SPRITES/HDGNTM101A-01-01-01-TRICKPLAY.bif",
            "image_external": null,
            "duration": "00:50:02",
            "date": "25/02/2023",
            "year": "2022",
            "preview": "false",
            "season_number": "1",
            "episode_number": "1",
            "format_types": "susc",
            "live_enabled": "0",
            "live_type": null,
            "live_ref": null,
            "source_uri": "",
            "timeshift": null,
            "votes_average": 4,
            "rating_code": "R",
            "proveedor_name": "PARAMOUNT",
            "proveedor_code": "paramount",
            "encoder_tecnology": {
                "id": null,
                "desc": null
            },
            "recorder_technology": {
                "id": null,
                "desc": null
            },
            "resource_name": null,
            "rollingcreditstime": null,
            "rollingcreditstimedb": null,
            "is_series": false,
            "channel_number": null
        },
    ],
    "total": 4
}
describe('Railcard page test case', () => {
    window.HTMLElement.prototype.scrollIntoView = function () { };
    test('should render onclick railcard', () => {
        const props = {
            dataObject: mockdata
        }
        const { container } = renderWithState(<RailCards {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard liverailcardmockdata', () => {
        const props = {
            dataObject: liverailcardmockdata
        }
        const { container } = renderWithState(<RailCards {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard moviesrailcardmockdata', () => {
        const props = {
            dataObject: moviesrailcardmockdata
        }
        const { container } = renderWithState(<RailCards {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent.blur(getbytestid)
        fireEvent.focus(getbytestid)
        fireEvent.keyUp(getbytestid, { keyCode: '403' })
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick railcard moviesrailcardmockdata', () => {
        const props = {
            byUser: true
        }
        renderWithState(<RailCards {...props} />)
    })
})