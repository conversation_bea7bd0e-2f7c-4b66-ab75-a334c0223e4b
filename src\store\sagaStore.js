import { configureStore } from "@reduxjs/toolkit";
import createSagaMiddleware from "@redux-saga/core";
//importing Reducer
import { rootReducer } from "./rootReducer";

//importing saga
import loginSaga from "../apiSaga/loginSaga";
import registerSaga from "../apiSaga/registerSaga";
import RecoverPasswordSaga from "../apiSaga/RecoverPasswordSaga";
import getUserInfoSaga from "../apiSaga/getUserInfoSaga";
import ImageSaga from "../apiSaga/ImageSaga";
import navBarSaga from "../apiSaga/navBarsaga";
import bannerDataSaga from "../apiSaga/bannerSaga";
import epgSaga from "../apiSaga/epgSaga";
import premiumCarouselDataSaga from "../apiSaga/premiumCarouselDataSaga";
import initialSaga from "../apiSaga/initialSaga";
import isLoggedInSaga from "../apiSaga/isLoggedInSaga";
import VodMoviesDataSaga from "../apiSaga/VodMoviesSaga";
import vodSeriesSaga from "../apiSaga/vodSeriesSaga";
import watchListSaga from "../apiSaga/watchListSaga";
import settingsSaga from "../apiSaga/settingsSaga";
import showGenreContentSaga from "../apiSaga/subMenuDataSaga";
import profileSaga from "../apiSaga/profileSaga";
import searchSaga from '../apiSaga/searchSaga'
import playerSaga from "../apiSaga/playerSaga";
import getTermsAndConditionSaga from "../apiSaga/setTermsAndConditionSaga";
import homeSaga from "../apiSaga/homeSaga";


const saga = createSagaMiddleware();

export const store = configureStore({
    reducer: rootReducer,
    middleware: [saga]
});

saga.run(loginSaga);
saga.run(registerSaga);
saga.run(getUserInfoSaga);
saga.run(RecoverPasswordSaga);
saga.run(getTermsAndConditionSaga);
saga.run(ImageSaga);
saga.run(navBarSaga);
saga.run(bannerDataSaga);
saga.run(epgSaga);
saga.run(premiumCarouselDataSaga);
saga.run(VodMoviesDataSaga);
saga.run(initialSaga);
saga.run(isLoggedInSaga);
saga.run(watchListSaga);
saga.run(vodSeriesSaga);
saga.run(showGenreContentSaga);
saga.run(settingsSaga);
saga.run(profileSaga);
saga.run(searchSaga);
saga.run(playerSaga);
saga.run(homeSaga)

