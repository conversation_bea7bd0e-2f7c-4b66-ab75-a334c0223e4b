import initialSlice, {
  getCheckControlPin,
  getCheckControlPinSuccess,
  getCheckControlPinError,
  getLockedChannelsList,
  getLockedChannelsListSuccess,
  getLockedChannelsListError,
  getModifyControlPin,
  getModifyControlPinSuccess,
  getModifyControlPinError,
  getPasswordChange,
  getPasswordChangeSuccess,
  getPasswordChangeError,
  setControlPin,
  setControlPinSuccess,
  setControlPinError,
  getStatusControlPin,
  getStatusControlPinSuccess,
  getStatusControlPinError,
  getControlPin,
  getControlPinSuccess,
  getControlPinError,
  getTransactionHistory,
  getTransactionHistorySuccess,
  getTransactionHistoryError,
  getUserLogout,
  getUserLogoutSuccess,
  getUserLogoutError,
  getLogoutAllDevices,
  getLogoutAllDevicesSuccess,
  getLogoutAllDevicesError,
  getDevicesList,
  getDevicesListSuccess,
  getDevicesListError,
  getLockedChannelDelete,
  getLockedChannelDeleteSuccess,
  getLockedChannelDeleteError,
  getLockedChannelAdd,
  getLockedChannelAddSuccess,
  getLockedChannelAddError,
  getChangeControlPin,
  getChangeControlPinSuccess,
  getChangeControlPinError,
  getRemindControlPin,
  getRemindControlPinSuccess,
  getRemindControlPinError,
  disableControlPin,
  disableControlPinSuccess,
  disableControlPinError,
  getActiveSubscriptions,
  getActiveSubscriptionsSuccess,
  getActiveSubscriptionsError,
  getCancelSubscription,
  getCancelSubscriptionSuccess,
  getCancelSubscriptionError,
  getSubscriptionInfo,
  getSubscriptionInfoSuccess,
  getSubscriptionInfoError,
  getAddPayments,
  getAddPaymentsSuccess,
  getAddPaymentsError,
  getPaymentsConfirm,
  getPaymentsConfirmSuccess,
  getPaymentsConfirmError,
  getAddPromoCode,
  getAddPromoCodeSuccess,
  getAddPromoCodeError,
  getSubscribePlan,
  getSubscribePlanSuccess,
  getSubscribePlanError,
  getPaywayWorkflow,
  getPaywayWorkflowSuccess,
  getPaywayWorkflowError,
  getViewSubscribeData,
  getCancelSubscribeViewData,
  getTransactionFilters,
  getSettingsState,
  getFaqFilters,
  getPaymentDetails,
  getClearPasswordState,
  getClearAllSettingsState,
  clearRemindPinSettingsState,
  getCMSflowforViewSubs,
  getCMSflowforViewSubsSuccess,
  getCMSflowforViewSubsError,
  setLastTouch,
  clearLastTouch,
  clearFPNotification,
  getVodSubscriptionInfo,
  getVodSubscriptionInfoSuccess,
  getVodSubscriptionInfoError,
  clearCheckControlPin,
  clrSubscriptionInfo,
  setlastTypedText,
  getClearSubscriptionInfo,
  getClearModifyControlPin,
} from './settingsSlice'

describe('initialSlice reducer', () => {
  const initialState = {
    setlastTypedText: {},
    checkControlPin: {},
    lockedChannelsList: {},
    modifyControlPin: {},
    passwordChange: {},
    setControlPinData: {},
    setControlPinSuccess: {},
    setControlPinError: {},
    statusControlPin: {},
    controlPin: {},
    transactionHistory: {},
    userLogout: {},
    logoutAllDevices: {},
    devicesList: {},
    lockedChannelAdd: {},
    lockedChannelDelete: {},
    changeControlPin: {},
    changeControlPinError: {},
    remindControlPin: {},
    disableControlPin: {},
    disableControlPinError: {},
    activeSubscriptions: {},
    cancelSubscription: {},
    getSubsInfo: {},
    addPayments: {},
    paymentsConfirm: {},
    addPromoCode: {},
    subscribePlan: {},
    paywayWorkFlow: {},
    viewSubscribeData: {},
    cancelSubscribeViewData: {},
    transactionFilter: {},
    controlPinStatus:{},
    settingsState: {},
    faqFilters: {},
    paymentData: {},
    cmsViewSub: {},
    lasttouch: '',
    isLoading: false,
    error: {},
    getVodSubsInfo: {},
    addFibraLinePayments: {},
  fibraPaymentsConfirm: {},
  lastTypedText: {},
  }

  it('should handle getClearModifyControlPin', () => {
    const payload = { trailer: 'player' }
    const action = { type: getClearModifyControlPin.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getChangeControlPinError', () => {
    const payload = { trailer: 'player' }
    const action = { type: getChangeControlPinError.type, payload }
    const expectedState = {
      ...initialState,
      changeControlPinError: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getClearSubscriptionInfo', () => {
    const payload = { trailer: 'player' }
    const action = { type: getClearSubscriptionInfo.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle clrSubscriptionInfo', () => {
    const payload = { trailer: 'player' }
    const action = { type: clrSubscriptionInfo.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle clearCheckControlPin', () => {
    const payload = { trailer: 'player' }
    const action = { type: clearCheckControlPin.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle clearFPNotification', () => {
    const payload = { trailer: 'player' }
    const action = { type: clearFPNotification.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle clearLastTouch', () => {
    const payload = { trailer: 'player' }
    const action = { type: clearLastTouch.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle clearRemindPinSettingsState', () => {
    const payload = { trailer: 'player' }
    const action = { type: clearRemindPinSettingsState.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle setlastTypedText', () => {
    const payload = { trailer: 'player' }
    const action = { type: setlastTypedText.type, payload }
    const expectedState = {
      ...initialState,
      lastTypedText: payload,
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle setControlPinSuccess', () => {
    const payload = {  response: { control: 'pin' } }
    const action = { type: setControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      setControlPinData: payload.response,
      setControlPinError: null,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle setControlPinError', () => {
    const payload = { control: 'pin' }
    const action = { type: setControlPinError.type, payload }
    const expectedState = {
      ...initialState,
      setControlPinData: null,
      setControlPinError: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle checkControlPin', () => {
    const action = { type: getCheckControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLockedChannelsList', () => {
    const action = { type: getLockedChannelsList.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getModifyControlPin', () => {
    const action = { type: getModifyControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPasswordChange', () => {
    const action = { type: getPasswordChange.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle setControlPin', () => {
    const action = { type: setControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getStatusControlPin', () => {
    const action = { type: getStatusControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getControlPin', () => {
    const action = { type: getControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getTransactionHistory', () => {
    const action = { type: getTransactionHistory.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getUserLogout', () => {
    const action = { type: getUserLogout.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLogoutAllDevices', () => {
    const action = { type: getLogoutAllDevices.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getDevicesList', () => {
    const action = { type: getDevicesList.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLockedChannelDelete', () => {
    const action = { type: getLockedChannelDelete.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLockedChannelAdd', () => {
    const action = { type: getLockedChannelAdd.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getChangeControlPin', () => {
    const action = { type: getChangeControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getRemindControlPin', () => {
    const action = { type: getRemindControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle disableControlPin', () => {
    const action = { type: disableControlPin.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getActiveSubscriptions', () => {
    const action = { type: getActiveSubscriptions.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getCancelSubscription', () => {
    const action = { type: getCancelSubscription.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSubscriptionInfo', () => {
    const action = { type: getSubscriptionInfo.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getAddPayments', () => {
    const action = { type: getAddPayments.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPaymentsConfirm', () => {
    const action = { type: getPaymentsConfirm.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getAddPromoCode', () => {
    const action = { type: getAddPromoCode.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSubscribePlan', () => {
    const action = { type: getSubscribePlan.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPaywayWorkflow', () => {
    const action = { type: getPaywayWorkflow.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle CMS flow', () => {
    const action = { type: getCMSflowforViewSubs.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle  Series Subscription info', () => {
    const action = { type: getVodSubscriptionInfo.type }
    const expectedState = { ...initialState, isLoading: true }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getStartHeaderInfoSuccess', () => {
    const payload = { control: 'pin' }
    const action = { type: getCheckControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      checkControlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLockedChannelsListSuccess', () => {
    const payload = { channel: 'list' }
    const action = { type: getLockedChannelsListSuccess.type, payload }
    const expectedState = {
      ...initialState,
      lockedChannelsList: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getModifyControlPinSuccess', () => {
    const payload = { control: 'pin' }
    const action = { type: getModifyControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      modifyControlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPasswordChangeSuccess', () => {
    const payload = { password: 'change' }
    const action = { type: getPasswordChangeSuccess.type, payload }
    const expectedState = {
      ...initialState,
      passwordChange: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getStatusControlPinSuccess', () => {
    const payload = { control: 'pin' }
    const action = { type: getStatusControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      statusControlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getControlPinSuccess', () => {
    const payload = { control: 'pin' }
    const action = { type: getControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      controlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getTransactionHistorySuccess', () => {
    const payload = { transaction: 'history' }
    const action = { type: getTransactionHistorySuccess.type, payload }
    const expectedState = {
      ...initialState,
      transactionHistory: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getUserLogoutSuccess', () => {
    const payload = { logout: 'user' }
    const action = { type: getUserLogoutSuccess.type, payload }
    const expectedState = {
      ...initialState,
      userLogout: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLogoutAllDevicesSuccess', () => {
    const payload = { logout: 'all devices' }
    const action = { type: getLogoutAllDevicesSuccess.type, payload }
    const expectedState = {
      ...initialState,
      logoutAllDevices: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getDevicesListSuccess', () => {
    const payload = { device: 'list' }
    const action = { type: getDevicesListSuccess.type, payload }
    const expectedState = {
      ...initialState,
      devicesList: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLockedChannelDeleteSuccess', () => {
    const payload = { device: 'list' }
    const action = { type: getLockedChannelDeleteSuccess.type, payload }
    const expectedState = {
      ...initialState,
      lockedChannelDelete: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getLockedChannelAddSuccess', () => {
    const payload = { locked: 'channel add' }
    const action = { type: getLockedChannelAddSuccess.type, payload }
    const expectedState = {
      ...initialState,
      lockedChannelAdd: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getChangeControlPinSuccess', () => {
    const payload = { change: 'control pin' }
    const action = { type: getChangeControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      changeControlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getRemindControlPinSuccess', () => {
    const payload = { control: 'pin' }
    const action = { type: getRemindControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      remindControlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getActiveSubscriptionsSuccess', () => {
    const payload = { active: 'Subscription' }
    const action = { type: getActiveSubscriptionsSuccess.type, payload }
    const expectedState = {
      ...initialState,
      activeSubscriptions: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getCancelSubscriptionSuccess', () => {
    const payload = { cancel: 'Subscription' }
    const action = { type: getCancelSubscriptionSuccess.type, payload }
    const expectedState = {
      ...initialState,
      cancelSubscription: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSubscriptionInfoSuccess', () => {
    const payload = { subs: 'info' }
    const action = { type: getSubscriptionInfoSuccess.type, payload }
    const expectedState = {
      ...initialState,
      getSubsInfo: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getAddPaymentsSuccess', () => {
    const payload = { subs: 'info' }
    const action = { type: getAddPaymentsSuccess.type, payload }
    const expectedState = {
      ...initialState,
      addPayments: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPaymentsConfirmSuccess', () => {
    const payload = { payment: 'confirmation' }
    const action = { type: getPaymentsConfirmSuccess.type, payload }
    const expectedState = {
      ...initialState,
      paymentsConfirm: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getAddPromoCodeSuccess', () => {
    const payload = { promo: 'code' }
    const action = { type: getAddPromoCodeSuccess.type, payload }
    const expectedState = {
      ...initialState,
      addPromoCode: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSubscribePlanSuccess', () => {
    const payload = { subscribe: 'plan' }
    const action = { type: getSubscribePlanSuccess.type, payload }
    const expectedState = {
      ...initialState,
      subscribePlan: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPaywayWorkflowSuccess', () => {
    const payload = { propayway: 'work' }
    const action = { type: getPaywayWorkflowSuccess.type, payload }
    const expectedState = {
      ...initialState,
      paywayWorkFlow: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getViewSubscribeData', () => {
    const payload = { view: 'subscribe' }
    const action = { type: getViewSubscribeData.type, payload }
    const expectedState = {
      ...initialState,
      viewSubscribeData: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getCancelSubscribeViewData', () => {
    const payload = { cancel: 'subscribe' }
    const action = { type: getCancelSubscribeViewData.type, payload }
    const expectedState = {
      ...initialState,
      cancelSubscribeViewData: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getTransactionFilters', () => {
    const payload = { transaction: 'filters' }
    const action = { type: getTransactionFilters.type, payload }
    const expectedState = {
      ...initialState,
      transactionFilter: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getSettingsState', () => {
    const payload = { settings: 'filters' }
    const action = { type: getSettingsState.type, payload }
    const expectedState = {
      ...initialState,
      settingsState: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getFaqFilters', () => {
    const payload = { faq: 'filters' }
    const action = { type: getFaqFilters.type, payload }
    const expectedState = {
      ...initialState,
      faqFilters: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getPaymentDetails', () => {
    const payload = { Payment: 'details' }
    const action = { type: getPaymentDetails.type, payload }
    const expectedState = {
      ...initialState,
      paymentData: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getCMSflowforViewSubsSuccess', () => {
    const payload = { CMS: 'flow' }
    const action = { type: getCMSflowforViewSubsSuccess.type, payload }
    const expectedState = {
      ...initialState,
      cmsViewSub: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle setLastTouch', () => {
    const payload = { last: 'touch' }
    const action = { type: setLastTouch.type, payload }
    const expectedState = {
      ...initialState,
      lasttouch: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getVodSubscriptionInfoSuccess', () => {
    const payload = { Series: 'subscription' }
    const action = { type: getVodSubscriptionInfoSuccess.type, payload }
    const expectedState = {
      ...initialState,
      getVodSubsInfo: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle disableControlPinSuccess', () => {
    const payload = { Series: 'subscription' }
    const action = { type: disableControlPinSuccess.type, payload }
    const expectedState = {
      ...initialState,
      disableControlPin: payload,
      isLoading: false
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getClearPasswordState', () => {
    const payload = { Series: 'subscription' }
    const action = { type: getClearPasswordState.type }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })
  getClearAllSettingsState
  it('should handle getClearAllSettingsState', () => {
    const payload = { Series: 'subscription' }
    const action = {
      type: getClearAllSettingsState.type,
      checkControlPin: {},
      lockedChannelsList: {},
      modifyControlPin: {},
      passwordChange: {},
      setControlPinData: {},
      setControlPinError: {},
      statusControlPin: {},
      controlPin: {},
      transactionHistory: {},
      userLogout: {},
      logoutAllDevices: {},
      lockedChannelDelete: {},
      lockedChannelAdd: {},
      changeControlPin: {},
      remindControlPin: {},
      disableControlPin: {},
      disableControlPinError: {},
      activeSubscriptions: {},
      cancelSubscription: {},
      getSubsInfo: {},
      addPayments: {},
      paymentsConfirm: {},
      addPromoCode: {},
      subscribePlan: {},
      paywayWorkFlow: {},
      viewSubscribeData: {},
      cancelSubscribeViewData: {},
      transactionFilter: {},
      settings: {},
      changeControlPinError: {},
      faqFilters: {},
      paymentData: {},
      cmsViewSub: {},
      controlPinStatus:{},
      isLoading: false,
    }
    const expectedState = { ...initialState }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })
  ////////////////////ERROR CASES///////////////////////

  it('should handle Control Pin Error', () => {
    const payload = 'Error message'
    const action = { type: disableControlPinError.type, payload }
    const expectedState = {
      ...initialState,
      isLoading: false,
      disableControlPinError: payload
    }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Control Pin Error', () => {
    const payload = 'Error message'
    const action = { type: getCheckControlPinError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Locked Channel List Error', () => {
    const payload = 'Error message'
    const action = { type: getLockedChannelsListError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Modify Control Pin Error', () => {
    const payload = 'Error message'
    const action = { type: getModifyControlPinError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle  Password Change Error', () => {
    const payload = 'Error message'
    const action = { type: getPasswordChangeError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Control pin  Error', () => {
    const payload = 'Error message'
    const action = { type: getStatusControlPinError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Control pin  Error', () => {
    const payload = 'Error message'
    const action = { type: getControlPinError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle transaction history  Error', () => {
    const payload = 'Error message'
    const action = { type: getTransactionHistoryError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle user logout  Error', () => {
    const payload = 'Error message'
    const action = { type: getUserLogoutError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle user logout  Error', () => {
    const payload = 'Error message'
    const action = { type: getLogoutAllDevicesError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle device list Error', () => {
    const payload = 'Error message'
    const action = { type: getDevicesListError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle lock channel delete  Error', () => {
    const payload = 'Error message'
    const action = { type: getLockedChannelDeleteError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle lock channel add  Error', () => {
    const payload = 'Error message'
    const action = { type: getLockedChannelAddError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle remind control pin  Error', () => {
    const payload = 'Error message'
    const action = { type: getRemindControlPinError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Subscriptions  Error', () => {
    const payload = 'Error message'
    const action = { type: getActiveSubscriptionsError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Subscription cancel Error', () => {
    const payload = 'Error message'
    const action = { type: getCancelSubscriptionError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Subscription cancel Error', () => {
    const payload = 'Error message'
    const action = { type: getVodSubscriptionInfoError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Subscription cancel Error', () => {
    const payload = 'Error message'
    const action = { type: getVodSubscriptionInfoError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle CMS flow Error', () => {
    const payload = 'Error message'
    const action = { type: getCMSflowforViewSubsError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Subscription Error', () => {
    const payload = 'Error message'
    const action = { type: getSubscriptionInfoError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Payments Error', () => {
    const payload = 'Error message'
    const action = { type: getAddPaymentsError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Payments Confirmation Error', () => {
    const payload = 'Error message'
    const action = { type: getPaymentsConfirmError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Add Promocode Error', () => {
    const payload = 'Error message'
    const action = { type: getAddPromoCodeError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle Subscription plan Error', () => {
    const payload = 'Error message'
    const action = { type: getSubscribePlanError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle  payway workflow Error', () => {
    const payload = 'Error message'
    const action = { type: getPaywayWorkflowError.type, payload }
    const expectedState = { ...initialState, isLoading: false, error: payload }
    expect(initialSlice(initialState, action)).toEqual(expectedState)
  })
})
