import React from 'react'
import './EpgSkeletonLoading.scss'
import './MiniEpg.scss'

export function EpgSkeletonLoading() {
  return (
    <div>
      <div className="description-page-shimmer">
        <div className="description-page-header epg-shimmer"></div>
        <div className="description-page-subtitle epg-shimmer"></div>
        <div className="description-page-duration epg-shimmer"></div>
        <div className="page-description epg-shimmer"></div>
        <div className="description-page-guide epg-shimmer"></div>
      </div>
      <div className="epg-grid-shimmer">
        {Array.from({ length: 12 }, (_, index) => (
          <div key={index} className="epg-cell">
            <div className="epg-cell-image epg-shimmer"></div>
            <div className="epg-cell-content epg-shimmer"></div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function MiniEpgSkeleton() {
  return (
    <div className="mini-epg-container">
      <div className="mini-channel-wrapper">
        <span className="mini-channel-up">
          <img src="images/Chevron_Up.png" alt="chevron_up" />
        </span>
        <div className="mini-channel-container">
        <span className="mini-channel-number"></span>
          <span className="mini-channel mini-epg-skeleton"></span>
        </div>
        <span className="mini-channel-down">
          <img src="images/Chevron_Down.png" alt="chevron_up" />
        </span>
      </div>
      <div className="mini-program-wrapper">
        <span className="mini-program-left">
        </span>
        <>
          <div className="mini-program-container-1">
          <span className="mini-program-recorder" ></span>
            <span className="mini-program-image mini-epg-skeleton"></span>
            <div className="mini-program mini-epg-skeleton"></div>
            <div className="mini-program-details mini-epg-skeleton"></div>
            <div className="mini-progress-bar mini-epg-skeleton"></div>
          </div>
          <div className="mini-program-container-2">
          <span className="mini-program-recorder" ></span>
            <span className="mini-program-image mini-epg-skeleton"></span>
            <div className="mini-program mini-epg-skeleton"></div>
            <div className="mini-program-details mini-epg-skeleton"></div>
            <div className="mini-progress-bar mini-epg-skeleton"></div>
          </div>
        </>
        <span className="mini-program-right">
          <img src="images/Chevron_Right.png" alt="chevron_up" />
        </span>
      </div>
    </div>
  )
}
