import { call, takeEvery } from 'redux-saga/effects'
import {
  getActiveSubscriptionsSuccess,
  getActiveSubscriptionsError,
  getCancelSubscriptionSuccess,
  getCancelSubscriptionError,
  getChangeControlPinSuccess,
  getChangeControlPinError,
  getCheckControlPinSuccess,
  getCheckControlPinError,
  getLockedChannelDeleteSuccess,
  getLockedChannelDeleteError,
  getLockedChannelsListSuccess,
  getLockedChannelsListError,
  getModifyControlPinSuccess,
  getModifyControlPinError,
  getPasswordChangeSuccess,
  getPasswordChangeError,
  getRemindControlPinSuccess,
  getRemindControlPinError,
  setControlPinSuccess,
  setControlPinError,
  getStatusControlPinSuccess,
  getStatusControlPinError,
  getControlPinStatusSuccess,
  getControlPinStatusError,
  getTransactionHistorySuccess,
  getTransactionHistoryError,
  getUserLogoutSuccess,
  getUserLogoutError,
  getLogoutAllDevicesSuccess,
  getLogoutAllDevicesError,
  getDevicesListSuccess,
  getDevicesListError,
  getAddPaymentsSuccess,
  getAddPaymentsError,
  getAddFibraLinePaymentsSuccess,
  getAddFibraLinePaymentsError,
  getFibraPaymentsConfirmSuccess,
  getFibraPaymentsConfirmError,
  getPaymentsConfirmSuccess,
  getPaymentsConfirmError,
  getAddPromoCodeSuccess,
  getAddPromoCodeError,
  getSubscribePlanSuccess,
  getSubscribePlanError,
  getPaywayWorkflowSuccess,
  getPaywayWorkflowError,
  getCMSflowforViewSubsSuccess,
  getCMSflowforViewSubsError,
  getControlPinSuccess,
  getControlPinError,
  disableControlPinSuccess,
  disableControlPinError,
  getLockedChannelAddSuccess,
  getLockedChannelAddError,
  getSubscriptionInfoSuccess,
  getSubscriptionInfoError,
  getVodSubscriptionInfoSuccess,
  getVodSubscriptionInfoError,
  addTelcelPaymentSuccess,
  addTelcelPaymentError,
  confirmTelcelSuccess,
  confirmTelcelError,
  getClientSuccess,
  getClientError,
  addClientSuccess,
  addClientError,
  getAvailableCreditCardsSuccess,
  getAvailableCreditCardsError,
  addCreditCardSuccess,
  addCreditCardError,
  updateCreditCardSuccess,
  updateCreditCardError,
  deleteCreditCardSuccess,
  deleteCreditCardError,
  creditCardPaymentSuccess,
  creditCardPaymentError,
  getNewPlanSelectorSuccess,
  getNewPlanSelectorError,
  getNewPlanSelectorV1Success,
  getNewPlanSelectorV1Error,
  getMultipurchaseButtonSuccess,
  getMultipurchaseButtonError
} from '../store/slices/settingsSlice'
import { store } from '../store/sagaStore'
import { COMMON_URL, URL } from '../utils/environment'
import { request } from '../utils/request'
import { setSkeltonLoading } from '../store/slices/login'

function* getActiveSubscriptionsApi({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(
      request,
      URL.ACTIVE_SUBSCRIPTIONS_URL +
        '&HKS=' +
        payload.hks +
        '&user_id=' +
        payload.userId +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getActiveSubscriptionsSuccess(response))
        },
        onError(error) {
          store.dispatch(getActiveSubscriptionsError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getSubscriptionInfoApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.GET_SUBSCRIPTIONS_INFO_URL}${payload.url}&HKS=${payload.hks}&user_id=${payload.userId}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getSubscriptionInfoSuccess(response))
        },
        onError(error) {
          store.dispatch(getSubscriptionInfoError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getNewPlanSelectorApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload?.url}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}&HKS=${payload?.hks}&user_id=${payload?.userId}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getNewPlanSelectorSuccess(response))
        },
        onError(error) {
          store.dispatch(getNewPlanSelectorError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getNewPlanSelectorV1Api({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload?.url}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getNewPlanSelectorV1Success(response))
        },
        onError(error) {
          store.dispatch(getNewPlanSelectorV1Error(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}


function* getCancelSubscriptionApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.CANCEL_SUBSCRIPTIONS_URL +
        '&HKS=' +
        payload?.hks +
        '&user_id=' +
        payload?.userId +
        '&purchase_id=' +
        payload?.purchaseId +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getCancelSubscriptionSuccess(response))
        },
        onError(error) {
          store.dispatch(getCancelSubscriptionError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* getChangeControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.CHANGE_CONTROL_PIN_URL}HKS=${payload.hks}&user_id=${payload.user_id}&code=${payload.code}&new_code=${payload.new_code}&user_token=${payload.user_token}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getChangeControlPinSuccess(response))
        },
        onError(error) {
          store.dispatch(getChangeControlPinError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* getcheckControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.CHECK_CONTROL_PIN_URL}HKS=${payload.hks}&controlPIN=${payload.controlPIN}&userId=${payload.userId}&parental=${payload.parental}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getCheckControlPinSuccess(response))
        },
        onError(error) {
          store.dispatch(getCheckControlPinError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* getLockedChannelDeleteApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.LOCKED_CHANNEL_DELETE_URL}&HKS=${payload.hks}&group_id=${payload.group_id}&user_hash=${payload.user_hash}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLockedChannelDeleteSuccess(response))
        },
        onError(error) {
          store.dispatch(getLockedChannelDeleteError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* getLockedChannelAddApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.LOCKED_CHANNEL_ADD_URL}&HKS=${payload.hks}&group_id=${payload.group_id}&user_hash=${payload.user_hash}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLockedChannelAddSuccess(response))
        },
        onError(error) {
          store.dispatch(getLockedChannelAddError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getlockedChannelsListApi({ payload }) {
  const region = localStorage.getItem('region')
  payload?.loader && store.dispatch(setSkeltonLoading(true))
  try {
    yield call(
      request,
      `${URL.LOCKED_CHANNELS_LIST_URL}HKS=${payload.hks}&user_hash=${payload.user_hash}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLockedChannelsListSuccess(response))
          store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(getLockedChannelsListError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
    store.dispatch(setSkeltonLoading(false))
  }
}

function* getModifyControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.MODIFY_CONTROL_PIN_URL}&region=${region}&status_purchase_pin=${payload.purchasePin}&status_parental_pin=${payload.parentalPin}&status_channel_pin=${payload.channelPin}&accesss_parental_code=${payload.accessParentalCode}&user_id=${payload.userId}&user_token=${payload.userToken}&pin=${payload.userPin}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getModifyControlPinSuccess(response))
        },
        onError(error) {
          store.dispatch(getModifyControlPinError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getPasswordChangeApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.PASSWORD_CHANGE_URL +
        '&newpassword=' +
        payload.newPassword +
        '&controlpassword=' +
        payload.newPassword +
        '&oldpassword=' +
        payload.oldPassword +
        '&user_id=' +
        payload.userId +
        '&user_hash=' +
        payload.userHash +
        '&HKS=' +
        payload.HKS +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getPasswordChangeSuccess(response))
        },
        onError(error) {
          store.dispatch(getPasswordChangeError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* getRemindControlPinApi({ payload }) {
  try {
    yield call(
      request,
      `${URL.REMIND_CONTROL_PIN}HKS=${payload.hks}&user_hash=${payload.user_hash}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getRemindControlPinSuccess(response))
        },
        onError(error) {
          store.dispatch(getRemindControlPinError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* disableControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.DISABLE_CONTROL_PIN}HKS=${payload.hks}&user_id=${payload.user_id}&code=${payload.code}&user_token=${payload.user_token}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(disableControlPinSuccess(response))
        },
        onError(error) {
          store.dispatch(disableControlPinError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* setControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.SET_CONTROL_PIN_URL}&HKS=${payload.hks}&set_user_id=${payload.userId}&code=${payload.code}&user_token=${payload.user_token}&parental=${payload.parental}&rating=${payload.rating}&purchase=${payload.purchase}&channel=${payload.channel}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(setControlPinSuccess(response))
        },
        onError(error) {
          store.dispatch(setControlPinError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getStatusControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  !payload?.loadingPage && store.dispatch(setSkeltonLoading(true))

  try {
    yield call(
      request,
      `${URL.STATUS_CONTROL_PIN_URL}&region=${region}&HKS=${payload.hks}&user_id=${payload.userId}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getStatusControlPinSuccess(response))
          store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(getStatusControlPinError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getControlPinStatusApi({ payload }) {
  const region = localStorage.getItem('region')
  !payload?.loadingPage && store.dispatch(setSkeltonLoading(true))

  try {
    yield call(
      request,
      `${URL.CONTROL_PIN_STATUS_URL}&region=${region}&HKS=${payload.hks}&user_id=${payload.userId}&user_token=${payload.user_token}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getControlPinStatusSuccess(response))
          store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(getControlPinStatusError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* getControlPinApi({ payload }) {
  const region = localStorage.getItem('region')
  !payload?.loadingPage && store.dispatch(setSkeltonLoading(true))
  try {
    yield call(
      request,
      `${URL.GET_CONTROL_PIN_URL}&HKS=${payload.hks}&user_id=${payload.user_id}&user_token=${payload.user_token}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getControlPinSuccess(response))
          store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(getControlPinError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getTransactionHistoryApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.TRANSACTION_HISTORY_URL +
        '&HKS=' +
        payload.hks +
        '&user_id=' +
        payload.userId +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getTransactionHistorySuccess(response))
        },
        onError(error) {
          store.dispatch(getTransactionHistoryError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getUserLogoutApi({ payload }) {
  try {
    yield call(
      request,
      URL.USER_LOGOUT_URL + '&HKS=' + payload.hks,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getUserLogoutSuccess(response))
        },
        onError(error) {
          store.dispatch(getUserLogoutError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getLogoutAllDevicesApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.USER_LOGOUT_ALL_DEVICES_URL}&region=${region}&HKS=${payload.hks}&user_id= ${payload.userId}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLogoutAllDevicesSuccess(response))
        },
        onError(error) {
          store.dispatch(getLogoutAllDevicesError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getDevicesListApi({ payload }) {
  try {
    yield call(
      request,
      `${URL.DEVICES_LIST_URL}&HKS=${payload.hks}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getDevicesListSuccess(response))
        },
        onError(error) {
          store.dispatch(getDevicesListError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* addTelcelPaymentApi({ payload }) {
  let apiUrl = payload?.existingTelcel
    ? `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&format=json`
    : `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&numberField=${payload.number}&payway=${payload.payway}&format=json`

  try {
    yield call(
      request,
      apiUrl,
      {
        method: 'GET',
        Authorization: `Bearer ${payload?.userToken}`
      },
      {
        onSuccess(response) {
          store.dispatch(addTelcelPaymentSuccess(response))
        },
        onError(error) {
          store.dispatch(addTelcelPaymentError(error))
        }
      }
    )
  } catch (error) {
    console.error('telcel catch error --> ', error)
  }
}

function* confirmTelcelApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&numberField=${payload.number}&pin=${payload.otp}&format=json`,
      {
        method: 'GET',
        Authorization: `Bearer ${payload?.userToken}`
      },
      {
        onSuccess(response) {
          store.dispatch(confirmTelcelSuccess(response))
        },
        onError(error) {
          store.dispatch(confirmTelcelError(error))
        }
      }
    )
  } catch (error) {
    console.error('telcel otp catch error --> ', error)
  }
}

function* getAddPaymentsApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `payway=${payload.payway}&token=${payload.userToken}&buyToken=${
          payload.buyToken
        }&extra_params=${
          payload.number
            ? '{"account" : "' + payload.number + '"}'
            : '{"claveServicio":"' + payload.inputValue + '"}'
        }`
      },
      {
        onSuccess(response) {
          store.dispatch(getAddPaymentsSuccess(response))
        },
        onError(error) {
          store.dispatch(getAddPaymentsError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getAddFibraLinePaymentsApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `payway=${payload.payway}&token=${payload.userToken}&buyToken=${
          payload.buyToken
        }&extra_params=${
          payload.number
            ? '{"account" : "' + payload.number + '"}'
            : '{"claveServicio":"' + payload.inputValue + '"}'
        }`
      },
      {
        onSuccess(response) {
          store.dispatch(getAddFibraLinePaymentsSuccess(response))
        },
        onError(error) {
          store.dispatch(getAddFibraLinePaymentsError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getFibraPaymentsConfirmApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `payway=${payload.payway}&token=${payload.userToken}&buyToken=${
          payload.buyToken
        }&extra_params=${
          '{"claveServicio" : "' +
          payload.number +
          '"}' +
          '{"service_Id":"' +
          payload.serviceId +
          '"}'
        }`
      },
      {
        onSuccess(response) {
          store.dispatch(getFibraPaymentsConfirmSuccess(response))
        },
        onError(error) {
          store.dispatch(getFibraPaymentsConfirmError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getPaymentsConfirmApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `token=${payload.userToken}&buyToken=${payload.buyToken}&payway=${
          payload.payway
        }&access_pin_token=${payload.accessPinToken}&access_pin_code=${
          payload.otp
        }&user_id=${payload.userId}&extra_params=${
          '{"account" : "' + payload.number + '"}'
        }`
      },
      {
        onSuccess(response) {
          store.dispatch(getPaymentsConfirmSuccess(response))
        },
        onError(error) {
          store.dispatch(getPaymentsConfirmError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getAddPromoCodeApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&api_version=${COMMON_URL.api_version}&format=json&pincode=${payload.promoCode}&token=${payload.userToken}`,
      {
        method: 'POST'
      },
      {
        onSuccess(response) {
          store.dispatch(getAddPromoCodeSuccess(response))
        },
        onError(error) {
          store.dispatch(getAddPromoCodeError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getSubscribePlanApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `token=${payload.userToken}&buyToken=${payload.buyToken}`
      },
      {
        onSuccess(response) {
          store.dispatch(getSubscribePlanSuccess(response))
        },
        onError(error) {
          store.dispatch(getSubscribePlanError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getMultipurchaseButtonApi({ payload }) {
  const region = localStorage.getItem('region')
   try {
      yield call(
        request,
        `${URL.MULTICONTENT_PAYMENT_INFO}&region=${region}&user_id=${payload?.user_id}`,
        {
          method: 'POST',
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded',
             Authorization: `Bearer ${payload?.user_token}`
          },
          body: {
            extra_params: {group_ids: payload.group_ids}
          }
        },
        {
          onSuccess(responce) {
            store.dispatch(getMultipurchaseButtonSuccess(responce))
          },
          onError(error) {
            store.dispatch(getMultipurchaseButtonError(error))
          }
        }
      )
    }
  catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getPaywayWorkflowApi({ payload }) {
  store.dispatch(setSkeltonLoading(true))
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.workflow}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&format=json`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getPaywayWorkflowSuccess(response))
          store.dispatch(setSkeltonLoading(false))
        },
        onError(error) {
          store.dispatch(getPaywayWorkflowError(error))
          store.dispatch(setSkeltonLoading(false))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getCMSflowforViewSubsApi({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.CMS_LEVEL_V1_URL +
        '&node=' +
        payload.tabValue +
        '&user_id=' +
        payload.userId +
        '&type=abonos' +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getCMSflowforViewSubsSuccess(response))
        },
        onError(error) {
          store.dispatch(getCMSflowforViewSubsError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getVodSubscriptionInfo({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      payload?.type !== 'watchfree'
        ? `${URL.GET_SUBSCRIPTIONS_INFO_URL}${payload.url}&HKS=${
            payload.hks
          }&user_id=${payload.userId}&region=${region}&is_kids=${
            payload.is_kids == 'false' ? 0 : 1
          }`
        : `${URL.GET_SUBSCRIPTIONS_INFO_URL}${
            payload.url
          }&region=${region}&is_kids=${payload.is_kids == 'false' ? 0 : 1}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getVodSubscriptionInfoSuccess(response))
        },
        onError(error) {
          store.dispatch(getVodSubscriptionInfoError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* addClientApi({ payload }) {
  try {
    yield call(
      request,
      `${payload.url}/cliente`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: atob(payload?.headerToken)
        },
        body: {
          id_externo: payload.userId, //(user ID from app),
          nombre: payload.firstname, //(profile),
          apellido_paterno: payload.lastname, //(last name),
          email: payload.email, //(email),
          direccion: {
            linea1: payload.lineal,
            ciudad: payload.ciudad,
            cp: payload.cp
          },
          device_fingerprint:
            localStorage.getItem('hks') +
            new Date().toTimeString().split(' ')[0]
        }
      },
      {
        onSuccess(response) {
          store.dispatch(addClientSuccess(response))
        },
        onError(error) {
          store.dispatch(addClientError(error))
        }
      }
    )
  } catch (error) {
    console.error('add client catch error --> ', error)
  }
}

function* getClientApi({ payload }) {
  try {
    yield call(
      request,
      `${payload.url}/cliente/email/${payload?.email}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: atob(payload?.headerToken)
        }
      },
      {
        onSuccess(response) {
          store.dispatch(getClientSuccess(response))
        },
        onError(error) {
          store.dispatch(getClientError(error))
        }
      }
    )
  } catch (error) {
    console.error('get client catch error --> ', error)
  }
}

function* getAvailableCreditCardsApi({ payload }) {
  try {
    yield call(
      request,
      `${payload.url}/cliente/${payload.clientId}/tarjeta`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: atob(payload?.headerToken)
        }
      },
      {
        onSuccess(response) {
          store.dispatch(getAvailableCreditCardsSuccess(response))
        },
        onError(error) {
          store.dispatch(getAvailableCreditCardsError(error))
        }
      }
    )
  } catch (error) {
    console.error('get available credit card catch error --> ', error)
  }
}

function* addCreditCardApi({ payload }) {
  try {
    yield call(
      request,
      `${payload.url}/tarjeta`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: atob(payload?.headerToken)
        },
        body: {
          nombre: payload.firstname,
          pan: payload.cardNumber,
          cvv2: payload.cvv2,
          expiracion_mes: payload.expiryMonth,
          expiracion_anio: payload.expiryYear,
          direccion: {
            linea1: payload.lineal,
            cp: payload.cp,
            telefono: {
              numero: payload.mobileNumber
            },
            ciudad: payload.ciudad,
            estado: payload.estado,
            pais: payload.pais
          },
          cliente_id: payload.clientId,
          default: payload.isDefault,
          cargo_unico: payload.singleUse
        }
      },
      {
        onSuccess(response) {
          store.dispatch(addCreditCardSuccess(response))
        },
        onError(error) {
          store.dispatch(addCreditCardError(error))
        }
      }
    )
  } catch (error) {
    console.error('add credit card error --> ', error)
  }
}

function* updateCreditCardApi({ payload }) {
  try {
    yield call(
      request,
      `${payload.url}/tarjeta/${payload?.cardToken}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: atob(payload?.headerToken)
        },
        body: payload.updateFields
      },
      {
        onSuccess(response) {
          store.dispatch(updateCreditCardSuccess(response))
        },
        onError(error) {
          store.dispatch(updateCreditCardError(error))
        }
      }
    )
  } catch (error) {
    console.error('update credit card error --> ', error)
  }
}

function* deleteCreditCardApi({ payload }) {
  try {
    yield call(
      request,
      `${payload.url}/tarjeta/${payload?.cardToken}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: atob(payload?.headerToken)
        }
      },
      {
        onSuccess(response) {
          store.dispatch(deleteCreditCardSuccess(response))
        },
        onError(error) {
          store.dispatch(deleteCreditCardError(error))
        }
      }
    )
  } catch (error) {
    console.error('delete credit card error --> ', error)
  }
}

function* creditCardPaymentApi({ payload }) {
  try {
    yield call(
      request,
      `${COMMON_URL.BASE_URL}${payload.apiUrl}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `token=${payload.userToken}&buyToken=${payload.buyToken}&payway=${
          payload.payway
        }&user_id=${payload.userId}&extra_params=${
          '{"client_id" : "' +
          payload.clientId +
          '", "card_token" : "' +
          payload.cardToken +
          '", "card_type" : "' +
          payload.cardType +
          '", "device_fingerprint" : "' +
          localStorage.getItem('hks') +
          new Date().toTimeString().split(' ')[0] +
          '"}'
        }`
      },
      {
        onSuccess(response) {
          store.dispatch(creditCardPaymentSuccess(response))
        },
        onError(error) {
          store.dispatch(creditCardPaymentError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* settingsSaga() {
  yield takeEvery('settingsSlice/getSubscriptionInfo', getSubscriptionInfoApi),
    yield takeEvery('settingsSlice/getNewPlanSelector', getNewPlanSelectorApi),
    yield takeEvery(
      'settingsSlice/getNewPlanSelectorV1',
      getNewPlanSelectorV1Api
    ),
    yield takeEvery(
      'settingsSlice/getActiveSubscriptions',
      getActiveSubscriptionsApi
    ),
    yield takeEvery(
      'settingsSlice/getCancelSubscription',
      getCancelSubscriptionApi
    ),
    yield takeEvery(
      'settingsSlice/getChangeControlPin',
      getChangeControlPinApi
    ),
    yield takeEvery('settingsSlice/getCheckControlPin', getcheckControlPinApi),
    yield takeEvery(
      'settingsSlice/getLockedChannelDelete',
      getLockedChannelDeleteApi
    ),
    yield takeEvery(
      'settingsSlice/getLockedChannelAdd',
      getLockedChannelAddApi
    ),
    yield takeEvery(
      'settingsSlice/getLockedChannelsList',
      getlockedChannelsListApi
    ),
    yield takeEvery(
      'settingsSlice/getModifyControlPin',
      getModifyControlPinApi
    ),
    yield takeEvery('settingsSlice/getPasswordChange', getPasswordChangeApi),
    yield takeEvery(
      'settingsSlice/getRemindControlPin',
      getRemindControlPinApi
    ),
    yield takeEvery('settingsSlice/disableControlPin', disableControlPinApi),
    yield takeEvery('settingsSlice/setControlPin', setControlPinApi),
    yield takeEvery(
      'settingsSlice/getStatusControlPin',
      getStatusControlPinApi
    ),
    yield takeEvery(
      'settingsSlice/getControlPinStatus',
      getControlPinStatusApi
    ),
    yield takeEvery('settingsSlice/getControlPin', getControlPinApi),
    yield takeEvery(
      'settingsSlice/getTransactionHistory',
      getTransactionHistoryApi
    ),
    yield takeEvery('settingsSlice/getUserLogout', getUserLogoutApi),
    yield takeEvery(
      'settingsSlice/getLogoutAllDevices',
      getLogoutAllDevicesApi
    ),
    yield takeEvery('settingsSlice/getDevicesList', getDevicesListApi),
    yield takeEvery('settingsSlice/getAddPayments', getAddPaymentsApi),
    yield takeEvery(
      'settingsSlice/getAddFibraLinePayments',
      getAddFibraLinePaymentsApi
    ),
    yield takeEvery(
      'settingsSlice/getFibraPaymentsConfirm',
      getFibraPaymentsConfirmApi
    ),
    yield takeEvery('settingsSlice/getPaymentsConfirm', getPaymentsConfirmApi),
    yield takeEvery('settingsSlice/getAddPromoCode', getAddPromoCodeApi),
    yield takeEvery('settingsSlice/getSubscribePlan', getSubscribePlanApi),
    yield takeEvery('settingsSlice/getMultipurchaseButton', getMultipurchaseButtonApi),
    yield takeEvery('settingsSlice/getPaywayWorkflow', getPaywayWorkflowApi),
    yield takeEvery(
      'settingsSlice/getCMSflowforViewSubs',
      getCMSflowforViewSubsApi
    ),
    yield takeEvery(
      'settingsSlice/getVodSubscriptionInfo',
      getVodSubscriptionInfo
    ),
    yield takeEvery('settingsSlice/addTelcelPayment', addTelcelPaymentApi)
  yield takeEvery('settingsSlice/confirmTelcel', confirmTelcelApi)
  yield takeEvery('settingsSlice/addClient', addClientApi),
    yield takeEvery('settingsSlice/getClient', getClientApi),
    yield takeEvery(
      'settingsSlice/getAvailableCreditCards',
      getAvailableCreditCardsApi
    )
  yield takeEvery('settingsSlice/updateCreditCard', updateCreditCardApi),
    yield takeEvery('settingsSlice/addCreditCard', addCreditCardApi),
    yield takeEvery('settingsSlice/deleteCreditCard', deleteCreditCardApi),
    yield takeEvery('settingsSlice/creditCardPayment', creditCardPaymentApi)
}
