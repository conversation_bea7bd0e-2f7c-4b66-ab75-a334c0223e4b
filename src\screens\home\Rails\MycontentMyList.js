import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './ContinueWatching.scss'
import { useDispatch } from 'react-redux'
import { getVodMoviesWatchlist } from '../../../store/slices/VodMoviesSlice'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { HOME, SELECT_CONTENT } from '../../../GoogleAnalyticsConstants'
import { getMyListCurrFocus } from '../../../store/slices/HomeSlice'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const MycontentMyList = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const startTimeRef = useRef(null)
  const [content, setContent] = useState([])
  const [focusContent, setFocusContent] = useState(false)
  const [focusedId, setFocusedId] = useState(null)
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const railImage = useSelector(state => state?.vodMovies?.watchList)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const lastTouch = localStorage.getItem('lasttouch')
  const userId = localStorage.getItem('userId')
  const deleteWatchList = useSelector(state => state?.vodMovies?.delWatchList)
  const backelement = document.getElementById(props?.backfocusid)
  const filterlistConfiguration = apaMetaData?.byr_filterlist_configuration && JSON?.parse(apaMetaData?.byr_filterlist_configuration)
  const filterFilterlist = filterlistConfiguration?.[region]?.filterlist
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
    useEffect(() => {
    if (backelement) {
      backelement?.focus()
      backelement?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'center'
      })
    }else if(focusContent && railImage?.length > 0){
      document.getElementById(`index${props?.index}${railImage?.length-1}`)?.focus()
    }
  }, [backelement,railImage])

  const goToMoviesSeries = (item,index) => {
    const engagementTime =  Date.now() - startTimeRef.current;  
    const userData = {
      suscriptions: userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key => userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
      content_section : navbarTab,
      content_list : props?.title,
      modulo_name : 'carrusel',
      page_path: HOME,
      page_title: navbarTab,
      engagement_time_msec: engagementTime,
      previous_path: props?.gaPreviousPath
    }
    if (item?.is_series || item?.episode_number) {
      // GA : Content Selection Event
      pushContentSelectionEvent(userData, item, index, SELECT_CONTENT)
      navigate('/series', { state: { data: item, backfocusid: `index${props?.index}${index}`,pageName:'inicio' } })
    } else {
      // GA : Content Selection Event
      pushContentSelectionEvent(userData, item, index, SELECT_CONTENT)
      navigate('/movies', { state: { vodData: item, backfocusid: `index${props?.index}${index}`,pageName:'inicio' } })
    }
    startTimeRef.current = Date.now();
  }
  const element = document.getElementById('mycontentmylist')
  useEffect(() => {
    if (focusContent) {
      element?.focus()
       element?.scrollIntoView({  
        block: 'center', 
        inline: 'center',
        behavior: 'smooth' })
    }
  }, [focusContent,element])
  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }
  const handleFocus = data => {
    setContent(data)
    setFocusContent(true)
  }
  const handleBlur = data => {
    setFocusContent(false)
  }
  const handleTranslationchange = useCallback(keyname => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  useEffect(() => {
    startTimeRef.current = Date.now()
    dispatch(
      getVodMoviesWatchlist({
        id:
          deleteWatchList?.msg == 'OK'
            ? deleteWatchList?.lasttouch?.favorited
            : lastTouch,
        userId,
        HKS: userDetails?.session_stringvalue,
        user_hash: userDetails?.session_userhash,
        filterlist:filterFilterlist
      })
    )
    return () => {
      startTimeRef.current = null
    }
  }, [])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (key.redcode == keycode) {
        navigate('/deletecard', {
          state: { deleteData: content, page: 'mylist', backfocusid:focusedId }
        })
      }
    }
  }

  const handleLgkey = keycode => {
    if (focusContent) {
      if (keycode == 403) {
        navigate('/deletecard', {
          state: { deleteData: content, page: 'mylist', backfocusid:focusedId }
        })
      }
    }
  }

  const keypresshandler = event => {
    dispatch(getMyListCurrFocus(false))
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  return (
    <div className="ContinueWatchingrailContainer" 
    style={{height : railImage?.[0] ? 370 : 340, marginBottom: railImage?.[0] ? 40 : 10}}
    id='mycontentmylist'>
          {railImage?.[0] ? (
            <div className="railTitle">
              <SafeHTML html={props?.title || ''} />
            </div>
          ) : (
            ''
          )}
            <div className='ContinueWatchingsub'>
          {railImage?.[0] ? (
            <div className="rail-wrapper continue-wrapper">
              {railImage?.map((each, index, array) => (
                <>
                  <button
                    className="rail_block focusable"
                    key={index}
                    onClick={() => goToMoviesSeries(each,index)}
                    onFocus={() => {
                      handleFocus(each)
                      setFocusedId(`index${props?.index}${index}`)
                    }}
                    onBlur={() => handleBlur(each)}
                    data-testid={`rail_card_click${index}`}
                    id={`index${props?.index}${index}`}
                    data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                    data-sn-right={index != array.length - 1 && undefined}
                    data-sn-left={index == 0 ? '' : undefined}
                  >
                    {each?.image_small && each?.image_small !== '' ? (
                      <>
                        <LazyLoadImage
                          src={each?.image_small}
                          placeholderSrc="images/landscape_card.png"
                          key={index}
                          className="rail-image"
                          id={`railfocus${index}`}
                        />

                        <div className="title-icon-block">
                          <div className="continuewatchingShow-title">
                            <div className="continuewatchingtitle">
                              {each?.title}
                            </div>
                          </div>
                          <div className="deleteIcons">
                            <img
                              src={'images/Home_icons/red.png'}
                              className="redDot"
                            />
                            <img
                              src={'images/Home_icons/delete.png'}
                              className="delete"
                            />
                          </div>
                        </div>
                      </>
                    ) : (
                      <LazyLoadImage
                        src="images/landscape_card.png"
                        loading="lazy"
                        alt="PlaceHolder"
                        className="rail-image"
                      />
                    )}

                    {/* tags */}

                    {each.image_small && each?.proveedor_code == 'amco' ? (
                      each?.format_types === 'ppe,download' ? (
                        <div className="proveedorBlockRailAlq">
                          <img src={'images/Alquilar.svg'} className="tagAlq" />
                        </div>
                      ) : each?.format_types === 'ppe' ? (
                        <div className="proveedorBlockRailAlq">
                          <img src={'images/Alquilar.svg'} className="tagAlq" />
                        </div>
                      ) : null
                    ) : each.image_small &&
                      each?.proveedor_code &&
                      each?.image_medium ? (
                      <div className="proveedorBlockRail_vero_hara">
                        {Addproveedor(providerLabel?.[each?.proveedor_code]?.susc) && (
                          <img
                            id="#icon1"
                            className={each?.proveedor_code === 'picardia2' ? 'picardia-image'
                              : 'premium-icon'
                            }
                            src={Addproveedor(providerLabel?.[each?.proveedor_code]?.susc)}
                          />
                        )}
                        {each?.format_types === 'free' && userDetails?.subscriptions?.length == 0 ? (
                          <div className="verahora-tag">VER AHORA</div>
                        ) : null}
                        {each.image_small &&
                          each?.proveedor_code === 'picardia2' &&
                          each?.image_medium &&
                          <div
                            className="picardia-proveedorBlockRail">
                            <img src={'images/Adultus.svg'} className="picardia-tag" />
                          </div>
                        }
                      </div>
                    ) : null}
                  </button>
                </>
              ))}
            </div>
          ) : (
            localStorage.getItem('miscontenidos') && (
              <div>
                <div className="mycontent-railTitle">
                  <SafeHTML html={props?.title || ''} />
                </div>
                  <div className="nocontent-card-main">
                    <button className="nocontent-card focusable"
                      id={`index${props?.index}0`}
                      onFocus={handleFocus}
                      onBlur={handleBlur}
                      data-sn-right
                      data-sn-left
                      data-sn-down
                      data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                    >
                      <p className='mycontent-rail-text'>
                        {handleTranslationchange(
                          'ribbons_placeholder_PorVer_label'
                        )}</p>
                      <div className='nocontent-sub-card'>
                        <img className="cardimage1 focusable" src={'images/mycontent_placeholder.png'} />
                        <img className="cardimage2" src={'images/mycontent_placeholder.png'} />
                        <img className="cardimage3" src={'images/mycontent_placeholder.png'} />
                      </div>
                    </button>
                </div>
              </div>
            )
          )}
        </div>
    </div>
  )
}

export default MycontentMyList
