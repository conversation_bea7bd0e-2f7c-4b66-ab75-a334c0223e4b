import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, useLocation, useNavigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import LanguageSettings from './LanguageSettings';
import configureStore from 'redux-mock-store';

const mockStore = configureStore([]);

// Mock translations data
const mockTranslations = {
  language: {
    'en-US': {
      original_language: 'Idioma Original',
      profiles_settings_language_audio_option_spa: 'Español',
      profiles_settings_language_slash_label: '/',
      profiles_settings_language_subtitle_disabled: 'Desactivados',
      subtitled_in_spanish: 'Spanish Subtitles',
      subtitled_in_portuguese: 'Portuguese Subtitles',
      portuguese_language: 'Portuguese',
      spanish_language: 'Spanish',
      label_on_demand_title: 'On Demand',
      label_on_live_title: 'TV',
      label_on_demand_description: 'Aquí puedes configurar el idioma y subtítulos para los contenidos on demand.',
      label_on_live_description: 'Aquí puedes configurar el idioma y subtítulos para los contenidos Iineales.',
      choose_language_settings: 'Seleccione su idioma'
    }
  }
};

const store = mockStore({
  initialReducer: {
    appMetaData: {
      translations: JSON.stringify(mockTranslations)
    }
  }
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn((key) => {
    switch (key) {
      case 'region':
        return 'en-US';
      case 'vodContentLanguage':
        return 'O-OR';
      case 'liveContentLanguage':
        return 'D-ES';
      default:
        return null;
    }
  }),
  setItem: jest.fn(),
  clear: jest.fn()
};

// Setup localStorage mock
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true
});

document.getElementById = jest.fn().mockReturnValue(null);

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: jest.fn(),
  useNavigate: jest.fn(),
}));

describe('LanguageSettings', () => {
  const mockNavigate = jest.fn();

  beforeEach(() => {
    useNavigate.mockReturnValue(mockNavigate);
    useLocation.mockReturnValue({ state: null });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Helper function to render component with all required providers
  const renderComponent = () => {
    return render(
      <Provider store={store}> {/* Added Provider wrapper */}
        <MemoryRouter>
          <LanguageSettings />
        </MemoryRouter>
      </Provider>
    );
  };

  it('renders both language sections', () => {
    renderComponent();
    expect(screen.getByText('On Demand')).toBeInTheDocument();
    expect(screen.getByText('TV')).toBeInTheDocument();
  });

  it('navigates to choose-language page for On Demand', () => {
    renderComponent();
    fireEvent.click(screen.getByText('Idioma Original'));
    expect(mockNavigate).toHaveBeenCalledWith('/settings/profile-settings/choose-language', {
      state: { 
        type: 'onDemand', 
        currentLanguage: 'Idioma Original',
        backFocus: 'vod-dropdown-id'
      }
    });
  });
  it('renders correct text for each section', () => {
    renderComponent();
    expect(screen.getByText('Aquí puedes configurar el idioma y subtítulos para los contenidos on demand.')).toBeInTheDocument();
    expect(screen.getByText('Aquí puedes configurar el idioma y subtítulos para los contenidos Iineales.')).toBeInTheDocument();
    expect(screen.getAllByText('Seleccione su idioma').length).toBe(2);
  });

  it('renders dropdown chevron images', () => {
    renderComponent();
    const images = screen.getAllByAltText('Dropdown chevron');
    expect(images.length).toBe(2);
    images.forEach(img => {
      expect(img).toHaveAttribute('src', 'images/LiveTv/ic_chevron_down.png');
    });
  });

  it('handles keyboard events', () => {
    renderComponent();
    
    // Simulate back button keypress
    fireEvent.keyUp(document, { keyCode: 8 });
    expect(mockNavigate).toHaveBeenCalledWith('/settings', { 
      state: { activeTab: 'adjustingProfiles' } 
    });
    
    // Test other keycodes
    fireEvent.keyUp(document, { keyCode: 10009 });
    fireEvent.keyUp(document, { keyCode: 461 });
    
    // Navigate should have been called 3 times total
    expect(mockNavigate).toHaveBeenCalledTimes(3);
  });
});