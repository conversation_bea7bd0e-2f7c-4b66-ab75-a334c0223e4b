import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import AlphaNumeric from '../../Keyboard/AlphaNumericPayment';
import { useNavigate, useLocation } from 'react-router-dom'
import '../../../styles/ManagePayments.css'
import { getAddPayments } from '../../../store/slices/settingsSlice'
import { BackButtonComponent } from '../../../CommonComponent';
const AddPaymentsMobile = props => {
    const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
    const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
    const region = localStorage.getItem('region')
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const { state } = useLocation()
    const [showErrMsg, setShowErrMsg] = useState(false);  
    const [mobileNumber,setMobileNumber] = useState();
    const [otp,setOtp] = useState();
  
    const [inputMode,setInputMode]= useState(1);
    //1 = Mobile number input
    //2 = OTP input
    const [isLoginBtnEnable,setLoginBtnEnable]= useState(true);
    const [otpBtnEnable,setOtpBtnEnable] = useState(true);
    const apilanguage = translations?.language?.[region];
    const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
    const paymentDataForAPI = useSelector(state => state?.settingsReducer?.paymentData,)
    const AddPaymentDataResponse = useSelector(state => state?.settingsReducer?.addPayments?.response,)
    const handleAddMobileNumber = () => {
        setInputMode(2);
        setInputMode(inputMode == 1 ? 2:1);	
        return false;
        dispatch(
          getAddPayments({
            apiUrl: paymentDataForAPI?.buyLink,
            payway: paymentDataForAPI?.gateway,
            hks: userDetails?.session_stringvalue,
            userId: userDetails?.parent_id,
            buyToken: paymentDataForAPI?.buyToken,
            userToken: userDetails?.user_token,
            number: mobileNumber,
            paramType: 'account',
          }),
        )
    }
    const handleMobileOtp = (e) => {
      navigate('/my-settings/my-Accounts/manage-payments/mobile/finish-subscription');	
    }
    const handleGoPreviousPage = e => {
      navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new');
    }  
    useEffect(() => {     
      if(mobileNumber  && mobileNumber.length){
        setLoginBtnEnable(false);
      }else{
        setLoginBtnEnable(true);
      }      
    },[mobileNumber]);
    useEffect(() => {     
      if(otp  && otp.length){
        setOtpBtnEnable(false);
      }else{
        setOtpBtnEnable(true);
      }      
    },[otp]);
  return (
    <div className="paymentMobile g1">     
        <div style={{ float: 'right'}}>
            <BackButtonComponent  id_={'paymentmobile-page-back'} onClick_={e => {handleGoPreviousPage(e)}} text={apilanguage?.atv_back_notification} />
        </div>
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div className="keyboardArea">
          <div className="keyboard-layout keyboardWrapper">
            {
              inputMode == 1 && 
              <AlphaNumeric
                  keyMode="numeric"
                  name='add-payment-mobile'                  
                  onChange={(val) => {setMobileNumber(val) }}
                  val={mobileNumber}
              />
            }
            {
              inputMode == 2 && 
              <AlphaNumeric
                keyMode="numeric"
                name='add-payment-mobile'             
                onChange={(val) => {setOtp(val)}} 
              />
            }            
          </div>
        </div>		
        <div className="textDataArea" style={{marginTop:'100px'}} >
            {inputMode == 1 ? 
            <>
                <p className="title">Numero Telcel</p>
                <input onChange={(e)=>{console.log(e.target.value)}}  onMouseOver={e => {e.target.focus()}}  className="trans-input focusable mobile-input" type="text" name="input-number" placeholder={'Numero a 10 Digitos'} value={mobileNumber} />
                <button disabled={isLoginBtnEnable}  onMouseOver={e => {e.target.focus()}}  className="trans-button btnMobile focusable" onClick={handleAddMobileNumber}	> <span>SIGUIENTE</span> </button>
            </>
            :<>
              <p className="title" style={{textAlign:'center'}}>Codigo de validacion eniviado a tu celular</p>
                <div style={{'display':'flex','flexWrap':'nowrap'}}>
                  <input  onMouseOver={e => {e.target.focus()}}  className=" trans-input focusable mobile-otp" type="text" name="input-number" placeholder={''} value={otp} />
                  <button   onMouseOver={e => {e.target.focus()}}  className="trans-button  btnOtp focusable" >REENVIARPIN</button>
                </div>			
              <button disabled={otpBtnEnable}  className="trans-button focusable btnMobile" onMouseOver={e => {e.target.focus()}} id='trans-otp-button'	onClick={(e)=>{handleMobileOtp()}}>	<span>SIGUIENTE_OTP</span></button>
            </>
            }
          <button className="trans-button focusable btnCancelMobileOtp"  onMouseOver={e => {e.target.focus()}} id='trnas-cancel-button' onClick={e => {
            if(inputMode == 2){
              setInputMode(1);            
            }else if(inputMode == 1){
              handleGoPreviousPage();
            }
            }} > <span>CANCLEAR</span>  </button>
        </div>
      </div>
    </div>
  )
}
export default AddPaymentsMobile