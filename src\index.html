<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="/bootstrap.min.css" rel="stylesheet" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    />
    <title>Claro Video</title>
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
    />
<script type="module">
 import { CURRENT_PLATFORM } from '../src/utils/devicePlatform'
  window.devicePlatform = CURRENT_PLATFORM;
    var a = window.devicePlatform
      document.getElementById("ptName").innerHTML = a.platform
</script>
    <script type="text/javascript">
      window.addEventListener('keydown', function (inEvent) {
        if (window.event) {
          keycode = inEvent.keyCode
        } else if (e.which) {
          keycode = inEvent.which
        }
      })

      window.addEventListener('load', function () {
        // Initialize Spatial Navigation
        SpatialNavigation.init()

        // Adjust zoom level based on screen resolution
        const screenWidth = window.innerWidth
        const screenHeight = window.innerHeight

        if (screenWidth === 1280 && screenHeight === 720) {
          document.body.style.zoom = '0.67'
        } else if (screenWidth === 1920 && screenHeight === 1080) {
          document.body.style.zoom = '1.0' // Default zoom level for 1920x1080
        }
      })
    </script>

    <script
      type="text/javascript"
      src="//cdnjs.cloudflare.com/ajax/libs/lodash.js/0.10.0/lodash.min.js"
    ></script>
    <script src="https://cdn.jsdelivr.net/npm/js-spatial-navigation@1.0.1/spatial_navigation.min.js"></script>
    <script>
      window.addEventListener('load', function () {
        // Initialize
        SpatialNavigation.init()
      })
    </script>
    <script type="text/javascript" src="dist/purify.min.js"></script>
  </head>

  <body style="zoom: 1">
  <h1 id="ptName" style="color: white;"></h1>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
