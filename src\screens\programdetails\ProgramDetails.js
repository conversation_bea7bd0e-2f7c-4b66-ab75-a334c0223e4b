import React, { useEffect, useRef, useState } from 'react'
import './ProgramDetails.scss'
import { useDispatch, useSelector } from 'react-redux'
import moment from 'moment'
import {
  addLiveTvSeriesRecording,
  addLiveTvEpisodeRecording,
  delLiveTvEpisodeRecording,
  delLiveTvSeriesRecording,
  addFavouriteLive,
  delFavouriteLive,
  getAlerts,
  getFavouriteAlerts,
  getClearRecordingState,
  addLiveReminder,
  delLiveReminder,
  getLiveTvRecording,
  clearReminderState,
  getLiveTvSeriesRecordingList,
  getFavouriteLive,
  clearFavouriteRespose
} from '../../store/slices/EpgSlice'
import { useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import {
  getControlPin,
  getLockedChannelAdd,
  getStatusControlPin
} from '../../store/slices/settingsSlice'
import { getAvailableSubtitles } from '../../store/slices/PlayerSlice'
import { COMMON_URL } from '../../utils/environment'
import { pushPlayerInteractionEvent, pushScreenViewEvent } from '../../GoogleAnalytics'
import { CAMBIAR_IDIOMA_DEL_CANAL, CONTENIDO_BLOQUEADO, GRABAR_PROGRAMA, PLAYER, SUBSCRIPTION_CAROUSEL_LIVE } from '../../GoogleAnalyticsConstants'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const ProgramDetails = ({
  hideLanguage,
  setShowLiveControls,
  audioData,
  focusButton,
  subscriptions
}) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const region = localStorage.getItem('region')

  const defaultAudio = useSelector(state => state?.player?.defaultAudio)
  const audioOptions = useSelector(state => state?.player?.audioOptions)
  const subtitleReduxData = useSelector(state => state?.player?.subtitleOptions)
  const player = useSelector(state => state?.player?.playerInstance)
  const liveChannnelInfo = useSelector(
    state => state?.player?.playerinfo?.response
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const securityPinCheck = useSelector(
    state => state?.settingsReducer?.controlPin
  )
  const programdetailsdata = useSelector(
    state => state?.epg?.viewProgramDetailsData?.payload
  )
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const paywayTokenResponse = useSelector(
    state => state?.epg?.paywayToken?.response
  )
  const recordingList = useSelector(
    state => state?.epg?.RecordingList?.response
  )
  const addSeriesRecording = useSelector(
    state => state?.epg?.addSeriesRecording?.response
  )
  const addEpisodeRecording = useSelector(
    state => state?.epg?.addEpisodeRecording?.response
  )
  const delSeriesRecoding = useSelector(
    state => state?.epg?.deleteRecordingSeries?.response
  )
  const delEpisodeRecoding = useSelector(
    state => state?.epg?.deleteRecordingEpisode?.response
  )
  const addSeriesErrors = useSelector(
    state => state?.epg?.recordingSeriesError?.errors
  )
  const addEpisodeErrors = useSelector(
    state => state?.epg?.recordingEpisodeError?.errors
  )
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )
  const reminderList = useSelector(state => state?.epg?.ReminderLive?.response)
  const addReminder = useSelector(
    state => state?.epg?.addReminderList?.response
  )
  const deleteReminder = useSelector(state => state?.epg?.delReminder?.response)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const epgVersion = useSelector(state => state?.epg?.epgVersion)
  const getFavouriteList = useSelector(state => state?.epg?.favouriteLive)
  const lockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelAdd
  )
  const statusControlPin = useSelector(
    state => state?.settingsReducer?.statusControlPin?.response
  )
  const seriesRecordingList = useSelector(
    state => state?.epg?.RecordingListSeries
  )
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )

  const [showAudioAndSubtitles, setShowAudioAndSubtitles] = useState(false)
  const [showRecordingOptions, setShowRecordingOptions] = useState(false)
  const [selectedAudio, setSelectedAudio] = useState(
    JSON?.parse(localStorage?.getItem('selectedAudio')) ?? defaultAudio
  )
  const [eventStatus, setEventStatus] = useState('')
  const [favouriteChannel, setFavouriteChannel] = useState([])
  const [hideCurrentPage, setHideCurrentPage] = useState(false)
  const [showAlert, setShowAlert] = useState(false)
  const [unlockedChannelsCount, setUnlockedChannelsCount] = useState(0)
  const [lockedChannelsCount, setLockedChannelsCount] = useState(0)
  const [epgChannel, setEpgChannel] = useState(
    epgSevenDaysData[1]?.channelResponse
  )
  const [subtitleData, setSubtitleData] = useState('')
  const contentDataRef = useRef(null)

  let groupId = programdetailsdata?.channelData?.group_id
  const recordCheckValue = programdetailsdata?.programData?.event_alf_id

  let isRecordedSeries = []

  const addfavouriteList = useSelector(state => state?.epg?.addfavouriteList)
  const delfavourite = useSelector(state => state?.epg?.delfavourite)
  const lastTouch = localStorage.getItem('lasttouch')

  seriesRecordingList?.map(each =>
    each?.seriesResponse?.recordings?.map(every =>
      every?.channel?.event?.event_alf_id == recordCheckValue
        ? isRecordedSeries.push(every)
        : null
    )
  )

  const isRecordedEpisode = recordingList?.recordings?.filter(
    each => each?.channel?.event?.event_alf_id == recordCheckValue
  )

  const reminderCheck = programdetailsdata?.programData?.id
  const isReminder =
    typeof reminderList != 'string' &&
    reminderList?.filter(each => each?.event_id == reminderCheck)

  const isLastLockChannel =
    epgSevenDaysData?.[1]?.channelResponse?.length - 1 !=
    lockedChannelsList?.length

  const today = new Date()
  const now =
    today.getFullYear() +
    '/' +
    (today.getMonth() + 1 < 10
      ? '0' + (today.getMonth() + 1)
      : today.getMonth() + 1) +
    '/' +
    (today.getDate() < 10 ? '0' + today.getDate() : today.getDate()) +
    ' ' +
    today.toLocaleTimeString('en-US', { hour12: false })

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const payway = paywayTokenResponse?.paqs?.paq?.filter(
    each =>
      each?.groups?.includes(groupId) &&
      each?.npvrstorage != 0 &&
      each?.timeshift != 0
  )

  let totalStorage = 0
  paywayTokenResponse?.paqs?.paq?.map(each =>
    Number(each?.npvrstorage) > 0
      ? (totalStorage += Number(each?.npvrstorage))
      : 0
  )
  const usedStorage = recordingList?.time_used?.total
  const usedStorageConvertToHours = (usedStorage / 3600).toFixed(1)

  const programTime = programdetailsdata?.programData?.date_begin?.split(' ')
  const startTime = programTime?.[1]?.split(':')
  const startTimeSeconds =
    +startTime?.[0] * 3600 + +startTime?.[1] * 60 + +startTime?.[2]
  const currentTime = today
    .toLocaleTimeString('en-US', { hour12: false })
    ?.split(':')
  const currentTimeSeconds =
    +currentTime?.[0] * 3600 + +currentTime?.[1] * 60 + +currentTime?.[2]

  const isLastUnlockedChannel = () => {
    // Check if there's only one unlocked channel left
    return unlockedChannelsCount === 1
  }

  const handleChangeAudio = (playerInstance, value) => {
    playerInstance?.setAudio(value)
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const handleAddReminder = e => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      e?.target?.innerText?.toLowerCase()
    )
    if (watchFree) {
      navigate('/EPconfirmation', {
        state: { page: 'livePlayer', gaContentData: contentDataRef.current }
      })
    }
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_id: userDetails?.user_id,
      user_hash: userDetails?.session_userhash,
      channel_id: programdetailsdata?.programData?.channel_id,
      type:
        programdetailsdata?.programData?.ext_series_id == null
          ? 'epg_event'
          : 'epg_series',
      event_id: programdetailsdata?.programData?.id,
      exp_date:
        programdetailsdata?.programData?.date_begin ??
        programdetailsdata?.programData?.begintime
    }
    dispatch(addLiveReminder(payload))
  }

  const handleRemovalSchedule = e => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      e?.target?.innerText?.toLowerCase()
    )
    if (watchFree) {
      navigate('/EPconfirmation', {
        state: { page: 'livePlayer', gaContentData: contentDataRef.current }
      })
    }
    const payload = {
      reminder_id: isReminder?.[0]?.id,
      user_hash: userDetails?.session_userhash,
      hks: userDetails?.session_stringvalue
    }
    dispatch(delLiveReminder(payload))
  }

  // Lock and Unlock channel

  const getLockedChannelsIcon = item => {
    const found = lockedChannelsList?.find(
      each => each?.id === item || each?.id === item?.id
    )

    if (found) {
      return true
    }
    return false
  }

  const isBlocked = getLockedChannelsIcon(
    programdetailsdata?.channelData?.group_id
  )
  let sameChannelPlay =
    programdetailsdata?.channelData?.group_id ==
    localStorage.getItem('live-channel-id')

  const handleRecordingSeries = e => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      e?.target?.innerText?.toLowerCase()
    )
    const payload = {
      payway_token: (payway && payway[0]?.payway_token) || '',
      user_id: userDetails?.user_id,
      group_id: programdetailsdata?.channelData?.group_id,
      channel_id: programdetailsdata?.programData?.channel_id,
      user_token: userDetails?.user_token,
      event_alf_id: programdetailsdata?.programData?.event_alf_id
    }

    if (totalStorage > usedStorageConvertToHours) {
      if (isBlocked && !watchFree) {
        // Navigate to security pin page
        contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
        navigate('/settings/profile-settings/check-pin', {
          state: {
            pageName: 'livePlayerRecord',
            recordType: 'Series',
            payload: payload,
            eventStatus: eventStatus,
            gaContentData: contentDataRef.current
          }
        })
      } else {
        dispatch(addLiveTvSeriesRecording(payload))
      }
    } else {
      dispatch(
        getAlerts({
          message: `${truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_EspacioInsuficiente',
            30
          )} |
          ${truncateText(
            'TvEnVivo_Notificacion_TextoCuerpo_EspacioInsuficiente',
            70
          )}`,
          image: 'images/warning.png',
          status: 'storagealert'
        })
      )
    }
  }

  const handleRecordingEpisode = (e, data) => {
    e.preventDefault()
    pushPlayerInteractionEvent(
      { modulo_name: GRABAR_PROGRAMA, ...contentDataRef.current },
      e?.target?.innerText?.toLowerCase()
    )
    const offsetValue = currentTimeSeconds - startTimeSeconds
    const payload = {
      payway_token: (payway && payway[0]?.payway_token) || '',
      user_id: userDetails?.user_id,
      group_id: programdetailsdata?.channelData?.group_id,
      channel_id: programdetailsdata?.programData?.channel_id,
      user_token: userDetails?.user_token,
      event_alf_id: programdetailsdata?.programData?.event_alf_id,
      offset: data && offsetValue
    }

    if (totalStorage > usedStorageConvertToHours) {
      if (isBlocked && !watchFree) {
        // Navigate to security pin page
        contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
        navigate('/settings/profile-settings/check-pin', {
          state: {
            pageName: 'livePlayerRecord',
            recordType: 'Episode',
            payload: payload,
            eventStatus: eventStatus,
            gaContentData: contentDataRef.current
          }
        })
      } else {
        dispatch(addLiveTvEpisodeRecording(payload))
      }
    } else {
      dispatch(
        getAlerts({
          message: `${truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_EspacioInsuficiente',
            30
          )} |
          ${truncateText(
            'TvEnVivo_Notificacion_TextoCuerpo_EspacioInsuficiente',
            70
          )}`,
          image: 'images/warning.png',
          status: 'storagealert'
        })
      )
    }
  }

  const handleDeleteRecordingEpisode = e => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      e?.target?.innerText?.toLowerCase()
    )
    const payload =
      isRecordedEpisode?.[0]?.actions?.delete ??
      isRecordedSeries?.[0]?.actions?.delete
    if (isBlocked && !watchFree) {
      // Navigate to security pin page
      contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
      navigate('/settings/profile-settings/check-pin', {
        state: {
          pageName: 'livePlayerRecord',
          recordType: 'Episode',
          payload: payload,
          eventStatus: eventStatus,
          status: 'unlockEvent',
          gaContentData: contentDataRef.current
        }
      })
    } else {
      dispatch(delLiveTvEpisodeRecording(payload))
    }
  }

  const handleDeleteRecordingSeries = e => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      e?.target?.innerText?.toLowerCase()
    )
    const payload = isRecordedSeries?.[0]?.actions?.delete
    if (isBlocked && !watchFree) {
      // Navigate to security pin page
      contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
      navigate('/settings/profile-settings/check-pin', {
        state: {
          pageName: 'livePlayerRecord',
          recordType: 'Episode',
          payload: payload,
          eventStatus: eventStatus,
          status: 'unlockEvent',
          gaContentData: contentDataRef.current
        }
      })
    } else {
      dispatch(delLiveTvSeriesRecording(payload))
    }
  }

  useEffect(() => {
    const payload = {
      hks: userDetails?.session_stringvalue,
      user_id: userDetails?.user_id,
      user_token: userDetails?.user_token
    }
    dispatch(getControlPin(payload))
    dispatch(
      getStatusControlPin({
        hks: userDetails?.session_stringvalue,
        userId: userDetails?.user_id
      })
    )
    setShowAudioAndSubtitles(audioData.current)
    pushScreenViewEvent({screenName:'live_program_details',screenData:userDetails,prevScreenName:'live_player'})

    return () => {
      contentDataRef.current = null
    }
  }, [])

  useEffect(() => {
    // Count unlocked channels
    const unlockedCount = epgChannel.filter(
      channel => !getLockedChannelsIcon(channel.group_id)
    )?.length
    const lockedCount = epgChannel.filter(channel =>
      getLockedChannelsIcon(channel.group_id)
    )?.length
    setUnlockedChannelsCount(unlockedCount)
    setLockedChannelsCount(lockedCount)
  }, [epgChannel, getLockedChannelsIcon])

  const handleBlockChannelButtonClick = e => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      e?.target?.innerText?.toLowerCase()
    )
    const isLastChannel = isLastUnlockedChannel
    let alertTimeout
    if (isLastChannel) {
      checkSecurityPin(e, 'Lock', programdetailsdata?.channelData)
    } else {
      navigate('/livePlayer', {
        state: { showControls: 'live' },
        replace: true
      })
      setHideCurrentPage(true)
      setShowAlert(true)

      alertTimeout = setTimeout(() => {
        setShowAlert(false)
      }, 12000)

      setTimeout(() => {
        clearTimeout(alertTimeout)
      }, 12000)
    }
  }

  const checkSecurityPin = (e, data, item) => {
    e.preventDefault()

    if (
      !securityPinCheck?.response?.profiles?.[0]?.channel?.active ||
      statusControlPin?.pin_channel?.status == 0
    ) {
      navigate('/settings/profile-settings', {
        state: {
          data,
          item,
          pageName: 'parentalControl',
          returnPageName: '/livePlayer'
        }
      })
    } else if (data == 'Lock') {
      const payload = {
        hks: userDetails?.session_stringvalue,
        group_id:
          programdetailsdata?.channelData?.group_id ??
          programdetailsdata?.channelData?.channel_group_id,
        user_hash: userDetails?.session_userhash
      }
      isLastLockChannel
        ? dispatch(getLockedChannelAdd(payload))
        : (dispatch(
            getAlerts({
              message: `${translations?.language?.[region]?.notBlockAllChannels_alert_title_label} |
        ${translations?.language?.[region]?.notBlockAllChannels_alert_description_label}`,
              image: 'images/warning.png',
              status: 'reminder'
            })
          ),
          hideLanguage(false))
    } else {
      contentDataRef.current['modulo_name'] = CONTENIDO_BLOQUEADO
      contentDataRef.current['content_section'] = PLAYER
      navigate('/my-settings/help-And-Settings/security-pin/configure', {
        state: {
          data,
          item,
          gaContentData: contentDataRef.current,
          pageName: '/livePlayer'
        }
      })
    }
  }

  const formatDate = date => {
    return moment(date, 'YYYY-MM-DDTHH:mm:ss').format('HH:mm[hs]')
  }

  const formatDuration = duration => {
    return moment(duration, 'HH:mm:ss').format(
      duration?.startsWith('00') ? `mm [min]` : `H[h] mm [min]`
    )
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009 || keycode === 8) {
      if (showAudioAndSubtitles && audioData.current == false) {
        setShowAudioAndSubtitles(false)
      } else if (showRecordingOptions) {
        setShowRecordingOptions(false)
      } else {
        hideLanguage(false)
        focusButton && focusButton('idioma')
        setShowLiveControls && setShowLiveControls('live')
        audioData.current = false
      }
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode === 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
) {
      if (showAudioAndSubtitles && audioData.current == false) {
        setShowAudioAndSubtitles(false)
      } else if (showRecordingOptions) {
        setShowRecordingOptions(false)
      } else {
        hideLanguage(false)
        focusButton && focusButton('idioma')
        setShowLiveControls && setShowLiveControls('live')
        audioData.current = false
      }
    }
  }

  const handleRegressar = () => {
    hideLanguage(false)
    audioData.current = false
    focusButton && focusButton('idioma')
    setShowLiveControls && setShowLiveControls('live')
  }

  const handleOptionsPanel = event => {
    pushPlayerInteractionEvent(
      { modulo_name: CAMBIAR_IDIOMA_DEL_CANAL, ...contentDataRef.current },
      CAMBIAR_IDIOMA_DEL_CANAL
    )
    setShowAudioAndSubtitles(true)
    event.preventDefault()
  }

  const handleShowRecording = () => {
    pushPlayerInteractionEvent(
      { modulo_name: GRABAR_PROGRAMA, ...contentDataRef.current },
      GRABAR_PROGRAMA
    )
    setShowRecordingOptions(!showRecordingOptions)
  }

  const buttonName = () => {
    if (isRecordedSeries?.length > 0) {
      if (eventStatus == 'Present Event' || 'Future Event') {
        return (
          translations?.language?.[region]
            ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion ??
          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion'
        )
      } else {
        return (
          translations?.language?.[region]
            ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion ??
          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion'
        )
      }
    } else if (isRecordedEpisode?.length > 0) {
      if (eventStatus == 'Present Event' || 'Future Event') {
        return (
          translations?.language?.[region]
            ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion ??
          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion'
        )
      } else {
        return (
          translations?.language?.[region]
            ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion ??
          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoCancelarGrabacion'
        )
      }
    } else {
      return (
        translations?.language?.[region]
          ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoGrabar ??
        'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoGrabar'
      )
    }
  }

  useEffect(() => {
    const favouriteChannelFilter = getFavouriteList?.response?.groups?.filter(
      item => item?.id == programdetailsdata?.channelData?.group_id
    )
    setFavouriteChannel(favouriteChannelFilter)
  }, [getFavouriteList])

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [showRecordingOptions, showAudioAndSubtitles, keypresshandler]) //In the initial load, because of dependency it is not called, hence removed.

  useEffect(() => {
    subtitleReduxData && setSubtitleData(subtitleReduxData)
  }, [subtitleReduxData])

  useEffect(() => {
    if (programdetailsdata && Object.keys(programdetailsdata)?.length) {
      contentDataRef.current = {
        user_id:
          userDetails?.user_id || loginInfo?.user_id || registerInfo?.user_id,
        parent_id: userDetails?.parent_id,
        sign_up_method: 'correo electronico',
        suscriptions: subscriptions,
        user_type: watchFree ? 'anonimo' : 'registrado',
        device: COMMON_URL?.device_type,
        device_model: COMMON_URL?.device_model,
        device_name: COMMON_URL?.device_name,
        authpn: COMMON_URL?.authpn,
        content_subsection: 'no aplica',
        content_section: 'player',
        country:
          userDetails?.country_code?.toLowerCase() ||
          loginInfo?.country_code?.toLowerCase() ||
          registerInfo?.country_code?.toLowerCase(),
        content_type: 'tv',
        content_availability: 'por suscripcion',
        provider:
          programdetailsdata?.channelData?.group?.common?.proveedor_code,
        channel_name: programdetailsdata?.channelData?.name?.toLowerCase(),
        content_id: programdetailsdata?.channelData?.group_id,
        content_name:
          programdetailsdata?.programData?.ext_original_name?.toLowerCase(),
        content_category:
          programdetailsdata?.programData?.dvb_content?.toLowerCase(),
        modulo_name: SUBSCRIPTION_CAROUSEL_LIVE
      }
    }
    if (!programdetailsdata?.fromSearch) {
      if (
        (now > programdetailsdata?.programData?.date_begin &&
          now < programdetailsdata?.programData?.date_end) ||
        programdetailsdata?.fromLivePlayer
      ) {
        setEventStatus('Present Event')
      } else if (now < programdetailsdata?.programData?.date_begin) {
        setEventStatus('Future Event')
      } else {
        setEventStatus('Past Event')
      }
    } else {
      if (
        now > programdetailsdata?.programData?.begintime &&
        now < programdetailsdata?.programData?.endtime
      ) {
        setEventStatus('Present Event')
      } else if (now < programdetailsdata?.programData?.begintime) {
        setEventStatus('Future Event')
      } else {
        setEventStatus('Past Event')
      }
    }
  }, [programdetailsdata])

  useEffect(() => {
    if (addSeriesRecording?.success) {
      if (eventStatus == 'Present Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabando', 30)} |
            ${
              translations?.language?.[region]
                ?.recording_alert_description_presentSeason_label
            }`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else if (eventStatus == 'Past Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabado', 30)} |
            ${
              translations?.language?.[region]
                ?.recording_alert_description_pastSeason_label
            }`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_PorGrabar',
              30
            )} |
            ${
              translations?.language?.[region]
                ?.recording_alert_description_futureSeason_label
            }`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      }
      hideLanguage(false)
    } else if (addEpisodeRecording?.success) {
      if (eventStatus == 'Present Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabando', 30)} |
            ${
              translations?.language?.[region]
                ?.recording_alert_description_presentEpisode_label
            }`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else if (eventStatus == 'Past Event') {
        dispatch(
          getAlerts({
            message: `${truncateText('Player_Boton_TextoAccion_Grabado', 30)} |
            ${
              translations?.language?.[region]
                ?.recording_alert_description_presentEpisode_label
            }`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      } else {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_PorGrabar',
              30
            )} |
            ${
              translations?.language?.[region]
                ?.recording_alert_description_futureEpisode_label
            }`,
            image: 'images/Program_Details_Icons/record_icon.png',
            status: 'add'
          })
        )
      }
      hideLanguage(false)
    } else if (delEpisodeRecoding?.success) {
      if (eventStatus == 'Present Event') {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_GrabacionCancelada',
              30
            )} |
            ${truncateText(
              'TvEnVivo_Notificacion_TextoCuerpo_GrabacionCancelada_Episodio',
              70
            )}`,
            image: 'images/Record_Icon.png',
            status: 'delete'
          })
        )
      } else if (eventStatus == 'Past Event') {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_GrabacionEliminada',
              30
            )} |
              ${
                translations?.language?.[region]
                  ?.deleteRecording_alert_description_pastEpisode_label
              }`,
            image: 'images/Record_Icon.png',
            status: 'delete'
          })
        )
      } else {
        dispatch(
          getAlerts({
            message: `${translations?.language?.[region]?.cancelRecording_alert_title_futureEvent_label} |
            ${translations?.language?.[region]?.cancelRecording_alert_description_episode_label}`,
            image: 'images/Record_Icon.png',
            status: 'delete'
          })
        )
      }
      hideLanguage(false)
    } else if (delSeriesRecoding?.success) {
      if (eventStatus == 'Present Event') {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_GrabacionCancelada',
              30
            )} |
            La serie se elimino de la sección Mis contenidos`,
            image: 'images/Record_Icon.png',
            status: 'delete'
          })
        )
      } else if (eventStatus == 'Past Event') {
        dispatch(
          getAlerts({
            message: `${truncateText(
              'TvEnVivo_Notificacion_TextoTitulo_GrabacionEliminada',
              30
            )} |
            ${truncateText(
              'TvEnVivo_Notificacion_TextoCuerpo_GrabacionCancelada_Evento',
              70
            )}`,
            image: 'images/Record_Icon.png',
            status: 'delete'
          })
        )
      } else {
        dispatch(
          getAlerts({
            message: `${translations?.language?.[region]?.cancelRecording_alert_title_futureEvent_label} |
            ${translations?.language?.[region]?.cancelRecording_alert_description_futureSeason_label}`,
            image: 'images/Record_Icon.png',
            status: 'delete'
          })
        )
      }
      hideLanguage(false)
    } else if (
      addSeriesErrors?.[0]?.message == 'Time Limit Exceeded' ||
      addEpisodeErrors?.[0]?.message == 'Time Limit Exceeded'
    ) {
      dispatch(
        getAlerts({
          message: `${translations?.language?.[region]?.atv_timeshift_value_exceeded_title} |
        ${translations?.language?.[region]?.atv_timeshift_value_exceeded_desc}`,
          status: 'error'
        })
      )
      hideLanguage(false)
    } else if (addSeriesErrors || addEpisodeErrors) {
      dispatch(
        getAlerts({
          message: `${translations?.language?.[region]?.errorRecording_alert_title_label} |
        ${translations?.language?.[region]?.errorRecording_alert_description_label}`,
          status: 'error'
        })
      )
    } else if (addReminder) {
      dispatch(
        getAlerts({
          message: `${truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_Recordatorio',
            30
          )} |
          ${truncateText(
            'TvEnVivo_Notificacion_TextoCuerpo_Recordatorio',
            70
          )}`,
          image: 'images/Program_Details_Icons/reminder_alert_image.png',
          status: 'reminder'
        })
      )
      hideLanguage(false)
    } else if (deleteReminder) {
      dispatch(
        getAlerts({
          message: `${truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_RecordatorioEliminado',
            30
          )} |
          ${truncateText(
            'TvEnVivo_Notificacion_TextoCuerpo_RecordatorioEliminado',
            70
          )}`,
          image: 'images/Program_Details_Icons/reminder_alert_image.png',
          status: 'reminder'
        })
      )
      hideLanguage(false)
    } else if (lockchannel?.msg === 'OK') {
      const alertDetailLabel = truncateText(
        'TvEnVivo_Notificacion_TextoTitulo_CanalBloqueado',
        30
      )
      const replacementText = `${programdetailsdata?.channelData?.name} ${programdetailsdata?.channelData?.number}`
      const message1 = alertDetailLabel?.replace('@canal', replacementText)
      const message2 = ` | ${truncateText(
        'TvEnVivo_Notificacion_TextoCuerpo_CanalBloqueado',
        70
      )}`

      dispatch(
        getAlerts({
          image: 'images/lock_icon_liveTV.png',
          message: `${message1}`,
          message2: `${message2}`,
          status: 'lock',
          subStatus: 'add-lock'
        })
      )
      hideLanguage(false)
    }
    dispatch(getClearRecordingState())
    dispatch(clearReminderState())
  }, [
    addSeriesRecording,
    addEpisodeRecording,
    delEpisodeRecoding,
    delSeriesRecoding,
    addSeriesErrors,
    addEpisodeErrors,
    addReminder,
    deleteReminder,
    lockchannel
  ])

  useEffect(() => {
    var seriesApiCallData = []
    dispatch(getLiveTvRecording({ user_token: userDetails?.user_token })) &&
      new Promise(async (resolve, reject) => {
        const recordingPromises = []
        recordingList?.series?.map(each => {
          const promise = fetch(
            `${COMMON_URL.BASE_URL}/services/recordings/v1/series/list?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_type=${COMMON_URL.device_type}&device_model=${COMMON_URL.device_model}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_id=${COMMON_URL.device_id}&format=json&by_serie=0
            &user_token=${userDetails?.user_token}&user_id=${userDetails?.parent_id}&serie_id=${each?.serie_id}
            &season_id=${each?.season_id}&group_id=${each?.group_id}&language=${each?.language}&region=${region}`
          ).then(async data => {
            let value = await data.json()
            seriesApiCallData.push({
              seriesResponse: value?.response
            })
          })
          recordingPromises.push(promise)
        })
        Promise.all(recordingPromises)
          .then(() => {
            setTimeout(() => {
              resolve(dispatch(getLiveTvSeriesRecordingList(seriesApiCallData)))
            }, 500)
          })
          .catch(reject)
      })
  }, [addSeriesRecording, delEpisodeRecoding, delSeriesRecoding])

  const handleSelectAudio = (event, value) => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      `audio ${event?.target?.innerText?.toLowerCase()}`
    )
    setSelectedAudio(value)
    handleChangeAudio(player, value?.id)
    localStorage.setItem('selectedAudio', JSON?.stringify(value))
    event?.preventDefault()
  }

  const handleSubtitle = (event, value, enable) => {
    pushPlayerInteractionEvent(
      contentDataRef.current,
      `subtitulo ${event?.target?.innerText}`
    )
    if (enable) {
      localStorage.setItem('selectedSubtitle', JSON?.stringify(value))
      player?.subtitles?.enable(value?.id)
    } else {
      localStorage.setItem('selectedSubtitle', '')
      player?.subtitles
        ?.list()
        .filter(sub => sub.enabled)
        .forEach(enabledSub => player?.subtitles?.disable(enabledSub.id))
      dispatch(getAvailableSubtitles(player?.subtitles?.list()))
    }
    event?.preventDefault()
  }

  useEffect(() => {
    if (addfavouriteList?.msg == 'OK') {
      localStorage.setItem('lasttouch', addfavouriteList?.lasttouch?.favorited)
      dispatch(
        getFavouriteLive({
          epg_version: epgVersion,
          hks: userDetails?.session_stringvalue,
          user_id: watchFree ? 0 : userDetails?.user_id,
          user_token: userDetails?.user_token,
          lasttouch: lastTouch,
          user_hash: userDetails?.session_userhash
        })
      )
      dispatch(
        getFavouriteAlerts({
          message: `${truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_CanalFavoriteado',
            30
          )} | ${truncateText(
            'TvEnVivo_Notificacion_TextoCuerpo_CanalFavoriteado',
            70
          )}`,
          displayMyContentButton: true
        })
      )
      hideLanguage(false)
      dispatch(clearFavouriteRespose())
    } else if (delfavourite?.msg == 'OK') {
      localStorage.setItem('lasttouch', delfavourite?.lasttouch?.favorited)
      dispatch(
        getFavouriteLive({
          epg_version: epgVersion,
          hks: userDetails?.session_stringvalue,
          user_id: watchFree ? 0 : userDetails?.user_id,
          user_token: userDetails?.user_token,
          lasttouch: delfavourite?.lasttouch?.favorited ?? lastTouch,
          user_hash: userDetails?.session_userhash
        })
      )
      dispatch(
        getFavouriteAlerts({
          message: `${truncateText(
            'TvEnVivo_Notificacion_TextoTitulo_CanalFavoritoEliminado',
            30
          )} | ${truncateText(
            'TvEnVivo_Notificacion_TextoCuerpo_CanalFavoritoEliminado',
            70
          )}`,
          displayMyContentButton: false
        })
      )
      hideLanguage(false)
      dispatch(clearFavouriteRespose())
    }
  }, [addfavouriteList, delfavourite])

  const handleFavourite = e => {
    if (watchFree) {
      navigate('/EPconfirmation', {
        state: { page: 'livePlayer', gaContentData: contentDataRef.current }
      })
    } else if (favouriteChannel?.length == 0) {
      pushPlayerInteractionEvent(
        contentDataRef.current,
        e?.target?.innerText?.toLowerCase()
      )
      dispatch(
        addFavouriteLive({
          epg_version: epgVersion,
          user_hash: userDetails?.session_userhash,
          user_id: userDetails?.parent_id,
          user_token: userDetails?.user_token,
          object_id: programdetailsdata?.channelData?.group_id
        })
      )
    } else {
      pushPlayerInteractionEvent(
        contentDataRef.current,
        e?.target?.innerText?.toLowerCase()
      )
      dispatch(
        delFavouriteLive({
          epg_version: epgVersion,
          user_hash: userDetails?.session_userhash,
          user_id: userDetails?.parent_id,
          user_token: userDetails?.user_token,
          object_id: programdetailsdata?.channelData?.group_id
        })
      )
    }
  }

  const handleLabelTranslate = item => {
    switch (item) {
      case 'EN':
      case 'ENG':
        return 'Inglés'
      case 'ES':
      case 'SPA':
        return 'Español'
      case 'PT':
        return 'Portugués'
      default:
        return 'Por Defecto'
    }
  }

  return (
    <>
      {showAlert && (
        <div className="lastchannel-alert-message-container">
          <div className="lastchannel-alert-message">
            <img
              src="images/warning.png"
              alt="Warning Icon"
              className="warning-icon"
            />
            <span>
              <p>
                {translations?.language?.[region]
                  ?.notBlockAllChannels_alert_title_label &&
                translations?.language?.[region]
                  ?.notBlockAllChannels_alert_description_label
                  ? `${translations.language[region].notBlockAllChannels_alert_title_label} |
      ${translations.language[region].notBlockAllChannels_alert_description_label} `
                  : 'Acción no realizada | No es posible bloquear todos los canales'}
              </p>
            </span>
          </div>
        </div>
      )}

      {!hideCurrentPage && (
        <div id="programDetailsId" className="program-details-container">
          <div className="program-description">
            {/* <button
          id="back-button"
          data-sn-right={'.program-record'}
          className="back-indicator focusable"
          onClick={handleRegressar}
        >
         <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
          <img className="back-image" src={'images/back_button.png'}/>
          <p className="back-text">{translations?.language?.[region]?.atv_back_notification}</p>
        </button> */}
            <button
              className="back-indicator-button focusable"
              id="back-button"
              data-sn-right={'.program-record'}
              onKeyUp={event =>
                (event?.keyCode === 13 || event?.key == 'Enter') &&
                handleRegressar()
              }
            >
              <img
                className="yellow-indicator-button"
                src={'images/yellow_shortcut.png'}
              />
              <img
                className="back-indicator-image"
                src={'images/back_button.png'}
              />
              <span>
                {truncateText('BotonShortcut_TextoTitulo_Regresar', 30)}
              </span>
            </button>
            {programdetailsdata && (
              <div className="program-description-container">
                <h1
                  className={
                    programdetailsdata?.programData?.name?.length <= 31
                      ? 'program-details-title'
                      : 'program-title1'
                  }
                >
                  {programdetailsdata?.programData?.name}
                </h1>
                <span className="program-status-label">
                  {eventStatus === 'Present Event' && (
                    <img
                      src="images/LiveTv/tag_ahora.png"
                      alt="Present Event"
                    />
                  )}
                  {eventStatus === 'Past Event' && (
                    <img src="images/LiveTv/tag_emitido.png" alt="Past Event" />
                  )}
                  {eventStatus === 'Future Event' && (
                    <img
                      src="images/LiveTv/tag_mastarde.png"
                      alt="Future Event"
                    />
                  )}
                </span>
                <div className="program-details-category">
                  {programdetailsdata?.programData?.dvb_content && (
                    <span
                      className={`program-category ${
                        programdetailsdata?.programData?.dvb_content
                          ? 'no-content'
                          : ''
                      }`}
                    >
                      {programdetailsdata?.programData?.dvb_content}
                    </span>
                  )}
                  {(programdetailsdata?.programData?.date_end ??
                    programdetailsdata?.programData?.endtime) && (
                    <span
                      className={`program-year ${
                        !programdetailsdata?.programData?.dvb_content
                          ? 'no-content'
                          : ''
                      }`}
                    >
                      {programdetailsdata?.programData?.date_end?.slice(0, 4) ??
                        programdetailsdata?.programData?.endtime?.slice(0, 4)}
                    </span>
                  )}
                  <span className="program-age-alert">
                    {` ${programdetailsdata?.programData?.parental_rating} Años`}
                  </span>

                  <span className="program-broadcast-schedule">
                    {`${formatDate(
                      programdetailsdata?.programData?.date_begin ??
                        programdetailsdata?.programData?.begintime
                    )} a ${formatDate(
                      programdetailsdata?.programData?.date_end ??
                        programdetailsdata?.programData?.endtime
                    )}`}
                  </span>
                  <span className="program-duration">
                    {formatDuration(programdetailsdata?.programData?.duration)}
                  </span>
                </div>
                <p className="program-synopsis">
                  {programdetailsdata?.programData?.description}
                </p>
                <span className="program-description-details">
                  {programdetailsdata?.programData?.ext_director && (
                    <span className="director-details">
                      <span className="program-description-title">
                        {truncateText('Metadata_TextoDirector', 30) + ':'}
                      </span>
                      <span>
                        {programdetailsdata?.programData?.ext_director}
                      </span>
                    </span>
                  )}
                  {programdetailsdata?.programData?.talent && (
                    <span
                      className={`protagonists-details ${
                        programdetailsdata?.programData?.ext_director
                          ? 'extra-details'
                          : ''
                      }`}
                    >
                      <span className="program-description-title">
                        {truncateText('Metadata_TextoProtagonistas', 30) + ':'}
                      </span>
                      <span>
                        {programdetailsdata?.programData?.talent &&
                          programdetailsdata?.programData?.talent
                            .split(',')
                            .slice(0, 2)
                            .join(', ')}
                        {programdetailsdata?.programData?.talent &&
                        programdetailsdata?.programData?.talent?.split(',')
                          ?.length > 2 ? (
                          <span>, ...</span>
                        ) : (
                          ''
                        )}
                      </span>
                    </span>
                  )}
                  {programdetailsdata?.programData?.ext_country && (
                    <span
                      className={`country-details ${
                        programdetailsdata?.programData?.talent ||
                        programdetailsdata?.programData?.ext_director
                          ? 'extra-details'
                          : ''
                      }`}
                    >
                      <span className="program-description-title">
                        {truncateText('Metadata_TextoPais', 30) + ':'}
                      </span>
                      <span>
                        {programdetailsdata?.programData?.ext_country}
                      </span>
                    </span>
                  )}
                </span>
              </div>
            )}
          </div>
          <div className="program-options">
            <div className="program-channel-details">
              <span className="program-channel-number">
                {programdetailsdata?.fromLivePlayer
                  ? liveChannnelInfo?.group?.common?.extendedcommon?.media
                      ?.channel_number
                  : programdetailsdata?.channelData?.number < 100
                  ? programdetailsdata?.channelData?.number < 10
                    ? '00' + programdetailsdata?.channelData?.number
                    : '0' + programdetailsdata?.channelData?.number
                  : programdetailsdata?.channelData?.number}
              </span>
              <div className="program-channel-logo">
                {/* {programdetailsdata?.fromLivePlayer ? (
              liveChannnelInfo?.group?.common?.extendedcommon?.media
                ?.originaltitle
            ) :  */}
                {programdetailsdata?.channelData?.image ? (
                  <LazyLoadImage src={programdetailsdata?.channelData?.image} />
                ) : (
                  <LazyLoadImage src={'images/channel_placeholder.png'} />
                )}
              </div>
            </div>
            {showAudioAndSubtitles && (
              <div className="channel-options-label">
                {truncateText(
                  'TvEnVivo_PanelOpciones_MenuOpciones_TextoIdioma',
                  30
                )}
              </div>
            )}
            {!showAudioAndSubtitles &&
            ((payway?.[0]?.npvrstorage > 0 && payway?.[0]?.timeshift > 0) ||
              eventStatus == 'Future Event') ? (
              <div className="program-options-name">
                <span>
                  {showRecordingOptions
                    ? truncateText(
                        'TvEnVivo_PanelOpciones_MenuOpciones_TextoTituloGrabarPrograma',
                        30
                      )
                    : truncateText(
                        'TvEnVivo_PanelOpciones_MenuOpciones_TextoTitulo1',
                        30
                      )}
                </span>
              </div>
            ) : null}

            {!showAudioAndSubtitles && !showRecordingOptions && (
              <>
                {payway?.[0]?.npvrstorage > 0 &&
                payway?.[0]?.timeshift > 0 &&
                programdetailsdata?.channelData?.group?.common?.timeshift !==
                  null &&
                programdetailsdata?.programData?.ext_recordable == '1' ? (
                  <button
                    autoFocus={true}
                    data-sn-left={'#back-button'}
                    className="program-record focusable"
                    onClick={
                      isRecordedEpisode?.length > 0
                        ? handleDeleteRecordingEpisode
                        : !programdetailsdata?.programData?.ext_series_id &&
                          eventStatus != 'Present Event'
                        ? handleRecordingEpisode
                        : handleShowRecording
                    }
                  >
                    <div className="record-icon">
                      <img src="images/Program_Details_Icons/white_recording_icon.png" />
                    </div>
                    <div className="record-program">{buttonName()}</div>
                  </button>
                ) : null}
                {eventStatus == 'Future Event' ? (
                  <div>
                    {typeof reminderList == 'string' ||
                    isReminder?.length == 0 ? (
                      <button
                        data-sn-left={'#back-button'}
                        className="channel-favorites focusable"
                        onClick={e => handleAddReminder(e)}
                      >
                        <div className="channel-favorites-icon">
                          <img src="images/Program_Details_Icons/clock_icon.png" />
                        </div>
                        <div className="channel-favorites-text">
                          {truncateText(
                            'TvEnVivo_PanelOpciones_MenuOpciones_Opcion2_TextoRecordatorio',
                            30
                          )}
                        </div>
                      </button>
                    ) : (
                      <button
                        data-sn-left={'#back-button'}
                        className="channel-favorites focusable"
                        onClick={e => handleRemovalSchedule(e)}
                      >
                        <div className="channel-favorites-icon">
                          <img src="images/Program_Details_Icons/clock_icon.png" />
                        </div>
                        <div className="channel-favorites-text">
                          {truncateText(
                            'TvEnVivo_PanelOpciones_MenuOpciones_Opcion2_TextoQuitarRecordatorio',
                            30
                          )}
                        </div>
                      </button>
                    )}
                  </div>
                ) : null}
                <div className="channel-options">
                  <span className="channel-options-name">
                    {truncateText(
                      'TvEnVivo_PanelOpciones_MenuOpciones_TextoTitulo2',
                      30
                    )}
                  </span>
                </div>
                <button
                  data-sn-left={'#back-button'}
                  className="channel-favorites focusable"
                  onClick={e => handleFavourite(e)}
                  autoFocus={
                    !payway?.[0]?.npvrstorage > 0 ||
                    !payway?.[0]?.timeshift > 0 ||
                    !programdetailsdata?.channelData?.group?.common
                      ?.timeshift ||
                    programdetailsdata?.programData?.ext_recordable == 0
                  }
                >
                  <div className="channel-favorites-icon">
                    <img src="images/programDetails_favourite_Icon.png" />
                  </div>
                  <div className="channel-favorites-text">
                    {favouriteChannel?.length == 0 &&
                      truncateText(
                        'TvEnVivo_PanelOpciones_MenuOpciones_Opcion3_TextoFavorito',
                        30
                      )}
                    {favouriteChannel?.length != 0 &&
                      truncateText(
                        'TvEnVivo_PanelOpciones_MenuOpciones_Opcion3_TextoEliminarFavorito',
                        30
                      )}
                  </div>
                </button>
                {(programdetailsdata?.fromLivePlayer ||
                  eventStatus === 'Present Event') && (
                  <button
                    data-sn-left={'#back-button'}
                    className="program-language focusable"
                    onClick={event => handleOptionsPanel(event)}
                  >
                    <div className="language-icon">
                      {' '}
                      <img src="images/Program_Details_Icons/language_icon.png" />
                    </div>
                    <div className="language-program-text">
                      {truncateText(
                        'TvEnVivo_PanelOpciones_MenuOpciones_Opcion4_TextoIdioma',
                        30
                      )}
                    </div>
                  </button>
                )}

                {userDetails?.admin && (
                  <>
                    {lockedChannelsList &&
                    getLockedChannelsIcon(
                      programdetailsdata?.channelData?.group_id
                    ) ? (
                      <button
                        data-sn-left={'#back-button'}
                        onClick={e => {
                          checkSecurityPin(
                            e,
                            'Unlock',
                            programdetailsdata?.channelData
                          )
                        }}
                        className="block-channel focusable"
                      >
                        <div className="block-channel-icon">
                          <img src={'images/lock_icon_miniEPG.png'}></img>
                        </div>
                        <div className="block-channel-text">
                          {translations?.language?.[region]
                            ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion5_TextoDesbloquear ? (
                            <p style={{ color: 'white' }}>
                              {truncateText(
                                'TvEnVivo_PanelOpciones_MenuOpciones_Opcion5_TextoDesbloquear',
                                30
                              )}
                            </p>
                          ) : (
                            <p style={{ color: 'white' }}>Desbloquear canal</p>
                          )}
                        </div>
                      </button>
                    ) : (
                      <button
                        data-sn-left={'#back-button'}
                        onClick={e => handleBlockChannelButtonClick(e)}
                        className="block-channel focusable"
                      >
                        <div className="block-channel-icon">
                          <img src={'images/lock_icon_miniEPG.png'}></img>
                        </div>
                        {translations?.language?.[region]
                          ?.TvEnVivo_PanelOpciones_MenuOpciones_Opcion5_TextoBloquear ? (
                          <div className="block-channel-text">
                            {truncateText(
                              'TvEnVivo_PanelOpciones_MenuOpciones_Opcion5_TextoBloquear',
                              30
                            )}
                          </div>
                        ) : (
                          <div className="block-channel-text">
                            {truncateText(
                              'TvEnVivo_PanelOpciones_MenuOpciones_Opcion5_TextoBloquear',
                              30
                            )}{' '}
                          </div>
                        )}
                      </button>
                    )}
                  </>
                )}
              </>
            )}
            {showRecordingOptions && (
              <>
                {programdetailsdata?.programData?.ext_series_id ? (
                  <>
                    <button
                      autoFocus={true}
                      className="program-record focusable"
                      onClick={e => {
                        !isRecordedSeries?.length > 0
                          ? handleRecordingSeries(e)
                          : handleDeleteRecordingSeries(e)
                      }}
                    >
                      <div className="record-icon">
                        <img src="images/Program_Details_Icons/white_recording_icon.png" />
                      </div>
                      <div className="record-program">
                        {truncateText(
                          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoSerie',
                          30
                        )}
                      </div>
                    </button>
                    <button
                      className="program-record focusable"
                      onClick={e => {
                        !isRecordedSeries?.length > 0
                          ? handleRecordingEpisode(e)
                          : handleDeleteRecordingEpisode(e)
                      }}
                    >
                      <div className="record-icon">
                        <img src="images/Program_Details_Icons/white_recording_icon.png" />
                      </div>
                      <div className="record-program">
                        {!isRecordedSeries?.length > 0 &&
                        eventStatus == 'Present Event'
                          ? truncateText(
                              'TvEnVivo_PanelOpciones_MenuOpciones_Opcion2_TextoEpisodio',
                              30
                            )
                          : 'Este episodio'}
                      </div>
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      autoFocus={true}
                      className="program-record focusable"
                      onClick={e => handleRecordingEpisode(e)}
                    >
                      <div>
                        <img src="images/Program_Details_Icons/from_beginning_record_icon.png" />
                      </div>
                      <div className="record-program">
                        {truncateText(
                          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion1_TextoDesdeElInicio',
                          30
                        )}
                      </div>
                    </button>
                    <button
                      className="program-record focusable"
                      onClick={e => handleRecordingEpisode(e, 'now')}
                    >
                      <div className="record-icon">
                        <img src="images/Program_Details_Icons/white_recording_icon.png" />
                      </div>
                      <div className="record-program">
                        {truncateText(
                          'TvEnVivo_PanelOpciones_MenuOpciones_Opcion2_TextoDesdeAhora',
                          30
                        )}
                      </div>
                    </button>
                  </>
                )}
              </>
            )}
            <div className="program-options-name">
              {showAudioAndSubtitles
                ? truncateText(
                    'TvEnVivo_PanelOpciones_MenuOpciones_TextoTituloAudio',
                    30
                  )
                : ''}
            </div>
            {showAudioAndSubtitles && (
              <>
                {audioOptions?.length > 0 &&
                  audioOptions?.map((item, index) => (
                    <button
                      key={index}
                      className="program-record focusable"
                      id={'language-btn' + index}
                      data-sn-left={'#back-button'}
                      autoFocus={
                        audioOptions?.length == 1 ||
                        (selectedAudio && selectedAudio?.id == item?.id)
                      }
                      onClick={event =>
                        audioOptions?.length > 1
                          ? handleSelectAudio(event, item)
                          : null
                      }
                    >
                      <div className="channel-favorites-icon">
                        <img src="images/Vod_Icons/language_audio.png" />
                      </div>
                      <p className="record-program subtitle-audio-text">
                        {audioOptions?.length == 1
                          ? truncateText(
                              'TVEnVivo_PanelOpciones_MenuOpciones_Opcion_TextoAudioOriginal',
                              20
                            )
                          : handleLabelTranslate(item?.label?.toUpperCase())}
                      </p>
                      {(audioOptions?.length == 1 ||
                        (selectedAudio && selectedAudio?.id == item?.id)) && (
                        <img
                          className="selected-audio-icon"
                          src="images/Vod_Icons/language_selected.png"
                        />
                      )}
                    </button>
                  ))}
              </>
            )}

            {showAudioAndSubtitles && (
              <>
                {subtitleData?.length > 0 && (
                  <div className="program-options-name">
                    <span>
                      {showAudioAndSubtitles
                        ? truncateText(
                            'TvEnVivo_PanelOpciones_MenuOpciones_TextoTituloSubtitulos',
                            30
                          )
                        : truncateText(
                            'TvEnVivo_PanelOpciones_MenuOpciones_TextoTitulo1',
                            30
                          )}
                    </span>
                  </div>
                )}
                {subtitleData?.length > 0 &&
                  subtitleData?.map((item, index) => (
                    <button
                      key={index}
                      className="program-record focusable"
                      data-sn-left={'#back-button'}
                      onClick={event => handleSubtitle(event, item, true)}
                    >
                      <div className="channel-favorites-icon">
                        <img src="images/Vod_Icons/language_subs.png" />
                      </div>
                      <p className="record-program subtitle-audio-text">
                        {handleLabelTranslate(item?.label?.toUpperCase())}
                      </p>
                      {item?.enabled && (
                        <img
                          className="selected-audio-icon"
                          src="images/Vod_Icons/language_selected.png"
                        />
                      )}
                    </button>
                  ))}
                {subtitleData?.length > 0 && (
                  <button
                    className="program-record focusable"
                    data-sn-left={'#back-button'}
                    onClick={event => handleSubtitle(event, null, false)}
                  >
                    <div className="channel-favorites-icon">
                      <img src="images/Vod_Icons/ic_disabledlanguage56px.png" />
                    </div>
                    <p className="record-program subtitle-audio-text">
                      {
                        translations?.language?.[region]
                          ?.button_live_subtitle_off
                      }
                    </p>
                    {!(subtitleData.filter(sub => sub.enabled)?.length > 0) && (
                      <img
                        className="selected-audio-icon"
                        src="images/Vod_Icons/language_selected.png"
                      />
                    )}
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default ProgramDetails
