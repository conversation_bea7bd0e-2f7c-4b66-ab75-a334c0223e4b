import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  playerinfo: {},
  contToken: '',
  paywaytoken: {},
  getMediaRes: {},
  getMediaError: {},
  trailerplayerinfo: {},
  trailerplayerResponseError: {},
  license: '',
  channelData: {},
  audioOptions: [],
  defaultAudio: '',
  playerInstance: null,
  EnablePlayerEpisode: false,
  recordplayerinfo: {},
  npawPluginInstance: null,
  livePlayerCertificate: null,
  trackstartresponse: null,
  genreDetails: ''
}

export const PlayerSlice = createSlice({
  name: 'player',
  initialState,
  reducers: {
    getLivePlayer: (state, { payload }) => {},
    getLivePlayerSuccess: (state, { payload }) => {
      state.playerinfo = payload
    },
    getLivePlayerError: (state, { payload }) => {},
    resetLivePlayer: (state, { payload }) => {
      state.playerinfo = initialState.playerinfo
    },
    getMediaAPI: (state, { payload }) => {
      state.getMediaRes = {}
    },
    getMediaAPISuccess: (state, { payload }) => {
      state.getMediaRes = payload
      state.getMediaError = {}
    },
    getMediaAPIError: (state, { payload }) => {
      state.getMediaError = payload
      state.getMediaRes = {}
    },
    clearGetMediaRes: (state, { payload }) => {
      state.getMediaRes = {}
    },
    clearGetMediaErr: (state, { payload }) => {
      state.getMediaError = {}
    },
    getRecordPlayer: (state, { payload }) => {},
    getRecordPlayerSuccess: (state, { payload }) => {
      state.recordplayerinfo = payload
    },
    getRecordPlayerError: (state, { payload }) => {},
    getTrailerPlayer: (state, { payload }) => {},
    getTrailerPlayerSuccess: (state, { payload }) => {
      state.trailerplayerinfo = payload
    },
    getTrailerPlayerError: (state, { payload }) => {
      state.trailerplayerResponseError = payload
    },
    clearTrailerPlayerError: (state, { payload }) => {
      state.trailerplayerResponseError = {}
    },
    getLinealPayway: (state, { payload }) => {},
    getLinealPaywayApiSuccess: (state, { payload }) => {
      state.paywaytoken = payload
    },
    getLinealPaywayApiError: (state, { payload }) => {},
    getDrmLicense: (state, { payload }) => {
      state.license = payload
    },
    getChannelData: (state, { payload }) => {
      state.channelData = payload
    },
    removeTrailerPlayerData: (state, { payload }) => {
      state.trailerplayerinfo = {}
    },
    EnableplayerEpisodeScreen: (state, { payload }) => {
      state.EnablePlayerEpisode = payload
    },
    getAvailableAudio: (state, { payload }) => {
      state.audioOptions = payload
    },
    getDefaultAudio: (state, { payload }) => {
      state.defaultAudio = payload
    },
    getAvailableSubtitles: (state, { payload }) => {
      state.subtitleOptions = payload
    },
    setPlayerInstance: (state, { payload }) => {
      state.playerInstance = payload
    },
    removePlayerInstance: state => {
      state.playerInstance = initialState.playerInstance
    },
    setNpawPluginInstance: (state, { payload }) => {
      state.npawPluginInstance = payload
    },
    setLivePlayerCertificate: (state, { payload }) => {
      state.livePlayerCertificate = payload
    },
    clearRecordPlayerInfo: state => {
      state.recordplayerinfo = {}
    },
    getPlayerTrackAPI: (state, { payload }) => {},
    getPlayerTrackAPISuccess: (state, { payload }) => {
      state.trackstartresponse = payload
    },
    getPlayerTrackAPIError: (state, { payload }) => {
      state.trackstartresponse = {}
    },
    setGenreDetails: (state, { payload }) => {
      state.genreDetails = payload
    }
  }
})

export const {
  getDrmLicense,
  getMediaAPI,
  getMediaAPISuccess,
  getMediaAPIError,
  clearGetMediaRes,
  clearGetMediaErr,
  getLinealPayway,
  getLinealPaywayApiSuccess,
  getLinealPaywayApiError,
  getLivePlayer,
  getLivePlayerSuccess,
  getLivePlayerError,
  resetLivePlayer,
  getChannelData,
  getTrailerPlayer,
  getTrailerPlayerSuccess,
  getTrailerPlayerError,
  clearTrailerPlayerError,
  removeTrailerPlayerData,
  EnableplayerEpisodeScreen,
  getAvailableAudio,
  getDefaultAudio,
  getAvailableSubtitles,
  setPlayerInstance,
  removePlayerInstance,
  getRecordPlayer,
  getRecordPlayerSuccess,
  getRecordPlayerError,
  setNpawPluginInstance,
  setLivePlayerCertificate,
  clearRecordPlayerInfo,
  getPlayerTrackAPI,
  getPlayerTrackAPIError,
  getPlayerTrackAPISuccess,
  setGenreDetails
} = PlayerSlice.actions
export default PlayerSlice.reducer
