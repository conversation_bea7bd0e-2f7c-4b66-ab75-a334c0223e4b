import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  checkControlPin: {},
  lockedChannelsList: {},
  modifyControlPin: {},
  passwordChange: {},
  setControlPinData: {},
  setControlPinError: {},
  statusControlPin: {},
  controlPinStatus: {},
  controlPin: {},
  transactionHistory: {},
  userLogout: {},
  logoutAllDevices: {},
  devicesList: {},
  lockedChannelAdd: {},
  lockedChannelDelete: {},
  changeControlPin: {},
  changeControlPinError: {},
  remindControlPin: {},
  disableControlPin: {},
  disableControlPinError: {},
  activeSubscriptions: {},
  cancelSubscription: {},
  getSubsInfo: {},
  getPlanSelector: {},
  getPlanSelectorV1: {},
  addPayments: {},
  addFibraLinePayments: {},
  fibraPaymentsConfirm: {},
  paymentsConfirm: {},
  addPromoCode: {},
  subscribePlan: {},
  paywayWorkFlow: {},
  viewSubscribeData: {},
  cancelSubscribeViewData: {},
  transactionFilter: {},
  settingsState: {},
  faqFilters: {},
  paymentData: {},
  cmsViewSub: {},
  lasttouch: '',
  isLoading: false,
  error: {},
  lastTypedText: '',
  getVodSubsInfo: {},
  afterSubscribePlayerData: {},
  vodID: '',
  liveIndex: 0,
  toBackPage: {},
  getClientDetails: {},
  addClientDetails: {},
  getCardDetails: {},
  addCardDetails: {},
  updateCardDetails: {},
  deleteCardDetails: {},
  telcelPayment: {},
  telcelOtp: {},
  creditCardPaymentData: {},
  multipurchasebuttoninfo : []
}

export const settingsSlice = createSlice({
  name: 'settingsSlice',
  initialState,
  reducers: {
    setlastTypedText: (state, { payload }) => {
      state.lastTypedText = payload
    },
    getCheckControlPin: state => {
      state.isLoading = true
    },
    getCheckControlPinSuccess: (state, { payload }) => {
      state.checkControlPin = payload
      state.isLoading = false
    },
    getCheckControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getLockedChannelsList: state => {
      state.isLoading = true
    },
    getLockedChannelsListSuccess: (state, { payload }) => {
      state.lockedChannelsList = payload
      state.isLoading = false
    },
    getLockedChannelsListError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getModifyControlPin: state => {
      state.isLoading = true
    },
    getModifyControlPinSuccess: (state, { payload }) => {
      state.modifyControlPin = payload
      state.isLoading = false
    },
    getModifyControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getPasswordChange: state => {
      state.isLoading = true
    },
    getPasswordChangeSuccess: (state, { payload }) => {
      state.passwordChange = payload
      state.isLoading = false
    },
    getPasswordChangeError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    setControlPin: state => {
      state.isLoading = true
    },
    setControlPinSuccess: (state, { payload }) => {
      state.setControlPinData = payload?.response
      state.setControlPinError = null
      state.isLoading = false
    },
    setControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.setControlPinData = null
      state.setControlPinError = payload
    },

    getStatusControlPin: state => {
      state.isLoading = true
    },
    getStatusControlPinSuccess: (state, { payload }) => {
      state.statusControlPin = payload
      state.isLoading = false
    },
    getStatusControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

     getControlPinStatus: state => {
      state.isLoading = true
    },
    getControlPinStatusSuccess: (state, { payload }) => {
      state.controlPinStatus = payload
      state.isLoading = false
    },
    getControlPinStatusError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    
    getControlPin: state => {
      state.isLoading = true
    },
    getControlPinSuccess: (state, { payload }) => {
      state.controlPin = payload
      state.isLoading = false
    },
    getControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getTransactionHistory: state => {
      state.isLoading = true
    },
    getTransactionHistorySuccess: (state, { payload }) => {
      state.transactionHistory = payload
      state.isLoading = false
    },
    getTransactionHistoryError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getUserLogout: state => {
      state.isLoading = true
    },
    getUserLogoutSuccess: (state, { payload }) => {
      state.userLogout = payload
      state.isLoading = false
    },
    getUserLogoutError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getLogoutAllDevices: state => {
      state.isLoading = true
    },
    getLogoutAllDevicesSuccess: (state, { payload }) => {
      state.logoutAllDevices = payload
      state.isLoading = false
    },
    getLogoutAllDevicesError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getDevicesList: state => {
      state.isLoading = true
    },
    getDevicesListSuccess: (state, { payload }) => {
      state.devicesList = payload
      state.isLoading = false
    },
    getDevicesListError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getLockedChannelDelete: state => {
      state.isLoading = true
    },
    getLockedChannelDeleteSuccess: (state, { payload }) => {
      state.lockedChannelDelete = payload
      state.isLoading = false
    },
    getLockedChannelDeleteError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getLockedChannelAdd: state => {
      state.isLoading = true
    },
    getLockedChannelAddSuccess: (state, { payload }) => {
      state.lockedChannelAdd = payload
      state.isLoading = false
    },
    getLockedChannelAddError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getChangeControlPin: state => {
      state.isLoading = true
    },
    getChangeControlPinSuccess: (state, { payload }) => {
      state.changeControlPin = payload
      state.isLoading = false
    },
    getChangeControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.changeControlPinError = payload
    },

    getRemindControlPin: state => {
      state.isLoading = true
    },
    getRemindControlPinSuccess: (state, { payload }) => {
      state.remindControlPin = payload
      state.isLoading = false
    },
    getRemindControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    disableControlPin: state => {
      state.isLoading = true
    },
    disableControlPinSuccess: (state, { payload }) => {
      state.disableControlPin = payload
      state.isLoading = false
    },
    disableControlPinError: (state, { payload }) => {
      state.isLoading = false
      state.disableControlPinError = payload
    },

    getActiveSubscriptions: state => {
      state.isLoading = true
    },
    getActiveSubscriptionsSuccess: (state, { payload }) => {
      state.activeSubscriptions = payload
      state.isLoading = false
    },
    getActiveSubscriptionsError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getCancelSubscription: state => {
      state.isLoading = true
    },
    getCancelSubscriptionSuccess: (state, { payload }) => {
      state.cancelSubscription = payload
      state.isLoading = false
    },
    getCancelSubscriptionError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getSubscriptionInfo: state => {
      state.isLoading = true
    },
    getSubscriptionInfoSuccess: (state, { payload }) => {
      state.getSubsInfo = payload
      state.isLoading = false
    },
    getSubscriptionInfoError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getNewPlanSelector: state => {
      state.isLoading = true
    },
    getNewPlanSelectorSuccess: (state, { payload }) => {
      state.getPlanSelector = payload
      state.isLoading = false
    },
    getNewPlanSelectorError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getNewPlanSelectorV1: state => {
      state.isLoading = true
    },
    getNewPlanSelectorV1Success: (state, { payload }) => {      
      state.getPlanSelectorV1 = payload
      state.isLoading = false
    },
    getNewPlanSelectorV1Error: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getClearSubscriptionInfo: state => {
      state.getSubsInfo = initialState.getSubsInfo
    },

    getAddPayments: state => {
      state.isLoading = true
    },
    getAddPaymentsSuccess: (state, { payload }) => {
      state.addPayments = payload
      state.isLoading = false
    },
    getAddPaymentsError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    addTelcelPayment: state => {
      state.isLoading = true
    },
    addTelcelPaymentSuccess: (state, { payload }) => {
      state.telcelPayment = payload
      state.isLoading = false
    },
    addTelcelPaymentError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    confirmTelcel: state => {
      state.isLoading = true
    },
    confirmTelcelSuccess: (state, { payload }) => {
      state.telcelOtp = payload
      state.isLoading = false
    },
    confirmTelcelError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    getAddFibraLinePayments: state => {
      state.isLoading = true
    },
    getAddFibraLinePaymentsSuccess: (state, { payload }) => {
      state.addFibraLinePayments = payload
      state.isLoading = false
    },
    getAddFibraLinePaymentsError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getFibraPaymentsConfirm: state => {
      state.isLoading = true
    },
    getFibraPaymentsConfirmSuccess: (state, { payload }) => {
      state.fibraPaymentsConfirm = payload
      state.isLoading = false
    },
    getFibraPaymentsConfirmError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getPaymentsConfirm: state => {
      state.isLoading = true
    },
    getPaymentsConfirmSuccess: (state, { payload }) => {
      state.paymentsConfirm = payload
      state.isLoading = false
    },
    getPaymentsConfirmError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getMultipurchaseButton: state => {
      state.isLoading = true
    },
    getMultipurchaseButtonSuccess: (state, { payload }) => {
      state.multipurchasebuttoninfo = payload
      state.isLoading = false
    },
    getMultipurchaseButtonError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getAddPromoCode: state => {
      state.isLoading = true
    },
    getAddPromoCodeSuccess: (state, { payload }) => {
      state.addPromoCode = payload
      state.isLoading = false
    },
    getAddPromoCodeError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getSubscribePlan: state => {
      state.isLoading = true
    },
    getSubscribePlanSuccess: (state, { payload }) => {
      state.subscribePlan = payload
      state.isLoading = false
    },
    getSubscribePlanError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    clearSubScribePlan: state => {
      state.subscribePlan = initialState.subscribePlan
    },
    clearPromoCode: state => {
      state.addPromoCode = initialState.addPromoCode
    },
    clearPaymentError: state => {
      state.telcelPayment = initialState.telcelPayment
      state.addFibraLinePayments = initialState.addFibraLinePayments
      state.addPromoCode = initialState.addPromoCode
      state.fibraPaymentsConfirm = initialState.fibraPaymentsConfirm
      state.paymentData = initialState.paymentData
      state.telcelOtp = initialState.telcelOtp
      state.error = initialState.error
      state.updateCardDetails = initialState.updateCardDetails
    },
    getPaywayWorkflow: state => {
      state.isLoading = true
    },
    getPaywayWorkflowSuccess: (state, { payload }) => {
      state.paywayWorkFlow = payload
      state.isLoading = false
    },
    getPaywayWorkflowError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getClient: state => {
      state.isLoading = true
    },
    getClientSuccess: (state, { payload }) => {
      state.getClientDetails = payload
      state.isLoading = false
    },
    getClientError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    addClient: state => {
      state.isLoading = true
    },
    addClientSuccess: (state, { payload }) => {
      state.addClientDetails = payload
      state.isLoading = false
    },
    addClientError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    getAvailableCreditCards: state => {
      state.isLoading = true
    },
    getAvailableCreditCardsSuccess: (state, { payload }) => {
      state.getCardDetails = payload
      state.isLoading = false
    },
    getAvailableCreditCardsError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    addCreditCard: state => {
      state.isLoading = true
    },
    addCreditCardSuccess: (state, { payload }) => {
      state.addCardDetails = payload
      state.isLoading = false
    },
    addCreditCardError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    updateCreditCard: state => {
      state.isLoading = true
    },
    updateCreditCardSuccess: (state, { payload }) => {
      state.updateCardDetails = payload
      state.isLoading = false
    },
    updateCreditCardError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    deleteCreditCard: state => {
      state.isLoading = true
    },
    deleteCreditCardSuccess: (state, { payload }) => {
      state.deleteCardDetails = payload
      state.isLoading = false
    },
    deleteCreditCardError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    creditCardPayment: state => {
      state.isLoading = true
    },
    creditCardPaymentSuccess: (state, { payload }) => {
      state.creditCardPaymentData = payload
      state.isLoading = false
    },
    creditCardPaymentError: (state, { payload }) => {
      state.error = payload
      state.isLoading = false
    },
    getViewSubscribeData: (state, { payload }) => {
      state.viewSubscribeData = payload
    },

    getCancelSubscribeViewData: (state, { payload }) => {
      state.cancelSubscribeViewData = payload
    },

    getTransactionFilters: (state, { payload }) => {
      state.transactionFilter = payload
    },

    getSettingsState: (state, { payload }) => {
      state.settingsState = payload
    },

    getFaqFilters: (state, { payload }) => {
      state.faqFilters = payload
    },

    getPaymentDetails: (state, { payload }) => {
      state.paymentData = payload
    },

    getCMSflowforViewSubs: state => {
      state.isLoading = true
    },
    getCMSflowforViewSubsSuccess: (state, { payload }) => {
      state.cmsViewSub = payload
      state.isLoading = false
    },
    getCMSflowforViewSubsError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    setLastTouch: (state, { payload }) => {
      state.lasttouch = payload
    },

    clearLastTouch: state => {
      state.lasttouch = ''
    },

    getClearPasswordState: state => {
      state.passwordChange = {}
    },

    clearFPNotification: state => {
      state.remindControlPin = {}
    },

    clearFibraLineData: state => {
      state.addFibraLinePayments = {}
    },

    getClearAllPaymentsData: state => {
      state.fibraPaymentsConfirm = {}
      state.paymentsConfirm = {}
      state.addPromoCode = {}
      state.addPayments = {}
      state.addFibraLinePayments = {}
    },

    getClearSetControlPin: state => {
      state.setControlPinData = {}
    },

    getClearAllSettingsState: state => {
      state.lastTypedText = {}
      state.checkControlPin = {}
      state.lockedChannelsList = {}
      state.modifyControlPin = {}
      state.passwordChange = {}
      state.setControlPinData = {}
      state.setControlPinError = {}
      state.statusControlPin = {}
      state.controlPinStatus = {}
      state.controlPin = {}
      state.transactionHistory = {}
      state.userLogout = {}
      state.logoutAllDevices = {}
      state.lockedChannelDelete = {}
      state.lockedChannelAdd = {}
      state.changeControlPin = {}
      state.remindControlPin = {}
      state.disableControlPin = {}
      state.disableControlPinError = {}
      state.activeSubscriptions = {}
      state.cancelSubscription = {}
      state.getSubsInfo = {}
      state.addPayments = {}
      state.addFibraLinePayments = {}
      state.paymentsConfirm = {}
      state.fibraPaymentsConfirm = {}
      state.addPromoCode = {}
      state.subscribePlan = {}
      state.paywayWorkFlow = {}
      state.viewSubscribeData = {}
      state.cancelSubscribeViewData = {}
      state.transactionFilter = {}
      state.settingsState = {}
      state.changeControlPinError = {}
      state.faqFilters = {}
      state.paymentData = {}
      state.cmsViewSub = {}
      state.isLoading = false
    },
    clearRemindPinSettingsState: state => {
      state.remindControlPin = {}
    },
    clearCheckControlPin: state => {
      state.checkControlPin = {}
    },
    getVodSubscriptionInfo: state => {
      state.isLoading = true
    },
    getVodSubscriptionInfoSuccess: (state, { payload }) => {
      state.getVodSubsInfo = payload
      state.isLoading = false
    },
    getVodSubscriptionInfoError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    clrSubscriptionInfo: (state, { }) => {
      state.getVodSubsInfo = {}
    },
    getClearModifyControlPin: state => {
      state.modifyControlPin = {}
    },

    getFinishSubscriptionPlayerData: (state, { payload }) => {
      state.afterSubscribePlayerData = payload
    },
    getVodID: (state, { payload }) => {
      state.vodID = payload
    },
    getLiveIndex: (state, { payload }) => {
      state.liveIndex = payload
    },
    getContractBackHandle: (state, { payload }) => {
      state.toBackPage = payload
    },
    clearGetContractBackHandle: (state) => {
      state.toBackPage = {}
    }
  }
})

export const {
  setlastTypedText,
  getCheckControlPin,
  getCheckControlPinSuccess,
  getCheckControlPinError,
  getLockedChannelsList,
  getLockedChannelsListSuccess,
  getLockedChannelsListError,
  getModifyControlPin,
  getModifyControlPinSuccess,
  getModifyControlPinError,
  getPasswordChange,
  getPasswordChangeSuccess,
  getPasswordChangeError,
  setControlPin,
  setControlPinSuccess,
  setControlPinError,
  getStatusControlPin,
  getStatusControlPinSuccess,
  getStatusControlPinError,
  getControlPinStatus,
  getControlPinStatusSuccess,
  getControlPinStatusError,
  getControlPin,
  getControlPinSuccess,
  getControlPinError,
  getTransactionHistory,
  getTransactionHistorySuccess,
  getTransactionHistoryError,
  getUserLogout,
  getUserLogoutSuccess,
  getUserLogoutError,
  getLogoutAllDevices,
  getLogoutAllDevicesSuccess,
  getLogoutAllDevicesError,
  getDevicesList,
  getDevicesListSuccess,
  getDevicesListError,
  getLockedChannelDelete,
  getLockedChannelDeleteSuccess,
  getLockedChannelDeleteError,
  getLockedChannelAdd,
  getLockedChannelAddSuccess,
  getLockedChannelAddError,
  getChangeControlPin,
  getChangeControlPinSuccess,
  getChangeControlPinError,
  getRemindControlPin,
  getRemindControlPinSuccess,
  getRemindControlPinError,
  disableControlPin,
  disableControlPinSuccess,
  disableControlPinError,
  getActiveSubscriptions,
  getActiveSubscriptionsSuccess,
  getActiveSubscriptionsError,
  getCancelSubscription,
  getCancelSubscriptionSuccess,
  getCancelSubscriptionError,
  getSubscriptionInfo,
  getSubscriptionInfoSuccess,
  getSubscriptionInfoError,
  getMultipurchaseButton,
  getMultipurchaseButtonSuccess,
  getMultipurchaseButtonError,
  getAddPayments,
  getAddPaymentsSuccess,
  getAddPaymentsError,
  getAddFibraLinePayments,
  getAddFibraLinePaymentsSuccess,
  getAddFibraLinePaymentsError,
  getFibraPaymentsConfirm,
  getFibraPaymentsConfirmSuccess,
  getFibraPaymentsConfirmError,
  getPaymentsConfirm,
  getPaymentsConfirmSuccess,
  getPaymentsConfirmError,
  getAddPromoCode,
  getAddPromoCodeSuccess,
  getAddPromoCodeError,
  getSubscribePlan,
  getSubscribePlanSuccess,
  getSubscribePlanError,
  getPaywayWorkflow,
  getPaywayWorkflowSuccess,
  getPaywayWorkflowError,
  getViewSubscribeData,
  getCancelSubscribeViewData,
  getTransactionFilters,
  getSettingsState,
  getFaqFilters,
  getPaymentDetails,
  getClearPasswordState,
  getClearAllSettingsState,
  clearRemindPinSettingsState,
  getCMSflowforViewSubs,
  getCMSflowforViewSubsSuccess,
  getCMSflowforViewSubsError,
  setLastTouch,
  clearLastTouch,
  clearFPNotification,
  getVodSubscriptionInfo,
  getVodSubscriptionInfoSuccess,
  getVodSubscriptionInfoError,
  clearCheckControlPin,
  clrSubscriptionInfo,
  getClearSubscriptionInfo,
  getClearModifyControlPin,
  clearSubScribePlan,
  clearPromoCode,
  getFinishSubscriptionPlayerData,
  getVodID,
  getLiveIndex,
  clearFibraLineData,
  addTelcelPayment,
  addTelcelPaymentSuccess,
  addTelcelPaymentError,
  confirmTelcel,
  confirmTelcelSuccess,
  confirmTelcelError,
  getClearAllPaymentsData,
  getClient,
  getClientSuccess,
  getClientError,
  addClient,
  addClientSuccess,
  addClientError,
  getAvailableCreditCards,
  getAvailableCreditCardsSuccess,
  getAvailableCreditCardsError,
  addCreditCard,
  addCreditCardSuccess,
  addCreditCardError,
  updateCreditCard,
  updateCreditCardSuccess,
  updateCreditCardError,
  deleteCreditCard,
  deleteCreditCardSuccess,
  deleteCreditCardError,
  creditCardPayment,
  creditCardPaymentSuccess,
  creditCardPaymentError,
  getClearSetControlPin,
  clearPaymentError,
  getContractBackHandle,
  clearGetContractBackHandle,
  getNewPlanSelector,
  getNewPlanSelectorSuccess,
  getNewPlanSelectorError,
  getNewPlanSelectorV1,
  getNewPlanSelectorV1Success,
  getNewPlanSelectorV1Error
} = settingsSlice.actions

export default settingsSlice.reducer
