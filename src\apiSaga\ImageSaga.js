import { call, takeEvery } from '@redux-saga/core/effects'
import { store } from '../store/sagaStore'
import { getImageSuccess } from '../store/slices/Images'
import { BASE_URL, URL } from '../utils/environment'
import { request } from '../utils/request'
import { getAppKey } from '../utils/AppKeys'

function* getImageApi({ payload }) {
  const tvType = typeof tizen !== 'undefined' ? 'SAMSUNG_TIZEN' : 'LG_WEBOS'

  try {
    yield call(
      request,
      `${URL.IMAGE_URL}&region=${payload}&sessionKey=${getAppKey(
        tvType
      )}-${payload}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getImageSuccess(response))
        },
        onError(error) {
          console.error('error --> ', error)
        }
      }
    )
  } catch (error) {
    console.error('error --> ', error)
  }
}

export default function* ImageSaga() {
  yield takeEvery('images/getImage', getImageApi)
}
