import React from "react";
import { render,fireEvent,queryByAttribute } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router, useLocation } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import VodSkeletonLoading from "./VodSkeletonLoading";


const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
jest.mock("react-router-dom", () => ({
    ...jest.requireActual("react-router-dom"),
    useLocation: () => ({
      state: {
        page:"series"
      }
    })
  }));

describe('landing page test', () => {
    test('it should render the skeleton loading', async () => {
        const wrapper = await renderWithState(<VodSkeletonLoading />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 8,
                charCode: 8
          });
    })
    
})