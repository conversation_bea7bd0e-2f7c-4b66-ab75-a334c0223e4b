.loaderWrapper {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 9999;
  background: #000000a3;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  display: flex;
  width: 100%;
}

.loaderText {
  text-align: left;
  font: normal normal normal 28px/34px "Roboto",serif;
  letter-spacing: 0px;
  color: #EEEEEE;
}

.loaderIcon {
  margin: 0px 0;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.loaderContent {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
}

.smallCircle {
  height: 20px;
  width: 20px;
  border: 1px solid #eee;
  display: flex;
  border-radius: 38px;
}

.centerCircle {
  height: 75px;
  width: 75px;
  border: 4px solid #eee;
  display: flex;
  border-radius: 38px;
}

@media screen and (min-width: 1920px) {
  .epg-content-position{
    margin-top:619px
  }
  .QR_image {
    width: 350px;
    height: 45rem;
    /* margin-top: -2rem; */
    margin-left: 15rem;
}
  .channel_num{
    margin-left:35px
  }
  .htv{
    margin-bottom: 12rem!important;
  }
  .tv-btn-size{
    border-radius: 5px;
     width: 399px !important;
     height: 76px !important;
   }
   .sign-in-tv{
    margin-top: 58px;
  }
  .log-nxt-btn{
    margin-right: 13rem!important;
    margin-top: 7rem!important;
  }
  .detail-top{
    position: fixed;
    margin-top: 3rem;
  }
  .cg_position{
    margin-top: 7rem;
  }
  .card-title-font{
    font-size: 27px;
   display: flex;
  }
  .image-size{
    max-width:80% !important;
    margin-right:44px;
  }
  .default-image{
    width:81%;
    margin-right:44px;
  }
  .timing-width{
    margin-left:10px;
    color:#EEEEEE;
    font-size: 25px !important;
    opacity: 80%;
  }
  .data-unvailable{
    color: #EEEEEE;
    margin-top: 79px;
    font-size: 38px !important;
    margin-left: 25rem;
    text-align: center;
  }
  .toast-msg-font{
    font-size: 26px !important;
    width:30rem !important;
  }
  .broadcast-err{
    font-size: 22px !important;
    margin-left: 30px !important;
    margin-top: 10px !important
  }
  .tv-font-size-sec{
    font: normal normal normal 30px 'Roboto', sans-serif !important; 
   }
  .login_submit_smarttv{
    border-radius:7px !important;
  }
  .railitem-tvicon {
    width: 140px !important;
    height: 76px !important;
   
}
  .pcroceed-btntv{
    border-radius:5px !important;
  }
  .plan-height-tv{
    overflow: auto;
  }
  .plan-height-tv::-webkit-scrollbar {
    display: none;
  }

  .card-sub-title-tv{
    margin-top: 8px;
  }
  .month-plan-tv{
    font: normal normal normal 24px/32px "Roboto",serif !important;
    margin-left: 30px;
    margin-top: 6px;
  }
  .pack-validity-tv{
    font: normal normal normal 24px/25px "Roboto",serif !important;
    left: 30px;
    position: relative;
  }
  .card-title-tv{
    font: normal normal normal 30px/37px "Roboto",serif !important;
    margin-left: 30px;
  }
  .btn-view-detail-tv{
    font: normal normal normal 24px/32px "Roboto",serif !important;
    height:62px;
    width: 285px !important;
    margin-top: 9px;
  }
  .btn-buy-now-tv{
    font: normal normal normal 28px/37px "Roboto",serif !important;
    width: 284px !important;
    height: 60px;
    margin-top: 10px;
  }
  .amnt-val{
    font: normal normal normal 40px/37px "Roboto",serif !important;
    margin-left: 5px;
  }
  .amount-position{
    width: 212px;
  }
  .price-symbol i{
    font-size: 34px !important;
    margin-left: -28px;
  }
  .tv_card_bg_img {
    height: 15rem !important;
  }
.margin_top {
  margin-top: 15px;
}
  .tv_d_flex {
    /* display: flex; */
    justify-content: center;
  }

  .smart-tv-expand .left-space-top-menu {
    /* padding-left: 7rem!important;   */
    margin-top: 1rem !important;
  }

  .left-space-top-menu {
    padding-left: 1.5rem !important;
    margin-top: 1rem !important;

  }

  .tv-railscroll {
    max-height: 74vh !important;
    padding-bottom: 31.2rem !important;
  }

  .content-header {
    padding-left: 3rem !important;
  }

  .tv-preview-lock {
    height: 16.5rem !important;
  }

  .tv-bg-color {
    background-color: #2E2E2E !important;
  }

  .movie-card-img {
    /* width: 100%;
    height: 100%; */
  }

  .rail-card {
    padding-left: 2rem !important;
    /* rail cards and l2 menu should be in same line */
  }

  .top-rails .nav-item .nav-link {
    border-radius: 5rem;
  }

  .top-rails .nav-item .nav-link .nspan {
    font-size: 1.5rem !important;
    padding-right: 2rem !important;
    padding-left: 2rem !important;
  }

  .tv_video_settings:focus{
    border: solid 2px #fff!important;
  }

  .avatar-title-tv {
    font: normal normal normal 48px/58px "Roboto", sans-serif;
    margin-top: 45px;
    margin-bottom: 60px;
    text-transform: capitalize;
  }
}

@media screen and (max-width: 1280px) {
  .avatar-title-tv {
    font: normal normal normal 48px/58px "Roboto", sans-serif;
    margin-bottom: 20px;
  }
  .QR_image{
    width: 252px;
    height:31rem;
    margin-left: 9rem;
    margin-top : -1px;
  }
  .col-w{
    width: 50%;
    margin-top: 3rem;
  }
  .htv {
    margin-bottom: 6rem!important;
}
.tv-font-size-sec {
  font: normal normal normal 21px 'Roboto', sans-serif !important;
}
.log-nxt-btn {
  margin-right: 0rem!important;
  margin-top: 0rem!important;
}
.reg-keybord-tv {
  margin-top: 1.5rem;
}
.sign-in-tv {
  margin-top: 0px;
}
.tv-btn-size {
  border-radius: 5px;
  width: 235px !important;
  height: 56px !important;
}
.detail-top{
    position: fixed;
    margin-top: 1.5rem;
  }
  .cg_position{
    margin-top: 3rem;
  }
}

.tv_apps-width{
  height: 180px !important;
}

@media screen and (min-width: 1920px) {
  .proceed_margin{
    margin-top: 19rem;
  }
  .suspend-content-bg{
    border:none;
  }
  .forgot-btn_tv{
    margin-left: 14.6rem !important;
    height: 68px !important;
    width: fit-content !important;
    font: normal normal normal 33px/39px "Roboto", sans-serif !important;
  }
  .suspend-modal{
    margin-top:196px;
  }
  .suspend-text{
    margin-bottom: -11px !important;
    margin-top: 0% !important;
  }
  .suspend-img{
    width:33px !important;
    height:33px !important;
  }
  .tv_offline_btn{
    margin-top:10px;
  }
  .goback-button{
    margin-top:47px
  }
  .lock-icon-tv{
    width: 10rem !important;
    margin-bottom: -152px !important;
    margin-left: 5rem !important;
  }
  .pre-text-center{
    margin-top:32px !important;
    width:5rem !important;
  }
  .line-position{
    position: relative;
    top:6px;
    z-index: 30;
  }
  .text1-msg{
    font: normal normal normal 21px/36px "Roboto", sans-serif !important;
  }
  .navbar-logo-tv{ 
    width: 180px !important;
    top: 4rem !important;
    left: 6.3rem !important;
    height: 80px
  }
  .logout-text-tv{
    /* width: 419px; */
    width: max-content;
  }
  .account-heading{
    font: normal normal normal 42px/51px "Roboto", sans-serif;
    margin-top: 5.5rem !important;
  }
  .account-title{
    font: normal normal normal 26px/30px "Roboto", sans-serif;
    width: 15rem!important;
  }
  .account-value{
    font: normal normal normal 26px/48px "Roboto", sans-serif;
    line-height: 5.4rem !important;
  }
  .btn-submitfont{
    font: normal normal normal 33px/39px "Roboto", sans-serif !important;
    margin-top: 32px;
  }
  .account-card{
    max-width: 100rem !important;
    width: 43rem;
    margin-top: 4rem !important;
  }
  .centered{
    font: normal normal normal 26px/40px "Roboto", sans-serif !important;
  }
  .tooltip-container img {
    width: 30rem !important;
    max-width: 100rem !important;
  }
  .account-toast {
    font-size: 26px;
    width:496px;
  }
  .account-toast-text{
    padding:2rem !important;
  }
  .account-help {
    font: normal normal normal 24px/29px "Roboto", sans-serif !important;
  }
  .tag_rail {
    width: 22px !important;
}
  .px-2rem {
    padding-left: 3rem !important;
  }
  .tv_apps-width{
    height: 194px !important;
  }
  .tv_account-right-card{
    height: 602px;
    }

  .main-menu-expand ul>li.active {
    /* //background-color: red; */
    /* background-color: #EEEEEE !important; */
  }
  .main-menu-expand:hover ul>li>a>.sidebar-imgspan img {
    height: 45px!important;
}
  .main-menu-expand ul>li>a>.sidebar-imgspan img {
    height: 45px;
    vertical-align: middle;
  }

  .main-menu-expand ul>li.active>a .nav-text {
    color: #000;
    font-weight: 700;
  }

  .fixed-menu {
    font-size: 26px !important;
  }

  .main-menu-expand ul>li.active>a>span>img.menu-icon.tv_menu_icon {
    /* //background-color: red; */
    object-position: 0px -118px !important;
  }
  .tv-main-menu ul>li.active#shows>a>span>img.menu-icon{
    object-position: 0px -245px!important;
  }

}

.series-episode-card .card_img>img {
  min-height: 12rem;
}

.tv_series-card-desc {
  min-height: 60px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.smart-tv-expand .content-div #home-container .container-fluid .row #railListDiv .tab-content {
  width: 82% !important;

}

.help_pg{
    margin-top: 7rem !important;
    margin-left: 4rem;
}

.rdmore_tv:focus{
  background: none !important;
}

.tv_popup{
  opacity: .8 !important;
}

@media screen and (min-width: 1919px) {
  .sidebar-logo-tv{
    width:127px !important;
    margin-left:2.4rem !important;
  }
  .account-navbar-logo-tv{
    height:16px !important;
    width:auto !important;
  }
  .displayfocus{
    font: normal normal medium 30px/37px "Roboto", sans-serif !important;
  }
  .video-font{
    font: normal normal 300 60px/72px "Roboto", sans-serif !important;
    width:707px;
    margin-top: 204px;
  }
  .font-v{
    font: normal normal 300 55px/72px "Roboto", sans-serif !important;
  }
  .playback-option{
    margin-left:300px !important;
    line-height: 2.5;
    margin-top: 157px;
  }
  .otptexttv{
    font: normal normal normal 27px/30px "Roboto", sans-serif !important;
    width: 690px !important;
    margin-left: -158px !important;
    margin-bottom: 150px!important;
  }
  /* .existdots{
    margin-left: 0px !important;
  } */
  .channelrmv{
    margin-top:10px;
  }
  .detailIconimg{
    width:43px !important;
    height:34px !important;
  }
  .toastsizechange{
    font-size:22px !important;
    margin-top:10px;
  }
  .err-mplayer .error-icon{
    width:88px !important;
  }
  .toast-msgprofile{
    font-size:30px;
    width:570px;
  }
  .lockmsg-toast{
    font-size:26px !important;
    top:-0.7% !important
  }

  .tv-profile-margin{
    padding-left: 0.8rem;
  }

  .btn-submit-profilebtn {
    font-size:27px !important;
    letter-spacing: 0px;
    border-radius: 5px !important;
    margin-top: 4px;
  }

  .age-grp-msg span {
    font: normal normal normal 22px/30px 'Roboto', sans-serif;
    width: 670px;
  }

  .age-grp-msg img {
    width: 75px !important;
    padding-left: 20px;
  }
  .col-5age-msg{
    margin-left:-150px !important;
  }
  .age-grp-msg:before {
    border-width: 14px !important;
    top:33% !important;
  }
  .tv_set_icon {
    width: 24% !important;
  }
  .tv_menu_icon{
    margin-left: 4.5rem !important;
    object-position: -1px 2px !important;
  }
  .tv_now, .tv_later{
    font-size: 20px !important;
  }
  .pre_timing{
    margin-top: 27px;
  }
  .channel-no{
    font-size: 1.5rem !important;
    margin-top: -7px !important;
  }
  .tv_set_name{
    margin-top: 1rem !important;
    font-size: 26px !important;
  }
  .tv_set_title{
    font-size: 22px !important;
  }
  .tv_set_head, .tv_help_head, .tv_play_head, .legal_data{
    font-size: 32px !important;
  }
  .tv_offline_text{
    font: normal normal normal 30px/46px 'Roboto', sans-serif !important;
  }
  .tv_subs_img{
    max-width: unset !important;
  }
  .help-txt{
    font: normal normal normal 23px/29px 'Roboto', sans-serif !important;
    margin-left: 30px;
    top:-7px !important;
    width:90% !important;
  }
  .legal_data{
    color: #BEBEBE !important;
    height: 73vh !important;
    border: none;
  }
  .legal_data p, .legal_data h4{
    font-size: unset !important;
  }
  .tv_playback_icon{
    width: 33% !important;
  }
  .legal_img{
    height: 39px!important;
  }
  .unlocktoastIcon{
    height: 39px !important;
    margin-right: 1rem !important;
  }
  .legal-img{
    height: 39px!important;
  }
  .unlock_toast_tv{
    padding-top: 1rem !important;
  }
  .tv_video_playback{
    padding-inline-start: 20px !important;
    padding-inline-end: 27.8rem !important;
  }
  
  .header-tv-h3{
    text-align: center;
    font-size: 48px!important;
    margin-top: 10%;
  }
  .header-tv-h4, .tv_confirm_head h4{
    font-size: 36px !important;
  }
  .para-tv-header{
    text-align: center !important;
    font-size: 36px!important;
    margin-top: 1%;
    margin-bottom: 5% !important;
  }
  .num-keyboard{
    margin-top: 10px;
}
.tv_faq_pg{
  margin-top: 7% !important;
}
.tv_register{
  justify-content: flex-end;
}
.smart-tv-next{
  margin-left: 41% !important;
}
.tv_err_img{
  width: 13rem !important;
  opacity: .7 !important;
}
.tv_faq_pg .legal-head{
  margin-top: unset !important;
}
.faq_head, .faq_title_head, .preview-heading, .tv_screen_head{
  font-size: 30px !important;
}
.faq_title_head {
  margin-top: 2rem!important;
}
.faq_head{
  margin-left:-10px;
}
.legal-heading{
  font: normal normal normal 32px/48px 'Roboto',sans-serif !important;
}
.legal-text{
  font-size:24px !important;
  width: 439px;
  padding-bottom: 122px !important;
}
.help-txt-legal{
  margin-left: -3%;
  font: normal normal normal 22px/29px 'Roboto',sans-serif !important;
  max-width: 460px;
  text-align:center;
  width: 26rem !important;
}
.standgy_icon_80{
  width:145px !important;
  margin-top: 29px;
}
.legal_title_head{
  font-size: 48px !important;
  padding-left: 6vw !important;
  margin-top: 2% !important;
}
.tv_offline_title{
  font: normal normal normal 34px/35px 'Roboto',sans-serif !important;
}
.tv_confirm_btn{
  font-size: 33px !important;
  padding-left: 9rem !important;
  padding-right: 9rem !important;
}
.faq_title, .faq_answer, .preview-heading.later, .tv_help_mail, .tv_play_name, .tv_err_msg, .tv_offline_btn, .tv_confirm_pg{
  font-size: 30px !important;
}
.tv_otp_err{
  font-size: 24px !important;
  margin-top: unset !important;
  margin-bottom: 127px!important;
}
.faq_question, .tv_lock_card p, .tv_help_num, .tv_confirm_con{
  font-size: 28px !important;
}
p.tv_lock_text, .tv_help_text, .tv_rail_time, .tv_rail_seas, .tv_rail_desc{
  font-size: 24px !important;
}
.tv_rail_seas, .tv_rail_desc{
  opacity: 0.8;
}
.details_tv_head{
  margin-top: 3.5rem !important;
}
.tv_settings_cards{
  padding: 30px 10px 21px !important;
}
.tv_err_off_img{
  width: 11.5rem !important;
}
.lock_err{
  margin-top: unset !important;
  font-size: 24px !important;
}
.custom_radio.faq_head label::before, .custom_radio.faq_head label::after{
  width: 29px;
  height: 29px;
}
.custom_radio.faq_head label::after{
  left: -24px !important;
}

.otp-box-tv{
  margin-top:6.5rem
}
.otp-form-control-tv {
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 55px;
}
.dots{
  height: 20px;
  width: 20px;
  margin-bottom: 15px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center;
  margin-left: 12px;
}
.dots-v{
  height: 20px;
  width: 20px;
  margin-bottom: 20px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center !important; 
  /* margin-left: 5px; */
}
.dots-e{
  height: 20px;
  width: 20px;
  margin-bottom: 20px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center !important; 
  /* margin-left: 8px; */
}
.lock-toast-tv{
  height: 100px !important;
  font-size: 26px !important;
  width: 447px !important;
  box-shadow: none;
  border: none!important;
}
.topSearchHeading-tv{
  margin-left: 47px;
  color: #CACACA;
  font: normal normal normal 26px/29px 'Roboto', sans-serif !important;
}
  .sliderWrapper {
    display: flex;
    flex-direction: row;
    height: 200px;
    width: 100%;
    overflow: auto;
    margin-left: 30px;
  }
  .search-modal-text-tv {
    font: normal normal normal 18px/40px 'Roboto', sans-serif;
    margin-top: 1%;
    margin-left: 7rem;
    margin-right: 7rem;
    line-height: initial;
    text-align: center;
}
	
.search-modal-text-img-tv {
    font: normal normal normal 18px/40px 'Roboto', sans-serif;
    margin-top: 3%;
    margin-left: 15rem;
}
.search-no-internet-modal-tv {
    height: 18rem;
    background: #1C1C1C;
    width: 42rem;
    margin-left: -12.5rem;
}
}
@media screen and (max-width: 1366px) {
  .header-tv-h3{
    text-align: center;
    font-size: 35px!important;
    margin-top: 5% !important;
  }
  .header-tv-h4{
    font-size: 36px !important;
  }
  .para-tv-header{
    text-align: center !important;
    font-size: 25px!important;
    margin-top: 1% !important;
    margin-bottom: 3% !important;
  }
  .num-keyboard{
    margin-top: 10px;
}
.otp-box-tv{
  margin-top:6.5rem
}
.otp-form-control-tv {
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 55px;
}
.dots{
  height: 15px;
  width: 15px;
  margin-bottom: 5px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center;
  margin-left: 2px;
}
.dots-v{
  height: 15px;
  width: 15px;
  margin-bottom: 5px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center !important; 
  margin-left: 5px;
}
.dots-e{
  height: 15px;
  width: 15px;
  margin-bottom: 5px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center !important; 
  margin-left: 8px;
}
}
@media screen and (max-width: 1280px) {
  .header-tv-h3{
    text-align: center;
    font-size: 35px!important;
    margin-top: 10%;
  }
  .para-tv-header{
    text-align: center !important;
    font-size: 25px!important;
    margin-top: 1%;
    margin-bottom: 5% !important;
  }
  .num-keyboard{
    margin-top: 10px;
}
.otp-box-tv{
  margin-top:6.5rem
}
.otp-form-control-tv {
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 55px;
}
.dots{
  height: 15px;
  width: 15px;
  margin-bottom: 5px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center;
}
.dots-v{
  height: 15px;
  width: 15px;
  margin-bottom: 5px;
  background-color: white;
  display: flex;
  border-radius: 20px;
  text-align: center !important; 
  margin-left: 5px;
}
}
.tv-rail-div{
  width: 95% !important;
  
   }
   .setting-width{
 width: 82%!important;
  }
  .tv-setting{
    height:100vh;
    margin-top: 12px;}

    .main-menu-expand ul>li.active#shows>a>span>img.menu-icon{
      object-position: 0px -121px!important;}
      
   .main-menu-expand:hover ul>li>a>.sidebar-imgspan img {
    height: 45px;
   }
    .main-menu-expand ul>li>a>.sidebar-imgspan img {
      height: 45px; 
      vertical-align: middle;
    } 
    .set_lan_width{
      width: 285px!important;
      height: 267px!important;
    }
    .log_width{
      width: 120px !important;
      margin-top: 4rem !important;
    }
    .log_font{
      font-size:24px !important;
      margin-top: 3rem !important;
    }
    .logout_font{
      font-size:20px !important;
      width: 290px;
    }

/* for the curve style */
@media screen and (min-width: 1920px)  {
  .tv-setting{
    margin-top: 20px;
  }
  .logout_font{
    font-size:24px !important;
    width: 471px;
  }
  .log_width{
    width: 120px !important;
    margin-top: 8rem !important;
  }
  .has-outline {
    display: inline-block;
    position: relative;
    outline: none !important;
  }
  
  .has-outline:focus::after,  
  .btn.has-outline:focus::after{
    border-radius: 6px !important;
    padding: 5px !important;
    /* border: 2px solid white !important; */
    outline: none !important;
    position: absolute;
    content: '';
    top: -12px !important;
    left: -12px !important;
    bottom: -12px !important;
    right: -12px !important;
    outline-offset: unset !important;
  }

  .has-outline-lang {
    display: inline-block;
    position: relative;
    outline: none !important;
  }
  
  .has-outline-lang:focus::after,  
  .btn.has-outline-lang:focus::after{
    border-radius: 6px !important;
    padding: 5px !important;
    /* border: 2px solid white !important; */
    outline: none !important;
    position: absolute;
    content: '';
    top: -4px !important;
    left: -4px !important;
    bottom: -4px !important;
    right: -4px !important;
    outline-offset: unset !important;
  }

  .has-outline-key {
    display: flex;
    position: relative;
  }
  .subscription_focus{
    width:78rem;
  }
 .subscription_focus:focus{
    border: 3px solid #EEEEEE !important;
  }
  .player-epg{
    border: 3px solid transparent !important;
  }
  .planListingtv{
    height: 240px !important;
  }
  .player-epg:hover, 
  .player-epg:focus{
  border: 3px solid #EEEEEE !important;
}
  .has-outline-key:focus::after, 
  .btn.has-outline-key:focus::after{
    border-radius: 6px !important;
    padding: 5px !important;
    /* border: 2px solid white !important; */
    position: absolute;
    content: '';
    top: -5px !important;
    left: -5px !important;
    bottom: -5px !important;
    right: -5px !important;
    outline-offset: unset !important;
    }
    .has-outline-player {
      display: inline-block;
      position: relative;
      outline: none !important;
    }
    
    .has-outline-player:focus::after, 
     
    .btn.has-outline-player:focus::after{
      border-radius: 45px !important;
      padding: 5px !important;
      /* border: 2px solid white !important; */
      outline: none !important;
      position: absolute;
      content: '';
      top: -7px !important;
      left: -7px !important;
      bottom: -7px !important;
      right: -7px !important;
      outline-offset: unset !important;
      }
      .has-outline-label {
        display: inline-block;
        position: relative;
        outline: none !important;
      }

      .has-outline-label:focus::after, 
      .btn.has-outline-label:focus::after{
        border-radius: 45px !important;
        padding: 5px !important;
        /* border: 2px solid white !important; */
        outline: none !important;
        position: absolute;
        content: '';
        top: -3px !important;
        left: -3px !important;
        bottom: -3px !important;
        right: -3px !important;
        outline-offset: unset !important;
        }
        .has-outline-player-sub {
          display: inline-block;
          position: relative;
          outline: none !important;
        }
        .has-outline-player-sub:hover::after, 
        .has-outline-player-sub:focus::after, 
        .btn.has-outline-player-sub:hover::after, 
        .btn.has-outline-player-sub:focus::after{
          border-radius: 45px !important;
          padding: 5px !important;
          /* border: 2px solid white !important; */
          outline: none !important;
          position: absolute;
          content: '';
          top: -3px !important;
          left: -3px !important;
          bottom: -3px !important;
          right: -3px !important;
          outline-offset: unset !important;
          }
          .has-outline-player-v-set { 
            display: inline-block;
            position: relative;
            outline: none !important;
          }
          
          .has-outline-player-v-set:focus::after, 
           
          .btn.has-outline-player-v-set:focus::after{
            border-radius: 45px !important;
            padding: 5px !important;
            /* border: 2px solid white !important; */
            outline: none !important;
            position: absolute;
            content: '';
            top: -3px !important;
            left: -3px !important;
            bottom: -3px !important;
            right: -3px !important;
            outline-offset: unset !important;
            }
        .tv-reg-margin{
          margin-top: 4rem !important;
        }
      .tv-trailer-color{
        background-color: #4E4E4E;
        margin-top: 0rem;
        padding: 0.45em 0.85em !important;
        font: normal normal normal 33px 'Roboto', serif !important;
      }
      .tv-text-discount{
        font: normal normal normal 50px "Roboto",sans-serif;
        color: #EE3D1B!important;
        font-weight: 400 !important;
       }
       .coupon-price-tv i{
        font-size: 2.5rem !important;
        font-weight: 400 !important;
      }
      .tv-text-planname{
        font: normal normal normal 30px "Roboto",sans-serif;
        font-weight: 300;
      }
      .original-price-tv i{
        font-size: 1.5rem !important;
        font-weight: 300 !important;
        color: #888888 !important;
      }
      .strikethrough-tv:before {
        position: absolute;
        color: #EE3D1B;
        content: "";
        left: 0;
        top: 50%;
        right: 0;
        border-top: 3px solid !important;
        border-color: inherit;
        
        -webkit-transform:rotate(-12deg);
        -moz-transform:rotate(-12deg);
        -ms-transform:rotate(-12deg);
        -o-transform:rotate(-12deg);
        transform:rotate(-12deg);
      }
      .audio_button{
        margin-left: 1rem;
      }
      .tv-sub-center-div h1{
        font-size: 60px!important;
       }
       .tv-sub-center-div {
        width: 60rem!important;
       }
    }
.has-outline {
  display: inline-block;
  position: relative;
  outline: none !important;
}

.has-outline:focus::after, 
.btn.has-outline:focus::after{
  border-radius: 6px !important;
  padding: 5px !important;
  /* border: 2px solid white !important; */
  outline: none !important;
  position: absolute;
  content: '';
  top: -12px !important;
  left: -12px !important;
  bottom: -12px !important;
  right: -12px !important;
  outline-offset: unset !important;
}
.has-outline-key {
  display: flex;
  position: relative;
}

.has-outline-key:focus::after,  
.btn.has-outline-key:focus::after{
  border-radius: 6px !important;
  padding: 5px !important;
  /* border: 2px solid white !important; */
  outline: none !important;
  position: absolute;
  content: '';
  top: -5px !important;
  left: -5px !important;
  bottom: -5px !important;
  right: -5px !important;
  outline-offset: unset !important;
  }
  .has-outline-player {
    display: inline-block;
    position: relative;
    outline: none !important;
  }
  
  .has-outline-player:focus::after, 
   
  .btn.has-outline-player:focus::after{
    border-radius: 45px !important;
    padding: 5px !important;
    /* border: 2px solid white !important; */
    outline: none !important;
    position: absolute;
    content: '';
    top: -7px !important;
    left: -7px !important;
    bottom: -7px !important;
    right: -7px !important;
    outline-offset: unset !important;
    }
    .has-outline-player-v-set {
      display: inline-block;
      position: relative;
      outline: none !important;
    }
     
    .has-outline-player-v-set:focus::after, 
     
    .btn.has-outline-player-v-set:focus::after{
      border-radius: 45px !important;
      padding: 5px !important;
      /* border: 2px solid white !important; */
      outline: none !important;
      position: absolute;
      content: '';
      top: -3px !important;
      left: -3px !important;
      bottom: -3px !important;
      right: -3px !important;
      outline-offset: unset !important;
      }
    .has-outline-player-sub {
      display: inline-block;
      position: relative;
      outline: none !important;
    }
    .has-outline-player-sub:hover::after, 
    .has-outline-player-sub:focus::after, 
    .btn.has-outline-player-sub:hover::after, 
    .btn.has-outline-player-sub:focus::after{
      border-radius: 45px !important;
      padding: 5px !important;
      /* border: 2px solid white !important; */
      outline: none !important;
      position: absolute;
      content: '';
      top: -3px !important;
      left: -3px !important;
      bottom: -3px !important;
      right: -3px !important;
      outline-offset: unset !important;
      }

    .player-margin{
      margin-left: 2px;
    }
    .player-margin-subs{
      margin-left: 5px;
    }
  .tv-font-size-primary{
   font: normal normal normal 33px 'Roboto', sans-serif !important; 
  }
  .tv-font-size-primary:focus, 
  .tv-font-size-primary:hover{
    font: normal normal normal 33px 'Roboto', sans-serif !important; 
  }

   .tv-font-size-sec:focus, 
   .tv-font-size-sec:hover{
     font: normal normal normal 30px 'Roboto', sans-serif !important; 
     border-radius: 4px !important;
   }
   
   .margin-top{
    margin-top: 0.5rem !important; 
    margin-bottom:0.75rem !important;
   }

   .tv-lock-margin-top{
    margin-top: 4rem;
   }
   .tv-dislang-mb{
     margin-bottom: 1rem !important;
   }

   .genborder{
    width: fit-content;
    display: inline-block;
    border: 2px solid transparent;
    border-radius: 50px;

   }
   .genFocus{
    border: 2px solid white;
   }
   .btn-check:checked+.genborder>.btn-secondary{
    color: #EEEEEE!important;
  
    background: #676767!important;
    border-radius: 36px;
   }

   .tv-gender-check-label:focus{
    border: solid 2px #EEEEEE;
   }
   .border-color{
    border-color: transparent!important;
   }
   .genMargin{
    margin: 7px!important;
   }
   .reg-mar{
    margin-left: -47px;
   }
   .reg-form-text{
    margin-left: -29px;
   }

    .tv_voucher_top{
      margin-top: 8rem;
      height: 63px;
      font: normal normal normal 48px/37px "Roboto", sans-serif !important;
     }
     .tv_voucher_sub{
      font: normal normal normal 36px/38px "Roboto", sans-serif !important;
     }
     .tv_sub_tle{
      height: 48px;
      font: normal normal normal 36px/38px "Roboto", sans-serif;
      margin-top: 1.5rem;
     }
     .voucher-keyboard-cover{
      margin-top: 2.2rem;
     }
     .tv-voucher-code{
      margin-left: -2.2rem;
     }
     .tv-voucher-pin{
      margin-left: -2.2rem;
     }
   .tv-vocher-sbmit{
    font-size: 33px !important;
    margin-top: 8rem;
    margin-left: -1rem;
    /* color: #FFFFFF !important; */
    border-radius: 8px !important ;
    width: fit-content!important;
  height: 4.75rem !important;
   }
   .tv-voucher-btn{
     justify-content: left!important;
   }
   
.basic_plan_data{
  margin-left: 13.563rem;
}
   
   .tv-subscriptionWrapper{
    margin-left: 3.75rem;
    min-width: 82%!important;
   }
   .tv-subscriptionWrapper>.subImgContainer{
    margin-left: -4.5rem !important;
   }
   .tv-sub-center-div h1{
    font-size: 60px!important;
   }

   .tv-subscriptionWrapper>.mt-3>.w-22{
    margin-top: 10px;
    margin-bottom: 10px;
   }
   .tv-sub-price{
    font: normal normal normal 40px/37px "Roboto", sans-serif !important;
    color: #FFFFFF;
   }
   .tv-sbcn-tle{
    font: normal normal normal 35px/37px "Roboto", sans-serif;
    color: #CACACA;
    margin-top:5px ;
   }
   .tv-subplan-card{
    margin-top: 2.5rem!important;
   }
   .tv-subplan-card>.planCard img{
    width: 425px;
    height: 240px;
    border-radius: 8px;
    opacity: 1;

   }
   .tv-sb-planName{
    font: normal normal normal 36px/37px "Roboto",sans-serif;
    color: #EEEEEE;
   }
   .tv-text-muted{
    font: normal normal normal 24px/25px "Roboto",sans-serif;
    letter-spacing: 0px;
    color: #ACACAC!important;
    
   }
  .tv-text-muted-mt{
   margin-top: 10px;
  }
  .tv-subs-desc-div{
    font-size: 21px!important ;
    height:90px !important;
    color: #FFFFFF;
  }
    
  .tv-sub-price i{
    color: #FFFFFF;
    margin-right: 4px;
    font-size: 32px!important;
  }
  .tv-vocher-pop1{
    font-size: 22.2px!important;
    margin-top: -6px;
  }
  .tv-vocher-pop2{
    font-size: 23px!important;
    margin-top: 2px;
    margin-bottom: 26px;
  }
  .tv-plan{

    font: normal normal normal 25px/42px "Roboto", sans-serif;
  }
   .tv-channels,.tv-OTT,.tv-Channel-Bundle,.tv-A-La-Carte{
    font: normal normal normal 24px/42px "Roboto", sans-serif;
   }

   .voucher-plans-width{
    width:43rem;
   }

   .redeem-icon-tv{

    font-size: 14px;
    padding: 0px 4px;
    /* margin: -1px; */
    background: transparent;
    color: black;
    margin-right: 10px;
  
  }

  .redeem-text-tv{
    font-size: 33px;
    vertical-align: inherit;
  }
  .btn-redeem-tv{
    padding-left: 1.2rem !important;
    padding-right: 1.2rem !important;
  }
  .voucher-margin-left{
    margin-left: 5px;
  }
  .tv-player-title h3, .tv-contTitle{
    font-size: 40px!important;
  }

  .tv-contDetails-details, .tv-detailDescPlayer{
    font-size: 26px !important;
  }
  
  .tv-player-title p{
    font-size: 30px!important;
  }
  .tv_err_inter-img{
   margin-left: -3rem;
  }
  .tv-reg-margin{
    margin-top: 4.5rem !important;
    margin-bottom: 5rem!important;
  }

  .padding-right{
    padding-right:0px !important;
  }

  .shaka_video_div{
    display: block !important;
    position: absolute;
    top: 0;
    left: 0;
  }

  .shaka_player_popup{
    height: 0;
    width: 0;
  }


.legal_data > p > a {
  pointer-events: none;
}
.showsTostTV{
 margin-top:2.7rem!important; 
 margin-left: 5rem!important; 
 width: 28.1rem!important; 
 font-size: 1.6rem!important; 
}
.input-indent{
text-indent: .5px;
/* direction: rtl; */
max-width: 600px;

}
.signIn{
  margin-left: 43px;
}
.form-label-signIn{
  margin-left: -41px;
}
.profile-imgcls-tv{
  margin-top: -27px;
}
.tv-age-values{
  margin-left: -19px;
}
.register-logo-tv{
  width: 350px!important;
  height: 260px!important;
  margin-top: -6rem!important;
}

.reg-keybord-tv{
  margin-top: 3.5rem;
}
.coupon_keyboard_tv{
  position: relative;
  left: -1.5rem;
  top:1rem
}
.reg1-keybord{
  margin-top: 109px;
}
.reg-name-tv{
  margin-top: -41px;
}
.coupon_input{
  display: flex;
  justify-content: center;
}
.coupon_mar{
  margin-left: -0.5rem !important;
  border: 2px solid transparent;
}

.hide_div_data_tv{
  visibility: hidden;
}
.hide_coupon_data_tv{
  background: #000000;
}
@media screen and (min-width: 1920px) {
  .image_size{
    max-height: 213px !important;
  }
  .tag_rail_gift_tv{
    width: 36px!important;
    margin: 4px!important;
  }
  .tag_rail_tv{
    width: 26px!important;
    margin: 9px!important;
  }
  .rail_gift_epi{
    width: 34px!important;
    margin-bottom: 24px;
    margin-left: 4px;
  }
  .no_episodes_tv{
    text-align: left !important;
    font-family: 'Roboto Regular', sans-serif;
    opacity: 0.96;
    font-size: 26px !important;
    padding-left: 1px!important;
    
  }
  .series-card-desc-tv{
    font-size: 18px!important;
  }

  }
.tag_detail_gift_tv{
  width: 48px!important;
}
.profileName_margin{
  margin-left: 10px!important;
}
 .coupon-code-font{
  font: normal normal normal 30px 'Roboto', sans-serif !important;
  margin-bottom: 2rem !important;
 }


/* .tv-player-media-content{
  top: 12%!important;
} */
.tv_EPG_fav{
  width: 35px!important;
  margin-top: -1rem;
  margin-left: -5px;
}
.EPG_rail_money_tv{
  width: 30px!important;
  height: 30px!important;
  padding: 5px 8px!important;
}
.profile_key_style{
  margin-top: -5px!important;
}
.tv-card-border{
  border: 1px transparent !important;
  }

  .Shows-episodes-tv>.p-1>.d-flex p{
    font-size: 26px!important;
    margin: 10px 0px 10px 0px;

  }
  .Shows-episodes-tv>.p-1>.fw-bold{
    font-size: 26px!important;
    margin-bottom: 8px!important;
  }
  .episode-top-rails-tv{
    margin-top: 10%;
    margin-left: -0.7%;
  }
  .video_quality_tv{
    margin-top: 10%;
    margin-left: 2%;
  }
 .video_quality_tv>.fdiv>.qltTitle{
 font-size: 28px!important;
 font-weight: 300!important;
 margin-top: -9%;
 }

 .audio_list_tv {
  margin-left: 6%!important;
 }
 .audio_list_tv>.fdiv>.audio-substitle{
  font-size: 28px!important;
  font-weight: 300!important;
  width: 12rem!important;
 }
 .audio_list_tv>.pe-2>.audio-substitle{
  font-size: 28px!important;
  font-weight: 300!important;
  margin-top: -17px;
 }
 .audio-tv{
  font-size: 28px!important;
  font-weight: 300!important;
  margin-top: -15px;
 }
 .audio-vod-tv{
  font-size: 28px!important;
  font-weight: 300!important;
  width: 12rem!important;
  margin-top: -17px;
 }
 .position-tv{
  position: fixed;
  width: 90%;
 }
 .oops-img-tv{
  width: 5rem;
 }
 
 .shaka-controls-container[shown=true]~.shaka-text-container {
  bottom: 22%!important;
 }
 /* .shaka-controls-container~.shaka-text-container {
  bottom: 22%!important;
 } */
 .shaka-text-container span {
   background-color: transparent!important;
 }
 .shaka-text-container div {
  color: #FFFFFF!important;
  text-shadow: 0px 0px 3px #00000080!important;
  opacity: 1!important;
  font-size: 30px!important;
}
.video-control-bottom {
  bottom: 22%!important;
}
.video-subtitle-bottom{
  bottom: 5%!important;
}
.tv-apps-size{
  object-fit: cover;
 }
 .tv-subtle{
  margin-left: -5rem;
 }
 .search-his-img-tv{
  width: 25px !important;
}
.search-form-control-tv{
 width: 430px !important;
}
.audio-list-tv-text {
 margin-left: 6%!important;
}

 .tv_errSrchTxt{
  font-size: 35px!important;
}

.tv_errTxt{
 font-size: 28px!important;
}

.tv-search-form-control,.tv-search-form-control::placeholder, .tv-search-history p, .tv-search-data p{
 font-size: 26px!important;
}

.tv-border-none{
  border-radius: 0px !important;
}
 @media screen and (min-width:1919px) {
  .event-card-tv{
    padding-top: 61.25% !important;
  }
 }
 .search_border{
  border-bottom: 2px solid white!important;
 }
 .disp_lang_tv{
  overflow: auto;
  padding-top: 50px;
  padding-bottom: 50px;
  justify-content: center;
 }
.searchTV-input{
  width: 42%!important;
  background-color: transparent!important;
  margin-left: 4rem!important;
  direction: rtl!important;
}
.search-place-placeholder{
  color: #EEEEEE;
  opacity: 0.5;

}
@media screen and (max-width:1280px){
  .tv-font-size-primary {
    font: normal normal normal 25px 'Roboto', sans-serif !important;
  }
  .tv-font-size-primary:focus, .tv-font-size-primary:hover {
    font: normal normal normal 25px 'Roboto', sans-serif !important;
  }
  .register-logo-tv {
      width: 250px!important;
      height: 200px!important;
      margin-top: -3rem!important;
  }
  .help_pg {
    margin-top: 1.5rem !important;
    margin-left: 0rem;
  }
  .legal_data {
    color: #BEBEBE !important;
    height: 72vh !important;
    border: none;
    font-size: 20px;
  }
  .legal_title_head {
    font-size: 35px !important;
    padding-left: 6vw !important;
    margin-top: 10% !important;
  }
  .navbar-logo-tv {
    width: 115px !important;
    height: 55px;
    position: relative;
  }
  .tv-sbcn-tle {
    font: normal normal normal 25px/30px "Roboto", sans-serif;
  }
  .image_size {
    max-height: 160px !important;
    width: 45% !important;
  }
  .tv-sb-planName {
    font: normal normal normal 25px/30px "Roboto",sans-serif;
  }
  .tv-sub-price i {
    font-size: 22px!important;
}
  .tv-sub-price {
    font: normal normal normal 28px/30px "Roboto", sans-serif !important;
  }
  .tv-text-muted {
    font: normal normal normal 17px/20px "Roboto",sans-serif;
  }
  .tv-subs-desc-div {
    font-size: 15px!important;
  }
  .plancard-tv{
    width: 110% !important;
  }
  .validity_pack_tv{
    position: relative;
    left: -5rem;
  }
  .redeem-text-tv {
    font-size: 20px;
  }
  .proceed_margin {
    margin-top: 12rem;
  }
  .tc-title-head{
    margin-top: 0rem !important;
  }
  .otptexttv {
    font: normal normal normal 20px/25px "Roboto", sans-serif !important;
    width: 635px !important;
    margin-left: -180px !important;
    margin-bottom: 5rem !important;
  }
  .tv-sub-center-div h1 {
    font-size: 35px!important;
  }
  .pcroceed-btntv {
    font-size: 23px !important;
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }
  .lang-width{
    width: 21rem;
    text-align: left !important;
  }
  .lang-width:focus{
    border: 2px solid #eee !important;
  }
  .tv-font-size-sec:focus, .tv-font-size-sec:hover {
    font: normal normal normal 21px 'Roboto', sans-serif !important;
    border-radius: 4px !important;
}
.tv_voucher_top{
  margin-top: 5rem;
  height: 45px;
  font: normal normal normal 34px/37px "Roboto", sans-serif !important;
 }
 .tv_voucher_sub{
  font: normal normal normal 25px/38px "Roboto", sans-serif !important;
 }
 .voucher-keyboard-cover{
  margin-top: 2.2rem;
  margin-right: 2rem;
 }
 .tv-vocher-sbmit{
  font-size: 23px !important;
    margin-top: 0rem;
    padding-left: 3rem !important;
    padding-right: 3rem !important;
    height: 3.75rem !important;
 }
 .delete-img{
  width: 11rem !important;
  margin-top: -2rem;
 }
 .form-label-signIn{
  font-size: 20px;
}
.plan_span{
  font-size: 17px !important;
}
.faq_title{
  font-size: 20px;
}
 .player-epg:hover, 
  .player-epg:focus{
  border: 2px solid #EEEEEE !important;
}
.tv-subtle{
  margin-left: 0rem;
}
.tv_EPG_fav {
  width: 25px!important;
  margin-top: -0.8rem;
  margin-left: -18px;
}
.subscription_focus:focus{
  border: 3px solid #EEEEEE !important;
}
.subscription_focus{
  width: 53rem;
  height: 11rem;
}
}