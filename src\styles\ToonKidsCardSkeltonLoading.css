
.container {
    height: 100vh;
    display: flex;
    flex-flow: wrap row;
    justify-content: center;
    align-items: center;
}
.skeleton {
    margin-top: 20px;
    padding:15px;
    max-width: 300px;
    width: 100%;
    /* background: #fff; */
    margin-bottom: 20px;
    margin-left: 30px;
    border-radius: 5px;
    display: flex;
    /* box-shadow: 0 3px 4px 0 rgba(227, 225, 225, 0.753), 0 3px 3px -2px rgba(114, 110, 110, 0.2), 0 1px 8px 0 rgba(128, 119, 119, 0.12); */
}
.skeleton .square {
    height: 80px;
    border-radius: 5px;
    /* background: rgba(130, 130, 130, 0.2); */
    background: -webkit-gradient(linear, left top,
    right top, from(transparent), 
    color-stop(rgba(211, 206, 206, 0.893)),
    to(transparent));
      
background: linear-gradient(90deg, transparent,
rgba(120, 118, 118, 0.641), transparent);
    background-size: 800px 100px;
    animation: wave-squares 0.8s infinite ease-out;
}
.skeleton .line:last-child{
   margin-bottom: 0;
}
.circle{
   border-radius: 50% !important;
    height: 320px !important;
    width: 320px;
}

@keyframes wave-squares {
   0% {
       background-position: -468px 0;
   }
    100% {
       background-position: 468px 0;
   }
}
