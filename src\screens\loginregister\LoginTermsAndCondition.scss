$color-white: #ffffff;
$font-family-roboto: Roboto;
$position-absolute: absolute;
$font-weight-bold: bold;
$position-relative: relative;
$display-flex: flex;

.login-terms-and-condition {

    .terms-and-condition-logo {
        width: 244.04px;
        position: $position-absolute;
        left: 91px;
        top: 34px;
    }
    .terms-and-condition-title {
        height: 64px;
        width: 818px;
        color: $color-white;
        font-family: Robot<PERSON>;
        font-size: 55px;
        font-weight: $font-weight-bold;
        letter-spacing: 0;
        line-height: 60px;
        text-align: center;
        position: $position-absolute;
        top: 81px;
        left: 553px;
    }
    .login-terms-and-condition-title {
        height: 105px;
        width: 750px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 36px;
        letter-spacing: 0.39px;
        line-height: 52.5px;
        text-align: center;
        position: $position-absolute;
        top: 237px;
        left: 595px;
    }
    .radio-box {
        position: $position-absolute;
        top: 378px;
        left: 89px;
        display: $display-flex;
    }

    .checkbox-containerreg {
        display: $display-flex;
        align-items: center;
        outline: unset;
        position: $position-relative;
        left: 597px;
        top: 15px;
    }

    .checkbox-containerreg:focus input~.checkmarkreg {
        z-index: 1;
        background: transparent 0% 0% no-repeat padding-box;
        border: 4px solid #981c15;
        border-radius: 6px;
    }

    .checkbox-subcontainereg {
        bottom: 45px;
        display: block;
        position: $position-relative;
        cursor: pointer;
        font-size: 22px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .checkbox-subcontainereg input {
        position: $position-absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkmarkreg {
        position: $position-absolute;
        top: 0;
        left: 0;
        height: 66px;
        width: 64px;
        background-color: #212224;
        border-radius: 6px;
    }

    .checkbox-subcontainereg input:checked~.checkmarkreg {
        background-color: #212224;
    }

    .checkbox-subcontainereg input:checked~.checkmarkreg:after {
        display: block;
    }

    .checkbox-subcontainereg .checkmarkreg:after {
        left: 20px;
        top: 14px;
        width: 14px;
        height: 25px;
        font-weight: $font-weight-bold;
        border: solid white;
        border-width: 0px 7px 7px 0;
        -webkit-transform: rotate(35deg);
        -ms-transform: rotate(35deg);
        transform: rotate(35deg);
    }

    .checkmarkreg:after {
        content: '';
        position: $position-absolute;
        display: none;
    }

    .checkbox-error-msg {
        width: 714px;
        height: 54px;
        background: #eeeeee 0% 0% no-repeat padding-box;
        border-radius: 6px;
        opacity: 1;
        font-size: 28px;
        text-align: center;
        padding-top: 16px;
        letter-spacing: 0px;
        color: #981c15;
        z-index: 1;
        position: $position-absolute;
        top: 99px;
    }

    .checkbox-error-msg:after,
    .checkbox-error-msg:before {
        left: 22px;
        bottom: 67px;
        border: solid transparent;
        content: ' ';
        width: 0;
        position: $position-absolute;
        pointer-events: none;
    }

    .checkbox-error-msg:after {
        border-width: 34px 13px 18px 14px;
        border-bottom: 20px solid #eeeeee;
    }

    .checkbox_title {
        height: 56px;
        width: 462px;
        color: $color-white;
        font-family: $font-family-roboto;
        font-size: 24px;
        letter-spacing: 0px;
        line-height: 26px;
        position: $position-relative;
        left: 91px;
        bottom:10px;
    }

    .main-buttons {
        display: $display-flex;
        flex-direction: column;
        position: $position-absolute;
        top: 526px;
        left: 670px;

        .terms-and-condition-button {
            height: 72px;
            width: 740px;
            position: $position-relative;
            right: 55px;
            border-radius: 8.8px;
            background-color: #2e303d;
            color: $color-white;
            font-family: $font-family-roboto;
            font-size: 32px;
            font-weight: $font-weight-bold;
            letter-spacing: -0.51px;
            line-height: 35px;
            text-align: center;

            &:focus {
                right: 112px;
                width: 830px;
                border-radius: 10.03px;
            }
        }

        .privacy-policy-button {
            height: 72px;
            width: 740px;
            position: $position-relative;
            right: 55px;
            top: 32px;
            border-radius: 8.8px;
            background-color: #2e303d;
            color: #ffffff;
            font-family: $font-family-roboto;
            font-size: 32px;
            font-weight: $font-weight-bold;
            letter-spacing: -0.51px;
            line-height: 35px;
            text-align: center;

            &:focus {
                right: 112px;
                width: 830px;
                border-radius: 10.03px;
            }
        }

        .login-confirm-button {
            height: 72px;
            width: 830px;
            top: 64px;
            text-transform: uppercase;
            position: $position-relative;
            right: 112px;
            border-radius: 8.8px;
            background-color: #4b1512;
            color: #ffffff;
            font-family: $font-family-roboto;
            font-size: 32px;
            font-weight: $font-weight-bold;
            letter-spacing: -0.51px;
            line-height: 35px;
            text-align: center;

            &:disabled {
                color: #787878;
            }

            &:focus {
                right: 112px;
                width: 830px;
                border-radius: 10.03px;
                background-color: #981c15;
            }
        }
    }
}