import React, { useEffect, useState, useCallback } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import '../appsettings/HelpAndSettings/TermsAndConditions.scss'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushScreenViewEvent } from '../../GoogleAnalytics'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const RegTermsAndConditions = () => {
  const navigate = useNavigate()
  const { state } = useLocation()

  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const apaMetaDataTnC = useSelector(
    state => state?.initialReducer?.metaDataHelp
  )
  const region = localStorage.getItem('region')

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const translationsTnC =
    apaMetaDataTnC?.translations && JSON?.parse(apaMetaDataTnC?.translations)

  const handleScrollUp = () => {
    const element = document.getElementById('data-div')
    element.scrollTop += -300
    element.scrollTop == 0 && document.getElementById("regtermsandcondition").focus()
  }

  const handleScrollDown = () => {
    const element = document.getElementById('data-div')
    element.scrollTop += 300 
    element.scrollTop >= 300 && document.getElementById("regtermsandcondition").blur()
  }
  const navigateBack=()=>{
    state?.page == 'logintermsandcondition' ? navigate('/login-Terms-and-Conditons', { 
      state: { 
        checkbox: state?.checkbox,
        seriesEpisodeData: state?.seriesEpisodeData,
        fromDetailsPage: state?.fromDetailsPage,
        pageName: state?.pageName,
      } 
    }) : navigate('/register', {
      state: { 
        regmail: state?.regmail, 
        regpassword: state?.regpassword, 
        focus: true, checkbox: state?.checkbox,
        seriesEpisodeData: state?.seriesEpisodeData,
        fromDetailsPage: state?.fromDetailsPage,
        pageName: state?.pageName,
      }
    })
  }
  const handlesamsungkey = useCallback(
    (key, keycode) => {
      if (key.yellowcode === keycode || keycode === 10009 || keycode == 16) {
        state?.page == 'logintermsandcondition' ? navigate('/login-Terms-and-Conditons', { 
          state: { 
            checkbox: state?.checkbox, 
            seriesEpisodeData: state?.seriesEpisodeData,
            fromDetailsPage: state?.fromDetailsPage,
            pageName: state?.pageName,
          } 
        }) : navigate('/register', {
          state: { 
            regmail: state?.regmail, 
            regpassword: state?.regpassword, 
            focus: true, checkbox: state?.checkbox, 
            seriesEpisodeData: state?.seriesEpisodeData,
            fromDetailsPage: state?.fromDetailsPage,
            pageName: state?.pageName,
          }
        })
      }
    },
    [navigate]
  )

  const handleLgkey = useCallback(
    keycode => {
      if (keycode === 405 || keycode === 461 || keycode === 16 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)
) {
        state?.page == 'logintermsandcondition' ? navigate('/login-Terms-and-Conditons', { 
          state: { 
            checkbox: state?.checkbox,
            seriesEpisodeData: state?.seriesEpisodeData,
            fromDetailsPage: state?.fromDetailsPage,
            pageName: state?.pageName,
          } 
        }) : navigate('/register', {
          state: { 
            regmail: state?.regmail, 
            regpassword: state?.regpassword, 
            focus: true, checkbox: state?.checkbox,
            seriesEpisodeData: state?.seriesEpisodeData,
            fromDetailsPage: state?.fromDetailsPage,
            pageName: state?.pageName, 
          }
        })
      }
    },
    [navigate]
  )

  const keypresshandler = useCallback(
    event => {
      const keycode = event.keyCode
      if (keycode === 38) {
        handleScrollUp()
      } else if (keycode === 40) {
        handleScrollDown()
      } else if (typeof tizen !== 'undefined') {
        tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
        const codes = {
          yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
        }
        handlesamsungkey(codes, keycode)
      } else {
        handleLgkey(keycode)
      }
    },
    [handlesamsungkey, handleLgkey]
  )

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    // GA : ScreenView Event
    pushScreenViewEvent({screenName:'terms and condition',prevScreenName:'register'})

    if (translationsTnC?.language?.[region]?.atv_terms_and_conditions) {
      const element = document.getElementById('tyc_full_text_id')
      if (element) {
        element.style.backgroundColor = 'transparent'
        element.style.width = '1310px'
      }
    }
  }, [translationsTnC, region])

  const termsAndConditionsText =
    translationsTnC?.language?.[region]?.tyc_full_text || ''
  return (
    <div className="terms-and-condition">
      <div>
        <img
          src={'images/claro-video-logo.png'}
          className="claro-logo"
          alt="logo"
        />
        <button
          className="back-indicator focusable"
          id="regtermsandcondition"
          onClick={() => navigateBack()}>
          <img
            className="yellow-indicator"
            src={'images/yellow_shortcut.png'}
            alt="Yellow shortcut"
          />
          <img
            className="back-image"
            src={'images/back_button.png'}
            alt="Back button"
          />
          <p className="back-text">
            {translations?.language?.[region]?.atv_back_notification || 'Back'}
          </p>
        </button>
      </div>
      <div className="page-div">
        <div className="data-class" id="data-div">
          <div id="tyc_full_text_id">
            <SafeHTML html={termsAndConditionsText} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default RegTermsAndConditions