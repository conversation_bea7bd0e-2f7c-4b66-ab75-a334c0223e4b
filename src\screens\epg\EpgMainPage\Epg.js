import React, { lazy, Suspense, useMemo } from 'react'
import { EpgSkeletonLoading } from './EpgSkeletonLoading'
import EpgGrid from './EpgGrid'

// Memoized container styles to prevent recreation on every render
const containerStyles = {
  width: '1920px',
  position: 'absolute',
  height: '1080px',
  top: '0px',
  zIndex: 2
}

function Epg(props) {
  // Memoized props to prevent unnecessary re-renders of EpgGrid
  const epgGridProps = useMemo(() => ({
    setShowLiveControls: props?.setShowLiveControls,
    channelErr: props?.channelErr,
    handlePastContentDay: props?.handlePastContentDay,
    checkPastContent: props?.checkPastContent,
    subscriptions: props?.subscriptions
  }), [
    props?.setShowLiveControls,
    props?.channelErr,
    props?.handlePastContentDay,
    props?.checkPastContent,
    props?.subscriptions
  ])

  return (
    <div style={containerStyles}>
      <Suspense fallback={<EpgSkeletonLoading />}>
        <EpgGrid {...epgGridProps} />
      </Suspense>
    </div>
  )
}

// Memoize the component with custom comparison for better performance
export default React.memo(Epg, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.setShowLiveControls === nextProps.setShowLiveControls &&
    prevProps.channelErr === nextProps.channelErr &&
    prevProps.handlePastContentDay === nextProps.handlePastContentDay &&
    prevProps.checkPastContent === nextProps.checkPastContent &&
    prevProps.subscriptions === nextProps.subscriptions
  )
})
