
import { call, takeEvery } from "redux-saga/effects";
import { setSkeltonLoading, getIsLoggedinRefreshSuccess, getIsLoggedinRefreshError, getIsLoggedinV1Success, getIsLoggedinV1Error } from "../store/slices/login";
import { store } from "../store/sagaStore";
import { URL } from '../utils/environment'
import { request } from "../utils/request";

function* getIsLoggedinRefreshApi({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(request,
      URL.IS_LOGGED_IN_V1_URL + '&HKS=' + payload + '&region=' + region,
      {
        method: 'GET',
      },
      {
        onSuccess(response) {
          store.dispatch(getIsLoggedinRefreshSuccess(response))
        },
        onError(error) {
          store.dispatch(getIsLoggedinRefreshError(error))
        },
      }
    );
  } catch (error) {
    console.error('catch error --> ', error)
    store.dispatch(setSkeltonLoading(false))
  }
}
function* getIsLoggedinV1Api({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(request,
      URL.IS_LOGGED_IN_V1_URL + '&HKS=' + payload?.HKS + '&region=' + region,
      {
        method: 'GET',
      },
      {
        onSuccess(response) {
          store.dispatch(getIsLoggedinV1Success(response))
        },
        onError(error) {
          store.dispatch(setSkeltonLoading(false))
          store.dispatch(getIsLoggedinV1Error(error))
        },
      }
    );
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* isLoggedInSaga() {
  yield takeEvery('login/getIsLoggedinRefresh', getIsLoggedinRefreshApi)
  yield takeEvery('login/getIsLoggedinV1', getIsLoggedinV1Api)

}
