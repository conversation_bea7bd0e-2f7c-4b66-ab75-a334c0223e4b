.plan-selector-app-css {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: #121212;
}

.plan-selector-claro-logo {
	height: 34.36px;
	width: 169.64px;
	margin-top: 67px;
	margin-left: 90px;
}

.plan-selector-back-indicator-button {
	display: inline-block;
	color: #ffffff;
	width: 290px;
	height: 48px;
	border-radius: 6.6px;
	font-size: 16px;
	background-color: #2e303d;
	vertical-align: middle;
	float: right;
	margin-top: 25px;
	margin-right: 40px;


	.yellow-indicator-button {
		width: 20px;
		height: 20px;
		padding: 0px 24px 0px 24px;
		vertical-align: middle;
	}

	.back-indicator-image {
		width: 35px;
		height: 28px;
		padding: 0px 24px 0px 0px;
		vertical-align: middle;
	}

	span {
		display: inline-block;
		vertical-align: middle;
		font-family: Roboto;
		font-weight: bold;
		font-size: 29px;
		color: #ffffff;
		width: 146px;
		height: 34px;
	}
}

.plan-selector-back-indicator-button:focus {
	border: 3px solid #fff;
}

.plan-selector-title {
	font-size: 32px;
	font-weight: normal;
	text-align: left;
	letter-spacing: 0px;
	color: #ffffff;
	opacity: 1;
	margin-left: 12px;
	padding-left: 53px;
	font-family: "Roboto";
	margin-top: 20px;
}

.add-subscription-container {
	display: flex;
	margin-left: 64px;
	margin-right: 20px;
	overflow-x: scroll;
	scroll-snap-type: x mandatory;

	.add-subscription-block {
		display: block;
		height: 755px;
	}

	.add-subscription-card {
		margin-top: 17px;
		width: 248px;
		height: 590px;
		margin-right: 32px;
		position: relative;
		opacity: 0.8;

		.add-subcription-logo-container {
			width: 248px;
			height: 64px;
		}
		.add-subcription-logo{
		    height: 52px;
		    width: 200px;
		}
		.addsubscription-banner {
			width: 248px;
			height: 590px;
		}
		&:focus {
			width: 285px;
			height: 679px;
			margin-top: 14px;
			z-index: 1;
			scroll-snap-align: end;
			border: 3px solid #ffffff;
			border-radius: 3px;
			opacity: 1;

			.addsubscription-banner {
				width: 284px;
				height: 679px;
			}

			.add-subcription-logo-container {
				width: 285px;
				height: 73.6px;
			}

			.sub-pricing-info {
				height: 46px !important;
			}

			.sub-price-type,
			.sub-price-css {
				font: 29.9px Roboto Bold !important;
				font-weight: bold !important;
			}

			.sub-symbol,
			.sub-periodicity {
				font: 18.4px Roboto !important;
			}

			.sub-tax-label {
				height: 27.6px !important;
				font: 20.7px Roboto !important;
			}

			.sub-free-chargestr-container {
				margin-top: 18.4px !important;
				height: 82.8px !important;

				.sub-free-chargestr {
					font: 24.15px Roboto !important;
					color: #eeeeee !important;
					height: 60.6px !important;
					overflow: hidden;
				}
			}

			.sub-button-div {
				margin-top: 18.4px !important;
				margin-bottom: 25.06px !important;

			}

			.sub-button {
				height: 46px !important;
				width: 230px !important;
				font: 16.1px Roboto bold !important;
				font-weight: bold !important;

				.sub-button-span {
					display: flex;
					flex-direction: column;
				}
			}


			.sub-meta-data {
				height: 338.6px;
			}
		}

		.sub-meta-data {
			position: absolute;
			bottom: 5px;
			width: 100%;
			text-align: center;
			height: 294px;
			display: flex;
			flex-direction: column;

			.sub-pricing-info {
				color: #eeeeee;
				display: flex;
				justify-content: center;
				height: 40px;
				align-items: center;

				.sub-price-type,
				.sub-price-css {
					font: 26px Roboto Bold;
					font-weight: bold;
				}

				.sub-symbol,
				.sub-periodicity {
					font: 16px Roboto;
				}
			}

			.sub-tax-label {
				font: 18px Roboto;
				color: #eeeeee;
				text-transform: capitalize;
				height: 24px;
			}

			.sub-free-chargestr-container {
				margin-top: 16px;

				.sub-free-chargestr {
					font: 21px Roboto;
					color: #eeeeee;
					height: 72px;
					overflow: hidden;
				}
			}

			.sub-button-div {
				margin-top: 16px;
				margin-bottom: 22px;
				display: flex;
				justify-content: center;

				.sub-button {
					height: 40px;
					width: 200px;
					color: #ffffff;
					font: 14px Roboto bold;
					display: flex;
					justify-content: center;
					align-items: center;
					font-weight: bold;

					.sub-button-span {
						display: flex;
						flex-direction: column;
					}
				}
			}
		}
	}

	.sub-view-plan-button {
		width: 248px;
		height: 48px;
		background-color: #2e303d;
		color: #ffffff;
		display: flex;
		justify-content: space-around;
		vertical-align: middle;
		align-items: center;
		margin-top: 10px;
		border-radius: 6.6px;

		.subscription-blueShortcut {
			width: 20px;
			height: 20px;
		}

		.view-button-content {
			font-family: Roboto;
			font-size: 25px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 29.04px;
			height: 30px;
			width: 211px;

			.view-button-sub-div {
				display: flex;
				flex-direction: column;
			}
		}
	}

	.sub-view-plan-button:focus {
		margin-top: 10px;
		width: 242px;
		border: 3px solid #ffffff;
		// border-radius: 3px;
	}
}