import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import configure<PERSON><PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import RegisterPassword from "./Registerpassword";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

describe('RegisterPassword page test', () => {
    test('enter input', () => {
        const result = renderWithState(<RegisterPassword />)
        let input = result.container.querySelector('input[name="password"]')
        fireEvent.change(input, { target: { value: 'Preethi12345' } })
        fireEvent.keyUp(input,{keyCode: '10009'})
        renderWithState(<RegisterPassword />)
    })
    test('input should change after onChange', () => {
        const result = renderWithState(<RegisterPassword />)
        let input = result.container.querySelector('input[name="password"]')
        fireEvent.change(input, { target: { value: 'Preeti12' } })
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(result.container, 'reg-psd-btn')
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        renderWithState(<RegisterPassword />)
    })
})