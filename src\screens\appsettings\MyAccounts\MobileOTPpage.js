import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import BottomBar from "../BottomBar";
import '../../../styles/ManagePayments.css';
import { useDispatch } from "react-redux";
import { getPaymentsConfirm } from "../../../store/slices/settingsSlice";
import { useSelector } from "react-redux";

const MobileOTPpage = (props) => {

	const navigate = useNavigate();
	const dispatch = useDispatch();
	const region = localStorage.getItem('region')

	const [otp,setOtp] = useState('');
	const [showErrMsg, setShowErrMsg] = useState(false)
	const [errMsg, setErrMsg] = useState('');

	const userDetails = useSelector((state) => state?.login?.isLoggedIn?.response);
	const paymentDataForAPI = useSelector((state) => state?.settingsReducer?.paymentData)
	const AddPaymentDataResponse = useSelector((state) => state?.settingsReducer?.addPayments?.response)
	const otpResponse = useSelector((state) => state?.settingsReducer?.paymentsConfirm?.response)
	const apaMetaData = useSelector((state) => state?.initialReducer?.appMetaData)
	
	const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)

	const handleVerifyOtp = () => {
		dispatch(getPaymentsConfirm({'apiUrl':paymentDataForAPI?.buyLink,'buyToken':AddPaymentDataResponse?.buyToken,'userToken':userDetails?.user_token ,'accessPinToken':AddPaymentDataResponse?.response?.access_pin_token ,'otp':otp ,'number':AddPaymentDataResponse?.extra_params?.account}));
	};

	const handleOTP =(e) => {
		setOtp(e.target.value);
	}

	useEffect(() => {
		if(otpResponse?.status === 200){
			navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new')
		}
		else{
			setShowErrMsg(true);
			setErrMsg(otpResponse?.msg)
		}
	},[otpResponse])

	return(
		<div className="App-Settings">
			<div className="App-logo">
        <img src={'images/logo.png'} className="logo-img" alt="logo" />
      </div>
			<div>
				<p className="title">{translations?.language?.[region]?.hubgate_access_subtitle_label ? translations?.language?.[region]?.hubgate_access_subtitle_label: 'hubgate_access_subtitle_label'}</p>
				<p className="subTitle">{translations?.language?.[region]?.hubgate_validationCode_title ? translations?.language?.[region]?.hubgate_validationCode_title: 'hubgate_validationCode_title'}</p>
				<p className="subText">{translations?.language?.[region]?.hubgate_validationCode_subtitle ? translations?.language?.[region]?.hubgate_validationCode_subtitle: 'hubgate_validationCode_subtitle'}</p>
				<div>
					<input 
						className="inputBox focusable" 
						type="number"  
						id='input-otp'
						name='input-otp'
						placeholder= " Enter OTP"
						value={otp} 
						onChange={handleOTP} 
					/>
					<button className="resendOTPButton" id='resend-otp'><span className="resendOTPSpan">{translations?.language?.[region]?.hubgate_validationCode_sendPIN_button_sendPIN ? translations?.language?.[region]?.hubgate_validationCode_sendPIN_button_sendPIN: 'hubgate_validationCode_sendPIN_button_sendPIN'}</span></button>
					<div className="error-box">
						<div className={` ${showErrMsg ? 'invalid-otp' : "invisible"}`}>{errMsg}</div>
					</div>
				</div>
				<div className="bottom-button">
					<button className="nextButton focusable" id="next" onClick={ handleVerifyOtp }>
					<span>
						{translations?.language?.[region]?.setupPin_modal_option_button_next ? translations?.language?.[region]?.setupPin_modal_option_button_next : 'setupPin_modal_option_button_next'}
					</span>
				</button>
				</div>
			</div>
			<BottomBar image={"images/selectBack.png"} title={translations?.language?.[region]?.atv_back_notification ? translations?.language?.[region]?.atv_back_notification: 'atv_back_notification'}/>
		</div>
	)
};

export default MobileOTPpage;
