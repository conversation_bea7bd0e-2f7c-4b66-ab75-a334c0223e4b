import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import BottomBar from "../BottomBar";
import '../../../styles/ManagePayments.css';

const ViewPayments = (props) => {

	const navigate = useNavigate();

	const ClaroImg = "images/claroLogo.png";


	useEffect(() => {

	},[])

  return (
		<div className="App-Settings">
			<div className="App-logo">
        <img src={'images/logo.png'} className="logo-img" alt="logo" />
      </div>
			<span className="title">Claro Mobile Number</span>
				<div className="view-payments-div">
					<img src={ClaroImg}/>
					<span className="view-screen-text">Registered Mobile Number: ****7178</span>
				</div>
			<BottomBar image={"images/selectBack.png"} title={"Back"}/>
		</div>
	)
};

export default ViewPayments;