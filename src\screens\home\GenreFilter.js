import React, { useEffect, useState } from 'react'
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { useSelector } from 'react-redux';
import { store } from '../../store/sagaStore';
import { filterDadta, getclickFilterrail, getFilterCurrentFocus, subMenuRailCards, subMenuRailCardsdata } from '../../store/slices/subMenuDataSlice';
import './GenreRail.scss'
import { pushScreenViewEvent } from '../../GoogleAnalytics';
import { CURRENT_PLATFORM } from '../../utils/devicePlatform';

const GenreFilter = props => {
  const filtersubmenuUrl = []
  const filtersumurl = []
  const contentlistUrl = useSelector(state => state?.SubMenuFilter?.railListurl?.response?.modules?.module[0]?.components?.component)

  contentlistUrl?.map(each => {
    each.type === 'Listadoinfinito' && filtersubmenuUrl?.push({ urlData: each?.properties?.ordenamiento })
  })
  contentlistUrl?.map(each => {
    each.type === 'Listadoinfinito' && filtersumurl?.push({ urlData: each?.properties?.url })
  })
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const hidedata = useSelector(state => state?.SubMenuFilter?.filterclickdata)
  const submenuUrl = filtersubmenuUrl[0]?.urlData
  const filterUrl = filtersumurl[0]?.urlData

  const filterOption = (e, data, index) => {
    e.preventDefault()
    const [path, queryString] = filterUrl?.split('?');    
    const params = new URLSearchParams(queryString);
    let order_id = data?.replace(/[^0-9]/g, '')
    let order_way = data?.replace(/[^A-Z a-z]/g, '')
    params?.set('order_id', order_id);
    params?.set('order_way', order_way);
    const finalFilterUrl = `${path}?${params?.toString()}`;
    store.dispatch(getFilterCurrentFocus(true))
    store.dispatch(subMenuRailCardsdata(true))
    store.dispatch(filterDadta(true))
    store.dispatch(subMenuRailCards({ url: finalFilterUrl, HKS: userDetails?.session_stringvalue, user_id: userDetails?.user_id }))
    store.dispatch(getclickFilterrail(false))
    localStorage.setItem('setFilterOptionActive', index)
  }
  const handleBack = () => {
    store.dispatch(getFilterCurrentFocus(true))
    localStorage.setItem('backfilter', true)
    hidedata ? store.dispatch(getclickFilterrail(false)) : store.dispatch(subMenuRailCardsdata(false))
  }
  const keypresshandler = (event) => {
    const keycode = event.keyCode;
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow']);
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode);
    } else {
      handleLgkey(keycode);
    }
  };

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      localStorage.setItem('backfilter', true)
      store.dispatch(getFilterCurrentFocus(true))
      store.dispatch(getclickFilterrail(false))
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  (keycode == 120 ||  keycode === 8) ) ) {
      store.dispatch(getFilterCurrentFocus(true))
      localStorage.setItem('backfilter', true)
      store.dispatch(getclickFilterrail(false))
    }
  }

  useEffect(() => {
    SpatialNavigation.focus();
    document.getElementById('filteroption0')?.focus()
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(()=>{
  pushScreenViewEvent({screenName:'genre_filter_screen', screenData: userDetails, prevScreenName: 'home'})
},[])

  return (
    <div className="submenufiltercontainer">
      <div className="filter-regresser-box">
        <button
          className="filter-backScreen focusable"
          onClick={e => handleBack(e)}
          id="genrebackbutton"
        >
          <LazyLoadImage
            className="filter-back-img-icon"
            src={'images/Vcard_Icons/yellowcircle_small.png'}
            placeholderSrc={'images/Vcard_Icons/yellowcircle_small.png'}
          />
          <LazyLoadImage
            className="filter-back-img-icon"
            src={'images/Vcard_Icons/icon_backpage.png'}
            placeholderSrc={'images/Vcard_Icons/icon_backpage.png'}
          />
          <span className="filter-back-button-regresar-title">REGRESAR</span>
        </button>
      </div>
      {submenuUrl?.map((text, index) => {
        return (
          <button
            className={`SubfilterTab ${localStorage.getItem('setFilterOptionActive') == index ? 'SubfilterTab-active' : ''
              } focusable`}
            id={`filteroption${index}`}
            onClick={e => filterOption(e, text?.order, index)}
            key={`filter_${index}`}
          >
            {text?.label.replace(/&aacute;/g, 'á')}
          </button>
        )
      })}
    </div>
  )
}

export default GenreFilter