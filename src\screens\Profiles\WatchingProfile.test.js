// Add this before your imports
jest.mock('npaw-plugin-es5', () => {
    return {
      __esModule: true,
      default: jest.fn().mockImplementation(() => ({
        initialize: jest.fn(),
        // Add any other methods from NpawPlugin that your code uses
      }))
    };
  });

import React from "react";
import { fireEvent, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import '@testing-library/jest-dom';
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import WatchingProfile from "./WatchingProfile";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const mockresponse = {
    "response": {
        "data": {
            "id": "65801a8272ffc675594c00c7",
            "title": "Profile6579a9e7145f7f7fe00d7249",
            "description": "Profile Group Test",
            "totalMembers": 5,
            "creationDate": "2023-12-18 10:10:10",
            "maxMembers": 5,
            "type": "profiles",
            "members": [
                {
                    "gamification_id": "6579a9e0d7249",
                    "username": "Ls Fake",
                    "user_image": "http://clarovideocdn6.clarovideo.net/herederosaccidente02.png",
                    "rol": "admin",
                    "admin": true,
                    "change_name": false,
                    "is_kids": "false",
                    "partnerUserId": "92821840",
                    "user_hash": "OTI4MjE4NTQ5N3wyOTg=="
                }   
            ]
        }
    },
    "status": "0",
    "msg": "OK"
}
describe('Add Profile page test', () => {
    global.SpatialNavigation = {
        focus: jest.fn(),
        init: jest.fn(),
        add: jest.fn(),
        makeFocusable: jest.fn()
      };
    test('should render api mock data', () => {
        renderWithState(<WatchingProfile />)
    })

   test('lg tv keycode', ()=> {
    const { container } = renderWithState(<WatchingProfile />)
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'EditWatchProfBtn_id');
    fireEvent.keyUp(scroll,{keyCode: '1009'})
    fireEvent.keyUp(scroll,{keyCode: '461'})
    fireEvent(
        scroll,
        new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        })
    )
   })

   test('lg tv keycode', ()=> {
    initialState.profile = {
        profileData: mockresponse,
    }
    const { container } = renderWithState(<WatchingProfile />)
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'image0');
   
    fireEvent(
        scroll,
        new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        })
    )
   })

})