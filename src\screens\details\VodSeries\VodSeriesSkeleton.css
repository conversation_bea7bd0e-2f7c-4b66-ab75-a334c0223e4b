.Vod-episode-shimmer{
    display: flex;
    gap: 10px;
    padding: 10px;
    background-color: #000000;
    
  }

  .Vod-season-shimmer{
    display: flex;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    background-color: transparent;
    overflow: scroll hidden;
  }
  
  .vod-episode-card {
    border-radius: 5px;
    padding: 10px;
    background-color: #242424;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: loadingAnimation 1s infinite;
    display: flex;
    justify-content: center;
    grid-gap: 10px;
    align-items: center;
    height: 35px;
  }

  .vod-cast-card {
    border-radius: 5px;
    padding: 10px;
    margin-right: 20px;
    background-color: #242424;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: loadingAnimation 1s infinite;
    display: flex;
    justify-content: center;
    grid-gap: 10px;
    align-items: center;
    height: 35px;
  }

  .vod-mlt-card {
    border-radius: 5px;
    padding: 10px;
    background-color: #242424;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    animation: loadingAnimation 1s infinite;
    display: flex;
    justify-content: center;
    grid-gap: 10px;
    align-items: center;
    height: 35px;
  }
  
  .Vod-season-button {
    height: 56px;
    min-width: 147px;
    background-color: #2F2F2F;
    border-radius: 2px;
    border-radius: 35px;
  }
  
  .vod-cell-content {
    height: 10px;
    width: 40%;
    height: 50%;
    background-color: #2F2F2F;
    border-radius: 2px;
  } */
  
  .skeleton {
    animation: loadingAnimation 1s infinite;
  }
  
  @keyframes loadingAnimation {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }
  
  .description-page-shimmer {
    /* padding: 20px; */
    /* background-color: #242424; */
    background: url("../../../../tv/images/hero_banner.png"), linear-gradient(85deg, rgb(0 0 0 / 78%) 0%, rgb(85 71 71) 50%, rgb(165 94 94 / 0%) 100%);
    height: 100vh;  
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
   
  }
  .description-page-header{
    height: 20px;
    width: 50%;
    height: 15%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
  }
  .description-page-subtitle{
    width: 50%;
    height: 10%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
    margin: 10px 0px 10px 25px;
  }
  .description-page-duration{
    width: 25%;
    height: 5%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
  }
  .page-description{
    width: 50%;
    height: 30%;
    background-color: #2F2F2F;
    border-radius: 2px;
    animation: loadingAnimation 1s infinite;
    margin:10px 0px 10px 25px;
  }
 
  @media (min-width: 1920px) and (max-width: 2560px) {
    .Vod-episode-shimmer{
      width: 1920px;
      overflow: scroll hidden;
    }
    .Vod-season-shimmer {
      width: 1920px;

    }
    .description-page-shimmer{
      height: 840px;
      width: 1920px;
      padding: 50px 0px 0px 50px;
    }
    .vod-episode-card{
      min-width:500px;
      height: 440px;
    }
    .vod-cast-card{
        min-width: 250px;
        height: 280px;
      }
      .vod-mlt-card{
        min-width:500px;
        height: 320px;
      }
    .description-page-header{
      width: 50%;
      height: 15%;
    
    }
    .description-page-subtitle{
      width: 50%;
      height: 10%;
      margin: 20px 0px 20px 25px;
    }
    .description-page-duration{
      max-width: 25% !important;
      height: 5%;
    }
    .page-description{
      max-width: 45% !important;
      height: 30%;
      margin: 20px 0px 20px 25px;
    }
  
  }