import React, { useEffect, useState, useRef } from 'react'
import './Language.scss'
import moment from 'moment'
import { useSelector } from 'react-redux'
import { CURRENT_PLATFORM } from '../utils/devicePlatform'

const Language = ({ data, id, hideLanguage, player, subtitles }) => {
  const languageRef = useRef([])
  const contentData = data?.group?.common
  const seasonEpisode = contentData?.extendedcommon?.media
  const region = localStorage.getItem('region')
  const sortingOrder = 'ODS'
  let languageData =
    [...data?.language?.options].sort(
      (a, b) =>
        sortingOrder.indexOf(a.option_id.slice(0, 1)) -
        sortingOrder.indexOf(b.option_id.slice(0, 1))
    ) ?? []
  const [selectedLangIdx, setSelectedLangIdx] = useState(
    languageData.findIndex(
      data => data.option_id == localStorage.getItem('vodContentLanguage')
    )
  )
  const [lastClickTime, setLastClickTime] = useState(0);
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  useEffect(() => {
    if (selectedLangIdx >= 0) {
      languageRef.current?.[selectedLangIdx]?.focus()
    } else {
      localStorage.setItem('vodContentLanguage', 'O-OR')
      setSelectedLangIdx(0)
      languageRef.current?.[0]?.focus()
    }
  }, [languageRef, selectedLangIdx])

const selectedLanguage = (item, index) => {
  const now = Date.now(); 
  // Prevent rapid successive clicks AND clicking the already selected language
  if (now - lastClickTime < 500 || selectedLangIdx === index) return; 
  // Update the last click time
  setLastClickTime(now);
  // Store the current playback time before changing language
  const currentTime = player?.getCurrentTime ? player.getCurrentTime() : player?.currentTime; 
  const selectedOptType = item?.option_id.includes('S-');
  const convertedSelAud =
    !selectedOptType && item?.option_id == 'O-EN'
      ? 'or'
      : item?.option_id.replace('D-', '');
  subtitles(player, item, index, convertedSelAud);
  setSelectedLangIdx(index); 
  // Restore the playback position
  setTimeout(() => {
    if (player?.seek) {
      player.seek(currentTime);
    } else if (player?.currentTime !== undefined) {
      player.currentTime = currentTime;
    }
  }, 300);
}

  return (
    <div className="language-container" id={id}>
      <div className="language-left-container">
        <button
          className="back-btn focusable"
          id={'back-btn'}
          onClick={() => hideLanguage(false)}
          data-sn-right={'#language-btn'}
          data-sn-down={'#language-btn'}
        >
          <img className="yellow-dot" src="images/Home_icons/yellowdot.png" />
{((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') ?
<img className="back-icon" src="images/Vod_Icons/regresar.png" /> :
          <img className="yellow-dot" src="images/Vod_Icons/regresar.png" />)}
          <span className="back-text">
            {apilanguage?.BotonShortcut_TextoTitulo_Regresar ??
              'BotonShortcut_TextoTitulo_Regresar'.slice(0, 8)}
          </span>
        </button>
        <div className="details-container">
          <h6 className="content-title">
            {seasonEpisode?.serie?.title
              ? seasonEpisode?.serie?.title.length > 26
                ? `${seasonEpisode?.serie?.title.slice(0, 26)}...`
                : seasonEpisode?.serie?.title
              : contentData?.title && contentData?.title?.length > 67 //utilizing the logic present in vodPlayer.js for the slice operation
              ? `${contentData?.title.slice(0, 67)}...`
              : contentData?.title}
          </h6>
          {seasonEpisode?.episode?.number && (
            <h4 className="episode-details">
              {apilanguage?.playing_playerControls_abbreviationSeason_label ??
                'playing_playerControls_abbreviationSeason_label'.slice(
                  0,
                  5
                )}{' '}
              {seasonEpisode?.episode?.season} |{' '}
              {apilanguage?.playing_playerControls_abbreviationEpisode_label ??
                'playing_playerControls_abbreviationEpisode_label'.slice(
                  0,
                  3
                )}{' '}
              {seasonEpisode?.episode?.number}:
              {contentData?.title && contentData?.title.length > 20
                ? `${contentData?.title.slice(0, 27)}...`
                : contentData?.title}{' '}
            </h4>
          )}
          <h4 className="episode-details">
            {moment(contentData?.duration, 'HH:mm:ss').format(
              contentData?.duration.startsWith('00')
                ? `mm [min]`
                : `H [h] mm [min]`
            )}
          </h4>
          <p className="episode-description">
            {contentData?.description && contentData?.description.length > 268
              ? `${contentData?.description.slice(0, 268)}...`
              : contentData?.description}
          </p>
        </div>
      </div>

      <div className="language-right-container">
        <div className="language-sub-container">
          <h4 className="language-label">
            {apilanguage?.panelOptions_sidemenu_title_selectLanguage_label ??
              'panelOptions_sidemenu_title_selectLanguage_label'.slice(0, 6)}
          </h4>
          {languageData.length > 0 &&
            languageData.map((item, index) => (
              <button
                data-sn-left={'#back-btn'}
                ref={ref => (languageRef.current[index] = ref)}
                key={index}
                className="language-btn focusable"
                id={'language-btn'}
                onClick={() => {
                  selectedLanguage(item, index)
                }}
              >
                <img
                  className="language-icon"
                  src={
                    item?.option_id.toLowerCase().startsWith('s')
                      ? 'images/Vod_Icons/language_subs.png'
                      : 'images/Vod_Icons/language_audio.png'
                  }
                />
                <p className="language-title">
                  {item?.label_large && item?.label_large.length > 24
                    ? `${item?.label_large.slice(0, 24)}...`
                    : item?.label_large}
                </p>
                {selectedLangIdx === index && (
                  <img
                    className="language-sel-icon"
                    src="images/Vod_Icons/language_selected.png"
                  />
                )}
              </button>
            ))}
        </div>
      </div>
    </div>
  )
}

export default Language
