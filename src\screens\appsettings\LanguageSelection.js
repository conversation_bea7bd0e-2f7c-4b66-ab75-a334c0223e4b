import React, { useState, useEffect, useRef } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useSelector } from 'react-redux'
import './LanguageSelection.scss'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const LanguageSelection = () => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const type = state?.type
  const currentLanguage = state?.currentLanguage
  const languageRefs = useRef([])
  const [selectOptionActive, setSelectOptionActive] = useState()

  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response) 

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }

  const languages = [
    {
      name: truncateText('subtitled_in_spanish', 30),
      code: 'S-ES'
    },
    {
      name: truncateText('subtitled_in_portuguese', 30),
      code: 'S-PT'
    },
    {
      name: truncateText('original_language', 30),
      code: 'O-OR'
    },
    {
      name: truncateText('portuguese_language', 30),
      code: 'D-PT'
    },
    {
      name: truncateText('spanish_language', 30),
      code: 'D-ES'
    }
  ]

  const handleLanguageSelect = (language, index) => {
    localStorage.setItem(
      type === 'onDemand' ? 'vodContentLanguage' : 'liveContentLanguage',
      language.code
    )

    setSelectOptionActive(language.code)
    navigate('/settings/profile-settings', {
      state: { selectedLanguage: language.name, type, backFocus: state?.backFocus },
      replace: true
    })
  }

  const keySeriesPressFunc = event => {
    if (event.keyCode === 10009 || event.keyCode === 461 || event?.keyCode === 8) {
      navigate('/settings/profile-settings', { state: { defaultFocus: true, backFocus: state?.backFocus } })
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keySeriesPressFunc)

    return () => {
      document.removeEventListener('keyup', keySeriesPressFunc)
    }
  }, [keySeriesPressFunc])

  useEffect(() => {
    const savedLanguage = localStorage.getItem(
      type === 'onDemand' ? 'vodContentLanguage' : 'liveContentLanguage'
    )
    setSelectOptionActive(savedLanguage ?? '')

    // If it's the first time (no language selected)
    const isFirstTime = !savedLanguage;
    if (isFirstTime) {
      languageRefs.current[0]?.focus();
    } else {
      // Here I am finding the index of the current language
      const currentIndex = languages.findIndex(lang => lang.name === currentLanguage)
      if (currentIndex !== -1 && languageRefs.current[currentIndex]) {
        languageRefs.current?.[currentIndex]?.focus()
      }
    }
  }, [type, currentLanguage])

  useEffect(()=>{
  pushScreenViewEvent({screenName:'language_selection',screenData:userDetails,prevScreenName:'idioma'})
  },[])
  
  return (
    <div className="lang-selection-container">
      {languages.map((language, index) => (
        <button
          key={index}
          ref={el => languageRefs.current[index] = el}
          className={`lang-title-container ${selectOptionActive == language?.code
            ? 'lang-title-container-active'
            : ''
            } focusable`}
          onClick={() => handleLanguageSelect(language, index)}
        > 
          <span className="lang-title">{language.name}</span>
          {selectOptionActive === language.code && (
            <img className="check-icon" src={'images/CheckIcon.png'} alt="Selected" />
          )}
        </button>
      ))}
    </div>
  )
}

export default LanguageSelection
