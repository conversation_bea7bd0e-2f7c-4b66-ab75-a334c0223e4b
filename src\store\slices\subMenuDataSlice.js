import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  genre: {},
  railListurl: {},
  raildata: {},
  loading: false,
  error: {},
  submenudata: false,
  index: 0,
  totalrailcard: 0,
  filtercards: false,
  filterdata: {},
  filterclickdata: false,
  cuerrntindex: '',
  tabvalue: '',
  submenu: false,
  filterbackfocus: false
}

export const subMenuDataSlice = createSlice({
  name: 'SubMenuFilter',
  initialState,
  reducers: {
    getSubMenu: (state, { payload }) => {},
    getSubMenuSuccess: (state, { payload }) => {
      state.railListurl = payload
    },
    getSubMenuFailure: (state, { payload }) => {
      state.error = payload
    },
    subMenuRailCardsFailure: (state, { payload }) => {
      state.error = payload
    },
    subMenuNextRailCards: (state, { payload }) => {},
    subMenuRailCardsdata: (state, { payload }) => {
      state.submenudata = payload
    },
    subMenuRailIndex: (state, { payload }) => {
      state.index = payload
    },
    subMenuRailCards: (state, { payload }) => {},
    railsRes: (state, { payload }) => {
      state.totalrailcard = payload
      let data
      if (payload?.railDat?.rails?.groups?.length > 0) {
        data = payload?.railDat?.rails?.groups
      } else if (payload?.newRailDat?.rails?.groups?.length > 0) {
        const existingRails = JSON.parse(JSON.stringify(state.raildata))
        data = [...existingRails?.assets, ...payload.newRailDat.rails?.groups]
      }
      const uniqueItem = { assets: data }
      state.raildata = uniqueItem
    },
    subMenuRailCardsFailure: (state, { payload }) => {
      state.error = payload
    },
    filterDadta: (state, { payload }) => {
      state.filtercards = payload
    },
    getNewRail: (state, { payload }) => {},
    getFilterrail: (state, { payload }) => {
      state.filterdata = payload
    },
    getclickFilterrail: (state, { payload }) => {
      state.filterclickdata = payload
    },
    getcurrentindex: (state, { payload }) => {
      state.cuerrntindex = payload
    },
    getTabvalue: (state, { payload }) => {
      state.tabvalue = payload
    },
    getsubmenuval: (state, { payload }) => {
      state.submenu = payload
    },
    getFilterCurrentFocus: (state, { payload }) => {
      state.filterbackfocus = payload
    },
    clearSubmenuCard: (state, { payload }) => {
      state.railListurl = {}
      state.raildata={}
    }
  }
})

export const {
  getTabvalue,
  getsubmenuval,
  getcurrentindex,
  getSubMenu,
  getclickFilterrail,
  getFilterrail,
  getNewRail,
  filterDadta,
  railsRes,
  subMenuRailCards,
  subMenuNextRailCards,
  getSubMenuSuccess,
  subMenuRailCardsSuccess,
  getSubMenuFailure,
  subMenuRailCardsFailure,
  subMenuRailCardsdata,
  subMenuRailIndex,
  getFilterCurrentFocus,
  clearSubmenuCard
} = subMenuDataSlice.actions
export default subMenuDataSlice.reducer
