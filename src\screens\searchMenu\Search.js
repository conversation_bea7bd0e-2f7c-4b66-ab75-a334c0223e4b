import { LazyLoadImage } from 'react-lazy-load-image-component'
import {
  getSearchData,
  getPredictiveSearchData,
  getMulticontentSearchData,
  getProgressbarBookmark,
  getTalentSearchData,
  setReturnFocusById
} from '../../store/slices/SearchSlice'
import moment from 'moment/moment'
import Navbar from '../home/<USER>'

//nav
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import AlphaNumeric from '../Keyboard/AlphaNumeric'
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom'
import './../../styles/search.scss'
import '../VodMovies/VodMovies.scss'
import ProgressBar from '../Progressbar/Progressbar'
import {
  getControlPin,
  getStatusControlPin,
  getSubscriptionInfo
} from '../../store/slices/settingsSlice'
import { getMediaAPI, getRecordPlayer } from '../../store/slices/PlayerSlice'

import {
  getLockedChannelsList,
  getClearAllSettingsState
} from '../../store/slices/settingsSlice'

import {
  getLiveReminder,
  getPayWayToken,
  getProgramDetailsData
} from '../../store/slices/EpgSlice'
import { vodSeries } from '../../store/slices/vodSeriesSlice'
import { getChannelData } from '../../store/slices/PlayerSlice'
import TalentSearch from '../talentSearch/TalentSearch'
import PlayerModelSelection from '../VodMovies/PlayerModelSelection'
import { setShowTalentModule } from '../../store/slices/SearchSlice'
import { getLiveTvRecording } from '../../store/slices/EpgSlice'
import PlayerErrorHandler from '../bitmovinPlayer/PlayerErrorHandler'
import { pushSearchEvent } from '../../GoogleAnalytics'
import Lottie from 'react-lottie-player'
import animationData from '../../../src/json/animationData.json'
import { getVodMoviesCastData } from '../../store/slices/VodMoviesSlice'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const Search = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const numRef = useRef(null)
  const abcRef = useRef(null)
  const borrarRef = useRef(null)
  const vaciarRef = useRef(null)
  const voiceRef = useRef(null)
  const { state } = useLocation()
  const [searchValueParams, setSearchvalueParams] = useSearchParams()
  const [searchValue, setSearchValue] = useState(
    searchValueParams.get('q') || state?.inputValue || ''
  )
  const [currentButtonFocus, setCurrentButtonFocus] = useState('')
  const [showApiFailureModal, setShowApiFailureModal] = useState(false)
  const [eventClicked, setEventClicked] = useState(false)
  const [warningPlay, setWarningPlay] = useState(false)
  const [filterlist, setFilterlist] = useState('')
  const [castData, setCastData] = useState([])
  const [moviesData, setMoviesData] = useState({})
  const [seriesData, setSeriesData] = useState('')
  const [paywaychannelChannels, setPaywayChannels] = useState([])
  const [videoplayerWarning, setVideoPlayerWarning] = useState(false)
  const [progress, setPercent] = useState(0)
  const [contentData, setContentData] = useState('')
  const [talentSearchfield, setTalentSearchfield] = useState('')
  const [playBackChoice, setPlaybackChoice] = useState('')
  const [selectedPlayBack, setSelectedPlayBack] = useState()
  const [vodSimultaneous, setVodSimultaneous] = useState({})
  
  const [talentSearchfieldEnable, setTalentSearchfieldEnable] = useState(false)

  const [playbackRetry, setPlaybackRetry] = useState(false)
  const [streamType, setStreamType] = useState('')
  const [returnFocus, setFocusIndex] = useState(state?.returnFocus ?? '')
  const [channelInfoData, setChannelInfoData] = useState({})
  const [buttonClicked, setButtonClicked] = useState(false)
  const hasError = document.querySelectorAll('.error-layout')

  const blinkerText = useRef()
  let data = { searchStr: searchValue }
  const userId = localStorage.getItem('userId')
  const lastTouch = localStorage.getItem('lasttouch')

  let apaMetaData = useSelector(
    state => state?.initialReducer?.appMetaData
  )
  let filteredRegionLists = apaMetaData?.byr_filterlist_configuration ? JSON.parse(apaMetaData?.byr_filterlist_configuration) : ''

  const recomendation = useSelector(state => state?.search?.data) || []
  const predictiveSearch = useSelector(
    state => state?.search?.predictiveData?.contenidos
  )
  const multiContentSearch = useSelector(
    state => state?.search?.multiContentSearch
  )
  const progressbarContentData = useSelector(
    state => state?.search?.progressbarContent
  )
  const showTalentModule = useSelector(state => state?.search?.showTalentModule)

  const castTray = useSelector(
    state =>
      state?.search?.predictiveData?.talentos?.external_talents?.gracenote
  )
  const castdetails = useSelector(
    state => state?.search?.predictiveData?.talentos?.talents?.talent
  )
  let talentSearchData = useSelector(state => state?.search?.talentSearchData) 
  const securityPinCheck = useSelector(
    state =>
      state?.settingsReducer?.controlPin?.response?.profiles?.[0]?.parental
        ?.active
  )
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const vodDetails = useSelector(state => state?.login?.vcardDetails)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)

  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
  const epgVersion = useSelector(state => state?.epg?.epgVersion)
  const userDeatilResp = useSelector(
    state => state?.login?.isLoggedIn?.response
  )
  const userDetails = useSelector(
    state => state?.login?.isLoggedIn?.response?.session_userhash
  )
  const userProfileflag = useSelector(
    state => state?.login?.isLoggedIn?.response?.is_kids
  )
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)

  const apaTranslationData = useSelector(
    state => state?.initialReducer?.appMetaData
  )
  const translations =
    apaTranslationData?.translations &&
    JSON?.parse(apaTranslationData?.translations)
  const region = localStorage.getItem('region')
  const apilanguage = translations?.language?.[region]

  const recordingsepisode = useSelector(
    state => state?.epg?.RecordingList?.response?.recordings
  )
  const recordingList = useSelector(
    state => state?.epg?.RecordingList?.response
  )

  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups
  )

  const unlockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelDelete
  )

  const lockchannel = useSelector(
    state => state?.settingsReducer?.lockedChannelAdd
  )
  const ReminderList = useSelector(state => state?.epg?.ReminderLive?.response) 
  const vodSeriesDataRedux = useSelector(
    state => state?.getVodSeries?.seriesData
  )
  const payWayToken = useSelector(
    state => state?.epg?.paywayToken?.response?.paqs?.paq
  )
  const getMediaRes = useSelector(state => state?.player?.getMediaRes?.response) 
  const addSubscriptions = useSelector(
    state => state?.settingsReducer?.getSubsInfo
  )
  const getMediaError = useSelector(
    state => state?.player?.getMediaError?.errors?.[0]
  )
  const showReturnFocus = useSelector(state => state?.search?.setReturnFocus)
  const isLoadingPreSearch = useSelector(state => state?.search?.isPredictive)
  const isRecomendationLoading = useSelector(
    state => state?.search?.isRecomendationLoading
  )
  const vodMoviesCast = useSelector(state => state?.vodMovies?.castData)
  const age_rating = vodMoviesCast?.common?.extendedcommon?.media?.rating
  const statusControlPin = useSelector(
    state =>
      state?.settingsReducer?.statusControlPin?.response?.pin_parental?.info
        ?.value
  )
  const providersLabelConfiguration =
    apaMetaData?.providers_label_configuration &&
    JSON?.parse(apaMetaData?.providers_label_configuration)
    const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
  const payload = {
        search_term: searchValue,
        user_id : userDeatilResp?.user_id,
        parent_id : userDeatilResp?.parent_id,
        suscriptions : userDeatilResp?.subscriptions && Object.keys( userDeatilResp?.subscriptions)?.filter(key =>  userDeatilResp?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : loginInfo || registerInfo? 'registrado':'anonimo'
      }
  const supportedStream =
    apaMetaData?.supported_stream && JSON.parse(apaMetaData?.supported_stream)
  const getPriority = contentType => {
    switch (contentType) {
      case 'P':
        return 1
      case 'S':
        return 2
      case 'L':
        return 3
      case 'E':
        return 4
      default:
        return 5
    }
  }

  const sortedRecommendationArray = [...recomendation]?.sort((a, b) => {
    return getPriority(a?.content_type) - getPriority(b?.content_type)
  })

  useEffect(() => {
    dispatch(getSearchData({ userhash: userDetails, is_kids: userProfileflag }))
    setSearchValue(state?.inputValue ?? searchValue)
    setStreamType(
     !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
        ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
        : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
    )
    blinkerText.current = setInterval(() => {
      if (document.querySelector('.search-input.input-text')) {
        let p = document.querySelector('.search-input.input-text').value
        if (p?.length) {
          if (p?.substr(-1, 1) == '|') {
            let removePipe = p?.replace('|', '')
            document.querySelector('.search-input.input-text').value =
              removePipe
          } else {
            document.querySelector('.search-input.input-text').value = p + '|'
          }
        }
      }
    }, 1000)

    return () => {
      clearInterval(blinkerText.current)
      setButtonClicked(false)
      setTalentSearchfieldEnable(false)
    }
  }, [])

  useEffect(() => {
    setFilterlist(
      filteredRegionLists?.[`${region}`]?.filterlist ??
        filteredRegionLists?.default?.filterlist
    )
  }, [filteredRegionLists])

  useEffect(() => {
    dispatch(
      getLiveReminder({
        user_hash: userDeatilResp?.session_userhash,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue
      })
    )
    dispatch(
      getLockedChannelsList({
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        user_hash: userDeatilResp?.session_userhash
      })
    )
    dispatch(
      getPayWayToken({
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        user_id: userDeatilResp?.parent_id
      })
    )
    dispatch(
      getLiveTvRecording({
        user_token: userDeatilResp?.user_token
      })
    )
  }, [predictiveSearch, recomendation])

  useEffect(() => {
    if (addSubscriptions) {
      setContentData(addSubscriptions)
      if (watchFree) {
        dispatch(
          getMediaAPI({
            id: moviesData?.id,
            payway_token: addSubscriptions?.response?.playButton?.payway_token,
            HKS: vodDetails?.confirmscreen
              ? loginInfo?.session_stringvalue ??
                registerInfo?.session_stringvalue
              : userDeatilResp?.session_stringvalue,
            userId: userDeatilResp?.user_id,
            type: 'watchfree',
            streamType
          })
        )
      } else {
        dispatch(
          getMediaAPI({
            id: seriesData?.id ?? moviesData?.id,
            payway_token: addSubscriptions?.response?.playButton?.payway_token,
            HKS: vodDetails?.confirmscreen
              ? loginInfo?.session_stringvalue ??
                registerInfo?.session_stringvalue
              : userDeatilResp?.session_stringvalue,
            userId: userDeatilResp?.user_id,
            user_token: userDeatilResp?.user_token,
            streamType
          })
        )
      }
    }
  }, [addSubscriptions])
  // Fixed useEffect to handle navigation when API response is ready
  useEffect(() => {
    if(getMediaError?.code) {
      const code = getMediaError?.code
      if (
        code == 'PLY_DEV_00006' ||
        code == 'PLY_CSS_00001' ||
        code == 'PLY_CSS_00004'
      ) {
        const data = {
          errFromPlayer: false,
          errorMessage1:
            code == 'PLY_DEV_00006'
              ? (apilanguage?.PLY_DEV_00006_title ?? 'PLY_DEV_00006_title').slice(0, 19)
              : (apilanguage?.PLY_CSS_00001_title ?? 'PLY_CSS_00001_title').slice(0, 19),
          errorMessage2:
            code == 'PLY_DEV_00006'
              ? (apilanguage?.PLY_DEV_00010 ?? 'PLY_DEV_00010').slice(0, 19)
              : (apilanguage?.PLY_CSS_00001 ?? 'PLY_CSS_00001').slice(0, 19),
          errorType: code == 'PLY_DEV_00006' ? 'device' : 'playback'
        }
        setVodSimultaneous(data)
        return
      }
    }
    else if (eventClicked && getMediaRes && moviesData) {
      // Reset the flag first to prevent multiple calls
      setEventClicked(false)


      // Handle security pin checkS
      if (
        (securityPinCheck &&
          statusControlPin == 50 &&
          parseInt(age_rating?.code) > 18) ||
        (statusControlPin == 40 && parseInt(age_rating?.code) > 16) ||
        (statusControlPin == 30 && parseInt(age_rating?.code) >= 13) ||
        (statusControlPin == 20 && parseInt(age_rating?.code) > 7)
      ) {
        navigate('/my-settings/help-And-Settings/security-pin/configure', {
          state: {
            data: 'Create',
            item: moviesData,
            pageName: moviesData?.is_series ? 'series' : 'movies',
            contentDataplayer: vodSeriesDataRedux,
            episodeData: moviesData?.is_series ? seriesData?.seasons[0]?.episodes : '',
            moviedata: moviesData?.is_series ? '' : moviesData,
            getMediaData: getMediaRes,
            resume: playBackChoice == 'begin' ? false : true,
            returnPage: 'search',
            inputValue: searchValue
          }
        })
      } else if (!watchFree) {
        if (!videoplayerWarning && moviesData?.vistime?.last?.hasOwnProperty('progress')) {
          dispatch(setShowTalentModule(false))
          setShowApiFailureModal(false)
          setVideoPlayerWarning(true)
          setCurrentButtonFocus('resume')
          setPlaybackChoice('resume') 
        } else {
          // Navigate directly to VodPlayer
          moviesData?.is_series
            ? navigate('/vodPlayer', {
                state: {
                  moviesData,
                  contentDataplayer: vodSeriesDataRedux,
                  episodeData: seriesData?.seasons[0]?.episodes,
                  showControls: 'vod',
                  getMedia: getMediaRes,
                  resume: playBackChoice == 'begin' ? false : true,
                  returnPage: 'search',
                  inputValue: searchValue
                }
              })
            : navigate('/vodPlayer', {
                state: {
                  data: moviesData,
                  episodeData: '',
                  showControls: 'vod',
                  getMedia: getMediaRes,
                  resume: playBackChoice == 'begin' ? false : true,
                  returnPage: 'search',
                  inputValue: searchValue
                }
              })
        }
      } else if (
        watchFree &&
        moviesData?.format_types !== 'free' &&
        moviesData?.format_types !== 'free,download'
      ) {
        navigate('/EPconfirmation', {
          state: {
            returnPage: 'search',
            page: 'search',
            inputValue: searchValue,
            moviesData: moviesData
          }
        })
      }
    } else {
      if(warningPlay && videoplayerWarning && moviesData?.vistime?.last?.hasOwnProperty('progress'))
        // Navigate directly to VodPlayer
        moviesData?.is_series
          ? navigate('/vodPlayer', {
              state: {
                moviesData,
                contentDataplayer: vodSeriesDataRedux,
                episodeData: seriesData?.seasons[0]?.episodes,
                showControls: 'vod',
                getMedia: getMediaRes,
                resume: playBackChoice == 'begin' ? false : true,
                returnPage: 'search',
                inputValue: searchValue
              }
            })
          : navigate('/vodPlayer', {
              state: {
                data: moviesData,
                episodeData: '',
                showControls: 'vod',
                getMedia: getMediaRes,
                resume: playBackChoice == 'begin' ? false : true,
                returnPage: 'search',
                inputValue: searchValue
              }
            })
      }
  }, [eventClicked, getMediaRes, moviesData, getMediaError])

  useEffect(() => {
    setSeriesData(vodSeriesDataRedux)
  }, [vodSeriesDataRedux])

  useEffect(() => {
    state?.inputValue != searchValue &&
      dispatch(
        getPredictiveSearchData({
          epg_version: epgVersion,
          value: searchValue,
          filterlist: filterlist,
          is_kids: userProfileflag
        })
      )
    if (searchValue) {
      localStorage.setItem('searchValue', searchValue)
    }
  }, [searchValue])

  useEffect(() => {
    if (contentData?.playButton?.visible == 1) {
      setContentData(contentData)
      const totalDuration = contentData?.playButton?.media?.duration?.seconds
      const playedDuration =
        contentData?.playButton?.media?.initial_playback_in_seconds
      const percentage = ((playedDuration / totalDuration) * 100).toFixed(2)
      setPercent(percentage)
    }
  }, [contentData])

  useEffect(() => {
    return clearInput()
  }, [])

  useEffect(() => {
    const element = document.getElementById(showReturnFocus)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
        block: 'center'
      })
      element.focus()
      setCurrentButtonFocus(showReturnFocus)
    }
    showReturnFocus && setCurrentButtonFocus(showReturnFocus)
  }, [showTalentModule, showReturnFocus, videoplayerWarning])

  useEffect(() => {
    let castdatas = []
    castTray?.map(item => {
      castdatas.push({
        ...item,
        castName: item?.first_name
          ? item?.first_name + ' ' + item?.last_name
          : item?.name
      })
    })
    castdetails?.map(item => {
      castdatas.push({ ...item, castName: item?.name })
    })
    setCastData(castdatas)
  }, [castTray])

  const uniqueCastData = []
  const seen = {}

  castData?.forEach(item => {
    if (!seen[item.id]) {
      seen[item.id] = true
      uniqueCastData.push(item)
    }
  })

  useEffect(() => {
    let group_id = {}
    let searchvalueId = []
    predictiveSearch?.length > 0 &&
      predictiveSearch?.map(item => {
        if (item?.content_type == 'S' || item?.content_type == 'P') {
          group_id[item?.id] = { format_types: item.format_types }
        }
        searchvalueId?.push(item.id)
      })

    if (
      state?.inputValue != searchValue &&
      (predictiveSearch?.length != 0 || group_id != {})
    ) {
      dispatch(
        getMulticontentSearchData({
          group_ids: { group_ids: group_id },
          user_token: userDeatilResp?.user_token
        })
      )
    }
    dispatch(
      getProgressbarBookmark({
        group_id: searchvalueId,
        user_hash: userDeatilResp?.session_userhash,
        filterlist: filterlist,
        lasttouch: userDeatilResp?.lasttouch?.seen
          ? userDeatilResp?.lasttouch?.seen
          : lastTouch,
        userId: userId ? userId : userDetails?.parent_id
      })
    )
  }, [predictiveSearch])

  useEffect(() => {
    let recom_group_id = {}
    let recomendValueId = []
    recomendation &&
      recomendation?.length > 0 &&
      recomendation?.map(item => {
        if (item?.content_type == 'S' || item?.content_type == 'P') {
          recom_group_id[item?.id] = { format_types: item.format_types }
        }
        recomendValueId?.push(item.id)
      })
    if (
      state &&
      state.inputValue != searchValue &&
      (recomendation?.length != 0 || recom_group_id != {})
    ) {
      dispatch(
        getMulticontentSearchData({
          group_ids: { group_ids: recom_group_id },
          user_token: userDeatilResp?.user_token
        })
      )
    }
    if (recomendValueId?.length != 0 && predictiveSearch?.length == 0)
      dispatch(
        getProgressbarBookmark({
          group_id: recomendValueId.toString(),
          user_hash: userDeatilResp?.session_userhash,
          filterlist: filterlist,
          lasttouch: userDeatilResp?.lasttouch?.seen
            ? userDeatilResp?.lasttouch?.seen
            : lastTouch,
          userId: userId ? userId : userDetails?.parent_id
        })
      )
  }, [recomendation])

  useEffect(() => {
    let subscribedChannel = []
    payWayToken?.length > 0 &&
      payWayToken?.map(val => {
        val?.timeshift &&
          val?.timeshift != '0' &&
          (subscribedChannel = subscribedChannel.concat(val.groups.split(',')))
      })
    setPaywayChannels(subscribedChannel)
  }, [payWayToken])

  const getIsContractChanel = item => {
    const foundContract = payWayToken?.filter(each =>
      each?.groups?.includes(item)
    )
    if (foundContract?.length > 0) {
      return true
    }
    return false
  }

  useEffect(() => {
    if (unlockchannel?.msg === 'OK') {
      dispatch(getClearAllSettingsState())
    }
  }, [unlockchannel])

  useEffect(() => {
    if (lockchannel?.msg === 'OK') {
      dispatch(getClearAllSettingsState())
    }
  }, [lockchannel])

  useEffect(() => {
    if (playbackRetry && streamType) {
      dispatch(
        getMediaAPI({
          id: seriesData?.id ?? moviesData?.id,
          payway_token: addSubscriptions?.response?.playButton?.payway_token,
          HKS: vodDetails?.confirmscreen
            ? loginInfo?.session_stringvalue ??
              registerInfo?.session_stringvalue
            : userDeatilResp?.session_stringvalue,
          userId: vodDetails?.confirmscreen
            ? loginInfo?.user_id ?? registerInfo?.user_id
            : userDetails?.user_id,
          user_token: loginInfo?.user_token ?? registerInfo?.user_token,
          streamType
        })
      )
    }
  }, [playbackRetry, streamType])

  useEffect(() => {
    if (playbackRetry && (getMediaRes?.media || getMediaError?.code)) {
      setVodSimultaneous({})
      setPlaybackRetry(false)
      setStreamType(
         !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      const payload = {
        vistime: {
          last: { progress: getMediaRes?.media?.initial_playback_in_seconds }
        }
      }
      getMediaError?.code != 'PLY_PLY_00009' && handleCheckPlayerStatus(payload)
    }
  }, [getMediaRes, getMediaError, playbackRetry])

  useEffect(() => {
    const code = getMediaError?.code
    if (
      (code == 'PLY_DEV_00006' ||
        code == 'PLY_CSS_00001' ||
        code == 'PLY_CSS_00004' ||
        code == 'PLY_PLY_00009') &&
      playbackRetry
    ) {
      setPlaybackRetry(false)
      setStreamType(
        !supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0] + '_ma'
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
    }
    if (code == 'PLY_PLY_00009' && !playbackRetry) {
      setStreamType(
        supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
          ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
          : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
      )
      setPlaybackRetry(true)
    }
  }, [getMediaError])

  const callPurchaseApi = channelInfo => {
    setButtonClicked(true)
    setChannelInfoData(channelInfo)
    dispatch(
      getSubscriptionInfo({
        userId: userDeatilResp?.parent_id,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        url: `group_id=${
          channelInfo?.channel_group?.common?.id ?? channelInfo?.id
        }`
      })
    )
  }

  useEffect(() => {
    if (buttonClicked && addSubscriptions?.msg == 'OK') {
      const subscriptionResponse = addSubscriptions?.response?.listButtons
      const buttonMap = subscriptionResponse?.button ?? []
      if (buttonMap.length > 0) {
        if (buttonMap?.length == 1) {
          buttonMap?.map(each => {
            if (each?.purchasable) {
              navigate('/premiumSubscription', {
                state: {
                  data:
                    channelInfoData?.channel_group?.common ?? channelInfoData,
                  priceDetails: each,
                  returnPage: 'searchToPlayer',
                  pageName: '/search',
                  inputValue: searchValue
                }
              })
            } else {
              handleNavigateToNonContract(channelInfoData)
            }
          })
        } else {
          const subScriptionValuesFalse = buttonMap.every(
            item => item?.purchasable === false
          )
          if (subScriptionValuesFalse) {
            handleNavigateToNonContract(channelInfoData)
          } else {
            const subscriptionTrueValues = buttonMap?.every(
              item => item?.purchasable === true
            )
            const singleSubscription = buttonMap?.filter(
              item => item?.purchasable === true
            )
            const subscriptionNodeValueFalse = buttonMap?.filter(
              item => item?.purchasable === false
            )
            const exactlySingleSubscriptionTrue =
              singleSubscription?.length === 1 &&
              subscriptionNodeValueFalse?.length === buttonMap?.length - 1
            if (subscriptionTrueValues) {
              navigate('/multiSubscription', {
                state: {
                  pageName: 'search',
                  returnPage: 'searchToPlayer',
                  data:
                    channelInfoData?.channel_group?.common ?? channelInfoData,
                  inputValue: searchValue,
                  type: 'MultiSubscription'
                }
              })
            } else if (exactlySingleSubscriptionTrue) {
              navigate('/premiumSubscription', {
                state: {
                  data:
                    channelInfoData?.channel_group?.common ?? channelInfoData,
                  priceDetails: singleSubscription[0],
                  returnPage: 'searchToPlayer',
                  inputValue: searchValue
                }
              })
            }
          }
        }
      } else {
        handleNavigateToNonContract(channelInfoData)
      }
    }
  }, [addSubscriptions])

  const handleNavigateToNonContract = channelInfoData => {
    navigate('/subScriptionCallPage', {
      state: {
        data: channelInfoData?.channel_group?.common ?? channelInfoData,
        inputValue: searchValue,
        returnPage: 'searchToPlayer',
        pageName: 'search'
      }
    })
  }

  const truncateText = (str, length, ending) => {
    if (length == null) {
      length = 100
    }
    if (ending == null) {
      ending = '...'
    }
    if (str?.length > length) {
      return str?.substring(0, length - ending?.length) + ending
    } else {
      return str
    }
  }

  const handleClick = item => {
    setMoviesData(item)
    if (item?.is_series) {
      let vodSeriesId = item?.id
      dispatch(vodSeries({ vodSeriesId, userId }))
    }
    dispatch(
      getSubscriptionInfo({
        userId: userDeatilResp?.parent_id,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        url: `group_id=${seriesData?.id ?? item?.id}`
      })
    )
    if(videoplayerWarning) setWarningPlay(true)
  }

  const handleCheckPlayerStatus = (item, focusIndex) => {
    setMoviesData(item)
    setSelectedPlayBack(item)
    dispatch(setReturnFocusById(focusIndex))
    // Set flag to indicate we're waiting for API response

    dispatch(
      getVodMoviesCastData({
        id: item?.id,
        userId: userDeatilResp?.parent_id,
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        is_kids: userProfileflag
      })
    )
    dispatch(
      getStatusControlPin({
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        userId: userDeatilResp?.parent_id
      })
    )
    handleClick(item)
    dispatch(
      getControlPin({
        hks: vodDetails?.confirmscreen
          ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
          : userDeatilResp?.session_stringvalue,
        user_id: userDeatilResp?.parent_id,
        user_token: userDeatilResp?.user_token
      })
    )
    // Navigation logic is now handled in useEffect when getMediaRes becomes available
    // This ensures first click works properly
    setEventClicked(true)
  }

  const handleInputChange = e => {
    setCurrentButtonFocus('keyBoard')
    let inputVal = e
    // GA event triggers only if search characters length is greater than 3
    if (inputVal?.length >= 3) {
      const payload = {
        search_term: inputVal,
        user_id : userDeatilResp?.user_id,
        parent_id : userDeatilResp?.parent_id,
        suscriptions : userDeatilResp?.subscriptions && Object.keys(userDeatilResp?.subscriptions)?.filter(key =>  userDeatilResp?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : loginInfo || registerInfo? 'registrado':'anonimo'
      }
      pushSearchEvent(payload)
    }
    setSearchValue(inputVal)
    inputVal != ''
      ? setSearchvalueParams({ q: inputVal }, { replace: true })
      : setSearchvalueParams({}, { replace: true })
  }

  const handlebackButton = () => {
    setVideoPlayerWarning(false)
    setShowApiFailureModal(false)
    dispatch(setShowTalentModule(false))
  }

  const clearInput = () => {
    state?.inputValue != null
      ? setSearchValue(state?.inputValue)
      : setSearchValue('')
    setSearchvalueParams({}, { replace: true })
    talentSearchData = null
  }
  const goToMovies = (item, focusIndex) => {
    dispatch(setReturnFocusById(focusIndex))
    pushSearchEvent(payload)
    item?.is_series
      ? navigate('/series', {
          state: {
            data: item,
            inputValue: searchValue,
            returnPage: 'search',
            flag: true
          }
        })
      : navigate('/movies', {
          state: {
            vodData: item,
            inputValue: searchValue,
            returnPage: 'search',
            flag: true
          }
        })
  }

  const goToRecordLivepage = (item, focusindex, eventValue) => {
    // let recordgetmedia = eventValue?.actions?.play?.dashwv
    // (
    //   supportedStream?.default?.[
    //     typeof tizen == 'undefined' ? 'lg' : 'samsung'
    //   ]?.vod?.[0]?.includes('_ma')
    //     ? supportedStream?.default?.[
    //         typeof tizen == 'undefined' ? 'lg' : 'samsung'
    //       ]?.vod?.[0].slice(0, -3)
    //     : supportedStream?.default?.[
    //         typeof tizen == 'undefined' ? 'lg' : 'samsung'
    //       ]?.vod?.[0]
    // )
    pushSearchEvent(payload)
    if (lockedChannelsList?.find(e => e?.id == eventValue?.channel?.group_id)) {
      navigate('/my-settings/help-And-Settings/security-pin/configure', {
        state: {
          data: 'record',
          item: item ?? featureItem,
          pageName: '/vodPlayer',
          contentName: 'record',
          selectedContent: eventValue,
          backfocusid: focusindex,
          returnPage: 'search',
          inputValue: searchValue
        },
        replace: true
      })
    } else {
    if (CURRENT_PLATFORM === 'netrange' || CURRENT_PLATFORM === 'zeasn' || CURRENT_PLATFORM === 'hisense') {
    dispatch(
        getRecordPlayer(
          eventValue?.actions?.play?.(
            supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]?.includes('_ma')
              ? supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0].slice(0, -3)
              : supportedStream?.default?.[CURRENT_PLATFORM]?.vod?.[0]
          )
        )
      )   
    } else {
        dispatch(getRecordPlayer(eventValue?.actions?.play?.dashwv))
      }
      navigate('/vodPlayer', {
          state: {
            page: 'record',
            backfocusid: focusindex,
            returnPage: 'search',
            inputValue: searchValue,
            recordTitle: eventValue?.channel?.event?.name,
            showControls: 'vod'
          }
        })
    }
    dispatch(setReturnFocusById(focusindex))
  }

  const goToLiveSeries = (
    item,
    eventLchannel,
    featureItem,
    focusIndex,
    channelStatus
  ) => {
    pushSearchEvent(payload)
    let lockedChannel = navigate(
      '/my-settings/help-And-Settings/security-pin/configure',
      {
        state: {
          data: 'epg',
          item: item ?? featureItem,
          pageName: '/livePlayer',
          returnPage: 'search',
          inputValue: searchValue
        },
        replace: true
      }
    )
    dispatch(setReturnFocusById(focusIndex))
    let filteredLiveChannels = {
      group_id: item?.id ?? featureItem?.id,
      name: item?.title ?? featureItem?.title,
      number: item?.channel_number ?? featureItem?.channel_number,
      image: item?.image_small ?? featureItem?.image_small,
      timeshift: item?.timeshift ?? featureItem?.timeshift
    }

    if (eventLchannel == 'future') {
      dispatch(
        getChannelData({
          group_id: item?.id ?? featureItem?.id,
          timeshift: item?.timeshift ?? featureItem?.timeshift,
          epgIndex: epgSevenDaysData[1]?.channelResponse.findIndex(
            itrObj => itrObj.group_id == item?.id ?? featureItem?.id
          )
        })
      ),
        dispatch(
          getProgramDetailsData({
            programData: featureItem,
            channelData: filteredLiveChannels,
            fromSearch: true
          })
        ),
        channelStatus != 'lockedChanel'
          ? navigate('/livePlayer', {
              state: {
                showControls: 'live',
                featureTag: true,
                pageName: 'search',
                returnPage: 'search',
                inputValue: searchValue,
                returnFocus: focusIndex
              }, //Added showControls flag which will enable live player after navigation
              replace: true
            })
          : lockedChannel
    } else if (eventLchannel == 'ahora') {
      dispatch(
        getChannelData({
          group_id: item?.id ?? featureItem?.id,
          timeshift: item?.timeshift ?? featureItem?.timeshift,
          epgIndex: epgSevenDaysData[1]?.channelResponse.findIndex(
            itrObj => itrObj.group_id == item?.id ?? featureItem?.id
          )
        })
      )

      channelStatus != 'lockedChanel'
        ? navigate('/livePlayer', {
            state: {
              showControls: 'live',
              featureTag: false,
              pageName: 'search',
              returnPage: 'search',
              inputValue: searchValue,
              returnFocus: focusIndex
            }, //Added showControls flag which will enable live player after navigation
            replace: true
          })
        : lockedChannel
    } else if (eventLchannel == 'past') {
      dispatch(
        getChannelData({
          group_id: item?.id ?? featureItem?.id,
          timeshift: item?.timeshift ?? featureItem?.timeshift,
          switchChannel: 'yes',
          startTime: featureItem?.unix_begin ?? item?.actual_event?.unix_begin,
          endTime: featureItem?.unix_end ?? item?.actual_event?.unix_end,
          catchup: featureItem?.ext_catchup ?? item?.live_enabled,
          epgIndex: epgSevenDaysData[0]?.channelResponse.findIndex(
            itrObj => itrObj.group_id == item?.id ?? featureItem?.id
          )
        })
      )
      dispatch(
        getProgramDetailsData({
          programData: featureItem,
          channelData: filteredLiveChannels,
          fromSearch: true
        })
      )
      channelStatus != 'lockedChanel'
        ? navigate('/livePlayer', {
            state: {
              showControls: 'live',
              featureTag: false,
              pageName: 'search',
              returnPage: 'search',
              inputValue: searchValue,
              returnFocus: focusIndex
            }, //Added showControls flag which will enable live player after navigation
            replace: true
          })
        : lockedChannel
    }
  }
  const handleCastClick = (item, index) => {
    setFocusIndex(index)
    dispatch(setReturnFocusById(index))
    setTalentSearchfield(item?.castName)
    setTalentSearchfieldEnable(true)
    const payload = {
      hks: vodDetails?.confirmscreen
        ? loginInfo?.session_stringvalue ?? registerInfo?.session_stringvalue
        : userDeatilResp?.session_stringvalue,
      value: item?.id,
      typemusic: 'album',
      provider_id: '3',
      filterlist: filterlist,
      field: 'TALENT'
    }
    dispatch(getTalentSearchData(payload))
  }

  useEffect(() => {
    if (talentSearchData && talentSearchfieldEnable) {
      talentSearchData?.length > 0
        ? dispatch(setShowTalentModule(true))
        : navigate('/alertWarning', {
            state: {
              searchData: searchValue,
              pageName: 'search',
              returnPage: 'search',
              inputValue: searchValue
            }, //Added showControls flag which will enable live player after navigation
            replace: true
          })
    }
  }, [talentSearchData])

  const focusShift = keycode => {
    if (keycode === 38) {
      setTimeout(() => {
        voiceRef?.current?.focus()
        numRef?.current?.focus()
        abcRef?.current?.focus()
        borrarRef?.current?.focus()
        vaciarRef?.current?.focus()
      }, 200)
    }
  }

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const dateToYMD = curdate => {
    let contentDate = new Date(parseInt(curdate))
    var contentDay = contentDate.getUTCDate()
    var monthNames = [
      'Jan.',
      'Feb.',
      'Mar.',
      'Apr.',
      'May.',
      'Jun.',
      'Jul.',
      'Aug.',
      'Sep.',
      'Oct.',
      'Nov.',
      'Dec.'
    ]
    var formattedDate = contentDay + ' ' + monthNames[contentDate.getUTCMonth()]
    return formattedDate
  }

  const handleChannelsClass = item => {
    if (item?.content_type == 'E') {
      if (
        lockedChannelsList?.filter(e => e?.id == item?.channel_group_id)?.[0]
      ) {
        return lockedChannelsList
          ?.filter(e => e?.id == item?.channel_group_id)
          ?.map(
            eventData =>
              eventData?.id == item?.channel_group_id && 'image-layout-lock'
          )
      } else {
        return 'image-layout'
      }
    } else if (item?.content_type == 'L' || item?.record_id) {
      if (lockedChannelsList?.filter(e => e?.id == item?.id)?.[0]) {
        return lockedChannelsList
          ?.filter(e => e?.id == item?.id)
          ?.map(eventData => eventData?.id == item?.id && 'image-layout-lock')
      } else {
        return 'image-layout'
      }
    } else if (item?.record_id) {
      if (
        lockedChannelsList?.filter(e => e?.id == item?.channel?.group_id)?.[0]
      ) {
        return lockedChannelsList
          ?.filter(e => e?.id == item?.channel?.group_id)
          ?.map(
            eventData =>
              eventData?.id == item?.channel?.group_id && 'image-layout-lock'
          )
      } else {
        return 'image-layout'
      }
    }
  }

  const isReminderAvailable = programData => {
    let futures =
      new Date().getTime() <
      new Date(
        programData?.actual_event
          ? unixValue(programData?.actual_event?.unix_begin)
          : programData?.date_begin ?? programData?.begintime
      ).getTime()
        ? true
        : false

    const isReminder =
      typeof ReminderList != 'string' &&
      ReminderList?.filter(
        each =>
          each?.event_id == programData?.id ?? programData?.actual_event?.id
      )
    return isReminder?.length > 0 && futures ? true : false
  }

  const pastEvent = item => {
    let unixBeginDate = item?.actual_event
      ? unixValue(item?.actual_event?.unix_begin)
      : item?.date_begin ?? item?.begintime
    let unixEndDate = item?.actual_event
      ? unixValue(item?.actual_event?.unix_end)
      : item?.date_end ?? item?.endtime
    // let futures =
    //   new Date().getTime() < new Date(unixBeginDate).getTime() && 'future'
    // let ahora =
    //   new Date(unixBeginDate).getTime() <= new Date().getTime() &&
    //   new Date().getTime() < new Date(unixEndDate).getTime() &&
    //   'ahora'
    // let past = !ahora && !futures && 'past'
    // return past
    if (new Date().getTime() < new Date(unixBeginDate).getTime()) {
      return 'future'
    } else if (
      new Date(unixBeginDate).getTime() <= new Date().getTime() &&
      new Date().getTime() < new Date(unixEndDate).getTime()
    ) {
      return 'ahora'
    } else {
      return 'past'
    }
  }

  const handleChannelsNav = (item, focusIndex) => {
    dispatch(setReturnFocusById(focusIndex))
    let futures =
      new Date().getTime() <
        new Date(item?.date_begin ?? item?.begintime).getTime() && 'future'
    let ahora =
      new Date(item?.date_begin ?? item?.begintime).getTime() <=
        new Date().getTime() &&
      new Date().getTime() <=
        new Date(item?.date_end ?? item?.endtime).getTime() &&
      'ahora'
    let past = !ahora && !futures && 'past'

    if (item?.content_type == 'E') {
      if (
        lockedChannelsList?.filter(e => e?.id == item?.channel_group_id)?.[0]
      ) {
        goToLiveSeries(
          item?.channel_group?.common,
          futures ? futures : ahora ? ahora : past,
          item,
          focusIndex,
          'lockedChanel'
        )
      } else if (getIsContractChanel(item?.channel_group_id)) {
        goToLiveSeries(
          item?.channel_group?.common,
          futures ? futures : ahora ? ahora : past,
          item,
          focusIndex
        )
      } else {
        if (watchFree) {
          navigate('/EPconfirmation', {
            state: {
              returnPage: 'search',
              page: 'search',
              inputValue: searchValue,
              gaContentData: item
            }
          })     
        } else {
          callPurchaseApi(item)
        }
      }
    } else if (item?.content_type == 'L') {
      let futuresChannelLive =
        new Date().getTime() <
          new Date(unixValue(item?.actual_event?.unix_begin)).getTime() &&
        'future'

      let ahoraLive =
        new Date(unixValue(item?.actual_event?.unix_begin)).getTime() <=
          new Date().getTime() &&
        new Date().getTime() <=
          new Date(unixValue(item?.actual_event?.unix_end)).getTime() &&
        'ahora'

      let pastLive = !ahoraLive && !futuresChannelLive && 'past'

      if (lockedChannelsList?.filter(e => e?.id == item?.id)?.[0]) {
        goToLiveSeries(
          item?.channel_group?.common,
          futuresChannelLive
            ? futuresChannelLive
            : ahoraLive
            ? ahoraLive
            : pastLive,
          item,
          focusIndex,
          'lockedChanel'
        )
      } else if (getIsContractChanel(item?.id)) {
        goToLiveSeries(
          item,
          futuresChannelLive
            ? futuresChannelLive
            : ahoraLive
            ? ahoraLive
            : past,
          item,
          focusIndex
        )
      } else {
        if (watchFree) {
          navigate('/EPconfirmation', {
            state: {
              returnPage: 'search',
              page: 'search',
              inputValue: searchValue,
              gaContentData: item
            }
          })     
        } else {
          callPurchaseApi(item)
        }
      }
    }
  }

  function secondsToHms(d) {
    d = Number(d)
    let h = Math.floor(d / 3600)
    let m = Math.floor((d % 3600) / 60)
    let hDisplay = h > 0 ? h + ' h ' : ''
    let mDisplay = m > 0 ? m + ' min ' : ''
    return hDisplay + mDisplay
  }

  const dateToY = curdate => {
    return moment(curdate).format('DD MMM. | YYYY')
  }

  const unixValue = item => {
    return moment.unix(item).format('YYYY/MM/DD HH:mm:ss')
  }

  const goToMovieSeriesEvent = (item, focusIndex) => {
    dispatch(setReturnFocusById(focusIndex))
    if (item?.format_types != 'ppe,download' || item?.format_types != 'ppe') {
      if (
        multiContentSearch && Object.keys(multiContentSearch)?.includes(item?.id) &&
        multiContentSearch?.[item?.id]?.visible
      ) {
        let selectedData = progressbarContentData?.filter(
          eventData => eventData?.id == item?.id
        )?.[0]
        if (selectedData?.vistime?.last?.hasOwnProperty('progress')) {
          handleCheckPlayerStatus(selectedData, focusIndex)
        } else {
          handleCheckPlayerStatus(item, focusIndex)
        }
      } else {
        goToMovies(item, focusIndex)
      }
    } else {
      goToMovies(item, focusIndex)
    }
  }

  const isRecordAvailable = (eventData, channelData) => {
    const payway = payWayToken?.filter(
      each =>
        each?.groups?.includes(channelData?.common?.id) &&
        each?.npvrstorage != 0 &&
        each?.timeshift != 0
    )
    if (
      payway?.length > 0 &&
      eventData?.ext_recordable == '1' &&
      channelData?.common?.timeshift !== null
    ) {
      return true
    } else {
      return false
    }
  }

  const getRecordIcon = (channel_id, event_id, item) => {
    const recordCheckValue = channel_id + event_id
    const isRecordedSeries = recordingList?.series?.filter(
      each => each?.records == recordCheckValue
    )
    const isRecordedEpisode = recordingList?.recordings?.filter(
      each => each?.record_id == recordCheckValue
    )
    if (isRecordedSeries?.length > 0) {
      return 'images/Record_Complete.png'
    } else if (
      new Date().getTime() <
        new Date(item?.date_begin ?? item?.begintime).getTime() &&
      isRecordedEpisode?.length > 0 &&
      isRecordedEpisode[0]?.status == 'scheduled'
    ) {
      return 'images/Program_Details_Icons/record_icon.png'
    } else if (
      new Date().getTime() >
        new Date(unixValue(item?.date_end ?? item?.endtime)).getTime() &&
      isRecordedEpisode?.length > 0 &&
      isRecordedEpisode[0]?.status == 'ingested'
    ) {
      return 'images/Record_Complete.png'
    } else if (
      new Date(item?.date_begin ?? item?.begintime).getTime() <=
        new Date().getTime() &&
      new Date().getTime() <=
        new Date(item?.date_end ?? item?.endtime).getTime() &&
      isRecordedEpisode?.length > 0 &&
      isRecordedEpisode[0]?.status == 'recording'
    ) {
      return 'images/Program_Details_Icons/record_icon.png'
    } else {
      return false
    }
  }

  const handleTranslationchange = useCallback(
    keyname => {
      return apilanguage?.[keyname]
        ? apilanguage?.[keyname]
        : keyname?.slice(0, 13) + '...'
    },
    [apilanguage]
  )

  const popularSearch = () => {
    return (
      <>
        <div>
          {/* Kids functionality */}
          {/* {userDeatilResp?.is_kids == true ? (
                     <div className='error-layout'>
                         <div className='no-encontramos-coinc'>Es posible que este contenido no esté</div>
                         <div className='search-error'>disponible para este perfil.</div>
                         <div className='utiliza-otras-palabr'>
                             Podés elegir entre las opciones que te sugerimos a continuación.
                         </div>
                     </div>

                 ) : ( */}
          <div className="error-layout">
            <div className="no-encontramos-coinc">
              {handleTranslationchange(
                'Buscador_SinCoincidencias_Error1_Titulo'
              )}
            </div>
            <div
              className={
                recomendation?.length == 0 &&
                predictiveSearch?.length == 0 &&
                castData?.length == 0 &&
                searchValue?.length > 0
                  ? 'search-network-error'
                  : 'search-error'
              }
            >{`${searchValue}`}</div>
            <div className="utiliza-otras-palabr">
              {handleTranslationchange('Buscador_SinCoincidencias_Error_Hint1')}
            </div>
          </div>
          {/* )} */}

          {/* {userId && userDetails ? ( */}
          <>
            <span className="busquedas-populares">
                {handleTranslationchange('popularSearches_default_title_label')}
              </span>
            <div  className={hasError ? 'result-layout-error' : 'result-layout'}>
              {!isRecomendationLoading && recomendation?.length == 0 && (
                <div className="popular-error-layout">
                  <div>
                    {' '}
                    {handleTranslationchange(
                      'Buscador_SinContenidos_Error_Hint1'
                    )}
                    {handleTranslationchange(
                      'Buscador_SinContenidos_Error_Hint2'
                    )}
                  </div>
                </div>
              )}
              {isRecomendationLoading ? (
                <div className="search-container-loader">
                  <Lottie
                    options={{
                      rendererSettings: {
                        preserveAspectRatio: 'xMidYMid slice'
                      }
                    }}
                    loop
                    animationData={animationData}
                    play
                  />
                </div>
              ) : (
                sortedRecommendationArray?.length > 0 &&
                sortedRecommendationArray?.map((item, index, array) => (
                  <div className="result-container">
                    <>
                      <button
                        data-test="popularTest"
                        id={`popularSecondTest-${index}`}
                        className="image-layout focusable focusImg"
                        onFocus={() => {
                          setCurrentButtonFocus(`popularSecondTest-${index}`)
                          document
                            .getElementById(`popularSecondTest-${index}`)
                            .scrollIntoView({
                              behavior: 'smooth',
                              inline: 'center',
                              block: 'center'
                            })
                        }}
                        autoFocus={
                          showReturnFocus == `popularSecondTest-${index}`
                        }
                        key={index}
                        onClick={() => {
                          item?.content_type == 'L' || item?.content_type == 'E'
                            ? handleChannelsNav(
                                item,
                                `popularSecondTest-${index}`
                              )
                            : goToMovieSeriesEvent(
                                item,
                                `popularSecondTest-${index}`
                              )
                        }}
                      >
                        {item?.image_small &&
                        (item?.content_type == 'L' ||
                          item?.content_type == 'E') ? (
                          <LazyLoadImage
                            src={
                              item?.image_small || 'images/landscape_card.png'
                            }
                            loading="lazy"
                            alt="PlaceHolder"
                            className={handleChannelsClass(item)}
                            placeholderSrc={'images/landscape_card.png'}
                          />
                        ) : (
                          <LazyLoadImage
                            src={
                              item?.image_small || 'images/landscape_card.png'
                            }
                            loading="lazy"
                            alt="PlaceHolder"
                            className="popular-search-img-layout focusable"
                            placeholderSrc={'images/landscape_card.png'}
                          />
                        )}
                        {lockedChannelsList
                          ?.filter(e => e?.id == item?.id)
                          ?.map(
                            eventData =>
                              eventData?.id == item?.id && (
                                <>
                                  <div className="locked-channel-icon">
                                    <img
                                      src={
                                        'images/Search_Icons/lock_channel.png'
                                      }
                                      className="tag-alq"
                                    />
                                  </div>
                                </>
                              )
                          )}
                        <div className="inline-img-button-layout">
                          <div className="progress-button">
                            {progressbarContentData?.length > 0 &&
                              progressbarContentData?.map(eventData =>
                                eventData?.id == item?.id ? (
                                  <div className="progress-margin">
                                    <ProgressBar
                                      style={{
                                        visibility:
                                          eventData?.vistime?.last?.hasOwnProperty(
                                            'progress'
                                          )
                                            ? ''
                                            : 'hidden'
                                      }}
                                      isLoading={false}
                                      percent={
                                        eventData?.vistime?.last?.progress
                                      }
                                      size={'small'}
                                      showInfo={true}
                                      sliderWidth={219}
                                    />
                                  </div>
                                ) : null
                              )}
                          </div>
                          <div className="play-button">
                            {item?.content_type == 'L' ||
                            item?.content_type == 'E' ? (
                              paywaychannelChannels?.includes(
                                item?.content_type == 'L'
                                  ? item?.id
                                  : item?.channel_group_id
                              ) && (
                                <img
                                  src={'images/Search_Icons/ic_card_play.png'}
                                  loading="lazy"
                                  alt="PlaceHolder"
                                />
                              )
                            ) : multiContentSearch && Object.keys(multiContentSearch)?.includes(
                                item?.id
                              ) && multiContentSearch?.[item?.id]?.visible ? (
                              item?.format_types != 'ppe,download' ||
                              item?.format_types != 'ppe' ? (
                                <img
                                  src={'images/Search_Icons/ic_card_play.png'}
                                  loading="lazy"
                                  alt="PlaceHolder"
                                />
                              ) : null
                            ) : null}
                          </div>
                        </div>
                        {item.image_small && item?.proveedor_code == 'amco' ? (
                          item?.format_types === 'ppe,download' ? (
                            <div className="proveedor-block-rail-alq">
                              <img
                                src={'images/Alquilar.svg'}
                                className="tag-alq"
                              />
                            </div>
                          ) : item?.format_types === 'ppe' ? (
                            <div className="proveedor-block-rail-alq">
                              <img
                                src={'images/Alquilar.svg'}
                                className="tagAlq"
                              />
                            </div>
                          ) : null
                        ) : item.image_small &&
                          item?.proveedor_code &&
                          item?.image_medium ? (
                          <div className="proveedors-block-rail">
                            {Addproveedor(providerLabel?.[item?.proveedor_code]?.susc) && (
                              <img
                                className={
                                  item?.proveedor_code === 'picardia2'
                                    ? 'picardia-image'
                                    : 'premium-icon'
                                }
                                src={Addproveedor(providerLabel?.[item?.proveedor_code]?.susc)}
                              />
                            )}
                            {item?.image_small &&
                              item?.proveedor_code === 'picardia2' &&
                              item?.image_medium && (
                                <div className="adults-tag">
                                  <img
                                    src={'images/Adultus.svg'}
                                    className="picardia-tag"
                                  />
                                </div>
                              )}
                            {item?.format_types === 'free' &&
                            userDeatilResp?.subscriptions?.length == 0 ? (
                              <div className="proveedors-block-rail">
                                <div className="verahora-tag">
                                  {handleTranslationchange('labels_free_value')}
                                </div>
                              </div>
                            ) : null}
                          </div>
                        ) : null}
                      </button>
                      <div className="popular-detail-layout">
                        <div className="icon-tag-coloumn">
                          <div className="result-content-title">
                            {truncateText(item?.title, 18)}
                          </div>
                        </div>
                        {item?.content_type == 'P' && (
                          <div className="result-content-year">
                            {item?.year}
                          </div>
                        )}

                        {item?.content_type == 'S' && (
                          <div className="result-channel-detail">
                            {item?.season_number}
                            {' ' +
                              handleTranslationchange(
                                'Buscador_Resultados_Metadata_TextoTemporadaCorto'
                              )}{' '}
                            | {item?.year}
                          </div>
                        )}
                        {(item?.content_type == 'P' ||
                          item?.content_type == 'S') && (
                          <button
                            className="result-content-moreinfo focusable"
                            id={`masInfo-${index}`}
                            data-test="popularTest"
                            onFocus={() =>
                              setCurrentButtonFocus(`masInfo-${index}`)
                            }
                            key={index}
                            onClick={() => goToMovies(item, `masInfo-${index}`)}
                            autoFocus={showReturnFocus == `masInfo-${index}`}
                          >
                            <span className="mas-info">
                              {handleTranslationchange(
                                'Buscador_Resultados_texto_TextoBotonInfo'
                              )}
                            </span>
                          </button>
                        )}
                      </div>
                    </>
                  </div>
                ))
              )}
            </div>
          </>
          {/* ) : (null)} */}
        </div>
      </>
    )
  }
  return (
    <>
      {eventClicked && 
              <div className="search-container-loader">
                  <Lottie
                    options={{
                      rendererSettings: {
                        preserveAspectRatio: 'xMidYMid slice'
                      }
                    }}
                    loop
                    animationData={animationData}
                    play
                  />
                </div>}
      {showTalentModule ? (
        <TalentSearch
          filterlist={filterlist}
          talentSearchData={talentSearchData}
          talentSearchfield={talentSearchfield}
          Addproveedor={Addproveedor}
          setCurrentButtonFocus={setCurrentButtonFocus}
          userDeatilResp={userDeatilResp}
          currentButtonFocus={currentButtonFocus}
          keyParams={'search'}
          setSearchValue={setSearchValue}
          searchValue={searchValue}
          returnFocus={returnFocus}
          setFocusIndex={setFocusIndex}
        />
      ) : videoplayerWarning ? (
        <PlayerModelSelection
          handleBackInfoTab={handlebackButton}
          handleClick={handleClick}
          selectedPlayBack={selectedPlayBack}
          handleIconClick={setPlaybackChoice}
          keyParam={'search'}
          setSearchValue={setSearchValue}
          searchValue={searchValue}
          setCurrentButtonFocus={setCurrentButtonFocus}
        />
      ) : vodSimultaneous?.errorType ? (
        <PlayerErrorHandler
          retry={setPlaybackRetry}
          closePopup={setVodSimultaneous}
          data={vodSimultaneous}
        />
      ) : (
        <>
          {showTalentModule ? '' : <Navbar page={'search'} />}
          <div className="search-container-layout" id="search-container">
            <div className="keyboard-layout keyboardWrapper">
              <AlphaNumeric
                type="alphaNumeric"
                onChange={e => handleInputChange(e)}
                searchValue={searchValue}
                name="search"
                id="searchleft"
                className="keyboardimage focusable"
                dsndown={'#keyupdata0'}
                onkeyDown={focusShift}
                autoFocus={
                  showReturnFocus
                    ? currentButtonFocus === showReturnFocus
                    : currentButtonFocus === 'keyBoard'
                }
                onFocus={() =>
                  showReturnFocus
                    ? setCurrentButtonFocus(showReturnFocus)
                    : setCurrentButtonFocus('keyBoard')
                }
                data-testid="keyboardTest"
                setCurrentButtonFocus={setCurrentButtonFocus}
                numRef={numRef}
                abcRef={abcRef}
                borrarRef={borrarRef}
                vaciarRef={vaciarRef}
              />
            </div>
            <div className="search-screen-layout">
              <div>
                <button
                  className="search-input-layout focusable"
                  id="searchInput"
                  data-sn-up="#serachId"
                >
                  <LazyLoadImage
                    className="search-icon"
                    alt="Search.."
                    src={'images/Search_Icons/ic_input_search.png'}
                  />
                  <span className="cursor-point">
                    {searchValue?.length == 0 && '|'}
                  </span>

                  <input
                    className="search-input input-text"
                    placeholder={handleTranslationchange(
                      `search_default_placeHolder_textfield`
                    )}
                    autoComplete="off"
                    id="searchInput"
                    name="search"
                    type="text"
                    value={searchValue}
                    onChange={e => {
                      handleInputChange(e?.target?.value)
                    }}
                    inputMode="none"
                    data-testid="inputTest"
                    disabled
                  />
                </button>
              </div>

              {((!isLoadingPreSearch &&
                predictiveSearch &&
                predictiveSearch?.length > 0) ||
                (castData && castData?.length > 0)) &&
              searchValue ? (
                <span className="resultados">
                  {handleTranslationchange('Buscador_Resultados_TextoTitulo1')}
                </span>
              ) : (
                ''
              )}
              {predictiveSearch?.length == 0 &&
              castData?.length == 0 &&
              searchValue?.length > 0 ? (
                popularSearch()
              ) : (
                <>
                  {searchValue?.length <= 0 ? (
                    <>
                      {!isRecomendationLoading && (
                        <span className="busquedas-populares">
                          {handleTranslationchange(
                            'popularSearches_default_title_label'
                          )}
                        </span>
                      )}

                      {!isRecomendationLoading &&
                        recomendation?.length == 0 && (
                          <div className="popular-error-layout">
                            <div>
                              {' '}
                              {handleTranslationchange(
                                'Buscador_SinContenidos_Error_Hint1'
                              )}
                              {handleTranslationchange(
                                'Buscador_SinContenidos_Error_Hint2'
                              )}
                            </div>
                          </div>
                        )}
                      <div className="result-layout">
                        {sortedRecommendationArray?.length > 0 &&
                          sortedRecommendationArray?.map(
                            (item, index, array) => (
                              <div className="result-container">
                                <>
                                  <button
                                    data-test="popularTest"
                                    id={`recommendData-${index}`}
                                    className="image-layout focusable focusImg"
                                    onFocus={() => {
                                      setCurrentButtonFocus(
                                        `recommendData-${index}`
                                      )
                                    }}
                                    autoFocus={
                                      showReturnFocus ==
                                      `recommendData-${index}`
                                    }
                                    key={index}
                                    data-sn-right={
                                      index != array.length - 1 && undefined
                                    }
                                    onClick={() => {
                                      item?.content_type == 'L' ||
                                      item?.content_type == 'E'
                                        ? handleChannelsNav(
                                            item,
                                            `recommendData-${index}`
                                          )
                                        : goToMovieSeriesEvent(
                                            item,
                                            `recommendData-${index}`
                                          )
                                    }}
                                  >
                                    {item?.image_small &&
                                    (item?.content_type == 'L' ||
                                      item?.content_type == 'E') ? (
                                      <LazyLoadImage
                                        src={
                                          item?.image_small
                                            ? item?.image_small
                                            : 'images/landscape_card.png'
                                        }
                                        loading="lazy"
                                        alt="PlaceHolder"
                                        className={handleChannelsClass(item)}
                                      />
                                    ) : (
                                      <LazyLoadImage
                                        src={
                                          item?.image_small
                                            ? item?.image_small
                                            : 'images/landscape_card.png'
                                        }
                                        loading="lazy"
                                        alt="PlaceHolder"
                                        className="image-layout"
                                      />
                                    )}
                                    {lockedChannelsList
                                      ?.filter(e => e?.id == item?.id)
                                      ?.map(
                                        eventData =>
                                          eventData?.id == item?.id && (
                                            <>
                                              <div className="locked-channel-icon">
                                                <img
                                                  src={
                                                    'images/Search_Icons/lock_channel.png'
                                                  }
                                                  className="tag-alq"
                                                />
                                              </div>
                                            </>
                                          )
                                      )}

                                    <div className="inline-img-button-layout">
                                      <div className="progress-button">
                                        {progressbarContentData?.length > 0 &&
                                          progressbarContentData?.map(
                                            eventData =>
                                              eventData?.id == item?.id ? (
                                                <div className="progress-margin">
                                                  <ProgressBar
                                                    style={{
                                                      visibility:
                                                        eventData?.vistime?.last?.hasOwnProperty(
                                                          'progress'
                                                        )
                                                          ? ''
                                                          : 'hidden'
                                                    }}
                                                    isLoading={false}
                                                    percent={
                                                      eventData?.vistime?.last
                                                        ?.progress
                                                    }
                                                    size={'small'}
                                                    showInfo={true}
                                                    sliderWidth={219}
                                                  />
                                                </div>
                                              ) : null
                                          )}
                                      </div>
                                      <div className="play-button">
                                        {item?.content_type == 'L' ||
                                        item?.content_type == 'E' ? (
                                          paywaychannelChannels?.includes(
                                            item?.content_type == 'L'
                                              ? item?.id
                                              : item?.channel_group_id
                                          ) && (
                                            <img
                                              src={
                                                'images/Search_Icons/ic_card_play.png'
                                              }
                                              loading="lazy"
                                              alt="PlaceHolder"
                                            />
                                          )
                                        ) : item?.content_type == 'P' ||
                                          item?.content_type == 'S' ? (
                                            multiContentSearch && Object.keys(
                                            multiContentSearch
                                          )?.includes(item?.id) &&
                                          multiContentSearch?.[item?.id]
                                            ?.visible ? (
                                            item?.format_types !=
                                              'ppe,download' ||
                                            item?.format_types != 'ppe' ? (
                                              <img
                                                src={
                                                  'images/Search_Icons/ic_card_play.png'
                                                }
                                                loading="lazy"
                                                alt="PlaceHolder"
                                              />
                                            ) : null
                                          ) : null
                                        ) : null}
                                      </div>
                                    </div>
                                    {item?.image_small &&
                                    item?.proveedor_code == 'amco' ? (
                                      item?.format_types === 'ppe,download' ? (
                                        <div className="proveedor-block-rail-alq">
                                          <img
                                            src={'images/Alquilar.svg'}
                                            className="tag-alq"
                                          />
                                        </div>
                                      ) : item?.format_types === 'ppe' ? (
                                        <div className="proveedor-block-rail-alq">
                                          <img
                                            src={'images/Alquilar.svg'}
                                            className="tag-alq"
                                          />
                                        </div>
                                      ) : null
                                    ) : item?.image_small &&
                                      item?.proveedor_code &&
                                      item?.image_medium ? (
                                      <div className="proveedors-block-rail">
                                        {Addproveedor(providerLabel?.[item?.proveedor_code]?.susc) && (
                                          <img
                                            className={
                                              item?.proveedor_code ===
                                              'picardia2'
                                                ? 'picardia-image'
                                                : 'premium-icon'
                                            }
                                            src={Addproveedor(
                                              providerLabel?.[item?.proveedor_code]?.susc
                                            )}
                                          />
                                        )}
                                        {item?.image_small &&
                                          item?.proveedor_code ===
                                            'picardia2' &&
                                          item?.image_medium && (
                                            <div className="adults-tag">
                                              <img
                                                src={'images/Adultus.svg'}
                                                className="picardia-tag"
                                              />
                                            </div>
                                          )}
                                        {item?.format_types === 'free' &&
                                        userDeatilResp?.subscriptions?.length ==
                                          0 ? (
                                          <div className="proveedors-block-rail">
                                            <div className="verahora-tag">
                                              {handleTranslationchange(
                                                'labels_free_value'
                                              )}
                                            </div>
                                          </div>
                                        ) : null}
                                      </div>
                                    ) : null}
                                  </button>
                                  {/* {item?.content_type == 'P' || item?.content_type == 'S' ? */}
                                  <div className="popular-detail-layout">
                                    <div className="icon-tag-coloumn">
                                      <div className="result-content-title">
                                        {truncateText(item?.title, 18)}
                                      </div>
                                    </div>
                                    {item?.content_type == 'P' && (
                                      <div className="result-content-year">
                                        {item?.year}
                                      </div>
                                    )}

                                    {item?.content_type == 'S' && (
                                      <div className="result-channel-detail">
                                        {item?.season_number}
                                        {' ' +
                                          handleTranslationchange(
                                            'Buscador_Resultados_Metadata_TextoTemporadaCorto'
                                          )}{' '}
                                        | {item?.year}
                                      </div>
                                    )}
                                    {(item?.content_type == 'P' ||
                                      item?.content_type == 'S') && (
                                      <button
                                        className="result-content-moreinfo focusable"
                                        data-test="popularTest"
                                        onFocus={() =>
                                          setCurrentButtonFocus(
                                            `recommendData-${index}`
                                          )
                                        }
                                        key={index}
                                        autoFocus={
                                          showReturnFocus ==
                                          `recommendData-${index}`
                                        }
                                        onClick={() =>
                                          goToMovies(
                                            item,
                                            `recommendData-${index}`
                                          )
                                        }
                                      >
                                        <span className="mas-info">
                                          {handleTranslationchange(
                                            'Buscador_Resultados_texto_TextoBotonInfo'
                                          )}
                                        </span>
                                      </button>
                                    )}
                                  </div>
                                </>
                              </div>
                            )
                          )}
                      </div>
                    </>
                  ) : null}

                  <div className="result-layout">
                    {isLoadingPreSearch ? (
                      <div className="search-container-loader">
                        <Lottie
                          options={{
                            rendererSettings: {
                              preserveAspectRatio: 'xMidYMid slice'
                            }
                          }}
                          loop
                          animationData={animationData}
                          play
                        />
                      </div>
                    ) : ((searchValue?.length !== 0 && castData) ||
                        predictiveSearch) &&
                      (predictiveSearch?.length > 0 || castData?.length > 0) ? (
                      <>
                        {predictiveSearch?.length > 0 &&
                          predictiveSearch?.map((item, index, array) => (
                            <>
                              {item?.content_type == 'P' ||
                              item?.content_type == 'S' ? (
                                <div className="result-container">
                                  <>
                                    <button
                                      data-test="movieSeriesTest"
                                      className="image-layout focusable focusImg"
                                      id={`movieSeries-${index}`}
                                      onFocus={() => {
                                        setCurrentButtonFocus(
                                          `movieSeries-${index}`
                                        )
                                        document
                                          .getElementById(
                                            `movieSeries-${index}`
                                          )
                                          .scrollIntoView({
                                            behavior: 'smooth',
                                            inline: 'center',
                                            block: 'center'
                                          })
                                      }}
                                      autoFocus={
                                        showReturnFocus ==
                                        `movieSeries-${index}`
                                      }
                                      key={index}
                                      onClick={() =>
                                        goToMovieSeriesEvent(
                                          item,
                                          `movieSeries-${index}`
                                        )
                                      }
                                    >
                                      {item?.image_small &&
                                      (item?.content_type == 'L' ||
                                        item?.content_type == 'E') ? (
                                        <LazyLoadImage
                                          src={
                                            item?.image_small ||
                                            'images/landscape_card.png'
                                          }
                                          loading="lazy"
                                          alt="PlaceHolder"
                                          className="image-layout"
                                          placeholderSrc={
                                            'images/landscape_card.png'
                                          }
                                        />
                                      ) : (
                                        <LazyLoadImage
                                          src={
                                            item?.image_small ||
                                            'images/landscape_card.png'
                                          }
                                          loading="lazy"
                                          alt="PlaceHolder"
                                          className="popular-search-img-layout focusable"
                                          placeholderSrc={
                                            'images/landscape_card.png'
                                          }
                                        />
                                      )}

                                      <div className="inline-img-button-layout">
                                        <div className="progress-button">
                                          {multiContentSearch && Object.keys(
                                            multiContentSearch
                                          )?.includes(item?.id) &&
                                          multiContentSearch?.[item?.id]
                                            ?.visible
                                            ? progressbarContentData?.map(
                                                eventData =>
                                                  eventData?.id == item?.id ? (
                                                    <ProgressBar
                                                      style={{
                                                        visibility:
                                                          eventData?.vistime?.last?.hasOwnProperty(
                                                            'progress'
                                                          )
                                                            ? ''
                                                            : 'hidden'
                                                      }}
                                                      isLoading={false}
                                                      percent={
                                                        eventData?.vistime?.last
                                                          ?.progress
                                                      }
                                                      size={'small'}
                                                      showInfo={true}
                                                      sliderWidth={219}
                                                    />
                                                  ) : null
                                              )
                                            : null}
                                        </div>
                                        <div className="play-button">
                                          {multiContentSearch && Object.keys(
                                            multiContentSearch
                                          )?.includes(item?.id) &&
                                          multiContentSearch?.[item?.id]
                                            ?.visible ? (
                                            <img
                                              src={
                                                'images/Search_Icons/ic_card_play.png'
                                              }
                                              alt="PlaceHolder"
                                            />
                                          ) : null}
                                        </div>
                                      </div>
                                      <div>
                                        {item.image_small &&
                                        item?.proveedor_code == 'amco' ? (
                                          multiContentSearch && Object.keys(
                                            multiContentSearch
                                          )?.includes(item?.id) &&
                                          multiContentSearch?.[item?.id]
                                            ?.visible ? null : item?.format_types ===
                                            'ppe,download' ? (
                                            <div className="proveedor-block-rail-alq">
                                              <img
                                                src={'images/Alquilar.svg'}
                                                className="tag-alq"
                                              />
                                            </div>
                                          ) : item?.format_types === 'ppe' ? (
                                            <div className="proveedor-block-rail-alq">
                                              <img
                                                src={'images/Alquilar.svg'}
                                                className="tag-alq"
                                              />
                                            </div>
                                          ) : null
                                        ) : item.image_small &&
                                          item?.proveedor_code &&
                                          item?.image_medium ? (
                                          <div className="proveedors-block-rail">
                                            {Addproveedor(
                                              providerLabel?.[item?.proveedor_code]?.susc
                                            ) && (
                                              <img
                                                className={
                                                  item?.proveedor_code ===
                                                  'picardia2'
                                                    ? 'picardia-image'
                                                    : 'premium-icon'
                                                }
                                                src={Addproveedor(
                                                  providerLabel?.[item?.proveedor_code]?.susc
                                                )}
                                              />
                                            )}
                                            {item?.image_small &&
                                              item?.proveedor_code ===
                                                'picardia2' &&
                                              item?.image_medium && (
                                                <div className="adults-tag">
                                                  <img
                                                    src={'images/Adultus.svg'}
                                                    className="picardia-tag"
                                                  />
                                                </div>
                                              )}
                                            {item?.format_types === 'free' &&
                                            userDeatilResp?.subscriptions
                                              ?.length == 0 ? (
                                              <div className="proveedors-block-rail">
                                                <div className="verahora-tag">
                                                  {handleTranslationchange(
                                                    'labels_free_value'
                                                  )}
                                                </div>
                                              </div>
                                            ) : null}
                                          </div>
                                        ) : null}
                                      </div>
                                    </button>

                                    <div className="popular-detail-layout">
                                      <div className="icon-tag-coloumn">
                                        <div className="result-content-title">
                                          {truncateText(item?.title, 18)}
                                        </div>
                                      </div>
                                      {item?.content_type == 'P' && (
                                        <div className="result-content-year">
                                          {item?.year}
                                        </div>
                                      )}

                                      {item?.content_type == 'S' && (
                                        <div className="result-channel-detail">
                                          {item?.season_number}
                                          {' ' +
                                            handleTranslationchange(
                                              'Buscador_Resultados_Metadata_TextoTemporadaCorto'
                                            )}{' '}
                                          | {item?.year}
                                        </div>
                                      )}

                                      <button
                                        className="result-content-moreinfo focusable focusImg"
                                        data-test="movieSeriesTest"
                                        onFocus={() =>
                                          setCurrentButtonFocus(
                                            `movieSeries-${index}`
                                          )
                                        }
                                        id={`movieSeriesMasInfo-${index}`}
                                        key={index}
                                        onClick={() =>
                                          goToMovies(
                                            item,
                                            `movieSeriesMasInfo-${index}`
                                          )
                                        }
                                        autoFocus={
                                          showReturnFocus ==
                                          `movieSeriesMasInfo-${index}`
                                        }
                                      >
                                        <span className="mas-info">
                                          {handleTranslationchange(
                                            'Buscador_Resultados_texto_TextoBotonInfo'
                                          )}
                                        </span>
                                      </button>
                                    </div>
                                  </>
                                </div>
                              ) : null}
                              {/* channel */}
                              {item?.content_type == 'L' &&
                              item?.actual_event ? (
                                <>
                                  {item?.timeshift != '0' &&
                                  paywaychannelChannels?.includes(item?.id) ? (
                                    pastEvent(item) == 'past' ? null : (
                                      <div className="result-container">
                                        <>
                                          <button
                                            data-test="popularTest"
                                            id={`resultLive-${index}`}
                                            className="image-layout focusable focus-img"
                                            onFocus={() => {
                                              setCurrentButtonFocus(
                                                `resultLive-${index}`
                                              )
                                              document
                                                .getElementById(
                                                  `resultLive-${index}`
                                                )
                                                .scrollIntoView({
                                                  behavior: 'smooth',
                                                  inline: 'center',
                                                  block: 'center'
                                                })
                                            }}
                                            autoFocus={
                                              showReturnFocus ==
                                              `resultLive-${index}`
                                            }
                                            key={index}
                                            onClick={() => {
                                              handleChannelsNav(
                                                item,
                                                `resultLive-${index}`
                                              )
                                            }}
                                          >
                                            {item?.image_small ? (
                                              <LazyLoadImage
                                                src={
                                                  item?.image_small
                                                    ? item?.image_small
                                                    : 'images/landscape_card.png'
                                                }
                                                loading="lazy"
                                                alt="PlaceHolder"
                                                className={handleChannelsClass(
                                                  item
                                                )}
                                                placeholderSrc={
                                                  'images/landscape_card.png'
                                                }
                                              />
                                            ) : (
                                              <LazyLoadImage
                                                src="images/record_placeholder.png"
                                                loading="lazy"
                                                alt="PlaceHolder"
                                                className="image-layout"
                                              />
                                            )}

                                            {lockedChannelsList
                                              ?.filter(e => e?.id == item?.id)
                                              ?.map(
                                                eventData =>
                                                  eventData?.id == item?.id && (
                                                    <>
                                                      <div className="locked-channel-icon">
                                                        <img
                                                          src={
                                                            'images/Search_Icons/lock_channel.png'
                                                          }
                                                          className="tag-alq"
                                                        />
                                                      </div>
                                                    </>
                                                  )
                                              )}
                                            <div className="inline-img-button-layout">
                                              <div className="progress-button">
                                                {progressbarContentData?.length >
                                                  0 &&
                                                  progressbarContentData?.map(
                                                    eventData =>
                                                      eventData?.id ==
                                                      item?.id ? (
                                                        <div className="progress-margin">
                                                          <ProgressBar
                                                            style={{
                                                              visibility:
                                                                eventData?.vistime?.last?.hasOwnProperty(
                                                                  'progress'
                                                                )
                                                                  ? ''
                                                                  : 'hidden'
                                                            }}
                                                            isLoading={false}
                                                            percent={
                                                              eventData?.vistime
                                                                ?.last?.progress
                                                            }
                                                            size={'small'}
                                                            showInfo={true}
                                                            sliderWidth={219}
                                                          />
                                                        </div>
                                                      ) : null
                                                  )}
                                              </div>
                                              {paywaychannelChannels?.includes(
                                                item?.id
                                              ) && (
                                                <div className="play-button">
                                                  {new Date().getTime() <
                                                    new Date(
                                                      unixValue(
                                                        item?.actual_event
                                                          ?.unix_begin
                                                      )
                                                    ).getTime() &&
                                                  new Date().getTime() <
                                                    new Date(
                                                      unixValue(
                                                        item?.actual_event
                                                          ?.unix_end
                                                      )
                                                    ).getTime() ? null : (
                                                    <img
                                                      src={
                                                        'images/Search_Icons/ic_card_play.png'
                                                      }
                                                      loading="lazy"
                                                      alt="PlaceHolder"
                                                    />
                                                  )}
                                                </div>
                                              )}
                                            </div>
                                            {item.image_small &&
                                            item?.proveedor_code == 'amco' ? (
                                              item?.format_types ===
                                              'ppe,download' ? (
                                                <div className="proveedor-block-rail-alq">
                                                  <img
                                                    src={'images/Alquilar.svg'}
                                                    className="tag-alq"
                                                  />
                                                </div>
                                              ) : item?.format_types ===
                                                'ppe' ? (
                                                <div className="proveedor-block-rail-alq">
                                                  <img
                                                    src={'images/Alquilar.svg'}
                                                    className="tag-alq"
                                                  />
                                                </div>
                                              ) : null
                                            ) : item.image_small &&
                                              item?.proveedor_code &&
                                              item?.image_medium ? (
                                              <div className="proveedors-block-rail">
                                                {Addproveedor(
                                                  providerLabel?.[item?.proveedor_code]?.susc
                                                ) && (
                                                  <img
                                                    className={
                                                      item?.proveedor_code ===
                                                      'picardia2'
                                                        ? 'picardia-image'
                                                        : 'premium-icon'
                                                    }
                                                    src={Addproveedor(
                                                      providerLabel?.[item?.proveedor_code]?.susc
                                                    )}
                                                  />
                                                )}
                                                {item?.image_small &&
                                                  item?.proveedor_code ===
                                                    'picardia2' &&
                                                  item?.image_medium && (
                                                    <div className="adults-tag">
                                                      <img
                                                        src={
                                                          'images/Adultus.svg'
                                                        }
                                                        className="picardia-tag"
                                                      />
                                                    </div>
                                                  )}
                                                {item?.format_types ===
                                                  'free' &&
                                                userDeatilResp?.subscriptions
                                                  ?.length == 0 ? (
                                                  <div className="proveedors-block-rail">
                                                    <div className="verahora-tag">
                                                      {handleTranslationchange(
                                                        'labels_free_value'
                                                      )}
                                                    </div>
                                                  </div>
                                                ) : null}
                                              </div>
                                            ) : null}
                                          </button>
                                          <div className="result-detail-layout">
                                            <div className="icon-tag-coloumn">
                                              <div className="result-content-title">
                                                {truncateText(
                                                  item?.actual_event?.name,
                                                  18
                                                )}
                                              </div>
                                            </div>
                                            <div className="result-channel-detail">
                                              {item?.channel_number} |{' '}
                                              {truncateText(item?.title, 12)}
                                            </div>

                                            <div className="record-icon-row">
                                              <span>
                                                <LazyLoadImage
                                                  className="live-img"
                                                  alt="Search.."
                                                  src={
                                                    'images/Search_Icons/ic_cards_tv.png'
                                                  }
                                                />
                                              </span>
                                              <span className="deatil-status-tag">
                                                {item?.actual_event
                                                  ?.unix_begin &&
                                                  (new Date(
                                                    unixValue(
                                                      item?.actual_event
                                                        ?.unix_begin
                                                    )
                                                  ).getTime() <=
                                                    new Date().getTime() &&
                                                  new Date().getTime() <=
                                                    new Date(
                                                      unixValue(
                                                        item?.actual_event
                                                          ?.unix_end
                                                      )
                                                    ).getTime() ==
                                                    true ? (
                                                    <LazyLoadImage
                                                      className="deatil-status-tag"
                                                      alt="Search.."
                                                      src={
                                                        'images/Search_Icons/tag_card_ahora.png'
                                                      }
                                                    />
                                                  ) : new Date().getTime() <
                                                      new Date(
                                                        unixValue(
                                                          item?.actual_event
                                                            ?.unix_begin
                                                        )
                                                      ).getTime() ==
                                                    true ? (
                                                    <LazyLoadImage
                                                      className="deatil-status-tag"
                                                      alt="Search.."
                                                      src={
                                                        'images/Search_Icons/tag_card_mastarde.png'
                                                      }
                                                    />
                                                  ) : (
                                                    <LazyLoadImage
                                                      className="deatil-status-tag"
                                                      alt="Search.."
                                                      src={
                                                        'images/Search_Icons/tag_card_emitido.png'
                                                      }
                                                    />
                                                  ))}
                                              </span>
                                            </div>
                                            <div className="content-time">
                                              {dateToYMD(
                                                item?.actual_event &&
                                                  new Date(
                                                    unixValue(
                                                      item?.actual_event
                                                        ?.unix_begin
                                                    )
                                                  ).getTime()
                                              ) +
                                                ' ' +
                                                '|' +
                                                ' '}
                                              {item?.actual_event
                                                ? unixValue(
                                                    item?.actual_event
                                                      ?.unix_begin
                                                  ).slice(-8, -3)
                                                : `${item?.begintime?.slice(
                                                    -8,
                                                    -3
                                                  )} `}
                                              {' - '}
                                              {item?.actual_event
                                                ? unixValue(
                                                    item?.actual_event?.unix_end
                                                  ).slice(-8, -3)
                                                : `${item?.endtime?.slice(
                                                    -8,
                                                    -3
                                                  )}`}
                                            </div>
                                          </div>
                                        </>
                                      </div>
                                    )
                                  ) : null}
                                </>
                              ) : null}
                              {/* recording tray in progress */}
                              {Array.isArray(recordingsepisode) &&
                                recordingsepisode.length > 0 &&
                                recordingsepisode
                                  ?.filter(
                                    e => e?.channel?.group_id == item?.id
                                  )
                                  ?.map(
                                    (each, indexs) =>
                                      each?.channel?.group_id == item?.id &&
                                      each?.status == 'ingested' && (
                                        <div className="result-container">
                                          <>
                                            <button
                                              className="image-layout focusable focus-img"
                                              data-test="popularTest"
                                              id={`recordingLive-${index}`}
                                              onFocus={() => {
                                                setCurrentButtonFocus(
                                                  `recordingLive-${index}`
                                                )
                                                document
                                                  .getElementById(
                                                    `recordingLive-${index}`
                                                  )
                                                  .scrollIntoView({
                                                    behavior: 'smooth',
                                                    inline: 'center',
                                                    block: 'center'
                                                  })
                                              }}
                                              autoFocus={
                                                showReturnFocus ==
                                                `recordingLive-${index}`
                                              }
                                              key={index}
                                              onClick={() => {
                                                goToRecordLivepage(
                                                  item,
                                                  `recordingLive-${index}`,
                                                  each
                                                )
                                              }}
                                            >
                                              {each?.channel?.image ? (
                                                <>
                                                  <LazyLoadImage
                                                    src={each?.channel?.image}
                                                    placeholderSrc="images/record_placeholder.png"
                                                    key={index}
                                                    className={handleChannelsClass(
                                                      item
                                                    )}
                                                    id={`railfocus${index}`}
                                                  />
                                                </>
                                              ) : (
                                                <LazyLoadImage
                                                  src="images/record_placeholder.png"
                                                  loading="lazy"
                                                  alt="PlaceHolder"
                                                  className="image-layout"
                                                />
                                              )}
                                              {lockedChannelsList
                                                ?.filter(
                                                  e =>
                                                    e?.id ==
                                                    each?.channel?.group_id
                                                )
                                                ?.map(
                                                  eventData =>
                                                    eventData?.id ==
                                                      each?.channel
                                                        ?.group_id && (
                                                      <>
                                                        <div className="locked-channel-icon">
                                                          <img
                                                            src={
                                                              'images/Search_Icons/lock_channel.png'
                                                            }
                                                            className="tag-alq"
                                                          />
                                                        </div>
                                                      </>
                                                    )
                                                )}
                                              <div className="inline-img-button-layout">
                                                {paywaychannelChannels?.includes(
                                                  item?.id ??
                                                    item?.channel_group_id
                                                ) && (
                                                  <div className="inline-play-button">
                                                    {(new Date().getTime() <
                                                      new Date(
                                                        item?.actual_event
                                                          ? unixValue(
                                                              item?.actual_event
                                                                ?.unix_begin
                                                            )
                                                          : item?.date_begin ??
                                                            item?.begintime
                                                      ).getTime() &&
                                                      new Date().getTime() <
                                                        new Date(
                                                          item?.actual_event
                                                            ? unixValue(
                                                                item
                                                                  ?.actual_event
                                                                  ?.unix_end
                                                              )
                                                            : item?.date_end ??
                                                              item?.endTime
                                                        ).getTime()) ==
                                                    false ? (
                                                      <img
                                                        src={
                                                          'images/Search_Icons/ic_card_play.png'
                                                        }
                                                        alt="PlaceHolder"
                                                      />
                                                    ) : null}
                                                  </div>
                                                )}
                                              </div>
                                              <div className="deleteIcons-record">
                                                <img
                                                  src={
                                                    'images/Home_icons/red.png'
                                                  }
                                                  className="redDot"
                                                />
                                                <img
                                                  src={
                                                    'images/Home_icons/delete.png'
                                                  }
                                                  className="delete"
                                                />
                                              </div>
                                            </button>
                                            <div className="result-detail-layout">
                                              <div className="icon-tag-coloumn">
                                                <div className="result-content-title">
                                                  {each?.channel?.event?.name
                                                    ?.length > 19
                                                    ? `${each?.channel?.event?.name.slice(
                                                        0,
                                                        16
                                                      )}...`
                                                    : each?.channel?.event
                                                        ?.name}
                                                </div>
                                              </div>
                                              <div className="duration-time">
                                                {' '}
                                                {dateToY(each?.date)}
                                              </div>
                                              <div className="content-time">
                                                {secondsToHms(
                                                  each?.channel?.event?.duration
                                                )}
                                              </div>
                                              <div className="record-icon-row">
                                                <div className="recording-image">
                                                  <img
                                                    src={
                                                      'images/Search_Icons/ic_grabado.png'
                                                    }
                                                    className="grabado"
                                                  />
                                                </div>
                                                <div className="record-status">
                                                  {handleTranslationchange(
                                                    'Buscador_Resultados_Texto_EstadoGrabacion'
                                                  )}
                                                </div>
                                              </div>
                                            </div>
                                          </>
                                        </div>
                                      )
                                  )}
                              {item?.content_type == 'E' ? (
                                <>
                                  {!item?.channel_group?.common?.timeshift ||
                                  item?.channel_group?.common?.timeshift !=
                                    '0' ? (
                                    pastEvent(item) == 'past' ? null : (
                                      <div className="result-container">
                                        <>
                                          <button
                                            data-test="popularTest"
                                            id={`resultEvent-${index}`}
                                            className="image-layout focusable focusImg"
                                            onFocus={() => {
                                              setCurrentButtonFocus(
                                                `resultEvent-${index}`
                                              )
                                              document
                                                .getElementById(
                                                  `resultEvent-${index}`
                                                )
                                                .scrollIntoView({
                                                  behavior: 'smooth',
                                                  inline: 'center',
                                                  block: 'center'
                                                })
                                            }}
                                            autoFocus={
                                              showReturnFocus ==
                                              `resultEvent-${index}`
                                            }
                                            key={index}
                                            onClick={() =>
                                              handleChannelsNav(
                                                item,
                                                `resultEvent-${index}`
                                              )
                                            }
                                          >
                                            {item?.channel_image ? (
                                              <LazyLoadImage
                                                src={
                                                  item?.channel_image
                                                    ? item?.channel_image
                                                    : 'images/landscape_card.png'
                                                }
                                                loading="lazy"
                                                alt="PlaceHolder"
                                                className={handleChannelsClass(
                                                  item
                                                )}
                                                placeholderSrc={
                                                  'images/landscape_card.png'
                                                }
                                              />
                                            ) : (
                                              <LazyLoadImage
                                                src="images/record_placeholder.png"
                                                loading="lazy"
                                                alt="PlaceHolder"
                                                className="image-layout"
                                              />
                                            )}
                                            {lockedChannelsList
                                              ?.filter(
                                                e =>
                                                  e?.id ==
                                                  item?.channel_group_id
                                              )
                                              ?.map(
                                                eventData =>
                                                  eventData?.id ==
                                                    item?.channel_group_id && (
                                                    <>
                                                      <div className="locked-channel-icon">
                                                        <img
                                                          src={
                                                            'images/Search_Icons/lock_channel.png'
                                                          }
                                                          className="tag-alq"
                                                        />
                                                      </div>
                                                    </>
                                                  )
                                              )}
                                            <div className="inline-img-button-layout">
                                              <div className="progress-button">
                                                {progressbarContentData?.length >
                                                0 ? (
                                                  progressbarContentData?.map(
                                                    eventData =>
                                                      eventData?.id ==
                                                      item?.channel_group_id ? (
                                                        <div
                                                          style={{
                                                            marginTop: '56px'
                                                          }}
                                                        >
                                                          <ProgressBar
                                                            style={{
                                                              visibility:
                                                                eventData?.vistime?.last?.hasOwnProperty(
                                                                  'progress'
                                                                )
                                                                  ? ''
                                                                  : 'hidden'
                                                            }}
                                                            isLoading={false}
                                                            percent={
                                                              eventData?.vistime
                                                                ?.last?.progress
                                                            }
                                                            size={'small'}
                                                            showInfo={true}
                                                            sliderWidth={226}
                                                          />
                                                        </div>
                                                      ) : null
                                                  )
                                                ) : new Date(
                                                    item?.date_begin ??
                                                      item?.begintime
                                                  ).getTime() <=
                                                    new Date().getTime() &&
                                                  new Date().getTime() <
                                                    new Date(
                                                      item?.date_end ??
                                                        item?.endtime
                                                    ).getTime() ==
                                                    true ? (
                                                  <div
                                                    style={{
                                                      marginTop: '0.6px'
                                                    }}
                                                  >
                                                    <ProgressBar
                                                      style={{
                                                        visibility:
                                                          new Date(
                                                            item?.date_begin ??
                                                              item?.begintime
                                                          ).getTime() <=
                                                            new Date().getTime() &&
                                                          new Date().getTime() <
                                                            new Date(
                                                              item?.date_end ??
                                                                item?.endtime
                                                            ).getTime() ==
                                                            true
                                                            ? ''
                                                            : 'hidden'
                                                      }}
                                                      isLoading={false}
                                                      percent={Math.min(
                                                        ((moment().unix() -
                                                          item?.unix_begin) /
                                                          (item?.unix_end -
                                                            item?.unix_begin)) *
                                                          100,
                                                        100
                                                      )}
                                                      size={'small'}
                                                      showInfo={true}
                                                      sliderWidth={Math.min(
                                                        ((moment().unix() -
                                                          item?.unix_begin) /
                                                          (item?.unix_end -
                                                            item?.unix_begin)) *
                                                          100,
                                                        100
                                                      )}
                                                    />
                                                  </div>
                                                ) : null}
                                              </div>

                                              {(paywaychannelChannels?.includes(
                                                item?.channel_group_id
                                              ) ||
                                                (new Date(
                                                  item?.date_begin ??
                                                    item?.begintime
                                                ).getTime() <=
                                                  new Date().getTime() &&
                                                  new Date().getTime() <
                                                    new Date(
                                                      item?.date_end ??
                                                        item?.endtime
                                                    ).getTime() ==
                                                    true)) && (
                                                <div className="play-button">
                                                  {(new Date().getTime() <
                                                    new Date(
                                                      item?.date_begin ??
                                                        item?.begintime
                                                    ).getTime() &&
                                                    new Date().getTime() <
                                                      new Date(
                                                        item?.date_end ??
                                                          item?.endtime
                                                      ).getTime()) == false ? (
                                                    <img
                                                      src={
                                                        'images/Search_Icons/ic_card_play.png'
                                                      }
                                                      loading="lazy"
                                                      alt="PlaceHolder"
                                                    />
                                                  ) : null}
                                                </div>
                                              )}
                                            </div>

                                            {item.image_small &&
                                            item?.proveedor_code == 'amco' ? (
                                              item?.format_types ===
                                              'ppe,download' ? (
                                                <div className="proveedor-block-rail-alq">
                                                  <img
                                                    src={'images/Alquilar.svg'}
                                                    className="tag-alq"
                                                  />
                                                </div>
                                              ) : item?.format_types ===
                                                'ppe' ? (
                                                <div className="proveedor-block-rail-alq">
                                                  <img
                                                    src={'images/Alquilar.svg'}
                                                    className="tag-alq"
                                                  />
                                                </div>
                                              ) : null
                                            ) : item.image_small &&
                                              item?.proveedor_code &&
                                              item?.image_medium ? (
                                              <div className="proveedors-block-rail">
                                                {Addproveedor(
                                                 providerLabel?.[item?.proveedor_code]?.susc
                                                ) && (
                                                  <img
                                                    className={
                                                      item?.proveedor_code ===
                                                      'picardia2'
                                                        ? 'picardia-image'
                                                        : 'premium-icon'
                                                    }
                                                    src={Addproveedor(
                                                      providerLabel?.[item?.proveedor_code]?.susc
                                                    )}
                                                  />
                                                )}
                                                {item?.image_small &&
                                                  item?.proveedor_code ===
                                                    'picardia2' &&
                                                  item?.image_medium && (
                                                    <div className="adults-tag">
                                                      <img
                                                        src={
                                                          'images/Adultus.svg'
                                                        }
                                                        className="picardia-tag"
                                                      />
                                                    </div>
                                                  )}
                                                {item?.format_types ===
                                                  'free' &&
                                                userDeatilResp?.subscriptions
                                                  ?.length == 0 ? (
                                                  <div className="proveedors-block-rail">
                                                    <div className="verahora-tag">
                                                      {handleTranslationchange(
                                                        'labels_free_value'
                                                      )}
                                                    </div>
                                                  </div>
                                                ) : null}
                                              </div>
                                            ) : null}
                                          </button>
                                          <div className="result-detail-layout">
                                            <div className="icon-tag-coloumn">
                                              <div className="result-content-title">
                                                {truncateText(item?.name, 18)}
                                              </div>
                                            </div>
                                            <div className="result-channel-detail">
                                              {item?.channel_number} |{' '}
                                              {truncateText(
                                                item?.channel_name,
                                                12
                                              )}
                                            </div>
                                            <div className="record-icon-row">
                                              <span>
                                                <LazyLoadImage
                                                  className="live-img"
                                                  alt="Search.."
                                                  src={
                                                    'images/Search_Icons/ic_cards_tv.png'
                                                  }
                                                />
                                              </span>
                                              <span className="deatil-status-tag">
                                                {new Date(
                                                  item?.date_begin ??
                                                    item?.begintime
                                                ).getTime() <=
                                                  new Date().getTime() &&
                                                new Date().getTime() <=
                                                  new Date(
                                                    item?.date_end ??
                                                      item?.endtime
                                                  ).getTime() ==
                                                  true ? (
                                                  <LazyLoadImage
                                                    className="deatil-status-tag"
                                                    alt="Search.."
                                                    src={
                                                      'images/Search_Icons/tag_card_ahora.png'
                                                    }
                                                  />
                                                ) : new Date().getTime() <
                                                    new Date(
                                                      item?.date_begin ??
                                                        item?.begintime
                                                    ).getTime() ==
                                                  true ? (
                                                  <LazyLoadImage
                                                    className="deatil-status-tag"
                                                    alt="Search.."
                                                    src={
                                                      'images/Search_Icons/tag_card_mastarde.png'
                                                    }
                                                  />
                                                ) : (
                                                  <LazyLoadImage
                                                    className="deatil-status-tag"
                                                    alt="Search.."
                                                    src={
                                                      'images/Search_Icons/tag_card_emitido.png'
                                                    }
                                                  />
                                                )}
                                              </span>
                                              {isRecordAvailable(
                                                item,
                                                item?.channel_group
                                              ) &&
                                                getRecordIcon(
                                                  item?.channel_id,
                                                  item?.id,
                                                  item
                                                ) && (
                                                  <span>
                                                    <LazyLoadImage
                                                      src={getRecordIcon(
                                                        item?.channel_id,
                                                        item?.id,
                                                        item
                                                      )}
                                                      className="icon-recordatorio"
                                                    />
                                                  </span>
                                                )}
                                              {isReminderAvailable(item) && (
                                                <span>
                                                  <LazyLoadImage
                                                    src={
                                                      'images/reminder_icon_small.png'
                                                    }
                                                    className="reminder-icon"
                                                  />
                                                </span>
                                              )}
                                            </div>
                                            <div className="content-time">
                                              {dateToYMD(
                                                new Date(
                                                  item?.date_begin ??
                                                    item?.begintime
                                                ).getTime()
                                              ) +
                                                ' ' +
                                                '|' +
                                                ' '}
                                              {`${item?.begintime?.slice(
                                                -8,
                                                -3
                                              )} `}
                                              {' - '}
                                              {`${item?.endtime?.slice(
                                                -8,
                                                -3
                                              )}`}
                                            </div>
                                          </div>
                                        </>
                                      </div>
                                    )
                                  ) : null}
                                </>
                              ) : null}
                            </>
                          ))}
                        {uniqueCastData?.length > 0 ? (
                          <>
                            {uniqueCastData?.map((item, index) => (
                              <div className="cast-result-container">
                                <>
                                  <div className="talent-wrapper">
                                    <button
                                      data-test="popularTest"
                                      id={`castLayout-${index}`}
                                      className="cast-button-layout focusable"
                                      onFocus={() => {
                                        setCurrentButtonFocus(
                                          `castLayout-${index}`
                                        )
                                        showReturnFocus ===
                                          `castLayout-${index}` &&
                                          setCurrentButtonFocus(
                                            `castLayout-${index}`
                                          )
                                        document
                                          .getElementById(`castLayout-${index}`)
                                          .scrollIntoView({
                                            behavior: 'smooth',
                                            inline: 'center',
                                            block: 'center'
                                          })
                                      }}
                                      autoFocus={
                                        showReturnFocus == `castLayout-${index}`
                                      }
                                      key={index}
                                      onClick={() =>
                                        handleCastClick(
                                          item,
                                          `castLayout-${index}`
                                        )
                                      }
                                    >
                                      <LazyLoadImage
                                        src={
                                          item?.image
                                            ? item?.image
                                            : 'images/Search_Icons/talento_img.png'
                                        }
                                        loading="lazy"
                                        alt="PlaceHolder"
                                        className="talent-oval"
                                        placeholderSrc={
                                          'images/Search_Icons/talento_img.png'
                                        }
                                      />
                                    </button>
                                    {/*  */}
                                    <div className="talent-result-detail-layout">
                                      <div className="icon-tag-coloumn">
                                        <div className="talent-name">
                                          {truncateText(item?.castName, 18)}
                                        </div>
                                      </div>
                                      <div className="actor-director">
                                        {item?.search_roles?.length > 0 &&
                                          item?.search_roles
                                            .filter(
                                              (role, index, self) =>
                                                index ===
                                                self.findIndex(
                                                  t => t.NOMBRE === role.NOMBRE
                                                )
                                            )
                                            .map((e, i, arr) => {
                                              return i < 4
                                                ? e?.NOMBRE +
                                                    `${
                                                      i === arr.length - 1
                                                        ? ' '
                                                        : ', '
                                                    }`
                                                : i === 4
                                                ? '...'
                                                : null
                                            })}
                                      </div>
                                    </div>
                                  </div>
                                </>
                              </div>
                            ))}
                          </>
                        ) : null}
                      </>
                    ) : null}
                  </div>
                </>
              )}
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default Search
