<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="146" height="50.004" viewBox="0 0 146 50.004">
  <defs>
    <filter id="Background" x="0" y="0" width="146" height="50.004" filterUnits="userSpaceOnUse">
      <feOffset dy="4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#8d4ca7" flood-opacity="0.149"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Atoms_AAF_Tag_Alquilá" data-name="Atoms/AAF/Tag/Alquilá" transform="translate(17.986 4.826)">
    <g transform="matrix(1, 0, 0, 1, -17.99, -4.83)" filter="url(#Background)">
      <rect id="Background-2" data-name="Background" width="128" height="32.004" rx="3" transform="translate(9 5)" fill="#1d3374"/>
    </g>
    <text id="ALQUILÁ" transform="translate(11.014 23.174)" fill="#fff" font-size="19.2" font-family="Roboto-Medium, Roboto" font-weight="500" letter-spacing="0.017em"><tspan x="0" y="0">PICARDIA</tspan></text>
  </g>
</svg>
