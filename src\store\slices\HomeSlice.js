import { createSlice } from '@reduxjs/toolkit'

export const homeSlice = createSlice({
  name: 'homeSlice',
  initialState: {
    bannerHightlight: {},
    navbarData: {},
    navbarNodeData: {},
    navbarKidsData: {},
    pageData: [],
    pageKidsData: [],
    urlsData: [],
    urlsDataKids: [],
    level: [],
    levelKids: [],
    levelV1Response: {},
    levelUserV1Response: {},
    premiumData: {},
    premiumNodeValue: {},
    NavTabValue: 'homeuser',
    storenavdata: 'Inicio',
    storeFirstIndex:'',
    isLoading: false,
    error: {},
    mycontent: false,
    notificationmsg: false,
    notificationdata: false,
    premiumCardFocus: false,
    myListCurrFocus: false,
    premiumRailFocus: false,
    contentMenu: ''
  },

  reducers: {
    // NavData slice
    getNavData: state => {
      state.isLoading = true
    },
    getNavSuccess: (state, { payload }) => {
      state.navbarData = payload
      state.isLoading = false
    },
    getNavSuccessallNodes: (state, { payload }) => {
      state.navbarNodeData = payload
    },
    getPage: (state, { payload }) => {
      state.pageData = payload
      state.isLoading = false
    },
    getNavError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getUrls: (state, { payload }) => {
      state.urlsData = payload
      state.isLoading = false
    },
    getLevel: (state, { payload }) => {
      state.level = payload
      state.isLoading = false
    },

    //Kids Nav Data
    getNavDataKids: state => {
      state.isLoading = true
    },
    getNavKidsSuccess: (state, { payload }) => {
      state.navbarKidsData = payload
      state.isLoading = false
    },
    getPageKids: (state, { payload }) => {
      state.pageKidsData = payload
      state.isLoading = false
    },
    getNavKidsError: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getUrlsKids: (state, { payload }) => {
      state.urlsDataKids = payload
      state.isLoading = false
    },
    getLevelKids: (state, { payload }) => {
      state.levelKids = payload
      state.isLoading = false
    },

    // bannerslice
    getBannerData: state => {
      state.isLoading = true
    },
    getBannerSuccess: (state, { payload }) => {
      state.bannerHightlight = payload
      state.isLoading = false
    },
    getBannerError: (state, {payload}) => {
      state.isLoading = false
      state.error = payload
    },

    // premium channel
    premiumCarouselData: state => {
      state.isLoading = true
    },
    premiumCarouselDataSuccess: (state, { payload }) => {
      state.premiumData = payload
      state.isLoading = false
    },
    premiumCarouselDataFailure: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    // leveluser

    getPremiumNodeValue: (state, { payload }) => {
      state.premiumNodeValue = payload
    },
    getPremiumCardFocus: (state, { payload }) => {
      state.premiumCardFocus = payload
    },

    getContentMenu: (state, {payload}) => {
      state.contentMenu = payload
    },

    getPremiumValueStateClear: state => {
      state.premiumNodeValue = {}
    },

    getNavTabValue: (state, { payload }) => {
      state.NavTabValue = payload
    },
    getNavTabValueClear: state => {
      state.NavTabValue = {}
    },
    // while go to down to top should reach the active
    getFirstIndex: (state, { payload }) => {
      state.storeFirstIndex = payload
    },
    getNavValue: (state, { payload }) => {
      state.storenavdata = payload
    },
    getMyContentPage: (state, { payload }) => {
      state.mycontent = payload
    },
    getNotificationmsg: (state, { payload }) => {
      state.notificationmsg = payload
    },
    getNotification: (state, { payload }) => {
      state.notificationdata = payload
    },
    getMyListCurrFocus: (state, { payload }) => {
      state.myListCurrFocus = payload
    },
    getCMSLevelV1: state => {
      state.isLoading = true
    },
    getCMSLevelV1Success: (state, { payload }) => {      
      state.levelV1Response = payload
      state.isLoading = false
    },
    getCMSLevelV1Error: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },

    getCMSLevelUserV1: state => {
      state.isLoading = true
    },
    getCMSLevelUserV1Success: (state, { payload }) => {      
      state.levelUserV1Response = payload
      state.isLoading = false
    },
    getCMSLevelUserV1Error: (state, { payload }) => {
      state.isLoading = false
      state.error = payload
    },
    getPremiumFocus: (state, { payload }) => {
      state.premiumRailFocus = payload
    },
    getClearState: state => {
      state.level = []
      state.levelKids = []
      state.levelV1Response = {}
      state.pageData = []
      state.navbarData = {}
      state.navbarNodeData = {}
      state.urlsData = []
      state.levelUserV1Response = {}
    },
    getClearStatePremiumCard: state => {
      state.levelV1Response = {}
      state.levelUserV1Response = {}
      state.bannerHightlight = {}
    }
  }
})

export const {
  getNavData,
  getNavSuccess,
  getNavSuccessallNodes,
  getNavError,
  getNavDataKids,
  getNavKidsSuccess,
  getNavKidsError,
  getPage,
  getUrls,
  getLevel,
  getPageKids,
  getUrlsKids,
  getLevelKids,
  premiumCarouselData,
  premiumCarouselDataSuccess,
  premiumCarouselDataFailure,
  getBannerData,
  getBannerSuccess,
  getBannerError,
  getPremiumNodeValue,
  getPremiumValueStateClear,
  getNavValue,
  getFirstIndex,
  getMyContentPage,
  getNotificationmsg,
  getNotification,
  getClearState,
  getClearStatePremiumCard,
  getNavTabValue,
  getNavTabValueClear,
  getPremiumCardFocus,
  getContentMenu,
  getMyListCurrFocus,
  getPremiumFocus,
  getCMSLevelV1,
  getCMSLevelV1Success,
  getCMSLevelV1Error,
  getCMSLevelUserV1,
  getCMSLevelUserV1Success,
  getCMSLevelUserV1Error
} = homeSlice.actions

export default homeSlice.reducer
