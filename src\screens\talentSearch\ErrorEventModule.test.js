import React from 'react'
import { fireEvent, queryByAttribute, render } from '@testing-library/react/'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import { MemoryRouter } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import { fromJS } from 'immutable'
import ErrorEventModule from './ErrorEventModule'

const initialState = fromJS({})
const mockStore = configureStore([])
const history = createHistory()
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <MemoryRouter
      history={history}
      initialEntries={[{ state: { pageName: 'search', searchData: 'hgdsfv' } }]}
    >
      {children}
    </MemoryRouter>
  </Provider>
)
export const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}
describe('ErrrEventModule page test', () => {
  test('it should render the ErrrEventModule Page', () => {
    const props = {
      pageName: 'vodSeries',
      setCurrentButtonFocus: jest.fn()
    }
    const { container } = renderWithState(<ErrorEventModule {...props} />)
    initialState.SubMenuFilter = {
      filterclickdata: true
    }
    const getById = queryByAttribute.bind(null, 'id')
    const scroll = getById(container, 'searchdata')
    fireEvent.keyUp(scroll, { keyCode: '461' })
    fireEvent(
      scroll,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })
  test('it should render the ErrrEventModule Page', () => {
    const props = {
      pageName: 'vodMovies',
      setCurrentButtonFocus: jest.fn()
    }
    const { container } = renderWithState(<ErrorEventModule {...props} />)
    initialState.SubMenuFilter = {
      filterclickdata: true
    }
    const getById = queryByAttribute.bind(null, 'id')
    const scroll = getById(container, 'searchdata')
    fireEvent.keyUp(scroll, { keyCode: '461' })
    fireEvent(
      scroll,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
  })

  test('handles Samsung TV green key', () => {
    const props = {
      onChange: jest.fn(),
      type: 'ErrorEventModule',
      setCurrentButtonFocus: jest.fn()
    }
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 10009 })
      }
    }
    const { container } = renderWithState(<ErrorEventModule {...props} />)
    const getById = queryByAttribute.bind(null, 'id')
    const scroll = getById(container, 'searchdata')
    fireEvent.keyUp(scroll, { keyCode: 10009 })
    fireEvent(
      scroll,
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true
      })
    )
    delete global.tizen
  })
})
