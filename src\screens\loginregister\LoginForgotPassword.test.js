import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import LoginForgotPassword from "./LoginForgotPassword";

const initialState = fromJS({});
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};
describe('LoginForgotPassword page test', () => {
    test('it should render the LoginForgotPassword Page onclick enter email button', () => {
        const { container } = renderWithState(<LoginForgotPassword />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'email');
        fireEvent.keyPress(scroll, { key: "Enter", charCode: 13 })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/loginForgotPasswordMail')
    })
    test('it should render the LoginForgotPassword Page onclick signin button', () => {
        const state = {
            email: 'Preethi12345'
        }
        const { container } = renderWithState(<LoginForgotPassword {...state} />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'signid');
        fireEvent.keyUp(scroll, { keyCode: '10009' })
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
        history.push('/loginForgotPasswordMessage')
    })
    test('it should render the LoginForgotPassword Page onclick cancle button', () => {
        const { container } = renderWithState(<LoginForgotPassword />)
        const getById = queryByAttribute.bind(null, 'id');
        const scroll = getById(container, 'cancleid');
        fireEvent(
            scroll,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })
})
