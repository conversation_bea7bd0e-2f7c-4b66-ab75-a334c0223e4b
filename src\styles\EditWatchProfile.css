.app-logo {
    width: 1920px;
    height: 1080px;
}

.logo-size {
    margin-top: 157px;
    margin-left: 821px;
    width: 261px;
    height: 76px;

}

.profilewatch {
    margin-top: 70px;
    height: 57px;
    text-align: center;
    font-size: 48px;
    font-family: <PERSON><PERSON>;
    letter-spacing: 0px;
    color: #EEEEEE;
    opacity: 1;
}

.profileimg_container {
    display: flex;
    justify-content: center;
    margin-top: 8rem;
}

.editwatch_profbtn {
    margin-top: 10rem;
    margin-left: 52rem;
    width: 360px;
    font-size: 34px;
    height: 72px;
    color: #EEEEEE;
    background: #2E303D 0% 0% no-repeat padding-box;
    border-radius: 44px;
    opacity: 1;
    text-align: center;
}

.watchprof_names {
    width: 224px;
    height: 224px;
    padding-left: 2rem;
    padding-right: 4rem;
}

.watchprofile_name {
    margin-left: 5rem;
    height: 33px;
    text-align: left;
    font-size: 28px;
    font-family: <PERSON><PERSON>;
    letter-spacing: 0px;
    color: #A4A9AE;
    opacity: 1;
}

.imagetitle {
    text-align: center;
    position: relative;
}

.editicon_img {
       width: 73px;
       height: 73px;
       margin: auto;
       display: flex;   
       }

.UserProfileImage:focus>.editicon_img,
.UserProfileImage:active>.editicon_img {
    top: 125px;
    left: 120px;
}



.profile-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4.5rem;
    background: transparent linear-gradient(90deg, #2B2C31F2 0%, #34353BF2 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 20px #00000029;
    opacity: 1;
    margin-top: 5rem;
    display: flex;
    justify-content: space-between;
}

.footer-text {
    width: 57px;
    height: 25px;
    white-space: nowrap;
    text-align: left;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    font-family: "Roboto";
    font-size: 22px;
    margin-right: 57rem;
}

.profile_backbtn {
    margin-top: 1rem;
    width: 40px;
    height: 40px;
    margin-left: 56rem;
}


.NotificationLayout {
    background: #1A1A1A 0% 0% no-repeat padding-box;
    border: 2px solid #50595E;
    border-radius: 10px 0px 0px 10px;
    opacity: 1;
    width: 0;
    align-items: center;
    display: flex;
    position: absolute;
    white-space: nowrap;
    height: 140px;
    top: 60px;
    right: -4px;
    transition: cubic-bezier(1, 0, 0, 1) ;
    /* transition: 2s cubic-bezier(0, -0.31, 0.99, 1.24); */
    overflow-x: hidden;
}

.NotificationLayout.show {
    width: 750px;
    opacity: 1 !important;
}

.NotificationTextLayout {
    margin-left: 42px;
}

.NotificationText {
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    width: 500px !important;
    margin: 13px 0px;
    text-wrap: wrap;
}

.EditUserImage { 
width: 224px;
height: 224px;
background-repeat: no-repeat !important;
background-size: cover !important;
border-radius: 50%;
display: flex;
opacity:0.5;
}


.UserProfileImage:focus>.EditUserImage,
.UserProfileImage:active>.EditUserImage{
    z-index: 1;
    box-shadow: 0px 0px 24px #981C15;
     border: 3px solid #981C15; 
    outline: unset;
    border-radius: 50%;
    padding: 10px;
    width: 272px !important;
    height: 272px !important;
    opacity: 1;
    transform: scale(1);
}
.Penicon_img{
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 22px;
    height: 224px;
    width: 224px;
}
.UserProfileImage:focus>.Penicon_img{
    position: absolute;
    bottom: 12rem;
    left: 3rem;
}

