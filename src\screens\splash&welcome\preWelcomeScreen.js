import React, { useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import './Splash.scss'
import Lottie from 'react-lottie-player'
import animationData from '../../json/animationData.json'
import { useDispatch, useSelector } from 'react-redux'
import { COMMON_URL } from '../../utils/environment'
import {
	getPage,
	getPageKids,
	getUrls,
	getUrlsKids
} from '../../store/slices/HomeSlice'
import { pushScreenViewEvent } from '../../GoogleAnalytics'

const preWelcomeScreen = () => {
	const navigate = useNavigate()
	const dispatch = useDispatch()
	const { state } = useLocation()

	const navData = useSelector(state => state?.homeReducer?.navbarData)
	const navaDataKids = useSelector(state => state?.homeReducer?.navbarKidsData)
	const startheaderinfo = useSelector(state => state?.initialReducer?.startHeaderInfo?.response)
	const loginData = useSelector(state => state?.login?.loginSuccess?.response)
	const registerSuccess = useSelector(state => state?.login?.registerSuccess?.response)
	const homePageData = useSelector(state => state?.homeReducer?.pageData)
	const homePageKidsData = useSelector(state => state?.homeReducer?.pageKidsData)

	const userId = loginData?.user_id ?? registerSuccess?.user_id ?? localStorage.getItem('loginId')


	useEffect(() => {
		if (navData?.length != undefined) {
			var levelUrlsData = []
			var sortArray = []
			const navPromise = new Promise((resolve, reject) => {
				const navPromises = []
				navData?.map((navValue, index) => {
					if (navValue.code !== undefined) {
						const promise = fetch(
							`${COMMON_URL.BASE_URL}/services/cms/v1/level?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&node=${navValue?.code}&region=${startheaderinfo?.region}&user_id=${userId}`
						).then(async data => {
							let value = await data.json()
							levelUrlsData.push({
								code: navValue?.code,
								page: navValue?.page,
								content: value?.response?.modules?.module,
								menu: navValue?.menu,
								index: index
							})
						})
						navPromises.push(promise)
					}
				})
				Promise.all(navPromises).then(() => {
					setTimeout(() => {
						sortArray = [...levelUrlsData].sort((a, b) => a.index - b.index)
						resolve(sortArray)
					}, 500)
				})
			})
			navPromise.then(val => {
				dispatch(getPage(val))
			})
		}
	}, [navData, userId])


	useEffect(() => {
		if (navaDataKids.length != undefined) {
			var levelKidsUrlsData = []
			var sortArrayKids = []
			const navKidsPromise = new Promise((resolve, reject) => {
				const navPromises = []
				navaDataKids?.map((navValue, index) => {
					if (navValue.code !== undefined) {
						const promise = fetch(
							`${COMMON_URL.BASE_URL}/services/cms/v1/level?authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_type=${COMMON_URL.device_type}&format=json&node=${navValue?.code}&region=${startheaderinfo?.region}&user_id=${userId}&type=kids`
						).then(async data => {
							let value = await data.json()
							levelKidsUrlsData.push({
								code: navValue?.code,
								page: navValue?.page,
								content: value?.response?.modules?.module,
								menu: navValue?.menu,
								index: index
							})
						})
						navPromises.push(promise)
					}
				})
				Promise.all(navPromises).then(() => {
					setTimeout(() => {
						sortArrayKids = [...levelKidsUrlsData].sort(
							(a, b) => a.index - b.index
						)
						resolve(sortArrayKids)
					}, 500)
				})
			})
			navKidsPromise.then(val => {
				dispatch(getPageKids(val))
			})
		}
	}, [navaDataKids, userId])

	//Fetching corresponding above CMS and CMS Kids url value from Reducer

	const homePageContent = homePageData?.map(each => ({
		page: each?.page,
		code: each?.code,
		contentData: each?.content
	}))

	const homePageComponents = homePageContent?.map(each => ({
		page: each?.page,
		code: each?.code,
		content: each?.contentData?.map(content => content?.components?.component)
	}))

	const homePageContentKids = homePageKidsData?.map(each => ({
		page: each?.page,
		code: each?.code,
		contentData: each?.content
	}))

	const homePageComponentsKids = homePageContentKids?.map(each => ({
		page: each?.page,
		code: each?.code,
		content: each?.contentData?.map(content => content?.components?.component)
	}))

	const filteringData = []
	const filteringDataKids = []
	const levelApiUrlsData = []
	const levelApiUrlsDataKids = []

	homePageComponents?.map(each =>
		filteringData.push({
			page: each?.page,
			code: each?.code,
			railsData: each?.content?.map(railsData =>
				railsData
					?.map((data, index) =>
						data?.properties?.large
							? {
								urls: railsData[index + 1]?.properties?.url,
								type: railsData[index + 1]?.type,
								title: data?.properties?.large
							}
							: data?.type === 'Highlight'
								? {
									urls: railsData[index]?.properties?.url,
									type: data?.type
								}
								: data?.type === 'tooncharacter'
									? {
										urls: railsData[index]?.properties?.url,
										type: data?.type
									}
									: null
					)
					.filter(
						(element, currentIndex, array) =>
							array.findIndex(
								toonItem => toonItem?.type === 'tooncharacter'
							) === currentIndex || element?.type !== 'tooncharacter'
					)
			)
		})
	)

	filteringData?.map(each =>
		levelApiUrlsData.push({
			page: each?.page,
			code: each?.code,
			railsData: each?.railsData?.map(railsData =>
				railsData?.filter(data => data !== null)
			)
		})
	)

	homePageComponentsKids?.map(each =>
		filteringDataKids.push({
			page: each?.page,
			code: each?.code,
			railsData: each?.content?.map(railsData =>
				railsData?.map((data, index) =>
					data?.properties?.large
						? {
							urls: railsData[index + 1]?.properties?.url,
							type: railsData[index + 1]?.type,
							title: data?.properties?.large
						}
						: data?.type === 'Highlight'
							? {
								urls: railsData[index]?.properties?.url,
								type: data?.type
							}
							: null
				)
			)
		})
	)

	filteringDataKids?.map(each =>
		levelApiUrlsDataKids.push({
			page: each?.page,
			code: each?.code,
			railsData: each?.railsData?.map(railsData =>
				railsData?.filter(data => data !== null)
			)
		})
	)

	useEffect(() => {
		if (
			levelApiUrlsData?.length == navData?.length &&
			levelApiUrlsDataKids?.length == navaDataKids?.length
		) {
			dispatch(getUrls(levelApiUrlsData)),
				dispatch(getUrlsKids(levelApiUrlsDataKids)),
				navigate('/welcome', { 
					state: { 
						confirmscreen: state?.confirmscreen, 
						seriesEpisodeData: state?.seriesEpisodeData, 
						page: state?.page,
						pageName: state?.pageName,
						fromDetailsPage: state?.fromDetailsPage
					} 
				})
		}
	}, [levelApiUrlsData, levelApiUrlsDataKids])

	useEffect(()=>{
	pushScreenViewEvent({screenName:'prewelcome_screen', screenData: loginData ?? registerSuccess, prevScreenName: state?.pageName ?? state?.gaPageName, })
	},[])

	return (
		<div className="container-splash">
			<img className="splash" src="images/claro-video-logo.png" />
			<Lottie loop animationData={animationData} play />
		</div>
	)
}
export default React.memo(preWelcomeScreen)