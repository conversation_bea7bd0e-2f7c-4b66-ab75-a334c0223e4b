$display-flex: flex;
$position-center: center;

.LtvButtonLayout {
  margin-left: 2px;
  margin-top: 49px;
  justify-content: space-between;
  width: 1060px;
  display: $display-flex;
}

.featurePastButtonLayout {
  margin-left: 2px;
  margin-top: 49px;
  justify-content: space-between;
  width: 670px;
  display: $display-flex;
}
.LtvButtonImage {
  display: block;
  padding: 10px;
  margin: 0 auto;
  opacity: 1;
  width: 88px;
  height: 88px;
  background: transparent 0% 0% no-repeat padding-box;
}
.LtvButtonTextFocusOut {
  text-align: $position-center;
  font: normal normal bolder 22px/48px Roboto;
  letter-spacing: 0px;
  color: #7f8282;
  text-transform: uppercase;
  opacity: 1;
  margin: 0;
  cursor: pointer;
}
.LtvButtonText {
  text-align: $position-center;
  font: normal normal bolder 22px/48px Roboto;
  letter-spacing: 0.7px;
  color: #ffffff;
  text-transform: uppercase;
  opacity: 1;
  margin: 0;
  cursor: pointer;
}
.LtvC<PERSON><PERSON><PERSON><PERSON> {
  height: 60px;
  /* UI Properties */
  font: var(--unnamed-font-style-normal) normal 32px/39px
    var(--unnamed-font-family-roboto);
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: $position-center;
  font: normal normal medium 32px/39px Roboto;
  letter-spacing: 0px;
  color: #dddddd;
  text-transform: uppercase;
  opacity: 1;
  margin-top: 17px;
  /* margin-left: 20px; */
  font-size: 40px;
}
.LtvprogressBarPosition {
  width: 1080px;
  margin-top: 10px;
  margin-left: auto;
  margin-right: 846px;
  margin-bottom: 24px;
}

.image-wrapper:first-of-type,
.image-wrapper:nth-child(1) {
  float: left;
}

.image-wrapper:last-of-type,
.image-wrapper:nth-child(2) {
  float: right;
}

.image-wrapper {
  max-width: 50%;
  display: $display-flex;
  margin-right: 30px;
}

.scale-image {
  width: 140px;
  height: 70px;
}

.button-container-div {
  text-align: left;
  opacity: 1;
  /* width: 1820px; */
  height: 1020px;
  background: #000000c9
    linear-gradient(90deg, #121212 0%, #121212f2 27%, #03151f00 100%) 0% 0%
    no-repeat padding-box;
  position: relative;
  padding: 10px 10px 10px 60px;
  top: 0px;
  z-index: 1;

  width: 1850px;
  /* height: 300px; */
  /* border: 1px solid #ff8899; */
  display: $display-flex;
  justify-content: $position-center;
  align-items: $position-center;
}

.RecWarningTitle {
  font: normal normal normal 36px/30px Roboto;
  letter-spacing: 0px;
  color: #eeeeee;
  opacity: 1;
  justify-content: $position-center;
  align-items: $position-center;
  display: $display-flex;
  text-align: $position-center;
  margin-bottom: 80px;
}

.RecWarningTitleButton {
  justify-content: $position-center;
  background: #2e303d 0% 0% no-repeat padding-box;
  border-radius: 44px;
  opacity: 1;
  width: 364px;
  height: 82px;
  margin-left: 40px;
}

.RecWarningTitleButtonContents {
  font: normal normal normal 34px/40px Roboto;
  letter-spacing: 0px;
  color: #eeeeee;
  opacity: 1;
  margin-top: 3%;
  margin-left: 78px;
}

.RecWarningTitleButton:focus {
  background: #981c15 0% 0% no-repeat padding-box;
}

.ReminderContainer {
  width: 1416px;
  height: 363px;
  background: #1a1a1a 0% 0% no-repeat padding-box;
  border: 2px solid #50595d;
  border-radius: 15px;
  opacity: 1;
  margin-top: 400px;
}

.ReminderTitle {
  font: normal normal normal 42px/57px Roboto;
  letter-spacing: 0px;
  color: #eeeeee;
  opacity: 1;
  display: $display-flex;
  text-align: $position-center;
  margin-bottom: 0px;
  text-align: left;
  margin-left: 221px;
  margin-top: -81px;
}

.ReminderDesc {
  width: 955px;
  height: 90px;
  /* UI Properties */
  opacity: 1;
}

.reminderImg {
  width: 88px;
  margin-left: 99px;
  margin-top: 26px;
}

.ReminderButtonTitle {
  font: normal normal normal 34px/40px Roboto;
  letter-spacing: 0px;
  color: #eeeeee;
  opacity: 1;
  margin-top: 3%;
  margin-left: 65px;
}

.ReminderTitleButton {
  justify-content: $position-center;
  background: #2e303d 0% 0% no-repeat padding-box;
  border-radius: 44px;
  opacity: 1;
  width: 364px;
  height: 82px;
  margin-left: 40px;
}

.ReminderTitleButton:focus {
  background: #981c15 0% 0% no-repeat padding-box;
}

.shortcut-bar {
  position: fixed;
  height: 99px;
  width: 1840px;
  opacity: 0.6;
  border-radius: 12px 12px 0 0;
  background-color: #000000;
  display: $display-flex;
  justify-content: flex-end;
  bottom: 600px;
}

.shortcut-bar-text {
  height: 28px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 24px;
  letter-spacing: 0;
  line-height: 26px;
  margin: 0 20px;
  opacity: 1;
}

.epg-shortcut-bar {
  display: $display-flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: $position-center;
}

.shortcut-bar-icon {
  height: 20px;
  width: 20px;
  border-radius: 10px;
}

.shortcut-bar-icon-red {
  background-color: #f80000;
}

.shortcut-bar-icon-blue {
  background-color: #008eff;
}

.shortcut-bar-icon-green {
  background-color: #00af1d;
}

.shortcut-bar-icon-yellow {
  background-color: #ffe100;
}
