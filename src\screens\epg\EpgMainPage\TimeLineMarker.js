import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { URL, COMMON_URL } from '../../../utils/environment'
import { getEpgChannel } from '../../../store/slices/EpgSlice'
import '../../../styles/TimeLineMarker.scss'
import './EpgGrid.scss'

export function DateMarker(props) {
  const dispatch = useDispatch()
  const [date, setDate] = useState(
    `${
      new Date().getHours() < 10
        ? '0' + new Date().getHours()
        : new Date().getHours()
    }.` +
      `${
        new Date().getMinutes() < 10
          ? '0' + new Date().getMinutes()
          : new Date().getMinutes()
      }hs.`
  )

  const [secPos, setSecPos] = useState(0)
  useEffect(() => {
    const intervalId = setInterval(() => {
      let hour =
        new Date().getHours() < 10
          ? '0' + new Date().getHours()
          : new Date().getHours()
      let minute =
        new Date().getMinutes() < 10
          ? '0' + new Date().getMinutes()
          : new Date().getMinutes()

      setDate(`${hour}.${minute}hs.`)
      setSecPos(new Date().getSeconds())
      //Condition to refetch the data in EPG by 00:00 AM
      if (hour == '00' && minute == '00') {
        addChannelData()
      }
    }, 5000)
    return () => clearInterval(intervalId)
  }, [])

  const markerDatePosition = useCallback(() => {
    return `${
      new Date().getSeconds() * (21.233 / 60) +
      new Date().getMinutes() * 21.233 +
      220 +
      new Date().getHours() * 2 * 637
    }px`
  }, [secPos])

  function addChannelData() {
    let region = localStorage.getItem('region')
    let menuId = sessionStorage.getItem('nodeId')
    let epgVersion = sessionStorage.getItem('epgVersion')
    let soaVersion = sessionStorage.getItem('soaVersion')

    var sevenDayEpgData = []

    const epgDataPromise = new Promise(async (resolve, reject) => {
      const fetchPromises = []

      for (let i = 1; i <= 7; i++) {
        let startDate = getNextDays(i - 2)
        let endDate = getNextDays(i - 1)
        let formattedStartDate = `${startDate.getFullYear()}${
          startDate.getMonth() + 1 < 10
            ? '0' + (startDate.getMonth() + 1)
            : startDate.getMonth() + 1
        }${
          startDate.getDate() < 10
            ? '0' + startDate.getDate()
            : startDate.getDate()
        }00${'00'}00`

        let formattedEndDate = `${endDate.getFullYear()}${
          endDate.getMonth() + 1 < 10
            ? '0' + (endDate.getMonth() + 1)
            : endDate.getMonth() + 1
        }${
          endDate.getDate() < 10 ? '0' + endDate.getDate() : endDate.getDate()
        }23${'00'}00`

        const sevenDaysDataPromise = fetch(
          URL.EPG_CHANNEL_URL +
            `&region=${region}&device_id=${COMMON_URL.device_id}&format=json&quantity=2000&from=0` +
            `&date_from=${formattedStartDate}&date_to=${formattedEndDate}&node_id=${menuId}&epg_version=${epgVersion}&metaData=full&soaVersion=${soaVersion}`
        ).then(async data => {
          let value = await data.json()
          sevenDayEpgData.push({
            key: i,
            channelResponse: value?.response?.channels,
            index: i
          })
        })

        fetchPromises.push(sevenDaysDataPromise)
      }

      await Promise.all(fetchPromises)
        .then(() => {
          var sortedEpgData = []
          sortedEpgData = [...sevenDayEpgData].sort((a, b) => a.index - b.index)
          setTimeout(() => {
            resolve(dispatch(getEpgChannel(sortedEpgData)))
          }, 1000)
        })
        .catch(reject)
    })
  }

  const getNextDays = (daysToAdd, currentDate = new Date()) => {
    const nextDate = new Date(currentDate)
    nextDate.setDate(currentDate.getDate() + daysToAdd)
    return nextDate
  }

  return (
    <>
      <div
        className="tooltip"
        style={{ left: markerDatePosition(), scrollBehavior: 'smooth' }}
      >
        <span className="arrow-point"></span>
        <div className="tooltiptext">
          <span className='date-text'>{date}</span>
        </div>
      </div>
    </>
  )
}
