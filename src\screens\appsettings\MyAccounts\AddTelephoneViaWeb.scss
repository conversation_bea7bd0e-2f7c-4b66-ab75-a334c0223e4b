body {
  margin: 0px;
}

.land-line-add-number-via-web {
  width: var(--maxWidth);
  height: var(--maxHeight);
  display: flex;
  flex-direction: column;

  .upper-back-button-area {
    float: right;
    margin-right: 40px;
    margin-top: 30px;

    .upper-button-back {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0px 24px;
      // justify-content: space-between;
      border-radius: 6.6px;
      background-color: #2e303d;
      color: #ffffff;
      font-family: Roboto;
      font-size: 29.04px;
      font-weight: bold;
      float: right;
      width: 292px;

      &:focus {
        background-color: #c60000;
      }

      .yellow-indicator-back {
        height: 20px;
        width: 20px;
        margin-left: 20px;
      }

      .image-back {
        height: 24px;
        width: 30px;
        margin-left: 20px;
      }

      .text-back {
        margin-left: 20px;
      }
    }
  }

  .logo-image {
    margin-top: 20px;
    margin-left: 60px;
    opacity: 1;
    width: 246px;
    height: auto;
  }

  .icon-info-button {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;

    .icon-div {
      border-radius: 50px;
      width: 50px;
      background: white;
      color: black;
      font-size: 40px;
      padding: 10px 10px 10px 10px;
      text-align: center;
      margin-bottom: 6vh;
      margin-top: 10vh;
    }

    .landline-text-desc {
      color: #ffffff;
      font-family: Roboto;
      font-size: 36px;
      text-align: center;
    }

    .add-telephone-via-web-buttons-area {
      margin-top: 10vh;

      .landline-finish-subscription-btn {
        height: 72px;
        width: 720px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #2e303d;
        border-radius: 6px;

        &:focus {
          background-color: #981c15;
        }
      }

      .option-button-close-text {
        color: #ffffff;
        font-family: Roboto;
        font-size: 32px;
        font-weight: bold;
      }
    }
  }
}

//Change Payment Confirmation Screen
.app-css-confirmation-screen {
  width: 1920px;
  height: 1080px;
  position: fixed;
  background: #121212;

  .confirmation-screen-logo {
    margin-top: 67px;
    margin-left: 90px;
  }

  .confirmation-screen-main-div {
    height: 936px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .green-tick-img {
      height: 73px;
      width: 73px;
    }

    .confirmation-screen-text {
      height: 42px;
      width: 451px;
      color: #eeeeee;
      font-family: Roboto;
      font-size: 36px;
      letter-spacing: 0;
      line-height: 42px;
      text-align: center;
      margin-top: 29px;
    }

    .confirmation-screen-acceptar-button {
      height: 82.08px;
      width: 821.37px;
      border-radius: 10.03px;
      background-color: #981c15;
      color: #ffffff;
      font-family: Roboto;
      font-size: 36.5px;
      font-weight: bold;
      letter-spacing: -0.58px;
      line-height: 42.18px;
      display: flex;
      justify-content: center;
      text-align: center;
      align-items: center;
      margin-top: 150px;
    }
  }
}
