
.app-css{
	width: 1920px;
	height: 1080px;
	position: fixed;
	background: radial-gradient(circle, #404040 0%, #363636 10.26%, #121212 100%, #121212 100%);

	.back-button-view-detail {
		display: flex;
        align-items: center;
        height: 62px;
        width: 306px;
        border-radius: 6.6px;
        background-color: #2e303d;
		margin-left: 50px;
		margin-top: 50px;
        box-sizing: border-box;
        border: none;
        outline: none;

		&:focus {
			border: 3px solid #ffffff;
            border-radius: 14px;
            outline: none;
		}

		.yellow-indicator {
			height: 20px;
            width: 20px;
            margin-right: 24px;
                  margin-left: 24px;
            flex-shrink: 0;
		}

		.back-image {
			height: 24px;
            width: 30px;
            margin-right: 24px;
            flex-shrink: 0;
		}

		.back-text {
			color: #ffffff;
            font-family: Roboto, sans-serif;
            font-size: 29.04px;
            font-weight: bold;
            line-height: 30px;
            flex-shrink: 0;
            margin: 0;
		}
	}

	.view-details-main-div {
		display: flex;
		margin-left: 60px;


		.left-div-view-details{
			display: flex;
			flex-direction: column;
			margin-top: 70px;

			.image-logo{
				margin-left: 25px;
				width: auto;
				height: auto;
				max-width: 288px;
				max-height: 88px;
				object-fit: contain;
			}
		}

		.view-details-text{
			margin-left: 25px;
			text-align: left;
			color: #ffffff;
			margin-top: 20px;
			text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.20);
			font-family: Roboto;
			font-size: 28px;
			font-style: normal;
			font-weight: 400;
			line-height: 32px; /* 133.333% */
		}

		.view-probar-addon-description1 {
			width: 509px;
		}

		.view-probar-addon-description2 {
			width: 509px;
			height: 96px;
			flex-shrink: 0;
		}

		.view-details-container {
			display: flex;
			flex-direction: column;
		}

		.disfruta {
		  color: #00a9ff;
		}

		.view-details-pricing{
			height: 57px;
			margin-left: 25px;
			text-align: left;
			font-family: Roboto;
			color: #ffffff;
			margin-top: 96px;
			text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.20);
			font-family: Roboto;
			font-size: 24px;
			font-style: normal;
			font-weight: 400;
			line-height: 32px; /* 133.333% */

			.price-title {
				margin-left: 2px;
			}
			.slash-line, .periodicity, .tax-label {
				margin-left: 8px;
			}
		}
		.subs-button-view-screen{
			width: 501px;
			height: 69px;
			background: #2e303d 0% 0% no-repeat padding-box;
			border-radius: 6.6px;
			opacity: 1;
			text-align: center;
			font: normal normal bold 28px/48px Roboto;
			letter-spacing: 0px;
			color: #ffffff;
			opacity: 1;
			margin-top: 11px;

			&:focus {
				background: #981c15 0% 0% no-repeat padding-box;
				transform: scale(1.1);
			}
		}

		.view-freestr-text{
			text-align: left;
			letter-spacing: 0px;
                  	margin-left: 25px;
			color: #fff000;
			text-shadow:  0 1px 3px 0 rgba(0,0,0,0.2);
			font-size: 32px;
			font-family: Roboto;
			opacity: 1;
			margin-top: -18px;
		}

		.view-details-description{
			width: 698px;
			height: 71px;
			line-height: 23px;
			margin-left: 25px;
			text-align: left;
			letter-spacing: 0px;
			color: #ffffff;
			text-shadow: 0 1px 3px 0 rgba(0,0,0,0.2);
			font: normal normal normal 28px/48px Roboto;
			opacity: 0.7;
			margin-top: 12px;
		}

		.right-div-view-details{
			display: flex;
			flex-direction: column;
			grid-gap: 17.11px;
			width: 759px;
			margin-left: 144px;

			.horizontal_topimg{
				width: 759px;
				height: 213.934px;
				}

			.right-second-div-first-card,
			.right-second-div-second-card{
				margin-bottom: 21.39px;
			}
		}

		.right-div-wrapper{
			display: flex;
			
			.right-second-div-view-details{
				margin-right: 24.18px;
				.each-landscape-banner-image{
					border:none;
					outline: none;
					width: 331.132px;
					height: 186.96px;
				}
				.each-landscape-banner-image,a{
					border:none;
					outline: none;
					}
		    }
				.each-banner-image {
					width: 403.684px;
					height: 611.596px;
				}
		}
	}

}