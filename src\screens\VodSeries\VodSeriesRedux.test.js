import vodSeriesSlice, {
  vodSeries,
  vodSeriesDataSuccess,
  vodSeriesDataError,
  vodSeriesCast,
  vodSeriesCastSuccess,
  vodSeriesCastError,
  vodSeriesMLT,
  vodSeriesMLTSuccess,
  vodSeriesMLTError
} from '../../store/slices/vodSeriesSlice'

import vodWatchListSlice, {
  getWatchList,
  getWatchListSuccess,
  getWatchListError,
  addWatchList,
  addWatchListSuccess,
  addWatchListError,
  delWatchList,
  delWatchListSuccess,
  delWatchListError
} from '../../store/slices/getWatchListSlice'

describe('MyVodSeriesData reducer', () => {
  const state = {
    seriesData: [],
    isLoading: true,
    error: {},
    seriesCastData: [],
    isSeriesCastLoading: true,
    seriesCastError: {},
    seriesMLTData: [],
    isSeriesMLTLoading: true,
    seriesMLTError: {}
  }

  const getmycontent = {
    id: '933171',
    title: 'La Otra Cara de La Guerra',
    title_episode: null,
    description:
      'Arturs se enlista en el ejército para vengar la muerte de su madre en manos de los alemanes.',
    description_large:
      'Después de presenciar el disparo de su madre por las tropas alemanas invasoras, Arturs, de 16 años, decide enlistarse en los batallones nacionales de fusileros letones del ejército imperial ruso, con la esperanza de vengarse y encontrar la gloria.',
    is_series: true,
    channel_number: null,
    status: '0',
    msg: 'OK'
  }

  const getmycontentFailure = {
    response: null,
    status: '1',
    msg: 'ERROR',
    errors: {
      error: ['Invalid Argument on field: error_params_group_id'],
      code: 'error_params'
    }
  }

  it('should handle initial state', () => {
    const initialState = state
    const action = { type: 'unknown' }
    const expectedState = initialState
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle vodSeriesData', () => {
    const initialState = { ...state, data: { id: '1234' } }
    const action = vodSeries({ id: '1234' })
    const expectedState = { ...state, data: { id: '1234' } }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle vodSeriesDataSuccess', () => {
    const payload = {
      data: getmycontent
    }
    const initialState = { ...state, data: payload }
    const action = vodSeriesSlice(
      initialState,
      vodSeriesDataSuccess(payload, { getmycontent })
    )
    const expectedState = { ...state, data: payload }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle vodSeriesDataError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = vodSeriesSlice(
      initialState,
      vodSeriesDataError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle VodSeriesCast', () => {
    const initialState = { ...state, castdata: { id: '1234' } }
    const action = vodSeriesCast({ id: '1234' })
    const expectedState = { ...state, castdata: { id: '1234' } }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle VodSeriesCastSuccess', () => {
    const payload = {
      data: getmycontent
    }
    const initialState = { ...state, data: payload }
    const action = vodSeriesSlice(
      initialState,
      vodSeriesCastSuccess(payload, { getmycontent })
    )
    const expectedState = { ...state, data: payload }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle VodSeriesCastError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = vodSeriesSlice(
      initialState,
      vodSeriesCastError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle VodSeriesMLT', () => {
    const initialState = { ...state, seriesMLTData: { id: '1234' } }
    const action = vodSeriesMLT({ id: '1234' })
    const expectedState = { ...state, seriesMLTData: { id: '1234' } }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle VodSeriesMLTSuccess', () => {
    const payload = {
      data: getmycontent
    }
    const initialState = { ...state, data: payload }
    const action = vodSeriesSlice(
      initialState,
      vodSeriesMLTSuccess(payload, { getmycontent })
    )
    const expectedState = { ...state, data: payload }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle VodSeriesMLTError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = vodSeriesSlice(
      initialState,
      vodSeriesMLTError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(vodSeriesSlice(initialState, action)).toEqual(expectedState)
  })
})

describe('VodWatchListData reducer', () => {
  const state = {
    watchList: [],
    addWatchList: {},
    delWatchList: {},
    isGetWatchlistLoading: true,
    isAddWatchlistLoading: true,
    isDelWatchlistLoading: true,
    getWatchlistError: {},
    addWatchlistError: {},
    delWatchlistError: {}
  }

  const getmycontent = {
    id: '933171',
    title: 'La Otra Cara de La Guerra',
    title_episode: null,
    description:
      'Arturs se enlista en el ejército para vengar la muerte de su madre en manos de los alemanes.',
    description_large:
      'Después de presenciar el disparo de su madre por las tropas alemanas invasoras, Arturs, de 16 años, decide enlistarse en los batallones nacionales de fusileros letones del ejército imperial ruso, con la esperanza de vengarse y encontrar la gloria.',
    is_series: true,
    channel_number: null,
    status: '0',
    msg: 'OK'
  }

  const watchListContent = {
    status: '200',
    msg: 'OK'
  }

  const getmycontentFailure = {
    response: null,
    status: '1',
    msg: 'ERROR',
    errors: {
      error: ['error_params_user'],
      code: 'error_params'
    }
  }

  it('should handle initial state', () => {
    const initialState = state
    const action = { type: 'unknown' }
    const expectedState = initialState
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getWatchListData', () => {
    const initialState = { ...state, data: { id: '1234' } }
    const action = getWatchList({ id: '1234' })
    const expectedState = { ...state, data: { id: '1234' } }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getWatchListSuccess', () => {
    const payload = {
      data: getmycontent
    }
    const initialState = { ...state, data: payload }
    const action = vodWatchListSlice(
      initialState,
      getWatchListSuccess(payload, { getmycontent })
    )
    const expectedState = { ...state, data: payload }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle getWatchListError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = vodWatchListSlice(
      initialState,
      getWatchListError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle AddWatchList', () => {
    const initialState = { ...state, castdata: { id: '1234' } }
    const action = addWatchList({ id: '1234' })
    const expectedState = { ...state, castdata: { id: '1234' } }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle AddWatchListSuccess', () => {
    const payload = {
      data: watchListContent
    }
    const initialState = { ...state, data: payload }
    const action = vodWatchListSlice(
      initialState,
      addWatchListSuccess(payload, { watchListContent })
    )
    const expectedState = { ...state, data: payload }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle AddWatchListError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = vodWatchListSlice(
      initialState,
      addWatchListError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle DelWatchList', () => {
    const initialState = { ...state, seriesMLTData: { id: '1234' } }
    const action = delWatchList({ id: '1234' })
    const expectedState = { ...state, seriesMLTData: { id: '1234' } }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle DelWatchListSuccess', () => {
    const payload = {
      data: watchListContent
    }
    const initialState = { ...state, data: payload }
    const action = vodWatchListSlice(
      initialState,
      delWatchListSuccess(payload, { watchListContent })
    )
    const expectedState = { ...state, data: payload }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })

  it('should handle DelWatchListError', () => {
    const errorData = {
      data: getmycontentFailure
    }
    const initialState = { ...state, error: errorData }
    const action = vodWatchListSlice(
      initialState,
      delWatchListError(errorData, { getmycontentFailure })
    )
    const expectedState = { ...state, error: errorData }
    expect(vodWatchListSlice(initialState, action)).toEqual(expectedState)
  })
})
