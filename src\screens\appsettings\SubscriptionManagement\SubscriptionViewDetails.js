import React, { useState, useEffect, useCallback } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  getCMSflowforViewSubs,
  getViewSubscribeData
} from '../../../store/slices/settingsSlice'
import './subscribtionViewdetails.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { COMMON_URL } from '../../../utils/environment'

const SubscriptionViewDetails = props => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()
  const region = localStorage.getItem('region')

  const [destacadosData, setDestacadosData] = useState(null)
  const [imagesData, setImagesData] = useState(null)

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const viewData = useSelector(
    state => state?.settingsReducer?.viewSubscribeData
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const mosaicImagesModules = useSelector(
    state => state?.settingsReducer?.cmsViewSub?.response?.modules?.module
  )
  const watchFree = useSelector(state => state?.login?.watchFreestate)
  const vodSeriesCastRedux = useSelector(
    state => state?.getVodSeries?.seriesCastData
  )

  const destacadosUrls = []
  const mosaicImagesUrls = []

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]

  const mosaicComponents = mosaicImagesModules?.map(
    each => each?.components?.component
  )

  mosaicComponents?.map(url => {
    url.map(each =>
      each.type === 'Highlight'
        ? destacadosUrls?.push({
            urlData: each?.properties?.url,
            type: each?.type
          })
        : each.type === 'Carrouselvertical'
        ? mosaicImagesUrls?.push({
            urlData: each?.properties?.url,
            type: each?.type
          })
        : null
    )
  })

  const handleSubscribe = e => {
    dispatch(getViewSubscribeData(viewData))
    if (
      watchFree &&
      vodSeriesCastRedux?.common?.extendedcommon?.format?.types !==
        'free,download' &&
      vodSeriesCastRedux?.common?.extendedcommon?.format?.types !== 'free'
    ) {
      navigate('/EPconfirmation', {
        state: {
          seriesEpisodeData:
            vodSeriesCastRedux?.common ??
            state?.dataId ??
            state?.seriesEpisodeData ??
            state?.vodMoviesData ??
            state?.vodData,
          page: 'details',
          pageName: state?.pageName,
          fromDetailsPage: true
        }
      })
    } else if (state?.pageName === '/multiSubscription') {
      const sendData = state?.data ?? state?.dataId
      const sendIndex = state?.sendIndex
      navigate(
        '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
        {
          state: {
            pageName:
              '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
            data: sendData,
            sendIndex: sendIndex,
            previousPage: state?.pageName,
            returnPage: state?.returnPage,
            inputValue: state?.inputValue,
            subscribeInfoData: state?.subscribeInfoData
          }
        }
      )
    } else if (state?.pageName === '/premiumSubscription') {
      navigate(
        '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
        {
          state: {
            pageName:
              '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
            data: state?.data ?? state?.dataId,
            returnPage: state?.returnPage,
            previousPage: state?.pageName,
            inputValue: state?.inputValue,
            subscribeInfoData: state?.subscribeInfoData,
            groupId: state?.groupId
          }
        }
      )
    } else {
      const sendIndex = state?.sendIndex
      navigate(
        '/my-settings/my-subscriptions/add-subscriptions/subscribe-new',
        {
          state: {
            pageName:
              '/my-settings/my-subscriptions/add-subscriptions/viewDetails',
            data: state?.data ?? state?.dataId,
            vodData: state?.vodData,
            previousPage: state?.pageName,
            returnPage: state?.returnPage,
            sendIndex: sendIndex,
            subscribeInfoData: state?.subscribeInfoData,
            focusIndex: state?.focusIndex,
            groupId: state?.groupId
          }
        }
      )
    }
  }

  const fetchApiDestacados = async params => {
    if (params !== undefined) {
      return await fetch(
        `${COMMON_URL?.BASE_URL}${params}&authpn=${COMMON_URL?.authpn}&authpt=${COMMON_URL?.authpt}&device_category=${COMMON_URL?.device_category}&device_manufacturer=${COMMON_URL?.device_manufacturer}&device_model=${COMMON_URL?.device_model}&device_type=${COMMON_URL?.device_type}&format=json`
      ).then(async data => {
        const value = await data.json()
        setDestacadosData(value)
      })
    }
  }
  const fetchApiImages = async params => {
    if (params !== undefined) {
      return await fetch(
        `${COMMON_URL?.BASE_URL}${params}&authpn=${COMMON_URL?.authpn}&authpt=${COMMON_URL?.authpt}&device_category=${COMMON_URL?.device_category}&device_manufacturer=${COMMON_URL?.device_manufacturer}&device_model=${COMMON_URL?.device_model}&device_type=${COMMON_URL?.device_type}&format=json`
      ).then(async data => {
        const value = await data.json()
        setImagesData(value)
      })
    }
  }

  const getDescriptionLabel = data => {
    return viewData?.offertype == 'subscrition'
      ? `${handleTranslationchange(
          `Transaccionales_Paquetes_TextoPromocion1_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_Paquetes_TextoPromocion2_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_Paquetes_TextoPromocion3_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_Paquetes_TextoPromocion4_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_Paquetes_TextoPromocion5_${data}`,
          100
        )}`
      : `${handleTranslationchange(
          `Transaccionales_ProbarAddon_TextoPromocion1_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_ProbarAddon_TextoPromocion2_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_ProbarAddon_TextoPromocion3_${data}`,
          100
        )}`
  }

  const getDescriptionAdditionalInfo = data => {
    return viewData?.offertype != 'subscrition'
      ? `${handleTranslationchange(
          `Transaccionales_ProbarAddon_TextoComplementario1_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_ProbarAddon_TextoComplementario2_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_ProbarAddon_TextoComplementario3_${data}`,
          100
        )}`
      : `${handleTranslationchange(
          `Transaccionales_Paquetes_TextoComplementario1_${data}`,
          100
        )}\n${handleTranslationchange(
          `Transaccionales_Paquetes_TextoComplementario2_${data}`,
          100
        )}\n`
  }

  useEffect(() => {
    fetchApiDestacados(destacadosUrls?.[0]?.urlData),
      fetchApiImages(mosaicImagesUrls?.[0]?.urlData)
  }, [mosaicImagesModules])

  useEffect(() => {
    dispatch(
      getCMSflowforViewSubs({
        tabValue: viewData?.family,
        userId: userDetails?.parent_id
      })
    )
    document.getElementById('subscriptionDetailPage-subs-btn').focus()
  }, [])

  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keyup', keyPressFunc)
    return () => {
      document.removeEventListener('keyup', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      if (state?.fromDetailsPage || state?.page == 'details') {
        if (state?.pageName == '/series') {
          navigate(state?.pageName, {
            state: {
              pageName: '/series',
              dataId: vodSeriesCastRedux?.common ?? state?.seriesEpisodeData,
              data: state?.data ?? state?.dataId ?? state?.seriesEpisodeData
            }
          })
        } else if (state?.pageName == '/movies') {
          navigate(state?.pageName, {
            state: {
              pageName: '/movies',
              vodData:
                state?.seriesEpisodeData ??
                state?.vodMoviesData ??
                state?.vodData,
              vodMoviesData:
                state?.seriesEpisodeData ??
                state?.vodMoviesData ??
                state?.vodData
            }
          })
        }
      } else if (state?.pageName === '/multiSubscription') {
        const sendData = state?.data ?? state?.dataId
        const returnIndex = state?.sendIndex
        navigate('/multiSubscription', {
          state: {
            data: sendData,
            backIndex: returnIndex,
            pageName: state?.returnPage
          }
        })
      } else if (state?.pageName === '/premiumSubscription') {
        navigate('/premiumSubscription', {
          state: {
            data: state?.data,
            subscribeInfoData: state?.subscribeInfoData,
            returnPage: state?.returnPage,
            groupId: state?.groupId
          }
        })
      } else if (state?.pageName === '/home') {
        navigate(state?.pageName, {
          state: {
            focusIndex: state?.focusIndex
          }
        })
      } else {
        navigate(state?.pageName, {
          state: {
            data: state?.data ?? state?.dataId,
            vodData: state?.vodData,
            groupId: state?.groupId
          }
        })
      }
    }
  }

  const handleLgkey = keycode => {
    if (
      keycode === 405 ||
      keycode === 461 ||
((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120) ||
      keycode == 'backClick' ||
      keycode == 8
    ) {
      if (state?.fromDetailsPage || state?.page == 'details') {
        if (state?.pageName == '/series') {
          navigate(state?.pageName, {
            state: {
              pageName: '/series',
              dataId: vodSeriesCastRedux?.common ?? state?.seriesEpisodeData,
              data: state?.data ?? state?.dataId ?? state?.seriesEpisodeData
            }
          })
        } else if (state?.pageName == '/movies') {
          navigate(state?.pageName, {
            state: {
              pageName: '/movies',
              vodData:
                state?.seriesEpisodeData ??
                state?.vodMoviesData ??
                state?.vodData,
              vodMoviesData:
                state?.seriesEpisodeData ??
                state?.vodMoviesData ??
                state?.vodData
            }
          })
        }
      } else if (
        state?.pageName === '/multiSubscription' ||
        state?.returnPage == 'search'
      ) {
        const sendData = state?.data ?? state?.dataId
        const returnIndex = state?.sendIndex
        navigate('/multiSubscription', {
          state: {
            data: sendData,
            sendIndex: returnIndex,
            pageName: state?.returnPage
          }
        })
      } else if (state?.pageName === '/premiumSubscription') {
        navigate('/premiumSubscription', {
          state: {
            data: state?.data ?? state?.dataId,
            subscribeInfoData: state?.subscribeInfoData,
            returnPage: state?.returnPage ?? state?.previousPage,
            groupId: state?.groupId
          }
        })
      } else if (state?.pageName === '/home') {
        navigate(state?.pageName, {
          state: {
            focusIndex: state?.focusIndex,
            data: state?.data ?? state?.dataId,
            vodData: state?.vodData
          }
        })
      } else {
        navigate(state?.pageName, {
          state: {
            data: state?.data ?? state?.dataId,
            vodData: state?.vodData,
            groupId: state?.groupId
          }
        })
      }
    }
  }

  const handleTranslationchange = useCallback(
    (str, length) => {
      const text = apilanguage?.[str] ?? str
      if (!length) {
        length = 100
      }
      if (text?.length >= length) {
        return `${text?.slice(0, length)}...`
      } else {
        return text
      }
    },
    [apilanguage]
  )

  const displayProbarAddOn = () => {
    return (
      <>
        <img
          src={
            apaAssetsImages?.[
              `Transaccionales_ProbarAddon_ImagenLogoCanal_${viewData?.family}`
            ] ||
            apaAssetsImages?.[
              `Transaccionales_ProbarAddon_ImagenLogoCanal_${viewData?.family?.toUpperCase()}`
            ] ||
            viewData?.logo
          }
          className="image-logo"
        />
        <span className="view-details-text view-probar-addon-description1 view-details-container">
          <span>
            {handleTranslationchange(
              `Transaccionales_ProbarAddon_TextoPromocion1_${viewData?.producttype}` ||
                `Transaccionales_ProbarAddon_TextoPromocion1_${viewData?.producttype?.toUpperCase()}`,
              60
            )}
          </span>
          <span>
            {handleTranslationchange(
              `Transaccionales_ProbarAddon_TextoPromocion2_${viewData?.producttype}` ||
                `Transaccionales_ProbarAddon_TextoPromocion2_${viewData?.producttype?.toUpperCase()}`,
              60
            )}
          </span>
          <span>
            {handleTranslationchange(
              `Transaccionales_ProbarAddon_TextoPromocion3_${viewData?.producttype}` ||
                `Transaccionales_ProbarAddon_TextoPromocion3_${viewData?.producttype?.toUpperCase()}`,
              60
            )}
          </span>
        </span>
        <span className="view-details-text">
          <span className="disfruta">{`${handleTranslationchange(
            `Transaccionales_ProbarAddon_TextoOferta1_${viewData?.producttype}` ||
              `Transaccionales_ProbarAddon_TextoOferta1_${viewData?.producttype?.toUpperCase()}`,
            40
          )}`}</span>
          {`${handleTranslationchange(
            `Transaccionales_ProbarAddon_TextoOferta2_${viewData?.producttype}` ||
              `Transaccionales_ProbarAddon_TextoOferta2_${viewData?.producttype?.toUpperCase()}`,
            20
          )}`}
        </span>
        <span className="view-details-pricing view-probar-addon-description2">
          <span className="currency-title">{viewData?.currency}</span>
          <span className="price-title">{viewData?.price}</span>
          <span className="slash-line">
            {handleTranslationchange(
              'Transaccionales_ProbarAddon_TextoDiagonal',
              2
            )}
          </span>
          <span className="periodicity">
            {handleTranslationchange(viewData?.periodicity, 8)}
          </span>
        </span>
        <button
          className="subs-button-view-screen focusable"
          id="subscriptionDetailPage-subs-btn"
          onClick={handleSubscribe}
        >
          {handleTranslationchange(
            `Transaccionales_ProbarAddon_TextoBotonPrimario_${viewData?.producttype}` ||
              `Transaccionales_ProbarAddon_TextoBotonPrimario_${viewData?.producttype?.toUpperCase()}`,
            40
          )}
        </button>
        <span className="view-details-description view-details-container">
          <span>
            {handleTranslationchange(
              `Transaccionales_ProbarAddon_TextoComplementario1_${viewData?.producttype}` ||
                `Transaccionales_ProbarAddon_TextoComplementario1_${viewData?.producttype?.toUpperCase()}`,
              60
            )}
          </span>
          <span>
            {handleTranslationchange(
              `Transaccionales_ProbarAddon_TextoComplementario2_${viewData?.producttype}` ||
                `Transaccionales_ProbarAddon_TextoComplementario2_${viewData?.producttype?.toUpperCase()}`,
              60
            )}
          </span>
          <span>
            {handleTranslationchange(
              `Transaccionales_ProbarAddon_TextoComplementario3_${viewData?.producttype}` ||
                `Transaccionales_ProbarAddon_TextoComplementario3_${viewData?.producttype?.toUpperCase()}`,
              60
            )}
          </span>
        </span>
      </>
    )
  }

  const displayPaquetes = () => {
    return (
      <>
        <img
          src={
            apaAssetsImages?.[
              `Transaccionales_Paquetes_ImagenLogoCanal_${viewData?.family}`
            ] ||
            apaAssetsImages?.[
              `Transaccionales_Paquetes_ImagenLogoCanal_${viewData?.family?.toUpperCase()}`
            ] ||
            viewData?.logo
          }
          className="image-logo"
        />
        <span className="view-details-text">
          <>{`${handleTranslationchange(
            `Transaccionales_Paquetes_TextoPromocion1_${viewData?.producttype}` ||
              `Transaccionales_Paquetes_TextoPromocion1_${viewData?.producttype?.toUpperCase()}`,
            60
          )}`}</>
        </span>
        <span className="view-details-pricing">
          <span className="currency-title">{viewData?.currency}</span>
          <span className="price-title">{viewData?.price}</span>
          <span className="slash-line">
            {handleTranslationchange(
              'Transaccionales_ProbarAddon_TextoDiagonal',
              2
            )}
          </span>
          <span className="periodicity">
            {handleTranslationchange(viewData?.periodicity, 8)}
          </span>
        </span>
        <span className="tax-label">
          {handleTranslationchange(
            `Transaccionales_Paquetes_TextoImpuestos_${viewData?.producttype}` ||
              `Transaccionales_Paquetes_TextoImpuestos_${viewData?.producttype?.toUpperCase()}`,
            15
          )}
        </span>
        <button
          className="subs-button-view-screen focusable"
          id="subscriptionDetailPage-subs-btn"
          onClick={handleSubscribe}
        >
          {handleTranslationchange(
            `Transaccionales_Paquetes_TextoBotonPrimario_${viewData?.producttype}` ||
              `Transaccionales_Paquetes_TextoBotonPrimario_${viewData?.producttype?.toUpperCase()}`,
            40
          )}
        </button>
        <span className="view-details-description">
          {`${handleTranslationchange(
            `Transaccionales_Paquetes_TextoComplementario1_${viewData?.producttype}` ||
              `Transaccionales_Paquetes_TextoComplementario1_${viewData?.producttype?.toUpperCase()}`
          )}\n${handleTranslationchange(
            `Transaccionales_Paquetes_TextoComplementario2_${viewData?.producttype}` ||
              `Transaccionales_Paquetes_TextoComplementario2_${viewData?.producttype?.toUpperCase()}`
          )}`}
        </span>
      </>
    )
  }

  return (
    <div className="app-css subscriptionDetails ">
      <button
        className="back-button-view-detail focusable"
        id="subscriptionDetailPageBack"
        onClick={() => handleLgkey('backClick')}
      >
        {' '}
        <img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
        <img className="back-image" src={'images/back_button.png'} />
        <p className="back-text">
          {handleTranslationchange('BotonShortcut_TextoTitulo', 10)}
        </p>
      </button>
      <div className="view-details-main-div">
        <div className="left-div-view-details">
          {state?.pageName == '/multiSubscription'
            ? displayPaquetes()
            : displayProbarAddOn()}
        </div>
        <div className="right-div-view-details">
          <LazyLoadImage
            className='horizontal_topimg'
            placeholderSrc={'images/horizontal_placeholderimg.png'}
            src={destacadosData?.response?.highlight?.[0]?.image_highlight || ""}
            onError={(e)=>{e.target.src='images/horizontal_placeholderimg.png'}}
                    />
          <div className="right-div-wrapper">
            <div className="right-second-div-view-details">
            <div className="right-second-div-first-card">
              <LazyLoadImage
                className="each-landscape-banner-image"
                placeholderSrc={'/images/landscape_card.png'}
                src={imagesData?.response?.groups?.[0]?.image_base_horizontal || imagesData?.response?.groups?.[0]?.images?.horizontal || ''}
                onError={(e)=>{e.target.src='/images/landscape_card.png'}}
              />
              </div>
              <div className="right-second-div-second-card">
              <LazyLoadImage
                className="each-landscape-banner-image"
                placeholderSrc={'images/landscape_card.png'}
                src={imagesData?.response?.groups?.[1]?.image_base_horizontal || imagesData?.response?.groups?.[1]?.images?.horizontal || ''}
                onError={(e)=>{e.target.src='/images/landscape_card.png'}}
              />
              </div>
              <LazyLoadImage
                className="each-landscape-banner-image"
                placeholderSrc={'images/landscape_card.png'}
                src={imagesData?.response?.groups?.[2]?.image_base_horizontal || imagesData?.response?.groups?.[2]?.images?.horizontal || ''}
                onError={(e)=>{e.target.src='/images/landscape_card.png'}}
              />
            </div>
            <LazyLoadImage
              className="each-banner-image"
              placeholderSrc={'images/vertical_card.png'}
              src={imagesData?.response?.groups?.[0]?.image_base_vertical || imagesData?.response?.groups?.[0]?.images?.vertical || ''}
              onError={(e)=>{e.target.src='images/vertical_card.png'}}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default SubscriptionViewDetails
