$position-relative: relative;
$display-flex: flex;
$color-white: #ffffff;
$font-family-roboto: Roboto;

.new-mini-epg-container {
  top: 730px;
  width: 1777.5px;
  height: 239.5px;
  position: absolute;
  display: $display-flex;
  justify-content: flex-start;
  margin: 0 70px;
  z-index: 3;
}

.epg-chevron-top {
  position: $position-relative;
  top: 0px;
  left: 169.25px;
}

.epg-chevron-left-img {
  height: 39px;
  width: 19.5px;
  position: $position-relative;
  top: 98.25px;
}

.epg-right-chevron {
  height: 39px;
  width: 19.5px;
  position: $position-relative;
  top: 98.25px;
  left: 5.75px;
}

.epg-chevron-top-img,
.epg-chevron-down-img {
  width: 39px;
  height: 19.5px;
  left: 126.5px;
  position: $position-relative;
}

.new-mini-epg-channel-container {
  display: $display-flex;
  flex-direction: column;
  margin-top: 3px;
  width: 291px;
  margin-left: 12.25px;
}

.new-mini-epg-channel {
  height: 150px;
  width: 291px;
  border-radius: 12px;
  background-color: #1a1a1a;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.new-mini-epg-program-container {
  width: 1390px;
  height: 239.5px;
  position: $position-relative;
  display: $display-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  right: 15px;
  color: $color-white;
}



.mini-epg-nav {
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
}

.new-mini-epg-program {
  height: 152px;
  width: 688px;
  border-radius: 12px;
  background-color: #1a1a1a;
  display: $display-flex;
  align-items: center;

  // &:focus,
  // &:focus-within,
  // &:active {
  //   scroll-snap-align: start;
  //   height: 160px;
  //   width: 688px;
  //   position: relative;
  //   top: -9px;
  //   border-radius: 12px;
  //   background-color: #282828;
  //   outline: none;
  //   display: $display-flex;
  //   align-items: center;
  //   padding-bottom: 8px;
  //   outline: none;
  // }

  &:focus,
  &:active {
    scroll-snap-align: start;
    height: 160px;
    width: 688px;
    position: relative;
    top: -9px;
    border-radius: 12px;
    background-color: #282828;
    outline: none;
    display: $display-flex;
    align-items: center;
    padding-bottom: 8px;
    outline: none;

    .new-mini-epg-record-text {
      margin-top: 20px;
    }

    .new-mini-epg-program-record-img {
      margin-right: 0px;
      margin-top: 16.5px;

    }
  }
}

.new-mini-epg-program-image {
  margin-left: 16px;
  margin-top: 16px;
}

.new-mini-epg-program-details {
  display: $display-flex;
  flex-direction: column;
  margin-left: 16px;
  width: 435px;
  position: relative;
  height: 130px;
  justify-content: center;
}

.new-mini-epg-program-title {
  height: 38px;
  width: 386px;
  color: $color-white;
  font-family: $font-family-roboto;
  font-size: 32px;
  // font-weight: 500;
  letter-spacing: 0;
  line-height: 38px;
  margin-top: 20px;
}

.new-mini-epg-program-recording {
  height: 32px;
  color: $color-white;
  font-family: $font-family-roboto;
  font-size: 25px;
  letter-spacing: 0;
  line-height: 32px;
  text-align: right;
  display: $display-flex;
  justify-content: flex-end;
  margin-right: 9.35px;
  position: absolute;
  top: 0;
  right: 0;


}

.new-mini-epg-duration {
  height: 32px;
  display: $display-flex;
  margin-top: 10px;
}

.new-mini-epg-date {
  height: 32px;
  position: $position-relative;
  width: 86px;
  font-size: 28px;
  color: $color-white;
  font-family: $font-family-roboto;
  line-height: 32px;
}

.new-mini-epg-timing {
  height: 32px;
  width: 165px;
  color: $color-white;
  font-family: $font-family-roboto;
  font-size: 28px;
  letter-spacing: 0;
  line-height: 32px;
}

.new-mini-epg-label {
  height: 32px;
  width: 98px;
  position: $position-relative;
  left: 6px;
}

.new-mini-epg-program-record-img {
  height: 38.15px;
  width: 38.15px;
  position: $position-relative;
  margin-left: 8.5px;
  margin-top: 4.57px;
  margin-right: 9.35px;
}

.new-mini-epg-record-text {
  margin-top: 10.27px;
  color: #ffffff;
  font-family: Roboto;
  font-size: 25px;
  letter-spacing: 0.26px;
  line-height: 32px;
  text-align: right;
}

.new-mini-epg-reminder-img {
  position: relative;
  left: 59px;
}

.mini-epg-block-channel {
  .mini-epg-block-icon-image {
    width: 35px;
    height: 46px;
    position: absolute;
    top: 27px;
    right: 26px;
  }
}

.mini-epg-progress-bar {
  height: 5px;
  width: 331px;
  background-color: rgba(153, 153, 153, 0.22);
  position: $position-relative;
  top: 10px;
}

.mini-epg-progress {
  width: 0%;
  height: 100%;
  background-color: #8A0000;
}

.new-mini-epg-channel-number {
  height: 85px;
  color: $color-white;
  font-family: $font-family-roboto;
  font-size: 36px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 45px;
  position: absolute;
  top: 36px;
  left: 5px;
  // margin-top: 22px;
}

.new-mini-epg-channel-image {
  margin-left: 56px;
  // height: 120px;   //future reference need for these values
  // width: 125px;
  // position: $position-relative;
  .channel-img {
    object-fit: contain;
    height: 100%;
  }

}



.new-mini-epg-fav-channel {
  height: 25px;
  width: 21.89px;
  position: absolute;
  top: 88.89px;
  left: 34px;
}

.event-alert-main-epg {
  display: flex;
  position: relative;
  bottom: 181px;
  align-items: center;
  z-index: 101;
  flex-direction: row;
  justify-content: center;

  .event-tooltip {
    display: flex;
    padding: 0px 30px;
    border-radius: 18px;
    height: 80px;
    color: white;
    background: #2E303D;
    font-size: 40px;
    align-items: center;
    margin-right: 15px;

    .alert-text {
      font-size: 36px;
      color: #ffffff;
      font-family: Roboto;
    }
  }
}