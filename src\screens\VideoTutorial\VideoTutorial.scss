.Tutorial-Container {
  position: relative;
  margin-top: 10px;
  margin-left: 3px;
  width: 1900px;
  height: 1060px;

  #btnStart {
    position: absolute;
    bottom: 140px;
    background-color: #981c15;
    width: 214px;
    height: 72px;
    left: 44%;
    border-radius: 44px;
    justify-content: center;
    display: flex;
    align-items: center;
    font-family: Roboto;
    font-size: 26px;
    line-height: 37px;
    color: #ffffff;
  }

  #videoTutorial {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensures it fills the container nicely */
  } 

  #btnSkip {
    position: absolute;
    bottom: 140px;
    background-color: #5c5c5c;
    width: 214px;
    height: 72px;
    left: 44%;
    border-radius: 44px;
    justify-content: center;
    display: flex;
    align-items: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s, background-color 0.5s;
    font-family: Roboto;
    font-size: 26px;
    line-height: 37px;
    color: #ffffff;

    &.visible {
      opacity: 1;
      pointer-events: none;
    }

    &.enabled {
      pointer-events: auto;
      background-color: #981c15;
    }
  }
}
