import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ParentalControlSettings from "./ParentalControlSettings";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <MemoryRouter
      history={history}
      initialEntries={[{ state: { subPage: 'parentalControl' } }]}
    >
      {children}
    </MemoryRouter>
  </Provider>
);

const renderWithState = (ui) => {
  return render(ui, { wrapper: Wrapper });
};

const mockIsLoggedInSuccessResponse = {
  response: {
    session_stringvalue: "ZTEATV412001224226580292a69e33",
    session_userhash: "************************************************************************************************",
    user_id: "123",
    user_token: "token123"
  }
};

const mockCheckPinSuccessResponse = {
  response: {
    profiles: [{
      parental: { active: true }
    }],
    hashed_code: "somehash"
  }
};

const mockStatusControlPin = {
  data: {
    pin_channel: { status: 1 },
    pin_parental: { status: 1 }
  }
};

describe('Parental Control Settings page test', () => {
  beforeEach(() => {
    initialState.login = {
      isLoggedIn: mockIsLoggedInSuccessResponse
    };
    initialState.settingsReducer = {
      controlPin: mockCheckPinSuccessResponse,
      controlPinStatus: mockStatusControlPin,
      remindControlPin: { response: null },
      modifyControlPin: { data: null }
    };
  });

  test('should render without api mock data', () => {
    initialState.login = {
      isLoggedIn: { status: "1" }
    };
    initialState.settingsReducer = {
      controlPin: { status: "1" },
      remindControlPin: { status: "1" }
    };
    renderWithState(<ParentalControlSettings />);
  });

  test('should render with api mock data', () => {
    renderWithState(<ParentalControlSettings />);
  });

  test('navigate to change pin page', () => {
    const { container } = renderWithState(<ParentalControlSettings />);
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'changePin');
    if (buttonClick) {
      fireEvent(
        buttonClick,
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      );
      history.push('/settings/profile-settings/change-pin');
    } else {
      throw new Error('Change Pin button not found');
    }
  });

  test('forget pin click', () => {
    const { container } = renderWithState(<ParentalControlSettings />);
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'forgetPin');
    
    if (buttonClick) {
      fireEvent(
        buttonClick,
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      );
    } else {
      throw new Error('Forget Pin button not found');
    }
  });

  test('navigate to deactivate pin page', () => {
    const { container } = renderWithState(<ParentalControlSettings />);
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'deactivatePin');
    
    if (buttonClick) {
      fireEvent(
        buttonClick,
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      );
      history.push('/settings/profile-settings/deactivate-pin');
    } else {
      throw new Error('Deactivate Pin button not found');
    }
  });

  test('navigate to block channel page', () => {
    const { container } = renderWithState(<ParentalControlSettings />);
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'blockChannelClick');
    
    if (buttonClick) {
      fireEvent(
        buttonClick,
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      );
      history.push('/settings/profile-settings/check-pin');
    } else {
      throw new Error('Block Channel button not found');
    }
  });

  test('navigate to create pin page', () => {
    initialState.settingsReducer.controlPin.response.profiles[0].parental.active = false;
    initialState.settingsReducer.controlPin.response.hashed_code = undefined;
    
    const { container } = renderWithState(<ParentalControlSettings />);
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'createPin');
    
    if (buttonClick) {
      fireEvent.focus(buttonClick);
      fireEvent(
        buttonClick,
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      );
      history.push('/my-settings/help-And-Settings/security-pin/configure');
    } else {
      throw new Error('Create Pin button not found');
    }
  });
  test('navigate to content classification page', () => {
    // Set up the required state
    initialState.settingsReducer = {
      controlPin: {
        response: {
          profiles: [
            {
              parental: {
                active: true
              }
            }
          ],
          hashed_code: 'some-hash' // This will set title to 'pinCreated'
        }
      },
      controlPinStatus: {
        data: {
          pin_parental: {
            status: 1 // This enables the content classification button
          }
        }
      }
    };
    
    initialState.login = {
      isLoggedIn: mockIsLoggedInSuccessResponse
    };

    const { container } = renderWithState(<ParentalControlSettings />);
    const getById = queryByAttribute.bind(null, 'id');
    const buttonClick = getById(container, 'contentClassification');
    
    if (buttonClick) {
      fireEvent(
        buttonClick,
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      );
      history.push('/settings/profile-settings/content-classification');
    } else {
      throw new Error('Content Classification button not found. Make sure hashed_code exists in controlPin response and pin_parental.status is 1.');
    }
});
});