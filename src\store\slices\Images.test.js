import { ImageSlice, getImageSuccess, getImage } from './Images';

describe('ImageSlice reducers', () => {
    it('should handle getImage', () => {
        const initialState = {
            imageresponse: '',
          };
          const mockPayload = ''
          const newState = ImageSlice.reducer(initialState, getImage(mockPayload));
          expect(newState.imageresponse).toBe(mockPayload);
      })
  it('should handle getImageSuccess', () => {
    const initialState = {
      imageresponse: '',
    };
    const mockPayload = {
        "AMCO_est_brasil_wp0": "https://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_brasil_wp0.png?1574457943",
        "AMCO_est_chile_wp0": "https://clarovideocdn6.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_chile_wp0.png?1574457944",
        "AMCO_est_mexico_wp0": "https://clarovideocdn7.clarovideo.net/pregeneracion/cms/apa_andina/3c5b773dc773765539920a1ac3d944fa/AMCO_est_mexico_wp0.png?1574457944",
    }
    const newState = ImageSlice.reducer(initialState, getImageSuccess(mockPayload));
    expect(newState.imageresponse).toBe(mockPayload);
  });
});