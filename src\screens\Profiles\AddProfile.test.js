import React from "react";
import { fireEvent, queryByAttribute, render, screen, act, waitFor } from "@testing-library/react";
import '@testing-library/jest-dom';
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import thunk from 'redux-thunk';
import AddProfile from "./AddProfile";

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

// Mocking navigation functionality
const mockedNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedNavigate,
}));

// Extended initial state to cover more test scenarios
const initialState = {
  profile: {
    userProfile: [],
    profileCreateData: {
      msg: '',
      response: {
        lasttouch: {
          profile: 'test_profile'
        },
        firstname: 'Test User'
      }
    },
    profileAvatarData: {}
  },
  login: {
    loginSuccess: {
      response: {
        session_stringvalue: 'test_session',
        user_id: 'test_user',
        user_token: 'test_token',
        gamification_id: 'test_gamification'
      }
    },
    registerSuccess: {
      response: {
        session_stringvalue: 'test_register_session',
        user_id: 'test_register_user',
        user_token: 'test_register_token',
        gamification_id: 'test_register_gamification'
      }
    }
  },
  settingsReducer: {
    controlPin: {
      response: {
        profiles: [{
          parental: {
            active: false
          },
          channel: {
            active: false
          }
        }]
      }
    },
    setControlPinData: {
      profiles: [{
        parental: {
          active: false
        },
        channel: {
          active: false
        }
      }]
    },
    checkControlPin: {
      msg: '',
      errors: ''
    },
    remindControlPin: {
      response: {
        email_sent: false
      }
    }
  },
  initialReducer: {
    appMetaData: {
      translations: JSON.stringify({
        language: {
          US: {
            atv_back_notification: 'Back',
            addProfile_access_title_label: 'Add Profile',
            addProfile_access_option_button_avatarSelector: 'Choose Avatar',
            addProfile_access_nameProfile_label: 'Profile Name',
            USR_USR_00015: 'Invalid characters',
            kids_tooltip_title_label: 'Kids Profile',
            kids_tooltip_parentalRestriction_label_validation: 'Parental restrictions',
            kids_tooltip_additionalRestrictions_label_validation: 'Additional restrictions',
            addProfile_access_option_button_save: 'Save',
            userProfile_password_option_button_cancel: 'Cancel'
          }
        }
      })
    }
  }
};

const renderWithState = (ui, { store = mockStore(initialState), ...renderOptions } = {}) => {
  const Wrapper = ({ children }) => (
    <Provider store={store}>
      <MemoryRouter
        initialEntries={[
          {
            state: {
              username: 'Test User',
              selectedProf: 'profile_image.png',
              data: {
                gamification_id: '6579a68439b44705076d056b',
                username: 'Ls Fakelll',
                user_image: 'http://clarovideocdn1.clarovideo.net/lanegociadora01.png',
                rol: 'admin',
                admin: true,
                change_name: false,
                is_kids: 'false',
                partnerUserId: '92820606',
                user_hash: 'OTI4MjAQxNDllM2QyYWU4ZTQyYTYxZDgyYjI1YWNlNWI3YjcxODkyNQ=='
              }
            }
          }
        ]}
      >
        {children}
      </MemoryRouter>
    </Provider>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

describe('Add Profile page test', () => {
  beforeEach(() => {
    localStorage.setItem('region', 'US');
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  test('should render api mock data', () => {
    renderWithState(<AddProfile />);
    expect(screen.getByText('Add Profile')).toBeInTheDocument();
  });

  test('should do profile name button click', () => {
    const { container } = renderWithState(<AddProfile />);
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'profilename');
    fireEvent.focus(scroll);
    fireEvent.keyUp(scroll, { keyCode: '405' });
    fireEvent.keyUp(scroll, { keyCode: '461' });
    fireEvent.click(scroll);
  });

  test('should do choose profile button click', () => {
    const { container } = renderWithState(<AddProfile />);
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'ChooseProfileText_Container_id');
    fireEvent.click(scroll);
  });

  test('should do cancel button click', () => {
    const { container } = renderWithState(<AddProfile />);
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'bu');
    fireEvent.click(scroll);
  });

  test('should handle profile name input blur', () => {
    const { container } = renderWithState(<AddProfile />);
    const input = container.querySelector('input[name="profilename"]');
    fireEvent.blur(input, { target: { value: 'Ls Fakelll' } });
  });

  test('should handle profile name input change', () => {
    const { container } = renderWithState(<AddProfile />);
    const input = container.querySelector('input[name="profilename"]');
    fireEvent.change(input, { target: { value: 'Ls Fakelll' } });
  });

  test('should handle profile name input change with invalid characters', () => {
    const { container } = renderWithState(<AddProfile />);

    const input = container.querySelector('input[name="profilename"]');
    fireEvent.change(input, { target: { value: '<EMAIL>' } });

    // Verify error message appears
    expect(screen.getByText('Invalid characters')).toBeInTheDocument();
  });

  test('handles Samsung TV back key', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 10009 }),
      },
    };
    const { container } = renderWithState(<AddProfile />);
    const getById = queryByAttribute.bind(null, 'id');
    const scroll = getById(container, 'ChooseProfileText_Container_id');
    fireEvent.keyUp(scroll, { keyCode: 10009 });
    delete global.tizen;
  });

  test('should handle back button click', () => {
    const { container } = renderWithState(<AddProfile />);
    const backButton = container.querySelector('#addprofileback');
    fireEvent.click(backButton);
  });

  test('should handle save button click', () => {
    const { container } = renderWithState(<AddProfile />);
    const saveButton = container.querySelector('.profile-save');
    fireEvent.click(saveButton);
  });

  test('should handle kids profile selection', () => {
    const store = mockStore(initialState);
    const { container } = renderWithState(<AddProfile />, { store });
    const kidsButton = screen.queryByText('Kids Profile');
    if (kidsButton) {
      fireEvent.click(kidsButton);
    }
  });

  test('should handle input focus and blur', () => {
    const { container } = renderWithState(<AddProfile />);
    const inputContainer = container.querySelector('#add-profile-name-input-container');
    fireEvent.focus(inputContainer);
    fireEvent.blur(inputContainer);
  });

  test('should handle keyboard focus', () => {
    const { container } = renderWithState(<AddProfile />);
    const keyboard = container.querySelector('.left-container-virtual-keyboard');
    fireEvent.focus(keyboard);
  });

  // New tests to improve coverage

  test('should handle text input with blinking cursor effect', async () => {
    const { container } = renderWithState(<AddProfile />);
    const inputContainer = container.querySelector('#add-profile-name-input-container');

    // Trigger focus to activate blinker
    fireEvent.focus(inputContainer);

    // Fast-forward time to trigger the blinker interval
    act(() => {
      jest.advanceTimersByTime(700);
    });

    // Change input value to trigger the effect
    const input = container.querySelector('input[name="profilename"]');
    fireEvent.change(input, { target: { value: 'New Profile' } });

    // Fast-forward time again to see the blink effect
    act(() => {
      jest.advanceTimersByTime(1400);
    });

    // Unmount to test cleanup
    await waitFor(() => {
      expect(input).toBeInTheDocument();
    });
  });

  test('should handle PIN entry for parental control', () => {
    // Create a new store with security pin active
    const securityPinState = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        controlPin: {
          response: {
            profiles: [{
              parental: {
                active: true
              },
              channel: {
                active: true
              }
            }]
          }
        }
      }
    };

    const store = mockStore(securityPinState);
    const { container } = renderWithState(<AddProfile />, { store });

    // Trigger pin entry by setting up conditions
    const mockPin = ['1', '2', '3', '4', '5', '6'];

    // Dispatch action to simulate PIN validation
    store.dispatch({
      type: 'SET_PIN_VALUE',
      payload: mockPin
    });

    // Test PIN validation
    act(() => {
      store.dispatch({
        type: 'CHECK_CONTROL_PIN',
        payload: { msg: 'OK' }
      });
    });

    // Click save button to trigger pin validation
    const saveButton = container.querySelector('.profile-save');
    fireEvent.click(saveButton);
  });

  test('should handle PIN validation failure', () => {
    // Create a new store with security pin active and validation failure
    const pinFailState = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        controlPin: {
          response: {
            profiles: [{
              parental: {
                active: true
              },
              channel: {
                active: true
              }
            }]
          }
        },
        checkControlPin: {
          msg: 'ERROR',
          errors: 'Invalid PIN'
        }
      }
    };

    const store = mockStore(pinFailState);
    store.dispatch = jest.fn();

    const { container } = renderWithState(<AddProfile />, { store });

    // Simulate PIN validation failure
    act(() => {
      store.dispatch({
        type: 'GET_CHECK_CONTROL_PIN',
        payload: {
          msg: 'ERROR',
          errors: 'Invalid PIN'
        }
      });
    });

    const saveButton = container.querySelector('.profile-save');
    fireEvent.click(saveButton);
  });

  test('should handle profile creation success', () => {
    // Create store with profile creation success
    const profileSuccessState = {
      ...initialState,
      profile: {
        ...initialState.profile,
        profileCreateData: {
          msg: 'OK',
          response: {
            lasttouch: {
              profile: 'test_profile'
            },
            firstname: 'Test User'
          }
        }
      }
    };

    const store = mockStore(profileSuccessState);
    store.dispatch = jest.fn();

    renderWithState(<AddProfile />, { store });

    // Dispatch action to simulate profile creation success
    act(() => {
      store.dispatch({
        type: 'GET_PROFILE_DATA_SUCCESS',
        payload: {
          msg: 'OK',
          response: {
            lasttouch: {
              profile: 'test_profile'
            },
            firstname: 'Test User'
          }
        }
      });
    });

    // Verify navigation occurred
    expect(mockedNavigate).toHaveBeenCalledWith('/watchprofile', expect.any(Object));
  });

  test('should handle control PIN setup success', () => {
    // Create store with security pin setup success
    const pinSetupState = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        setControlPinData: {
          profiles: [{
            parental: {
              active: true
            },
            channel: {
              active: true
            }
          }]
        }
      }
    };

    const store = mockStore(pinSetupState);
    store.dispatch = jest.fn();

    renderWithState(<AddProfile />, { store });

    // Dispatch action to simulate PIN setup success
    act(() => {
      store.dispatch({
        type: 'SET_CONTROL_PIN_SUCCESS',
        payload: {
          profiles: [{
            parental: {
              active: true
            },
            channel: {
              active: true
            }
          }]
        }
      });
    });
  });

  test('should handle PIN reminder notification', () => {
    // Create store with reminder notification
    const reminderState = {
      ...initialState,
      settingsReducer: {
        ...initialState.settingsReducer,
        remindControlPin: {
          response: {
            email_sent: true
          }
        }
      }
    };

    const store = mockStore(reminderState);
    store.dispatch = jest.fn();

    renderWithState(<AddProfile />, { store });

    // Dispatch action to simulate reminder notification
    act(() => {
      store.dispatch({
        type: 'REMIND_CONTROL_PIN_SUCCESS',
        payload: {
          response: {
            email_sent: true
          }
        }
      });
    });

    // Fast-forward time to trigger timeout
    act(() => {
      jest.advanceTimersByTime(4000);
    });
  });
});