import React from 'react'
import Mosai<PERSON><PERSON>ilter from './MosaicFilter'

import {
  fireEvent,
  getByTestId,
  render,
  waitFor,
} from '@testing-library/react'
import Redux, { Provider, useDispatch, useSelector } from 'react-redux'
import configureStore from 'redux-mock-store'
import { createBrowserHistory } from 'history'
import { MemoryRouter, useNavigate } from 'react-router-dom'
import * as redux from 'react-redux'
import { store } from '../../store/sagaStore'

const mocksuccessresponse = [
  {
    app_behaviour: 'null',
    code: 'iptv_todos',
    id: '136069',
    id_parent: '18053',
    image: null,
    image_over: null,
    level: 1,
    menu_id: '1852',
    order: '3',
    provider_name: null,
    status: '1',
    text: 'Todos',
    type: 'dest'
}
]

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn()
  // useSelector:jest.fn()
}))

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn()
}))

const initialState = {}
const mockStore = configureStore([])
const history = createBrowserHistory()

const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
  <Provider store={reduxStore}>
    <MemoryRouter
      history={history}
      initialEntries={[
        {
          pathname: '/mosaicfilter'
        }
      ]}
    >
      {children}
    </MemoryRouter>
  </Provider>
)

const renderWithState = ui => {
  return render(ui, { wrapper: Wrapper })
}

describe('MosaicFilter', () => {
  let spyOnUseSelector
  beforeEach(() => {
    const mockUseDispatch = jest.fn()
    const mockDispatch = jest.fn()
    const mockUseSelector = jest.fn()
    const mockSelector = jest.fn()
    const mockUseNavigate = jest.fn()
    const mockNavigate = jest.fn()

    jest.mock('react-redux', () => ({
      useDispatch: () => mockUseDispatch.mockReturnValue(mockDispatch),
      useSelector: () => mockUseSelector.mockReturnValue(mockSelector)
    }))

    jest.mock('react-router-dom', () => ({
      useNavigate: () => mockUseNavigate.mockReturnValue(mockNavigate)
    }))

    // Mock useSelector hook
    spyOnUseSelector = jest.spyOn(redux, 'useSelector')
  })

  afterEach(() => {
    useSelector.mockClear()
  })

  it('Mosaic Filter', async () => {
    useSelector.mockImplementation(() => mocksuccessresponse)

    waitFor(() => {
      useNavigate.mockReturnValue(jest.fn())
      useDispatch.mockReturnValue(jest.fn())

      const wrapper = renderWithState(<MosaicFilter />, store)

      fireEvent(
        getByTestId(wrapper.container, 'mosaicselection'),

        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        }),
      )

      fireEvent(
        getByTestId(wrapper.container, 'lockScreen'),
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      )
      fireEvent(
        getByTestId(wrapper.container, 'FavoriteScreen'),
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      )
      fireEvent(
        getByAltText(wrapper.container, 'Todos'),

        new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        })
      )
    })
  })
})



