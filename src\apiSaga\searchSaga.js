import { call, takeEvery } from '@redux-saga/core/effects'
import { URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getSearchSuccess,
  getSearchError,
  getPredictiveSearchSuccess,
  getPredictiveSearchError,
  getMultiContentSearchSuccess,
  getMultiContentSearchError,
  getProgressbarBookmarkSuccess,
  getProgressbarBookmarkError,
  getTalentSearchSuccess,
  getTalentSearchError
} from '../store/slices/SearchSlice'

export function* fetchSearchSaga({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(
      request,
      `${URL.Search_RECOMENDATION_URL}&is_kids=${
        payload?.is_kids == true ? '1' : '0'
      }&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getSearchSuccess(response))
        },
        onError(error) {
          store.dispatch(getSearchError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* fetchBookmarkProgressBar({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(
      request,
      `${URL.SEARCH_PROGRESSBAR_BOOKMARK_URL}&user_id=${payload?.user_id}&region=${region}&filterlist=${payload?.filterlist}&group_id=${payload.group_id}&user_hash=${payload?.user_hash}&lasttouch=${payload?.lasttouch}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getProgressbarBookmarkSuccess(response))
        },
        onError(error) {
          store.dispatch(getProgressbarBookmarkError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* fetchMulticontentSearch(payload) {
  const region = localStorage.getItem('region')

  try {
    yield call(
      request,
      `${URL.MULTICONTENT_SEARCH_URL}&region=${region}`,
      {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json'
        },
        body: {
          token: payload?.payload?.user_token,
          extra_params: JSON.stringify(payload?.payload?.group_ids)
        }
      },
      {
        onSuccess(responce) {
          store.dispatch(getMultiContentSearchSuccess(responce))
        },
        onError(error) {
          store.dispatch(getMultiContentSearchError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* fetchPredictiveSearchSaga({ payload }) {
  const region = localStorage.getItem('region')

  try {
    yield call(
      request,
      `${URL.SEARCHPREDICTIVELINEAR}&is_kids=${
        payload?.is_kids == true ? '1' : '0'
      }&epg_version=${payload?.epg_version}&value=${
        payload?.value
      }&filterlist=${payload?.filterlist}&region=${region}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getPredictiveSearchSuccess(response))
        },
        onError(error) {
          store.dispatch(getPredictiveSearchError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export function* FetchTalentSearchSaga({ payload }) {
  const region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.Talent_Search_RECOMENDATION_URL}&field=${payload?.field}&value=${payload?.value}&HKS=${payload?.hks}&region=${region}&typemusic=${payload?.typemusic}&provider_id=${payload?.provider_id}}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getTalentSearchSuccess(response))
        },
        onError(error) {
          store.dispatch(getTalentSearchError(error))
        }
      }
    )
  } catch (error) {}
}

export default function* SearchDataSaga() {
  yield takeEvery('search/getSearchData', fetchSearchSaga)
  yield takeEvery('search/getPredictiveSearchData', fetchPredictiveSearchSaga)
  yield takeEvery('search/getMulticontentSearchData', fetchMulticontentSearch)
  yield takeEvery('search/getProgressbarBookmark', fetchBookmarkProgressBar)
  yield takeEvery('search/getTalentSearchData', FetchTalentSearchSaga)
}
