import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import '@testing-library/jest-dom';
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import EpgFilterScreen from './EpgFilterScreen';

jest.mock('./EpgMosaicScreen', () => {
    return function MockEpgMosaicScreen(props) {
        return <div data-testid="mosaic-screen">Mosaic Screen</div>;
    };
});

const localStorageMock = {
    getItem: jest.fn(() => 'test-region'),
    setItem: jest.fn(),
    clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

document.getElementById = jest.fn((id) => {
    if (id === 'mosaicScreen') return null;
    if (id === 'epgFilter') return { id: 'epgFilter' };
    return null;
});

global.tizen = {
    tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockImplementation((key) => {
            if (key === 'ColorF2Yellow') return { code: 403 };
            if (key === 'ColorF1Green') return { code: 402 };
            return { code: 0 };
        })
    }
};

const mockStore = configureStore([]);
const history = createHistory();

const mockProps = {
    closeFilterScreen: jest.fn(),
    closeMainEpgScreen: jest.fn(),
    handleNoChannelFilter: jest.fn(),
    resetFavScreen: jest.fn()
};

const initialState = {
    initialReducer: {
        appMetaData: {
            translations: JSON.stringify({
                language: {
                    'test-region': {
                        top_head_option_button_exit: 'Exit',
                        top_head_option_button_back: 'Back'
                    }
                }
            })
        }
    }
};

const renderComponent = (props = mockProps, store = mockStore(initialState)) => {
    return render(
        <Provider store={store}>
            <Router history={history}>
                <EpgFilterScreen {...props} />
            </Router>
        </Provider>
    );
};

describe('EpgFilterScreen Component', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test('should render EpgFilterScreen properly', () => {
        const { getByText } = renderComponent();

        expect(getByText('Filtrar')).toBeInTheDocument();
        expect(getByText('Ver categorías')).toBeInTheDocument();
        expect(getByText('Ver canales')).toBeInTheDocument();
        expect(getByText('Exit')).toBeInTheDocument();
        expect(getByText('Back')).toBeInTheDocument();
    });

    test('should handle green button keyup event', () => {
        const { getByText } = renderComponent();

        const greenButton = getByText('Exit').parentElement;
        fireEvent.keyUp(greenButton, { key: 'Enter', keyCode: 13 });

        expect(mockProps.closeFilterScreen).toHaveBeenCalledTimes(1);
        expect(mockProps.closeMainEpgScreen).toHaveBeenCalledTimes(1);
    });

    test('should handle yellow button keyup event', () => {
        const { getByText } = renderComponent();

        const yellowButton = getByText('Back').parentElement;
        fireEvent.keyUp(yellowButton, { key: 'Enter', keyCode: 13 });

        expect(mockProps.closeFilterScreen).toHaveBeenCalledTimes(1);
    });

    test('should show mosaic screen when categories button is clicked', () => {
        const { getByText, queryByTestId } = renderComponent();

        const categoriesButton = getByText('Ver categorías').parentElement;
        fireEvent.keyUp(categoriesButton, { key: 'Enter', keyCode: 13 });
        expect(mockProps.closeFilterScreen).not.toHaveBeenCalled();
        expect(queryByTestId('mosaic-screen')).toBeInTheDocument();
    });

    test('should show mosaic screen when channels button is clicked', () => {
        const { getByText, queryByTestId } = renderComponent();

        const channelsButton = getByText('Ver canales').parentElement;
        fireEvent.keyUp(channelsButton, { key: 'Enter', keyCode: 13 });
        expect(mockProps.closeFilterScreen).not.toHaveBeenCalled();
        expect(queryByTestId('mosaic-screen')).toBeInTheDocument();
    });

    test('should handle Samsung key events', () => {
        renderComponent();

        fireEvent.keyUp(document, { keyCode: 402 });

        expect(mockProps.closeFilterScreen).toHaveBeenCalledTimes(1);
        expect(mockProps.closeMainEpgScreen).toHaveBeenCalledTimes(1);

        jest.clearAllMocks();
        fireEvent.keyUp(document, { keyCode: 403 });
        jest.runAllTimers();
        expect(mockProps.closeFilterScreen).toHaveBeenCalled();
    });

    test('should handle LG key events', () => {
        const tizenBackup = global.tizen;
        delete global.tizen;

        renderComponent();
        fireEvent.keyUp(document, { keyCode: 404 });

        expect(mockProps.closeFilterScreen).toHaveBeenCalledTimes(1);
        expect(mockProps.closeMainEpgScreen).toHaveBeenCalledTimes(1);
        jest.clearAllMocks();
        fireEvent.keyUp(document, { keyCode: 405 });
        jest.runAllTimers();
        expect(mockProps.closeFilterScreen).toHaveBeenCalled();
        global.tizen = tizenBackup;
    });

    test('should handle exit keypress (461)', () => {
        renderComponent();
        fireEvent.keyUp(document, { keyCode: 461 });
        jest.runAllTimers();
        expect(mockProps.closeFilterScreen).toHaveBeenCalled();
    });

    test('should handle useEffect cleanup', () => {
        const { unmount } = renderComponent();
        const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
        unmount();
        expect(removeEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function));
        removeEventListenerSpy.mockRestore();
    });

    test('should handle toggle mosaic effect with mosaic screen open', () => {
        document.getElementById.mockImplementation((id) => {
            if (id === 'mosaicScreen') return { id: 'mosaicScreen' };
            if (id === 'epgFilter') return null;
            return null;
        });

        renderComponent();
        fireEvent.keyUp(document, { keyCode: 461 });
        jest.runAllTimers();
        expect(mockProps.closeFilterScreen).toHaveBeenCalled();
    });
});