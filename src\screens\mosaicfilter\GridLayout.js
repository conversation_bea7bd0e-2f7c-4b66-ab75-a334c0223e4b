import React, { useRef } from 'react'
import '../../styles/GridLayout.scss'
import AsyncImageLoader from '../epg/AsyncImageLoader'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
const GridLayout = ({ img, type }) => {
  const epgLineupRef = useRef([])
  const currentcard = useSelector(state => state?.epg?.currentcard)
  const { epgMenu, epgVersion, epgLineup, isChannelLoading, epgLineuplength } =
    useSelector(state => ({
      epgMenu: state?.epg?.epgMenu,
      epgVersion: state?.epg?.epgVersion,
      epgLineup: state?.epg?.epgLineup?.response?.channels,
      epgLineuplength: state?.epg?.epgLineup?.response?.channels?.length,
      isChannelLoading: state?.epg?.isChannelLoading
    }))

  const favoriteChannelList = useSelector(
    state => state?.epg?.favouriteLive?.response?.groups,
  )
  const lockedChannelsList = useSelector(
    state => state?.settingsReducer?.lockedChannelsList?.response?.groups,
  )

  const getFavouriteChannelsIcon = item => {
    const found = favoriteChannelList?.find(each => each.id === item)
    if (found) {
      return true
    }
    return false
  }

  const getLockedChannelsIcon = item => {
    const found = lockedChannelsList?.find(each => each.id === item)
    if (found) {
      return true
    }
    return false
  }
  const navigate = useNavigate()

  const handleConfigurePin = (data, item) => {
    navigate('/my-settings/help-And-Settings/security-pin/configure', {
      state: {
        data,
        item,
        pageName: '/my-settings/help-And-Settings/parental-control/locked-channels'
      },
      replace: true
    })
  }

  return (
    <div>
      {/* Grid Layout 1 */}
      {img?.length === 1 && (
        <>
          <div className={'grid-layout-1-container'}>
            {img.map((data, index) => (
              <button
                className="grid-layout-1 mosaic-channel-item focusable"
                ref={ref => (epgLineupRef.current[index] = ref)}
                key={index}
                id={'grid' + index}
                data-sn-up={
                  currentcard && Math.ceil((index + 1) / 4) === 1
                    ? `#${currentcard}`
                    : undefined
                }
                data-sn-right={
                  ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                  currentcard
                    ? `#${currentcard}`
                    : undefined
                }
                data-sn-left={
                  index == 0 && currentcard
                    ? `#${currentcard}`
                    : index % 4 == 0 && currentcard
                    ? `#${currentcard}`
                    : undefined
                }
                tabIndex={1}
              >
                <div className="favourite-lock-channel-icon-main-container">
                  {type === 'lock' ? (
                    <div className="lock-channel-icon">
                      <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                    </div>
                  ) : type === 'favourite' ? (
                    <div className="favourite-channel-icon">
                      <img src="images/Mosaic_Icons/icon_favourite.png" />
                    </div>
                  ) : (
                    <div className="favourite-lock-channel-icon">
                      <div className="favourite-channel-icon">
                        <img
                          src={
                            favoriteChannelList &&
                            getFavouriteChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_favourite.png'
                              : null
                          }
                          className={
                            favoriteChannelList &&
                            getFavouriteChannelsIcon(data?.group_id)
                              ? 'favourite-channel-icon'
                              : null
                          }
                        />
                      </div>
                      <img
                        src={
                          lockedChannelsList &&
                          getLockedChannelsIcon(data?.group_id)
                            ? 'images/Mosaic_Icons/icon_locked_channel.png'
                            : null
                        }
                        className={
                          lockedChannelsList &&
                          getLockedChannelsIcon(data?.group_id)
                            ? 'lock-channel-icon'
                            : null
                        }
                      />
                    </div>
                  )}
                </div>
                <div className="grid-layout-1-channel-logo">
                  <AsyncImageLoader
                    src={
                      type === 'epg'
                        ? data?.image
                        : type === 'lock'
                        ? data?.image_small
                        : type === 'favourite'
                        ? data?.image
                        : ''
                    }
                    alt="channel-logo"
                  />
                </div>
              </button>
            ))}
          </div>
        </>
      )}

      {/* GridLayout 2 */}

      {img?.length === 2 && (
        <>
          <div className={'grid-layout-2-container'}>
            {img.map((data, index) => (
              <button
                className="grid-layout-2 mosaic-channel-item focusable"
                ref={ref => (epgLineupRef.current[index] = ref)}
                key={index}
                id={'grid' + index}
                data-sn-up={
                  currentcard && Math.ceil((index + 1) / 4) === 1
                    ? `#${currentcard}`
                    : undefined
                }
                data-sn-right={
                  ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                  currentcard
                    ? `#${currentcard}`
                    : undefined
                }
                data-sn-left={
                  index == 0 && currentcard
                    ? `#${currentcard}`
                    : index % 4 == 0 && currentcard
                    ? `#${currentcard}`
                    : undefined
                }
                tabIndex={1}
              >
                <div className="favourite-lock-channel-icon-main-container">
                  {type === 'lock' ? (
                    <div className="lock-channel-icon">
                      <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                    </div>
                  ) : type === 'favourite' ? (
                    <div className="favourite-channel-icon">
                      <img src="images/Mosaic_Icons/icon_favourite.png" />
                    </div>
                  ) : (
                    <div className="favourite-lock-channel-icon">
                      <div className="favourite-channel-icon">
                        <img
                          src={
                            favoriteChannelList &&
                            getFavouriteChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_favourite.png'
                              : null
                          }
                          className={
                            favoriteChannelList &&
                            getFavouriteChannelsIcon(data?.group_id)
                              ? 'favourite-channel-icon'
                              : null
                          }
                        />
                      </div>
                      <img
                        src={
                          lockedChannelsList &&
                          getLockedChannelsIcon(data?.group_id)
                            ? 'images/Mosaic_Icons/icon_locked_channel.png'
                            : null
                        }
                        className={
                          lockedChannelsList &&
                          getLockedChannelsIcon(data?.group_id)
                            ? 'lock-channel-icon'
                            : null
                        }
                      />
                    </div>
                  )}
                </div>
                <div className="grid-layout-2-channel-logo">
                  <AsyncImageLoader
                    src={
                      type === 'epg'
                        ? data?.image
                        : type === 'lock'
                        ? data?.image_small
                        : type === 'favourite'
                        ? data?.image
                        : ''
                    }
                    alt="channel-logo"
                  />
                </div>
              </button>
            ))}
          </div>
        </>
      )}

      {/* GridLayout 3 */}
      {img?.length === 3 && (
        <>
          <div className={' grid-layout-3-container'}>
            {img.map((data, index) =>
              index == 0 ? (
                <button
                  className="grid-layout-3-item-1 mosaic-channel-item focusable"
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>
                  <div className="grid-layout-3-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </button>
              ) : (
                <button
                  className={'grid-layout-3-item mosaic-channel-item focusable'}
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>
                  <div className="grid-layout-3-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </button>
              ),
            )}
          </div>
        </>
      )}

      {/* Grid Layout 4 */}
      {img?.length === 4 && (
        <>
          <div className={'grid-layout-4-container'}>
            {img.map((data, index,item) =>
              index == 0 ? (
                <button
                  className="grid-layout-4-item-1 mosaic-channel-item focusable"
                  onClick={() => handleConfigurePin('Unlock', item)}
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img
                          src="images/Mosaic_Icons/icon_locked_channel.png"
                          className="lock-channel-icon"
                        />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img
                          src="images/Mosaic_Icons/icon_favourite.png"
                          className="favourite-channel-icon"
                        />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>
                  <div className="grid-layout-4-channel-logo">
                    <AsyncImageLoader
                      className="grid-layout-4-channel-logo-image "
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </button>
              ) : index == img.length - 1 ? (
                <button
                  className="grid-layout-4-item mosaic-channel-item focusable"
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img
                          src="images/Mosaic_Icons/icon_locked_channel.png"
                          className="lock-channel-icon"
                        />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img
                          src="images/Mosaic_Icons/icon_favourite.png"
                          className="favourite-channel-icon"
                        />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>
                  <div className="grid-layout-4-channel-logo">
                    <AsyncImageLoader
                      className="grid-layout-4-channel-logo-image "
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </button>
              ) : (
                <button
                  className={
                    'grid-layout-4-item  lock-channel-icon mosaic-channel-item focusable item ' +
                    index
                  }
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img
                          src="images/Mosaic_Icons/icon_locked_channel.png"
                          className="lock-channel-icon"
                        />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img
                          src="images/Mosaic_Icons/icon_favourite.png"
                          className="favourite-channel-icon"
                        />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png '
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>
                  <div className="grid-layout-4-channel-logo">
                    <AsyncImageLoader
                      className="grid-layout-4-channel-logo-image "
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </button>
              ),
            )}
          </div>
        </>
      )}

      {/* Grid Layout 5 */}
      {img?.length === 5 && (
        <>
          <div className={'grid-layout-5-container '}>
            {img.map((data, index) =>
              index == 0 && img?.length <= 9 ? (
                <div
                  className="grid-layout-5-item-1 mosaic-channel-item focusable"
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>

                  <div className="grid-layout-5-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt=""
                    />
                  </div>
                </div>
              ) : index == 3 ? (
                <div
                  className="mosaic-channel-item focusable grid-layout-5-item-4"
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>

                  <div className="grid-layout-5-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt=""
                    />
                  </div>
                </div>
              ) : index == 1 ? (
                <div
                  className="mosaic-channel-item focusable grid-layout-5-item-2"
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>

                  <div className="grid-layout-5-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </div>
              ) : index == 4 ? (
                <div
                  className="mosaic-channel-item focusable grid-layout-5-item-5"
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>

                  <div className="grid-layout-5-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt=""
                    />
                  </div>
                </div>
              ) : (
                <div
                  className={
                    'mosaic-channel-item focusable item grid-layout-5-item-3' +
                    index
                  }
                  ref={ref => (epgLineupRef.current[index] = ref)}
                  key={index}
                  id={'grid' + index}
                  data-sn-up={
                    currentcard && Math.ceil((index + 1) / 4) === 1
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-right={
                    ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                    currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  data-sn-left={
                    index == 0 && currentcard
                      ? `#${currentcard}`
                      : index % 4 == 0 && currentcard
                      ? `#${currentcard}`
                      : undefined
                  }
                  tabIndex={1}
                 >
                  <div className="favourite-lock-channel-icon-main-container">
                    {type === 'lock' ? (
                      <div className="lock-channel-icon">
                        <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                      </div>
                    ) : type === 'favourite' ? (
                      <div className="favourite-channel-icon">
                        <img src="images/Mosaic_Icons/icon_favourite.png" />
                      </div>
                    ) : (
                      <div className="favourite-lock-channel-icon">
                        <div className="favourite-channel-icon">
                          <img
                            src={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'images/Mosaic_Icons/icon_favourite.png'
                                : null
                            }
                            className={
                              favoriteChannelList &&
                              getFavouriteChannelsIcon(data?.group_id)
                                ? 'favourite-channel-icon'
                                : null
                            }
                          />
                        </div>
                        <img
                          src={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_locked_channel.png'
                              : null
                          }
                          className={
                            lockedChannelsList &&
                            getLockedChannelsIcon(data?.group_id)
                              ? 'lock-channel-icon'
                              : null
                          }
                        />
                      </div>
                    )}
                  </div>

                  <div className="grid-layout-5-channel-logo">
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                </div>
              ),
            )}
          </div>
        </>
      )}

      {/* Grid Layout 6 */}
      {img?.length === 6 && (
        <div className="grid-layout-6-container">
          {img.map((data, index) => (
            <div
              className={`mosaic-channel-item focusable grid-layout-6 ${
                index === 0
                  ? 'grid-layout-6-item1'
                  : index === 1
                  ? 'grid-layout-6-item2'
                  : index === 5
                  ? 'grid-layout-6-item3'
                  : ''
              }`}
              ref={ref => (epgLineupRef.current[index] = ref)}
              key={index}
              id={'grid' + index}
              data-sn-up={
                currentcard && Math.ceil((index + 1) / 4) === 1
                  ? `#${currentcard}`
                  : undefined
              }
              data-sn-right={
                ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                currentcard
                  ? `#${currentcard}`
                  : undefined
              }
              data-sn-left={
                index == 0 && currentcard
                  ? `#${currentcard}`
                  : index % 4 == 0 && currentcard
                  ? `#${currentcard}`
                  : undefined
              }
              tabIndex={1}
            >
              <div className="favourite-lock-channel-icon-main-container">
                {type === 'lock' ? (
                  <div className="lock-channel-icon">
                    <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                  </div>
                ) : type === 'favourite' ? (
                  <div className="favourite-channel-icon">
                    <img src="images/Mosaic_Icons/icon_favourite.png" />
                  </div>
                ) : (
                  <div className="favourite-lock-channel-icon">
                    <div className="favourite-channel-icon">
                      <img
                        src={
                          favoriteChannelList &&
                          getFavouriteChannelsIcon(data?.group_id)
                            ? 'images/Mosaic_Icons/icon_favourite.png'
                            : null
                        }
                        className={
                          favoriteChannelList &&
                          getFavouriteChannelsIcon(data?.group_id)
                            ? 'favourite-channel-icon'
                            : null
                        }
                      />
                    </div>
                    <img
                      src={
                        lockedChannelsList &&
                        getLockedChannelsIcon(data?.group_id)
                          ? 'images/Mosaic_Icons/icon_locked_channel.png'
                          : null
                      }
                      className={
                        lockedChannelsList &&
                        getLockedChannelsIcon(data?.group_id)
                          ? 'lock-channel-icon'
                          : null
                      }
                    />
                  </div>
                )}
              </div>
              <div className="grid-layout-6-channel-logo">
                <AsyncImageLoader
                  src={
                    type === 'epg'
                      ? data?.image
                      : type === 'lock'
                      ? data?.image_small
                      : type === 'favourite'
                      ? data?.image
                      : ''
                  }
                  alt="channel-logo"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Grid Layout 7 */}
      {img?.length === 7 && (
        <div className="grid-layout-7-container">
          {img.map((data, index) => (
            <div
              className={`mosaic-channel-item focusable grid-layout-7 ${
                index === 0
                  ? 'grid-layout-7-column1 grid-layout-7-row1 data1 '
                  : index === 1
                  ? 'grid-layout-7-column2 grid-layout-7-row1 data2 '
                  : index === 2
                  ? 'grid-layout-7-column1 grid-layout-7-row2 data3 '
                  : index === 3
                  ? 'grid-layout-7-column2 grid-layout-7-row2 data4 '
                  : index === 4
                  ? 'grid-layout-7-column3 grid-layout-7-row1 data5'
                  : index === 5
                  ? 'grid-layout-7-column3 grid-layout-7-row2 data6'
                  : index === 6
                  ? 'grid-layout-7-column3 grid-layout-7-row2 data7'
                  : ''
              }`}
              ref={ref => (epgLineupRef.current[index] = ref)}
              key={index}
              id={'grid' + index}
              data-sn-up={
                currentcard && Math.ceil((index + 1) / 4) === 1
                  ? `#${currentcard}`
                  : undefined
              }
              data-sn-right={
                ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                currentcard
                  ? `#${currentcard}`
                  : undefined
              }
              data-sn-left={
                index == 0 && currentcard
                  ? `#${currentcard}`
                  : index % 4 == 0 && currentcard
                  ? `#${currentcard}`
                  : undefined
              }
              tabIndex={1}
            >
              <div className="favourite-lock-channel-icon-main-container">
                {type === 'lock' ? (
                  <div
                    className="lock-channel-icon"
                    style={{ position: 'absolute', top: 0, left: 0 }}
                  >
                    <img
                      src="images/Mosaic_Icons/icon_locked_channel.png"
                      style={{ width: '75px', height: '35px' }}
                    />
                  </div>
                ) : type === 'favourite' ? (
                  <div
                    className="favourite-channel-icon"
                    style={{ position: 'absolute', top: 0, left: 0 }}
                  >
                    <img
                      src="images/Mosaic_Icons/icon_favourite.png"
                      style={{ width: '75px', height: '35px' }}
                    />
                  </div>
                ) : (
                  <div className="favourite-lock-channel-icon">
                    <div className="favourite-channel-icon">
                      <img
                        src={
                          favoriteChannelList &&
                          getFavouriteChannelsIcon(data?.group_id)
                            ? 'images/Mosaic_Icons/icon_favourite.png'
                            : null
                        }
                        className={
                          favoriteChannelList &&
                          getFavouriteChannelsIcon(data?.group_id)
                            ? 'favourite-channel-icon'
                            : null
                        }
                      />
                    </div>
                    <img
                      src={
                        lockedChannelsList &&
                        getLockedChannelsIcon(data?.group_id)
                          ? 'images/Mosaic_Icons/icon_locked_channel.png'
                          : null
                      }
                      className={
                        lockedChannelsList &&
                        getLockedChannelsIcon(data?.group_id)
                          ? 'lock-channel-icon'
                          : null
                      }
                    />
                  </div>
                )}
              </div>
              <div className="grid-layout-7-channel-logo">
                <AsyncImageLoader
                  src={
                    type === 'epg'
                      ? data?.image
                      : type === 'lock'
                      ? data?.image_small
                      : type === 'favourite'
                      ? data?.image
                      : ''
                  }
                  alt="channel-logo"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Grid Layout8 */}

      {img?.length === 8 && (
        <div className="grid-layout-8-container">
          <div className="grid-layout-8-left">
            {img.map(
              (data, index) =>
                (index === 0 || index === 1) && (
                  <div
                    key={index}
                    className="grid-layout-8-left-item mosaic-channel-item focusable"
                    ref={ref => (epgLineupRef.current[index] = ref)}
                    //  key={index}
                    id={'grid' + index}
                    data-sn-up={
                      currentcard && Math.ceil((index + 1) / 4) === 1
                        ? `#${currentcard}`
                        : undefined
                    }
                    data-sn-right={
                      ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                      currentcard
                        ? `#${currentcard}`
                        : undefined
                    }
                    data-sn-left={
                      index == 0 && currentcard
                        ? `#${currentcard}`
                        : index % 4 == 0 && currentcard
                        ? `#${currentcard}`
                        : undefined
                    }
                    tabIndex={1}
                  >
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                ),
            )}
          </div>
          <div className="grid-layout-8-right">
            {img.map(
              (data, index) =>
                (index === 2 ||
                  index === 3 ||
                  index === 4 ||
                  index === 5 ||
                  index === 6 ||
                  index === 7) && (
                  <div
                    key={index}
                    className="grid-layout-8-right-item mosaic-channel-item focusable"
                    ref={ref => (epgLineupRef.current[index] = ref)}
                    //  key={index}
                    id={'grid' + index}
                    data-sn-up={
                      currentcard && Math.ceil((index + 1) / 4) === 1
                        ? `#${currentcard}`
                        : undefined
                    }
                    data-sn-right={
                      ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                      currentcard
                        ? `#${currentcard}`
                        : undefined
                    }
                    data-sn-left={
                      index == 0 && currentcard
                        ? `#${currentcard}`
                        : index % 4 == 0 && currentcard
                        ? `#${currentcard}`
                        : undefined
                    }
                    tabIndex={1}
                  >
                    <AsyncImageLoader
                      src={
                        type === 'epg'
                          ? data?.image
                          : type === 'lock'
                          ? data?.image_small
                          : type === 'favourite'
                          ? data?.image
                          : ''
                      }
                      alt="channel-logo"
                    />
                  </div>
                ),
            )}
          </div>
        </div>
      )}

      {/* Grid Layout 9 */}
      {img?.length === 9 && (
        <div className="grid-layout-9-container ">
          {img.map((data, index) => (
            <div
              key={data.index}
              className={`grid-layout-9-item ${
                index === 0
                  ? 'grid-layout-9-item-1 mosaic-channel-item focusable'
                  : 'grid-layout-9-item-rest mosaic-channel-item focusable'
              }`}
              ref={ref => (epgLineupRef.current[index] = ref)}
              id={'grid' + index}
              data-sn-up={
                currentcard && Math.ceil((index + 1) / 4) === 1
                  ? `#${currentcard}`
                  : undefined
              }
              data-sn-right={
                ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                currentcard
                  ? `#${currentcard}`
                  : undefined
              }
              data-sn-left={
                index == 0 && currentcard
                  ? `#${currentcard}`
                  : index % 4 == 0 && currentcard
                  ? `#${currentcard}`
                  : undefined
              }
              tabIndex={1}
            >
              <div className="favourite-lock-channel-icon-main-container">
                {type === 'lock' ? (
                  <div className="lock-channel-icon">
                    <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                  </div>
                ) : type === 'favourite' ? (
                  <div className="favourite-channel-icon">
                    <img src="images/Mosaic_Icons/icon_favourite.png" />
                  </div>
                ) : (
                  <div className="favourite-lock-channel-icon">
                    <div className="favourite-channel-icon">
                      <img
                        id="getFavouriteChannelsIcon"
                        src={
                          favoriteChannelList &&
                          getFavouriteChannelsIcon(data?.group_id)
                            ? 'images/Mosaic_Icons/icon_favourite.png'
                            : null
                        }
                        className={
                          favoriteChannelList &&
                          getFavouriteChannelsIcon(data?.group_id)
                            ? 'favourite-channel-icon'
                            : null
                        }
                      />
                    </div>
                    <img
                      src={
                        lockedChannelsList &&
                        getLockedChannelsIcon(data?.group_id)
                          ? 'images/Mosaic_Icons/icon_locked_channel.png'
                          : null
                      }
                      className={
                        lockedChannelsList &&
                        getLockedChannelsIcon(data?.group_id)
                          ? 'lock-channel-icon'
                          : null
                      }
                    />
                  </div>
                )}
              </div>
              <div className="grid-layout-9-channel-logo">
                <AsyncImageLoader
                  src={
                    type === 'epg'
                      ? data?.image
                      : type === 'lock'
                      ? data?.image_small
                      : type === 'favourite'
                      ? data?.image
                      : ''
                  }
                  alt="channel-logo"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* GridLayout gretaer 9 */}

      {img?.length > 9 && (
        <>
          <div className={'grid-layout-greater-9-container '}>
            {img.map((data, index) => (
              <div
                key={data.index}
                className={`grid-layout-greater-9-item ${
                  index % 9 === 0
                    ? 'grid-layout-9-item-1 mosaic-channel-item focusable'
                    : 'grid-layout-9-item-rest mosaic-channel-item focusable'
                }`}
                ref={ref => (epgLineupRef.current[index] = ref)}
                id={'grid' + index}
                data-sn-up={
                  currentcard && Math.ceil((index + 1) / 4) === 1
                    ? `#${currentcard}`
                    : undefined
                }
                data-sn-right={
                  ((index + 1) % 4 == 0 || index == epgLineuplength - 1) &&
                  currentcard
                    ? `#${currentcard}`
                    : undefined
                }
                data-sn-left={
                  index == 0 && currentcard
                    ? `#${currentcard}`
                    : index % 4 == 0 && currentcard
                    ? `#${currentcard}`
                    : undefined
                }
                tabIndex={1}
              >
                <div className="favourite-lock-channel-icon-main-container">
                  {type === 'lock' ? (
                    <div className="lock-channel-icon">
                      <img src="images/Mosaic_Icons/icon_locked_channel.png" />
                    </div>
                  ) : type === 'favourite' ? (
                    <div className="favourite-channel-icon">
                      <img src="images/Mosaic_Icons/icon_favourite.png" />
                    </div>
                  ) : (
                    <div className="favourite-lock-channel-icon">
                      <div className="favourite-channel-icon">
                        <img
                          src={
                            favoriteChannelList &&
                            getFavouriteChannelsIcon(data?.group_id)
                              ? 'images/Mosaic_Icons/icon_favourite.png'
                              : null
                          }
                          className={
                            favoriteChannelList &&
                            getFavouriteChannelsIcon(data?.group_id)
                              ? 'favourite-channel-icon'
                              : null
                          }
                        />
                      </div>
                      <img
                        src={
                          lockedChannelsList &&
                          getLockedChannelsIcon(data?.group_id)
                            ? 'images/Mosaic_Icons/icon_locked_channel.png'
                            : null
                        }
                        className={
                          lockedChannelsList &&
                          getLockedChannelsIcon(data?.group_id)
                            ? 'lock-channel-icon'
                            : null
                        }
                      />
                    </div>
                  )}
                </div>
                <div className="grid-layout-gretaer-9-channel-logo">
                  <AsyncImageLoader
                    src={
                      type === 'epg'
                        ? data?.image
                        : type === 'lock'
                        ? data?.image_small
                        : type === 'favourite'
                        ? data?.image
                        : ''
                    }
                    alt="channel-logo"
                  />
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  )
}

export default GridLayout
