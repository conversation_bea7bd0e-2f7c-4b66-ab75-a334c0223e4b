import React from "react";
import { fireEvent, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { MemoryRouter } from "react-router-dom";
import configureS<PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ContentClassification from "./ContentClassification";

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
	<Provider store={reduxStore}>
		<MemoryRouter
			history={history}
			initialEntries={[{ state: { pin: '111111' } }]}
		>
			{children}
		</MemoryRouter>
	</Provider>
);
export const renderWithState = (ui) => {
	return render(ui, { wrapper: Wrapper });
};

const mockErrorResponse = {
	"status": "1"
}

const mockIsLoggedInSuccessResponse = {
	response: {
		session_stringvalue: "ZTEATV412001224226580292a69e33",
		user_id: "94520846",
		user_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3MjE3MTQ5OTUsImV4cCI6MTcyN"
	}
}

const mockStatusControlPinSuccessResponse = {
	respose: {
		pin_purchase: {
			status: 0
		},
		pin_parental: {
			status: 1,
			info: {
				name: "<![CDATA[NC-17]]>",
				description: "<![CDATA[Apta para mayores de 18]]>",
				value: 50
			}
		},
		pin_channel: {
			status: 1,
			info: []
		},
		status: 0
	}
}

const mockModifyControlPinSuccessResponse = {
	data: {
		update_accomplishment_status: {
			purchases_pin: 0,
			parental_pin: 1,
			channel_pin: 1
		}
	}
}

describe('Parental Control Settings page test', () => {

	test('should render without api mock data', () => {
		initialState.login = {
			isLoggedIn: mockErrorResponse
		}
		initialState.settingsReducer = {
			statusControlPin: mockErrorResponse
		}
		initialState.settingsReducer = {
			modifyControlPin: mockErrorResponse
		}
		renderWithState(<ContentClassification />)
	})

	test('should render with api mock data', () => {
		initialState.login = {
			isLoggedIn: mockIsLoggedInSuccessResponse
		}
		initialState.settingsReducer = {
			statusControlPin: mockStatusControlPinSuccessResponse
		}
		initialState.settingsReducer = {
			modifyControlPin: mockModifyControlPinSuccessResponse
		}
		renderWithState(<ContentClassification />)
	})

	test('content restriction to pg', () => {
		const { container } = renderWithState(<ContentClassification />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'pgButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('content restriction to pg-13', () => {
		const { container } = renderWithState(<ContentClassification />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'pg13Button')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('content restriction to r', () => {

		const { container } = renderWithState(<ContentClassification />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'rButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('content restriction to nc 17', () => {

		const { container } = renderWithState(<ContentClassification />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'nc17Button')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
	})

	test('navigate to parental control settings page', () => {
		const { container } = renderWithState(<ContentClassification />)
		const getById = queryByAttribute.bind(null, 'id');
		const buttonClick = getById(container, 'cerrarButton')
		fireEvent.focus(buttonClick)
		fireEvent(
			buttonClick,
			new MouseEvent('click', {
				bubbles: true,
				cancelable: true
			})
		)
		fireEvent.keyUp(buttonClick, { keyCode: '10009' })
		history.push('/settings/profile-settings')
	})

})
