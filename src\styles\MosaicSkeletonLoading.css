@import url(http://fonts.googleapis.com/css?family=Roboto:700,400,500,300);
.mosaic-topgrid-container-shimmer {
  width: 100%;
  overflow: scroll;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: loadingAnimation 1s infinite;
}

.mosaic-topgrid-content-shimmer {
  display: inline-flex;
  gap: 20px;
  padding: 80px 0px 50px 80px;
}

.mosaic-topgrid-item-shimmer {
  width: 320px;
  height: 180px;
  background: #282828 0% 0% no-repeat padding-box;
  border-radius: 3px;
  opacity: 1;
  text-align: center;
  display: grid;
  place-items: center;
  padding-top: 30px;
}

.mosaic-topgrid-item-shimmer .mosaic-topgrid-image {
  width: 42.64px;
  height: 55.99px;
  background-color: #2f2f2f;
}
.mosaic-topgrid-title {
  width: 122.64px;
  height: 25.99px;
  background-color: #2f2f2f;
}

@keyframes loadingAnimation {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.mosaic-grid-container-shimmer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 0px 90px 0px 90px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: loadingAnimation 1s infinite;
}

.mosaic-grid-item-shimmer {
  background: #282828 0% 0% no-repeat padding-box;
  border-radius: 4px;
  opacity: 1;
  text-align: center;
  width: 407px;
  height: 229px;
  display: grid;
  place-items: center;
}

.mosaic-channel-number-shimmer {
  font: normal normal normal 30px/39px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;

  height: 35px;
  width: 51px;
}

.mosaic-channel-image-shimmer {
  width: 209px;
  height: 53px;
  text-align: center;
  background-color: #2f2f2f;
}

.mosaic-channel-title-shimmer {
  text-align: center;
  font: normal normal normal 30px/38px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  background-color: #2f2f2f;
  width: 209px;
  height: 23px;
}
