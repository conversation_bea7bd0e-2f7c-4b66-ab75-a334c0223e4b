import React from "react";
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import '@testing-library/jest-dom';
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configure<PERSON><PERSON> from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import EpgDescription from "./Description";

const mockStore = configureStore([]);
const history = createHistory();

const Wrapper = ({ children, reduxStore }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);

const renderWithState = (ui, initialState) => {
    const store = mockStore(initialState);
    return render(ui, {
        wrapper: (props) => <Wrapper {...props} reduxStore={store} />
    });
};

// Mock localStorage and sessionStorage
beforeEach(() => {
    global.localStorage = {
        getItem: jest.fn().mockImplementation((key) => {
            if (key === 'region') return 'US';
            return null;
        }),
        setItem: jest.fn(),
    };
    global.sessionStorage = {
        setItem: jest.fn(),
    };
});

describe('Description Component Tests', () => {
    test('should render without crashing', () => {
        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {}
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'All Channels' },
                channelDownId: null
            }
        };
        renderWithState(<EpgDescription />, initialState);
    });

    test('should capitalize sentences correctly', () => {
        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {}
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'all channels. favorite shows. movie time.' },
                channelDownId: null
            }
        };

        renderWithState(<EpgDescription />, initialState);
        expect(screen.getByText('All channels. Favorite shows. Movie time.')).toBeInTheDocument();
    });

    test('should display correct filter text when not showing favorites', () => {
        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {
                                TvEnVivo_GuiaCompleta_PanelShortcuts_TextoFavoritos: 'Add to Favorites'
                            }
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: null
            }
        };

        const { container } = renderWithState(<EpgDescription />, initialState);
        const greenShortcutText = container.querySelector('.shortcut-bar-icon-green + .shortcut-bar-text');
        expect(greenShortcutText).toHaveTextContent('TvEnVivo_GuiaCompleta_PanelSho');
    });

    test('should format duration correctly with hours and minutes', () => {
        const props = {
            event: {
                name: 'Test Show',
                duration: '02:30:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T22:30:00',
                parental_rating: '12',
                description: 'A test show description'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {}
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: null
            }
        };

        renderWithState(<EpgDescription {...props} />, initialState);
        expect(screen.getByText('2h 30 mins')).toBeInTheDocument();
    });

    test('should format duration correctly with only hours', () => {
        const props = {
            event: {
                name: 'Test Show',
                duration: '01:00:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T21:00:00',
                parental_rating: '12',
                description: 'A test show description'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {}
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: null
            }
        };

        renderWithState(<EpgDescription {...props} />, initialState);
        expect(screen.getByText('1h')).toBeInTheDocument();
    });

    test('should format duration correctly with only minutes', () => {
        const props = {
            event: {
                name: 'Test Show',
                duration: '00:30:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T20:30:00',
                parental_rating: '12',
                description: 'A test show description'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {}
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: null
            }
        };

        renderWithState(<EpgDescription {...props} />, initialState);
        expect(screen.getByText('30 mins')).toBeInTheDocument();
    });

    test('should render director, talent and country information', () => {
        const props = {
            event: {
                name: 'Test Movie',
                duration: '02:30:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T22:30:00',
                parental_rating: '12',
                description: 'A test movie description',
                ext_director: 'Steven Spielberg',
                talent: 'Tom Hanks, Meryl Streep, Brad Pitt',
                ext_country: 'USA'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {
                                Metadata_TextoDirector: 'Director',
                                Metadata_TextoProtagonistas: 'Cast',
                                Metadata_TextoPais: 'Country'
                            }
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: null
            }
        };

        const { container } = renderWithState(<EpgDescription {...props} />, initialState);
        expect(container.querySelector('.talent-details:nth-child(1) .talent-title')).toHaveTextContent('Metadata_TextoDirector');
        expect(screen.getByText('Steven Spielberg')).toBeInTheDocument();
        expect(container.querySelector('.talent-details:nth-child(2) .talent-title')).toHaveTextContent('Metadata_TextoProtagonistas');
        expect(screen.getByText('Tom Hanks, Meryl Streep')).toBeInTheDocument();
        expect(container.querySelector('.talent-details:nth-child(3) .talent-title')).toHaveTextContent('Metadata_TextoPais');
        expect(screen.getByText('USA')).toBeInTheDocument();
    });

    test('should render blue button when groupData id does not match channelDownId', () => {
        const props = {
            groupData: {
                group: {
                    common: {
                        id: '123'
                    }
                }
            },
            event: {
                name: 'Test Show',
                duration: '02:30:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T22:30:00',
                parental_rating: '12',
                description: 'A test show description'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {
                                TvEnVivo_GuiaCompleta_PanelShortcuts_TextoOpciones: 'Options'
                            }
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: '456'
            }
        };

        const { container } = renderWithState(<EpgDescription {...props} />, initialState);
        const blueButton = container.querySelector('.shortcut-bar-icon-blue');
        expect(blueButton).toBeInTheDocument();
        const blueButtonText = container.querySelector('.shortcut-bar-icon-blue + .shortcut-bar-text');
        expect(blueButtonText).toHaveTextContent('TvEnVivo_GuiaCompleta_PanelSho');
    });

    test('should not render blue button when groupData id matches channelDownId', () => {
        const props = {
            groupData: {
                group: {
                    common: {
                        id: '123'
                    }
                }
            },
            event: {
                name: 'Test Show',
                duration: '02:30:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T22:30:00',
                parental_rating: '12',
                description: 'A test show description'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {
                                TvEnVivo_GuiaCompleta_PanelShortcuts_TextoOpciones: 'Options'
                            }
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: '123'
            }
        };

        const { container } = renderWithState(<EpgDescription {...props} />, initialState);
        const blueButton = container.querySelector('.shortcut-bar-icon-blue');
        expect(blueButton).not.toBeInTheDocument();
    });

    test('should not render shortcut bar when liveTvDetail is true', () => {
        const props = {
            liveTvDetail: true,
            event: {
                name: 'Test Show',
                duration: '02:30:00',
                date_begin: '2023-01-01T20:00:00',
                date_end: '2023-01-01T22:30:00',
                parental_rating: '12',
                description: 'A test show description'
            }
        };

        const initialState = {
            initialReducer: {
                appMetaData: {
                    translations: JSON.stringify({
                        language: {
                            US: {}
                        }
                    })
                }
            },
            epg: {
                epgFilterName: { payload: 'ALL CHANNELS' },
                channelDownId: null
            }
        };

        const { container } = renderWithState(<EpgDescription {...props} />, initialState);
        const shortcutBar = container.querySelector('.shortcut-bar-epg2');
        expect(shortcutBar).not.toBeInTheDocument();
    });
});