import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './ContinueWatching.scss'
import { COMMON_URL } from '../../../utils/environment'
import ProgressBar from '../../Progressbar/Progressbar'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import { pushContentSelectionEvent } from '../../../GoogleAnalytics'
import { HOME, SELECT_CONTENT } from '../../../GoogleAnalyticsConstants'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const ContinueWatching = props => {
  const navigate = useNavigate()
  const { state } = useLocation()

  const [title, setTitle] = useState('')
  const [duration, setDuration] = useState('')
  const [data, setData] = useState([])
  const [content, setContent] = useState([])
  const [focusContent, setFocusContent] = useState(false)
  const [focusedId, setFocusedId] = useState(null)
  const startTimeRef = useRef(null)
  const smallprogress = true
  const serieslargeProgress = true

  const railImage = props?.byUser
    ? data?.data?.groups ?? data?.response?.groups
    : props?.dataObject?.groups
  const filteredData = railImage?.filter(each => 
      each?.vistime?.last?.progress > 0 && each?.vistime?.last?.progress !== 100
    ) || [];
  const railImageStatus = data?.status
  const apaAssetData = useSelector(state => state?.Images?.imageresponse)

  const notification = useSelector(state => state?.homeReducer?.notificationmsg)
  const notificationmsg = useSelector(
    state => state?.homeReducer?.notificationdata
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const getMediaRes = useSelector(state => state?.player?.getMediaRes?.response)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const posterTitle =
    apaMetaData?.poster_title_configuration &&
    JSON?.parse(apaMetaData?.poster_title_configuration)

  const navData = useSelector(state => state?.homeReducer?.navbarData)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)   
  const currNavIdx = localStorage.getItem('currNavIdx')
   const navKids = useSelector(state => state?.homeReducer?.navbarKidsData)
    const navDataTab = userDetails?.is_kids === 'true' ? navKids : navData
    const seriesIndex = navDataTab?.findIndex(item => item?.code === 'seriesnv' && item?.page === 'Series' );
     const moviesIndex = navDataTab?.findIndex(item => item?.code === 'peliculas' && item?.page === 'Películas' );
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const providersLabelConfiguration =
  apaMetaData?.providers_label_configuration &&
  JSON?.parse(apaMetaData?.providers_label_configuration)
  const providerLabel = providersLabelConfiguration?.default ? providersLabelConfiguration?.default?.susc : providersLabelConfiguration?.[region]?.susc  
  const mycontentdata = localStorage.getItem('miscontenidos')
  const mycontentplaceholder = mycontentdata
    ? 'images/mycontent_placeholder.png'
    : 'images/landscape_card.png'
  const backelement =
    state?.fromPage == 'vodplayer'
      ? document.getElementById(`index${props?.index}0`)
      : document.getElementById(props?.backfocusid)
  const goToMoviesSeries = (item, index) => {
    localStorage.setItem('subMenu', 1)
    const engagementTime =  Date.now() - startTimeRef.current;  
    let userData = {
      suscriptions: userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key => userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
      content_section : navbarTab,
      content_list : props?.title,
      modulo_name : 'carrusel',
      content_list_id : 'carrusel horizontal',
      page_path: HOME,
      page_title: navbarTab,
      engagement_time_msec: engagementTime,
      previous_path: props?.gaPreviousPath
    }
    if (item?.is_series || item?.episode_number) {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', seriesIndex)
      pushContentSelectionEvent(userData,item,index, SELECT_CONTENT)
      navigate('/series', {
        state: { data: item, backfocusid: `index${props?.index}${index}`,pageName:'inicio' }
      })
    } else {
      // GA : Content Selection Event
      localStorage.setItem('currNavIdx', moviesIndex)
      pushContentSelectionEvent(userData,item, index, SELECT_CONTENT)
      navigate('/movies', {
        state: { vodData: item, backfocusid: `index${props?.index}${index}`,pageName:'inicio' }
      })
    }
    startTimeRef.current = Date.now()
  }

  const Addproveedor = code => {
    const addIcon = code?.vod ? code?.vod?.[0]?.url : code?.live_play0?.[0]?.url
    return apaAssetData && apaAssetData[addIcon] //checking the data is there in apa/assets
  }

  const handleTranslationchange = useCallback(keyname => {
    if (apilanguage?.[keyname] == '' || apilanguage?.[keyname] == undefined) {
      return [keyname]
    } else {
      return apilanguage?.[keyname]
    }
  }, [])

  const handleFocus = data => {
    setContent(data)
    setFocusContent(true)
    {
      data.is_series === true
        ? (setTitle(data.title), setDuration(data.duration))
        : null
    }
  }
  const handleBlur = data => {
    setFocusContent(false)
  }

  function removeLastTouchParam(url) {
    const urlParts = url.split('?')
    if (urlParts.length > 1) {
      const params = urlParts[1].split('&')
      const updatedParams = params.filter(
        param => !param.startsWith('lasttouch=')
      )
      return `${urlParts[0]}?${updatedParams.join('&')}`
    }
    return url
  }

  const fetchApi = async params => {
    const newParam = params && removeLastTouchParam(params)
    if (newParam !== undefined) {
      return await fetch(
        localStorage.getItem('continueWatchLasttouch') && props?.byUser === true
          ? ` ${COMMON_URL.BASE_URL}/${newParam}&authpn=${
              COMMON_URL.authpn
            }&authpt=${COMMON_URL.authpt}
            &device_category=${COMMON_URL.device_category}&device_id=${
              COMMON_URL.device_id
            }&device_manufacturer=${
              COMMON_URL.device_manufacturer
            }&device_model=${COMMON_URL.device_model}&device_name=${
              COMMON_URL.device_name
            }&device_type=${
              COMMON_URL.device_type
            }&lasttouch=${localStorage.getItem('continueWatchLasttouch')}`
          : ` ${COMMON_URL.BASE_URL}/${params}&authpn=${COMMON_URL.authpn}&authpt=${COMMON_URL.authpt}&device_category=${COMMON_URL.device_category}&device_id=${COMMON_URL.device_id}&device_manufacturer=${COMMON_URL.device_manufacturer}&device_model=${COMMON_URL.device_model}&device_name=${COMMON_URL.device_name}&device_type=${COMMON_URL.device_type}`
      ).then(async data => {
        const value = await data.json()
        setData(value)
      })
    }
  }

  const handleContentRetryClick = e => {
    e.preventDefault()
    fetchApi(props.dataObject)
  }

  useEffect(() => {
    if (props.byUser) {
      fetchApi(props.dataObject)
    }
  }, [props?.dataObject, props?.byUser])
  const element = document.getElementById('continueWatchRail')

  useEffect(() => {
    if (focusContent) {
      element?.scrollIntoView({
        block: 'center',
        inline: 'center',
        behavior: 'smooth'
      })
      element?.focus()
    }
  }, [focusContent, element])

  const handlesamsungkey = (key, keycode) => {
    if (focusContent) {
      if (key.redcode == keycode) {
        navigate('/deletecard', {
          state: {
            deleteData: content,
            delContinuewatch: true,
            page: 'continuewatch',
            backfocusid: focusedId
          }
        })
      }
    }
  }

  const handleLgkey = keycode => {
    if (focusContent) {
      if (keycode == 403) {
        navigate('/deletecard', {
          state: {
            deleteData: content,
            delContinuewatch: true,
            page: 'continuewatch',
            backfocusid: focusedId
          }
        })
      }
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF0Red'])
      const codes = {
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    if (backelement) {
      backelement?.focus();
    }else if(props?.delContinuewatch && filteredData?.length > 0){
      document.getElementById(`homecontinuewatch${filteredData?.length-1}`)?.focus()
    }
  }, [backelement, navData, railImage]);
  
  useEffect(() => {
		startTimeRef.current = Date.now()
    return () => {
      startTimeRef.current = null
    }
  }, [])

  return (
        <div className={`${filteredData.length > 0 && 'ContinueWatchingrailContainer'}`} id="continueWatchRail">
          {railImage?.find(
            duration =>
              duration?.vistime?.last?.progress > 0 &&
              duration?.vistime?.last?.progress !== 100
          ) ? (
            <div className="railTitle">
              <SafeHTML html={props?.title || ''} />
            </div>
          ) : (
            ''
          )}
           <div className='ContinueWatchingsub'>
          { filteredData.length > 0  ? (
            <div className="continue-wrapper">
              {filteredData?.map((each, index, array) => (
                <>            
                      <button
                        className="rail_block focusable"
                        key={index}
                        id={`index${props?.index}${index}`}
                        data-testid={`rail_card_click${index}`}
                        onClick={() => goToMoviesSeries(each, index)}
                        onFocus={() => {
                          handleFocus(each)
                          setFocusedId(`index${props.index}${index}`)
                        }}
                        data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                        data-sn-up={document.getElementById(`index${props?.index-1}0`) ? `#index${props?.index-1}0` : undefined}
                        onBlur={() => handleBlur(each)}
                        data-sn-right={index === filteredData.length - 1 ? '' : undefined}
                        data-sn-left={index == 0 ? '' : undefined}
                      >
                        {each.image_small && each.image_small !== '' ? (
                          <>
                            <LazyLoadImage
                              src={each.image_small}
                              placeholderSrc="images/landscape_card.png"
                              key={index}
                              className="rail-image"
                              id={`railfocus${index}`}
                            />

                            <div className="progress-bar-continuewatch">
                              <ProgressBar
                                percent={each?.vistime?.last?.progress}
                                size={'small'}
                                sliderWidth={0}
                                smallprogress={smallprogress}
                              />
                            </div>

                            <div className="title-icon-block">
                              <div className="continuewatchingShow-title">
                                <div className="continuewatchingtitle">
                                  {each?.title?.length >= 20
                                    ? `${each?.title?.slice(0, 20)}...`
                                    : each?.title}
                                </div>
                              </div>
                              <div className="deleteIcons">
                                <img
                                  src={'images/Home_icons/red.png'}
                                  className="redDot"
                                />
                                <img
                                  src={'images/Home_icons/delete.png'}
                                  className="delete"
                                />
                              </div>
                            </div>
                          </>
                        ) : (
                          <LazyLoadImage
                            src="images/landscape_card.png"
                            loading="lazy"
                            alt="PlaceHolder"
                            className="rail-image"
                          />
                        )}

                        {/* tags */}

                        {each.image_small && each?.proveedor_code == 'amco' ? (
                          each?.format_types === 'ppe,download' ? (
                            <div className="proveedorBlockRailAlq">
                              <img
                                src={'images/Alquilar.svg'}
                                className="tagAlq"
                              />
                            </div>
                          ) : each?.format_types === 'ppe' ? (
                            <div className="proveedorBlockRailAlq">
                              <img
                                src={'images/Alquilar.svg'}
                                className="tagAlq"
                              />
                            </div>
                          ) : // each?.format_types === 'free' ?
                          // <div className="proveedorBlockRail">
                          //   <img src={'images/verahora.png'} className="tag" />
                          // </div>
                          null
                        ) : each.image_small &&
                          each?.proveedor_code &&
                          each?.image_medium ? (
                          <div className="proveedorBlockRail_vero_hara">
                            {Addproveedor(providerLabel?.[each?.proveedor_code]?.susc) && (
                              <img
                                id="#icon1"
                                className={`${
                                  each?.proveedor_code === 'picardia2'
                                    ? 'picardia-image'
                                    : 'premium-icon'
                                }`}
                                src={Addproveedor(providerLabel?.[each?.proveedor_code]?.susc)}
                              />
                            )}
                            {each?.format_types === 'free' &&
                            userDetails?.subscriptions?.length == 0 ? (
                              <div className="verahora-tag">VER AHORA</div>
                            ) : null}
                            {each.image_small &&
                              each?.proveedor_code === 'picardia2' &&
                              each?.image_medium && (
                                <div className="picardia-proveedor-block-rail-continue">
                                  <img
                                    src={'images/Adultus.svg'}
                                    className="picardia-tag"
                                  />
                                </div>
                              )}
                          </div>
                        ) : null}
                      </button>
                    </>
              ))}
            </div>
          ) : null}
        </div>
    </div>
  )
}

export default ContinueWatching
