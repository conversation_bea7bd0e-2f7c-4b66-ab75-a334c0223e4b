import React, { useCallback, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import { store } from '../../store/sagaStore'
import NpawPlugin from 'npaw-plugin-es5'
import {
  clearLoginUserInfoState,
  getClearAllLoginStates,
  getLoginNavigation,
  getRegisterNavigation,
  getWatchFree
} from '../../store/slices/login'
import { pushScreenViewEvent, pushLandingEvent } from '../../GoogleAnalytics'
//For guest user flow.
import { setNpawPluginInstance } from '../../store/slices/PlayerSlice'
import '../loginregister/landingpage.scss'

const LandingPage = () => {
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const apaAssetsImages = useSelector(state => state?.Images?.imageresponse)
  const youboraInstance = useSelector(
     state => state?.player?.npawPluginInstance
  )
  const region = localStorage.getItem('region')
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const youbora =
    apaMetaData?.youbora_options && JSON?.parse(apaMetaData?.youbora_options)
  const startheaderinfo = useSelector(state => state?.initialReducer?.startHeaderInfo?.response)
  const startTimeRef = useRef(null)
  const navigate = useNavigate()
  const { state } = useLocation()

  useEffect(() => {
    startTimeRef.current = Date.now()
    // GA : ScreenView Event
    state?.backfocusid ? document.getElementById(state?.backfocusid)?.focus() : ''
    pushScreenViewEvent({screenName:'landing',prevScreenName:'splash'})
    SpatialNavigation.focus()
    localStorage.removeItem('isLoginUserTutorialShown')
    store.dispatch(getRegisterNavigation(false))
    store.dispatch(getLoginNavigation(false))
    store.dispatch(clearLoginUserInfoState())
    store.dispatch(getClearAllLoginStates())

    let npawPlugin
    if (!!youbora?.[region] && youbora?.[region]?.analytics?.enabled) {
      const parameters = {
        host: youbora?.[region]?.analytics?.host,
        'components': {'balancer' : false}
      }
      npawPlugin = new NpawPlugin(
        // youbora?.[region]?.analytics?.accountcode,
        //This above line needs to be uncommented, once the access for dashboard is acquired. For now, using default code below.
        'clarovideotatadev',
        parameters
      )
      !youboraInstance && store.dispatch(setNpawPluginInstance(npawPlugin))
    } else {
      npawPlugin = new NpawPlugin('clarovideotatadev')
      !youboraInstance && store.dispatch(setNpawPluginInstance(npawPlugin))
    }
    return () => {
      startTimeRef.current = null
    }
  }, [])

  const handleTranslationchange = useCallback(
    keyname => {
      if (!apilanguage?.[keyname]) {
        return [keyname]
      } else {
        return apilanguage?.[keyname]
      }
    },
    [apilanguage]
  )

  var divImage = {
    backgroundImage: `url(${apaAssetsImages?.landing_access_background})`
  }

  const watchfree = e => {
    e.preventDefault()
    store.dispatch(getWatchFree(true))
    localStorage.removeItem('currNavIdx')
    navigate('/prewelcome',{ 
     state: { 
       pageName:'landing_screen',
      } 
    })
  }

  const handleRegisterClick = () => {
    const registerButton = handleTranslationchange('Onboarding_Landing_TextoBotonPrimario')?.toLowerCase()
    const engagement_time_msec = Date.now() - startTimeRef.current
    // GA : Landing Event
    pushLandingEvent(registerButton,engagement_time_msec)
    navigate('/register')
  }

  const handleSignInClick = () => {
    const loginButton = handleTranslationchange('Onboarding_Landing_TextoBotonIniciaSesion')?.toLowerCase()
    const engagement_time_msec = Date.now() - startTimeRef.current
    // GA : Landing Event
    pushLandingEvent(loginButton,engagement_time_msec)
    navigate('/signin', {
    state: { backfocusid: 'signinId' }
    })
  }

  const handleGuestClick = (e) => {
    const guestButton = handleTranslationchange('landing_menu_option_button_anonymous')?.toLowerCase()
    const engagement_time_msec = Date.now() - startTimeRef.current
    // GA : Landing Event
    pushLandingEvent(guestButton,engagement_time_msec)
    watchfree(e)
  }

  return (
    <div className="landingpage">
      <div className="app-logo" style={divImage}>
        <img
          className="logo-size"
          src={'images/claro-video-logo.png'}
          alt="Logo"
        />
      </div>
      <div className="landingpage-Header">
        {handleTranslationchange('Onboarding_Landing_TextoTitulo')}
      </div>
      <div className="landingpage-Title">
        {handleTranslationchange('Onboarding_Landing_Texto1')}
      </div>
      <div className={'main-buttons'}>
        <div className="landing-register-button">
          <button
            onClick={handleRegisterClick}
            id="registerId"
            className="register-name focusable"
            data-sn-up={'#telmexButton'}
          >
            {handleTranslationchange('Onboarding_Landing_TextoBotonPrimario')}
          </button>
        </div>
        <div className="landing-signin-button ">
          <button
            onClick={handleSignInClick}
            id="signinId"
            className="sign-name focusable"
          >
            {handleTranslationchange(
              'Onboarding_Landing_TextoBotonIniciaSesion'
            )}
          </button>
        </div>
        <div>
          <button
            onClick={handleGuestClick}
            id="watchid"
            className="Watch-name focusable"
          >
            {handleTranslationchange('landing_menu_option_button_anonymous')}
          </button>
        </div>
      </div>
      {/* <div className="activate-button">
        <div className="subscription">{translations?.language?.[region]?.atv_promo_code_sub ?? 'atv_promo_code_sub'}</div>
        <button id='activateid' className="active-name focusable">{translations?.language?.[region]?.landing_menu_option_button_promotionalCode ?? 'landing_menu_option_button_promotionalCode'}</button>
      </div> */}
    </div>
  )
}

export default LandingPage
