.app-css-block-channel {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background-color: #121212;

	.claro-logo {
		height: 34.36px;
		width: 169.64px;
		margin-top: 67px;
		margin-left: 90px;
	}


	.back-indicator-button-pin {
		display: inline-block;
		color: #ffffff;
		width: 290px;
		height: 48px;
		border-radius: 6.6px;
		font-size: 16px;
		background-color: #2e303d;
		vertical-align: middle;
		float: right;
		margin-top: 40px;
		margin-right: 40px;


		.yellow-indicator-button {
			width: 20px;
			height: 20px;
			padding: 0px 24px 0px 24px;
			vertical-align: middle;
		}

		.back-indicator-image {
			width: 35px;
			height: 28px;
			padding: 0px 24px 0px 0px;
			vertical-align: middle;
		}

		span {
			display: inline-block;
			vertical-align: middle;
			font-family: Roboto;
			font-weight: bold;
			font-size: 29px;
			color: #ffffff;
			width: 146px;
			height: 34px;
		}
	}


	.back-indicator:focus,
	.back-indicator-button-pin:focus {
		background-color: #981c15;
	}


	//Change Security Pin unlocked channels

	.left-container-div {
		display: flex;
		position: absolute;
		margin-left: 108px;
		margin-top: 124px;
	}

	.hiding-forgot-pin {
		display: none;
	}

	.right-container-div {
		display: grid;
		position: absolute;
		margin-top: 41px;
		margin-left: 964px;

		.blocked-title {
			height: 72px;
			width: 739px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 60px;
			letter-spacing: 0;
			line-height: 71px;
			margin-left: 50px;
		}

		.sub-title-unlock {
			height: 94px;
			width: 720px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 47px;
			margin-top: 40px;
			margin-left: 50px;
		}

		.pin-box-div {
			display: flex;
			margin-top: 45px;
			margin-left: 50px;
			align-items: center;
			height: 83px;

			.pin-focused {
				height: 72px;
				width: 64px;
				border-radius: 6px;
				background-color: #212224;
				margin-right: 16px;
				font-size: 55px !important;
				text-align: center;
				color: #eeeeee;
				pointer-events: none;
				border: 4px solid #4C6F94;
				box-sizing: border-box;

			}
			.pin-wrapper {
				position: relative;
				display: inline-block;
			  }
			  
			  .pin-cursor {
				position: absolute;
				top: 50%;
				left: 40%;
				transform: translate(-50%, -50%);
				font-size: 24px; /* Small pipe */
				color: #eeeeee;
				animation: blink 1s step-start infinite;
				pointer-events: none;
			  }

			  @keyframes blink {
				50% { opacity: 0; }
			  }
			.pin-field-invalid {
				height: 72px;
				width: 64px;
				border-radius: 6px;
				background-color: #212224;
				margin-right: 16px;
				font-size: 55px !important;
				text-align: center;
				color: #eeeeee;
				pointer-events: none;
				border: 4px solid #981c15;
				box-sizing: border-box;
			}

			.pin-field {
				height: 72px;
				width: 64px;
				border-radius: 6px;
				background-color: #212224;
				margin-right: 16px;
				font-size: 55px !important;
				text-align: center;
				color: #eeeeee;
				border: none;
				pointer-events: none;
			}

			.see-pin-button {
				height: 72px;
				width: 225px;
				border-radius: 6px;
				background-color: #2e303d;
				margin-left: 15px;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.see-pin-button:focus {
				width: 259px;
				height: 83px;
				margin-left: 0px;
			}
		}

		.block-channel-button {
			height: 72px;
			width: 722px;
			border-radius: 6px;
			background-color: #4b1512;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			font-weight: bold;
			letter-spacing: -0.51px;
			line-height: 38px;
			text-align: center;
			margin-top: 26px;
			margin-left: 50px;
		}

		.block-channel-button:focus {
			height: 83px;
			width: 828px;
			border-radius: 6.9px;
			background-color: #981c15;
			margin-left: 0px;
			margin-top: 73px;
		}

		.block-channel-button:disabled {
			opacity: 0.5;
		}

		.block-channel-button-margin {
			margin-top: 84px;
		}

		.block-channel-button-cancel {
			height: 72px;
			width: 722px;
			border-radius: 6px;
			background-color: #2e303d;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			font-weight: bold;
			letter-spacing: -0.51px;
			line-height: 38px;
			text-align: center;
			margin-top: 26px;
			margin-left: 50px;
		}

		.block-channel-button-cancel:focus {
			height: 83px;
			width: 828px;
			border-radius: 6.9px;
			margin-left: 0px;
			margin-top: 17px;
		}

		.block-channel-button-cancel:disabled {
			opacity: 0.5;
		}

		.forgot-pin-button {
			height: 72px;
			width: 342px;
			border-radius: 6px;
			background-color: #212224;
			margin-top: 124px;
			margin-left: 430px;
			display: flex;
			justify-content: center;

			.forgot-pin-content {
				height: 56px;
				width: 319px;
				color: #ffffff;
				font-family: Roboto;
				font-size: 24px;
				letter-spacing: 0;
				line-height: 28px;
				text-align: center;
				position: relative;
				bottom: 15px;
			}
		}

		.forgot-pin-button:focus {
			background-color: #981c15;
		}

		.pin-error {
			border-radius: 6px;
			background-color: #ffffff;
			height: 93px;
			width: 466px;
			display: flex;
			justify-content: center;
			text-align: center;
			align-items: center;
			position: absolute;
			z-index: 1;
			left: 50px;
			bottom: 322px;

			.pin-error-contents {
				color: #981c15;
				font-family: 'Roboto';
				font-size: 31px;
				width: 415px;
			}

		}

		.pin-error:after,
		.pin-error:before {
			left: 214px;
			top: 6px;
			border: solid transparent;
			content: " ";
			width: 0;
			position: absolute;
			pointer-events: none;
		}

		.pin-error:after {
			border-width: 10px 10px 10px 20px;
			border-left: 27px solid #ffffff;
		}


		.pin-error::before {
			content: "";
			position: absolute;
			width: 60px;
			height: 25px;
			z-index: -1;
			right: 528px;
			transform: rotate(43deg);
			background-color: #ffffff;
			bottom: 185px;
		}
	}

	//Security Pin

	.create-pin-title {
		height: 63px;
		color: #ffffff;
		font-family: Roboto;
		font-size: 48px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 76px;
		text-align: center;
		text-shadow: 0 9px 23px 0 rgba(0, 0, 0, 0.5);
		display: block;

	}

	.create-pin-container {
		display: flex;
		justify-items: center;
		align-items: center;
		flex-direction: column;

		.create-pin-sub-titles {
			height: 192px;
			width: 1106px;
			color: #eeeeee;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 43px;
			text-align: center;
			margin-top: 92px;
		}

		.create-pin-button-container {
			display: flex;
			flex-direction: column;

			.create-pin-button {
				height: 72px;
				width: 509px;
				border-radius: 8.8px;
				background-color: #2e303d;
				color: #ffffff;
				font-family: Roboto;
				font-size: 32px;
				font-weight: bold;
				letter-spacing: -0.51px;
				line-height: 35px;
				text-align: center;
				margin-top: 28px;

			}

			.create-pin-button:focus {
				height: 82.08px;
				width: 580.26px;
				border-radius: 10.03px;
				background-color: #981c15;
			}
		}

	}

	.pin-enabled-info-page {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 96px;

		.info-page-title {
			height: 56px;
			width: 1144px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 48px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 56px;
			text-align: center;
		}

		.info-desc-1 {
			height: 240px;
			width: 1120px;
			color: #eeeeee;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 48px;
			text-align: center;
		}

		.info-desc-2 {
			height: 144px;
			width: 1120px;
			color: #eeeeee;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 48px;
			text-align: center;
		}
	}

}