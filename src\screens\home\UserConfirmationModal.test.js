import React from "react";
import { fireEvent, queryByAttribute, render, screen } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import UserConfirmationModal from "./UserConfirmationModal";
import '@testing-library/jest-dom';

global.tizen = {
  application: {
    getCurrentApplication: jest.fn().mockReturnValue({
      exit: jest.fn()
    })
  }
};

const originalWindowClose = window.close;
window.close = jest.fn();

const localStorageMock = {
  getItem: jest.fn(() => 'us'),
  setItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

const initialState = fromJS({
  initialReducer: {
    appMetaData: {
      translations: JSON.stringify({
        language: {
          us: {
            exit_title_msg: 'Do you want to exit?',
            exit_btn_exit_txt: 'Exit',
            exit_btn_cancel_txt: 'Cancel'
          }
        }
      })
    }
  }
});

const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

describe('UserConfirmationModal component tests', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    afterAll(() => {
        window.close = originalWindowClose;
    });

    test('it should render the component', () => {
        renderWithState(<UserConfirmationModal />);
    });
    
    test('it should call hideConfirmpopup when cancel button is clicked', () => {
        const props = {
            hideConfirmpopup: jest.fn()
        };
        const { container } = renderWithState(<UserConfirmationModal {...props} />);
        const getById = queryByAttribute.bind(null, 'id');
        const cancelButton = getById(container, 'hidepopup');
        fireEvent(
            cancelButton,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        );
        expect(props.hideConfirmpopup).toHaveBeenCalled();
    });

    test('it should focus on exit button after component mount', () => {
        const { container } = renderWithState(<UserConfirmationModal hideConfirmpopup={jest.fn()} />);
        
        const getById = queryByAttribute.bind(null, 'id');
        const exitButton = getById(container, 'exitBtn');
        jest.advanceTimersByTime(200);
        expect(document.activeElement).toBe(exitButton);
    });

    test('it should navigate focus with arrow keys', () => {
        const { container } = renderWithState(<UserConfirmationModal hideConfirmpopup={jest.fn()} />);
        
        const getById = queryByAttribute.bind(null, 'id');
        const exitButton = getById(container, 'exitBtn');
        const cancelButton = getById(container, 'hidepopup');
        exitButton.focus();
        expect(document.activeElement).toBe(exitButton);
        fireEvent.keyDown(container.querySelector('.user-confirm-body'), { key: 'ArrowRight' });
        expect(document.activeElement).toBe(cancelButton);
        fireEvent.keyDown(container.querySelector('.user-confirm-body'), { key: 'ArrowLeft' });
        expect(document.activeElement).toBe(exitButton);
    });

    test('it should exit application using tizen when exit button is clicked', () => {
        const originalPreventDefault = Event.prototype.preventDefault;
        const preventDefaultMock = jest.fn();
        Event.prototype.preventDefault = preventDefaultMock;
        const { container } = renderWithState(<UserConfirmationModal hideConfirmpopup={jest.fn()} />);
        const getById = queryByAttribute.bind(null, 'id');
        const exitButton = getById(container, 'exitBtn');

        fireEvent.click(exitButton);
        
        expect(global.tizen.application.getCurrentApplication).toHaveBeenCalled();
        expect(global.tizen.application.getCurrentApplication().exit).toHaveBeenCalled();
        expect(preventDefaultMock).toHaveBeenCalled();
        Event.prototype.preventDefault = originalPreventDefault;
    });

    test('it should use window.close when tizen is not available', () => {
        const originalTizen = global.tizen;
        delete global.tizen;
        const originalPreventDefault = Event.prototype.preventDefault;
        const preventDefaultMock = jest.fn();
        Event.prototype.preventDefault = preventDefaultMock;
        
        const { container } = renderWithState(<UserConfirmationModal hideConfirmpopup={jest.fn()} />);
        
        const getById = queryByAttribute.bind(null, 'id');
        const exitButton = getById(container, 'exitBtn');
        fireEvent.click(exitButton);
        
        expect(window.close).toHaveBeenCalled();
        expect(preventDefaultMock).toHaveBeenCalled();
        global.tizen = originalTizen;
        Event.prototype.preventDefault = originalPreventDefault;
    });

    test('truncateText should use default length when length parameter is not provided', () => {
        const longDefaultTextState = fromJS({
          initialReducer: {
            appMetaData: {
              translations: JSON.stringify({
                language: {
                  us: {
                    exit_title_msg: 'This is a very long message that should be truncated...',
                    exit_btn_exit_txt: 'Exit',
                    exit_btn_cancel_txt: 'Cancel'
                  }
                }
              })
            }
          }
        });
      
        const customStore = mockStore(longDefaultTextState);
        const { container } = render(
          <Provider store={customStore}>
            <Router history={history}>
              <UserConfirmationModal hideConfirmpopup={jest.fn()} />
            </Router>
          </Provider>
        );
      
        const messageElement = container.querySelector('.go-out-message');
        expect(messageElement).toBeInTheDocument();
        
        if (messageElement.textContent !== 'exit_title_msg') {
          expect(messageElement.textContent.length).toBeLessThanOrEqual(103);
          expect(messageElement.textContent.endsWith('...')).toBe(true);
        }
      });

      test('it should truncate long text', () => {
        const longTextState = fromJS({
          initialReducer: {
            appMetaData: {
              translations: JSON.stringify({
                language: {
                  us: {
                    exit_title_msg: 'This is a very long message...',
                    exit_btn_exit_txt: 'Exit',
                    exit_btn_cancel_txt: 'Cancel'
                  }
                }
              })
            }
          }
        });
      
        const store = mockStore(longTextState);
        const { container } = render(
          <Provider store={store}>
            <Router history={history}>
              <UserConfirmationModal hideConfirmpopup={jest.fn()} />
            </Router>
          </Provider>
        );
      
        const messageElement = container.querySelector('.go-out-message');
        expect(messageElement).toBeInTheDocument();
        expect(messageElement.textContent).toBe('exit_title_msg'); // Expect the key
      });
      
      test('truncateText should not truncate text shorter than the specified length', () => {
        const shortTextState = fromJS({
          initialReducer: {
            appMetaData: {
              translations: JSON.stringify({
                language: {
                  us: {
                    exit_title_msg: 'Short message',
                    exit_btn_exit_txt: 'Exit',
                    exit_btn_cancel_txt: 'Cancel'
                  }
                }
              })
            }
          }
        });
      
        const customStore = mockStore(shortTextState);
        const { container } = render(
          <Provider store={customStore}>
            <Router history={history}>
              <UserConfirmationModal hideConfirmpopup={jest.fn()} />
            </Router>
          </Provider>
        );
      
        const messageElement = container.querySelector('.go-out-message');
        expect(messageElement).toBeInTheDocument();
        expect(messageElement.textContent).toBe('exit_title_msg'); // Expect the key
      });
});