import { createSlice } from '@reduxjs/toolkit'

export const epgChannelSlice = createSlice({
  name: 'epgChannel',
  initialState: {
    epgChannel: [],
    isLoading: false,
    error: {},
  },
  reducers: {
    getEpgChannel: state => {
      state.isLoading = true
    },
    getEpgChannelSuccess: (state, action) => {
      state.epgChannel = action?.payload?.response?.channels
      state.isLoading = false
    },
    getEpgChannelError: (state, action) => {
      state.isLoading = false
      state.error = action.payload
    },
  },
})

export const { getEpgChannel, getEpgChannelSuccess, getEpgChannelError } =
  epgChannelSlice.actions

export default epgChannelSlice.reducer
