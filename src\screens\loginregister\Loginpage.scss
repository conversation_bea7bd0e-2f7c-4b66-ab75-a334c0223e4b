$position-center: center;
$color-white: #ffffff;
$background-color: #333547;
$white-space-nowrap: nowrap;
$overflow-hidden: hidden;
$text-overflow-ellipsis: ellipsis;

.App {
    top: 0px;
    left: 0px;
    width: 1920px;
    position: fixed;
    height: 1080px;
}

.App-logo {
    top: -1px;
    left: 0px;
    width: 1920px;
    height: 104px;
}

.logo-img {
    margin-top: 20px;
    margin-left: 60px;
    height: 121px;
    opacity: 1;
}

.loginpage-height {
    height: 522px;
}

.login-Header {
    margin-top: 10px;
    left: 873px;
    height: 57px;
    font-weight: 'Regular';
    text-align: $position-center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.choose-login-option {
    height: 86px;
    width: 770px;
    background-color: #1d1e26;
    margin-left: 566px;
    border-radius: 44px;
}

.login-mobile {
    color: $color-white;
    height: 55px;
    font-size: 26px;
    margin-top: 16px;
    margin-left: 23px;
}

.login-Remote {
    color: $color-white;
    font-size: 26px;
    margin-left: 51px;
    text-align: $position-center;
}

.login-Remote:focus {
    background-color: $background-color;
    border-radius: 32px;
    width: 369px;
    height: 68px;
    margin-left: 41px;
}

.login-Remote.active {
    background-color: $background-color;
    border-radius: 32px;
    width: 369px;
    height: 68px;
    margin-left: 41px;
}

.login-mobile:focus {
    background-color: $background-color;
    border-radius: 31px;
}

.login-title {
    margin-top: 147px;
}

.login-page-email {
    margin-top: 63px;
    margin-left: 618px;
    width: 109px;
    height: 33px;
    text-align: left;
    font: normal normal normal 40px/33px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.login-namereg {
    margin-top: 32px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    width: 564px;
    height: 54px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: $color-white;
    border: 3px;
    border-radius: 6px;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.email-redborder {
    margin-top: 32px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    width: 564px;
    height: 54px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: $color-white;
    border: 3px;
    border-radius: 6px;
}

.loginmail-placeholder {
    margin-top: 32px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    width: 564px;
    height: 54px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: #999;
    border: 3px;
    border-radius: 6px;
}

.login-namereg:focus,
.login-password:focus,
.loginmail-placeholder:focus,
.loginpassword-placeholder:focus {
    border: 3px solid #4c6f94;
    height: 45px;
    width: 557px;
}

.email-redborder:focus,
.password-redborder:focus {
    margin-top: 32px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    border: 3px solid #981c15;
    height: 45px;
    width: 557px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: $color-white;
    border-radius: 6px;
}

.loginbutton-height {
    height: 236px;
}

.login-password {
    margin-top: 23px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    width: 564px;
    height: 54px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: $color-white;
    border-radius: 6px;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.password-redborder {
    margin-top: 23px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    width: 564px;
    height: 54px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: $color-white;
    border-radius: 6px;
}

.loginpassword-placeholder {
    margin-top: 33px;
    margin-left: 600px;
    padding: 13px 119px 13px 26px;
    width: 564px;
    height: 54px;
    background-color: #212224;
    font: normal normal normal 30px/35px Roboto;
    opacity: 1;
    color: #999;
    border-radius: 6px;
}

/* .error-box {
    margin-top: 7px
} */
.login-error-box:before {
    border-right-color: red;
    border-width: 0;
    margin-top: 0;
}
.login-error-box{
    position: absolute;
    left: 601px;
    top: 487px;

.login-invalid-email {
    width: 709px;
    height: 77px;
    background: #eeeeee 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
    font-size: 28px;
    text-align: $position-center;
    letter-spacing: -0.45px;
    color: #981c15;
    opacity: 1;
    position: absolute;
}
.login-err-msg{
    margin-top: 18px;
    margin-left: 69px;
    width: 570px;
  }
  .login-err-msg-text{
    margin-left: 99px;
    width: 517px;
  }

.login-invalid-email:after,
.login-invalid-email:before {
    left: 345px;
    top: -38.4%;
    border: solid transparent;
    content: ' ';
    width: 0;
    position: absolute;
    pointer-events: none;
}

.login-invalid-email:after {
    border-width: 12px 14px 10px 15px;
    border-bottom: 27px solid #eeeeee;
}

.login-invalid-text {
    width: 709px;
    height: 120px;
    background: #eeeeee 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
    font-size: 28px;
    text-align: $position-center;
    letter-spacing: -1.05px;
    color: #981c15;
    opacity: 1;
    position: absolute;
    font-family: 'Roboto';
    font-weight: 500;
}

.login-invalid-text:after,
.login-invalid-text:before {
    left: 345px;
    bottom: 117px;
    border: solid transparent;
    content: ' ';
    width: 0;
    position: absolute;
    pointer-events: none;
}

.login-invalid-text:after {
    border-width: 12px 14px 10px 15px;
    border-bottom: 27px solid #eeeeee;
}
}
.invisible {
    display: none;
}
.loginpage-next-enable{
    color: #ffffff !important;
    background: #981c15 0% 0% no-repeat padding-box !important;
}
.loginpage-next,.loginpage-next-enable {
    margin-top: 23px;
    margin-left: 597px;
    width: 715px;
    font-size: 30px;
    font-family: 'Roboto';
    font-weight: bold;
    height: 80px;
    color: #787878;
    background: #4b1512 0% 0% no-repeat padding-box;
    opacity: 1;
    text-align: $position-center;
    border-radius: 6px;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;

    &:disabled {
        color: #787878;
    }
}

.loginpage-next:focus,.loginpage-next-enable:focus {
    width: 772px;
    margin-left: 568px;
    color: #ffffff;
    background: #981c15 0% 0% no-repeat padding-box;
}

.loginpage-clear {
    margin-top: 33px;
    margin-left: 597px;
    width: 719px;
    font-size: 30px;
    font-family: 'Roboto';
    font-weight: bold;
    height: 80px;
    color: #787878;
    background: #2e303d 0% 0% no-repeat padding-box;
    opacity: 1;
    text-align: $position-center;
    border-radius: 6px;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.loginpage-clear:focus {
    width: 772px;
    margin-left: 568px;
    color: #ffffff;
}

.sign-registerreg {
    display: flex;
    justify-content: center;
    margin-top: 29px;
    margin-left: 362px;
}

.sign-register {
    width: 343px;
    height: 72px;
    color: #ffffff;
    text-align: center;
    font: normal normal normal 25px / 30px Roboto;
    background-color: #212224;
    border-radius: 6px;
}

.sign-register:focus {
    width: 370px;
    margin-right: 20px;
}

/*forgot password*/
.recpassword-Header {
    margin-top: 15px;
    margin-left: 655px;
    width: 581px;
    height: 57px;
    text-align: center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}
.recpassword-main{
    margin-top: 184px;
}

        .loginn-error-box {
            position: absolute;
            left: 593px;
            top: 521px;
    
            .login-invalid-email {
                width: 709px;
                height: 95px;
                background: #eeeeee 0% 0% no-repeat padding-box;
                border-radius: 6px;
                opacity: 1;
                font-size: 28px;
                text-align: $position-center;
                letter-spacing: -0.45px;
                color: #981c15;
                opacity: 1;
                position: absolute;
            }
    
            .login-err-msg {
                margin-top: 25px;
                margin-left: 99px;
                width: 517px;
            }
            .login-err-msg-text{
                margin-left: 99px;
                width: 517px;
            }
    
            .login-invalid-email:after,
            .login-invalid-email:before {
                left: 345px;
                bottom: 87px;
                border: solid transparent;
                content: ' ';
                width: 0;
                position: absolute;
                pointer-events: none;
            }
    
            .login-invalid-email:after {
                border-width: 12px 14px 10px 15px;
                border-bottom: 27px solid #eeeeee;
            }
    
            .login-invalid-text {
                width: 709px;
                height: 85px;
                background: #eeeeee 0% 0% no-repeat padding-box;
                border-radius: 6px;
                opacity: 1;
                font-size: 28px;
                text-align: $position-center;
                letter-spacing: -0.45px;
                color: #981c15;
                opacity: 1;
                position: absolute;
            }
    
            .login-invalid-text:after,
            .login-invalid-text:before {
                left: 345px;
                bottom: 77px;
                border: solid transparent;
                content: ' ';
                width: 0;
                position: absolute;
                pointer-events: none;
            }
    
            .login-invalid-text:after {
                border-width: 12px 14px 10px 15px;
                border-bottom: 27px solid #eeeeee;
            }
        }
.login-forgot-email-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.login-forgot-email-btn {
    display: flex;
    justify-content: center;
    align-items: center;
}

.recpassword-Title {
    margin-top: 51px;
    height: 35px;
    text-align: center;
    font: normal normal normal 32px/30px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.recpassword-Header-email {
    margin-top: 15px;
    margin-left: 658px;
    width: 581px;
    height: 57px;
    text-align: center;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.recregister-namerec {
    margin-left: 602px;
    padding: 13px 119px 13px 30px;
    width: 538px;
    height: 42px;
    background-color: #212224;
    border: 2px solid #34353b;
    border-radius: 6px;
    text-align: left;
    font: normal normal normal 30px/35px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.recregister-namerec-mail {
    padding: 12px 10px 12px 13px;
    width: 832px;
    height: 42px;
    background-color: #212224;
    border: 2px solid #34353b;
    border-radius: 6px;
    text-align: left;
    font: normal normal normal 30px/35px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.recregister-namerec:focus {
    border: 2px solid #4c6f94;
}
.recregister-namerec-redborder {
    margin-left: 602px;
    padding: 12px 10px 12px 13px;
    width: 660px;
    height: 42px;
    background-color: #212224;
    border: 2px solid #34353b;
    border-radius: 6px;
    text-align: left;
    font: normal normal normal 30px/35px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.recregister-namerec-redborder:focus {
    border: 3px solid #981c15;
    margin-left: 602px;
    padding: 12px 10px 12px 13px;
    width: 660px;
    height: 42px;
    background-color: #212224;
    border-radius: 6px;
    text-align: left;
    font: normal normal normal 30px/35px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.recregister-namerec-mail:focus {
    border: 2px solid #4c6f94;
    width: 832px;
}

.message-next {
    width: 695px;
    height: 75px;
    background: #4b1512 0% 0% no-repeat padding-box;
    border-radius: 6px;
    font-size: 32px;
    font-weight: bold;
    font-family: 'Roboto';
    line-height: 35px;
    letter-spacing: -0.51px;
    text-align: center;
    color: #ffffff;
    text-overflow: ellipsis;
}

.container-ent {
    position: relative;
}

.center-ent {
    position: absolute;
    margin-top: 157px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.message-next:focus {
    background: #981c15 0% 0% no-repeat padding-box;
    border: 0px solid #981c15;
    width: 780px;
}

.recsign-next-email {
    margin-top: 42px;
    width: 720px;
    height: 75px;
    background: #4b1512 0% 0% no-repeat padding-box;
    border-radius: 6px;
    font-size: 34px;
    font-family: 'Roboto';
    font-weight: bold;
    letter-spacing: -0.58px;
    line-height: 42.18px;
    text-align: $position-center;
    color: #787878;
    opacity: 1;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.recsign-cancle {
    margin-top: 28px;
    margin-left: 599px;
    width: 695px;
    height: 72px;
    background: #2e303d 0% 0% no-repeat padding-box;
    border-radius: 6px;
    font-size: 34px;
    font-family: 'Roboto';
    letter-spacing: 0px;
    text-align: $position-center;
    color: $color-white;
    opacity: 1;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.recsign-cancle:focus {
    margin-left: 553px;
    width: 780px;
}

.recsign-next-email:focus {
    background: #981c15 0% 0% no-repeat padding-box;
    color: $color-white;
    width: 830px;
}

/*message*/
.mail-notification{
    text-align: center;
    font-size: 48px;
    color: #ffffff;
    font-family: 'Roboto';
    opacity: 1;
}
.message-Header {
    line-height: 61px;
    letter-spacing: 0px;
    font-weight: bold;
}
.message-subHeader{
    font-weight: bold;
    position: relative;
    bottom: 36px;
}
.meaasge-Title {
    width: 600px;
    height: 40px;
    text-align: center;
    line-height: 42px;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
    font-size: 36px;
    font-family: 'Roboto';
    position: relative;
    left: 660px;
}

.forget-password-msg {
    color: white;
    font-size: 48px;
    text-align: center;
    height: 60px;
    // margin-top: 0px;
}

.recsign-next {
    margin-top: 28px;
    margin-left: 603px;
    width: 691px;
    height: 75px;
    background: #4b1512 0% 0% no-repeat padding-box;
    border-radius: 6px;
    font-size: 34px;
    font-family: 'Roboto';
    font-weight: bold;
    letter-spacing: 0px;
    text-align: $position-center;
    color: #787878;
    opacity: 1;
    white-space: $white-space-nowrap;
    overflow: $overflow-hidden;
    text-overflow: $text-overflow-ellipsis;
}

.recsign-next:focus {
    background: #981c15 0% 0% no-repeat padding-box;
    border: 0px solid #981c15;
    color: #ffffff;
    margin-left: 553px;
    width: 780px;
}

/*Login Email*/

.login-email-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 100px;
}

.sign-in-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 94px;
}

.login-email {
    height: 56px;
    width: 960px;
    text-align: center;
    font-family: 'Roboto';
    padding-bottom: 24px;
    font-size: 40px;
    line-height: 48px;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
}

.forgotmail-placeholder {
    margin-left: 602px;
    padding: 13px 119px 13px 30px;
    width: 538px;
    height: 42px;
    background-color: #212224;
    border: 2px solid #34353b;
    border-radius: 6px;
    text-align: left;
    font: normal normal normal 30px/35px Roboto;
    letter-spacing: 0px;
    opacity: 1;
    color: #999;
}

.forgotmail-placeholder:focus {
    border: 2px solid #4c6f94;
}

.login-email-input {
    width: 759px;
    padding: 13px 49px 13px 24px;
    height: 51px;
    background-color: #212224;
    font: normal normal normal 30px / 35px Roboto;
    opacity: 1;
    border-radius: 6px;
    color: #ffffff;
    border: none;

    &:focus {
        width: 759px;
        border-radius: 6px !important;
        border: 3px solid #4c6f94 !important;
        outline: 0px solid #4c6f94 !important;
    }
}

.loginmail-signature:focus {
    background: #981c15 0% 0% no-repeat padding-box;
    color: #ffffff;
    width: 830px;
}

.loginmail-signature,.loginmail-signature-enable {
    width: 720px;
    font-size: 32px;
    height: 75px;
    line-height: 35px;
    color: #787878;
    font-family: 'Roboto';
    font-weight: bold;
    background: #4b1512 0% 0% no-repeat padding-box;
    opacity: 1;
    border-radius: 8.8px;
    text-align: center;
    letter-spacing: -0.51px;
    white-space: nowrap;
}
.loginmail-signature-enable{
    width: 830px !important;
    color: #ffffff !important;
    background: #981c15 0% 0% no-repeat padding-box !important;
}

/*loginpassword*/
.password-height {
    display: flex;
    justify-content: center;
    align-items: center;
}

.password-Header {
    margin-top: 60px;
    margin-left: 810px;
    height: 57px;
    text-align: left;
    font: normal normal normal 48px/57px Roboto;
    letter-spacing: 0px;
    color: $color-white;
    opacity: 1;
}

.password-Title {
    display: flex;
    margin-top: 60px;
    justify-content: center;
    height: 35px;
    font: normal normal normal 40px / 30px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    opacity: 1;
}

.Passwordtlt {
    margin-top: 64px;
    margin-left: 602px;
    width: 124px;
    height: 33px;
    text-align: left;
    font: normal normal normal 28px/33px Roboto;
    letter-spacing: 0px;
    color: #a4a9ae;
    opacity: 1;
}

.logininput-password {
    margin-right: 23px;
    padding: 0px 102px 0px 20px;
    width: 446px;
    height: 72px;
    background: #212224 0% 0% no-repeat padding-box;
    text-align: left;
    font: normal normal normal 30px / 35px Roboto;
    letter-spacing: 0px;
    color: #ffffff;
    border-radius: 6px;
    opacity: 1;
    border: none;
    text-decoration: none !important;
}

.logininput-password:focus {
    outline: 0px solid #4c6f94 !important;
    text-decoration: none !important;
    width: 446px;
    border: 3px solid #4c6f94 !important;
    border-radius: 6px !important;
}

.hidesymbol:focus {
    width: 259px;
    height: 83px;
    right: 14px;
    .imagesignIcon{
        margin-left: 97px;
    }
}

.hidesymbol {
    width: 225px;
    height: 72px;
    background-color: #2e303d;
    border-radius: 6px;
    position: relative;
}

.imagesignIcon {
    width: 60px;
    height: 48px;
    margin-top: 1px;
    margin-left: 85px;
}

.invalid-text-pass {
    top: 435px;
    left: 143px;
    width: 670px;
    background: #eeeeee 0% 0% no-repeat padding-box;
    border-radius: 6px;
    opacity: 1;
    font-size: 28px;
    padding: 21px;
    text-align: center;
    letter-spacing: -0.45px;
    color: #981c15;
    opacity: 1;
    margin-top: 170px;
    margin-left: 455px;
    position: absolute;
}

.invalid-text-pass:after,
.invalid-text-pass:before {
    left: 328px;
    bottom: 68px;
    border: solid transparent;
    content: ' ';
    width: 0;
    position: absolute;
    pointer-events: none;
}

.invalid-text-pass:after {
    border-width: 10px 15px 10px 15px;
    border-bottom: 27px solid #eeeeee;
}
.sign-In-enable {
    background: #981c15 0% 0% no-repeat padding-box !important;
    color: #ffffff !important;
    width: 830px !important;
}
.sign-In,.sign-In-enable {
    width: 720px;
    height: 85px;
    background: #4b1512 0% 0% no-repeat padding-box;
    opacity: 1;
    text-align: center;
    font-size: 32px;
    color: #787878;
    font-family: 'Roboto';
    font-weight: bold;
    line-height: 35px;
    letter-spacing: -0.51px;
    border-radius: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sign-In:focus {
    background: #981c15 0% 0% no-repeat padding-box;
    color: #ffffff;
    width: 830px;
}