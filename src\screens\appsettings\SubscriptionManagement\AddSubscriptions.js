import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  getNewPlanSelector,
  getViewSubscribeData
} from '../../../store/slices/settingsSlice'
import { getPremiumCardFocus } from '../../../store/slices/HomeSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import './AddSubscriptions.scss'
import { pushSubscriptionEvent } from '../../../GoogleAnalytics'
import { SUBSCRIPTION_CAROUSEL } from '../../../GoogleAnalyticsConstants'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}
const AddSubscriptions = props => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { state } = useLocation()
  const region = localStorage.getItem('region')

  const viewPlanRef = useRef(null)
  const focusIndexRef = useRef(null)


  const [viewPlan, setViewPlan] = useState('')
  const [focusedIndex, setFocusedIndex] = useState(null)
  const [focusContent, setFocusContent] = useState(false)

  const addSubscriptions = useSelector(state => state?.settingsReducer?.getPlanSelector?.offers)
  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
  const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)  
  const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)
  const apilanguage = translations?.language?.[region]

  const subscriptionData = []


  const handleSubscribe = (e, data, index) => {
    dispatch(getPremiumCardFocus(`index${props?.index}${index}`))
    dispatch(getViewSubscribeData(data))
      // GA : select plan Event
    const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys( userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
        content_section : navbarTab
      }
    pushSubscriptionEvent(userData,data,index,SUBSCRIPTION_CAROUSEL)
    navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new', {
      state: {
        pageName: '/home',
        focusIndex: focusIndexRef.current
      }
    })
  }

  const handlePlanFocus = (e, data, index) => {
    e.preventDefault()
    viewPlanRef.current = data
    focusIndexRef.current = index
    setViewPlan(data)
    setFocusedIndex(index)
    setFocusContent(true)
  }

  const handlePlanBlur = () => {
    setFocusContent(false)
  }

  const truncateText = (str, length) => {
    const text = apilanguage?.[str] ?? str
    if (!length) {
      length = 100
    }
    if (text?.length >= length) {
      return `${text?.slice(0, length)}...`
    } else {
      return text
    }
  }
  addSubscriptions?.map(each =>
    subscriptionData.push({
      bannerUrl: each?.assets?.background?.["165x394"],
      logo: each?.assets?.logo?.['165x40'],
      family: each?.producttype,
      periodicity: each?.translation?.labelTemporality,
      price: each?.price?.amount,
      currency: each?.price?.currency,
      styles: each?.translation?.btnSuscription?.fgColor,
      workflowStart: each?._links?.checkout?.href,
      taxLabel: each?.translation?.labelTaxes,
      infoString: each?.translation?.txtPromo?.texts?.map(t => t?.text).join(" "),
      subscribeButton: each?.translation?.btnSuscription?.texts?.[0]?.text,
      viewButton: each?.translation?.btnFeatures?.texts?.[1]?.text,
      producttype: each?.producttype
    })
  )

  const handleViewDetails = () => {
    dispatch(getViewSubscribeData(viewPlanRef.current))
    navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
      state: {
        pageName: '/home',
        focusIndex: focusIndexRef.current

      }
    })
  }

  useEffect(() => {
    dispatch(getNewPlanSelector({
      userId: userDetails?.parent_id,
      hks: userDetails?.session_stringvalue,
      url: props?.apiUrl,
    }))
    !props.fromHome && document?.getElementById(`index${props?.index}0`)?.focus()
    state?.focusIndex && document?.getElementById(`index${props?.index}${state?.focusIndex}`)?.focus()
  }, [props?.apiUrl])


  const keyPressFunc = useCallback(event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow', 'ColorF3Blue'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
        bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('keydown', keyPressFunc)
    return () => {
      document.removeEventListener('keydown', keyPressFunc)
    }
  }, [keyPressFunc])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      if (state?.pageName == 'home') {
        navigate('/home')
      }
      else {
        navigate(state?.pageName, {
          state: {
            data: state?.data,
            vodData: state?.vodData,
            pageName: state?.previousPage
          }
        })
      }
    }
    else if (key.bluecode === keycode) {
      handleViewDetails()
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode === 461 || keycode == 'backClick' || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      if (state?.pageName == 'home') {
        navigate('/home')
      }
      else {
        navigate(state?.pageName, {
          state: {
            data: state?.data,
            vodData: state?.vodData,
            pageName: state?.previousPage
          }
        })
      }
    }
    else if (keycode == 406 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 121)) {
      handleViewDetails()
    }
  }

  useEffect(() => {
    const element = document.getElementById(`index${props?.index}${focusedIndex}`)
    if (focusContent) {
      element && element.scrollIntoView({ behavior: 'smooth' })
      element?.focus()
    }
  }, [focusContent, focusedIndex])

  return (
    <div>
      {
        subscriptionData.length > 0 && (
          <div className="plan-selector-title">
            <SafeHTML html={props?.title || ''} />
          </div>
        )}

      {subscriptionData.length > 0 && (
        <div
          className="add-subscription-container"
          id='addSubscriptionId'
        >
          {subscriptionData?.map((each, index, array) =>
            each?.bannerUrl ? (
              <div className="add-subscription-block">
                <div>
                  <button
                    className="add-subscription-card focusable"
                    key={index}
                    id={`index${props?.index}${index}`}
                    onClick={e => handleSubscribe(e, each, index)}
                    onFocus={e => handlePlanFocus(e, each, index)}
                    onBlur={handlePlanBlur}
                    data-sn-left={index == 0 ? '' : undefined}
                    data-sn-right={index != array.length - 1 && undefined}
                    data-sn-up={
                      document.getElementById(`index${props?.index-1}0`) ? 
                      `#index${props?.index-1}0` : undefined
                      }
                  >
                    <div className="sub-meta-data">
                      <div className="add-subcription-logo-container">
                        <LazyLoadImage
                          className="add-subcription-logo"
                          src={each?.logo}
                        />
                      </div>

                      <div className="sub-pricing-info">
                        <span
                          className="sub-price-type"
                        >
                          {each?.currency}
                        </span>
                        <span
                          className="sub-price-css"
                        >
                          {each?.price}
                        </span>
                        {/* <span className="sub-symbol">{truncateText('price_subscriptionDescription_slash_label', 2)}</span> */}
                        <span className="sub-periodicity">
                          {each?.periodicity}
                        </span>
                      </div>
                      <div className="sub-tax-label">{each?.taxLabel}</div>
                      <div className="sub-free-chargestr-container">
                        <div className="sub-free-chargestr">{each?.infoString}</div>
                      </div>
                      <div className="sub-button-div">
                        <div
                          className="sub-button"
                          style={{ backgroundColor: each?.styles }}
                          id="subscribe-button"
                        >
                          <div className='sub-button-span'>
                            {/* <span style={{ color: each?.styles }}> */}
                              {each?.subscribeButton}
                            {/* </span> */}
                          </div>
                        </div>
                      </div>
                    </div>
                    <LazyLoadImage
                      className="addsubscription-banner"
                      src={each?.bannerUrl}
                    />
                  </button>
                </div>
                {focusedIndex === index && (
                  <button
                    className="sub-view-plan-button focusable"
                    onClick={handleViewDetails}
                    data-sn-left={index == 0 ? '' : undefined}
                    data-sn-right={index != array.length - 1 && undefined}
                    id="view-details"
                    style={{width: focusContent ? 292 : 248}}
                  >
                    <LazyLoadImage
                      src={'images/blue_shortcut.png'}
                      className="subscription-blueShortcut"
                    />
                    <span className="view-button-content">
                      <div className='view-button-sub-div'>
                        <span>{each?.viewButton}</span> 
                      </div>
                    </span>
                  </button>
                )}
              </div>
            ) : null
          )}
        </div>
      )}
    </div>
  )
}
export default AddSubscriptions