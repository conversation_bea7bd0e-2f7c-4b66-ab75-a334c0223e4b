import epgChannel<PERSON>lice, {
    getEpgChannel,
    getEpgChannelSuccess,
    getEpgChannelError
  } from './EpgChannelSlice';

  
  describe('epgChannelSlice', () => {
    const initialState = {
      epgChannel: [],
      isLoading: false,
      error: {}
    };
  
    it('should handle initial state', () => {
      expect(epgChannelSlice(undefined, { type: 'unknown' })).toEqual(initialState);
    });
  
    it('should handle getEpgChannel', () => {
      const nextState = epgChannelSlice(initialState, getEpgChannel());
      expect(nextState).toEqual({
        epgChannel: [],
        isLoading: true,
        error: {}
      });
    });
  
    it('should handle getEpgChannelSuccess', () => {
      const response = [
        [
            {
                "id": "20861",
                "group": {
                    "common": {
                        "id": "1147425",
                        "title": "UNO TV"
                    }
                },
               
            },         
        ]
      ]
      const action = {
        payload: { response }
      };
      const nextState = epgChannelSlice(initialState, getEpgChannelSuccess(action));
      expect(nextState).toEqual({
        epgChannel: response.channels,
        isLoading: false,
        error: {}
      });
    });

    it('should handle getEpgChannelError', () => {
      const error = { message: 'Error' };
      const nextState = epgChannelSlice(initialState, getEpgChannelError(error));
      expect(nextState).toStrictEqual({
        epgChannel: [],
        isLoading: false,
        error
      });
    });
  });