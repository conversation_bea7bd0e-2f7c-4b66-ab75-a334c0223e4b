import React from 'react'
import { fireEvent, queryByAttribute, render } from '@testing-library/react/'
import { Provider } from 'react-redux'
import 'regenerator-runtime/runtime'
import { BrowserRouter as Router } from 'react-router-dom'
import configureStore from 'redux-mock-store'
import { createHashHistory as createHistory } from 'history'
import { fromJS } from 'immutable'
import TalentSearch from './TalentSearch'
 
const initialState = fromJS({})
const mockStore = configureStore([])
const history = createHistory()
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
)
export const renderWithState = ui => {
    return render(ui, { wrapper: Wrapper })
}
 
const talentsearchmock = [
    {
        "id": "557821",
        "title": "El hombre lobo",
        "title_uri": "El-hombre-lobo",
        "title_original": "<PERSON><PERSON>, The",
        "description": "Un hombre investiga el salvaje asesinato de su hermano vinculado a una bestia de leyenda.",
        "image_large": "http://clarovideocdn9.clarovideo.net/PELICULAS/WOLFMANTHE/EXPORTACION_WEB/SS/WOLFMANTHEWHORIZONTAL.jpg?size=529x297",
        "duration": "01:42:15",
        "date": "02/07/2020",
        "year": "2010",
        "preview": "true",
        "format_types": "ppe,download",
        "rating_code": "R",
        "proveedor_name": "AMCO",
        "proveedor_code": "amco",
        "is_series": false,
        "type": "P"
    },
]
describe('landing page test', () => {
    test('it should render the landing Page', () => {
        renderWithState(<TalentSearch />)
    })
    test('it should render the landing Page', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        const props = {
            talentSearchData:talentsearchmock
        }
        initialState.search = {
            talentSearch: talentsearchmock
        }
        initialState.search = {
            progressbarContent: talentsearchmock
        }
        const { container } = renderWithState(<TalentSearch {...props}/>)
        const getById = queryByAttribute.bind(null, 'id')
        const buttonClick = getById(container, 'vodMoviesRecommend-0')
        fireEvent(
            buttonClick,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })

    test('it should render the landing Page', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        const props = {
            talentSearchData:talentsearchmock
        }
        initialState.search = {
            talentSearch: talentsearchmock
        }
        initialState.search = {
            progressbarContent: talentsearchmock
        }
        const { container } = renderWithState(<TalentSearch {...props}/>)
        const getById = queryByAttribute.bind(null, 'id')
        const buttonClick = getById(container, 'button-back-id')
        fireEvent.focus(buttonClick)  
        fireEvent(
            buttonClick,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        )
    })

    test('it should render the landing Page', () => {
        window.HTMLElement.prototype.scrollIntoView = function() {};
        const props = {
            talentSearchData:talentsearchmock
        }
        initialState.search = {
            talentSearch: talentsearchmock
        }
        initialState.search = {
            progressbarContent: talentsearchmock
        }
        const { container } = renderWithState(<TalentSearch {...props}/>)
        const getById = queryByAttribute.bind(null, 'id')
        const buttonClick = getById(container, 'button-back-id')
        fireEvent(
            buttonClick,
            new MouseEvent('focus', {
                bubbles: true,
                cancelable: true
            })
        )
    })
    
    test('When user press the back space it should go the vCard screen2', async () => {
        const wrapper = await renderWithState(<TalentSearch keyParam="search"
            setSearchValue={()=>{}}
            handleIconClick={()=>{}}
            handleClick={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 8,
                charCode: 8
          });
    })
    
    test('it should render the landing Page', async () => {
        window.tizen = {
            tvinputdevice:{
                getKey:()=>{
                    return {
                        code : "#ff0"
                    }
                },
                registerKeyBatch:()=>{
                    
                }
            }
        }
        const wrapper = await renderWithState(<TalentSearch keyParam="search" 
            setSearchValue={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 10009,
                charCode: 10009
          });
    })


    test('it should render the landing Page', async () => {
        window.tizen = {
            tvinputdevice:{
                getKey:()=>{
                    return {
                        code : "#ff0"
                    }
                },
                registerKeyBatch:()=>{
                    
                }
            }
        }
        const wrapper = await renderWithState(<TalentSearch keyParam="search" 
            setSearchValue={()=>{}}
            setCurrentButtonFocus={()=>{}}
            handleBackInfoTab={()=>{}} />)
            fireEvent.keyUp(wrapper.container, {
                key: "Backspace",
                code: "Backspace",
                keyCode: 461,
                charCode: 461
          });
    })
})