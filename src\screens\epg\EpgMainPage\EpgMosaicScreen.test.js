import React from "react";
import { fireEvent, getByText, queryByAttribute, render, screen } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import '@testing-library/jest-dom';
import { <PERSON><PERSON>erRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import EpgMosaicScreen from "./EpgMosaicScreen";
import { getChannelData } from '../../../store/slices/PlayerSlice';
import { getEpgChannel, getEpgFilterName } from '../../../store/slices/EpgSlice';

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn().mockReturnValue(jest.fn())
}));

const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);

export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const mockdata = [
    {
        key: 0,
        channelResponse: []
    },
    {
        key: 1,
        channelResponse: [
            {
                "channel_id": "3020085",
                "source_uri": "",
                "id": "236309637",
                "name": "Cierre De Transmisión",
                "description": "Cierre de emisión de Canal Iberoamericano.",
                "talent": null,
                "date_begin": "2025/03/24 20:00:00",
                "date_end": "2025/03/25 07:00:00",
                "unix_begin": **********,
                "unix_end": **********,
                "duration": "11:00:00",
                "language": "esp",
                "type": "0",
                "group_id": "channel1",
                "group": {
                    common: {
                        timeshift: true
                    }
                },
                "confirmado": null,
                "id_empleado": null,
                "image": "test-image.png",
                "number": "1"
            },
            {
                "channel_id": "3020086",
                "id": "236309638",
                "name": "Channel 2",
                "description": "Test channel 2",
                "date_begin": "2025/03/24 20:00:00",
                "date_end": "2025/03/25 07:00:00",
                "unix_begin": **********,
                "unix_end": **********,
                "group_id": "channel2",
                "group": {
                    common: {
                        timeshift: false
                    }
                },
                "image": "test-image-2.png",
                "number": "2"
            }
        ],
    }
];

const mockCatagoryMossaic = [
    { id: 1, text: "sports", image: "sports.png" },
    { id: 2, text: "movies", image: "movies.png" },
    { id: 3, text: "news", image: "news.png" },
    { id: 4, text: "kids", image: "kids.png" },
    { id: 5, text: "entertainment", image: "entertainment.png" }
];

const mockEpgFilterReduxData = {
    payload: {
        sports: ["channel1", "channel3"],
        movies: ["channel2", "channel4"],
        news: [],
        kids: ["channel5"],
        entertainment: []
    }
};

const mockPaywayResponse = [
    {
        groups: ["channel2", "channel3"]
    }
];

const mockAppMetaData = {
    translations: JSON.stringify({
        language: {
            us: {
                top_head_option_button_exit: "Exit",
                top_head_option_button_back: "Back",
                filterSelector_modal_option_button_categorieSelector: "Categories",
                filterSelector_modal_option_button_channelSelector: "Channels"
            }
        }
    })
};

describe('EpgMosaicScreen component tests', () => {
    beforeEach(() => {
        Storage.prototype.getItem = jest.fn(() => 'us');
        Element.prototype.scrollIntoView = jest.fn();
        const videoElement = document.createElement('video');
        videoElement.id = 'bitmovinplayer-video-player';
        document.body.appendChild(videoElement);
        jest.spyOn(require('react-redux'), 'useDispatch').mockImplementation(() => jest.fn());
    });

    afterEach(() => {
        const videoElement = document.getElementById('bitmovinplayer-video-player');
        if (videoElement) {
            videoElement.remove();
        }
        jest.clearAllMocks();
        if (Element.prototype.scrollIntoView.mockClear) {
            Element.prototype.scrollIntoView.mockClear();
        }
    });

    test('should render and handle green button click', () => {
        const props = {
            selectedFilter: 'categories',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn(),
            handleNoChannelFilter: jest.fn(),
            resetFavScreen: jest.fn()
        };
        
        initialState.epg = {
            epgChannelBackup: mockdata,
            epgMenu: {
                response: {
                    nodes: mockCatagoryMossaic
                }
            }
        };
        initialState.initialReducer = {
            appMetaData: mockAppMetaData
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const getById = queryByAttribute.bind(null, 'id');
        const greenButton = getById(container, 'yellowButtonBack');
        
        fireEvent.keyUp(greenButton, { key: 'Enter', keyCode: 13 });
        
        expect(props.closeMosaicScreen).toHaveBeenCalled();
        expect(props.closeFilterScreen).toHaveBeenCalled();
        expect(props.closeMainEpgScreen).toHaveBeenCalled();
    });

    test('should handle yellow button click', () => {
        const props = {
            selectedFilter: 'categories',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn(),
            handleNoChannelFilter: jest.fn(),
            resetFavScreen: jest.fn()
        };
        
        initialState.epg = {
            epgChannelBackup: mockdata,
            epgMenu: {
                response: {
                    nodes: mockCatagoryMossaic
                }
            }
        };
        initialState.initialReducer = {
            appMetaData: mockAppMetaData
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const yellowButton = container.querySelector('.yellow-button');
        
        fireEvent.keyUp(yellowButton, { key: 'Enter', keyCode: 13 });
        
        expect(props.closeMosaicScreen).toHaveBeenCalled();
        expect(props.closeFilterScreen).not.toHaveBeenCalled();
    });

    test('should handle category selection without channels', () => {
        const mockDispatch = jest.fn();
        require('react-redux').useDispatch.mockReturnValue(mockDispatch);
        
        const props = {
            selectedFilter: 'categories',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn(),
            handleNoChannelFilter: jest.fn(),
            resetFavScreen: jest.fn()
        };
        
        initialState.epg = {
            epgChannelBackup: mockdata,
            epgMenu: {
                response: {
                    nodes: mockCatagoryMossaic
                }
            },
            viewEpgFilteredData: mockEpgFilterReduxData,
            paywayToken: {
                response: {
                    paqs: {
                        paq: mockPaywayResponse
                    }
                }
            }
        };
        initialState.initialReducer = {
            appMetaData: mockAppMetaData
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const getById = queryByAttribute.bind(null, 'id');
        const categoryButton = getById(container, 'i-2');
        fireEvent.keyUp(categoryButton, { key: 'Enter', keyCode: 13 });
        expect(props.handleNoChannelFilter).toHaveBeenCalled();
    });

    test('should render channels view and handle channel selection', () => {
        const mockDispatch = jest.fn();
        require('react-redux').useDispatch.mockReturnValue(mockDispatch);
        
        const props = {
            selectedFilter: 'channels',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn(),
            handleNoChannelFilter: jest.fn(),
            resetFavScreen: jest.fn()
        };
        
        initialState.epg = {
            epgChannelBackup: mockdata,
            epgMenu: {
                response: {
                    nodes: mockCatagoryMossaic
                }
            },
            viewEpgFilteredData: mockEpgFilterReduxData,
            paywayToken: {
                response: {
                    paqs: {
                        paq: mockPaywayResponse
                    }
                }
            }
        };
        initialState.initialReducer = {
            appMetaData: mockAppMetaData
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const getById = queryByAttribute.bind(null, 'id');
        const channelButton = getById(container, 'i0');
        fireEvent.keyUp(channelButton, { key: 'Enter', keyCode: 13 });
        
        expect(mockDispatch).toHaveBeenCalledWith(getEpgFilterName(''));
        expect(mockDispatch).toHaveBeenCalledWith(getEpgChannel(mockdata));
        expect(props.closeMosaicScreen).toHaveBeenCalled();
        expect(props.closeFilterScreen).toHaveBeenCalled();
        expect(props.closeMainEpgScreen).toHaveBeenCalled();
    });

    test('should test setLivePlayer function for non-contract channel', () => {
        const mockDispatch = jest.fn();
        require('react-redux').useDispatch.mockReturnValue(mockDispatch);
        
        const props = {
            selectedFilter: 'channels',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn(),
            handleNoChannelFilter: jest.fn(),
            resetFavScreen: jest.fn()
        };
        
        initialState.epg = {
            epgChannelBackup: mockdata,
            paywayToken: {
                response: {
                    paqs: {
                        paq: mockPaywayResponse
                    }
                }
            }
        };
        initialState.initialReducer = {
            appMetaData: mockAppMetaData
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const channelInfo = mockdata[1].channelResponse[0];

        const channelButton = container.querySelector('.channel-bg');
        fireEvent.keyUp(channelButton, { key: 'Enter', keyCode: 13 });

        expect(mockDispatch).toHaveBeenCalledWith(getEpgChannel(mockdata));
        expect(mockDispatch).toHaveBeenCalledWith(
            expect.objectContaining({
                payload: expect.objectContaining({
                    group_id: expect.any(String),
                    switchChannel: 'yes'
                })
            })
        );
    });
    
    test('should handle component focus navigation', () => {
        const props = {
            selectedFilter: 'categories',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn(),
            handleNoChannelFilter: jest.fn(),
            resetFavScreen: jest.fn()
        };
        
        initialState.epg = {
            epgChannelBackup: mockdata,
            epgMenu: {
                response: {
                    nodes: mockCatagoryMossaic
                }
            },
            viewEpgFilteredData: mockEpgFilterReduxData
        };
        initialState.initialReducer = {
            appMetaData: mockAppMetaData
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const getById = queryByAttribute.bind(null, 'id');
        const firstCategoryButton = getById(container, 'i-0');
        fireEvent.focus(firstCategoryButton);
        expect(Element.prototype.scrollIntoView).toHaveBeenCalledWith({
            behavior: 'smooth',
            block: 'center'
        });
    });
    
    test('should handle original test case', () => {
        const props = {
            selectedFilter: 'categories',
            closeMosaicScreen: jest.fn(),
            closeFilterScreen: jest.fn(),
            closeMainEpgScreen: jest.fn()
        };
        initialState.epg = {
            epgChannelBackup: mockdata
        };
        
        const { container } = renderWithState(<EpgMosaicScreen {...props}/>);
        const getById = queryByAttribute.bind(null, 'id');
        const buttonClick = getById(container, 'yellowButtonBack');
        
        fireEvent.focus(buttonClick);
        fireEvent(
            buttonClick,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            })
        );
        fireEvent.keyUp(buttonClick, { key: 'Enter', keyCode: 13 });
        
        expect(props.closeMosaicScreen).toHaveBeenCalled();
        expect(props.closeFilterScreen).toHaveBeenCalled();
        expect(props.closeMainEpgScreen).toHaveBeenCalled();
    });
});