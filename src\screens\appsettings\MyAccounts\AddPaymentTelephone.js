import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getAddPayments } from "../../../store/slices/settingsSlice";
import '../../../styles/ManagePayments.css';
import BottomBar from "../BottomBar";

const AddPaymentTelephone = (props) => {
	const apaMetaData = useSelector((state) => state?.initialReducer?.appMetaData)
	const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const region = localStorage.getItem('region')
	const dispatch = useDispatch();
	const navigate = useNavigate();

	const [telephoneNumber,setTelePhoneNumber] = useState('');
	const [showErrMsg, setShowErrMsg] = useState(false)
	const [errMsg, setErrMsg] = useState('');

	const userDetails = useSelector((state) => state?.login?.isLoggedIn?.response);
	const paymentDataForAPI = useSelector((state) => state?.settingsReducer?.paymentData);
	const AddPaymentDataResponse = useSelector((state) => state?.settingsReducer?.addPayments?.response);



	const handleAddTelephone = () => {
		dispatch(getAddPayments({'apiUrl':paymentDataForAPI?.buyLink,'buyToken':paymentDataForAPI?.buyToken,'userToken':userDetails?.user_token ,'number':telephoneNumber,'paramType':'claveServicio'}));
	}


	const handleTelephoneNumber =(e) => {
		setTelePhoneNumber(e.target.value);
	}
	
	useEffect(() => {
		if(AddPaymentDataResponse?.status === 200){
			navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new')
		}
		else{
			setShowErrMsg(true);
			setErrMsg(AddPaymentDataResponse?.msg)
		}
		document.getElementById('input-number').focus()
	},[AddPaymentDataResponse])

	return(
		<div className="App-Settings">
			<div className="App-logo">
        <img src={'images/logo.png'} className="logo-img" alt="logo" />
      </div>
			<div>
				<p className="title">Claro Telephone Number</p>
				<p className="subTitle">
				{translations?.language?.[region]?.hubfacturafijagate_access_subtitle_label ? translations?.language?.[region]?.hubfacturafijagate_access_subtitle_label: 'hubfacturafijagate_access_subtitle_label'}
				</p>
				<p className="subText">
				{translations?.language?.[region]?.hubfacturafijagate_phoneNumber_placeHolder_textfield ? translations?.language?.[region]?.hubfacturafijagate_phoneNumber_placeHolder_textfield: 'hubfacturafijagate_phoneNumber_placeHolder_textfield'}
				</p>
				<input 
					className="inputBox focusable" 
					type="number" 
					id='input-number' 
					name='input-number'
					placeholder= {translations?.language?.[region]?.hubfacturafijagate_invoiceNumber_placeHolder_textfield ? translations?.language?.[region]?.hubfacturafijagate_invoiceNumber_placeHolder_textfield: 'hubfacturafijagate_invoiceNumber_placeHolder_textfield'}
					value={telephoneNumber} 
					onChange={handleTelephoneNumber} 

					/>
				<div className="error-box">
					<div className={` ${showErrMsg ? 'invalid-otp' : "invisible"}`}>{errMsg}</div>
				</div>
				<div className="bottom-button">
					<button className="nextButton focusable" id='next' onClick={handleAddTelephone}>
					{translations?.language?.[region]?.setupPin_modal_option_button_next ? translations?.language?.[region]?.setupPin_modal_option_button_next: 'setupPin_modal_option_button_next'}
				  </button>
				</div>
			</div>
			<BottomBar image={"images/selectBack.png"} title={translations?.language?.[region]?.atv_back_notification ? translations?.language?.[region]?.atv_back_notification: 'atv_back_notification'}/>
		</div>
	)
};

export default AddPaymentTelephone;
