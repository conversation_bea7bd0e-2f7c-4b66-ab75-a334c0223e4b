// import React from "react";
// import { fireEvent, getByText, queryByAttribute, render } from "@testing-library/react/";
// import { Provider } from "react-redux";
// import "regenerator-runtime/runtime";
// import '@testing-library/jest-dom';
// import { <PERSON>rowserRouter as Router } from "react-router-dom";
// import configureStore from "redux-mock-store";
// import { createHashHistory as createHistory } from "history";
// import { fromJS } from "immutable";
// import EpgGrid from "./EpgGrid";

// const initialState = {};
// const mockStore = configureStore([]);
// const history = createHistory();
// const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
//     <Provider store={reduxStore}>
//         <Router history={history}>{children}</Router>
//     </Provider>
// );
// export const renderWithState = (ui) => {
//     return render(ui, { wrapper: Wrapper });
// };


// describe('Add Profile page test', () => {
//     test('should render api mock data', () => {
//         global.IntersectionObserver?.prototype?.observe();

//         renderWithState(<EpgGrid />)
//     })


// })

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import EpgGrid from './EpgGrid';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

// Mock react-redux hooks
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn(),
  useDispatch: jest.fn(),
}));

// Mock other dependencies
jest.mock('react-lazy-load-image-component', () => ({
  LazyLoadImage: ({ src, placeholderSrc, width, height }) => (
    <img src={src || placeholderSrc} width={width} height={height} alt="" />
  ),
}));

jest.mock('moment', () => {
  const moment = () => ({
    format: jest.fn(() => '12:00:00'),
  });
  moment.format = jest.fn();
  return moment;
});

// Mock store
const mockStore = configureStore({
  reducer: {
    epg: () => ({
      epgMenu: { response: { nodes: [{ id: '1', text: 'Genre' }] } },
      epgLineup: { response: { channels: [] } },
      epgChannel: [
        { 
          channelResponse: [
            { 
              group_id: '1', 
              number: 1, 
              image: 'channel1.jpg',
              events: [
                { 
                  id: '1', 
                  name: 'Program 1', 
                  date_begin: '2023/01/01 12:00:00',
                  date_end: '2023/01/01 13:00:00',
                  duration: '01:00:00',
                  ext_eventimage_name: 'program1.jpg',
                  ext_catchup: 1,
                  ext_recordable: '1',
                  unix_begin: 1672567200,
                  unix_end: 1672570800,
                }
              ],
              group: {
                common: {
                  timeshift: 7,
                  id: '1',
                  extendedcommon: {
                    genres: {
                      genre: [{ desc: 'Genre' }]
                    }
                  }
                }
              }
            }
          ]
        }
      ],
      epgChannelBackup: [],
      ReminderLive: { response: [] },
      RecordingList: { response: { recordings: [] } },
      RecordingListSeries: [],
      favouriteLive: { response: { groups: [] } },
    }),
    player: () => ({
      channelData: {},
    }),
    settingsReducer: () => ({
      lockedChannelsList: { response: { groups: [] } },
      getSubsInfo: {},
    }),
    login: () => ({
      isLoggedIn: { response: { session_stringvalue: 'hks', session_userhash: 'hash', user_token: 'token', parent_id: '1' } },
      watchFreestate: false,
    }),
    initialReducer: () => ({
      appMetaData: {
        translations: JSON.stringify({
          language: {
            es: {
              'TvEnVivo_GuiaCompleta_Horarios_Metadata_TextoDia_Hoy': 'Hoy',
              'Player_Boton_TextoAccion_Grabar': 'Grabar',
            }
          }
        }),
        epg_show_event_poster: JSON.stringify({ es: { enable: true } })
      }
    }),
  },
});

describe('EpgGrid Component', () => {
  let dispatchMock;

  beforeEach(() => {
    dispatchMock = jest.fn();
    useDispatch.mockReturnValue(dispatchMock);
    useSelector.mockImplementation((selector) => selector(mockStore.getState()));
    
    // Mock IntersectionObserver
    global.IntersectionObserver = jest.fn(() => ({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: jest.fn(),
    }));
    
    // Mock Date
    jest.useFakeTimers().setSystemTime(new Date('2023-01-01T12:00:00'));
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('renders without crashing', () => {
    render(<EpgGrid setShowLiveControls={jest.fn()} />)
    //expect(screen.getByText('Hoy')).toBeInTheDocument();
  });

//   it('displays channel information', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       expect(screen.getByText('001')).toBeInTheDocument();
//     });
//   });

//   it('displays program information', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       expect(screen.getByText(/Program 1/)).toBeInTheDocument();
//     });
//   });

//   it('handles program focus and description update', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       const program = screen.getByText(/Program 1/).closest('.program');
//       fireEvent.focus(program);
      
//       expect(dispatchMock).toHaveBeenCalledWith(expect.objectContaining({
//         type: 'epg/getProgramDetailsData',
//         payload: expect.objectContaining({
//           programData: expect.any(Object),
//           channelData: expect.any(Object),
//         }),
//       }));
//     });
//   });

//   it('handles key navigation', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       const program = screen.getByText(/Program 1/).closest('.program');
//       fireEvent.keyDown(program, { key: 'ArrowDown', keyCode: 40 });
      
//       // Verify scroll functions were called
//       // This would need more specific mocks for the List components
//     });
//   });

//   it('opens program details on Enter key', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       const program = screen.getByText(/Program 1/).closest('.program');
//       fireEvent.keyDown(program, { key: 'Enter', keyCode: 13 });
      
//       // Verify dispatch was called with program details
//       expect(dispatchMock).toHaveBeenCalledWith(expect.objectContaining({
//         type: 'epg/getProgramDetailsData',
//       }));
//     });
//   });

//   it('handles filter screen toggle', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       // Simulate pressing the red button (keyCode 403)
//       fireEvent.keyDown(document, { keyCode: 403 });
      
//       // Verify filter screen state was updated
//       // This would need a way to check the internal state or mock the EpgFilterScreen component
//     });
//   });

//   it('displays recording icon for recordable programs', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       expect(screen.getByAltText('')).toHaveAttribute('src', 'images/LiveTv/ic_recording_gray_epg.png');
//     });
//   });

//   it('handles day navigation', async () => {
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       // Simulate navigating to next day
//       const firstProgram = screen.getByText(/Program 1/).closest('.program');
//       fireEvent.keyDown(firstProgram, { key: 'ArrowRight', keyCode: 39 });
      
//       // Verify day state was updated
//       // This would need a way to check the internal state
//     });
//   });

//   it('displays locked channel icon for locked channels', async () => {
//     // Update mock store to include locked channels
//     useSelector.mockImplementation((selector) => selector({
//       ...mockStore.getState(),
//       settingsReducer: {
//         lockedChannelsList: { 
//           response: { 
//             groups: [{ id: '1' }] 
//           } 
//         }
//       }
//     }));
    
//     render(
//       <Provider store={mockStore}>
//         <EpgGrid setShowLiveControls={jest.fn()} />
//       </Provider>
//     );
    
//     await waitFor(() => {
//       expect(screen.getByAltText('')).toHaveAttribute('src', 'images/lock_icon_liveTV.png');
//     });
//   });
});