import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import './EpgFilterScreen.scss'
import EpgMosaicScreen from './EpgMosaicScreen'

const EpgFilterScreen = ({
  closeFilterScreen,
  closeMainEpgScreen,
 // handleNoChannelFilter,  Removing as part of SCT-1447
  resetFavScreen,
  gaContentData
}) => {
  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const apilanguage = translations?.language?.[region]
  const [showMosaic, setShowMosaic] = useState(false)
  const [selectedFilter, setSelectedFilter] = useState('')
  const closeMosaicScreen = () => setShowMosaic(false)
  const [toggleMosaic, setToggleMosaic] = useState(false)

   useEffect(() => {
    const mosaicScreenOpen = document.getElementById('mosaicScreen')
    const filterScreenOpen = document.getElementById('epgFilter')
    if (toggleMosaic) {
      if (mosaicScreenOpen) {
        closeMosaicScreen()
        closeFilterScreen()
        setSelectedFilter('')
      } else if (filterScreenOpen) {
        closeFilterScreen()
      }
    }
    setToggleMosaic(false)
  }, [toggleMosaic])


  const handlesamsungkey = (key, keycode) => {
    const mosaicScreenOpen = document.getElementById('mosaicScreen')
    if (key.greencode === keycode) {
      closeFilterScreen()
      closeMainEpgScreen()
      mosaicScreenOpen && closeMosaicScreen()
    } else if (
      key.yellowcode === keycode ||
      keycode === 461 ||
      keycode === 65
    ) {
      setToggleMosaic(true)
    }
  }

  const handleLgkey = keycode => {
    const mosaicScreenOpen = document.getElementById('mosaicScreen')
    if (keycode === 404 || keycode === 84 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 119)) {
      closeFilterScreen()
      closeMainEpgScreen()
      mosaicScreenOpen && closeMosaicScreen()
    } else if (keycode === 405 || keycode === 461 || keycode === 66 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      setToggleMosaic(true)
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }
  

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [])

  return showMosaic ? (
    <EpgMosaicScreen
      selectedFilter={selectedFilter}
      closeMosaicScreen={closeMosaicScreen}
      closeFilterScreen={closeFilterScreen}
      closeMainEpgScreen={closeMainEpgScreen}
      //handleNoChannelFilter={handleNoChannelFilter}  Removing as part of SCT-1447
      resetFavScreen={resetFavScreen}
      gaContentData={gaContentData}
    />
  ) : (
    <div id="epgFilter" className="epg-filter-layout">
      <button
        className="green-button focusable"
        id="greenButton"
        data-sn-left={'#categoriesButton'}
        data-sn-down={'#categoriesButton'}
        data-sn-up="null"
        data-sn-right={'#yellowButton'}
        onKeyUp={e => {
          if (e.key === 'Enter' || e.keyCode === 13) {
            closeFilterScreen()
            closeMainEpgScreen()
          }
        }}
      >
        <img className="green-button-image" src={'images/green_shortcut.png'} />
        <p className="green-button-text">
          {apilanguage?.top_head_option_button_exit ||
            'top_head_option_button_exit'}
        </p>
      </button>
      <button
        className="yellow-button focusable"
        data-sn-left={'#greenButton'}
        data-sn-down={'#categoriesButton'}
        data-sn-up="null"
        data-sn-right="null"
        onKeyUp={e => {
          if (e.key === 'Enter' || e.keyCode === 13) {
            closeFilterScreen()
          }
        }}
        id="yellowButton"
      >
        <img
          className="yellow-button-image"
          src={'images/yellow_shortcut.png'}
        />
        <img className="back-image" src={'images/back_button.png'} />
        <p className="yellow-button-text">
          {apilanguage?.top_head_option_button_back ||
            'top_head_option_button_back'}
        </p>
      </button>
      <div className="epg-filter-list">
        <p className="page-title">Filtrar</p>
        <button
          autoFocus={true}
          className="title-categories focusable"
          data-sn-left="null"
          data-sn-down={'#channelsButton'}
          data-sn-up={'#greenButton'}
          data-sn-right={'#greenButton'}
          id="categoriesButton"
          onKeyUp={e => {
            if (e.key === 'Enter' || e.keyCode === 13) {
              setSelectedFilter('categories')
              setShowMosaic(true)
            }
          }}
        >
          <p className="title-categories-text">Ver categorías</p>
        </button>
        <button
          className="title-channels focusable"
          id="channelsButton"
          data-sn-left="null"
          data-sn-down="null"
          data-sn-up={'#categoriesButton'}
          data-sn-right={'#greenButton'}
          onKeyUp={e => {
            if (e.key === 'Enter' || e.keyCode === 13) {
              setSelectedFilter('channels')
              setShowMosaic(true)
            }
          }}
        >
          <p className="title-channels-text">Ver canales</p>
        </button>
      </div>
    </div>
  )
}

export default EpgFilterScreen
