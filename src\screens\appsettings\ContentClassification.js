import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import './Settings.scss';
import { getModifyControlPin, getStatusControlPin } from "../../store/slices/settingsSlice";
import { pushScreenViewEvent } from "../../GoogleAnalytics";

const ContentClassification = () => {

	const navigate = useNavigate()
	const dispatch = useDispatch()
	const { state } = useLocation()

	const region = localStorage.getItem('region')

	const [activeButton, setActiveButton] = useState('')

	const statusControlPin = useSelector(state => state?.settingsReducer?.statusControlPin?.response)
	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
	const modifyControlPin = useSelector(state => state?.settingsReducer?.modifyControlPin?.data)
	const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

	const translations = apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
	const apilanguage = translations?.language?.[region]


	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	}

	const handleClassificationRestrictions = (e, value) => {
		e.preventDefault()
		dispatch(getModifyControlPin({
			hks: userDetails?.session_stringvalue,
			userId: userDetails?.user_id,
			channelPin: statusControlPin?.pin_channel?.status,
			parentalPin: 1,
			purchasePin: statusControlPin?.pin_purchase?.status,
			accessParentalCode: value,
			userToken: userDetails?.user_token,
			userPin: state?.pin
		}))
		navigate('/settings/profile-settings', { state: { pageName: 'parentalControl', focusElement: 'classificationPG', selectedValue: value } })
	}

	const keypresshandler = (event) => {
		const keycode = event.keyCode;
		if (keycode === 461 || keycode === 10009) {
			navigate('/settings/profile-settings', { state: { pageName: 'parentalControl', focusElement: 'classificationPG' } })
		}
	}

	const handleCancelClick = () => {
		navigate('/settings/profile-settings', { state: { pageName: 'parentalControl', focusElement: 'classificationPG' } })
	}

	useEffect(() => {
		document.addEventListener("keyup", keypresshandler)
		return () => {
			document.removeEventListener("keyup", keypresshandler)
		}
	}, [keypresshandler])

	useEffect(() => {
		dispatch(getStatusControlPin({
			hks: userDetails?.session_stringvalue,
			userId: userDetails?.user_id
		}))
	}, [modifyControlPin])

	useEffect(() => {
		statusControlPin && setActiveButton(statusControlPin?.pin_parental?.info?.value)

	}, [statusControlPin])

	useEffect(()=>{
		pushScreenViewEvent({screenName:'content_classification', screenData: userDetails, prevScreenName: 'parental_control'})
	},[])
	
	return (
		<div className="content-classification focusable">
			<span className="content-classification-title">{truncateText('title_content_classification', 80)}</span>
			<span className="content-classification-sub-title">{truncateText('description_content_classification', 200)}</span>
			<button className={`content-classification-buttons content-classification-button-height ${activeButton == 20 ? 'content-classification-buttons-active' : ''}  focusable`} id='pgButton' autoFocus onClick={(e) => handleClassificationRestrictions(e, 20)}>
				<span className="content-classification-button-contents">
					{truncateText('profile_pin_clasification_code_pg', 5)} - {truncateText('profile_pin_clasification_label_pg', 150)}
					{activeButton == 20 && <img className="check-icon" src={'images/CheckIcon.png'} />}
				</span>
			</button>
			<button className={`content-classification-buttons ${activeButton == 30 ? 'content-classification-buttons-active' : ''} focusable`} id='pg13Button' onClick={(e) => handleClassificationRestrictions(e, 30)}>
				<span className="content-classification-button-contents">
					{truncateText('profile_pin_clasification_code_pg13', 10)} - {truncateText('profile_pin_clasification_label_pg13', 100)}
					{activeButton == 30 && <img className="check-icon" src={'images/CheckIcon.png'} />}
				</span>

			</button>
			<button className={`content-classification-buttons content-classification-button-height ${activeButton == 40 ? 'content-classification-buttons-active content-classification-button-height' : ''} focusable`} id='rButton' onClick={(e) => handleClassificationRestrictions(e, 40)}>
				<span className="content-classification-button-contents">
					{truncateText('profile_pin_clasification_code_r', 5)} - {truncateText('profile_pin_clasification_label_r', 200)}
					{activeButton == 40 && <img className="check-icon" src={'images/CheckIcon.png'} />}
				</span>
			</button>
			<button className={`content-classification-buttons ${activeButton == 50 ? 'content-classification-buttons-active' : ''} focusable`} id='nc17Button' onClick={(e) => handleClassificationRestrictions(e, 50)}>
				<span className="content-classification-button-contents">
					{truncateText('profile_pin_clasification_code_nc', 10)} - {truncateText('profile_pin_clasification_label_nc', 10)}
					{activeButton == 50 && <img className="check-icon" src={'images/CheckIcon.png'} />}
				</span>
			</button>
			<button className="content-classification-buttons focusable" id='cerrarButton' onClick={handleCancelClick} >
				<span className="content-classification-button-contents">{truncateText('content_classification_button_close', 10)}</span>
			</button>
		</div>
	)

}

export default React.memo(ContentClassification)