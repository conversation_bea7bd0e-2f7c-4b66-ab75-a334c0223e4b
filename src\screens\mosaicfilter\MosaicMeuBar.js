import React, {useEffect} from 'react'
import '../../styles/MosaicMenuBar.scss'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'
function MosaicMenuBar() {
  // translation
  const navigate = useNavigate()
  const region = localStorage.getItem('region')
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  
  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode) {
      navigate('/livePlayer')
    }
  }

  const handleLgkey = keycode => {
    if (keycode == 405 || keycode == 8 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      navigate('/livePlayer')
    }
  }

  return (
    <div className="mosaic-menu-bar-container">
      <div className="mosaic-menu-logo">
        Categories
        {/* {translations?.language?.[region]?.atv_back_notification} */}
      </div>
      <button className="backIndicator ">
        <img
          className="yellowIndicator"
          src={'images/Mosaic_Icons/yellow_shortcut_icon.png'}
        />
        <img
          className="backImage"
          src={'images/Mosaic_Icons/shortcut_back_icon.png'}
        />
        <p className="backText">
          {translations?.language?.[region]?.rating_description_label}
        </p>
      </button>
    </div>
  )
}

export default MosaicMenuBar
