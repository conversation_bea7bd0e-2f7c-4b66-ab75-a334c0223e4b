import watchListSlice, {
    getWatchList, getWatchListSuccess, getWatchListError, addWatchList, addWatchListSuccess,
    addWatchListError, delWatchList, delWatchListSuccess, delWatchListError, continueWatchlist,
    continueWatchlistSuccess, continueWatchlistError, delContinueWatch, delContinueWatchSuccess,
    delContinueWatchError, lastSeenWatch, lastSeenWatchListResponse, lastSeenWatchListResponseError,
    clearContentState
} from './getWatchListSlice';

describe('watchListSlice reducer', () => {
    const initialState = {
        watchList: [],
        addWatchList: {},
        delWatchList: {},
        isGetWatchlistLoading: false,
        isAddWatchlistLoading: false,
        isDelWatchlistLoading: false,
        getWatchlistError: {},
        addWatchlistError: {},
        delWatchlistError: {},
        continueWatch:false,
        continuewatchlist: {},
        continuewatcherror: {},
        deleteContinueWatch:false,
        deletecontinuewatchlist: {},
        delContinueWatchError: {},
        lastSeenWatchState:false,
        lastSeenWatchListData: {},
        lastSeenWatchListError: {},
        getEditImageFocus: {}
    }
    it('should return initialState', () => {
        expect(watchListSlice(undefined, {})).toEqual(initialState)
    });

    it('should handle getWatchList action', () => {
        const action = {type: getWatchList.type}
        const expectedState = {...initialState, isGetWatchlistLoading: true}
        expect(watchListSlice(initialState, action)).toEqual(expectedState)
    })

    it('should handle getWatchListSuccess action', () => {
        const action = {
            type: getWatchListSuccess.type,
            payload: {response: {groups: [
                {
                    "id": "1152314",
                    "title": "Kung Fu Panda 4",
                }
            ]}}
        }
        expect(watchListSlice(initialState, action)).toEqual({
            ...initialState,
            watchList: action.payload.response.groups,
            isGetWatchlistLoading: false,
            addWatchList: {},
            delWatchList: {}
        })
    });

    it('should handle getWatchListError action', () => {
        const action = {
            type: getWatchListError.type,
            payload: {data:{response: {}}}
        }
        expect(watchListSlice(initialState, action)).toEqual({
            ...initialState,
            isGetWatchlistLoading: false,
            getWatchlistError: action.payload.data.response 
        })
    });

    it('should handle addWatchList action', () => {
            expect(watchListSlice(initialState, addWatchList())).toEqual({
                ...initialState,
                isAddWatchlistLoading: true 
            }) 
    });

    it('should handle addWatchListSuccess action', () => {
        const payload = {watchlist: 'Success' }
        const action = {
            type: addWatchListSuccess.type,payload
        }
        expect(watchListSlice(initialState, action)).toEqual({
            ...initialState,
            isAddWatchlistLoading: false,
            addWatchList: payload
        })
    });

    it('should handle addWatchListErrorr', () => {
        const payload = 'Error message';
        const action = { type: addWatchListError.type, payload };
        const expectedState = { ...initialState, isAddWatchlistLoading: false, addWatchlistError: payload}
        expect(watchListSlice(initialState, action)).toEqual(expectedState);
      });

    it('should handle delWatchList action', () => {
        expect(watchListSlice(initialState, delWatchList())).toEqual({
            ...initialState,
            isDelWatchlistLoading: true  
        })
    });

    it('should handle delWatchListSuccess action', () => {
        const payload = { delWatchlist: 'success'}
        const action = {type: delWatchListSuccess.type,payload}
        const expectedState = {...initialState, isDelWatchlistLoading: false, delWatchList: payload}
        expect(watchListSlice(initialState, action)).toEqual(expectedState)
    });

    it('should handle delWatchListError action', () => {
        const action = {
            type: delWatchListError.type,
            payload: ''
        }
        expect(watchListSlice(initialState, action)).toEqual({
            ...initialState,
            isDelWatchlistLoading: false,
            delWatchlistError: action.payload
        })
    });

    it('should handle continueWatchlist action', () => {
        expect(watchListSlice(initialState, continueWatchlist())).toEqual({
            ...initialState,
            continueWatch: false
        })
    });

    it('should handle continueWatchlistSuccess action', () => {
        const payload = {continuewatchlist: 'continueWatchlistSuccess'}
        const action = {type: continueWatchlistSuccess.type, payload}
        const expectedState = {...initialState, continuewatchlist: payload}
        expect(watchListSlice(initialState, action)).toEqual(expectedState)
    });

    it('should handle continueWatchlistError action', () => {
        const action = {
            type: continueWatchlistError.type,
            payload: 'continueWatchlistError'
        }
        expect(watchListSlice(initialState, action)).toEqual({
           ...initialState,
           continuewatcherror: action.payload 
        })
    });

    it('should handle delContinueWatch action', () => {
        const payload = {Continue: 'Watch'}
        const action = {type: delContinueWatch.type,payload}
        const expectedState = {...initialState, deleteContinueWatch: false }
        expect(watchListSlice(initialState, action)).toEqual(expectedState)
    });

    it('should handle delContinueWatchSuccess action', () => {
        const action = {
            type: delContinueWatchSuccess.type,
            payload: 'delContinueWatchSuccess'
        }
        expect(watchListSlice(initialState,action)).toEqual({
            ...initialState,
            deletecontinuewatchlist: action.payload  
        })
    });

    it('should handle delContinueWatchError action', () => {
        const action = {
            type: delContinueWatchError.type,
            payload: 'delContinueWatchError'
        }
        expect(watchListSlice(initialState, action)).toEqual({
            ...initialState,
            lastSeenWatchState: false,
            delContinueWatchError: action.payload 
        })
    });

    it('should handle lastSeenWatch action', () => {
        expect(watchListSlice(initialState, lastSeenWatch())).toEqual({
            ...initialState,
            lastSeenWatchState: true  
        })
    });

    it('should handle lastSeenWatchListResponse action', () => {
        const payload = 'lastSeenWatchListResponse'
        const action = {type: lastSeenWatchListResponse.type, payload}
        const expectedState = {...initialState, lastSeenWatchState: false, lastSeenWatchListData: payload}
        expect(watchListSlice(initialState, action)).toEqual(expectedState)
    });

    it('should handle lastSeenWatchListResponseError action', () => {
        const payload = 'lastSeenWatchListResponseError'
        const action = { type: lastSeenWatchListResponseError.type, payload }
        const expectedState = {...initialState, lastSeenWatchState: false, lastSeenWatchListError: payload}
        expect(watchListSlice(initialState, action)).toEqual(expectedState)
    });

    it('should handle clearContentState action', () => {
        expect(watchListSlice(initialState, clearContentState())).toEqual({
            ...initialState,
            delWatchList: {},
            deletecontinuewatchlist: {} 
        })
    });

})