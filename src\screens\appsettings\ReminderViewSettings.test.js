import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import '@testing-library/jest-dom';
import ReminderViewSettings from './ReminderViewSettings';

// Mock the dependencies
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn()
}));

// Mock moment
jest.mock('moment', () => {
  const mockMoment = jest.fn().mockImplementation((date) => ({
    format: jest.fn().mockReturnValue('12:00 HS')
  }));
  return mockMoment;
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn()
};

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock document.getElementById
document.getElementById = jest.fn();

describe('ReminderViewSettings', () => {
  const mockNavigate = jest.fn();
  const today = new Date();
  const futureDate = new Date(today);
  futureDate.setDate(today.getDate() + 1);
  
  const futureTime = futureDate.getFullYear() + '/' + 
    (futureDate.getMonth() + 1).toString().padStart(2, '0') + '/' + 
    futureDate.getDate().toString().padStart(2, '0') + ' 14:00:00';
  
  const todayTime = today.getFullYear() + '/' + 
    (today.getMonth() + 1).toString().padStart(2, '0') + '/' + 
    today.getDate().toString().padStart(2, '0') + ' 23:59:59';

  beforeEach(() => {
    useNavigate.mockReturnValue(mockNavigate);
    localStorageMock.getItem.mockReturnValue('us');
    document.getElementById.mockReturnValue({ focus: jest.fn() });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  test('renders reminder list when reminders are available', () => {
    const mockReminders = [
      {
        data: {
          begintime: futureTime,
          endtime: futureTime,
          name: 'Test Program',
          channel_image: 'test.jpg',
          channel_name: 'Test Channel'
        }
      }
    ];
    
    useSelector.mockImplementation((selector) => {
      if (selector.toString().includes('ReminderLive')) return mockReminders;
      return { 
        appMetaData: {
          translations: JSON.stringify({ language: { us: {} } })
        }
      };
    });

    render(<ReminderViewSettings />);
    expect(screen.getByText('Test Program')).toBeInTheDocument();
  });

  // Test for handleReminderClick function (lines 92-100)
  test('handleReminderClick navigates to action screen with correct state', () => {
    const mockReminders = [
      {
        data: {
          begintime: futureTime,
          endtime: futureTime,
          name: 'Test Program',
          channel_image: 'test.jpg',
          channel_name: 'Test Channel'
        }
      }
    ];
    
    useSelector.mockImplementation((selector) => {
      if (selector.toString().includes('ReminderLive')) return mockReminders;
      return { 
        appMetaData: {
          translations: JSON.stringify({ language: { us: {} } })
        }
      };
    });

    render(<ReminderViewSettings screenName="testScreen" defaultFocus="testFocus" />);
    
    const button = screen.getByText('Test Program').closest('button');
    fireEvent.click(button);
    
    expect(mockNavigate).toHaveBeenCalledWith('/settings/actionScreenSettings', {
      state: {
        data: mockReminders[0],
        pageName: 'recordatorios',
        screenActive: 'testScreen',
        defaultFocus: 'testFocus'
      }
    });
  });

  // Test for handleKeyUp function with keyCode 405 (lines 102-112)
  test('handleKeyUp navigates with keyCode 405', () => {
    const mockReminders = [
      {
        data: {
          begintime: futureTime,
          endtime: futureTime,
          name: 'Test Program',
          channel_image: 'test.jpg',
          channel_name: 'Test Channel'
        }
      }
    ];
    
    useSelector.mockImplementation((selector) => {
      if (selector.toString().includes('ReminderLive')) return mockReminders;
      return { 
        appMetaData: {
          translations: JSON.stringify({ language: { us: {} } })
        }
      };
    });

    render(<ReminderViewSettings screenName="testScreen" defaultFocus="testFocus" />);
    
    const button = screen.getByText('Test Program').closest('button');
    fireEvent.keyUp(button, { keyCode: 405 });
    
    expect(mockNavigate).toHaveBeenCalledWith('/settings/actionScreenSettings', {
      state: {
        data: mockReminders[0],
        pageName: 'recordatorios',
        screenActive: 'testScreen',
        defaultFocus: 'testFocus'
      }
    });
  });

  // Test for Tizen device key handling (lines 113-128)
  test('handleKeyUp handles Tizen yellow key', () => {
    // Mock Tizen global object
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockReturnValue({ code: 403 })
      }
    };

    const mockReminders = [
      {
        data: {
          begintime: futureTime,
          endtime: futureTime,
          name: 'Test Program',
          channel_image: 'test.jpg',
          channel_name: 'Test Channel'
        }
      }
    ];
    
    useSelector.mockImplementation((selector) => {
      if (selector.toString().includes('ReminderLive')) return mockReminders;
      return { 
        appMetaData: {
          translations: JSON.stringify({ language: { us: {} } })
        }
      };
    });

    render(<ReminderViewSettings screenName="testScreen" defaultFocus="testFocus" />);
    
    const button = screen.getByText('Test Program').closest('button');
    fireEvent.keyUp(button, { keyCode: 403 });
    
    expect(global.tizen.tvinputdevice.registerKeyBatch).toHaveBeenCalledWith(['ColorF2Yellow']);
    expect(mockNavigate).toHaveBeenCalledWith('/settings/actionScreenSettings', {
      state: {
        data: mockReminders[0],
        pageName: 'recordatorios',
        screenActive: 'testScreen',
        defaultFocus: 'testFocus'
      }
    });

    // Clean up the global mock
    delete global.tizen;
  });

  // Test for isToday function (lines 140-144)
  test('displays today format for events happening today', () => {
    const mockReminders = [
      {
        data: {
          begintime: futureTime,
          endtime: todayTime, // Using today's date
          name: 'Today Program',
          channel_image: 'test.jpg',
          channel_name: 'Test Channel'
        }
      }
    ];
    
    useSelector.mockImplementation((selector) => {
      if (selector.toString().includes('ReminderLive')) return mockReminders;
      return { 
        appMetaData: {
          translations: JSON.stringify({ 
            language: { 
              us: {
                'recording_today_format': 'Today'
              } 
            } 
          })
        }
      };
    });

    render(<ReminderViewSettings />);
    // Since we're mocking moment to always return "12:00 HS", we should see "Today 12:00 HS"
    expect(screen.getByText('Today Program')).toBeInTheDocument();
  });
});