import React, { useEffect, useRef, useState, useCallback } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { getChannelData } from '../../store/slices/PlayerSlice'
import {
  getLiveReminder,
  getPayWayToken,
  getPopUpState,
  setReminderPopupVisible
} from '../../store/slices/EpgSlice'
import './ShowReminder.scss'
import { store } from '../../store/sagaStore'
import NetworkErrorScreen from '../appsettings/NetworkErrorScreen'
import { getClearAllSettingsState } from '../../store/slices/settingsSlice'
import {
  getClearAllLoginStates,
  getIsLoggedinRefresh,
  setSkeltonLoading
} from '../../store/slices/login'
import {
  getClearState,
  getNavTabValueClear,
  getPremiumValueStateClear
} from '../../store/slices/HomeSlice'
import { getClearProfileState } from '../../store/slices/ProfileSlice'
import { getClearEPGState } from '../../store/slices/EpgSlice'

const ShowReminder = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [scheduledReminders, setScheduledReminders] = useState([])
  const [showReminder, setShowReminder] = useState(null)
  const [checkedReminders, setCheckedReminders] = useState(new Set())
  const [completedReminders, setCompletedReminders] = useState(new Set())
  const [isInternetThere, setIsInternetThere] = useState(navigator.onLine)

  const sintonizarRef = useRef(null)

  const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
  const userDetailError = useSelector(
    state => state?.login?.isLoggedInRefreshError?.errors
  )
  const reminderList = useSelector(state => state?.epg?.ReminderLive)
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)

  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')
  const apilanguage = translations?.language?.[region]

  useEffect(() => {
    if (userDetails) {
      dispatch(
        getPayWayToken({
          hks: userDetails?.session_stringvalue,
          user_id: userDetails?.user_id
        })
      )
    }
  }, [userDetails, dispatch])

  useEffect(() => {
    if (showReminder) {
      sintonizarRef?.current?.focus()
      store.dispatch(getPopUpState(true))
      store.dispatch(setReminderPopupVisible(true))
      const timeoutId = setTimeout(() => {
        clearReminder(showReminder)
      }, 12000)
      return () => clearTimeout(timeoutId)
    } else {
      store.dispatch(setReminderPopupVisible(false))
    }
  }, [showReminder])

  useEffect(() => {
    if (typeof SpatialNavigation !== 'undefined') {
      SpatialNavigation.focus()
    }
  }, [])

  useEffect(() => {
    if (Array.isArray(reminderList?.response)) {
      const currTime = new Date()
      const newReminders = reminderList?.response
        .map((reminder, index) => ({
          ...reminder,
          beginTime: new Date(reminder?.data?.begintime),
          endTime: new Date(reminder?.data?.endtime),
          setOrder: index // Assuming each reminder has an 'order' property indicating its set order
        }))
        .filter(reminder => reminder.beginTime > currTime)
        .filter(reminder => !completedReminders.has(reminder.id))
        .sort((a, b) => a.order - b.order) // Sort reminders by their original set order

      setScheduledReminders(prevReminders => {
        const existingIds = new Set(prevReminders.map(reminder => reminder.id))
        const uniqueNewReminders = newReminders.filter(
          reminder => !existingIds.has(reminder.id)
        )
        const allReminders = [...prevReminders, ...uniqueNewReminders]
        return allReminders.sort((a, b) => a.setOrder - b.setOrder)
      })
    }
  }, [reminderList, completedReminders])

  // Store reminders in localStorage when they change
  useEffect(() => {
    if (scheduledReminders?.length > 0) {
      localStorage.setItem(
        'scheduledReminders',
        JSON.stringify(scheduledReminders)
      )
    }
  }, [scheduledReminders])

  useEffect(() => {
    const currTime = new Date().getTime()

    const savedReminders =
      JSON.parse(localStorage.getItem('scheduledReminders')) || []
    const filteredReminders = savedReminders.filter(reminder => {
      const beginTimeMs = new Date(reminder?.beginTime).getTime()
      return beginTimeMs >= currTime || completedReminders.has(reminder.id)
    })

    setScheduledReminders(filteredReminders)
  }, [])

  useEffect(() => {
    let timeoutId = null
    const processReminder = reminder => {
      // Show the current reminder
      setShowReminder(reminder)
      // Mark the current reminder as checked
      setCheckedReminders(prev => new Set([...prev, reminder.id]))
      // Set a timeout to clear the reminder after 12 seconds
      timeoutId = setTimeout(() => {
        clearReminder(reminder) // Clear the current reminder
        setShowReminder(null) // Reset showReminder to allow the next reminder
      }, 12000)
    }

    const checkReminders = () => {
      const currTime = new Date().getTime()
      // Skip if a reminder is already being shown
      if (showReminder) {
        return
      }
      // Find the next upcoming reminder
      const upcomingReminder = scheduledReminders?.find(reminder => {
        const beginTimeMs = new Date(reminder?.beginTime).getTime()
        const timeDiff = beginTimeMs - currTime
        const isWithin10Minutes = timeDiff <= 600000
        // Check if the reminder is within the next 10 minutes and not already checked
        return (
          isWithin10Minutes &&
          timeDiff > 540000 &&
          !checkedReminders.has(reminder?.id)
        )
      })

      if (upcomingReminder) {
        processReminder(upcomingReminder) // Process the upcoming reminder
      }
    }
    // Initial check and start of the interval
    checkReminders()
    const interval = setInterval(checkReminders, 1000)

    return () => {
      clearInterval(interval) // Clear the interval on unmount
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [scheduledReminders, checkedReminders, completedReminders, showReminder])

  // checking whether the user is logged in or not as we can use logout all my devices, so checking the user status
  useEffect(() => {
    const isLoggedInCheck = () => {
      dispatch(getIsLoggedinRefresh(userDetails?.session_stringvalue))
    }
    // if isLoggedIn api is throwing an error code then logging out the user by removing all the localstorage
    //and clearing the reducers

    if (userDetailError?.code == 'usuario_no_logueado') {
      localStorage.removeItem('userId')
      localStorage.removeItem('signinemail')
      localStorage.removeItem('token')
      localStorage.removeItem('login_user')
      localStorage.removeItem('register_user')
      localStorage.removeItem('hks')
      localStorage.removeItem('pwd')
      localStorage.removeItem('count')
      localStorage.removeItem('region')
      localStorage.removeItem('gamificationid')
      localStorage.removeItem('lasttouch')
      localStorage.removeItem('loginId')
      localStorage.removeItem('live-channel-id')
      localStorage.removeItem('programId')
      localStorage.removeItem('pastEvent')
      localStorage.removeItem('user_hash')
      localStorage.removeItem('currNavIdx')
      localStorage.removeItem('focusedEle')
      localStorage.removeItem('continueWatchLasttouch')
      localStorage.removeItem('hideTimer')
      localStorage.removeItem('currentVod')
      localStorage.removeItem('introFinished')
      localStorage.removeItem('currentVodId')
      localStorage.removeItem('stop')
      dispatch(getClearAllLoginStates())
      dispatch(getClearAllSettingsState())
      dispatch(getPremiumValueStateClear())
      dispatch(getClearProfileState())
      dispatch(getClearState())
      dispatch(getNavTabValueClear())
      dispatch(getClearEPGState())
      dispatch(setSkeltonLoading(false))
      SpatialNavigation.clear()
      SpatialNavigation.uninit()
      SpatialNavigation.init()
      navigate('/')
    }

    const isLoggedInInterval =
      userDetails && navigator.onLine && setInterval(isLoggedInCheck, 40000)
    return () => {
      clearInterval(isLoggedInInterval)
    }
  }, [userDetails, userDetailError])

  useEffect(() => {
    const networkCheck = () => {
      if (navigator.onLine) {
        setIsInternetThere(true)
      } else {
        setIsInternetThere(false)
      }
    }
    const networkCheckInterval = setInterval(networkCheck, 5000)
    return () => {
      clearInterval(networkCheckInterval)
    }
  }, [navigator, isInternetThere])

  useEffect(() => {
    if (userDetails) {
      dispatch(
        getLiveReminder({
          hks: userDetails?.session_stringvalue,
          user_hash: userDetails?.session_userhash
        })
      )
    }
  }, [userDetails])

  const clearReminder = reminder => {
    setCompletedReminders(prev => new Set([...prev, reminder?.id]))
    setScheduledReminders(prev => prev.filter(r => r.id !== reminder?.id))
    store.dispatch(setReminderPopupVisible(false))
    setShowReminder(null) // Ensure showReminder is reset

    // Update local storage after clearing
    const updatedReminders = scheduledReminders.filter(
      r => r.id !== reminder?.id
    )
    localStorage.setItem('scheduledReminders', JSON.stringify(updatedReminders))
  }

  const tuneCurrentReminder = reminder => {
  let channelIndex = -1;
  let matchedChannel = null;
    // let channelIndex = epgSevenDaysData[1]?.channelResponse.findIndex(
    //   itrObj => itrObj.group_id == reminder?.data?.channel_group_id
    // )

  if (reminder?.data?.channel_id) {
    channelIndex = epgSevenDaysData[1]?.channelResponse.findIndex(
      itrObj => itrObj?.id == reminder?.data?.channel_id
    );
    if (channelIndex !== -1) {
      matchedChannel = epgSevenDaysData[1]?.channelResponse[channelIndex];
    }
  }
    localStorage.setItem('alertTxtBtn1', reminder?.id)
    dispatch(
      getChannelData({
        group_id: matchedChannel?.group_id || reminder?.data?.channel_group_id,
        timeshift: reminder?.data?.channel_group?.common?.timeshift,
        switchChannel: 'yes',
        epgIndex: channelIndex
      })
    )
    clearReminder(showReminder)
    const currentPath = location.pathname
    if (currentPath !== '/livePlayer') {
      navigate('/livePlayer', {
        state: { showControls: 'live' },
        replace: true
      })
    }
  }

  useEffect(() => {
    window.addEventListener('keyup', handleKeyPress)

    return () => {
      window.removeEventListener('keyup', handleKeyPress)
    }
  }, [showReminder])

  const handleKeyPress = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF1Green', 'ColorF0Red'])
      const codes = {
        greencode: tizen.tvinputdevice.getKey('ColorF1Green').code,
        redcode: tizen.tvinputdevice.getKey('ColorF0Red').code
      }
      handlesamsungkey(codes, keycode, event)
    } else {
      handleLgkey(keycode, event)
    }
  }

  const handlesamsungkey = (key, keycode, event) => {
    if (showReminder) {
      if (key.redcode === keycode) {
        tuneCurrentReminder(showReminder)
      } else if (key.greencode === keycode) {
        clearReminder(showReminder)
      }
      event.preventDefault()
    }
  }

  const handleLgkey = (keycode, event) => {
    if (showReminder) {
      if (keycode == 403 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 118)) {
        tuneCurrentReminder(showReminder)
      } else if (keycode == 404 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 119)) {
        clearReminder(showReminder)
      }
      event.preventDefault()
    }
  }

  const handleTranslationchange = useCallback(
    keyname => {
      if (!apilanguage?.[keyname]) {
        return keyname?.slice(0, 13) + '...'
      } else {
        return apilanguage?.[keyname]
      }
    },
    [apilanguage]
  )

  const handleReminderFocus = event => {
    if (event.key === 'ArrowRight' || event.keyCode === 39) {
      event.preventDefault()
      const alertElement1 = document.querySelector('.alert-txt2')
      if (alertElement1) {
        alertElement1.focus()
      }
    }
    if (event.key === 'ArrowLeft' || event.keyCode === 37) {
      event.preventDefault()
      const alertElement2 = document.querySelector('.alert-txt1')
      if (alertElement2) {
        alertElement2.focus()
      }
    }
  }

  useEffect(() => {
    window.addEventListener('keyup', handleReminderFocus)
    return () => {
      window.removeEventListener('keyup', handleReminderFocus)
    }
  }, [showReminder, checkedReminders])

  return (
    <>
      {userDetails && showReminder && (
        <>
          <div className="show-container"></div>
          <div className="reminder-alert" id="reminderAlert">
            <div className="reminder-second-alert" id="secondReminder">
              <div className="alert-txt">
                <img
                  src="images/Program_Details_Icons/clock_icon.png"
                  className="clock-image"
                  alt="clock icon"
                />
                <span className="first-text">
                  <span className="channel-title">
                    {showReminder?.data?.ext_original_name?.length > 29
                      ? `${showReminder?.data?.ext_original_name?.slice(
                          0,
                          26
                        )}...`
                      : showReminder?.data?.ext_original_name}
                  </span>
                  <span className="line-mark">{' | '}</span>
                  <span className="channel-number">
                    {showReminder?.data?.channel_number}
                  </span>
                </span>
                <img
                  className="channel-logo"
                  src={showReminder?.data?.channel_image}
                  alt="channel logo"
                />
                <span className="second-text">
                  {new Date(showReminder?.data?.begintime).getHours()}:
                  {new Date(showReminder?.data?.begintime).getMinutes()}
                  {apilanguage?.atv_hs_vcard
                    ? apilanguage?.atv_hs_vcard
                    : 'hs'}{' '}
                  a {new Date(showReminder?.data?.endtime).getHours()}:
                  {new Date(showReminder?.data?.endtime).getMinutes()}
                  {apilanguage?.atv_hs_vcard ? apilanguage?.atv_hs_vcard : 'hs'}
                </span>
              </div>
            </div>
            <button
              className="alert-txt1 focusable"
              tabIndex="0"
              ref={sintonizarRef}
              onClick={() => tuneCurrentReminder(showReminder)}
            >
              <span className="bullet-red"></span>
              <span className="schedule-reminder-red-text">
                {handleTranslationchange(
                  'BotonShortcut_TextoTitulo_Sintonizar'
                )}
              </span>
            </button>
            <button
              className="alert-txt2 focusable"
              tabIndex="0"
              onClick={() => clearReminder(showReminder)}
            >
              <span className="bullet-green"></span>
              <span className="schedule-reminder-green-text">
                {handleTranslationchange('BotonShortcut_TextoTitulo_Cerrar')}
              </span>
            </button>
          </div>
        </>
      )}
      {!isInternetThere && (
        <NetworkErrorScreen setIsInternetThere={setIsInternetThere} />
      )}
    </>
  )
}

export default ShowReminder
