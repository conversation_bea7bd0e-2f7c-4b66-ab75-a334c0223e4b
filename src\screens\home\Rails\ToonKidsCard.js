import React, { useEffect, useRef } from 'react'
import './ToonKidsCard.scss'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { useNavigate } from 'react-router-dom'
import DOMPurify from 'dompurify'
import parse from 'html-react-parser'

const SafeHTML = ({ html }) => {
  const sanitizedHTML = DOMPurify.sanitize(html)
  return <>{parse(sanitizedHTML)}</>
}

const ToonKidsCard = props => {
  const navigate = useNavigate()
  const titleRef = useRef([])

  const ToonKidsData = props?.dataObject?.highlight

  useEffect(() => {
    const element = document.getElementById(props?.id)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }, [props?.id])

  const goToMoviesSeries = (item,index) => {
    localStorage.setItem('subMenu', 1)
    if (item?.is_series) {
      navigate('/series', { state: { data: item,pageName:'inicio' } })
    } else {
      navigate('/movies', { state: { vodData: item,pageName:'inicio' } })
    }
  }

  useEffect(() => {
    const maxLength = 26
   titleRef.current?.length > 0 &&
      titleRef.current.forEach(titleElement => {
      if (titleElement) {
        truncateText(titleElement, maxLength)
      }
    })
  }, [ToonKidsData])

  const truncateText = (element, maxLength) => {
    const originalText = element.textContent
    if (originalText.length > maxLength) {
      element.textContent = originalText.slice(0, maxLength) + '...'
    }
  }

     const handleKeyDown = event => {
       const element = document.getElementById('toon-carousel')
       if (element) {
         const cards = document.querySelectorAll('.Toonical_block')
         const focusedElement = document.activeElement
         const focusedIndex = Array.from(cards).indexOf(focusedElement)

         if (event.key === 'ArrowRight' && focusedIndex < cards.length - 1) {
           const nextCard = cards[focusedIndex + 1]
           const containerRect = element.getBoundingClientRect()
           const nextCardRect = nextCard.getBoundingClientRect()

           if (nextCardRect.right > containerRect.right) {
             element.scrollLeft += nextCardRect.right - containerRect.right
           }
         } else if (event.key === 'ArrowLeft' && focusedIndex > 0) {
           const prevCard = cards[focusedIndex - 1]
           const containerRect = element.getBoundingClientRect()
           const prevCardRect = prevCard.getBoundingClientRect()

           if (prevCardRect.left < containerRect.left) {
             element.scrollLeft -= containerRect.left - prevCardRect.left
           }
         }
       }
     }

     useEffect(() => {
       const Toonical_block = document.getElementById('toon-carousel')
       Toonical_block &&
         Toonical_block.addEventListener('keydown', handleKeyDown)
       return () => {
         Toonical_block &&
           Toonical_block.removeEventListener('keydown', handleKeyDown)
       }
     }, [])

  return (
    <>
      {props?.title != null || undefined ? (
        <div className="ToonrailTitle">
          <SafeHTML html={props?.title || ''} />
        </div>
      ) : (
        ''
      )}
      <div className="Toonrail-Container">
        <div className="Toonrail-wrapper" id="toon-carousel">
          <div className="ToonKids-block">
            {ToonKidsData?.map((item, index) => (
              <div className="toon-btn" key={index}>
                <button
                  className="Toonical_block focusable"
                  data-testid={`rail_card_click${index}`}
                  id={`index${props?.index}${index}`}
                  data-sn-down={document.getElementById(`index${props?.index+1}0`) ? `#index${props?.index+1}0` : undefined}
                  onClick={() => goToMoviesSeries(item,index)}
                >
                  <LazyLoadImage
                    src={item.image_highlight}
                    key={item.id}
                    className="rail-image-Toon"
                  />
                </button>
                <div
                  className="toon-title"
                  ref={el => (titleRef.current[index] = el)}
                >
                  {item.text_highlight}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  )
}

export default ToonKidsCard
