import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import {
	getNewPlanSelectorV1,
	getViewSubscribeData
} from '../../../store/slices/settingsSlice'
import { getCMSLevelV1, getCMSLevelUserV1 } from '../../../store/slices/HomeSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import './AddSubscriptions.scss'
import { getIsLoggedinV1 } from '../../../store/slices/login'
import { pushSubscriptionEvent } from '../../../GoogleAnalytics'

const NewPlanSelector = () => {
	const dispatch = useDispatch()
	const navigate = useNavigate()

	const { state } = useLocation()
	const region = localStorage.getItem('region')

	const viewPlanRef = useRef(null)
	const focusIndexRef = useRef(null)


	const [viewPlan, setViewPlan] = useState('')
	const [focusedIndex, setFocusedIndex] = useState(null)
	const [focusContent, setFocusContent] = useState(false)

	const userDetails = useSelector(state => state?.login?.isLoggedIn?.response)
	const apaMeta = useSelector(state => state?.initialReducer?.appMetaData)
	const cmsLevelV1Response = useSelector(state => state?.homeReducer?.levelV1Response?.response)
	const cmsLevelUserV1Response = useSelector(state => state?.homeReducer?.levelUserV1Response?.response)
	const isLoggedInV1Response = useSelector(state => state?.login?.isLoggedIn?.response)
	const planSelectorResponse = useSelector(state => state?.settingsReducer?.getPlanSelectorV1?.offers)
    const navbarTab = useSelector(state => state?.homeReducer?.storenavdata)
	const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)
	const apilanguage = translations?.language?.[region]
	const subscriptionData = []
	const handleSubscribe = (e, data, index) => {
		dispatch(getViewSubscribeData(data))
		 // GA : select plan Event
		const userData = {
        user_id : userDetails?.user_id,
        parent_id : userDetails?.parent_id,
        suscriptions : userDetails?.subscriptions && Object.keys(userDetails?.subscriptions)?.filter(key =>  userDetails?.subscriptions[key]).map(key => key?.toLowerCase()).join(', '),
        user_type : userDetails ? 'registrado':'anonimo',
		content_section : navbarTab?.page
      }
		pushSubscriptionEvent(userData,data,index)
		navigate('/my-settings/my-subscriptions/add-subscriptions/subscribe-new', {
			state: {
				pageName: '/newplanselector',
				focusIndex: focusIndexRef.current
			}
		})
	}

	const handlePlanFocus = (e, data, index) => {
		e.preventDefault()
		viewPlanRef.current = data
		focusIndexRef.current = index
		setViewPlan(data)
		setFocusedIndex(index)
		setFocusContent(true)
	}

	const handlePlanBlur = () => {
		setFocusContent(false)
	}

	const planFocus = (e) => {
		e.preventDefault();
		setFocusedIndex(null);
	}


	const truncateText = (str, length) => {
		const text = apilanguage?.[str] ?? str
		if (!length) {
			length = 100
		}
		if (text?.length >= length) {
			return `${text?.slice(0, length)}...`
		} else {
			return text
		}
	}

	planSelectorResponse?.map(each =>
		subscriptionData.push({
		  bannerUrl: each?.assets?.background?.["165x394"],
		  logo: each?.assets?.logo?.['165x40'],
		  family: each?.producttype,
		  periodicity: each?.translation?.labelTemporality,
		  price: each?.price?.amount,
		  currency: each?.price?.currency,
		  styles: each?.translation?.btnSuscription?.fgColor,
		  workflowStart: each?._links?.checkout?.href,
		  taxLabel: each?.translation?.labelTaxes,
		  infoString: each?.translation?.txtPromo?.texts?.map(t => t?.text).join(" "),
		  subscribeButton: each?.translation?.btnSuscription?.texts?.[0]?.text,
		  viewButton: each?.translation?.btnFeatures?.texts?.[1]?.text,
		  producttype: each?.producttype
		})
	  )

	const handleViewDetails = () => {
		dispatch(getViewSubscribeData(viewPlanRef.current))
		navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', {
			state: {
				pageName: '/newplanselector',
				focusIndex: focusIndexRef.current

			}
		})
	}

	useEffect(() => {
                SpatialNavigation.focus()
		document?.getElementById('add-cards0')?.focus()
		dispatch(getCMSLevelV1({ userId: userDetails?.parent_id, nodeValue: 'homeuser' }))
		dispatch(getIsLoggedinV1({
			HKS: userDetails?.session_stringvalue
		}))
	}, [])


	const cmsLevelType = cmsLevelV1Response?.modules?.module?.map((each) => each?.components?.component)

	const planSelectorCheck = cmsLevelType?.map((each) => each?.find((every) => every.type == 'planselector'))

	useEffect(() => {
		planSelectorCheck?.find(data => data !== undefined) &&
			dispatch(getCMSLevelUserV1({
				nodeValue: 'homeuser',
				userId: userDetails?.user_id,
				user_token: isLoggedInV1Response?.user_token
			}))
	}, [isLoggedInV1Response])

	const cmsLevelUserType = cmsLevelUserV1Response?.modules?.module?.map((each) => each?.components?.component)

	const planSelectorUrl = []
	cmsLevelUserType?.map((each) => each?.map((every) => every.type == 'planselector' ? planSelectorUrl.push(every) : null))


	useEffect(() => {
		dispatch(getNewPlanSelectorV1({
			userId: userDetails?.parent_id,
			hks: userDetails?.session_stringvalue,
			url: planSelectorUrl?.[0]?.properties?.url,
		}))
	}, [cmsLevelUserV1Response])

	const keyPressFunc = useCallback(event => {
		const keycode = event.keyCode
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow', 'ColorF3Blue'])
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
				bluecode: tizen.tvinputdevice.getKey('ColorF3Blue').code
			}
			handlesamsungkey(codes, keycode)
		} else {
			handleLgkey(keycode)
		}
	}, [])

	useEffect(() => {
		document.addEventListener('keydown', keyPressFunc)
		return () => {
			document.removeEventListener('keydown', keyPressFunc)
		}
	}, [keyPressFunc])

	const handlesamsungkey = (key, keycode) => {
		if (key.yellowcode === keycode || keycode === 10009) {
			navigate('/home')
		}
		else if (key.bluecode === keycode) {
			handleViewDetails()
		}
	}

	const handleLgkey = keycode => {
		if (keycode == 405 || keycode === 461 || keycode == 'backClick' || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
			if (state?.pageName == 'home') {
				navigate('/home')
			}
		}
		else if (keycode == 406 || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 121)) {
			handleViewDetails()
		}
	}

	return (
		<div className='plan-selector-app-css'>
			<div className="plan-selector-back">
				<img
					src={'images/Logos_Claro_Video.svg'}
					className="plan-selector-claro-logo"
					alt="logo"
				/>
				<button className="plan-selector-back-indicator-button focusable"
					id="back-button"
					onFocus={planFocus}
					onClick={(e) => handleLgkey('backClick')}
				>
					<img className='yellow-indicator-button' src={'images/yellow_shortcut.png'} />
					<img className='back-indicator-image' src={'images/back_button.png'} />
					<span>{truncateText('top_head_option_button_return', 30)}</span>
				</button>
			</div>
			<p className="AddSubstitle">
				{planSelectorResponse?.length !== 0
					? truncateText('planSelector_access_title_label', 70)
					: truncateText('atv_screen_title', 30)}
			</p>
			{planSelectorResponse?.length === 0 && (
				<span className="transaction-info">
					{truncateText('atv_allsubscribe_settings', 50)}
				</span>
			)
			}
			{
				<div
					className="add-subscription-container"
					id='addSubscriptionId'
				>
					{subscriptionData?.map((each, index, array) =>
					(
						<div className="add-subscription-block">
							<div>
								<button
									className="add-subscription-card focusable"
									key={index}
									id={`add-cards${index}`}
									onClick={e => handleSubscribe(e, each, index)}
									onFocus={e => handlePlanFocus(e, each, index)}
									onBlur={handlePlanBlur}
									data-sn-left={index == 0 ? '' : undefined}
									data-sn-right={index != array?.length - 1 && undefined}
								>
									<div className="sub-meta-data">
										<div className="add-subcription-logo-container">
											<LazyLoadImage
												className="add-subcription-logo"
												src={each?.logo}
											/>
										</div>

										<div className="sub-pricing-info">
											<span
												className="sub-price-type"
											>
												{each?.currency}
											</span>
											<span
												className="sub-price-css"
											>
												{each?.price}
											</span>
											<span className="sub-periodicity">
												{each?.periodicity}
											</span>
										</div>
										<div className="sub-tax-label">{each?.taxLabel}</div>
										<div className="sub-free-chargestr-container">
											<div className="sub-free-chargestr">
												{each?.infoString}
											</div>
										</div>
										<div className="sub-button-div">
											<div
												className="sub-button"
												style={{ backgroundColor: each?.styles }}
												id="subscribe-button"
											>
												<div className='sub-button-span'>
												{each?.subscribeButton}
												</div>
											</div>
										</div>
									</div>
									<LazyLoadImage
										className="addsubscription-banner"
										src={each?.bannerUrl}
										placeholderSrc='images/plan_selector_placeholder.png'
									/>
								</button>
							</div>
							{focusedIndex === index && (
								<button
									className="sub-view-plan-button focusable"
									onClick={handleViewDetails}
									data-sn-left={index == 0 ? '' : undefined}
									data-sn-right={index != array?.length - 1 && undefined}
									id="view-details"
								>
									<LazyLoadImage
										src={'images/blue_shortcut.png'}
										className="subscription-blueShortcut"
									/>
									<span className="view-button-content">
										<div className='view-button-sub-div'>
											<span>{each?.viewButton}</span>
										</div>
									</span>
								</button>
							)}
						</div>
					)
					)}
				</div>
			}
		</div>
	)
}
export default NewPlanSelector