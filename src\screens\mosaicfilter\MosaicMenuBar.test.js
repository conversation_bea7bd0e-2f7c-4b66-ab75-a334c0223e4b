import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import MosaicMenuBar from "./MosaicMeuBar";

const mockedNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedNavigate,
}));

const initialState = {
  initialReducer: {
    appMetaData: {
      translations: JSON.stringify({
        language: {
          en: {
            rating_description_label: "Back"
          }
        }
      })
    }
  }
};

const mockStore = configureStore([]);
const store = mockStore(initialState);

// Mock localStorage
beforeEach(() => {
  Object.defineProperty(window, 'localStorage', {
    value: {
      getItem: jest.fn(() => 'en'),
      setItem: jest.fn(),
    },
    writable: true
  });
});

describe('MosaicMenuBar Component', () => {
  beforeEach(() => {
    mockedNavigate.mockClear();
  });

  test('renders MosaicMenuBar component correctly', () => {
    render(
      <Provider store={store}>
        <Router>
          <MosaicMenuBar />
        </Router>
      </Provider>
    );
    expect(screen.getByText('Categories')).toBeTruthy();
    expect(screen.getByText('Back')).toBeTruthy();
  });

  test('keypresshandler handles key events for Samsung TV', () => {
    global.tizen = {
      tvinputdevice: {
        registerKeyBatch: jest.fn(),
        getKey: jest.fn().mockImplementation(() => ({ code: 403 }))
      }
    };

    render(
      <Provider store={store}>
        <Router>
          <MosaicMenuBar />
        </Router>
      </Provider>
    );
    fireEvent.keyUp(document, { keyCode: 403 });

    expect(mockedNavigate).toHaveBeenCalled();
    expect(mockedNavigate.mock.calls[0][0]).toBe('/livePlayer');
  });

  test('handleLgkey navigates on LG TV yellow key', () => {
    global.tizen = undefined;

    render(
      <Provider store={store}>
        <Router>
          <MosaicMenuBar />
        </Router>
      </Provider>
    );

    fireEvent.keyUp(document, { keyCode: 405 });
    expect(mockedNavigate).toHaveBeenCalled();
    expect(mockedNavigate.mock.calls[0][0]).toBe('/livePlayer');
  });
  test('removes event listener on component unmount', () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');

    const { unmount } = render(
      <Provider store={store}>
        <Router>
          <MosaicMenuBar />
        </Router>
      </Provider>
    );

    unmount();

    expect(removeEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function));
    removeEventListenerSpy.mockRestore();
  });
});