import React, { useState, useEffect, Suspense, lazy, useRef } from 'react'
import MosaicBottomBar from './MosaicBottomBar'
import MosaicGrid from './MosaicGrid'
import { useDispatch, useSelector } from 'react-redux'
import {
  getEpgMenu,
  getEpgVersion,
  getEpgLineup,
  getcurrentcardindex,
} from '../../store/slices/EpgSlice'

import MosaicMenuBar from './MosaicMenuBar'
import '../../styles/MosaicFilter.scss'

function MosaicFilter() {
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const region = localStorage.getItem('region')
  const [filter, setFilter] = useState([])
  const dispatch = useDispatch()
  const mosaicFilterRef = useRef([])

  const epgMenu = useSelector(state => state?.epg?.epgMenu?.response?.nodes)

  let code = {}

  epgMenu?.map(nodes => {
    if (nodes.code === 'tv_todos_not_demo') {
      code = { id: nodes.id }
      return
    }
  })
  const [mosaic, setMosaic] = useState()

  useEffect(() => {
    if (code.id && !mosaic) {
      setMosaic(code.id)
    }
  }, [code.id])

  useEffect(() => {
    epgMenu && setFilter(epgMenu)
  }, [epgMenu])

  useEffect(() => {
    setTimeout(() => {
      mosaicFilterRef?.current[0]?.focus()
    }, 3000)
  }, [])

  const getLockedChanneldata = e => {
    dispatch(getcurrentcardindex('lockScreen'))
    setMosaic('lockScreen')
  }

  const getFavoriteChanneldata = e => {
    dispatch(getcurrentcardindex('FavoriteScreen'))
    setMosaic('FavoriteScreen')
  }
  const handleMosaicSelection = (item, index) => {
    setMosaic(item.id)
    dispatch(getcurrentcardindex(item.code))
    setTimeout(() => {
      mosaicFilterRef?.current[index]?.focus()
    }, 100)
  }

  return (
    <div className="mosaic-main-page">
      <MosaicMenuBar />
      <div>
        {/* Mosaic Filter Navigation Bar */}
        <div className="mosaictopgrid-container">
          <div className="mosaictopgrid-content">
            {filter?.map((item, index) => (
              <div key={index}>
                <button
                  className={`${
                    mosaic == item.id
                      ? 'mosaic-top-grid-item-select focusable'
                      : 'mosaic-top-grid-item focusable'
                  }`}
                  key={index}
                  ref={ref => (mosaicFilterRef.current[index] = ref)}
                  data-sn-left={index == 0 ? ' ' : undefined}
                  data-sn-right={index != item.length - 1 && undefined}
                  data-sn-down={'#grid0'}
                  id={item.code}
                  data-testid="mosaicselection"
                  onClick={() => handleMosaicSelection(item, index)}
                >
                  <p className="mosaic-title">{item.text}</p>
                </button>
              </div>
            ))}
            <button
              className={`${
                mosaic == 'lockScreen'
                  ? 'mosaic-top-grid-item-select focusable'
                  : 'mosaic-top-grid-item focusable'
              }`}
              onClick={e => getLockedChanneldata(e)}
              id="lockScreen"
              data-sn-down={'#grid0'}
              data-testid="lockScreen"
            >
              <p className="mosaic-title">
               
                {translations?.language?.[region]
                  ?.blocked_channel_parental_control_settings_title
                  ? translations?.language?.[region]
                      ?.blocked_channel_parental_control_settings_title
                  : 'blocked_channel_parental_control_settings_title'}
              </p>
            </button>
            <button
              className={`${
                mosaic == 'FavoriteScreen'
                  ? 'mosaic-top-grid-item-select focusable'
                  : 'mosaic-top-grid-item focusable'
              }`}
              onClick={e => getFavoriteChanneldata(e)}
              id="FavoriteScreen"
              data-sn-down={'#grid0'}
              data-testid="FavoriteScreen"
            >
              <p className="mosaic-title">
              
                {translations?.language?.[region]
                  ?.playingLive_fullEpg_title_favoritesChannels_label
                  ? translations?.language?.[region]
                      ?.playingLive_fullEpg_title_favoritesChannels_label
                  : 'playingLive_fullEpg_title_favoritesChannels_label'}
              </p>
            </button>
          </div>
        </div>
      </div>
      <MosaicGrid node={mosaic} />
      <MosaicBottomBar />
    </div>
  )
}

export default MosaicFilter
