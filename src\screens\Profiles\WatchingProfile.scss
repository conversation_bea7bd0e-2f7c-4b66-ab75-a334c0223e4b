.add-profile{
    height:1080px;
    background-color: #121212;
       
    .profile-app-logo{
    width: 1920px;
    display: flex;
    justify-content: space-between;
    background-color: #121212;
    .logo-img{
        margin-top: 50px;
        margin-left: 96px;
        width: 171px;
        height: 36px;
     }
     .back-button{
        height: 48px;
        width: 292px;
        border-radius: 6.6px;
        background-color: #2E303D;
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 50px;
        margin-left: 96px;
        margin-right: 53px;
      
      .yellow-dot{
          height: 20px;
          width: 20px;
      }
      .back-arrow{
          height: 24px;
          width: 30px;
      }
      .back-button-text{
        height: 30px;
          width: 146px;
          color: #FFFFFF;
          font-family: Roboto;
          font-size: 29.04px;
          font-weight: bold;
          letter-spacing: 0;
          line-height: 29.04px;
      }
      }
      .back-button:focus{
        background-color: #981c15;
      }
}
.prf-watch-header-container {
    padding: 50px 50px;

.prf-watch-header {
    margin: 0;
    height: 70px;
    width: 849px;
    color: #F4F4F4;
    font-family: Roboto;
    font-size: 48px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 50px;
    text-align: center;
    margin-left:470px;
}
}

.user-profile-container {
    display: flex;
    width: 85%;
    height: 400px;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 50px;

    .user-profile-image {
        text-align: center;
        margin-top: 26px;
        margin-right: 24px;
        margin-left: 24px;
        height: 248px;
        width: 248px;
        opacity: 0.8;
    }

    .user-image {
        height: 100%;
        width: 100%;

    }
    .user-prof-name {
       
        height: 36px;
        width: 250px;
        color: #FFFFFF;
        font-family: Roboto;
        font-size: 32px;
        letter-spacing: 0;
        line-height: 35px;
        text-align: center;
        text-shadow: 0 2px 4px 0 #000000;
        margin-left: 16px;    
      }
}
.user-profile-image:focus,
.user-profile-image:active {
    z-index: 1;
    background: transparent 0% 0% no-repeat;
    border: 3px solid #ffff; 
    outline: unset;
    border-radius: 50%;
    padding: 10px;
    opacity: 1;
    height: 285.08px;
    width: 285px;
    margin-right:6px;
    margin-left:6px;
    margin-top:0px;
   
}
.user-profile-image:focus>.user-prof-name,
.user-profile-image:active>.user-prof-name {
    height: 41.4px;
    width: 295.23px;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 36.8px;
    letter-spacing: 0;
    line-height: 40px;
    text-align: center;
    text-shadow: 0 2px 5px 0 #000000;
    margin-top: 32.18px;
    transform: scale(1);
}

.edit-watch-profile {
    display: flex;
    justify-content: center;
    margin-top: 100px;

    .edit-watch-prof-btn {
        width: 509px;
        height: 72px;
        background: #981C15;
        color: #EEEEEE;
        height: 72px;
        width: 509px;
        border-radius: 8.8px;
        background-color: #981C15;
        color: #FFFFFF;
        font-family: Roboto;
        font-size: 32px;
        font-weight: bold;
        letter-spacing: -0.51px;
        line-height: 38px;
        text-align: center;
    }
}

.edit-watch-prof-btn:focus,
.edit-watch-prof-btn:focus {
    transform: scale(1.2);
    background: #981C15 0% 0% no-repeat padding-box;
    border-radius: 8.8px;
    opacity: 1;
}


}