import React, { useState, useEffect, useRef } from 'react'
import './WatchingProfile.scss'
import { useLocation, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import NpawPlugin from 'npaw-plugin-es5'
import {
  getProfileReadData,
  getUserProfile
} from '../../store/slices/ProfileSlice'
import {
  getLogin,
  clearLoginInfoState,
  setSkeltonLoading,
  clearIsloggedInStatus,
  getNavBarClicked,
  getSettingsClicked
} from '../../store/slices/login'
import {
  removePlayerInstance,
} from '../../store/slices/PlayerSlice'
import { LazyLoadImage } from 'react-lazy-load-image-component'
import { getNavTabValue } from '../../store/slices/HomeSlice'
import { setNpawPluginInstance } from '../../store/slices/PlayerSlice'
import { pushScreenViewEvent, pushProfileEvent} from '../../GoogleAnalytics'
import { CURRENT_PLATFORM } from '../../utils/devicePlatform'

const WatchingProfile = () => {
  const { state: profileState } = useLocation()
  const { state } = useLocation()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const userProfileRef = useRef([])

  const isLoggedInStatus = useSelector(state => state?.login?.isLoggedInStatus)
  const profiledataRedux = useSelector(
    state => state?.profile?.profileData?.response?.data
  )
  const lasttouchRedux = useSelector(state => state?.settingsReducer?.lasttouch)
  const loginInfo = useSelector(state => state?.login?.loginSuccess?.response)
  const registerInfo = useSelector(
    state => state?.login?.registerSuccess?.response
  )
  const apaMetaData = useSelector(state => state?.initialReducer?.appMetaData)
  const vcardSeriesDetails = useSelector(
    state => state?.login?.vcardSeriesDetails
  )
  const vcardDetails = useSelector(state => state?.login?.vcardDetails)
  const backCheck = useSelector(state => state?.profile?.isHomeScreen)
  const loginError = useSelector(state => state?.login?.loginError?.errors)
  const logintermsandconditionError = useSelector(
    state => state?.login?.termsAndConditionError?.errors
  )
  const logintermsandconditionSuccess = useSelector(
    state => state?.login?.termsAndConditionSuccess
  )
  const pushSessionError = useSelector(
    state => state?.profile?.pushSessionError?.errors
  )
  const isLoggedInError = useSelector(
    state => state?.login?.isLoggedInV1Error?.errors
  )
  const loadingState = useSelector(state => state?.login?.loading)

  const youboraInstance = useSelector(
    state => state?.player?.npawPluginInstance
  )
  const anonymousUser = useSelector(state => state?.login?.anonymousUser)
  const translations =
    apaMetaData?.translations && JSON?.parse(apaMetaData?.translations)
  const youbora =
     apaMetaData?.youbora_options && JSON?.parse(apaMetaData?.youbora_options)

  const region = localStorage.getItem('region')
  const apilanguage = translations?.language?.[region] 
  const token = localStorage.getItem('token')
  const appMetaDataVideo = useSelector(
      state => state?.initialReducer?.appMetaDataVideo
    )
  const videoTutorialData =
    appMetaDataVideo?.versionUpdate_tutorial_control_options &&
    JSON?.parse(appMetaDataVideo?.versionUpdate_tutorial_control_options)

  const [profiledata, setProfiledata] = useState('')
  const changingProfileError = useSelector(
    state => state?.login?.loginSuccess?.errors
  )
  const userDetailsError = useSelector(
    state => state?.login?.isLoggedInV1Error?.errors
  )

  const pushSession = useSelector(state => state?.profile?.pushSession?.errors)

  useEffect(() => {
    // GA ScreenView Event
    profiledataRedux && pushScreenViewEvent({screenName: profiledataRedux.type, screenData: loginInfo ?? registerInfo, prevScreenName:'login'})
    setProfiledata(profiledataRedux)
    if (profiledataRedux && profiledataRedux?.members.length > 0) {
      const index = profiledataRedux?.members.findIndex(
        i => i.username == profileState?.userName
      )
      userProfileRef?.current[index == -1 ? 0 : index]?.focus()
    }
  }, [profiledataRedux])

  useEffect(() => {
    const payload = {
      hks:
        registerInfo?.session_stringvalue ??
        loginInfo?.session_stringvalue ??
        localStorage.getItem('hks'),
      userid:
        registerInfo?.parent_id ??
        loginInfo?.parent_id ??
        localStorage.getItem('loginId'),
      token: registerInfo?.user_token ?? loginInfo?.user_token ?? token,
      gamificationid:
        registerInfo?.gamification_id ??
        loginInfo?.gamification_id ??
        localStorage.getItem('gamificationid'),
      lasttouch: lasttouchRedux
        ? loginInfo?.lasttouch?.profile
        : localStorage.getItem('lasttouch'),
      profiledata: profileState
    }

    payload?.gamificationid && dispatch(getProfileReadData(payload))
    let npawPlugin
    if (!!youbora?.[region] && youbora?.[region]?.analytics?.enabled) {
      const parameters = {
        host: youbora?.[region]?.analytics?.host,
        'components': {'balancer' : false}
      }
      npawPlugin = new NpawPlugin(
        // youbora?.[region]?.analytics?.accountcode,
        //This above line needs to be uncommented, once the access for dashboard is acquired. For now, using default code below.
        'clarovideotatadev',
        parameters
      )
      !youboraInstance && dispatch(setNpawPluginInstance(npawPlugin))
    } else {
      npawPlugin = new NpawPlugin('clarovideotatadev')
      !youboraInstance && dispatch(setNpawPluginInstance(npawPlugin))
    }
  }, [])

  const handlesamsungkey = (key, keycode) => {
    if (key.yellowcode === keycode || keycode === 10009) {
      backCheck && navigate('/settings', { state: { activeTab: 'admin' } })
    }
  }

  const handleLgkey = keycode => {
    if (keycode === 405 || keycode === 461 || keycode === 'back' || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
      backCheck && navigate('/settings', { state: { activeTab: 'admin' } })
    }
  }

  const keypresshandler = event => {
    const keycode = event.keyCode
    if (typeof tizen !== 'undefined') {
      tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
      const codes = {
        yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code
      }
      handlesamsungkey(codes, keycode)
    } else {
      handleLgkey(keycode)
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', keypresshandler)
    return () => {
      document.removeEventListener('keyup', keypresshandler)
    }
  }, [keypresshandler])

  useEffect(() => {
    if (
      (loginError && logintermsandconditionError) ||
      (loginError && !logintermsandconditionSuccess) ||
      pushSessionError ||
      isLoggedInError ||
      changingProfileError ||
      userDetailsError ||
      pushSession
    ) {
      dispatch(setSkeltonLoading(false))
      navigate('/service-error', { state: { pageName: 'watchProfile' } })
    }
  }, [
    loginError,
    pushSessionError,
    isLoggedInError,
    changingProfileError,
    userDetailsError,
    pushSession,
    logintermsandconditionError,
    logintermsandconditionSuccess
  ])


  const handle_addprofile = () => {
    navigate('/addprofile', {
      state: { selectedProf: '', profileImg: profileState?.img }
    })
  }

  const handle_editprofile = () => {
    //GA for profile event
    pushProfileEvent(
      loginInfo,
      truncateText(
        'Perfiles_SeleccionarPerfil_TextoBotonPrimario',
        20
      )?.toLowerCase(),
      truncateText(
        'Perfiles_SeleccionarPerfil_Título_TextoTitulo',
        100
      )?.toLowerCase()
    )
    navigate('/watcheditprofile', { state: { data: '', dafaultfocus: true } })
  }

  const handleProfileSelection = item => {
    dispatch(clearLoginInfoState()),
      dispatch(
        getLogin({
          userHash: item?.user_hash,
          type: 'nonAdmin',
          hks: loginInfo?.session_stringvalue
        })
      )
    dispatch(getUserProfile(item))
    dispatch(getNavTabValue('homeuser'))
    if (localStorage.getItem('currNavIdx')) {
      localStorage.removeItem('currNavIdx')
    }
    if (localStorage.getItem('cardContent')) {
      localStorage.removeItem('cardContent')
    }
    if (localStorage.getItem('searchValue')) {
      localStorage.removeItem('searchValue')
    }    
    if (localStorage.getItem('miscontenidos')) {
      localStorage.removeItem('miscontenidos')
    }
  }

const truncateText = (str, length) => {
  const text = apilanguage?.[str] ?? str
  if (!length) {
    length = 100
  }
  if (text?.length > length) {
    return `${text?.slice(0, length)}...`
  } else {
    return text
  }
}
  useEffect(() => {
        if (isLoggedInStatus && isLoggedInStatus == 'Success') {
        const shouldShowTutorial = localStorage.getItem('showTutorialAfterWatchProfile')
        if(videoTutorialData && videoTutorialData?.[region]){
        if (shouldShowTutorial) {
          localStorage.removeItem('showTutorialAfterWatchProfile') // clean up
          localStorage.setItem('isLoginUserTutorialShown', 'true') // mark as shown
          dispatch(clearIsloggedInStatus()) 
          navigate('/videoTutorial', {
            state: {
              data: '',
              seriesEpisodeData: state?.seriesEpisodeData,
              fromDetailsPage: state?.fromDetailsPage,
              pageName: state?.pageName
            }
          })
          return 
        }
        }
       switch (true) {       
        case anonymousUser?.page == 'livePlayer':
          dispatch(removePlayerInstance())
          dispatch(setSkeltonLoading(false))
          dispatch(clearIsloggedInStatus())
          navigate('/livePlayer', { state: { showControls: 'live' } })
          break
        case state?.fromDetailsPage:
          dispatch(setSkeltonLoading(false))
          navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails',{
            state: { 
              data: vcardSeriesDetails?.vodSeries ?? state?.data, 
              seriesEpisodeData: state?.seriesEpisodeData,
              fromDetailsPage: state?.fromDetailsPage,
              pageName: state?.pageName
            }
          })
          break
        case vcardSeriesDetails?.page == 'series':
          dispatch(setSkeltonLoading(false))
          dispatch(clearIsloggedInStatus())
          navigate('/series', {
            state: { data: vcardSeriesDetails?.vodSeries }
          })
          break
        case vcardDetails?.page == 'movies':
          dispatch(setSkeltonLoading(false))
          dispatch(clearIsloggedInStatus())
          navigate('/movies', {
            state: { vodData: vcardDetails?.vodMoviesData }
          })
          break
        case vcardSeriesDetails?.playerpage == 'playerrecord':
          dispatch(setSkeltonLoading(false))
          dispatch(clearIsloggedInStatus())
          navigate('/series', {
            state: { data: vcardSeriesDetails?.playerepisode }
          })
          break
        default:
          dispatch(getNavBarClicked(true))
          dispatch(getSettingsClicked(false))
          navigate('/home')
          break
      }
    }
  }, [isLoggedInStatus, vcardSeriesDetails, vcardDetails])

  return (
    !loadingState && (
      <div className="add-profile" style={{ height: 1080 }}>
        <div className="profile-app-logo">
          <img
            src={'images/claro-profile-logo.png'}
            className="logo-img"
            alt="logo"
          />
          {backCheck && (
            <button
              className="back-button focusable"
              id="back-btn-id"
              onClick={() => handleLgkey('back')}
            >
              <img
                src={'images/Profile_Icons/ic_shortcut_amarillo.png'}
                className="yellow-dot"
                alt="img not found"
              />
              <img
                src={'images/Profile_Icons/ic_shortcut_back.png'}
                className="back-arrow"
                alt="img not found"
              />
              <span className="back-button-text">       
                {
                    truncateText(
                      'BotonShortcut_TextoTitulo',
                      8
                    )
                  }
              </span>
            </button>
          )}
        </div>
        <div>
          <div className="prf-watch-header-container">
            <h5 className="prf-watch-header">
                 {truncateText('Perfiles_SeleccionarPerfil_Título_TextoTitulo', 100)}
            </h5>
          </div>
          <div className="parent-user-profile">
            <div className="user-profile-container">
              {profiledata &&
                profiledata?.members.map(
                  (item, index) =>
                    index < 5 && (
                      <button
                        className="user-profile-image focusable"
                        key={index}
                        id={`image${index}`}
                        ref={ref => (userProfileRef.current[index] = ref)}
                        autoFocus={index === 0}
                        onClick={() => handleProfileSelection(item)}
                        data-sn-up="#back-btn-id"
                      >
                        <img
                          className="user-image"
                          src={
                            item?.user_image && item?.user_image != 'null'&& 
                            item?.user_image != 'undefined' 
                              ? item?.user_image 
                              : 'images/Profile_Icons/profile_image.png' 
                          }
                        />
                        <p className="user-prof-name">
                          {item?.username.length > 15
                            ? `${item?.username.slice(0, 15)}...`
                            : item?.username}
                        </p>
                      </button>
                    )
                )}
              {profiledata && profiledata?.totalMembers <= 4 ? (
                <button
                  className="user-profile-image focusable"
                  onClick={handle_addprofile}
                  data-sn-down="#EditWatchProfBtn_id"
                  data-sn-up="#back-btn-id"
                >
                  <LazyLoadImage
                    src="images/Profile_Icons/Atoms_AAF_Onboarding_Profile_AddProfile.png"
                    className="user-image"
                  />
                  <p className="user-prof-name">
                      {truncateText('addProfile_access_title_label', 30)}
                  </p>
                </button>
              ) : null}
            </div>
            <div className="edit-watch-profile">
              <button
                id="EditWatchProfBtn_id"
                className="edit-watch-prof-btn focusable"
                onClick={handle_editprofile}
                data-sn-left
                data-sn-right
                data-sn-up={`#image0`}
              >
                <span className="edit-watch-prof-btn_txt">
                  {truncateText('Perfiles_SeleccionarPerfil_TextoBotonPrimario', 20)}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  )
}

export default WatchingProfile
