.app-css-my-devices {
	width: 1920px;
	height: 1080px;
	position: fixed;
	background-color: #121212;

	.claro-logo {
		height: 34.36px;
		width: 169.64px;
		margin-top: 67px;
		margin-left: 90px;
	}

	.back-indicator-button-pin {
		display: inline-block;
		color: #ffffff;
		width: 290px;
		height: 48px;
		border-radius: 6.6px;
		font-size: 16px;
		background-color: #2E303D;
		vertical-align: middle;
		float: right;
		margin-top: 40px;
		margin-right: 40px;


		.yellow-indicator-button {
			width: 20px;
			height: 20px;
			padding: 0px 24px 0px 24px;
			vertical-align: middle;
		}

		.back-indicator-image {
			width: 35px;
			height: 28px;
			padding: 0px 24px 0px 0px;
			vertical-align: middle;
		}

		span {
			display: inline-block;
			vertical-align: middle;
			font-family: Roboto;
			font-weight: bold;
			font-size: 29px;
			color: #ffffff;
			width: 146px;
			height: 34px;
		}
	}



	.back-indicator-button-pin:focus {
		background-color: #981C15;
	}

	.title-my-devices {
		color: #ffffff;
		font-family: Roboto;
		font-size: 48px;
		font-weight: bold;
		letter-spacing: 0;
		text-align: center;
		justify-content: center;
		display: flex;
	}

	.sub-text {
		color: #ffffff;
		font-family: Roboto;
		font-size: 40px;
		letter-spacing: 0;
		line-height: 200px;
		display: flex;
		text-align: center;
		justify-content: center;
	}

	.my-devices-logout-success {
		display: flex;
		justify-content: center;
		align-items: center;

		.signout_title {
			height: 42px;
			width: 655px;
			color: #eeeeee;
			font-family: Roboto;
			font-size: 36px;
			letter-spacing: 0;
		}

		.green-tick-img {
			margin-left: 18.6px;
		}

	}

	.my-devices-div {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;

		.logout-my-devices-button {
			height: 82.08px;
			width: 580.26px;
			border-radius: 10.03px;
			background-color: #2e303d;
		}

		.logout-my-devices-button:focus {
			background-color: #981c15;
		}

		.button-contents {
			color: #ffffff;
			font-family: Roboto;
			font-size: 36.5px;
			font-weight: bold;
			letter-spacing: -0.58px;
			line-height: 42.18px;
			text-align: center;
			text-transform: uppercase;
			margin-left: 62px
		}

		.sub-title-1 {
			height: 76px;
			width: 485px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			letter-spacing: 0;
			text-align: center;
			display: flex;
			margin-top: 58px;
		}
	}

	.no-devices-div {
		display: flex;
		justify-content: center;
		margin-top: 100px;

		.no-devices {
			height: 47px;
			width: 1520px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 40px;
			letter-spacing: 0;
			line-height: 47px;
			text-align: center;
		}
	}

	.subtitle-div {
		display: flex;
		justify-content: center;
		margin-top: 153px;

		.sub-title-2 {
			height: 76px;
			width: 576px;
			color: #ffffff;
			font-family: Roboto;
			font-size: 32px;
			letter-spacing: 0;
			text-align: center;
			display: flex;
		}
	}
}