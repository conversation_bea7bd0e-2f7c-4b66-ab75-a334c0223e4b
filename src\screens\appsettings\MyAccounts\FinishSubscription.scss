body {
  margin: 0px;
}

.checkout-subsription-done {
  width: var(--maxWidth);
  height: var(--maxHeight);
  display: flex;
  flex-direction: column;

  .checkout-subscription-page-loader {
    opacity: 0.5;
  }

  .subscription-container-loader {
    position: absolute;
    top: 207px;
    right: 767px;
  }

  .finish-subscription-title {
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    letter-spacing: 0;
    line-height: 48px;
    text-align: center;
    margin-top: 137px;
    margin-bottom: 48px;
  }

  .finish-details-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin-left: 72px;

    .finish-green-tick-mark {
      height: 72px;
      width: 72px;
      flex-shrink: 0;
    }
  }

  .finish-image-and-text {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    max-height: 448px;
    width: 805px;
    align-self: center;
    .finish-image-area {
      .finish-banner {
        height: 360px;
        width: 240px;
      }
    }
  }

  .finish-free-charge-string {
    margin-top: 16px;
    color: #00a9ff;
    font-family: <PERSON><PERSON>;
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.999px;
  }

  .finish-additional-info {
    color: #eee;
    font-family: Roboto;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: -0.874px;
    margin-top: 8px;
  }

  .finish-total-label-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    height: 32px;
    margin-top: 40px;

    .finish-total {
      color: #fff;
      font-family: Roboto;
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px; /* 114.286% */
    }

    .finish-pricing-currency {
      color: #fff;
      font-family: Roboto;
      font-size: 28px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px; /* 114.286% */
      margin-left: 10px;
    }

    .finish-periodicity {
      color: #eee;
      font-family: Roboto;
      font-size: 28px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px; /* 114.286% */
    }
  }

  .finish-tax-label {
    color: #fff;
    font-family: Roboto;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-left: 79px;
  }

  .subscription-paymentmethod-label {
    color: #fff;
    font-family: Roboto;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 114.286% */
    margin-top: 16px;
  }

  .finish-paid-with {
    color: #fff;
    font-family: Roboto;
    font-size: 28px;
    font-style: normal;
    font-weight: bold;
    line-height: 32px; /* 114.286% */
    margin-top: 8px;
  }

  .compra-paid-with {
    margin-bottom: 248px
  }

  .renta-addon-text {
    margin-top: 10px;
    margin-bottom: 80px;
  }

  .validity {
    display: flex;
    flex-direction: row;
    margin-top: 16px;
  }

  .finish-vigencia {
    color: #fff;
    font-family: Roboto;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 114.286% */
    margin-right: 8px;
  }

  .finish-page-duration {
    color: #fff;
    font-family: Roboto;
    font-size: 28px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px; /* 114.286% */
  }

  .asset-title {
    color: #ffffff;
    font-family: Roboto;
    font-size: 40px;
    font-style: normal;
    font-weight: 400;
    line-height: 48px; /* 120% */
  }

  .compra-total-wrapper {
    margin-top: 80px;
  }

  .finish-text-area {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 40px;
    width: 480px;

    .finish-subscription-logos {
      display: flex;
      height: 92px;

      .finish-buy-sub-logo {
        height: 92px;
      }
    }
  }

  .finish-buttons-area {
    display: flex;
    flex-direction: column;
    margin-top: 100px;
    align-items: center;

    .finish-subscription-page-buttons {
      height: 80.23px;
      width: 579.6px;
      border-radius: 12.67px;
      background-color: #2e303d;
      color: #ffffff;
      font-family: Roboto;
      font-size: 31.2px;
      font-weight: bold;
      text-align: center;
    }

    .finish-subscription-page-buttons:focus {
      background: #981c15;
      transform: scale(1.08);
      color: #ffffff;
      font-family: Roboto;
      font-size: 31.2px;
      font-weight: bold;
      text-align: center;
    }

    .reproducir-button-margin {
      margin-bottom: 30px;
    }
  }
}
