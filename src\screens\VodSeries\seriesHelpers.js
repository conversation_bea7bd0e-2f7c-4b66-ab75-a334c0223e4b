export const calculateVODTop = (vodSeriesCastRedux) => {
    const titleLength = vodSeriesCastRedux?.common?.extendedcommon?.media?.serie?.title?.length || 0;
    const originalTitleLength = vodSeriesCastRedux?.common?.extendedcommon?.media?.originaltitle?.length || 0;
    if (titleLength > 28 && originalTitleLength <= 27) return '111px';
    if (originalTitleLength <= 27) return '93px';
    return '';
  };
  
  export const calculateLargeVODTop = (vodSeriesCastRedux) => {
    const originalTitleLength = vodSeriesCastRedux?.common?.extendedcommon?.media?.originaltitle?.length || 0;
    return originalTitleLength > 27 ? '100px' : '0px';
  };
  
  export const calculateLargeVODPosition = (vodSeriesCastRedux) => {
    const titleLength = vodSeriesCastRedux?.common?.extendedcommon?.media?.serie?.title?.length || 0;
    const originalTitleLength = vodSeriesCastRedux?.common?.extendedcommon?.media?.originaltitle?.length || 0;
    if (originalTitleLength > 27 && titleLength > 28) return '';
    if (originalTitleLength > 27) return 'absolute';
    return '';
  };
  