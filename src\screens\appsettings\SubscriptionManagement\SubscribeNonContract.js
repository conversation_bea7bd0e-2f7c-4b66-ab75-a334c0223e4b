import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import './subscriptionNonContract.scss'
import {

	getChannelData,

} from '../../../store/slices/PlayerSlice'

const SubscriptionContract = () => {
	const navigate = useNavigate();
	const { state } = useLocation();
	
	const  subscribeInfoData  = state; 
	const liveChannelId = localStorage.getItem('live-playing-channel-id') !=
	'undefined'
	  ? localStorage.getItem('live-playing-channel-id')
	  : ''

	const region = localStorage.getItem('region')
	const playerChannelData = useSelector(state => state?.player?.channelData)
	const apaAssetsImages = useSelector((state) => state?.Images?.imageresponse)
	const apaMeta = useSelector((state) => state?.initialReducer?.appMetaData)
	const translations = apaMeta?.translations && JSON?.parse(apaMeta?.translations)
	const apilanguage = translations?.language?.[region]
	const epgSevenDaysData = useSelector(state => state?.epg?.epgChannel)
	const liveChannnelInfo = useSelector(state => state?.player?.playerinfo)
	const paywayResponse =
    useSelector(state => state?.epg?.paywayToken?.response?.paqs?.paq) ?? []
	let channelIndex = epgSevenDaysData?.[1]?.channelResponse.findIndex(itrObj => itrObj.group_id == liveChannelId)

	//data to be passed here 
	const handleViewDetails = (e) => {
		if (state?.pageName == "livePlayer") {
			navigate('/my-settings/my-subscriptions/add-subscriptions', { 
				state: { 
					pageName: "livePlayer", 
					data: playerChannelData, 
					groupId: state?.groupId 
				} 
			});
		}
		else {
			navigate('/my-settings/my-subscriptions/add-subscriptions/viewDetails', { 
				state: { 
					pageName: "/premiumSubscription", 
					groupId: state?.groupId 
				} 
			});
		}
	}

	const channelLogoUrl = (data) => {
		return apaAssetsImages?.["transactional_" + data + "_logo"]; //checking the data is there in apa/assets
	}

	const getViewDetailsButton = (data) => {
		return apilanguage?.['includes_' + data]
	}


	const keyPressFunc = event => {
		const keycode = event.keyCode;
		if (typeof tizen !== 'undefined') {
			tizen.tvinputdevice.registerKeyBatch(['ColorF2Yellow'])
			const codes = {
				yellowcode: tizen.tvinputdevice.getKey('ColorF2Yellow').code,
			}
			handlesamsungkey(codes, keycode);
		} else {
			handleLgkey(keycode);
		}
	}

	useEffect(() => {
		document.addEventListener('keyup', keyPressFunc)
		return () => {
			document.removeEventListener('keyup', keyPressFunc)
		}
	}, [])

	const handlesamsungkey = (key, keycode) => {
		channelIndex = epgSevenDaysData?.[1]?.channelResponse.findIndex(
			itrObj => itrObj.group_id == state?.groupId ?? liveChannnelInfo?.response?.group?.common?.id
		)
		const groupId =
        	epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group_id ??
        	paywayResponse?.[0]?.groups?.split(',').shift()
		if (key.yellowcode === keycode || keycode === 10009) {

			if (state?.pageName == 'livePlayer') {
				getChannelData({
					group_id: groupId,
					timeshift: epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group?.common?.timeshift,
					switchChannel: 'yes',
					epgIndex: channelIndex
				})

				navigate('/livePlayer', {
					state: { showControls: 'live', returnPage: 'livePlayer', groupId: state?.groupId },
					replace: true
				})

			}
			else {
				if (state?.pageName === "/home") {
					localStorage.setItem('currNavIdx', -1)
				}
				navigate(state?.pageName, { item: state?.item, groupId: state?.groupId })
			}


		}
	}



	const handleLgkey = (keycode) => {
		channelIndex = epgSevenDaysData?.[1]?.channelResponse.findIndex(
			itrObj => itrObj.group_id == state?.groupId ?? liveChannnelInfo?.response?.group?.common?.id
		)
		const groupId =
        	epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group_id ??
        	paywayResponse?.[0]?.groups?.split(',').shift()
		if (keycode == 405 || keycode === 461 || keycode == 'backClick' || ((CURRENT_PLATFORM == 'netrange' || CURRENT_PLATFORM == 'zeasn') &&  keycode == 120)) {
			if (state?.pageName == "livePlayer") {
				getChannelData({
					group_id: groupId,
					timeshift: epgSevenDaysData?.[1]?.channelResponse?.[channelIndex]?.group?.common?.timeshift,
					switchChannel: 'yes',
					epgIndex: channelIndex
				})
				navigate('/livePlayer', {
					state: { showControls: 'live', returnPage: 'livePlayer', groupId: state?.groupId },
					replace: true
				})
			}
			else {
				if (state?.pageName === "/home") {
					localStorage.setItem('currNavIdx', -1)
				}
				navigate(state?.pageName, { item: state?.item, groupId: state?.groupId })
			}

		}
	}

	const handleBackNavigation = () => {
		typeof tizen == 'undefined' ?
		handleLgkey('backClick'):
		handlesamsungkey(10009)
	}

	return (
		<div className="app-css">
			<button className="ack-indicator focusable" onClick={() => handleBackNavigation()}>
				<img className="yellow-indicator" src={'images/yellow_shortcut.png'} />
				<img className="back-image" src={'images/back_button.png'} />
				<p className="back-text">{apilanguage?.BotonShortcut_TextoTitulo_Regresar}</p>
			</button>

			<div className='subscription-container'>
				<h5 className='subscription-title'>{apilanguage?.subscription_modal_title_label ?? 'subscription_modal_title_label'}</h5>
				<div className='logo-container'>
					<img src={channelLogoUrl(subscribeInfoData?.data?.proveedor_code)} className='channelSubLogo' />
					<img src={channelLogoUrl(subscribeInfoData?.data?.proveedor_code)} className='channelSubLogo' />
				</div>
				<div className='details-container'>
					<h5 className='channel-name'>{apilanguage?.STV_SUBSCRIBE_CONTRACT ?? 'STV_SUBSCRIBE_CONTRACT'}</h5>
					<h6 className='channel-info'>{apilanguage?.STV_SUBSCRIBE_INFO ?? 'STV_SUBSCRIBE_INFO'}</h6>
					<h5 className='channel-option'>{apilanguage?.STV_SUBSCRIBE_INFO ?? 'STV_SUBSCRIBE_INFO'}</h5>
				</div>
				<div className='btn-container'>
					<button className='view-details-button focusable' id='details-button' autoFocus={true} onClick={(e) => handleViewDetails(e)}>{getViewDetailsButton(subscribeInfoData?.data?.proveedor_code) ?? '¿QUÉ INCLUYE'}</button>
					<button className='cancel-button focusable' id='cancel-button' onClick={() => handleBackNavigation()}>{apilanguage?.userProfile_password_option_button_cancel}</button>
				</div>
			</div>
		</div>
	)
}

export default SubscriptionContract
