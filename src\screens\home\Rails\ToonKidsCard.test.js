import React from "react";
import { fireEvent, getAllByText, getByAltText, getByTestId, getByText, queryByAttribute, render } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import ToonKidsCard from "./ToonKidsCard";


const initialState = {};
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

const moviesrailcardmockdata = [
    {
        "title": "",
        "title_uri": "",
        "volant": null,
        "crest": "",
        "url": null,
        "image_highlight": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_large": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_medium": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_small": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_background": "",
        "group_id": null,
        "special": null,
        "section": "paramount",
        "format_types": null,
        "live_enabled": "0",
        "live_type": null,
        "live_ref": null,
        "rating_code": null,
        "proveedor_name": null,
        "proveedor_code": null,
        "user_status": null,
        "type": "node"
    },
    {
        "title": "",
        "title_uri": "",
        "volant": null,
        "crest": "",
        "url": null,
        "image_highlight": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_large": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_medium": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_small": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_background": "",
        "group_id": null,
        "special": null,
        "section": "pandg_hbo",
        "format_types": null,
        "live_enabled": "0",
        "live_type": null,
        "live_ref": null,
        "rating_code": null,
        "proveedor_name": null,
        "proveedor_code": null,
        "user_status": "suscripto_hbo",
        "type": "node"
    },
    {
        "title": "L1MAX",
        "title_uri": "L1MAX",
        "volant": null,
        "crest": "LIGA1MAX IPTV PERÚ",
        "url": "destacado_clickUrlGrupo(1131243,'VIDEO');",
        "image_highlight": "https://clarovideocdn6.clarovideo.net/pregeneracion//cms/images/202403/366382_Default_L1MAX-Nuevo-PE_22204655.jpg",
        "image_large": "https://clarovideocdn8.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/SS/L1MAXWHORIZONTAL.jpg?size=529x297",
        "image_medium": "https://clarovideocdn6.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/SS/L1MAXWVERTICAL.jpg?size=200x300",
        "image_small": "https://clarovideocdn8.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/SS/L1MAXWHORIZONTAL.jpg?size=290x163",
        "image_background": "https://clarovideocdn8.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/CLEAN/L1MAX_e-1280x720.jpg",
        "group_id": "1131243",
        "special": null,
        "section": null,
        "format_types": "susc",
        "live_enabled": "1",
        "live_type": "1",
        "live_ref": "pe.liga1max",
        "rating_code": "G",
        "proveedor_name": "LIGA 1 MAX",
        "proveedor_code": "liga1max",
        "text_highlight": "L1MAX Nuevo PE",
        "is_series": false,
        "user_status": null,
        "type": "group"
    },
]
const seriesrailcardmockdata = [
    {
        "title": "",
        "title_uri": "",
        "volant": null,
        "crest": "",
        "url": null,
        "image_highlight": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_large": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_medium": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_small": "https://clarovideocdn4.clarovideo.net/pregeneracion//cms/images/202208/270536_Default_PARAMOUNT_premium_logo_29110216.png",
        "image_background": "",
        "group_id": null,
        "special": null,
        "section": "paramount",
        "format_types": null,
        "live_enabled": "0",
        "live_type": null,
        "live_ref": null,
        "rating_code": null,
        "proveedor_name": null,
        "proveedor_code": null,
        "user_status": null,
        "type": "node",
        "is_series": true,

    },
    {
        "title": "",
        "title_uri": "",
        "volant": null,
        "crest": "",
        "url": null,
        "image_highlight": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_large": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_medium": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_small": "https://clarovideocdn7.clarovideo.net/pregeneracion//cms/images/202211/296872_Default_HBO-premium-bra_03190239.png",
        "image_background": "",
        "group_id": null,
        "special": null,
        "section": "pandg_hbo",
        "format_types": null,
        "live_enabled": "0",
        "live_type": null,
        "live_ref": null,
        "rating_code": null,
        "proveedor_name": null,
        "proveedor_code": null,
        "user_status": "suscripto_hbo",
        "type": "node",
        "is_series": true,

    },
    {
        "title": "L1MAX",
        "title_uri": "L1MAX",
        "volant": null,
        "crest": "LIGA1MAX IPTV PERÚ",
        "url": "destacado_clickUrlGrupo(1131243,'VIDEO');",
        "image_highlight": "https://clarovideocdn6.clarovideo.net/pregeneracion//cms/images/202403/366382_Default_L1MAX-Nuevo-PE_22204655.jpg",
        "image_large": "https://clarovideocdn8.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/SS/L1MAXWHORIZONTAL.jpg?size=529x297",
        "image_medium": "https://clarovideocdn6.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/SS/L1MAXWVERTICAL.jpg?size=200x300",
        "image_small": "https://clarovideocdn8.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/SS/L1MAXWHORIZONTAL.jpg?size=290x163",
        "image_background": "https://clarovideocdn8.clarovideo.net/LIGA1MAX/PELICULAS/L1MAX/EXPORTACION_WEB/CLEAN/L1MAX_e-1280x720.jpg",
        "group_id": "1131243",
        "special": null,
        "section": null,
        "format_types": "susc",
        "live_enabled": "1",
        "live_type": "1",
        "live_ref": "pe.liga1max",
        "rating_code": "G",
        "proveedor_name": "LIGA 1 MAX",
        "proveedor_code": "liga1max",
        "text_highlight": "L1MAX Nuevo PE",
        "is_series": true,
        "user_status": null,
        "type": "group"
    },
]
describe('Premium channel page test case', () => {
    test('should render the Premium', () => {
        renderWithState(<ToonKidsCard />)
    })
    test('should render onclick Premium moviesrailcardmockdata', () => {
        const props = {
            dataObject: {
                highlight : moviesrailcardmockdata
            }
        }
        const { container } = renderWithState(<ToonKidsCard {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
    test('should render onclick Premium moviesrailcardmockdata', () => {
        const props = {
            dataObject: {
                highlight : seriesrailcardmockdata
            }
        }
        const { container } = renderWithState(<ToonKidsCard {...props} />)
        const getbytestid = getByTestId(container, 'rail_card_click0')
        fireEvent(
            getbytestid,
            new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
            })
        )
    })
})