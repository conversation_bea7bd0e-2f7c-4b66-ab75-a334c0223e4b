@import url('http://fonts.googleapis.com/css?family=Roboto:700,400,500,300');

.pin-confirmation-container {
    width: 1920px;
    height: 1080px;
    background: rgba(6, 10, 10, 0.842);
    overflow: hidden;

    .pin-confirmation-logo-container {
        margin-top: 40px;
        margin-left: 88px;

        .pin-confirmation-logo {
            width: 170px;
            height: 34px;
        }
    }
    .pin-confirmation-sub-container{
        margin-top: 50px;

    .pin-confirmation-message-heading-container {
        width: 1920px;
        display: flex;
        justify-content: center;
        height: 225px;

        .pin-confirmation-message-heading {
            text-align: center;
            color: #ffff;
            font: 48px Roboto;
            font-weight: bold;
            width: 605px;
        }
    }

    .pin-confirmation-message-container {
        width: 1920px;
        display: flex;
        justify-content: center;

        .pin-confirmation-message {
            text-align: center;
            color: #ffff;
            font: 40px Roboto;
            font-weight: normal;
            width: 1000px;
        }
    }

    .pin-confirmation-button-container {
        display: flex;
        justify-content: center;
		margin-top: 20px;

        .pin-confirmation-button {
            width: 580.26px;
            height: 82.08px;
            background-color: #981c15;
            color: #ffff;
            font: 36.5px Roboto;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
						&:focus {
							background-color: #981c15;
							
						}
        }
    }
}
}
