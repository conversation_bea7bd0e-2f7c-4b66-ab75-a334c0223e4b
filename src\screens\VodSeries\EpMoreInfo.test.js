import React from "react";
import { render,fireEvent,queryByAttribute } from "@testing-library/react/";
import { Provider } from "react-redux";
import "regenerator-runtime/runtime";
import { BrowserRouter as Router, useLocation } from "react-router-dom";
import configureStore from "redux-mock-store";
import { createHashHistory as createHistory } from "history";
import { fromJS } from "immutable";
import { EpMoreInfo } from "./EpMoreInfo";


const initialState = fromJS({
    initialReducer:{
        appMetaData:{
            translations:`{"language":{"test":{"DURATION":"VALUE"}}}`
        }
    }
});
const mockStore = configureStore([]);
const history = createHistory();
const Wrapper = ({ children, reduxStore = mockStore(initialState) }) => (
    <Provider store={reduxStore}>
        <Router history={history}>{children}</Router>
    </Provider>
);
export const renderWithState = (ui) => {
    return render(ui, { wrapper: Wrapper });
};

jest.mock("react-router-dom", () => ({
    ...jest.requireActual("react-router-dom"),
    useLocation: () => ({
      state: {
        page:"series",
        genreData:"genreData_genreData_genreData_genreData_genreData_genreData_genreData_genreData_genreData_genreData_",
        episodeItem:{
            title_episode:"title_episode_title_episode_title_episode_title_episode_title_episode_title_episode_title_episode_title_episode_",
            title:"title_title_title_title_title_title_title_title_title_title_title_title_title_title_title_title_title_title_title_title_"
        }
      }
    })
  }));
  

  let tizen = {
    tvinputdevice: {
        registerKeyBatch:()=>{

        },
        getKey:()=>{
            return {
                code:"#ff0"
            }
        }
    },
    getCurrentPosition: jest.fn(),
    // Add more mock methods or properties as needed
  };

describe('landing page test', () => {
    beforeEach(() => {
        // Create a writable property on window object
        Object.defineProperty(window, 'SpatialNavigation', {
          value: {focus:()=>{}},
          writable: true,
        });
        Object.defineProperty(window, 'tizen', {
            value: tizen,
            writable: true,
          });

          localStorage.setItem('region',"test");
      });
    test('it should render the landing Page', async () => {  
        const wrapper = await renderWithState(<EpMoreInfo />)
            fireEvent.keyUp(wrapper.container, {
                key: "ArrowDown",
                code: "ArrowDown",
                keyCode: 8,
                charCode: 8
          });
    })
    test('it should render the landing Page with tizen undefined', async () => {
        window.tizen = undefined;
        const wrapper = await renderWithState(<EpMoreInfo />)
            fireEvent.keyUp(wrapper.container, {
                key: "ArrowDown",
                code: "ArrowDown",
                keyCode: 8,
                charCode: 8
          });

          fireEvent.scroll(window)
    })
})