import { createSlice } from '@reduxjs/toolkit'

export const epgVersionSlice = createSlice({
  name: 'epgVersion',
  initialState: {
    epgVersion: '',
    soaVersion: '',
    isLoading: false,
    error: {},
  },
  reducers: {
    getEpgVersion: state => {
      state.isLoading = true
    },
    getEpgVersionSuccess: (state, action) => {
      state.epgVersion = action?.payload?.response?.epg_version
      state.soaVersion = action?.payload?.response?.soa_version
      state.isLoading = false
    },
    getEpgVersionError: (state, action) => {
      state.isLoading = false
      state.error = action.payload
    },
  },
})

export const { getEpgVersion, getEpgVersionSuccess, getEpgVersionError } =
  epgVersionSlice.actions

export default epgVersionSlice.reducer
