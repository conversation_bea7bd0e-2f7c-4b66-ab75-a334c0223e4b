import { call, takeEvery } from 'redux-saga/effects'
import { URL, COMMON_URL } from '../utils/environment'
import { request } from '../utils/request'
import { store } from '../store/sagaStore'
import {
  getEpgMenuError,
  getEpgMenuSuccess,
  getEpgChannelError,
  getEpgChannelSuccess,
  getEpgVersionError,
  getEpgVersionSuccess,
  getEpgLineupError,
  getEpgLineupSuccess,
  getFavouriteLive,
  getFavouriteLiveSuccess,
  getFavouriteLiveError,
  addFavouriteLiveSuccess,
  addFavouriteLiveError,
  delFavouriteLiveSuccess,
  delFavouriteLiveError,
  getLiveReminderSuccess,
  getLiveReminderError,
  addLiveReminderSuccess,
  addLiveReminderError,
  delLiveReminderSuccess,
  delLiveReminderError,
  getLiveTvRecordingSuccess,
  getLiveTvRecordingError,
  addLiveTvSeriesRecordingSuccess,
  addLiveTvSeriesRecordingError,
  addLiveTvEpisodeRecordingSuccess,
  addLiveTvEpisodeRecordingError,
  delLiveTvSeriesRecordingSuccess,
  delLiveTvSeriesRecordingError,
  delLiveTvEpisodeRecordingSuccess,
  delLiveTvEpisodeRecordingError,
  getPayWayTokenSuccess,
  getPayWayTokenError,
  getPayWayPurchase_ButtonInfoSuccess,
  getPayWayPurchase_ButtonInfoError,
  getLiveTvEpisodeRecordingSuccess,
  getLiveTvEpisodeRecordingError,
  getLiveTvCompletedRecordingSuccess,
  getLiveTvCompletedRecordingError,
  getLiveTvProgressRecordingSuccess,
  getLiveTvProgressRecordingError,
  deleteMyContentRecordingSuccess,
  deleteMyContentRecordingError
} from '../store/slices/EpgSlice'

function* getEpgMenu() {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.EPG_MENU_URL +
        `&region=${region}&device_so=${COMMON_URL.device_so}&format=json`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getEpgMenuSuccess(response))
          store.dispatch(getEpgVersion())
        },
        onError(error) {
          store.dispatch(getEpgMenuError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(getEpgMenuError(error))
  }
}

function* getEpgVersion() {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.EPG_VERSION_URL + `&region=${region}&format=json`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getEpgVersionSuccess(response))
          store.dispatch(getEpgLineup())
        },
        onError(error) {
          store.dispatch(getEpgVersionError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(getEpgVersionError(error))
  }
}

// function* getEpgChannel({payload}) {

//   let region = localStorage.getItem('region')
//   let menuId = sessionStorage.getItem('nodeId')
//   let epgVersion = sessionStorage.getItem('epgVersion')
//   let soaVersion = sessionStorage.getItem('soaVersion')

//   const date = new Date();
//   let startDate = `${date.getFullYear()+ (date.getMonth()+1).toString().padStart(2,'0') + date.getDate().toString().padStart(2,'0') + date.getHours().toString().padStart(2,'0') + (date.getMinutes() < 30 ? '00' : '30') + '00'}`
//   let endDate = `${date.getFullYear()+ (date.getMonth()+1).toString().padStart(2,'0') + parseInt(date.getDate() + 1).toString().padStart(2,'0') + date.getHours().toString().padStart(2,'0') + (date.getMinutes() < 30 ? '00' : '30') + '00'}`
//   try {
//     // const api1=  URL.EPG_LINEUP_URL +
//     // `&date_from=${startDate}&date_to=${endDate}&node_id=${payload.nodeid}&epg_version=${219014}&metaData=full&soaVersion=${0.10}`;
//     // const api2 =  URL.EPG_LINEUP_URL +
//     // `&date_from=${startDate}&date_to=${endDate}&epg_version=${219014}&metaData=full&soaVersion=${0.10}`;
//     // let url= api2;
//     // if(payload.nodeid)url=api1;

//     yield call(
//       request,
//       URL.EPG_CHANNEL_URL + `&region=${region}&device_id=${COMMON_URL.device_id}&format=json&quantity=2000&from=0` +
//         `&date_from=${startDate}&date_to=${endDate}&node_id=${menuId}&epg_version=${epgVersion}&metaData=full&soaVersion=${soaVersion}`,
//       {
//         method: 'GET',
//       },
//       {
//         onSuccess(response) {
//           store.dispatch(getEpgChannelSuccess(response))
//         },
//         onError(error) {
//           store.dispatch(getEpgChannelError(error))
//         },
//       },
//     )
//   } catch (error) {
//     store.dispatch(getEpgChannelError(error))
//   }
// }

function* getEpgLineup({ payload }) {
  const date = new Date()
  let startDate = `${date.getFullYear()}${
    date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  }${date.getDate() < 10 ? '0' + date.getDate() : date.getDate()}${
    date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  }${date.getMinutes() < 30 ? '00' : '30'}00`

  let endDate = `${date.getFullYear()}${
    date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  }${
    date.getDate() + 1 < 10 ? '0' + (date.getDate() + 1) : date.getDate() + 1
  }${date.getHours() < 10 ? '0' + date.getHours() : date.getHours()}${
    date.getMinutes() < 30 ? '00' : '30'
  }00`

  let region = localStorage.getItem('region')
  try {
    // const api1=  URL.EPG_LINEUP_URL +
    // `&date_from=${startDate}&date_to=${endDate}&node_id=${payload.nodeid}&epg_version=${219014}&metaData=full&soaVersion=${0.10}`;
    // const api2 =  URL.EPG_LINEUP_URL +
    // `&date_from=${startDate}&date_to=${endDate}&epg_version=${219014}&metaData=full&soaVersion=${0.10}`;
    // let url= api2;
    // if(payload.nodeid)url=api1;
    let epgversion = sessionStorage.getItem('epgVersion')
    let soaversion = sessionStorage.getItem('soaVersion')
    yield call(
      request,
      // url,
      URL.EPG_LINEUP_URL +
        `&region=${region}&device_id=${COMMON_URL.device_id}&format=json&quantity=2000&from=0` +
        `&date_from=${startDate}&date_to=${endDate}&node_id=${payload.nodeid}&epg_version=${epgversion}&metadata=full&soaVersion=${soaversion}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getEpgLineupSuccess(response))
        },
        onError(error) {
          store.dispatch(getEpgLineupError(error))
        }
      }
    )
  } catch (error) {
    store.dispatch(getEpgLineupError(error))
  }
}

function* getFavouriteLiveApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.FAVORITE_CHANNEL_URL +
        '&HKS=' +
        `${payload.hks}` +
        '&user_id=' +
        `${payload.user_id}` +
        '&user_hash=' +
        `${payload.user_hash}` +
        '&user_token=' +
        `${payload.user_token}` +
        '&epg_version=' +
        `${payload.epg_version}` +
        '&lasttouch=' +
        `${payload.lasttouch}` +
        '&region=' +
        region,
      {
        method: 'POST'
      },
      {
        onSuccess(response) {
          store.dispatch(getFavouriteLiveSuccess(response))
        },
        onError(error) {
          store.dispatch(getFavouriteLiveError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}
function* getPayWayTokenApi({ payload }) {
  let region = localStorage.getItem('region')

  try {
    yield call(
      request,
      URL.PAYWAY_TOKEN_URL +
        '&HKS=' +
        `${payload.hks}` +
        '&user_id=' +
        `${payload.user_id}` +
        '&ts=' +
        `1` +
        '&npvr=' +
        `1` +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getPayWayTokenSuccess(response))
        },
        onError(error) {
          store.dispatch(getPayWayTokenError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getPayWay_Purchase_ButtonInfo({ payload }) {
  let region = localStorage.getItem('region')

  try {
    yield call(
      request,
      URL.PayWay_Purchase_Button_Info +
        '&HKS=' +
        `${payload.hks}` +
        '&user_id=' +
        `${payload.user_id}` +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getPayWayPurchase_ButtonInfoSuccess(response))
        },
        onError(error) {
          store.dispatch(getPayWayPurchase_ButtonInfoError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* addFavouriteLiveApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.ADDFAVOURITE_CHANNEL_URL +
        '&user_id=' +
        `${payload.user_id}` +
        '&user_hash=' +
        `${payload.user_hash}` +
        '&epg_version=' +
        `${payload.epg_version}` +
        '&object_id=' +
        `${payload.object_id}` +
        '&region=' +
        region,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(addFavouriteLiveSuccess(response))
        },
        onError(error) {
          store.dispatch(addFavouriteLiveError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* delFavouriteLiveApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.DELETE_FAVOURITE_CHANNEL_URL +
        '&user_id=' +
        `${payload.user_id}` +
        '&user_hash=' +
        `${payload.user_hash}` +
        '&epg_version=' +
        `${payload.epg_version}` +
        '&object_id=' +
        `${payload.object_id}` +
        '&region=' +
        region,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(delFavouriteLiveSuccess(response))
        },
        onError(error) {
          store.dispatch(delFavouriteLiveError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getLiveReminderApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.REMINDER_CHANNEL_URL +
        '&HKS=' +
        `${payload.hks}` +
        '&user_hash=' +
        `${payload.user_hash}` +
        '&region=' +
        region,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLiveReminderSuccess(response))
        },
        onError(error) {
          store.dispatch(getLiveReminderError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* addLiveReminderApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.ADDREMINDER_CHANNEL_URL +
        '&HKS=' +
        `${payload.hks}` +
        '&user_id=' +
        `${payload.user_id}` +
        '&user_hash=' +
        `${payload.user_hash}` +
        '&channel_id=' +
        `${payload.channel_id}` +
        '&type=' +
        `${payload.type}` +
        '&event_id=' +
        `${payload.event_id}` +
        '&exp_date=' +
        `${payload.exp_date}` +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(addLiveReminderSuccess(response))
        },
        onError(error) {
          store.dispatch(addLiveReminderError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* delLiveReminderApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.DELETE_REMINDER_CHANNEL_URL +
        '&reminder_id=' +
        `${payload.reminder_id}` +
        '&user_hash=' +
        `${payload.user_hash}` +
        '&region=' +
        region +
        '&HKS=' +
        `${payload.hks}`,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(delLiveReminderSuccess(response))
        },
        onError(error) {
          store.dispatch(delLiveReminderError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

//rECORDINGS
function* getLiveTvRecordingListApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.LIVETV_RECORD_LIST_URL +
        '&user_token=' +
        `${payload.user_token}` +
        '&region=' +
        region +
        '&user_id=' +
        `${payload?.userId}`,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLiveTvRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(getLiveTvRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getLiveTvCompletedRecordingListApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.LIVETV_RECORD_LIST_URL}&user_token=${payload.user_token}&region=${region}&status=3.0`,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLiveTvCompletedRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(getLiveTvCompletedRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}
function* getLiveTvProgressRecordingListApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      `${URL.LIVETV_RECORD_LIST_URL}&user_token=${payload.user_token}&region=${region}&status=4.0`,

      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLiveTvProgressRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(getLiveTvProgressRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* getLiveTvRecordingEpisodeListApi({ payload }) {
  try {
    yield call(
      request,
      payload,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(getLiveTvEpisodeRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(getLiveTvEpisodeRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* addLiveTvSeriesRecordingApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.ADD_LIVETV_SERIES_RECORD_URL +
        '&user_id=' +
        `${payload.user_id}` +
        '&group_id=' +
        `${payload.group_id}` +
        '&user_token=' +
        `${payload.user_token}` +
        '&payway_token=' +
        `${payload.payway_token}` +
        '&channel_id=' +
        `${payload.channel_id}` +
        '&event_alf_id=' +
        `${payload.event_alf_id}` +
        '&region=' +
        region,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(addLiveTvSeriesRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(addLiveTvSeriesRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* addLiveTvEpisodeRecordingApi({ payload }) {
  let region = localStorage.getItem('region')
  try {
    yield call(
      request,
      URL.ADD_LIVETV_EPISODE_RECORD_URL +
        '&user_id=' +
        `${payload.user_id}` +
        '&group_id=' +
        `${payload.group_id}` +
        '&user_token=' +
        `${payload.user_token}` +
        '&payway_token=' +
        `${payload.payway_token}` +
        '&channel_id=' +
        `${payload.channel_id}` +
        '&event_alf_id=' +
        `${payload.event_alf_id}` +
        '&region=' +
        region +
        '&offset=' +
        `${payload?.offset}`,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(addLiveTvEpisodeRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(addLiveTvEpisodeRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* delLiveTvSeriesRecordingApi({ payload }) {
  try {
    yield call(
      request,
      payload,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(delLiveTvSeriesRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(delLiveTvSeriesRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* delLiveTvEpisodeRecordingApi({ payload }) {
  try {
    yield call(
      request,
      payload,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(delLiveTvEpisodeRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(delLiveTvEpisodeRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

function* deleteMyContentRecordingApi({ payload }) {
  try {
    yield call(
      request,
      payload,
      {
        method: 'GET'
      },
      {
        onSuccess(response) {
          store.dispatch(deleteMyContentRecordingSuccess(response))
        },
        onError(error) {
          store.dispatch(deleteMyContentRecordingError(error))
        }
      }
    )
  } catch (error) {
    console.error('catch error --> ', error)
  }
}

export default function* epgSaga() {
  yield takeEvery('epg/getEpgMenu', getEpgMenu)
  yield takeEvery('epg/getEpgVersion', getEpgVersion)
  yield takeEvery('epg/getEpgLineup', getEpgLineup)
  yield takeEvery('epg/getFavouriteLive', getFavouriteLiveApi)
  yield takeEvery('epg/addFavouriteLive', addFavouriteLiveApi)
  yield takeEvery('epg/delFavouriteLive', delFavouriteLiveApi)

  yield takeEvery('epg/getLiveReminder', getLiveReminderApi)
  yield takeEvery('epg/addLiveReminder', addLiveReminderApi)
  yield takeEvery('epg/delLiveReminder', delLiveReminderApi)

  yield takeEvery('epg/getLiveTvRecording', getLiveTvRecordingListApi)
  yield takeEvery(
    'epg/getLiveTvCompleteRecording',
    getLiveTvCompletedRecordingListApi
  )
  yield takeEvery(
    'epg/getLiveTvProgressRecording',
    getLiveTvProgressRecordingListApi
  )
  yield takeEvery(
    'epg/getLiveTvEpisodeRecoding',
    getLiveTvRecordingEpisodeListApi
  )
  yield takeEvery('epg/addLiveTvSeriesRecording', addLiveTvSeriesRecordingApi)
  yield takeEvery('epg/addLiveTvEpisodeRecording', addLiveTvEpisodeRecordingApi)
  yield takeEvery('epg/delLiveTvSeriesRecording', delLiveTvSeriesRecordingApi)
  yield takeEvery('epg/delLiveTvEpisodeRecording', delLiveTvEpisodeRecordingApi)
  yield takeEvery('epg/deleteMyContentRecording', deleteMyContentRecordingApi)
  yield takeEvery('epg/getPayWayToken', getPayWayTokenApi)
  yield takeEvery(
    'epg/getPayWayPurchase_ButtonInfo',
    getPayWay_Purchase_ButtonInfo
  )
}
